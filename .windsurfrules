-we have only one virtual environment at the root of the project (./venv). Always activate and use this venv for all backend commands, scripts, migrations, and server runs. Never use a different or nested venv. If you have venv-related issues, run `source ./venv/bin/activate` from the project root and ensure all dependencies are installed via `pip install -r backend/requirements.txt`.
-frontend must always run on port 3000. If you encounter a port conflict, kill any process using port 3000 before starting the frontend.
-run the frontend using the built-in browser preview so errors can be easily sent to Cascade chat.
-always ensure you have the full context for the changes you make. When not sure, ask or review the relevant files from beginning to end before proposing changes.
-all documentation should be stored in the docs folder under the appropriate subfolder. Every time you change the database schema or create a migration, you must update docs/rethink/qrvibe_schema.md to reflect the new schema. This ensures the documentation is always the source of truth for frontend/API/integration work.
-Both frontend and backend have their own `.env` files for environment-specific configuration. Never mix or share variables between these files.
-The backend is Python-based. All Python dependencies must be managed exclusively in `backend/requirements.txt`. Never install packages globally or outside the root venv.
- Always review existing migrations before creating a new one to avoid duplicate or conflicting schema changes (especially type/ID mismatches between integer and uuid).
- Never manually edit an applied migration; create a new migration for any change.
- Ensure every Alembic migration includes the correct revision, down_revision, and metadata at the top.
- Use human-readable Alembic revision IDs.
- Update seed scripts whenever the schema changes, so dev environments always have valid data.
- After every migration, immediately update docs/rethink/qrvibe_schema.md and verify it matches the latest DB schema. Remove or update docs for deprecated tables.
- After running a migration, verify the DB schema using alembic current and by inspecting the DB directly.
- Run backend and frontend tests after any migration or schema update.
- Never commit .env files or secrets to version control.
- If a schema change affects API responses or requests, update the relevant Pydantic models and frontend API types immediately.
- All schema, migration, and critical backend changes must be code reviewed by at least one other developer.
- Ensure meaningful error messages and logging for all critical operations, especially migrations and startup scripts.

# QRVibe BE Python Virtual Environment Policy

- Always use the root venv: `./venv`
- Activate with: `source ./venv/bin/activate`
- Install backend dependencies with: `pip install -r backend/requirements.txt`
- Never use or create a venv inside `/backend` or any subdirectory.
- All backend scripts, migrations, and server runs must use this venv.
- If you have venv issues, re-activate and re-install requirements.
- If a dependency version is unavailable, update `backend/requirements.txt` to a valid version and re-install.

# QRVibe Backend Import Policy

- Always use the full Python package path for all backend imports.
- Never use relative or ambiguous imports like `models.*`, `schemas.*`, `services.*`, etc.
- Always use `from backend.models.*`, `from backend.schemas.*`, `from backend.services.*`, etc.
- This applies to all backend modules, services, scripts, and integrations.
- If you encounter a `ModuleNotFoundError` for a top-level package, update the import path to the full `backend.*` path.

# QRVibe API Policy
- No API versioning
- All API routes should be mounted at the root level without version prefixes

# QRVibe Subscription Policy
- These are the only official subscription plans: free, starter, growth, enterprise

# QRVibe Addon Policy
- These are the only official addon plans: landing_page_builder, event_ticketing, premium_templates, white_labeling, additional_storage

# QRVibe Team Addon Policy
- These are the only official team addon plans: team_members, team_storage, team_backup

# QRVibe Frontend
- React 19 with Next.js 15
- TailwindCSS
- TypeScript    
- No venv is needed here. simply cd to frontend and run the appropriate cmd

# QRVibe Frontend Components & Design System

- Always use reusable components from the common folder for consistency
- Leverage the following design system components for all new features:
  - `Skeleton` components for loading states
  - `EmptyState` components for zero-data scenarios
  - `ActionButton` components for primary and secondary actions
  - Standard form components with validation
  - Toast notifications for success/error feedback
- Every page should handle these states consistently:
  - Loading state (with skeleton loaders)
  - Empty state (with helpful guidance)
  - Error state (with retry options)
  - Success/completion state
- All modals, drawers, and dialogs should use the same pattern and styling

# QRVibe Type Safety Guidelines

- Always use TypeScript interfaces/types for all data structures
- Use `type-only` imports with the `import type` syntax when importing just types
- Create comprehensive type definitions in the `types/` folder
- Ensure all API hooks have proper TypeScript return types
- Use nullish coalescing (`??`) and optional chaining (`?.`) for safe data access

# QRVibe Frontend API Integration

- Create dedicated API service files in `services/` for each domain
- Implement custom React hooks in `hooks/` for all data fetching
- Never use mock data in production code, always integrate with the real API
- Add proper error handling with toast notifications for user feedback
- Use React Query for API state management with proper caching policies
- All API URLs should use the root path (no `/api/` prefix) as they're mounted at root level

# QRVibe UI/UX
- We are using Apple design language
- All UI components must follow this styling pattern:
  - Clean, minimal interfaces with appropriate whitespace
  - Clear visual hierarchy and typography
  - Use system fonts with appropriate weights
  - Subtle animations for enhanced user experience

## Button Standards
- Primary buttons: White text on primary color (#6B48FF)
- Secondary buttons: Outlined or ghost style
- Never use gradients on buttons
- Consistent border radius (rounded-md)
- Hover and focus states for all interactive elements

## FE and BE Integration
- BE has all the APIs needed for a full FE integration so never use mock data but actual API for integration
- All API's are mounted at the root so no /api/

## QRVibe Colors
- Primary: #6B48FF (Vibrant Purple)
- Secondary: #00E5FF (Electric Cyan)
- Neutral: #F5F6F5 (Soft White)
- Error: #FF3B30
- Success: #34C759
- Warning: #FF9500

## Terminilogies
- all users on the platform are referred to us user/users. so anyone who subscribers as free, starter, growth or enterpise is a user. then based on their subscription plan, they can access the collaboration features on the platform which requires the org->team->project flow.

## Approach for fixing issues or errors
- we are senior engineers and our project is at 85% complettion so changes must be very contextual not guessed.

## Running servers
- backernd uses ./qrvibe.sh runserver
- frontend uses node server.cjs.

## Making code changes
- at 87% project completion, always ensure you get 95% context for the issue before making any code changes. we are senior engineers so we have to build a world class plaftorm

## Generating migrations
-we don't use auto genrate for migrations so crerate it manually. auto gives lots of problems. use the script ./qrvibe.sh to handle migration works. NB: project is at 87% completion

## No cookies in qrvibe
- refer to /Users/<USER>/aihub/qrvibe/auth_standards.md