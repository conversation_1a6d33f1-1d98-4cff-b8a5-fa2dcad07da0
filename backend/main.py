from dotenv import load_dotenv; load_dotenv(dotenv_path="backend/.env")
from fastapi import FastAP<PERSON>, Request, Response, Depends, HTTPException, Query, BackgroundTasks
from fastapi.responses import JSONResponse, HTMLResponse, StreamingResponse
from backend.core.middleware.cors import setup_cors
from fastapi.security import OAuth2PasswordRequestForm
import backend.models  # <-- Ensures all models are registered for SQLAlchemy
from backend.models.user import User
from backend.models.auth import UserCreate, UserRead
from backend.db.database import get_async_session
from typing import Dict
import os
import logging
import json
from backend.config.logging_config import setup_logging
from backend.routes import api_router
# from backend.api.admin import router as admin_router  # Removed to prevent double mounting of admin routers
from backend.api.user import router as user_router
from starlette.middleware.sessions import SessionMiddleware
from backend.core.middleware import RequestContextMiddleware
from backend.middleware.subscription_limits import SubscriptionLimitsMiddleware


from backend.core.config import settings
from backend.patches import apply_patches
from fastapi import FastAPI
from backend.core.middleware.cors import setup_cors

from fastapi.staticfiles import StaticFiles  # Add this import
from pathlib import Path
import re

# Configure logging
setup_logging()
logger = logging.getLogger(__name__)

# Log a startup message to test file logging
logger.info("=== QRVibe API Startup: Logging to file is enabled ===")
import sys
for handler in logger.handlers:
    handler.flush()

# Apply patches before initializing FastAPI
logger.info("Applying critical patches before initialization")
try:
    apply_patches()
except Exception as e:
    logger.error(f"Failed to apply patches: {str(e)}")

app = FastAPI(
    title="QRVibe API",
    description="API for QRVibe - QR code generation and management platform",
    version="1.0.0",
    docs_url="/docs",
    redoc_url="/redoc",
    openapi_url="/openapi.json",
    # Required for cross-domain cookie handling with frontend
    root_path="",
    swagger_ui_parameters={
        "persistAuthorization": True,
        "tryItOutEnabled": True,
        "displayRequestDuration": True,
        "filter": True
    }
)

# Apply centralized CORS configuration - must be FIRST to handle preflight requests
setup_cors(app)

# Route for handling CORS preflight requests globally
@app.options("/{rest_of_path:path}")
async def options_route(rest_of_path: str):
    from backend.core.middleware.cors import create_preflight_response
    # Use the centralized CORS preflight response utility
    return create_preflight_response(all_origins=True)

# Add session middleware for 2FA
app.add_middleware(
    SessionMiddleware,
    secret_key=os.getenv("SECRET_KEY"),
    max_age=3600  # 1 hour
)

# Add request context middleware
app.add_middleware(RequestContextMiddleware)

# Add subscription limits middleware
app.add_middleware(SubscriptionLimitsMiddleware)

# Add enhanced security middleware with rate limiting and security headers
from backend.middleware import register_middleware
register_middleware(app)

# Initialize token blacklist service
from backend.services.token_blacklist import token_blacklist
@app.on_event("startup")
async def initialize_token_blacklist():
    await token_blacklist.initialize()
    logger.info("Token blacklist service initialized")
    # Cleanup expired tokens periodically
    await token_blacklist.cleanup_expired()

# Configure storage paths and static file serving
from backend.core.storage import get_storage_path

# Get and configure storage directory
storage_path = get_storage_path()
logger.info(f"Mounting storage directory: {storage_path}")

# Ensure storage directory exists with proper permissions
storage_path.mkdir(parents=True, exist_ok=True)
os.chmod(storage_path, 0o755)

# Create QR codes directory
qr_codes_path = storage_path / "qr_codes"
qr_codes_path.mkdir(parents=True, exist_ok=True)
os.chmod(qr_codes_path, 0o755)

# Log the absolute path for debugging
logger.info(f"Storage directory absolute path: {os.path.abspath(str(storage_path))}")

# Mount storage for static file serving
app.mount("/storage", StaticFiles(directory=str(storage_path), check_dir=True), name="storage")

# Add a root endpoint for testing
@app.get("/")
def read_root():
    return {"message": "Welcome to QRVibe API"}

import jwt
import inspect
from starlette.middleware.base import BaseHTTPMiddleware

# Import our custom JSON utilities
from backend.core.json_utils import json_dumps

# Import required modules
from fastapi import status

# Standard JWT Authentication Middleware for protected endpoints
# Note: We've removed the custom AuthMeOverrideMiddleware in favor of using the standard 
# authentication flow with the get_current_user dependency for all endpoints including /auth/me.
# This improves security, maintainability, and follows industry-standard practices.

# Add debug middleware for authentication issues
class AuthDebugMiddleware(BaseHTTPMiddleware):
    async def dispatch(self, request, call_next):
        # Only intercept auth/me requests for debugging
        if request.url.path == "/auth/me":
            logger = logging.getLogger("backend.auth")
            logger.debug("AUTH DEBUG: Request to /auth/me")
            
            # Log cookie information for debugging
            cookies = request.cookies
            cookie_names = list(cookies.keys())
            logger.debug(f"AUTH DEBUG: Cookies: {cookie_names}")
            
            # Check for the fastapiusersauth cookie specifically
            if "fastapiusersauth" in cookies:
                auth_cookie = cookies["fastapiusersauth"]
                
                # Log the first part of the cookie for privacy
                logger.debug(f"AUTH DEBUG: Found fastapiusersauth cookie: {auth_cookie[:20]}...")
                
                # Use our key management system consistently
                from backend.core.security.jwt_keys import jwt_key_manager
                
                try:
                    # Always use jwt_key_manager for all token verification
                    payload = jwt_key_manager.decode_token(auth_cookie)
                    logger.debug(f"AUTH DEBUG: Token decoded successfully with jwt_key_manager")
                    logger.debug(f"AUTH DEBUG: Token subject: {payload.get('sub', 'missing')}")
                    
                    # Add debugging for request context
                    logger.debug(f"AUTH DEBUG: Request headers: {dict(request.headers)}")
                    logger.debug(f"AUTH DEBUG: Cookie header present: {request.headers.get('cookie') is not None}")
                except Exception as e:
                    logger.error(f"AUTH DEBUG: Token decode error: {str(e)}")
                    logger.debug(f"AUTH DEBUG: Token first 20 chars: {auth_cookie[:20]}")
            else:
                logger.debug("AUTH DEBUG: No fastapiusersauth cookie found")
                logger.debug(f"AUTH DEBUG: Available cookies: {cookie_names}")
        
        response = await call_next(request)
        
        # Log response for auth/me
        if request.url.path == "/auth/me":
            logger.debug(f"AUTH DEBUG: Response status for /auth/me: {response.status_code}")
            
        return response

# Add the middleware
app.add_middleware(AuthDebugMiddleware)

# Add CSRF protection middleware
from backend.core.middleware.csrf import CSRFProtectionMiddleware, add_csrf_middleware

# Add CSRF middleware to protect against CSRF attacks
add_csrf_middleware(app, exclude_paths=[
    "/auth/jwt/login",
    "/auth/logout",
    "/auth/register",
    "/auth/2fa",
    "/auth/me",
    "/auth/verify-email",
    "/auth/reset-password",
    "/admin/login",
    "/admin/logout"
])

# Add cookie security middleware to fix SameSite issues
@app.middleware("http")
async def bearer_token_auth_middleware(request: Request, call_next):
    # Handle OPTIONS requests (CORS preflight) with special care
    if request.method == "OPTIONS":
        # Use the centralized CORS utility for preflight responses
        from backend.core.middleware.cors import create_preflight_response
        
        logger.debug("[Auth] Preflight request handled directly in middleware")
        return create_preflight_response(all_origins=True)
        
    # Complete bypass for all documentation-related endpoints
    swagger_paths = ["/docs", "/redoc", "/openapi.json"]
    
    # Also bypass all Swagger UI static assets
    if request.url.path.startswith("/docs/") or request.url.path.startswith("/redoc/") or \
       any(request.url.path == path for path in swagger_paths):
        # Complete pass-through without any modification
        return await call_next(request)

    """
    Bearer Token Authentication Middleware
    
    Enforces strict Bearer token authentication per 2025 security standards.
    Removes any cookie-based authentication and ensures proper handling of auth headers.
    """
    # Explicitly remove any authentication cookies from incoming requests
    # Note: We can't modify headers directly as they're immutable, but we can use
    # Starlette's scope to modify the ASGI request
    if "cookie" in request.headers:
        cookies = request.headers["cookie"].split(";")
        # Filter out auth cookies but keep other cookies
        filtered_cookies = [c for c in cookies if not any(auth_cookie in c for auth_cookie in ["fastapiusersauth", "qrvibe_admin_auth"])]
        
        # Instead of modifying headers directly, we'll set a custom attribute that our
        # downstream handlers can check
        request.state.filtered_cookies = filtered_cookies
    
    # Process the request with strict Bearer token auth
    response = await call_next(request)
    
    # Remove any Set-Cookie headers that might set auth cookies
    if "set-cookie" in response.headers or "Set-Cookie" in response.headers:
        cookies_header = response.headers.get("set-cookie") or response.headers.get("Set-Cookie", "")
        cookies = cookies_header.split(",")
        
        # Filter out auth cookies
        valid_cookies = [c for c in cookies if not any(auth_cookie in c for auth_cookie in ["fastapiusersauth", "qrvibe_admin_auth"])]
        
        # Update headers only with non-auth cookies
        if valid_cookies:
            if "set-cookie" in response.headers:
                response.headers["set-cookie"] = ",".join(valid_cookies)
            else:
                response.headers["Set-Cookie"] = ",".join(valid_cookies)
        else:
            # Remove cookie headers entirely if all were auth cookies
            response.headers.pop("set-cookie", None)
            response.headers.pop("Set-Cookie", None)
    
    # Add security headers to enforce best practices
    response.headers["X-Content-Type-Options"] = "nosniff"
    response.headers["X-Frame-Options"] = "DENY"
    
    logger.debug("[Auth] Enforced Bearer token authentication for request")
    return response

# Mount the main API router at root ('/') for all endpoints (e.g., /auth/me, /qr, /teams, etc.)
app.include_router(
    api_router
)

# Integrate new admin and user routers for content management at root paths
# Mount user router directly to avoid double /user prefix since the router already has /user prefix
app.include_router(user_router)

# Add admin support router for ticket management
from backend.api.admin.support import router as admin_support_router
app.include_router(admin_support_router, prefix="/admin")

# Dashboard, finance, and security routers mounted at root paths
from backend.routes import dashboard
from backend.api.finance import router as finance_router
from backend.api.security import router as security_router
from backend.api.webhooks import router as webhooks_router

app.include_router(dashboard.router, prefix="/dashboard")
app.include_router(finance_router, prefix="/finance")
app.include_router(webhooks_router)  # Register webhook handlers for payment processors without prefix
app.include_router(security_router, prefix="/security")

# Register the smart poster PDF generation endpoint at /poster
from backend.routes import poster
app.include_router(poster.router, prefix="/poster", tags=["poster"])

# Register the admin integration management endpoints
from backend.routes import admin_integration
app.include_router(admin_integration.router, prefix="/admin")

# Register the enhanced security monitoring endpoints for admins
from backend.routes import admin_security
app.include_router(admin_security.router)

# Register the user LLM settings endpoints
from backend.routes import user_llm_settings
app.include_router(user_llm_settings.router)

# Register QR preview endpoints
from backend.routes import qr_preview
app.include_router(qr_preview.router)

# Register QR creation and management endpoints
from backend.routes import qr
app.include_router(qr.router, prefix="/qr", tags=["qr"])

# Enhanced QR endpoints have been removed
# from backend.routes import enhanced_qr
# app.include_router(enhanced_qr.router)

# Register brand profile management endpoints
from backend.routes import brand_profile
app.include_router(brand_profile.router, tags=["brand-profiles"])

# Register user settings endpoints
from backend.routes import user_settings
app.include_router(user_settings.router)

# Register notification endpoints
from backend.routes import notification
app.include_router(notification.router)

# Initialize QR preview cache
from backend.services.qr_preview_cache import get_qr_preview_cache

@app.on_event("startup")
async def init_qr_preview_cache():
    """Initialize and start the QR preview cache system"""
    cache = get_qr_preview_cache()
    await cache.start_cleanup_task()
    logger.info("QR preview cache initialized and cleanup task started")

@app.on_event("shutdown")
async def shutdown_qr_preview_cache():
    """Stop the QR preview cache system"""
    cache = get_qr_preview_cache()
    await cache.stop()
    logger.info("QR preview cache stopped")

# === DEBUG: Print all registered routes at startup ===
print("=== Registered routes ===")
for route in app.routes:
    print(route.path)

# Configure logging
logging.getLogger("backend").setLevel(logging.DEBUG)

# Add health check endpoint
@app.get("/health", tags=["health"])
async def health_check():
    """Simple health check endpoint for monitoring and frontend status checks"""
    return {"status": "healthy", "version": "1.0.0", "service": "qrvibe-api"}
