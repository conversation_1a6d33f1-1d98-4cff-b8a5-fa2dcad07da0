from fastapi import APIRouter, Depends, Query, Body, HTTPException, Response, File, UploadFile, Form
from sqlalchemy.ext.asyncio import AsyncSession
from typing import Dict, Any, List, Optional
from uuid import UUID
from datetime import datetime
import logging
import json
from sqlalchemy import and_
from pydantic import BaseModel

from backend.models.user import User
from backend.models.qr import QRCode
from backend.core.security import get_current_user
from backend.db.database import get_async_session
from backend.core.config import Settings
from backend.schemas.qr import QRResponse, QRResponseData
from backend.services.qr_preview import QRPreviewService

# Initialize settings
settings = Settings()

# Initialize logger
logger = logging.getLogger(__name__)

# Initialize the router
router = APIRouter()

# Function to verify active user
async def get_active_user(current_user: User = Depends(get_current_user)) -> User:
    """
    Verify that the current user is active.
    Raises HTTPException if user is not active.
    """
    if not current_user.is_active:
        raise HTTPException(status_code=403, detail="Inactive user")
    return current_user

@router.post("/download/social/{qr_id}")
async def download_qr_social(
    qr_id: UUID,
    platform: str = Query(..., description="Social media platform (instagram, twitter, facebook, etc.)"),
    custom_options: Dict[str, Any] = Body({}, description="Optional custom export options"),
    current_user: User = Depends(get_active_user),
    db: AsyncSession = Depends(get_async_session)
):
    """
    Download a QR code optimized for a specific social media platform
    
    This endpoint generates QR codes with platform-specific optimizations
    for dimensions, format, and visual appearance.
    """
    try:
        # Generate a unique request ID for tracking
        from uuid import uuid4
        request_id = str(uuid4())
        
        # Get current timestamp for metadata
        timestamp = datetime.utcnow().isoformat() + "Z"
        
        # Check rate limits
        if not await rate_limiter.check_api_rate(current_user, "download_social_qr"):
            raise HTTPException(
                status_code=429,
                detail={
                    "success": False,
                    "error": {
                        "code": "RATE_LIMITED",
                        "message": "Rate limit exceeded for social QR downloads"
                    },
                    "meta": {
                        "request_id": request_id,
                        "timestamp": timestamp,
                        "version": "2025.1"
                    }
                }
            )
        
        # Initialize exporter to check valid platforms
        exporter = QRExporter()
        
        # Validate platform
        if platform.lower() not in exporter.social_media_specs:
            raise HTTPException(
                status_code=400,
                detail={
                    "success": False,
                    "error": {
                        "code": "INVALID_PLATFORM",
                        "message": f"Invalid social media platform: {platform}",
                        "valid_platforms": list(exporter.social_media_specs.keys())
                    },
                    "meta": {
                        "request_id": request_id,
                        "timestamp": timestamp,
                        "version": "2025.1"
                    }
                }
            )
        
        try:
            # Get QR code from database
            qr = await qr_service._get_qr_code(db, qr_id)
            
            # Check if QR code exists
            if not qr:
                raise HTTPException(
                    status_code=404,
                    detail={
                        "success": False,
                        "error": {
                            "code": "RESOURCE_NOT_FOUND",
                            "message": f"QR code with ID {qr_id} not found"
                        },
                        "meta": {
                            "request_id": request_id,
                            "timestamp": timestamp,
                            "version": "2025.1"
                        }
                    }
                )
            
            # Validate access
            await qr_service._validate_qr_access(db, current_user, qr)
            
            # Get design options
            design_options = qr.design_options or {}
            
            # Export for social media
            qr_bytes = await exporter.export_for_social_media(
                qr_data=qr.qr_data,
                design_options=design_options,
                platform=platform,
                custom_options=custom_options
            )
            
            # Log successful download for analytics
            logger.info(f"User {current_user.id} downloaded social QR {qr_id} for platform {platform}")
            
            # Determine file extension and content type based on platform
            platform_specs = exporter.social_media_specs.get(platform.lower(), {})
            format = platform_specs.get("format", "png")
            
            content_types = {
                "png": "image/png",
                "jpg": "image/jpeg",
                "jpeg": "image/jpeg",
                "webp": "image/webp"
            }
            content_type = content_types.get(format, "image/png")
            
            # Set filename for download
            filename = f"qrvibe_{qr.id}_{platform.lower()}.{format}"
            
            # Return file response
            return Response(
                content=qr_bytes,
                media_type=content_type,
                headers={
                    "Content-Disposition": f"attachment; filename={filename}",
                    "X-Request-ID": request_id  # Include request ID in header for tracking
                }
            )
            
        except PermissionError as pe:
            # Handle permission errors
            raise HTTPException(
                status_code=403,
                detail={
                    "success": False,
                    "error": {
                        "code": "PERMISSION_DENIED",
                        "message": str(pe)
                    },
                    "meta": {
                        "request_id": request_id,
                        "timestamp": timestamp,
                        "version": "2025.1"
                    }
                }
            )
            
    except HTTPException:
        # Re-raise HTTP exceptions
        raise
    except Exception as e:
        # Log the error
        logger.error(f"Error downloading social QR {qr_id} for platform {platform}: {str(e)}", exc_info=True)
        
        # Return standardized error response
        raise HTTPException(
            status_code=500,
            detail={
                "success": False,
                "error": {
                    "code": "DOWNLOAD_ERROR",
                    "message": f"Failed to download QR code for {platform}",
                    "details": str(e) if settings.DEBUG else None
                },
                "meta": {
                    "request_id": request_id if 'request_id' in locals() else str(uuid4()),
                    "timestamp": datetime.utcnow().isoformat() + "Z" if 'timestamp' not in locals() else timestamp,
                    "version": "2025.1"
                }
            }
        )