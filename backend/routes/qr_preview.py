from fastapi import APIRouter, Depends, UploadFile, File, Form, HTTPException, Query, BackgroundTasks, Body, Request
from typing import Annotated
from sqlalchemy.ext.asyncio import AsyncSession
from backend.db.database import get_async_session
from backend.models.user import User
from backend.models.qr import QRType
from backend.services.auth import get_current_user
from backend.services.qr_preview import QRPreviewService
from backend.services.qr_enhancement import QREnhancementService
from backend.schemas.qr_preview import (
    QRDesignOptions,
    QRPreviewRequest,
    QRPreviewResponse,
    QRDownloadRequest,
    QRBatchPreviewRequest,
    QRBatchPreviewResponse,
    QRPreviewImageData
)
from backend.schemas.enhanced_qr import QREnhancedPreview
from fastapi.responses import Response, JSONResponse, StreamingResponse
from typing import Optional, List, Dict, Any
from uuid import UUID
import json
import base64
import io
import time
import asyncio
import logging
from fastapi import status
from backend.core.config import settings

# Initialize logger
logger = logging.getLogger(__name__)

# Create router without version prefix per company policy
router = APIRouter(prefix="/qr", tags=["qr"])

# Initialize services
qr_preview_service = QRPreviewService()
qr_enhancement_service = QREnhancementService()

from backend.core.rate_limit import RateLimiter
rate_limiter = RateLimiter()

@router.post("/preview")
async def preview_qr(
    request: Request,
    db: AsyncSession = Depends(get_async_session),
    current_user: User = Depends(get_current_user)
):
    """
    Generate a QR code preview with the given type, content and design options
    
    Args:
        request: The FastAPI request object (to access form data)
        db: Database session
        current_user: Current authenticated user
        
    Returns:
        QR code image as PNG
    """
    try:
        # Get QR preview service
        qr_preview_service = QRPreviewService()
        
        # Check API rate limits
        if not await rate_limiter.check_api_rate(current_user, "preview"):
            raise HTTPException(
                status_code=status.HTTP_429_TOO_MANY_REQUESTS,
                detail={
                    "success": False,
                    "error": {
                        "code": "RATE_LIMITED",
                        "message": "Rate limit exceeded for your subscription tier"
                    }
                }
            )
        
        # Parse the multipart form data
        form_data = await request.form()
        
        # Get request data from form - use the same format as preview/base64
        request_json = form_data.get("request")
        if not request_json:
            raise HTTPException(
                status_code=status.HTTP_422_UNPROCESSABLE_ENTITY,
                detail={
                    "success": False,
                    "error": {
                        "code": "VALIDATION_ERROR",
                        "message": "Missing 'request' parameter in form data"
                    }
                }
            )
        
        # Parse request JSON and extract data
        try:
            request_data = json.loads(request_json)
            logger.debug(f"Received QR preview request: {json.dumps(request_data, default=str)[:500]}")
            
            # Validate and extract parameters
            if "qr_type" not in request_data:
                raise HTTPException(
                    status_code=status.HTTP_422_UNPROCESSABLE_ENTITY,
                    detail={
                        "success": False,
                        "error": {
                            "code": "VALIDATION_ERROR",
                            "message": "Missing 'qr_type' in request data"
                        }
                    }
                )
            
            # Convert string qr_type to enum if needed
            try:
                qr_type_str = request_data["qr_type"]
                from backend.models.qr import QRType
                qr_type = QRType(qr_type_str)
            except ValueError:
                logger.error(f"Invalid QR type: {qr_type_str}")
                raise HTTPException(
                    status_code=status.HTTP_422_UNPROCESSABLE_ENTITY,
                    detail={
                        "success": False,
                        "error": {
                            "code": "VALIDATION_ERROR",
                            "message": f"Invalid QR type: {qr_type_str}"
                        }
                    }
                )
            
            # Get content, design options, and dimensions
            content = request_data.get("content", {})
            design_data = request_data.get("design", {})
            dimensions = request_data.get("dimensions", 300)
            
            # Convert design data to QRDesignOptions
            design = QRDesignOptions(**design_data) if design_data else None
            
            # Handle logo if present in form data
            logo = form_data.get("logo")
            logo_bytes = None
            if logo and isinstance(logo, UploadFile):
                logo_bytes = await logo.read()
                await logo.seek(0)  # Reset file pointer
                logger.debug(f"Received logo: {logo.filename}, size: {len(logo_bytes)} bytes")
            
            # Generate QR code preview
            qr_bytes = await qr_preview_service.generate_preview(
                qr_type=qr_type,
                content=content,
                design=design,
                logo=logo_bytes,
                dimensions=dimensions
            )
            
            # Return image response
            return Response(
                content=qr_bytes,
                media_type="image/png"
            )
        except json.JSONDecodeError as e:
            logger.error(f"Error parsing request JSON: {str(e)}")
            raise HTTPException(
                status_code=status.HTTP_422_UNPROCESSABLE_ENTITY,
                detail={
                    "success": False,
                    "error": {
                        "code": "VALIDATION_ERROR",
                        "message": f"Invalid JSON in request parameter: {str(e)}"
                    }
                }
            )
    except Exception as e:
        logger.error(f"Error generating QR preview: {str(e)}", exc_info=True)
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail={
                "success": False,
                "error": {
                    "code": "SERVER_ERROR",
                    "message": f"Failed to generate QR preview: {str(e)}"
                }
            }
        )


@router.post("/preview/svg")
async def preview_qr_svg(
    request: Request,
    db: AsyncSession = Depends(get_async_session),
    current_user: User = Depends(get_current_user)
):
    """
    Generate a QR code preview in SVG format and return it as a string
    
    Args:
        request: The FastAPI request object (to access form data)
        db: Database session
        current_user: Current authenticated user
        
    Returns:
        SVG content of the QR code in a JSON response following the standard format
    """
    try:
        # Get QR preview service
        qr_preview_service = QRPreviewService()
        
        # Check API rate limits
        if not await rate_limiter.check_api_rate(current_user, "preview_svg"):
            raise HTTPException(
                status_code=status.HTTP_429_TOO_MANY_REQUESTS,
                detail={
                    "success": False,
                    "error": {
                        "code": "RATE_LIMITED",
                        "message": "Rate limit exceeded for your subscription tier"
                    }
                }
            )
        
        # Parse the multipart form data
        form_data = await request.form()
        
        # Get request data from form
        request_json = form_data.get("request")
        if not request_json:
            raise HTTPException(
                status_code=status.HTTP_422_UNPROCESSABLE_ENTITY,
                detail={
                    "success": False,
                    "error": {
                        "code": "VALIDATION_ERROR",
                        "message": "Missing 'request' parameter in form data"
                    }
                }
            )
        
        # Parse request JSON and extract data
        try:
            request_data = json.loads(request_json)
            logger.debug(f"Received QR preview SVG request: {json.dumps(request_data, default=str)[:500]}")
            
            # Validate and extract parameters
            if "qr_type" not in request_data:
                raise HTTPException(
                    status_code=status.HTTP_422_UNPROCESSABLE_ENTITY,
                    detail={
                        "success": False,
                        "error": {
                            "code": "VALIDATION_ERROR",
                            "message": "Missing 'qr_type' in request data"
                        }
                    }
                )
            
            # Convert string qr_type to enum if needed
            try:
                qr_type_str = request_data["qr_type"]
                qr_type = QRType(qr_type_str)
            except ValueError:
                raise HTTPException(
                    status_code=status.HTTP_422_UNPROCESSABLE_ENTITY,
                    detail={
                        "success": False,
                        "error": {
                            "code": "VALIDATION_ERROR",
                            "message": f"Invalid QR type: {qr_type_str}"
                        }
                    }
                )
            
            # Extract content
            content = request_data.get("data", {})
            if not content:
                raise HTTPException(
                    status_code=status.HTTP_422_UNPROCESSABLE_ENTITY,
                    detail={
                        "success": False,
                        "error": {
                            "code": "VALIDATION_ERROR",
                            "message": "Missing or empty 'data' in request"
                        }
                    }
                )
            
            # Extract design options
            design_options = request_data.get("designOptions")
            if design_options:
                # Convert to Pydantic model
                design = QRDesignOptions(**design_options)
            else:
                design = None
            
            # Get logo from form data if provided
            logo = None
            logger.debug(f"Form data keys: {list(form_data.keys())}")
            if "logo" in form_data:
                logo_file = form_data["logo"]
                logger.debug(f"Logo file found: type={type(logo_file)}, filename={getattr(logo_file, 'filename', 'N/A')}")
                if isinstance(logo_file, UploadFile):
                    contents = await logo_file.read()
                    logo = contents
                    logger.debug(f"Logo content read: {len(contents)} bytes")
                else:
                    logger.debug(f"Logo file is not UploadFile: {type(logo_file)}")
            else:
                logger.debug("No logo key found in form data")
            
            # Get dimensions if provided
            dimensions = request_data.get("dimensions", 300)
            
            # Generate SVG QR code
            svg_content = await qr_preview_service.generate_preview_svg(
                qr_type=qr_type,
                content=content,
                design=design,
                logo=logo,
                dimensions=dimensions
            )
            
            # Create cache key for future use
            cache_key = f"svg_preview_{qr_type}_{hash(str(content))}"
            if design:
                cache_key += f"_{hash(str(design.dict()))}"
            if logo:
                cache_key += "_with_logo"
            cache_key += f"_{dimensions}"
            
            # Store in cache for future requests
            from backend.services.qr_preview_cache import get_qr_preview_cache
            cache = get_qr_preview_cache()
            cache.set(cache_key, svg_content)
            
            # Prepare response
            return JSONResponse(
                content={
                    "success": True,
                    "data": {
                        "image_data": svg_content,
                        "format": "svg",
                        "width": dimensions,
                        "height": dimensions
                    },
                    "error": None
                },
                status_code=200
            )
            
        except json.JSONDecodeError:
            raise HTTPException(
                status_code=status.HTTP_422_UNPROCESSABLE_ENTITY,
                detail={
                    "success": False,
                    "error": {
                        "code": "INVALID_JSON",
                        "message": "Invalid JSON in request parameter"
                    }
                }
            )
            
    except HTTPException:
        # Re-raise HTTP exceptions
        raise
    except Exception as e:
        # Log the error
        logger.error(f"Error generating QR preview SVG: {str(e)}", exc_info=True)
        
        # Return standardized error response
        return JSONResponse(
            content={
                "success": False,
                "data": None,
                "error": {
                    "code": "PREVIEW_ERROR",
                    "message": f"Failed to generate QR preview: {str(e)}"
                }
            },
            status_code=500
        )

@router.get("/{qr_id}/export", response_class=StreamingResponse)
async def export_qr(
    qr_id: UUID,
    format: str = Query("png", description="Download format: png, svg, or pdf"),
    dimensions: int = Query(1024, description="Image dimensions (width and height)"),
    with_logo: bool = Query(True, description="Include logo if available"),
    current_user: User = Depends(get_current_user),
    db: AsyncSession = Depends(get_async_session)
):
    """
    Download a QR code in the specified format and dimensions.
    
    This endpoint retrieves an existing QR code and converts it to the requested format.
    """
    from backend.services.qr_export import QRExportService
    from backend.services.qr import QRService
    from backend.services.storage import StorageService
    from sqlalchemy import select
    from backend.models.qr import QRCode
    
    # Initialize services
    export_service = QRExportService()
    qr_service = QRService()
    storage_service = StorageService()
    
    # Validate the format
    format = format.lower()
    if format not in ["png", "svg", "pdf"]:
        raise HTTPException(status_code=400, detail=f"Unsupported format: {format}")
    
    # Get the QR code
    result = await db.execute(select(QRCode).where(QRCode.id == qr_id))
    qr_code = result.scalar_one_or_none()
    
    if not qr_code:
        raise HTTPException(status_code=404, detail="QR code not found")
    
    # Verify user has access to this QR code
    if qr_code.user_id != current_user.id:
        # Check if user is part of the team that owns this QR code
        if not qr_code.team_id or not await qr_service._validate_team_access(db, current_user.id, qr_code.team_id):
            raise HTTPException(status_code=403, detail="You don't have access to this QR code")
    
    # Generate cache key
    cache_key = f"export:{qr_id}:{format}:{dimensions}:{with_logo}"
    
    # Try getting from cache
    from backend.services.qr_preview_cache import get_qr_preview_cache
    cache = get_qr_preview_cache()
    cached_bytes = await cache.get(cache_key)
    
    if cached_bytes:
        logger.debug(f"Cache hit for export {cache_key[:30]}")
        qr_bytes = cached_bytes
    else:
        # Prepare export options
        from backend.schemas.qr_preview import QRExportOptions
        options = QRExportOptions(
            format=format,
            width=dimensions,
            height=dimensions,
            include_logo=with_logo,
            dpi=300 if format == "pdf" else 72
        )
        
        # Get logo if available and requested
        logo_content = None
        if with_logo and qr_code.logo_url:
            try:
                logo_content = await storage_service.get_file(qr_code.logo_url)
            except Exception as e:
                # Just log the error but continue without logo
                logger.warning(f"Could not load logo for QR {qr_id}: {str(e)}")
        
        # Export the QR code
        qr_bytes = await export_service.export_qr(qr_code, options, logo_content)
        
        # Cache the result (30 minutes)
        await cache.set(cache_key, qr_bytes, ttl=1800)
    
    # Set appropriate filename and media type
    filename = f"qrvibe_qr_{qr_id}.{format}"
    media_type = {
        "png": "image/png",
        "svg": "image/svg+xml",
        "pdf": "application/pdf"
    }[format]
    
    # Return as download response
    headers = {
        "Content-Disposition": f"attachment; filename={filename}"
    }
    
    return StreamingResponse(
        io.BytesIO(qr_bytes),
        media_type=media_type,
        headers=headers
    )

@router.post("/batch", response_model=QRBatchPreviewResponse)
async def batch_generate_qr(
    batch_request: QRBatchPreviewRequest,
    background_tasks: BackgroundTasks,
    current_user: User = Depends(get_current_user),
    db: AsyncSession = Depends(get_async_session)
):
    """
    Generate multiple QR code previews in a single request.
    
    This is useful for bulk operations like generating QR codes for events,
    inventory, or marketing campaigns.
    
    Rate Limits:
    - Free tier: Max 10 QR codes per batch
    - Starter tier: Max 20 QR codes per batch
    - Growth tier: Max 50 QR codes per batch
    - Enterprise tier: Max 100 QR codes per batch
    """
    try:
        # Apply rate limiting based on subscription tier
        tier_limits = {
            "free": 10,
            "starter": 20,
            "growth": 50,
            "enterprise": 100
        }
        
        # Get user's subscription tier (default to free if not set)
        user_tier = getattr(current_user, "subscription_tier", "free").lower()
        max_batch_size = tier_limits.get(user_tier, 10)
        
        # Check batch size
        if len(batch_request.items) > max_batch_size:
            raise HTTPException(
                status_code=400,
                detail=f"Batch size exceeds the limit of {max_batch_size} for your {user_tier} subscription tier"
            )
        
        # Check if we should process in background (for larger batches)
        process_in_background = len(batch_request.items) > 5
        
        if process_in_background:
            # Generate a task ID
            task_id = f"preview_batch_{int(time.time())}_{current_user.id}"
            
            # Start background task
            background_tasks.add_task(
                qr_preview_service.process_batch_preview,
                batch_request=batch_request,
                user_id=current_user.id,
                task_id=task_id
            )
            
            # Return immediate response
            return QRBatchPreviewResponse(
                status="processing",
                message=f"Processing {len(batch_request.items)} QR previews in background",
                task_id=task_id,
                previews=[]
            )
        else:
            # Process immediately for small batches
            previews = []
            
            # Process each QR code in the batch
            for item in batch_request.items:
                try:
                    # Generate preview (base64 for easy inclusion in response)
                    base64_preview = await qr_preview_service.generate_preview_base64(
                        qr_type=item.qr_type,
                        content=item.content,
                        design=item.design
                    )
                    
                    # Add to results
                    previews.append({
                        "id": item.id,
                        "image_data": f"data:image/png;base64,{base64_preview}",
                        "format": "png"
                    })
                except Exception as item_error:
                    # Log error but continue processing other items
                    logger.error(f"Error generating preview for item {item.id}: {str(item_error)}")
                    previews.append({
                        "id": item.id,
                        "error": str(item_error)
                    })
            
            # Return combined response
            return QRBatchPreviewResponse(
                status="completed",
                message=f"Generated {len(previews)} QR previews",
                previews=previews
            )
            
    except Exception as e:
        logger.error(f"Batch preview error: {str(e)}", exc_info=True)
        raise HTTPException(
            status_code=500,
            detail=f"Failed to process batch preview request: {str(e)}"
        )
