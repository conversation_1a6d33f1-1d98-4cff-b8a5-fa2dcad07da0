from pydantic import BaseModel, <PERSON>
from typing import Optional, Dict, Any, List, Union
from backend.models.qr import QRType
from datetime import datetime
import uuid
from uuid import UUID
from backend.schemas.qr_common import StandardResponse


class AIEnhancementOptions(BaseModel):
    pattern: Optional[str] = None
    marker_shape: Optional[str] = None
    overlay_url: Optional[str] = None
    logo_position: Optional[str] = None
    template_id: Optional[str] = None
    prompt: Optional[str] = None
    accessibility_features: Optional[List[str]] = None
    generate_variations: Optional[bool] = None
    optimize_for: Optional[List[str]] = None

class QRDesignOptions(BaseModel):
    # Primary colors for QR code elements
    foreground_color: str = "#000000"  # Main QR code elements color
    background_color: str = "#FFFFFF"  # QR code background color
    corner_radius: Optional[float] = 0
    ai_enhancement: Optional[AIEnhancementOptions] = None
    error_correction: Optional[str] = "M"  # L, M, Q, H
    pattern: Optional[str] = "classic"
    
    # Marker colors and styles
    marker_border_style: Optional[str] = "square"
    marker_border_color: Optional[str] = "#000000"  # Color for marker borders
    marker_center_style: Optional[str] = "square"
    marker_center_color: Optional[str] = "#000000"  # Color for marker centers
    custom_marker_color: Optional[bool] = False  # Use custom colors for markers
    different_markers_colors: Optional[bool] = False  # Use different colors for each marker
    
    # Logo settings
    logo_size: Optional[float] = 1.0  # Fractional logo size (0.0-1.0), default 100%
    logo_position: Optional[str] = "center"  # Always center for simplicity
    logo_opacity: Optional[float] = 1.0
    logo_remove_background: Optional[bool] = False  # Remove background from logo
    
    # Frame settings
    frame_style: Optional[str] = "classic"
    frame_color: Optional[str] = "#000000"  # Color for the frame
    frame_text: Optional[str] = ""
    frame_font: Optional[str] = "SF Pro"  # Font for frame text
    frame_font_size: Optional[int] = 16  # Font size for frame text
    frame_thickness: Optional[int] = 4
    
    # Color mode for advanced styling
    color_mode: Optional[str] = "solid"  # solid, gradient, custom
    
    # Animation settings
    animation_type: Optional[str] = None
    animation_speed: Optional[float] = 1.0
    animation_loop: Optional[bool] = False
    
    # Overlay and size settings
    overlay_url: Optional[str] = None
    overlay_blend_mode: Optional[str] = "normal"
    size: Optional[int] = 300
    margin: Optional[int] = 4
    
    class Config:
        schema_extra = {
            "description": "Standardized QR code design options",
            "example": {
                "foreground_color": "#000000",
                "background_color": "#FFFFFF",
                "marker_border_color": "#000000",
                "marker_center_color": "#000000",
                "frame_color": "#000000"
            }
        }

class QRPreviewRequest(BaseModel):
    qr_type: QRType = Field(..., alias="qrType")
    content: Dict[str, Any] = Field(..., alias="data")
    design: Optional[QRDesignOptions] = Field(None, alias="designOptions")
    include_logo: Optional[bool] = False
    dimensions: Optional[int] = 300
    
    class Config:
        allow_population_by_field_name = True
        json_encoders = {
            datetime: lambda v: v.isoformat()
        }

class QRPreviewImageData(BaseModel):
    """Data model for QR preview image information"""
    image_data: str  # Base64 encoded image
    format: str = "png"
    width: int = 300
    height: int = 300

class QRPreviewResponse(StandardResponse):
    """Response for QR preview requests"""
    success: bool = True
    data: Optional[QRPreviewImageData] = None
    error: Optional[Dict[str, Any]] = None

class QRExportOptions(BaseModel):
    format: str = "png"  # png, svg, pdf
    width: int = 1024
    height: int = 1024
    dpi: Optional[int] = 300  # For PDF
    include_logo: bool = True
    include_border: bool = True
    border_size: Optional[int] = 4

class QRDownloadRequest(BaseModel):
    format: str = "png"  # png, svg, pdf
    dimensions: int = 1024  # Square dimensions
    with_logo: bool = True

class QRBatchGenerationItem(BaseModel):
    qr_type: QRType
    content: Dict[str, Any]
    design: Optional[QRDesignOptions] = None
    reference_id: Optional[str] = None  # Client-side reference

class QRBatchGenerationRequest(BaseModel):
    items: List[QRBatchGenerationItem]
    team_id: Optional[UUID] = None
    export_options: Optional[QRExportOptions] = None

class QRBatchItemData(BaseModel):
    """Data model for individual QR batch item response"""
    qr_id: UUID
    reference_id: Optional[str] = None
    preview_url: str
    download_urls: Dict[str, str]  # Format -> URL mapping

class QRBatchItemResponse(StandardResponse[QRBatchItemData]):
    """Standardized response for a single batch item."""
    pass

class QRBatchGenerationData(BaseModel):
    """Data model for batch generation response"""
    batch_id: UUID
    items: List[QRBatchItemData]
    created_at: datetime
    download_all_url: Optional[str] = None

class QRBatchGenerationResponse(StandardResponse[QRBatchGenerationData]):
    """Standardized response for batch generation."""
    pass

class QRBatchPreviewItem(BaseModel):
    id: str  # Client-side reference ID
    qr_type: QRType
    content: Dict[str, Any]
    design: Optional[QRDesignOptions] = None

class QRBatchPreviewRequest(BaseModel):
    """Request for generating multiple QR code previews in a single batch"""
    items: List[QRBatchPreviewItem]
    format: str = "png"
    dimensions: int = 300
    include_ai_enhancement: bool = False

class QRBatchPreviewItemData(BaseModel):
    """Data model for a single QR code preview in a batch"""
    id: str  # Same reference ID from request
    image_data: Optional[str] = None  # Base64 encoded image with data URI prefix
    format: Optional[str] = None

class QRBatchPreviewItemResponse(StandardResponse[QRBatchPreviewItemData]):
    """Standardized response for a single QR preview in a batch."""
    pass

class QRBatchPreviewData(BaseModel):
    """Data model for batch preview response"""
    status: str  # "completed", "processing", "failed"
    message: str
    task_id: Optional[str] = None  # For background processing
    previews: List[QRBatchPreviewItemData] = []

class QRBatchPreviewResponse(StandardResponse[QRBatchPreviewData]):
    """Standardized response for a batch QR code preview request."""
    pass
