import io
import json
import logging
import hashlib
from typing import Dict, Any, Optional, Union
import base64
import svgwrite
from qrcodegen import QrCode
from fastapi import UploadFile
from uuid import UUID
import re
import cairosvg
from PIL import Image
import io

from backend.schemas.qr_preview import QRDesignOptions, AIEnhancementOptions
from backend.services.color_utils import ensure_rgb
from backend.models.qr import QRType
from backend.services.qr_preview_cache import get_qr_preview_cache

logger = logging.getLogger(__name__)

class QRPreviewService:
    """Service for generating QR code previews with full SVG modular customization"""
    
    def __init__(self):
        self.error_correction_levels = {
            'L': QrCode.Ecc.LOW,    # 7% recovery
            'M': QrCode.Ecc.MEDIUM, # 15% recovery
            'Q': QrCode.Ecc.QUARTILE, # 25% recovery
            'H': QrCode.Ecc.HIGH    # 30% recovery
        }



    @staticmethod
    def _is_finder_pattern(x, y, size):
        # Top-left, top-right, bottom-left
        return ((x < 7 and y < 7) or (x > size-8 and y < 7) or (x < 7 and y > size-8))

    @staticmethod
    def _draw_module(dwg, x, y, module_size, color, style, offset_x=0, offset_y=0):
        cx = x * module_size + offset_x
        cy = y * module_size + offset_y

        if style == 'circle' or style == 'dots':
            # Circular modules
            dwg.add(dwg.circle(center=(cx+module_size/2, cy+module_size/2), r=module_size/2.2, fill=color, class_='qr-module', id='module-{}-{}'.format(x, y)))
        elif style == 'rounded':
            # Rounded square modules
            dwg.add(dwg.rect(insert=(cx, cy), size=(module_size, module_size), fill=color, rx=module_size*0.2, ry=module_size*0.2, class_='qr-module', id='module-{}-{}'.format(x, y)))
        elif style == 'diamond':
            # Diamond-shaped modules (rotated square)
            transform = f"rotate(45 {cx+module_size/2} {cy+module_size/2})"
            dwg.add(dwg.rect(insert=(cx+module_size*0.15, cy+module_size*0.15), size=(module_size*0.7, module_size*0.7), fill=color, transform=transform, class_='qr-module', id=f'module-{x}-{y}'))
        elif style == 'tiny-dots':
            # Smaller circular modules
            dwg.add(dwg.circle(center=(cx+module_size/2, cy+module_size/2), r=module_size/3, fill=color, class_='qr-module', id=f'module-{x}-{y}'))
        elif style == 'classy':
            # Square with border effect
            dwg.add(dwg.rect(insert=(cx+module_size*0.1, cy+module_size*0.1), size=(module_size*0.8, module_size*0.8), fill=color, class_='qr-module', id=f'module-{x}-{y}'))
        elif style == 'classy-rounded':
            # Rounded square with border effect
            dwg.add(dwg.rect(insert=(cx+module_size*0.1, cy+module_size*0.1), size=(module_size*0.8, module_size*0.8), fill=color, rx=module_size*0.15, ry=module_size*0.15, class_='qr-module', id=f'module-{x}-{y}'))
        elif style == 'horizontal':
            # Horizontal rectangular modules
            dwg.add(dwg.rect(insert=(cx, cy+module_size*0.3), size=(module_size, module_size*0.4), fill=color, class_='qr-module', id=f'module-{x}-{y}'))
        elif style == 'vertical':
            # Vertical rectangular modules
            dwg.add(dwg.rect(insert=(cx+module_size*0.3, cy), size=(module_size*0.4, module_size), fill=color, class_='qr-module', id=f'module-{x}-{y}'))
        elif style == 'diagonal':
            # Diagonal line modules
            transform = f"rotate(45 {cx+module_size/2} {cy+module_size/2})"
            dwg.add(dwg.rect(insert=(cx+module_size*0.1, cy+module_size*0.4), size=(module_size*0.8, module_size*0.2), fill=color, transform=transform, class_='qr-module', id=f'module-{x}-{y}'))
        elif style == 'mosaic':
            # Small squares with gaps (mosaic effect)
            dwg.add(dwg.rect(insert=(cx+module_size*0.15, cy+module_size*0.15), size=(module_size*0.7, module_size*0.7), fill=color, class_='qr-module', id=f'module-{x}-{y}'))
        elif style == 'mini':
            # Smaller square modules
            dwg.add(dwg.rect(insert=(cx+module_size*0.25, cy+module_size*0.25), size=(module_size*0.5, module_size*0.5), fill=color, class_='qr-module', id=f'module-{x}-{y}'))
        elif style == 'blocks':
            # Larger block modules (slightly bigger than normal)
            dwg.add(dwg.rect(insert=(cx-module_size*0.05, cy-module_size*0.05), size=(module_size*1.1, module_size*1.1), fill=color, class_='qr-module', id=f'module-{x}-{y}'))
        elif style == 'circles':
            # Circles with border effect
            dwg.add(dwg.circle(center=(cx+module_size/2, cy+module_size/2), r=module_size/2.2, fill=color, stroke='white', stroke_width=module_size*0.05, class_='qr-module', id=f'module-{x}-{y}'))
        else:
            # Default square modules (classic, square, or any unrecognized style)
            dwg.add(dwg.rect(insert=(cx, cy), size=(module_size, module_size), fill=color, class_='qr-module', id=f'module-{x}-{y}'))

    @staticmethod
    def _draw_finder_marker(dwg, mx, my, module_size, border_style, center_style, border_color, center_color, offset_x=0, offset_y=0):
        import logging
        logger = logging.getLogger(__name__)

        # Debug logging
        logger.debug(f"_draw_finder_marker called: mx={mx}, my={my}, module_size={module_size}, border_style={border_style}, center_style={center_style}, border_color={border_color}, center_color={center_color}, offset=({offset_x}, {offset_y})")

        # Create marker group
        g = dwg.g(class_='qr-marker', id=f'marker-{mx}-{my}')

        # Calculate positions and sizes
        outer_x, outer_y = mx*module_size + offset_x, my*module_size + offset_y
        outer_size = 7*module_size
        inner_x, inner_y = mx*module_size+module_size*1.2 + offset_x, my*module_size+module_size*1.2 + offset_y
        inner_size = 4.6*module_size
        center_x, center_y = mx*module_size+module_size*2.5 + offset_x, my*module_size+module_size*2.5 + offset_y
        center_size = 2*module_size

        logger.debug(f"Creating finder pattern group with id='marker-{mx}-{my}'")

        # Draw border based on border_style
        if border_style == 'circle':
            cx = mx * module_size + 3.5 * module_size + offset_x
            cy = my * module_size + 3.5 * module_size + offset_y
            r = 3.5 * module_size
            logger.debug(f"Drawing circle border at center=({cx}, {cy}), r={r}")
            g.add(dwg.circle(center=(cx, cy), r=r, fill=border_color, class_='qr-marker-outer'))
            g.add(dwg.circle(center=(cx, cy), r=r*0.6, fill='#fff', class_='qr-marker-inner'))
        elif border_style == 'rounded':
            logger.debug(f"Drawing rounded border at ({outer_x}, {outer_y})")
            corner_radius = module_size * 0.8
            g.add(dwg.rect(insert=(outer_x, outer_y), size=(outer_size, outer_size), fill=border_color, rx=corner_radius, ry=corner_radius, class_='qr-marker-outer'))
            g.add(dwg.rect(insert=(inner_x, inner_y), size=(inner_size, inner_size), fill='#fff', rx=corner_radius*0.6, ry=corner_radius*0.6, class_='qr-marker-inner'))
        elif border_style == 'diamond':
            # Diamond-shaped border (rotated square)
            outer_center_x, outer_center_y = outer_x + outer_size/2, outer_y + outer_size/2
            inner_center_x, inner_center_y = inner_x + inner_size/2, inner_y + inner_size/2

            transform_outer = f"rotate(45 {outer_center_x} {outer_center_y})"
            transform_inner = f"rotate(45 {inner_center_x} {inner_center_y})"

            g.add(dwg.rect(insert=(outer_x, outer_y), size=(outer_size, outer_size), fill=border_color, transform=transform_outer, class_='qr-marker-outer'))
            g.add(dwg.rect(insert=(inner_x, inner_y), size=(inner_size, inner_size), fill='#fff', transform=transform_inner, class_='qr-marker-inner'))
        elif border_style == 'extra-rounded':
            # Extra rounded border
            corner_radius = module_size * 1.5
            g.add(dwg.rect(insert=(outer_x, outer_y), size=(outer_size, outer_size), fill=border_color, rx=corner_radius, ry=corner_radius, class_='qr-marker-outer'))
            g.add(dwg.rect(insert=(inner_x, inner_y), size=(inner_size, inner_size), fill='#fff', rx=corner_radius*0.6, ry=corner_radius*0.6, class_='qr-marker-inner'))
        elif border_style == 'cut-corner':
            # Cut corner border (square with cut corners)
            cut_size = module_size * 0.8
            clip_path = f"polygon({cut_size}px 0px, 100% 0px, 100% calc(100% - {cut_size}px), calc(100% - {cut_size}px) 100%, 0px 100%, 0px {cut_size}px)"

            g.add(dwg.rect(insert=(outer_x, outer_y), size=(outer_size, outer_size), fill=border_color, style=f"clip-path: {clip_path}", class_='qr-marker-outer'))
            g.add(dwg.rect(insert=(inner_x, inner_y), size=(inner_size, inner_size), fill='#fff', class_='qr-marker-inner'))
        elif border_style == 'dotted':
            # Dotted border
            stroke_width = module_size * 0.3
            g.add(dwg.rect(insert=(outer_x, outer_y), size=(outer_size, outer_size), fill='none', stroke=border_color, stroke_width=stroke_width, stroke_dasharray=f"{stroke_width},{stroke_width}", class_='qr-marker-outer'))
            g.add(dwg.rect(insert=(inner_x, inner_y), size=(inner_size, inner_size), fill='#fff', class_='qr-marker-inner'))
        elif border_style == 'dashed':
            # Dashed border
            stroke_width = module_size * 0.3
            dash_length = module_size * 0.8
            g.add(dwg.rect(insert=(outer_x, outer_y), size=(outer_size, outer_size), fill='none', stroke=border_color, stroke_width=stroke_width, stroke_dasharray=f"{dash_length},{dash_length*0.5}", class_='qr-marker-outer'))
            g.add(dwg.rect(insert=(inner_x, inner_y), size=(inner_size, inner_size), fill='#fff', class_='qr-marker-inner'))
        else:
            # Default square border
            logger.debug(f"Drawing square border:")
            logger.debug(f"  Outer: insert=({outer_x}, {outer_y}), size=({outer_size}, {outer_size})")
            logger.debug(f"  Inner: insert=({inner_x}, {inner_y}), size=({inner_size}, {inner_size})")

            g.add(dwg.rect(insert=(outer_x, outer_y), size=(outer_size, outer_size), fill=border_color, class_='qr-marker-outer'))
            g.add(dwg.rect(insert=(inner_x, inner_y), size=(inner_size, inner_size), fill='#fff', class_='qr-marker-inner'))

        # Draw center based on center_style
        QRPreviewService._draw_marker_center(dwg, g, center_x, center_y, center_size, center_style, center_color, module_size)

        dwg.add(g)
        logger.debug(f"Successfully added finder pattern group to SVG")

    @staticmethod
    def _draw_marker_center(dwg, group, center_x, center_y, center_size, center_style, center_color, module_size):
        """Draw the center part of the marker based on center_style"""
        import logging
        logger = logging.getLogger(__name__)

        logger.debug(f"Drawing marker center: style={center_style}, color={center_color}, pos=({center_x}, {center_y}), size={center_size}")

        if center_style == 'circle':
            # Circle center
            cx = center_x + center_size/2
            cy = center_y + center_size/2
            r = center_size/2
            group.add(dwg.circle(center=(cx, cy), r=r, fill=center_color, class_='qr-marker-center'))
        elif center_style == 'dot':
            # Small dot center
            cx = center_x + center_size/2
            cy = center_y + center_size/2
            r = center_size/3
            group.add(dwg.circle(center=(cx, cy), r=r, fill=center_color, class_='qr-marker-center'))
        elif center_style == 'diamond':
            # Diamond center (rotated square)
            center_center_x, center_center_y = center_x + center_size/2, center_y + center_size/2
            transform = f"rotate(45 {center_center_x} {center_center_y})"
            group.add(dwg.rect(insert=(center_x, center_y), size=(center_size, center_size), fill=center_color, transform=transform, class_='qr-marker-center'))
        elif center_style == 'star':
            # Star center using SVG path
            cx = center_x + center_size/2
            cy = center_y + center_size/2
            r = center_size/2
            # Create a simple 5-point star
            star_path = f"M{cx},{cy-r} L{cx+r*0.3},{cy-r*0.3} L{cx+r},{cy-r*0.3} L{cx+r*0.5},{cy+r*0.2} L{cx+r*0.8},{cy+r} L{cx},{cy+r*0.6} L{cx-r*0.8},{cy+r} L{cx-r*0.5},{cy+r*0.2} L{cx-r},{cy-r*0.3} L{cx-r*0.3},{cy-r*0.3} Z"
            group.add(dwg.path(d=star_path, fill=center_color, class_='qr-marker-center'))
        elif center_style == 'plus':
            # Plus/cross center
            thickness = center_size * 0.3
            # Horizontal bar
            group.add(dwg.rect(insert=(center_x, center_y + center_size/2 - thickness/2), size=(center_size, thickness), fill=center_color, class_='qr-marker-center'))
            # Vertical bar
            group.add(dwg.rect(insert=(center_x + center_size/2 - thickness/2, center_y), size=(thickness, center_size), fill=center_color, class_='qr-marker-center'))
        elif center_style == 'heart':
            # Heart center using SVG path
            cx = center_x + center_size/2
            cy = center_y + center_size/2
            s = center_size * 0.4  # scale factor
            heart_path = f"M{cx},{cy+s*0.3} C{cx-s*0.8},{cy-s*0.5} {cx-s*0.8},{cy-s*1.2} {cx},{cy-s*0.8} C{cx+s*0.8},{cy-s*1.2} {cx+s*0.8},{cy-s*0.5} {cx},{cy+s*0.3} Z"
            group.add(dwg.path(d=heart_path, fill=center_color, class_='qr-marker-center'))
        elif center_style == 'cross':
            # X-shaped cross center
            thickness = center_size * 0.2
            # Diagonal line 1
            group.add(dwg.line(start=(center_x, center_y), end=(center_x + center_size, center_y + center_size), stroke=center_color, stroke_width=thickness, class_='qr-marker-center'))
            # Diagonal line 2
            group.add(dwg.line(start=(center_x + center_size, center_y), end=(center_x, center_y + center_size), stroke=center_color, stroke_width=thickness, class_='qr-marker-center'))
        elif center_style == 'rhombus':
            # Rhombus center (same as diamond but different name for compatibility)
            center_center_x, center_center_y = center_x + center_size/2, center_y + center_size/2
            transform = f"rotate(45 {center_center_x} {center_center_y})"
            group.add(dwg.rect(insert=(center_x, center_y), size=(center_size, center_size), fill=center_color, transform=transform, class_='qr-marker-center'))
        elif center_style == 'triangle':
            # Triangle center
            cx = center_x + center_size/2
            cy = center_y + center_size/2
            r = center_size/2
            triangle_path = f"M{cx},{cy-r} L{cx+r*0.866},{cy+r*0.5} L{cx-r*0.866},{cy+r*0.5} Z"
            group.add(dwg.path(d=triangle_path, fill=center_color, class_='qr-marker-center'))
        elif center_style == 'ring':
            # Ring center (circle with hole)
            cx = center_x + center_size/2
            cy = center_y + center_size/2
            outer_r = center_size/2
            inner_r = center_size/4
            group.add(dwg.circle(center=(cx, cy), r=outer_r, fill=center_color, class_='qr-marker-center'))
            group.add(dwg.circle(center=(cx, cy), r=inner_r, fill='#fff', class_='qr-marker-center-hole'))
        else:
            # Default square center
            group.add(dwg.rect(insert=(center_x, center_y), size=(center_size, center_size), fill=center_color, class_='qr-marker-center'))

    @staticmethod
    def _draw_frame(dwg, dimensions, design, offset_x=0, offset_y=0):
        """Draw frame around QR code based on frame_style"""
        import logging
        logger = logging.getLogger(__name__)

        frame_style = getattr(design, 'frame_style', 'classic')
        frame_color = getattr(design, 'frame_color', '#000000')
        frame_thickness = getattr(design, 'frame_thickness', 4)

        logger.debug(f"Drawing frame: style={frame_style}, color={frame_color}, thickness={frame_thickness}")

        # Calculate frame dimensions relative to QR code position
        frame_margin = 15  # Space between QR code and frame
        frame_x = offset_x - frame_margin
        frame_y = offset_y - frame_margin
        frame_width = dimensions + (frame_margin * 2)
        frame_height = dimensions + (frame_margin * 2)

        if frame_style == 'classic':
            # Simple rectangular frame
            dwg.add(dwg.rect(
                insert=(frame_x, frame_y),
                size=(frame_width, frame_height),
                fill='none',
                stroke=frame_color,
                stroke_width=frame_thickness,
                class_='qr-frame-classic'
            ))

        elif frame_style == 'modern':
            # Modern frame with gradient effect
            # Create unique gradient definition
            import time
            gradient_id = f'frame-gradient-{int(time.time() * 1000)}'
            gradient = dwg.defs.add(dwg.linearGradient(id=gradient_id))
            gradient.add_stop_color(0, frame_color)
            gradient.add_stop_color(1, frame_color, opacity=0.7)

            dwg.add(dwg.rect(
                insert=(frame_x, frame_y),
                size=(frame_width, frame_height),
                fill='none',
                stroke=f'url(#{gradient_id})',
                stroke_width=frame_thickness * 1.5,
                class_='qr-frame-modern'
            ))

        elif frame_style == 'rounded':
            # Rounded corner frame
            corner_radius = frame_thickness * 3
            dwg.add(dwg.rect(
                insert=(frame_x, frame_y),
                size=(frame_width, frame_height),
                fill='none',
                stroke=frame_color,
                stroke_width=frame_thickness,
                rx=corner_radius,
                ry=corner_radius,
                class_='qr-frame-rounded'
            ))

        elif frame_style == 'bold':
            # Bold double frame
            # Outer frame
            dwg.add(dwg.rect(
                insert=(frame_x, frame_y),
                size=(frame_width, frame_height),
                fill='none',
                stroke=frame_color,
                stroke_width=frame_thickness * 2,
                class_='qr-frame-bold-outer'
            ))
            # Inner frame
            inner_margin = 8
            inner_x = frame_x + inner_margin
            inner_y = frame_y + inner_margin
            inner_width = frame_width - (inner_margin * 2)
            inner_height = frame_height - (inner_margin * 2)
            dwg.add(dwg.rect(
                insert=(inner_x, inner_y),
                size=(inner_width, inner_height),
                fill='none',
                stroke=frame_color,
                stroke_width=frame_thickness,
                class_='qr-frame-bold-inner'
            ))

        elif frame_style == 'minimal':
            # Minimal thin frame
            dwg.add(dwg.rect(
                insert=(frame_x, frame_y),
                size=(frame_width, frame_height),
                fill='none',
                stroke=frame_color,
                stroke_width=1,
                class_='qr-frame-minimal'
            ))

        logger.debug(f"Successfully drew {frame_style} frame")

    @staticmethod
    def _draw_label(dwg, dimensions, design, offset_x=0, offset_y=0):
        """Draw frame label text in a separate bar below the QR code (like QRMonkey)"""
        import logging
        logger = logging.getLogger(__name__)

        frame_text = getattr(design, 'frame_text', '')
        frame_font = getattr(design, 'frame_font', 'SF Pro')
        frame_font_size = getattr(design, 'frame_font_size', 16)
        frame_color = getattr(design, 'frame_color', '#000000')

        # Map font names to web-safe alternatives for better SVG compatibility
        font_mapping = {
            'SF Pro': 'system-ui, -apple-system, BlinkMacSystemFont, "Segoe UI", sans-serif',
            'Inter': 'Inter, system-ui, sans-serif',
            'system-ui': 'system-ui, sans-serif',
            'Arial': 'Arial, sans-serif'
        }

        # Use mapped font or fallback to sans-serif
        mapped_font = font_mapping.get(frame_font, f'{frame_font}, sans-serif')

        if not frame_text:
            return

        logger.debug(f"Drawing frame label: text='{frame_text}', font={frame_font}, size={frame_font_size}, color={frame_color}")

        # Calculate label bar dimensions - positioned completely outside the frame
        frame_style = getattr(design, 'frame_style', 'classic')
        frame_margin = 20 if frame_style and frame_style != 'none' else 0

        # Label bar positioning - below the entire QR + frame area
        label_bar_width = dimensions + (2 * frame_margin)  # Same width as frame area
        label_bar_height = 40  # Fixed height for label bar
        label_bar_x = offset_x - frame_margin  # Align with frame
        label_bar_y = offset_y + dimensions + frame_margin + 10  # Below frame with gap

        # Create label background bar (like QRMonkey's black bar)
        label_bg = dwg.rect(
            insert=(label_bar_x, label_bar_y),
            size=(label_bar_width, label_bar_height),
            fill='#000000',  # Black background like QRMonkey
            rx=5,  # Slightly rounded corners
            class_='qr-label-background'
        )
        dwg.add(label_bg)

        # Position text in center of label bar
        label_text_x = label_bar_x + (label_bar_width / 2)
        label_text_y = label_bar_y + (label_bar_height / 2) + (frame_font_size / 3)  # Center vertically

        logger.debug(f"Label bar: x={label_bar_x}, y={label_bar_y}, width={label_bar_width}, height={label_bar_height}")
        logger.debug(f"Label text position: x={label_text_x}, y={label_text_y}")

        # Create text element with white text on black background
        text_element = dwg.text(
            frame_text,
            insert=(label_text_x, label_text_y),
            text_anchor='middle',
            font_size=f"{frame_font_size}px",
            font_family=mapped_font,
            fill='#FFFFFF',  # White text on black background
            font_weight='bold',
            class_='qr-frame-label'
        )

        dwg.add(text_element)
        logger.debug(f"Successfully added label bar with text: '{frame_text}'")
        logger.debug(f"Successfully drew frame label")

    @staticmethod
    async def _draw_logo(dwg, logo, dimensions, design, offset_x=0, offset_y=0):
        """
        Robustly embed a logo in the SVG QR code.
        Supports UploadFile, bytes, and (optionally) remote URL fetch.
        Handles size, opacity, and position from design options.
        Logs and fails gracefully on error.
        """
        import base64
        import io
        import logging
        logger = logging.getLogger(__name__)
        logger.debug(f"_draw_logo called with logo type: {type(logo)}, dimensions: {dimensions}")
        logo_bytes = None
        # Accept UploadFile, bytes, or (optionally) str URL
        try:
            if isinstance(logo, UploadFile):
                logo_bytes = await logo.read()
            elif isinstance(logo, bytes):
                logo_bytes = logo
            elif isinstance(logo, str) and logo.startswith('http'):
                # Optional: fetch logo from URL (robust, with timeout)
                import requests
                try:
                    resp = requests.get(logo, timeout=5)
                    resp.raise_for_status()
                    logo_bytes = resp.content
                except Exception as e:
                    logger.error(f"Failed to fetch logo from URL: {logo} - {e}")
                    return
            else:
                logger.error("Unsupported logo type for SVG embedding.")
                return
            # Validate and process image
            try:
                from PIL import Image
                img = Image.open(io.BytesIO(logo_bytes))
                # Validate image format
                if img.format not in ("PNG", "JPEG", "JPG", "WEBP"):
                    logger.error(f"Unsupported logo image format: {img.format}")
                    return
                # Resize logo - convert fractional size to pixels
                logo_size_fraction = getattr(design, 'logo_size', 0.3) if design else 0.3
                # Calculate proportional size based on QR code dimensions
                # The logo_size_fraction directly represents the percentage of QR code size
                logo_size = int(dimensions * logo_size_fraction)
                # Ensure minimum and maximum logo sizes
                logo_size = max(20, min(logo_size, dimensions // 2))

                # Remove background if requested
                remove_bg = getattr(design, 'logo_remove_background', False) if design else False
                if remove_bg:
                    try:
                        # Convert to RGBA if not already
                        if img.mode != 'RGBA':
                            img = img.convert('RGBA')

                        # Simple background removal - make white/light pixels transparent
                        data = img.getdata()
                        new_data = []
                        for item in data:
                            # If pixel is close to white (RGB > 240), make it transparent
                            if len(item) >= 3 and item[0] > 240 and item[1] > 240 and item[2] > 240:
                                new_data.append((255, 255, 255, 0))  # Transparent
                            else:
                                new_data.append(item)
                        img.putdata(new_data)
                        logger.debug("Applied background removal to logo")
                    except Exception as e:
                        logger.warning(f"Background removal failed, using original logo: {e}")

                try:
                    img = img.resize((logo_size, logo_size), Image.Resampling.LANCZOS)
                except Exception as e:
                    logger.error(f"Logo resize failed: {e}")
                    return
                buf = io.BytesIO()
                img.save(buf, format='PNG')
                b64 = base64.b64encode(buf.getvalue()).decode()
            except Exception as e:
                logger.error(f"Failed to process logo image: {e}")
                return
            # Positioning - always center for best QR code scannability
            # Center the logo within the QR code area, accounting for SVG offsets
            x = offset_x + (dimensions - logo_size) // 2
            y = offset_y + (dimensions - logo_size) // 2
            # Opacity
            opacity = getattr(design, 'logo_opacity', 1.0) if design else 1.0
            # Embed as SVG <image>
            dwg.add(dwg.image(href=f'data:image/png;base64,{b64}', insert=(x, y), size=(logo_size, logo_size), opacity=opacity, class_='qr-logo'))
        except Exception as e:
            logger.error(f"General error embedding logo in SVG: {e}")
            return

    async def generate_preview(
        self,
        qr_type: QRType,
        content: Dict[str, Any],
        design: Optional[QRDesignOptions] = None,
        logo: Optional[Union[UploadFile, bytes]] = None,
        dimensions: int = 300
    ) -> bytes:
        """
        Generate a QR code preview with current settings without saving to database
        
        Args:
            qr_type: Type of QR code
            content: Content data for QR code
            design: Design customization options
            logo: Optional logo to embed (can be UploadFile or bytes)
            dimensions: Output dimensions (width/height)
            
        Returns:
            QR code image as bytes
        """
        # Get cache instance
        from backend.services.qr_preview_cache import get_qr_preview_cache
        cache = get_qr_preview_cache()
        
        # Additional logging for debugging
        logger.debug(f"QR preview request received - type: {qr_type}, content keys: {list(content.keys()) if content else 'None'}")
        logger.debug(f"Logo type: {type(logo)}")
        
        # Handle design options properly to avoid serialization issues
        design_dict = None
        if design:
            try:
                design_dict = design.dict()
                # Log the design parameters but avoid excessive output
                logger.debug(f"Design options summary: pattern={design_dict.get('pattern')}, "
                         f"colors={design_dict.get('colors')}, "
                         f"logo_size={design_dict.get('logo_size')}, "
                         f"logo_position={design_dict.get('logo_position')}")
            except Exception as e:
                logger.error(f"Error converting design options to dict: {str(e)}", exc_info=True)
                # Use default design if conversion fails
                design = QRDesignOptions(color="#000000", background_color="#FFFFFF")
                design_dict = design.dict()
        
        # Create a cache key dictionary from the request - ensuring all objects are JSON serializable
        try:
            # Determine logo identifier for caching
            logo_identifier = None
            if logo is not None:
                if isinstance(logo, UploadFile):
                    logo_identifier = f"logo_file_{logo.filename}"
                elif isinstance(logo, bytes):
                    # Use a hash of the bytes for cache key
                    logo_hash = hashlib.md5(logo).hexdigest()
                    logo_identifier = f"logo_bytes_{logo_hash}"
                else:
                    logger.warning(f"Unknown logo type: {type(logo)}, using type as identifier")
                    logo_identifier = f"logo_type_{type(logo).__name__}"
            
            # Include all relevant design parameters in the cache key
            cache_data = {
                "qr_type": qr_type.value if hasattr(qr_type, 'value') else str(qr_type),
                "data": content,
                "dimensions": dimensions,
                "logo": logo_identifier,
            }
            
            # Add all design parameters to the cache key
            if design_dict:
                for key, value in design_dict.items():
                    if key not in ["ai_enhancement"]:  # Skip complex objects
                        cache_data[f"design_{key}"] = value
            
            # Create a cache key from the data
            cache_key = json.dumps(cache_data, sort_keys=True, default=str)
            cache_key_hash = hashlib.md5(cache_key.encode()).hexdigest()
            
            # Check if we have this QR in cache
            cached_qr = await cache.get(cache_key_hash)
            if cached_qr:
                logger.debug(f"Cache hit for QR preview")
                return cached_qr
                
            logger.debug(f"Cache miss for QR preview, generating new image")
        except Exception as e:
            logger.error(f"Error preparing cache data: {str(e)}", exc_info=True)
            # Continue without caching
            cache_key_hash = None
        
        try:
            # Format the data according to QR type
            qr_data = self._format_qr_data(qr_type, content)
            logger.debug(f"Formatted QR data for type {qr_type}")
            
            # Generate SVG string using modular logic
            svg_string = await self.generate_preview_svg_modular(qr_type, content, design, logo, dimensions)
            logger.debug(f"Generated SVG string for QR preview, length: {len(svg_string)}")

            # Convert SVG to PNG using CairoSVG
            import cairosvg
            png_bytes = cairosvg.svg2png(bytestring=svg_string.encode('utf-8'), output_width=dimensions, output_height=dimensions)
            logger.debug(f"Converted SVG to PNG bytes: {len(png_bytes)} bytes")

            # Logo is already embedded in SVG, no need for PNG overlay

            qr_bytes = png_bytes
            logger.debug(f"Final QR preview PNG bytes: {len(qr_bytes)} bytes")
            
            # Logo is already handled in SVG generation, no need for duplicate processing
            qr_bytes = png_bytes
            
            # Cache the result if caching is available
            if cache_key_hash:
                await cache.set(cache_key_hash, qr_bytes)
                logger.debug(f"Cached QR preview with key: {cache_key_hash}")
            
            return qr_bytes
            
        except Exception as e:
            logger.error(f"Error generating QR preview: {str(e)}", exc_info=True)
            raise Exception(f"Failed to generate QR preview: {str(e)}")
            
    async def generate_preview_base64(
        self,
        qr_type: QRType,
        content: Dict[str, Any],
        design: Optional[QRDesignOptions] = None,
        logo: Optional[Union[UploadFile, bytes]] = None,
        dimensions: int = 300
    ) -> str:
        """
        Generate a QR code preview and return as base64 string
        
        Args:
            qr_type: Type of QR code
            content: Content data for QR code
            design: Design customization options
            logo: Optional logo to embed
            dimensions: Output dimensions
            
        Returns:
            Base64 encoded QR code image
        """
        logger.debug(f"Generating QR preview as base64 for type: {qr_type}")
        qr_bytes = await self.generate_preview(qr_type, content, design, logo, dimensions)
        base64_data = base64.b64encode(qr_bytes).decode('utf-8')
        # Add data URL prefix for direct use in HTML/CSS
        base64_data = f"data:image/png;base64,{base64_data}"
        logger.debug(f"Generated base64 QR preview with length: {len(base64_data)}")
        return base64_data
        
    async def generate_preview_svg(
        self,
        qr_type: QRType,
        content: Dict[str, Any],
        design: Optional[QRDesignOptions] = None,
        logo: Optional[Union[UploadFile, bytes]] = None,
        dimensions: int = 300
    ) -> str:
        """
        Generate a modular SVG QR code preview (SVG only, no PNG legacy logic).
        """
        return await self.generate_preview_svg_modular(qr_type, content, design, logo, dimensions)
        
    async def generate_preview_svg_modular(
        self,
        qr_type: QRType,
        content: Dict[str, Any],
        design: Optional[QRDesignOptions] = None,
        logo: Optional[Union[UploadFile, bytes]] = None,
        dimensions: int = 300
    ) -> str:
        """
        Generate a modular SVG QR code preview with advanced customization for preview.
        """
        # Format QR data
        qr_data = self._format_qr_data(qr_type, content)
        error_correction = self.error_correction_levels.get(
            getattr(design, 'error_correction', 'M'), QrCode.Ecc.MEDIUM)
        # Generate QR matrix
        qr = QrCode.encode_text(qr_data, error_correction)
        size = qr.get_size()
        # Calculate SVG dimensions based on frame and label requirements
        frame_style = getattr(design, 'frame_style', None) if design else None
        frame_text = getattr(design, 'frame_text', '') if design else ''

        # Base margin for QR code
        base_margin = 5

        # Additional space for frame
        frame_margin = 20 if frame_style and frame_style != 'none' else 0

        # Additional space for label bar - positioned outside frame
        label_margin = 60 if frame_text else 0  # 40px bar + 10px gap + 10px bottom margin

        # Calculate total dimensions
        total_margin = base_margin + frame_margin
        svg_width = dimensions + (2 * total_margin)
        svg_height = dimensions + (2 * total_margin) + label_margin

        # For centering: if no frame, center the QR code in the SVG
        if frame_style == 'none' or not frame_style:
            # Use a consistent SVG size but center the QR code
            svg_width = dimensions + 40  # Consistent padding
            svg_height = dimensions + 40 + label_margin
            offset_x = 20  # Center horizontally
            offset_y = 20  # Center vertically
        else:
            offset_x = total_margin
            offset_y = total_margin

        # Create SVG with calculated dimensions and proper scaling
        dwg = svgwrite.Drawing(
            size=(svg_width, svg_height),
            viewBox=f"0 0 {svg_width} {svg_height}",
            preserveAspectRatio="xMidYMid meet"
        )

        # Calculate module size
        effective_dimensions = dimensions - (2 * base_margin)
        module_size = effective_dimensions / size
        # Colors & style
        fg_color = getattr(design, 'foreground_color', '#000') if design else '#000'
        bg_color = getattr(design, 'background_color', '#fff') if design else '#fff'
        # Draw background
        dwg.add(dwg.rect(insert=(0, 0), size=(svg_width, svg_height), fill=bg_color, id='qr-bg', class_='qr-bg'))
        # Draw modules (pattern) - EXCLUDE finder patterns
        pattern_style = getattr(design, 'pattern', 'square') if design else 'square'
        for y in range(size):
            for x in range(size):
                if qr.get_module(x, y):
                    # Marker/finder pattern detection - SKIP these for separate drawing
                    if QRPreviewService._is_finder_pattern(x, y, size):
                        continue  # Draw markers separately
                    # Pattern style for data modules only
                    QRPreviewService._draw_module(dwg, x, y, module_size, fg_color, pattern_style, offset_x, offset_y)
        # Draw finder patterns (markers) - ESSENTIAL for QR scannability
        marker_border_style = getattr(design, 'marker_border_style', 'square') if design else 'square'
        marker_center_style = getattr(design, 'marker_center_style', 'square') if design else 'square'
        marker_border_color = getattr(design, 'marker_border_color', fg_color) if design else fg_color
        marker_center_color = getattr(design, 'marker_center_color', fg_color) if design else fg_color

        # Debug: Log finder pattern generation
        finder_positions = [(0,0), (size-7,0), (0,size-7)]
        logger.debug(f"Drawing {len(finder_positions)} finder patterns at positions: {finder_positions}")
        logger.debug(f"QR size: {size}, border_style: {marker_border_style}, center_style: {marker_center_style}")

        for i, (mx, my) in enumerate(finder_positions):
            try:
                logger.debug(f"Drawing finder pattern {i+1}/3 at position ({mx}, {my})")
                QRPreviewService._draw_finder_marker(dwg, mx, my, module_size, marker_border_style, marker_center_style, marker_border_color, marker_center_color, offset_x, offset_y)
                logger.debug(f"Successfully drew finder pattern {i+1}/3")
            except Exception as e:
                logger.error(f"Error drawing finder pattern {i+1}/3 at ({mx}, {my}): {e}")
                raise
        # Draw frame/label if specified
        frame_style = getattr(design, 'frame_style', None) if design else None
        if frame_style and frame_style != 'none':
            QRPreviewService._draw_frame(dwg, dimensions, design, offset_x, offset_y)
        frame_text = getattr(design, 'frame_text', None) if design else None
        if frame_text:
            logger.debug(f"Drawing label bar with text: '{frame_text}', SVG dimensions: {svg_width}x{svg_height}")
            # Pass the QR code dimensions (without frame margins) for proper positioning
            QRPreviewService._draw_label(dwg, dimensions, design, offset_x, offset_y)
        # Logo overlay
        if logo:
            await QRPreviewService._draw_logo(dwg, logo, dimensions, design, offset_x, offset_y)
        # Return SVG string
        svg_string = dwg.tostring()

        # Debug: Log SVG content to check for missing elements
        logger.debug(f"Generated SVG length: {len(svg_string)}")
        logger.debug(f"SVG contains marker-0-0: {'marker-0-0' in svg_string}")
        logger.debug(f"SVG contains marker-18-0: {'marker-18-0' in svg_string}")
        logger.debug(f"SVG contains marker-0-18: {'marker-0-18' in svg_string}")

        # Count marker elements in SVG
        marker_outer_count = svg_string.count('qr-marker-outer')
        marker_inner_count = svg_string.count('qr-marker-inner')
        marker_dot_count = svg_string.count('qr-marker-dot')
        label_count = svg_string.count('qr-frame-label')
        label_bg_count = svg_string.count('qr-label-background')
        logger.debug(f"SVG marker counts - outer: {marker_outer_count}, inner: {marker_inner_count}, dot: {marker_dot_count}")
        logger.debug(f"SVG label counts - text: {label_count}, background: {label_bg_count}")

        # Check if label text is in SVG
        if frame_text and frame_text in svg_string:
            logger.debug(f"Label text '{frame_text}' found in SVG")
        elif frame_text:
            logger.warning(f"Label text '{frame_text}' NOT found in SVG")

        return svg_string

    def _get_logo_position(self, position: str, logo_px: int, dimensions: int):
        """
        Compute the (x, y) position for logo embedding in the SVG QR code.
        Args:
            position: One of 'center', 'top-left', 'top-right', 'bottom-left', 'bottom-right'
            logo_px: Logo size in pixels
            dimensions: QR code SVG dimensions
        Returns:
            (x, y) tuple for SVG insert
        """
        if not position or position == 'center':
            x = (dimensions - logo_px) // 2
            y = (dimensions - logo_px) // 2
        elif position == 'top-left':
            x, y = 0, 0
        elif position == 'top-right':
            x, y = dimensions - logo_px, 0
        elif position == 'bottom-left':
            x, y = 0, dimensions - logo_px
        elif position == 'bottom-right':
            x, y = dimensions - logo_px, dimensions - logo_px
        else:
            x = (dimensions - logo_px) // 2
            y = (dimensions - logo_px) // 2
        return (x, y)
        
    def _format_qr_data(self, qr_type: QRType, content: Dict[str, Any]) -> str:
        """
        Format content based on QR type for QR code generation
        
        Args:
            qr_type: Type of QR code
            content: Content data for QR code
            
        Returns:
            Formatted string for QR code
        """
        # Convert qr_type to string and handle case insensitivity
        qr_type_str = str(qr_type).lower() if isinstance(qr_type, QRType) else str(qr_type).lower()
        
        # Map frontend type names to backend enum types if needed
        type_mapping = {
            'calendar': 'event',
            'x': 'twitter',  # Handle X (formerly Twitter)
            'url_link': 'url',
            # Add more mappings here if frontend and backend use different names
        }
        
        # Apply type mapping if it exists
        qr_type_str = type_mapping.get(qr_type_str, qr_type_str)
        
        try:
            logger.debug(f"Formatting content for QR type: {qr_type_str}, content keys: {list(content.keys())}")
            
            # Handle URL type
            if qr_type_str == QRType.URL.lower():
                # Get URL from content
                url = content.get('url', '')
                
                # Add UTM parameters if provided
                utm_source = content.get('utm_source')
                utm_medium = content.get('utm_medium')
                utm_campaign = content.get('utm_campaign')
                
                if any([utm_source, utm_medium, utm_campaign]):
                    # Start with ? or & depending on if URL already has parameters
                    separator = '&' if '?' in url else '?'
                    
                    utm_params = []
                    if utm_source:
                        utm_params.append(f"utm_source={utm_source}")
                    if utm_medium:
                        utm_params.append(f"utm_medium={utm_medium}")
                    if utm_campaign:
                        utm_params.append(f"utm_campaign={utm_campaign}")
                    
                    url = f"{url}{separator}{('&').join(utm_params)}"
                
                return url
                
            elif qr_type_str == QRType.TEXT.lower():
                return content.get('text', '')
                
            elif qr_type_str == QRType.EMAIL.lower():
                email = content.get('email', '')
                subject = content.get('subject', '')
                body = content.get('body', '')
                
                email_content = f"mailto:{email}"
                if subject or body:
                    email_content += "?"
                    if subject:
                        email_content += f"subject={subject}"
                    if body:
                        if subject:
                            email_content += "&"
                        email_content += f"body={body}"
                return email_content
                
            elif qr_type_str == QRType.PHONE.lower():
                return f"tel:{content.get('phone', '')}"
                
            elif qr_type_str == QRType.SMS.lower():
                phone = content.get('phone', '')
                message = content.get('message', '')
                return f"sms:{phone}?body={message}" if message else f"sms:{phone}"
                
            elif qr_type_str == QRType.WIFI.lower():
                ssid = content.get('ssid', '')
                password = content.get('password', '')
                encryption = content.get('encryption', 'WPA')
                hidden = content.get('hidden', False)
                
                wifi_string = f"WIFI:S:{ssid};T:{encryption};"
                if password:
                    wifi_string += f"P:{password};"
                if hidden:
                    wifi_string += "H:true;"
                wifi_string += ";"
                return wifi_string
                
            elif qr_type_str == QRType.VCARD.lower():
                name = content.get('name', '')
                org = content.get('organization', '')
                title = content.get('title', '')
                email = content.get('email', '')
                phone = content.get('phone', '')
                website = content.get('website', '')
                address = content.get('address', '')
                
                vcard = [
                    "BEGIN:VCARD",
                    "VERSION:3.0"
                ]
                
                if name:
                    name_parts = name.split(' ', 1)
                    if len(name_parts) == 2:
                        vcard.append(f"N:{name_parts[1]};{name_parts[0]};;;")
                        vcard.append(f"FN:{name}")
                    else:
                        vcard.append(f"N:{name};;;;")
                        vcard.append(f"FN:{name}")
                
                if org:
                    vcard.append(f"ORG:{org}")
                
                if title:
                    vcard.append(f"TITLE:{title}")
                
                if email:
                    vcard.append(f"EMAIL:{email}")
                
                if phone:
                    vcard.append(f"TEL:{phone}")
                
                if website:
                    vcard.append(f"URL:{website}")
                
                if address:
                    vcard.append(f"ADR:;;{address};;;")
                
                vcard.append("END:VCARD")
                return "\n".join(vcard)
                
            # Handle both EVENT (backend) and CALENDAR (frontend) types
            elif qr_type_str in [QRType.EVENT.lower(), 'calendar']:
                summary = content.get('summary', '')
                start_time = content.get('start_time', '')
                end_time = content.get('end_time', '')
                location = content.get('location', '')
                description = content.get('description', '')
                
                calendar = [
                    "BEGIN:VEVENT"
                ]
                
                if summary:
                    calendar.append(f"SUMMARY:{summary}")
                
                if start_time:
                    calendar.append(f"DTSTART:{start_time}")
                
                if end_time:
                    calendar.append(f"DTEND:{end_time}")
                
                if location:
                    calendar.append(f"LOCATION:{location}")
                
                if description:
                    calendar.append(f"DESCRIPTION:{description}")
                
                calendar.append("END:VEVENT")
                return "\n".join(calendar)
                
            # Social Media QR Types
            elif qr_type_str in [QRType.X.lower(), 'twitter', 'twitter_profile']:
                username = content.get('username', '')
                text = content.get('text', '')
                
                if username and not username.startswith('@'):
                    username = '@' + username
                    
                if text:
                    return f"https://twitter.com/intent/tweet?text={text}"
                elif username:
                    return f"https://twitter.com/{username.lstrip('@')}"
                else:
                    return "https://twitter.com/"
                
            elif qr_type_str == QRType.WHATSAPP.lower():
                phone = content.get('phone', '')
                message = content.get('message', '')
                if phone:
                    if message:
                        return f"https://wa.me/{phone}?text={message}"
                    return f"https://wa.me/{phone}"
                return "https://www.whatsapp.com/"
                
            elif qr_type_str == QRType.INSTAGRAM.lower():
                username = content.get('username', '')
                if username:
                    return f"https://instagram.com/{username.lstrip('@')}"
                return "https://instagram.com/"
                
            elif qr_type_str == QRType.FACEBOOK.lower():
                username = content.get('username', '')
                if username:
                    return f"https://facebook.com/{username}"
                return "https://facebook.com/"
                
            elif qr_type_str == QRType.LINKEDIN.lower():
                username = content.get('username', '')
                if username:
                    return f"https://linkedin.com/in/{username}"
                return "https://linkedin.com/"
                
            elif qr_type_str == QRType.TIKTOK.lower():
                username = content.get('username', '')
                if username:
                    return f"https://tiktok.com/@{username.lstrip('@')}"
                return "https://tiktok.com/"
                
            elif qr_type_str == QRType.DISCORD.lower():
                invite = content.get('invite', '')
                if invite:
                    return f"https://discord.gg/{invite}"
                return "https://discord.com/"
                
            # App Links
            elif qr_type_str == QRType.APP_STORE.lower():
                app_id = content.get('app_id', '')
                if app_id:
                    return f"https://apps.apple.com/app/id{app_id}"
                return "https://apps.apple.com/"
                
            elif qr_type_str == QRType.GOOGLE_PLAY.lower():
                package_id = content.get('package_id', '')
                if package_id:
                    return f"https://play.google.com/store/apps/details?id={package_id}"
                return "https://play.google.com/store/apps"
                
            # Location Based
            elif qr_type_str == QRType.GEO.lower():
                latitude = content.get('latitude', '')
                longitude = content.get('longitude', '')
                if latitude and longitude:
                    return f"geo:{latitude},{longitude}"
                return "geo:0,0"
                
            elif qr_type_str == QRType.NAVIGATION.lower():
                latitude = content.get('latitude', '')
                longitude = content.get('longitude', '')
                address = content.get('address', '')
                
                if latitude and longitude:
                    return f"https://maps.google.com/maps?q={latitude},{longitude}"
                elif address:
                    return f"https://maps.google.com/maps?q={address}"
                return "https://maps.google.com/"
                
            # Meeting Links
            elif qr_type_str == QRType.ZOOM.lower():
                meeting_id = content.get('meeting_id', '')
                password = content.get('password', '')
                
                if meeting_id:
                    zoom_url = f"https://zoom.us/j/{meeting_id}"
                    if password:
                        zoom_url += f"?pwd={password}"
                    return zoom_url
                return "https://zoom.us/"
                
            elif qr_type_str == QRType.MEET.lower():
                meeting_code = content.get('meeting_code', '')
                if meeting_code:
                    return f"https://meet.google.com/{meeting_code}"
                return "https://meet.google.com/"
                
            elif qr_type_str == QRType.TEAMS.lower():
                meeting_id = content.get('meeting_id', '')
                if meeting_id:
                    return f"https://teams.microsoft.com/l/meetup-join/{meeting_id}"
                return "https://teams.microsoft.com/"
                
            elif qr_type_str == QRType.WEBEX.lower():
                meeting_link = content.get('meeting_link', '')
                if meeting_link:
                    return meeting_link
                return "https://webex.com/"
                
            # Business & Events
            elif qr_type_str == QRType.COUPON.lower():
                code = content.get('code', '')
                discount = content.get('discount', '')
                expiration = content.get('expiration', '')
                description = content.get('description', '')
                
                coupon_data = {}
                if code:
                    coupon_data['code'] = code
                if discount:
                    coupon_data['discount'] = discount
                if expiration:
                    coupon_data['expiration'] = expiration
                if description:
                    coupon_data['description'] = description
                    
                return json.dumps(coupon_data)
                
            elif qr_type_str == QRType.PRODUCT.lower():
                name = content.get('name', '')
                description = content.get('description', '')
                price = content.get('price', '')
                url = content.get('url', '')
                
                if url:
                    return url
                    
                product_data = {}
                if name:
                    product_data['name'] = name
                if description:
                    product_data['description'] = description
                if price:
                    product_data['price'] = price
                    
                return json.dumps(product_data)

            # Cryptocurrency
            elif qr_type_str == QRType.CRYPTO_WALLET.lower():
                address = content.get('address', '')
                coin = content.get('coin', 'BTC').upper()
                amount = content.get('amount', '')
                
                if coin == 'BTC':
                    result = f"bitcoin:{address}"
                    if amount:
                        result += f"?amount={amount}"
                    return result
                elif coin == 'ETH':
                    return f"ethereum:{address}"
                else:
                    crypto_data = {'coin': coin, 'address': address}
                    if amount:
                        crypto_data['amount'] = amount
                    return json.dumps(crypto_data)
                    
            # Transportation
            elif qr_type_str == QRType.TICKET.lower():
                ticket_id = content.get('ticket_id', '')
                passenger = content.get('passenger', '')
                departure = content.get('departure', '')
                destination = content.get('destination', '')
                departure_time = content.get('departure_time', '')
                
                ticket_data = {}
                if ticket_id:
                    ticket_data['ticket_id'] = ticket_id
                if passenger:
                    ticket_data['passenger'] = passenger
                if departure:
                    ticket_data['departure'] = departure
                if destination:
                    ticket_data['destination'] = destination
                if departure_time:
                    ticket_data['departure_time'] = departure_time
                    
                return json.dumps(ticket_data)
                
            elif qr_type_str == QRType.VEHICLE_REGISTRATION.lower():
                owner = content.get('owner', '')
                vehicle_id = content.get('vehicle_id', '')
                registration_number = content.get('registration_number', '')
                expiration_date = content.get('expiration_date', '')
                
                reg_data = {}
                if owner:
                    reg_data['owner'] = owner
                if vehicle_id:
                    reg_data['vehicle_id'] = vehicle_id
                if registration_number:
                    reg_data['registration_number'] = registration_number
                if expiration_date:
                    reg_data['expiration_date'] = expiration_date
                    
                return json.dumps(reg_data)
                
            elif qr_type_str == QRType.BOARDING_PASS.lower():
                passenger_name = content.get('passenger_name', '')
                flight_number = content.get('flight_number', '')
                departure = content.get('departure', '')
                destination = content.get('destination', '')
                date = content.get('date', '')
                seat = content.get('seat', '')
                gate = content.get('gate', '')
                
                boarding_data = {}
                if passenger_name:
                    boarding_data['passenger_name'] = passenger_name
                if flight_number:
                    boarding_data['flight_number'] = flight_number
                if departure:
                    boarding_data['departure'] = departure
                if destination:
                    boarding_data['destination'] = destination
                if date:
                    boarding_data['date'] = date
                if seat:
                    boarding_data['seat'] = seat
                if gate:
                    boarding_data['gate'] = gate
                    
                return json.dumps(boarding_data)
                
            elif qr_type_str == QRType.TRANSIT_PASS.lower():
                pass_id = content.get('pass_id', '')
                user_name = content.get('user_name', '')
                pass_type = content.get('pass_type', '')
                valid_from = content.get('valid_from', '')
                valid_to = content.get('valid_to', '')
                
                transit_data = {}
                if pass_id:
                    transit_data['pass_id'] = pass_id
                if user_name:
                    transit_data['user_name'] = user_name
                if pass_type:
                    transit_data['pass_type'] = pass_type
                if valid_from:
                    transit_data['valid_from'] = valid_from
                if valid_to:
                    transit_data['valid_to'] = valid_to
                    
                return json.dumps(transit_data)
                
            elif qr_type_str == QRType.PARKING.lower():
                ticket_id = content.get('ticket_id', '')
                location = content.get('location', '')
                entry_time = content.get('entry_time', '')
                exit_time = content.get('exit_time', '')
                vehicle_id = content.get('vehicle_id', '')
                
                parking_data = {}
                if ticket_id:
                    parking_data['ticket_id'] = ticket_id
                if location:
                    parking_data['location'] = location
                if entry_time:
                    parking_data['entry_time'] = entry_time
                if exit_time:
                    parking_data['exit_time'] = exit_time
                if vehicle_id:
                    parking_data['vehicle_id'] = vehicle_id
                    
                return json.dumps(parking_data)
                
            elif qr_type_str == QRType.RIDE_SHARE.lower():
                ride_id = content.get('ride_id', '')
                driver = content.get('driver', '')
                passenger = content.get('passenger', '')
                pickup = content.get('pickup', '')
                destination = content.get('destination', '')
                estimate = content.get('estimate', '')
                
                ride_data = {}
                if ride_id:
                    ride_data['ride_id'] = ride_id
                if driver:
                    ride_data['driver'] = driver
                if passenger:
                    ride_data['passenger'] = passenger
                if pickup:
                    ride_data['pickup'] = pickup
                if destination:
                    ride_data['destination'] = destination
                if estimate:
                    ride_data['estimate'] = estimate
                    
                return json.dumps(ride_data)
                
            elif qr_type_str == QRType.SCOOTER_UNLOCK.lower():
                scooter_id = content.get('scooter_id', '')
                unlock_code = content.get('unlock_code', '')
                location = content.get('location', '')
                battery = content.get('battery', '')
                rate = content.get('rate', '')
                
                scooter_data = {}
                if scooter_id:
                    scooter_data['scooter_id'] = scooter_id
                if unlock_code:
                    scooter_data['unlock_code'] = unlock_code
                if location:
                    scooter_data['location'] = location
                if battery:
                    scooter_data['battery'] = battery
                if rate:
                    scooter_data['rate'] = rate
                    
                return json.dumps(scooter_data)
                
            # PDF417 barcode for boarding passes, tickets, etc.
            elif qr_type_str == QRType.PDF417.lower():
                # Return raw data for PDF417 barcode generation
                data = content.get('data', '')
                return data
                
            # Healthcare QR types
            elif qr_type_str == QRType.MEDICAL_RECORD.lower():
                patient_id = content.get('patient_id', '')
                patient_name = content.get('patient_name', '')
                record_id = content.get('record_id', '')
                date = content.get('date', '')
                provider = content.get('provider', '')
                details = content.get('details', '')
                
                medical_data = {}
                if patient_id:
                    medical_data['patient_id'] = patient_id
                if patient_name:
                    medical_data['patient_name'] = patient_name
                if record_id:
                    medical_data['record_id'] = record_id
                if date:
                    medical_data['date'] = date
                if provider:
                    medical_data['provider'] = provider
                if details:
                    medical_data['details'] = details
                    
                return json.dumps(medical_data)
                
            elif qr_type_str == QRType.PRESCRIPTION.lower():
                patient_name = content.get('patient_name', '')
                doctor_name = content.get('doctor_name', '')
                medication = content.get('medication', '')
                dosage = content.get('dosage', '')
                frequency = content.get('frequency', '')
                issue_date = content.get('issue_date', '')
                expiry_date = content.get('expiry_date', '')
                
                prescription_data = {}
                if patient_name:
                    prescription_data['patient_name'] = patient_name
                if doctor_name:
                    prescription_data['doctor_name'] = doctor_name
                if medication:
                    prescription_data['medication'] = medication
                if dosage:
                    prescription_data['dosage'] = dosage
                if frequency:
                    prescription_data['frequency'] = frequency
                if issue_date:
                    prescription_data['issue_date'] = issue_date
                if expiry_date:
                    prescription_data['expiry_date'] = expiry_date
                    
                return json.dumps(prescription_data)
                
            elif qr_type_str == QRType.APPOINTMENT.lower():
                patient_name = content.get('patient_name', '')
                doctor_name = content.get('doctor_name', '')
                facility = content.get('facility', '')
                date = content.get('date', '')
                time = content.get('time', '')
                purpose = content.get('purpose', '')
                appointment_id = content.get('appointment_id', '')
                
                appointment_data = {}
                if patient_name:
                    appointment_data['patient_name'] = patient_name
                if doctor_name:
                    appointment_data['doctor_name'] = doctor_name
                if facility:
                    appointment_data['facility'] = facility
                if date:
                    appointment_data['date'] = date
                if time:
                    appointment_data['time'] = time
                if purpose:
                    appointment_data['purpose'] = purpose
                if appointment_id:
                    appointment_data['appointment_id'] = appointment_id
                    
                return json.dumps(appointment_data)
                
            elif qr_type_str == QRType.VACCINATION.lower():
                patient_name = content.get('patient_name', '')
                vaccine_name = content.get('vaccine_name', '')
                date = content.get('date', '')
                location = content.get('location', '')
                provider = content.get('provider', '')
                dose = content.get('dose', '')
                next_dose_date = content.get('next_dose_date', '')
                
                vaccination_data = {}
                if patient_name:
                    vaccination_data['patient_name'] = patient_name
                if vaccine_name:
                    vaccination_data['vaccine_name'] = vaccine_name
                if date:
                    vaccination_data['date'] = date
                if location:
                    vaccination_data['location'] = location
                if provider:
                    vaccination_data['provider'] = provider
                if dose:
                    vaccination_data['dose'] = dose
                if next_dose_date:
                    vaccination_data['next_dose_date'] = next_dose_date
                    
                return json.dumps(vaccination_data)
                
            elif qr_type_str == QRType.ALLERGY_INFO.lower():
                patient_name = content.get('patient_name', '')
                allergies = content.get('allergies', '')
                severity = content.get('severity', '')
                emergency_contact = content.get('emergency_contact', '')
                notes = content.get('notes', '')
                
                allergy_data = {}
                if patient_name:
                    allergy_data['patient_name'] = patient_name
                if allergies:
                    allergy_data['allergies'] = allergies
                if severity:
                    allergy_data['severity'] = severity
                if emergency_contact:
                    allergy_data['emergency_contact'] = emergency_contact
                if notes:
                    allergy_data['notes'] = notes
                    
                return json.dumps(allergy_data)
                
            elif qr_type_str == QRType.LAB_RESULT.lower():
                patient_name = content.get('patient_name', '')
                test_type = content.get('test_type', '')
                date = content.get('date', '')
                result = content.get('result', '')
                reference_range = content.get('reference_range', '')
                lab_name = content.get('lab_name', '')
                
                lab_data = {}
                if patient_name:
                    lab_data['patient_name'] = patient_name
                if test_type:
                    lab_data['test_type'] = test_type
                if date:
                    lab_data['date'] = date
                if result:
                    lab_data['result'] = result
                if reference_range:
                    lab_data['reference_range'] = reference_range
                if lab_name:
                    lab_data['lab_name'] = lab_name
                    
                return json.dumps(lab_data)
                
            elif qr_type_str == QRType.TELEMEDICINE.lower():
                patient_name = content.get('patient_name', '')
                doctor_name = content.get('doctor_name', '')
                date = content.get('date', '')
                time = content.get('time', '')
                meeting_link = content.get('meeting_link', '')
                instructions = content.get('instructions', '')
                
                telemedicine_data = {}
                if patient_name:
                    telemedicine_data['patient_name'] = patient_name
                if doctor_name:
                    telemedicine_data['doctor_name'] = doctor_name
                if date:
                    telemedicine_data['date'] = date
                if time:
                    telemedicine_data['time'] = time
                if meeting_link:
                    telemedicine_data['meeting_link'] = meeting_link
                if instructions:
                    telemedicine_data['instructions'] = instructions
                    
                return json.dumps(telemedicine_data)
                
            # IoT and Device QR types
            elif qr_type_str == QRType.DEVICE_CONFIG.lower():
                device_id = content.get('device_id', '')
                device_type = content.get('device_type', '')
                settings = content.get('settings', {})
                network_name = content.get('network_name', '')
                network_password = content.get('network_password', '')
                
                config_data = {}
                if device_id:
                    config_data['device_id'] = device_id
                if device_type:
                    config_data['device_type'] = device_type
                if settings:
                    config_data['settings'] = settings
                if network_name:
                    config_data['network_name'] = network_name
                if network_password:
                    config_data['network_password'] = network_password
                    
                return json.dumps(config_data)
                
            elif qr_type_str == QRType.SMART_HOME_CONTROL.lower():
                device_id = content.get('device_id', '')
                action = content.get('action', '')
                parameters = content.get('parameters', {})
                room = content.get('room', '')
                scheduled = content.get('scheduled', '')
                
                control_data = {}
                if device_id:
                    control_data['device_id'] = device_id
                if action:
                    control_data['action'] = action
                if parameters:
                    control_data['parameters'] = parameters
                if room:
                    control_data['room'] = room
                if scheduled:
                    control_data['scheduled'] = scheduled
                    
                return json.dumps(control_data)
                
            elif qr_type_str == QRType.SENSOR_DATA.lower():
                sensor_id = content.get('sensor_id', '')
                sensor_type = content.get('sensor_type', '')
                value = content.get('value', '')
                unit = content.get('unit', '')
                timestamp = content.get('timestamp', '')
                location = content.get('location', '')
                
                sensor_data = {}
                if sensor_id:
                    sensor_data['sensor_id'] = sensor_id
                if sensor_type:
                    sensor_data['sensor_type'] = sensor_type
                if value:
                    sensor_data['value'] = value
                if unit:
                    sensor_data['unit'] = unit
                if timestamp:
                    sensor_data['timestamp'] = timestamp
                if location:
                    sensor_data['location'] = location
                    
                return json.dumps(sensor_data)
                
            elif qr_type_str == QRType.MAINTENANCE.lower():
                equipment_id = content.get('equipment_id', '')
                maintenance_type = content.get('maintenance_type', '')
                technician = content.get('technician', '')
                date = content.get('date', '')
                notes = content.get('notes', '')
                next_service = content.get('next_service', '')
                
                maintenance_data = {}
                if equipment_id:
                    maintenance_data['equipment_id'] = equipment_id
                if maintenance_type:
                    maintenance_data['maintenance_type'] = maintenance_type
                if technician:
                    maintenance_data['technician'] = technician
                if date:
                    maintenance_data['date'] = date
                if notes:
                    maintenance_data['notes'] = notes
                if next_service:
                    maintenance_data['next_service'] = next_service
                    
                return json.dumps(maintenance_data)
                
            # Gaming and AR/VR QR types
            elif qr_type_str == QRType.GAME.lower():
                game_id = content.get('game_id', '')
                title = content.get('title', '')
                description = content.get('description', '')
                platform = content.get('platform', '')
                download_link = content.get('download_link', '')
                
                game_data = {}
                if game_id:
                    game_data['game_id'] = game_id
                if title:
                    game_data['title'] = title
                if description:
                    game_data['description'] = description
                if platform:
                    game_data['platform'] = platform
                if download_link:
                    game_data['download_link'] = download_link
                    
                return json.dumps(game_data)
                
            elif qr_type_str == QRType.VIRTUAL_ITEM.lower():
                item_id = content.get('item_id', '')
                name = content.get('name', '')
                game = content.get('game', '')
                rarity = content.get('rarity', '')
                properties = content.get('properties', {})
                
                item_data = {}
                if item_id:
                    item_data['item_id'] = item_id
                if name:
                    item_data['name'] = name
                if game:
                    item_data['game'] = game
                if rarity:
                    item_data['rarity'] = rarity
                if properties:
                    item_data['properties'] = properties
                    
                return json.dumps(item_data)
                
            elif qr_type_str == QRType.TOURNAMENT.lower():
                tournament_id = content.get('tournament_id', '')
                name = content.get('name', '')
                game = content.get('game', '')
                start_date = content.get('start_date', '')
                location = content.get('location', '')
                prize = content.get('prize', '')
                
                tournament_data = {}
                if tournament_id:
                    tournament_data['tournament_id'] = tournament_id
                if name:
                    tournament_data['name'] = name
                if game:
                    tournament_data['game'] = game
                if start_date:
                    tournament_data['start_date'] = start_date
                if location:
                    tournament_data['location'] = location
                if prize:
                    tournament_data['prize'] = prize
                    
                return json.dumps(tournament_data)
                
            elif qr_type_str == QRType.PLAYER_PROFILE.lower():
                player_id = content.get('player_id', '')
                username = content.get('username', '')
                game = content.get('game', '')
                rank = content.get('rank', '')
                stats = content.get('stats', {})
                team = content.get('team', '')
                
                profile_data = {}
                if player_id:
                    profile_data['player_id'] = player_id
                if username:
                    profile_data['username'] = username
                if game:
                    profile_data['game'] = game
                if rank:
                    profile_data['rank'] = rank
                if stats:
                    profile_data['stats'] = stats
                if team:
                    profile_data['team'] = team
                    
                return json.dumps(profile_data)
                
            elif qr_type_str == QRType.GAME_SAVE.lower():
                save_id = content.get('save_id', '')
                game = content.get('game', '')
                player = content.get('player', '')
                timestamp = content.get('timestamp', '')
                progress = content.get('progress', '')
                checkpoint = content.get('checkpoint', '')
                
                save_data = {}
                if save_id:
                    save_data['save_id'] = save_id
                if game:
                    save_data['game'] = game
                if player:
                    save_data['player'] = player
                if timestamp:
                    save_data['timestamp'] = timestamp
                if progress:
                    save_data['progress'] = progress
                if checkpoint:
                    save_data['checkpoint'] = checkpoint
                    
                return json.dumps(save_data)
                
            elif qr_type_str == QRType.AR_EXPERIENCE.lower():
                experience_id = content.get('experience_id', '')
                title = content.get('title', '')
                description = content.get('description', '')
                model_url = content.get('model_url', '')
                instructions = content.get('instructions', '')
                
                ar_data = {}
                if experience_id:
                    ar_data['experience_id'] = experience_id
                if title:
                    ar_data['title'] = title
                if description:
                    ar_data['description'] = description
                if model_url:
                    ar_data['model_url'] = model_url
                if instructions:
                    ar_data['instructions'] = instructions
                    
                return json.dumps(ar_data)
                
            elif qr_type_str == QRType.VR_CONTENT.lower():
                content_id = content.get('content_id', '')
                title = content.get('title', '')
                description = content.get('description', '')
                platform = content.get('platform', '')
                access_url = content.get('access_url', '')
                
                vr_data = {}
                if content_id:
                    vr_data['content_id'] = content_id
                if title:
                    vr_data['title'] = title
                if description:
                    vr_data['description'] = description
                if platform:
                    vr_data['platform'] = platform
                if access_url:
                    vr_data['access_url'] = access_url
                    
                return json.dumps(vr_data)
                
            # Legal and Compliance QR types
            elif qr_type_str == QRType.CONTRACT.lower():
                contract_id = content.get('contract_id', '')
                title = content.get('title', '')
                parties = content.get('parties', '')
                date = content.get('date', '')
                terms = content.get('terms', '')
                document_url = content.get('document_url', '')
                
                contract_data = {}
                if contract_id:
                    contract_data['contract_id'] = contract_id
                if title:
                    contract_data['title'] = title
                if parties:
                    contract_data['parties'] = parties
                if date:
                    contract_data['date'] = date
                if terms:
                    contract_data['terms'] = terms
                if document_url:
                    contract_data['document_url'] = document_url
                    
                return json.dumps(contract_data)
                
            elif qr_type_str == QRType.CERTIFICATION.lower():
                certification_id = content.get('certification_id', '')
                title = content.get('title', '')
                recipient = content.get('recipient', '')
                issuer = content.get('issuer', '')
                date = content.get('date', '')
                expiry_date = content.get('expiry_date', '')
                
                certification_data = {}
                if certification_id:
                    certification_data['certification_id'] = certification_id
                if title:
                    certification_data['title'] = title
                if recipient:
                    certification_data['recipient'] = recipient
                if issuer:
                    certification_data['issuer'] = issuer
                if date:
                    certification_data['date'] = date
                if expiry_date:
                    certification_data['expiry_date'] = expiry_date
                    
                return json.dumps(certification_data)
                
            elif qr_type_str == QRType.DOCUMENT_VERIFICATION.lower():
                document_id = content.get('document_id', '')
                document_type = content.get('document_type', '')
                issuer = content.get('issuer', '')
                issuance_date = content.get('issuance_date', '')
                verify_url = content.get('verify_url', '')
                
                verification_data = {}
                if document_id:
                    verification_data['document_id'] = document_id
                if document_type:
                    verification_data['document_type'] = document_type
                if issuer:
                    verification_data['issuer'] = issuer
                if issuance_date:
                    verification_data['issuance_date'] = issuance_date
                if verify_url:
                    verification_data['verify_url'] = verify_url
                    
                return json.dumps(verification_data)
                
            elif qr_type_str == QRType.LEGAL_NOTICE.lower():
                notice_id = content.get('notice_id', '')
                title = content.get('title', '')
                issuer = content.get('issuer', '')
                date = content.get('date', '')
                content_text = content.get('content', '')
                
                notice_data = {}
                if notice_id:
                    notice_data['notice_id'] = notice_id
                if title:
                    notice_data['title'] = title
                if issuer:
                    notice_data['issuer'] = issuer
                if date:
                    notice_data['date'] = date
                if content_text:
                    notice_data['content'] = content_text
                    
                return json.dumps(notice_data)
                
            # Internationalization QR types
            elif qr_type_str == QRType.MULTI_LANGUAGE.lower():
                default_language = content.get('default_language', '')
                translations = content.get('translations', {})
                
                multi_lang_data = {}
                if default_language:
                    multi_lang_data['default_language'] = default_language
                if translations:
                    multi_lang_data['translations'] = translations
                    
                return json.dumps(multi_lang_data)
                
            elif qr_type_str == "translation_service":
                source_language = content.get('source_language', '')
                target_language = content.get('target_language', '')
                text = content.get('text', '')
                service_url = content.get('service_url', '')

                translation_data = {}
                if source_language:
                    translation_data['source_language'] = source_language
                if target_language:
                    translation_data['target_language'] = target_language
                if text:
                    translation_data['text'] = text
                if service_url:
                    translation_data['service_url'] = service_url

                return json.dumps(translation_data)
                
            # Default fallback for any other QR types
            else:
                # First try to find a URL in the content
                for key in ['url', 'link', 'website']:
                    if key in content and content[key]:
                        return content[key]
                
                # If no URL is found, serialize the content as JSON
                if content:
                    try:
                        return json.dumps(content)
                    except Exception as e:
                        logger.error(f"Error serializing content for {qr_type_str}: {str(e)}")
                        
                # Last resort: return qr_type as text
                return f"QR Code Type: {qr_type_str}"
                
        except Exception as e:
            # Log the error but don't crash
            logger.error(f"Error formatting QR data for type {qr_type_str}: {str(e)}", exc_info=True)
            
            # Return a safe fallback
            try:
                # Try to return serialized content
                return json.dumps(content)
            except:
                # If that fails too, return a simple error message
                return f"QR Code: {qr_type_str}"
                
    # Legacy PNG/PIL QR code creation methods removed. All preview/export is SVG-only.
    # Modular SVG QR code generation is handled by generate_preview_svg_modular.
    # No references to PIL.Image, qrcode, ImageColor, or PNG output remain.
    #
    # All logo embedding is handled in SVG via _draw_logo. See below for robust logo upload/embed.
            qr.add_data(data)
            qr.make(fit=True)
            return qr.make_image(fill_color="black", back_color="white").get_image()

    async def _apply_custom_pattern(self, img, pattern, fg_color, bg_color):
        """
        Apply a custom pattern style that isn't natively supported by qrcode module drawers
        
        Args:
            img: Original QR image
            pattern: Pattern style to apply
            fg_color: Foreground color (RGB)
            bg_color: Background color (RGB)
            
        Returns:
            PIL Image with custom pattern applied
        """
        # Create a new image with the background color
        img_size = img.size
        styled_img = Image.new("RGB", img_size, bg_color)
        
        # Get pixel data
        pixels = img.convert('RGB').load()
        styled_pixels = styled_img.load()
        
        # Apply different patterns
        for i in range(img_size[0]):
            for j in range(img_size[1]):
                if pixels[i, j] == fg_color:  # If this is a foreground pixel
                    if pattern == "dots":
                        # Create a circular dot pattern
                        for di in range(-3, 4):
                            for dj in range(-3, 4):
                                ni, nj = i + di, j + dj
                                if 0 <= ni < img_size[0] and 0 <= nj < img_size[1]:
                                    if di*di + dj*dj <= 4:  # Small circle radius
                                        styled_pixels[ni, nj] = fg_color
                    
                    elif pattern == "classy":
                        # Create a square with border
                        styled_pixels[i, j] = fg_color
                        # Check if this is a border pixel
                        is_border = False
                        for di, dj in [(-1,0), (1,0), (0,-1), (0,1)]:
                            ni, nj = i + di, j + dj
                            if 0 <= ni < img_size[0] and 0 <= nj < img_size[1]:
                                if pixels[ni, nj] != fg_color:
                                    is_border = True
                                    break
                        if is_border:
                            styled_pixels[i, j] = fg_color
                        else:
                            # Inner part of the module gets a lighter color
                            r, g, b = fg_color
                            styled_pixels[i, j] = (
                                min(255, r + 50), 
                                min(255, g + 50), 
                                min(255, b + 50)
                            )
                                    
                    elif pattern == "rounded":
                        # Create a rounded square pattern
                        styled_pixels[i, j] = fg_color
                        # Add rounded corners
                        if i > 0 and j > 0 and i < img_size[0]-1 and j < img_size[1]-1:
                            styled_pixels[i-1, j] = fg_color
                            styled_pixels[i+1, j] = fg_color
                            styled_pixels[i, j-1] = fg_color
                            styled_pixels[i, j+1] = fg_color
                    
                    elif pattern == "horizontal":
                        # Create horizontal bars
                        for di in range(-2, 3):
                            ni = i + di
                            if 0 <= ni < img_size[0]:
                                styled_pixels[ni, j] = fg_color
                    
                    elif pattern == "vertical":
                        # Create vertical bars
                        for dj in range(-2, 3):
                            nj = j + dj
                            if 0 <= nj < img_size[1]:
                                styled_pixels[i, nj] = fg_color
                    
                    elif pattern == "diagonal":
                        # Create diagonal lines
                        for d in range(-2, 3):
                            ni, nj = i + d, j + d
                            if 0 <= ni < img_size[0] and 0 <= nj < img_size[1]:
                                styled_pixels[ni, nj] = fg_color
                    
                    elif pattern == "cross":
                        # Create a cross pattern
                        for d in range(-2, 3):
                            ni_h, nj_v = i + d, j + d
                            if 0 <= ni_h < img_size[0]:
                                styled_pixels[ni_h, j] = fg_color
                            if 0 <= nj_v < img_size[1]:
                                styled_pixels[i, nj_v] = fg_color
                    
                    elif pattern == "diamond":
                        # Create diamond pattern
                        size = 3  # Size of diamond
                        for di in range(-size, size+1):
                            for dj in range(-size, size+1):
                                if abs(di) + abs(dj) <= size:
                                    ni, nj = i + di, j + dj
                                    if 0 <= ni < img_size[0] and 0 <= nj < img_size[1]:
                                        styled_pixels[ni, nj] = fg_color
                    
                    elif pattern == "mini":
                        # Create mini square pattern (smaller than default)
                        if i % 2 == 0 and j % 2 == 0:  # Only every other pixel
                            styled_pixels[i, j] = fg_color
                    
                    else:
                        # Default - just copy the pixel
                        styled_pixels[i, j] = fg_color
        
        return styled_img

    async def _get_logo_content(self, logo):
        """
        Get logo content as bytes
        
        Args:
            logo: Logo (UploadFile or bytes)
            
        Returns:
            Logo content as bytes
        """
        try:
            if isinstance(logo, UploadFile):
                content = await logo.read()
                # Reset file position to allow multiple reads if needed
                await logo.seek(0)
                return content
            elif isinstance(logo, bytes):
                return logo
            else:
                logger.error(f"Unsupported logo type: {type(logo)}")
                return None
        except Exception as e:
            logger.error(f"Error reading logo content: {str(e)}", exc_info=True)
            return None

    def _add_logo_to_qr(self, qr_image, logo_content, design, size=48, position="center", opacity=1.0):
        """
        Add logo to QR code
        
        Args:
            qr_image: PIL Image of QR code
            logo_content: Logo image content as bytes
            design: QR design options
            size: Logo size in pixels
            position: Logo position ('center', 'top-left', 'top-right', 'bottom-left', 'bottom-right')
            opacity: Logo opacity (0.0-1.0)
            
        Returns:
            PIL Image with logo added
        """
        try:
            # Open logo image
            logo_img = Image.open(io.BytesIO(logo_content))
            
            # Handle fractional size (0.0-1.0) or pixel size
            if size <= 1.0:
                # Fractional size (0.0-1.0)
                logo_size_percent = min(size, 0.3)  # Max 30%
            else:
                # Pixel size - convert to percentage
                logo_size_percent = min(size / qr_image.width, 0.3)
            logo_size_px = int(qr_image.width * logo_size_percent)
            
            # Resize logo while preserving aspect ratio
            logo_img.thumbnail((logo_size_px, logo_size_px), Image.LANCZOS)
            
            # Create an RGBA version of the logo with transparency
            if logo_img.mode != 'RGBA':
                logo_img = logo_img.convert('RGBA')
                
            # Apply opacity
            if opacity < 1.0:
                # Get the alpha channel
                data = logo_img.getdata()
                new_data = []
                for item in data:
                    # Apply opacity to the alpha channel (4th value in RGBA)
                    new_data.append((item[0], item[1], item[2], int(item[3] * opacity) if len(item) > 3 else 255))
                logo_img.putdata(new_data)
            
            # Determine logo position
            qr_width, qr_height = qr_image.size
            logo_width, logo_height = logo_img.size
            
            if position == "center":
                pos_x = (qr_width - logo_width) // 2
                pos_y = (qr_height - logo_height) // 2
            elif position == "top-left":
                pos_x = qr_width // 10
                pos_y = qr_height // 10
            elif position == "top-right":
                pos_x = qr_width - logo_width - (qr_width // 10)
                pos_y = qr_height // 10
            elif position == "bottom-left":
                pos_x = qr_width // 10
                pos_y = qr_height - logo_height - (qr_height // 10)
            elif position == "bottom-right":
                pos_x = qr_width - logo_width - (qr_width // 10)
                pos_y = qr_height - logo_height - (qr_height // 10)
            else:
                # Default to center
                pos_x = (qr_width - logo_width) // 2
                pos_y = (qr_height - logo_height) // 2
            
            # Create a copy of the QR code image
            qr_with_logo = qr_image.copy()
            
            # Paste logo onto QR code image
            qr_with_logo.paste(logo_img, (pos_x, pos_y), logo_img)
            
            return qr_with_logo
            
        except Exception as e:
            logger.error(f"Error adding logo to QR: {str(e)}", exc_info=True)
            # Return the original QR code if there's an error
            return qr_image

    def _add_logo_to_svg(self, svg_string, logo_content, size=48, position="center", opacity=1.0):
        """
        Add logo to SVG QR code
        
        Args:
            svg_string: SVG QR code as string
            logo_content: Logo image content as bytes
            size: Logo size in pixels
            position: Logo position ('center', 'top-left', 'top-right', 'bottom-left', 'bottom-right')
            opacity: Logo opacity (0.0-1.0)
            
        Returns:
            SVG QR code with logo as string
        """
        try:
            # More robust SVG dimension detection
            # First try to get dimensions from viewBox
            svg_width = svg_height = 300  # Default fallback dimensions
            
            viewbox_match = re.search(r'viewBox=["\']([^"\']+)["\']', svg_string)
            if viewbox_match:
                viewbox = viewbox_match.group(1).split()
                if len(viewbox) == 4:
                    svg_width = float(viewbox[2])
                    svg_height = float(viewbox[3])
                    logger.debug(f"SVG dimensions from viewBox: {svg_width}x{svg_height}")
            else:
                # Fall back to width/height attributes
                svg_width_match = re.search(r'width=["\'](\d+)["\']', svg_string)
                svg_height_match = re.search(r'height=["\'](\d+)["\']', svg_string)
                if svg_width_match and svg_height_match:
                    svg_width = int(svg_width_match.group(1))
                    svg_height = int(svg_height_match.group(1))
                    logger.debug(f"SVG dimensions from attributes: {svg_width}x{svg_height}")
                else:
                    logger.warning("Could not determine SVG dimensions, using defaults")
            
            # Check if the logo is an SVG file
            is_svg_logo = False
            svg_logo_content = None
            
            # Simple check for SVG content
            if logo_content.startswith(b'<?xml') or logo_content.startswith(b'<svg'):
                try:
                    svg_logo_content = logo_content.decode('utf-8')
                    is_svg_logo = True
                    logger.info("Detected SVG logo, using direct SVG embedding")
                except UnicodeDecodeError:
                    # Not a valid UTF-8 SVG
                    is_svg_logo = False
            
            # Handle fractional size (0.0-1.0) or pixel size
            if size <= 1.0:
                # Fractional size (0.0-1.0)
                logo_size_percent = min(size, 0.3)  # Max 30%
            else:
                # Pixel size - convert to percentage
                logo_size_percent = min(size / svg_width, 0.3)
            logo_size_px = int(svg_width * logo_size_percent)
            
            # Maximum logo size limit for performance
            MAX_LOGO_SIZE = 1024  # pixels
            logo_size_px = min(logo_size_px, MAX_LOGO_SIZE)
            
            # Handle SVG logo directly if detected
            if is_svg_logo and svg_logo_content:
                # Clean up SVG logo content
                svg_logo_content = re.sub(r'<\?xml[^>]+\?>', '', svg_logo_content)
                svg_logo_content = re.sub(r'<!DOCTYPE[^>]+>', '', svg_logo_content)
                
                # Extract SVG dimensions to calculate scaling
                logo_width = logo_height = logo_size_px
                logo_viewbox = re.search(r'viewBox=["\']([^"\']+)["\']', svg_logo_content)
                if logo_viewbox:
                    vb = logo_viewbox.group(1).split()
                    if len(vb) == 4:
                        orig_width = float(vb[2])
                        orig_height = float(vb[3])
                        # Maintain aspect ratio
                        if orig_width > orig_height:
                            logo_height = int(logo_size_px * (orig_height / orig_width))
                        elif orig_height > orig_width:
                            logo_width = int(logo_size_px * (orig_width / orig_height))
                
                # Determine logo position
                if position == "center":
                    pos_x = (svg_width - logo_width) // 2
                    pos_y = (svg_height - logo_height) // 2
                elif position == "top-left":
                    pos_x = svg_width // 10
                    pos_y = svg_height // 10
                elif position == "top-right":
                    pos_x = svg_width - logo_width - (svg_width // 10)
                    pos_y = svg_height // 10
                elif position == "bottom-left":
                    pos_x = svg_width // 10
                    pos_y = svg_height - logo_height - (svg_height // 10)
                elif position == "bottom-right":
                    pos_x = svg_width - logo_width - (svg_width // 10)
                    pos_y = svg_height - logo_height - (svg_height // 10)
                else:
                    # Default to center
                    pos_x = (svg_width - logo_width) // 2
                    pos_y = (svg_height - logo_height) // 2
                
                # Extract the SVG content without the outer <svg> tag
                svg_content_match = re.search(r'<svg[^>]*>(.*?)</svg>', svg_logo_content, re.DOTALL)
                if svg_content_match:
                    inner_content = svg_content_match.group(1)
                    # Create a group with proper transform and opacity
                    logo_element = (
                        f'<g transform="translate({pos_x},{pos_y}) '
                        f'scale({logo_width/float(orig_width) if "orig_width" in locals() else 1})" '
                        f'opacity="{opacity}">{inner_content}</g>'
                    )
                    # Add to main SVG
                    svg_string = svg_string.replace("</svg>", f"{logo_element}</svg>")
                    return svg_string
            
            # Process raster image logo (default path)
            # Open logo image
            logo_img = Image.open(io.BytesIO(logo_content))
            
            # Resize logo while preserving aspect ratio
            logo_img.thumbnail((logo_size_px, logo_size_px), Image.LANCZOS)
            
            # Create an RGBA version of the logo with transparency
            if logo_img.mode != 'RGBA':
                logo_img = logo_img.convert('RGBA')
                
            # Apply opacity
            if opacity < 1.0:
                # Get the alpha channel
                data = logo_img.getdata()
                new_data = []
                for item in data:
                    # Apply opacity to the alpha channel (4th value in RGBA)
                    new_data.append((item[0], item[1], item[2], int(item[3] * opacity) if len(item) > 3 else 255))
                logo_img.putdata(new_data)
            
            # Determine logo position
            logo_width, logo_height = logo_img.size
            
            if position == "center":
                pos_x = (svg_width - logo_width) // 2
                pos_y = (svg_height - logo_height) // 2
            elif position == "top-left":
                pos_x = svg_width // 10
                pos_y = svg_height // 10
            elif position == "top-right":
                pos_x = svg_width - logo_width - (svg_width // 10)
                pos_y = svg_height // 10
            elif position == "bottom-left":
                pos_x = svg_width // 10
                pos_y = svg_height - logo_height - (svg_height // 10)
            elif position == "bottom-right":
                pos_x = svg_width - logo_width - (svg_width // 10)
                pos_y = svg_height - logo_height - (svg_height // 10)
            else:
                # Default to center
                pos_x = (svg_width - logo_width) // 2
                pos_y = (svg_height - logo_height) // 2
            
            # Save logo to a BytesIO object as PNG
            logo_bytes = io.BytesIO()
            logo_img.save(logo_bytes, format="PNG")
            logo_bytes.seek(0)
            
            # Encode the logo as base64
            logo_base64 = base64.b64encode(logo_bytes.read()).decode('utf-8')
            
            # Create image element for SVG
            image_element = f'<image x="{pos_x}" y="{pos_y}" width="{logo_width}" height="{logo_height}" preserveAspectRatio="none" xlink:href="data:image/png;base64,{logo_base64}" style="opacity:{opacity}"/>'
            
            # Add xlink namespace if not present
            if 'xmlns:xlink' not in svg_string:
                svg_string = svg_string.replace('<svg', '<svg xmlns:xlink="http://www.w3.org/1999/xlink"')
            
            # Add logo to SVG QR code before closing tag
            svg_string = svg_string.replace("</svg>", f"{image_element}</svg>")
            
            return svg_string
            
        except Exception as e:
            logger.error(f"Error adding logo to SVG: {str(e)}", exc_info=True)
            # Return the original SVG QR code if there's an error
            return svg_string

    def _convert_image_to_svg(self, image):
        """
        Convert a PIL Image to SVG
        
        Args:
            image: PIL Image
        
        Returns:
            SVG string
        """
        # Convert image to RGBA
        if image.mode != 'RGBA':
            image = image.convert('RGBA')
        
        # Get image size
        width, height = image.size
        
        # Create SVG string
        svg_string = f"<svg width=\"{width}\" height=\"{height}\">"
        
        # Iterate over pixels
        for x in range(width):
            for y in range(height):
                # Get pixel color
                r, g, b, a = image.getpixel((x, y))
                
                # Add pixel to SVG string
                svg_string += f"<rect x=\"{x}\" y=\"{y}\" width=\"1\" height=\"1\" fill=\"rgba({r}, {g}, {b}, {a/255})\"/>"
        
        # Close SVG string
        svg_string += "</svg>"
        
        return svg_string
