'use client';

import React, { useState, useEffect, useRef, useCallback } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { QRService } from '@/services/qrService.modern';
import { toast } from '@/components/ui/use-toast';
import { useSubscriptionFeatures } from '@/hooks/utils/useSubscriptionFeatures';
import Link from 'next/link';
import { 
  QRPreview, 
  QRExportOptions,
} from '@/components/ui/qr';
import { QRDesignOptions as QRDesignOptionsComponent } from '@/components/ui/qr/QRDesignOptions';
import type { 
  QRType, 
  QRCategory,
  QRDesignOptions 
} from '@/types/qr';

import type {
  PosterGenerationOptions
} from '../../../../../types/poster';

import { QRAdvancedOptions, transformAdvancedOptionsForApi } from '@/components/ui/qr/creator/QRAdvancedOptions';
import { QRDataForm } from '@/components/ui/QRDataForm/QRDataForm';
import { hasRequiredFields } from '@/lib/qr-required-fields';
import { getDefaultDataForType } from '@/lib/qr/DefaultDataProvider';
import QRCategoryGrid from '@/components/ui/QRCategoryGrid';
import QRTypeGrid from '@/components/ui/QRTypeGrid';
import { QR_CATEGORIES, QR_TYPES, getQRTypesByCategory, getCategoriesByPlan, isQRTypeAvailableForPlan, getPopularQRTypes } from '@/lib/qr-registry';
import clsx from 'clsx';
import { 
  Button, 
} from '@/components/ui';
import { 
  ArrowRightIcon, 
  ArrowLeftIcon, 
  DocumentIcon,
} from '@heroicons/react/24/outline';
import { QRContextualTools } from '@/components/ui/qr';
import { useRouter } from 'next/navigation';
import { useRealApi, shouldSimulateErrors } from '@/lib/dev/devTools';
import { DevToolbar } from '@/components/dev/DevToolbar';
import type { QRCodeCreateRequest } from '@/types/qr-operations';

// Utility function to check if we're in development environment
const isDevEnvironment = () => {
  return process.env.NODE_ENV === 'development' || process.env.NEXT_PUBLIC_APP_ENV === 'development';
};

/**
 * Apple-style QR Creator Page
 * Implements a fluid 4-phase wizard approach with persistent preview and smoother transitions
 */
export default function QRCreatorPage() {
  const router = useRouter();
  const { featureMatrix, checkFeatureAccess, isPaidPlan, getSubscriptionTier } = useSubscriptionFeatures();
  const [currentPlan, setCurrentPlan] = useState<string>(getSubscriptionTier());
  const [hasAdvancedFeatures, setHasAdvancedFeatures] = useState(isPaidPlan());
  const containerRef = useRef<HTMLDivElement>(null);
  
  // Check for developer plan override
  useEffect(() => {
    const checkDevPlan = () => {
      if (typeof window !== 'undefined') {
        const devPlanOverride = localStorage.getItem('qrvibe_dev_plan');
        if (devPlanOverride) {
          setCurrentPlan(devPlanOverride);
          setHasAdvancedFeatures(devPlanOverride === 'growth' || devPlanOverride === 'enterprise');
        } else {
          const currentTier = getSubscriptionTier();
          setCurrentPlan(currentTier);
          setHasAdvancedFeatures(currentTier === 'growth' || currentTier === 'enterprise');
        }
      }
    };
    
    checkDevPlan();
    // Don't re-run this effect on every render
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  // Define types for our app
  type CreationPhase = 'purpose' | 'advanced' | 'design' | 'use';
  type ActionVariant = 'primary' | 'secondary' | 'tertiary';
  
  const [phase, setPhase] = useState<CreationPhase>('purpose');
  const [prevPhase, setPrevPhase] = useState<CreationPhase | null>(null);
  
  // Store the phases array for reference
  const phases: CreationPhase[] = ['purpose', 'advanced', 'design', 'use'];
  
  // State for the selected QR type
  const [qrType, setQrType] = useState<QRType>('url' as QRType);
  
  // State for QR code data, design options, and other variables
  const [qrData, setQrData] = useState<Record<string, any>>(() => getDefaultDataForType(qrType));
  
  useEffect(() => {
    setQrData(getDefaultDataForType(qrType));
  }, [qrType]);
  
  // State for the design options - using properties from the QRDesignOptions interface
  // Start with minimal defaults to show a clean black and white QR code
  const [designOptions, setDesignOptions] = useState<Partial<QRDesignOptions>>({
    // Core design
    size: 256,
    margin: 10,

    // Pattern (module style) - use default/square for clean appearance
    pattern: 'square',

    // Marker styles - keep simple for default
    marker_border_style: 'square',
    marker_center_style: 'square', // Changed from 'circle' to 'square' for clean default

    // Logo options
    logo_size: 0.3, // 30% fractional size
    logo_position: 'center',
    logo_opacity: 1,

    // Frame options - start with no frame
    frame_style: 'none',
    frame_font: 'SF Pro',
    frame_font_size: 16,

    // Color options - explicit black and white
    foreground_color: '#000000',
    background_color: '#FFFFFF',
    color_mode: 'solid',

    // Shape properties (legacy compatibility)
    corner_radius: 0,

    // Error correction
    error_correction: 'M',

    // AI enhancement
    ai_enhance: false,
    ai_enhance_options: {}
  });
  
  // State for advanced options
  const [qrAdvancedState, setQrAdvancedState] = useState({
    dynamic: true,
    is_public: true,
    password_protected: false,
    password: '',
    geo_restriction: '',
    denied_countries: '',
    expiry_date: '',
    max_scans: '',
    team_id: '',
    buyer_email: '',
    enable_tracking: false,
    enable_analytics: false,
    connection_quality_tracking: false,
    schedule_active: false,
    schedule_start: '',
    schedule_end: ''
  });
  
  // Define available features based on plan tier
  const availableFeatures = {
    advancedDesign: currentPlan !== 'free',
    customLogo: currentPlan !== 'free',
    aiEnhancement: currentPlan === 'growth' || currentPlan === 'enterprise',
    batchUpload: currentPlan === 'growth' || currentPlan === 'enterprise',
    analytics: currentPlan !== 'free',
    passwordProtection: currentPlan !== 'free',
    geofencing: currentPlan === 'growth' || currentPlan === 'enterprise',
    expiryDate: currentPlan !== 'free',
    maxScans: currentPlan !== 'free'
  };
  
  // State for uploaded logo
  const [logoFile, setLogoFile] = useState<File | string | null>(null);
  
  // State for created QR code
  const [createdQR, setCreatedQR] = useState<{
    id: number | string; 
    url: string;
    imageUrl?: string;
    qrType?: string;
  } | null>(null);
  const [isCreating, setIsCreating] = useState(false);
  
  // State for preview URL
  const [previewUrl, setPreviewUrl] = useState<string>('');

  // QR Type options
  const qrTypes = [
    { value: 'url', label: 'Website URL', icon: '🌐', description: 'Link to a website or web page' },
    { value: 'email', label: 'Email', icon: '✉️', description: 'Compose an email when scanned' },
    { value: 'text', label: 'Plain Text', icon: '📝', description: 'Display simple text message' },
    { value: 'phone', label: 'Phone Number', icon: '📞', description: 'Dial a phone number when scanned' },
    { value: 'vcard', label: 'Contact Card', icon: '👤', description: 'Share contact information' },
    { value: 'wifi', label: 'WiFi Network', icon: '📶', description: 'Connect to WiFi network' },
    // Additional QR types would be listed here
  ];

  // Helper function to resolve circular dependencies
  const createHandlers = (
    qrType: QRType, 
    qrData: Record<string, any>, 
    phase: CreationPhase, 
    prevPhase: CreationPhase | null, 
    setPrevPhase: React.Dispatch<React.SetStateAction<CreationPhase | null>>, 
    setPhase: React.Dispatch<React.SetStateAction<CreationPhase>>, 
    containerRef: React.RefObject<HTMLDivElement>, 
    logoFile: File | string | null, 
    designOptions: Partial<QRDesignOptions>, 
    qrAdvancedState: {
      dynamic: boolean;
      is_public: boolean;
      password_protected: boolean;
      password: string;
      geo_restriction: string;
      denied_countries: string;
      expiry_date: string;
      max_scans: string;
      team_id: string;
      buyer_email: string;
      enable_tracking: boolean;
      enable_analytics: boolean;
      connection_quality_tracking: boolean;
      schedule_active: boolean;
      schedule_start: string;
      schedule_end: string;
    },
    setQrAdvancedState: React.Dispatch<React.SetStateAction<{
      dynamic: boolean;
      is_public: boolean;
      password_protected: boolean;
      password: string;
      geo_restriction: string;
      denied_countries: string;
      expiry_date: string;
      max_scans: string;
      team_id: string;
      buyer_email: string;
      enable_tracking: boolean;
      enable_analytics: boolean;
      connection_quality_tracking: boolean;
      schedule_active: boolean;
      schedule_start: string;
      schedule_end: string;
    }>>,
    setCreatedQR: React.Dispatch<React.SetStateAction<{
      id: number | string; 
      url: string;
      imageUrl?: string;
      qrType?: string;
    } | null>>, 
    setIsCreating: React.Dispatch<React.SetStateAction<boolean>>
  ) => {
    // Forward declarations to resolve circular references
    let handleCreateQR: () => Promise<void>;
    let handleNext: () => void;
    
    // Determine if the current phase can proceed
    const canProceed = () => {
      if (phase === 'purpose') {
        // Always allow proceeding from purpose (type selection) phase
        return true;
      }
      
      if (phase === 'advanced') {
        // Always allow proceeding from advanced phase
        return true;
      }
      
      if (phase === 'design') {
        // Check if required data is filled for the QR type
        return hasRequiredFields(qrType, qrData);
      }
      
      return true;
    };
    
    // Create the QR code using the modern QRService
    handleCreateQR = async () => {
      if (!canProceed()) return;
      
      setIsCreating(true);
      
      try {
        // Transform QR data to match API expectations
        const transformedData = QRService.transformDataForApi(qrType, qrData);
        
        if (!transformedData) {
          throw new Error(`Failed to transform data for QR type: ${qrType}`);
        }
        
        // Transform advanced options for API
        const advancedOptions = transformAdvancedOptionsForApi(qrAdvancedState);
        
        // Create QR code request payload using the proper API format
        const qrCreateRequest: QRCodeCreateRequest = {
          qr_type: qrType,
          name: qrData.name || `${QR_TYPES[qrType]?.label} QR - ${new Date().toLocaleDateString()}`,
          description: qrData.description || `Created on ${new Date().toLocaleString()}`,
          data: transformedData,
          design_options: {
            ...designOptions,
            logo_url: typeof logoFile === 'string' ? logoFile : undefined,
          },
          options: advancedOptions,
        };
        
        console.log('Creating QR code with data:', qrCreateRequest);
        
        // Check if we should use mock data (from developer tools setting)
        if (!useRealApi() || shouldSimulateErrors()) {
          console.log('Using mock QR creation with request:', qrCreateRequest);
          
          // Mock successful response
          setCreatedQR({
            id: `mock-${Date.now()}`,
            url: `https://qrvibe.app/q/${Date.now().toString(36)}`,
            imageUrl: '/assets/sample-qr.png',
            qrType: qrType,
          });
          
          toast({
            title: 'QR Code Created (Mock)',
            description: 'Your QR code has been generated using mock data. Toggle developer tools to use real API.',
          });
        } else {
          // Use the standard QR service
          const response = await QRService.create(qrCreateRequest);
          
          if (!response || !response.id) {
            throw new Error('Invalid response from QR creation service');
          }
          
          // Handle successful QR creation - transform the QRCodeResource to match our state type
          setCreatedQR({
            id: response.id,
            url: response.url || `https://qrvibe.app/q/${response.id}`,
            imageUrl: response.imageUrl || response.image_url,
            qrType: response.type || response.qr_type || qrType
          });
          
          toast({
            title: 'QR Code Created',
            description: 'Your QR code has been generated successfully.',
          });
          
          // Transition to use phase
          setPhase('use');
        }
      } catch (error: any) {
        console.error('Error creating QR code:', error);
        toast({
          title: 'Error creating QR code',
          description: error?.message || 'An unexpected error occurred. Please try again.',
          variant: 'destructive',
        });
        
        // Track error for analytics
        if (window.gtag) {
          window.gtag('event', 'qr_creation_error', {
            qrType: qrType,
            error: error?.message || 'Unknown error'
          });
        }
      } finally {
        setIsCreating(false);
      }
    };
    
    // Phase navigation handlers with smooth transitions
    handleNext = () => {
      if (!canProceed()) return;
      
      // Store the previous phase for animation direction
      setPrevPhase(phase);
      
      // Determine the next phase based on current phase
      if (phase === 'purpose') {
        setPhase('advanced');
      } else if (phase === 'advanced') {
        setPhase('design');
      } else if (phase === 'design') {
        // Either create the QR code or move to the final phase
        if (createdQR) {
          setPhase('use');
        } else {
          handleCreateQR();
        }
      }
      
      // Scroll container to top for mobile view with smooth animation
      if (containerRef.current) {
        containerRef.current.scrollTo({
          top: 0,
          behavior: 'smooth'
        });
      }
    };
    
    const handleBack = () => {
      // Store the previous phase for animation direction
      setPrevPhase(phase);
      
      // Determine the previous phase based on current phase
      if (phase === 'advanced') {
        setPhase('purpose');
      } else if (phase === 'design') {
        setPhase('advanced');
      } else if (phase === 'use') {
        setPhase('design');
      }
      
      // Scroll container to top for mobile view with smooth animation
      if (containerRef.current) {
        containerRef.current.scrollTo({
          top: 0,
          behavior: 'smooth'
        });
      }
    };
    
    return { canProceed, handleCreateQR, handleNext, handleBack };
  };

  const { canProceed, handleCreateQR, handleNext, handleBack } = createHandlers(
    qrType, 
    qrData, 
    phase, 
    prevPhase, 
    setPrevPhase, 
    setPhase, 
    containerRef, 
    logoFile, 
    designOptions, 
    qrAdvancedState, 
    setQrAdvancedState, 
    setCreatedQR, 
    setIsCreating
  );

  // Handle QR type selection with elegant fade transitions
  const hasUserInteractedWithForm = useRef(false);
  const handleTypeSelect = useCallback((type: QRType) => {
    setQrType(type);
    
    // Reset data when changing types but provide user-friendly default examples
    // based on the QR type to avoid empty preview
    const newData: Record<string, any> = {};
    
    // Use our DefaultDataProvider to get sensible defaults for this QR type
    // This will work for all 168 QR types defined in the system
    const defaultData = getDefaultDataForType(type);
    
    // Preserve any existing data for this type if the user has already entered it
    if (qrData[type] && typeof qrData[type] === 'object') {
      // For object types (like vcard, wifi, etc.), merge with defaults
      newData[type] = { ...defaultData, ...qrData[type] };
    } else if (qrData[type]) {
      // For simple types (like url, text, etc.), use existing value
      newData[type] = qrData[type];
    } else {
      // For types user hasn't used yet, use the defaults
      newData[type] = defaultData;
    }
    
    // For simple string fields at the root level, preserve those too
    Object.keys(qrData).forEach(key => {
      if (typeof qrData[key] === 'string' && key in defaultData) {
        newData[key] = qrData[key] || defaultData[key];
      }
    });
    
    setQrData(newData);
    
    // Move to the next phase automatically if user hasn't modified anything
    if (!hasUserInteractedWithForm.current) {
      setPhase('advanced');
    } else {
      handleNext();
    }
  }, [qrData, hasUserInteractedWithForm, setPhase, handleNext]);

  // Handle QR data change with real-time preview updates
  const handleDataChange = useCallback((data: Record<string, any>) => {
    hasUserInteractedWithForm.current = true;
    
    // For QR types that expect a nested structure
    if (typeof data === 'object' && !Array.isArray(data)) {
      setQrData(prevData => {
        // Create a deep copy of the previous data
        const newData = { ...prevData };
        
        // Merge in the new data
        Object.keys(data).forEach(key => {
          // Handle nested objects
          if (typeof data[key] === 'object' && data[key] !== null) {
            newData[key] = { ...(newData[key] || {}), ...data[key] };
          } else {
            newData[key] = data[key];
          }
        });
        
        // Always include the most recent data directly at the root level
        // to ensure it's accessible to the preview component
        if (qrType && data[qrType]) {
          Object.assign(newData, data[qrType]);
        }
        
        return newData;
      });
    } else {
      // Simple merge for flat data structures
      setQrData(prevData => ({
        ...prevData,
        ...data
      }));
    }
    
    // Log for debugging
    console.log('QR data updated:', data);
  }, [qrType]);

  // Handle design changes with live preview
  const handleDesignChange = useCallback((newDesign: Partial<QRDesignOptions>) => {
    hasUserInteractedWithForm.current = true;
    setDesignOptions(prev => {
      // Create a deep merged copy of the design options
      const updatedDesign = { ...prev };
      
      // Handle top-level properties
      Object.entries(newDesign).forEach(([key, value]) => {
        if (key === 'ai_enhance_options' && value && typeof value === 'object') {
          // Special handling for AI options object
          updatedDesign.ai_enhance_options = {
            ...(updatedDesign.ai_enhance_options || {}),
            ...value
          };
        } else {
          // Handle regular properties
          (updatedDesign as any)[key] = value;
        }
      });
      
      return updatedDesign;
    });
  }, []);

  // Handle logo file uploads with immediate preview updates
  const handleLogoChange = useCallback((logoFile: File | string | null) => {
    if (logoFile === null) {
      // Logo removal case
      setLogoFile(null);
      
      // Update design options to clear the logo_url
      setDesignOptions(prev => ({
        ...prev,
        logo_url: ''
      }));
    } else if (logoFile instanceof File) {
      // New logo file case
      setLogoFile(logoFile);
      
      // Read the file as Data URL for preview
      const reader = new FileReader();
      reader.onload = () => {
        const dataUrl = reader.result as string;
        
        // Update design options with the logo URL for immediate preview
        setDesignOptions(prev => ({
          ...prev,
          logo_url: dataUrl
        }));
      };
      reader.readAsDataURL(logoFile);
    } else if (typeof logoFile === 'string') {
      // Direct data URL case
      setLogoFile(logoFile);
      
      // Update design options
      setDesignOptions(prev => ({
        ...prev,
        logo_url: logoFile
      }));
    }
  }, []);

  // Add a function to remove the logo
  const removeLogo = useCallback(() => {
    handleLogoChange(null);
  }, [handleLogoChange]);

  // Reset the form to start over
  const handleReset = () => {
    // Confirm with the user before resetting
    if (window.confirm('Are you sure you want to start over? All your current progress will be lost.')) {
      setQrType('url' as QRType);
      setQrData({
        url: 'https://example.com',
        email: '<EMAIL>',
        text: 'Example text for your QR code',
        phone: '+1234567890',
        vcard: {
          name: 'John Doe',
          company: 'Example Company',
          title: 'Product Manager',
          phone: '+1234567890',
          email: '<EMAIL>',
          website: 'https://example.com',
          address: '123 Example St, City'
        },
        wifi: {
          ssid: 'ExampleWiFi',
          password: 'password123',
          encryption: 'WPA',
          hidden: false
        },
        event: {
          title: 'Example Event',
          start: new Date().toISOString().split('T')[0],
          end: new Date(Date.now() + 86400000).toISOString().split('T')[0],
          location: 'Example Venue',
          description: 'This is an example event'
        },
        social: {
          platform: 'twitter',
          username: 'exampleuser'
        },
        payment: {
          amount: '10.00',
          currency: 'USD',
          recipient: 'Example Business',
          memo: 'Payment for services'
        }
      });
      setDesignOptions({
        size: 256,
        margin: 10,
        pattern: 'square',
        marker_border_style: 'square',
        marker_center_style: 'square',
        logo_size: 0.3, // 30% fractional size
        logo_position: 'center',
        logo_opacity: 1,
        frame_style: 'none',
        foreground_color: '#000000',
        background_color: '#FFFFFF',
        color_mode: 'solid',
        corner_radius: 0,
        error_correction: 'M',
        ai_enhance: false,
        ai_enhance_options: {}
      });
      setLogoFile(null);
      setCreatedQR(null);
      setPhase('purpose');
      hasUserInteractedWithForm.current = false;
    }
  };

  // State for category selection
  const [selectedCategory, setSelectedCategory] = useState<QRCategory | null>(null);
  
  // Render the content for the current phase
  const renderPhaseContent = () => {
    const slideDirection = getSlideDirection();
    
    return (
      <AnimatePresence mode="wait">
        <motion.div
          key={phase}
          initial={{ opacity: 0, x: slideDirection }}
          animate={{ opacity: 1, x: 0 }}
          exit={{ opacity: 0, x: -slideDirection }}
          transition={{ 
            type: 'spring', 
            stiffness: 300, 
            damping: 30 
          }}
          className="w-full"
        >
          {phase === 'purpose' && (
            <div className="space-y-6">
              {!selectedCategory ? (
                // Category selection phase
                <>
                  <div className="text-center mb-8">
                    <h1 className="text-2xl font-medium mb-2">Choose QR Category</h1>
                    <p className="text-gray-500">What type of QR code would you like to create?</p>
                  </div>
                  
                  <QRCategoryGrid
                    categories={Object.values(QR_CATEGORIES)}
                    selectedCategory={selectedCategory}
                    onSelectCategory={setSelectedCategory}
                    availableCategories={getCategoriesByPlan(currentPlan as any).map(cat => cat.id)}
                    onUpgradeClick={() => router.push('/pricing')}
                    className="mb-8"
                  />
                  
                  <div className="text-center">
                    <p className="text-sm text-gray-500 mb-3">Most popular QR code types</p>
                    <div className="grid grid-cols-2 sm:grid-cols-4 lg:grid-cols-5 gap-3">
                      {getPopularQRTypes(5, currentPlan as any).map(type => (
                        <motion.div
                          key={type.id}
                          whileHover={{ scale: 1.05 }}
                          whileTap={{ scale: 0.95 }}
                          className="flex flex-col items-center justify-center p-3 rounded-lg border border-gray-100 bg-white cursor-pointer hover:shadow-sm"
                          onClick={() => {
                            setSelectedCategory(type.category);
                            setQrType(type.id);
                            setPhase('advanced');
                          }}
                        >
                          <div className="w-8 h-8 flex items-center justify-center rounded-full bg-gray-50 mb-1">
                            <span className="text-lg">{type.icon}</span>
                          </div>
                          <h3 className="font-medium text-center text-xs">{type.label}</h3>
                        </motion.div>
                      ))}
                    </div>
                  </div>
                </>
              ) : (
                // QR type selection within the chosen category
                <>
                  <div className="text-center mb-6">
                    <h1 className="text-2xl font-medium mb-2">{QR_CATEGORIES[selectedCategory].label} QR Codes</h1>
                    <p className="text-gray-500">{QR_CATEGORIES[selectedCategory].description}</p>
                    <motion.button
                      whileHover={{ scale: 1.05 }}
                      whileTap={{ scale: 0.95 }}
                      className="mt-3 inline-flex items-center text-xs font-medium text-primary"
                      onClick={() => setSelectedCategory(null)}
                    >
                      <ArrowLeftIcon className="h-3 w-3 mr-1" />
                      Back to categories
                    </motion.button>
                  </div>
                  
                  <QRTypeGrid
                    types={getQRTypesByCategory(selectedCategory, currentPlan as any)}
                    selectedType={qrType}
                    onSelectType={setQrType}
                    availableTypes={Object.values(QR_TYPES)
                      .filter(t => t.category === selectedCategory && isQRTypeAvailableForPlan(t.id, currentPlan as any))
                      .map(t => t.id)}
                    onUpgradeClick={() => router.push('/pricing')}
                    onSelectComplete={handleTypeSelect}
                    className="mb-6"
                  />
                  
                  {qrType && (
                    <div className="flex justify-center mt-6">
                      <Button 
                        variant="default"
                        size="lg"
                        onClick={() => setPhase('advanced')}
                        className="flex items-center gap-2"
                      >
                        Continue with {QR_TYPES[qrType]?.label}
                        <ArrowRightIcon className="h-4 w-4" />
                      </Button>
                    </div>
                  )}
                </>
              )}
            </div>
          )}
          
          {phase === 'advanced' && (
            <div className="space-y-6">
              <div className="text-center mb-6">
                <h1 className="text-2xl font-medium mb-2">
                  Advanced Options
                </h1>
                <p className="text-gray-500">
                  Configure additional settings for your QR code
                </p>
              </div>
              
              <QRAdvancedOptions 
                initialState={qrAdvancedState}
                onUpdateOptions={(newOptions) => setQrAdvancedState(newOptions)}
                onNext={() => setPhase('design')}
                onBack={() => setPhase('purpose')}
              />
            </div>
          )}
          
          {phase === 'design' && (
            <div className="space-y-6">
              <div className="text-center mb-6">
                <h1 className="text-2xl font-medium mb-2">
                  {qrType === 'url' ? 'Design Your Website QR Code' :
                   qrType === 'email' ? 'Design Your Email QR Code' :
                   qrType === 'text' ? 'Design Your Text QR Code' :
                   qrType === 'phone' ? 'Design Your Phone QR Code' :
                   qrType === 'vcard' ? 'Design Your Contact QR Code' :
                   qrType === 'wifi' ? 'Design Your WiFi QR Code' :
                   'Design Your QR Code'}
                </h1>
                <p className="text-gray-500">Customize your QR code content and appearance</p>
              </div>
              
              <div className="flex flex-col lg:flex-row gap-6">
                <div className="w-full lg:w-2/3 space-y-6">
                
                  <div className="bg-white border border-gray-100 rounded-2xl p-6 shadow-sm">
                    <h2 className="text-lg font-medium mb-4">QR Content</h2>
                    <QRDataForm
                      type={qrType}
                      data={qrData}
                      onChange={handleDataChange}
                    />
                  </div>
                
                  <div className="bg-white border border-gray-100 rounded-2xl p-6 shadow-sm">
                    <h2 className="text-lg font-medium mb-4">Appearance</h2>
                    
                    {/* Wrap QRDesignOptionsComponent in a collapsible section - closed by default */}
                    <div className="space-y-3">
                      <div className="border rounded-lg">
                        <button 
                          type="button"
                          onClick={() => {
                            const designSection = document.getElementById('design-options-content');
                            if (designSection) {
                              const isHidden = designSection.classList.contains('hidden');
                              if (isHidden) {
                                designSection.classList.remove('hidden');
                              } else {
                                designSection.classList.add('hidden');
                              }
                            }
                          }}
                          className="w-full flex items-center justify-between p-3 text-left focus:outline-none"
                        >
                          <span className="font-medium">Design Options</span>
                          <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 9l-7 7-7-7" />
                          </svg>
                        </button>
                        <div id="design-options-content" className="p-4 border-t hidden">
                          <QRDesignOptionsComponent
                            qrType={qrType}
                            data={qrData}
                            initialDesign={{
                              size: designOptions.size,
                              margin: designOptions.margin,
                              pattern: designOptions.pattern,
                              marker_border_style: designOptions.marker_border_style,
                              marker_border_color: designOptions.marker_border_color,
                              marker_center_style: designOptions.marker_center_style,
                              marker_center_color: designOptions.marker_center_color,
                              logo_size: designOptions.logo_size,
                              logo_position: designOptions.logo_position,
                              logo_opacity: designOptions.logo_opacity,
                              frame_style: designOptions.frame_style,
                              frame_color: designOptions.frame_color,
                              frame_text: designOptions.frame_text,
                              frame_font: designOptions.frame_font,
                              frame_font_size: designOptions.frame_font_size,
                              frame_thickness: designOptions.frame_thickness,
                              foreground_color: designOptions.foreground_color,
                              background_color: designOptions.background_color,
                              color_mode: designOptions.color_mode,
                              corner_radius: designOptions.corner_radius,
                              error_correction: designOptions.error_correction,
                              ai_enhance: designOptions.ai_enhance,
                              ai_enhance_options: designOptions.ai_enhance_options
                            }}
                            logo={logoFile !== null ? logoFile : undefined}
                            onDesignChange={handleDesignChange}
                            onLogoChange={handleLogoChange}
                          />
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
                
                <div className="w-full lg:w-1/3">
                  <div className="sticky top-24 bg-white border border-gray-100 rounded-2xl p-6 shadow-sm">
                    <h2 className="text-lg font-medium mb-4">Preview</h2>
                    <div className="flex flex-col items-center">
                      <QRPreview
                        qrType={qrType}
                        data={qrData}
                        designOptions={{
                          size: designOptions.size,
                          margin: designOptions.margin,
                          pattern: designOptions.pattern,
                          marker_border_style: designOptions.marker_border_style,
                          marker_border_color: designOptions.marker_border_color,
                          marker_center_style: designOptions.marker_center_style,
                          marker_center_color: designOptions.marker_center_color,
                          logo_size: designOptions.logo_size,
                          logo_position: designOptions.logo_position,
                          logo_opacity: designOptions.logo_opacity,
                          frame_style: designOptions.frame_style,
                          frame_color: designOptions.frame_color,
                          frame_text: designOptions.frame_text,
                          frame_font: designOptions.frame_font,
                          frame_font_size: designOptions.frame_font_size,
                          frame_thickness: designOptions.frame_thickness,
                          foreground_color: designOptions.foreground_color,
                          background_color: designOptions.background_color,
                          color_mode: designOptions.color_mode,
                          corner_radius: designOptions.corner_radius,
                          error_correction: designOptions.error_correction,
                          ai_enhance: designOptions.ai_enhance,
                          ai_enhance_options: designOptions.ai_enhance_options
                        }}
                        logo={logoFile !== null ? logoFile : undefined}
                        className="w-full max-w-[400px] aspect-square mb-4"
                      />
                      <div className="text-center text-sm text-gray-500 mt-2">
                        {qrData.url || qrData.email || qrData.phone || qrData.text || 'Your QR Code Preview'}
                      </div>
                      <div className="mt-6 w-full">
                        <button 
                          type="button"
                          onClick={handleNext}
                          className="w-full inline-flex justify-center items-center rounded-md border border-transparent bg-primary py-2 px-4 text-sm font-medium text-white shadow-sm hover:bg-primary-dark focus:outline-none focus:ring-2 focus:ring-primary focus:ring-offset-2"
                        >
                          Continue to Next Step
                          <ArrowRightIcon className="ml-2 h-4 w-4" />
                        </button>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          )}
          
          {phase === 'use' && createdQR && (
            <div className="space-y-8">
              <div className="text-center mb-8">
                <h1 className="text-2xl font-medium mb-2">Your QR Code is Ready!</h1>
                <p className="text-gray-500">Download, share or get the embed code for your QR code</p>
              </div>
              
              <div className="grid grid-cols-1 lg:grid-cols-2 gap-8 max-w-5xl mx-auto">
                <div className="flex flex-col items-center justify-center bg-white border border-gray-100 rounded-2xl p-8 shadow-sm">
                  {/* QR Final Preview */}
                  <div className="w-full max-w-[320px] aspect-square mb-6">
                    <img
                      src={createdQR.url}
                      alt="Generated QR Code"
                      className="w-full h-full object-contain"
                    />
                  </div>
                  <div className="text-center mt-2">
                    <h3 className="font-medium">
                      {qrType === 'url' ? qrData.url :
                      qrType === 'email' ? qrData.email :
                      qrType === 'phone' ? qrData.phone :
                      qrType === 'text' ? qrData.text :
                      qrType === 'vcard' ? qrData.vcard.name :
                      'Your QR Code'}
                    </h3>
                  </div>
                </div>
                
                <div className="bg-white border border-gray-100 rounded-2xl p-8 shadow-sm">
                  <h2 className="text-lg font-medium mb-6">Share Your QR Code</h2>
                  
                  <div className="space-y-4">
                    <QRExportOptions
                      qrId={createdQR.id.toString()}
                      qrType={qrType}
                      data={qrData}
                      designOptions={{
                        size: designOptions.size,
                        margin: designOptions.margin,
                        pattern: designOptions.pattern,
                        marker_border_style: designOptions.marker_border_style,
                        marker_border_color: designOptions.marker_border_color,
                        marker_center_style: designOptions.marker_center_style,
                        marker_center_color: designOptions.marker_center_color,
                        logo_size: designOptions.logo_size,
                        logo_position: designOptions.logo_position,
                        logo_opacity: designOptions.logo_opacity,
                        frame_style: designOptions.frame_style,
                        frame_color: designOptions.frame_color,
                        frame_text: designOptions.frame_text,
                        frame_font: designOptions.frame_font,
                        frame_font_size: designOptions.frame_font_size,
                        frame_thickness: designOptions.frame_thickness,
                        foreground_color: designOptions.foreground_color,
                        background_color: designOptions.background_color,
                        color_mode: designOptions.color_mode,
                        corner_radius: designOptions.corner_radius,
                        error_correction: designOptions.error_correction,
                        ai_enhance: designOptions.ai_enhance,
                        ai_enhance_options: designOptions.ai_enhance_options
                      }}
                      onDownloadComplete={() => {
                        // Analytics would be tracked here
                      }}
                    />
                    
                    <div className="border-t border-gray-100 pt-4 mt-6">
                      <Link
                        href={`/dashboard/qr-codes/${createdQR.id}`}
                        className="text-primary hover:text-primary/90 font-medium flex items-center justify-center w-full"
                      >
                        <DocumentIcon className="w-5 h-5 mr-2" />
                        View QR Code details
                      </Link>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          )}
        </motion.div>
      </AnimatePresence>
    );
  };

  // Get phase name for display
  const getPhaseName = (currentPhase: CreationPhase) => {
    switch (currentPhase) {
      case 'purpose': return 'Purpose';
      case 'advanced': return 'Advanced';
      case 'design': return 'Design';
      case 'use': return 'Use';
      default: return '';
    }
  };

  // Determine content slide direction for animations
  const getSlideDirection = () => {
    if (!prevPhase) return 0;
    
    const phases: CreationPhase[] = ['purpose', 'advanced', 'design', 'use'];
    const prevIndex = phases.indexOf(prevPhase);
    const currentIndex = phases.indexOf(phase);
    
    return prevIndex < currentIndex ? -100 : 100;
  };

  // Get contextual actions based on current phase
  const getSecondaryActions = (): string[] => {
    if (phase === 'purpose') {
      // No secondary actions in the purpose phase
      return [];
    } else if (phase === 'advanced') {
      return [];
    } else if (phase === 'design') {
      return ['reset'];
    } else if (phase === 'use' && createdQR) {
      return ['share', 'copy', 'restart'];
    }
    
    return [];
  };

  // Get primary action based on current phase
  const getPrimaryAction = (): string => {
    if (phase === 'design') {
      return 'create';
    } else if (phase === 'use') {
      return 'download';
    }
    
    return 'create';
  };
  
  // Handle contextual action clicks
  const handleContextualAction = (action: string) => {
    switch (action) {
      case 'create':
        handleCreateQR();
        break;
      case 'download':
        if (createdQR) {
          // Trigger download via URL
          const link = document.createElement('a');
          link.href = createdQR.url;
          link.download = `qr-code-${Date.now()}.png`;
          link.click();
        }
        break;
      case 'share':
        if (createdQR && navigator.share) {
          navigator.share({
            title: 'My QR Code',
            text: 'Check out my QR Code created with QRVibe',
            url: createdQR.url
          });
        }
        break;
      case 'copy':
        if (createdQR) {
          navigator.clipboard.writeText(createdQR.url);
          toast({
            title: "URL Copied",
            description: "QR code URL copied to clipboard"
          });
        }
        break;
      case 'reset':
      case 'restart':
        handleReset();
        break;
      default:
        break;
    }
  };

  // Generate a preview whenever relevant state changes
  const generateQRPreview = async () => {
    if (isCreating) return;
    
    try {
      // Transform QR data to match API expectations
      const transformedData = QRService.transformDataForApi(qrType, qrData);
      
      if (!transformedData) {
        throw new Error(`Failed to transform data for QR type: ${qrType}`);
      }
      
      // Transform advanced options for API
      const advancedOptions = transformAdvancedOptionsForApi(qrAdvancedState);
      
      // Create preview request using standardized format
      const previewRequest = {
        qrType,
        data: transformedData,
        designOptions: {
          ...designOptions,
          logo_url: typeof logoFile === 'string' ? logoFile : undefined,
        },
        logoFile: logoFile instanceof File ? logoFile : undefined,
        advancedOptions,
      };
      
      // Get SVG preview content with standardized API call (SVG-only)
      const svgContent = await QRService.generatePreviewSvg(previewRequest);
      setPreviewUrl(svgContent);
      
      // If we're in the final phase, generate the actual QR
      if (phase === 'use' && !createdQR) {
        const qrCreateRequest: QRCodeCreateRequest = {
          qr_type: qrType,
          name: qrData.name || `${QR_TYPES[qrType]?.label} QR - ${new Date().toLocaleDateString()}`,
          description: qrData.description || `Created on ${new Date().toLocaleString()}`,
          data: transformedData,
          design_options: {
            ...designOptions,
            logo_url: typeof logoFile === 'string' ? logoFile : undefined,
          },
          options: advancedOptions,
        };
        
        const response = await QRService.create(qrCreateRequest);
        
        if (!response || !response.id) {
          throw new Error('Invalid response from QR creation service');
        }
        
        // Handle successful QR creation - transform the QRCodeResource to match our state type
        setCreatedQR({
          id: response.id,
          url: response.url || `https://qrvibe.app/q/${response.id}`,
          imageUrl: response.imageUrl || response.image_url,
          qrType: response.type || response.qr_type || qrType
        });
      }
    } catch (error: any) {
      console.error('Error generating QR preview:', error);
      // Don't show error toasts for preview failures - it's annoying during typing
    }
  };

  useEffect(() => {
    const generateQRPreview = async () => {
      if (isCreating) return;
      
      try {
        // Transform QR data to match API expectations
        const transformedData = QRService.transformDataForApi(qrType, qrData);
        
        if (!transformedData) {
          throw new Error(`Failed to transform data for QR type: ${qrType}`);
        }
        
        // Transform advanced options for API
        const advancedOptions = transformAdvancedOptionsForApi(qrAdvancedState);
        
        // Create preview request using standardized format
        const previewRequest = {
          qrType,
          data: transformedData,
          designOptions: {
            ...designOptions,
            logo_url: typeof logoFile === 'string' ? logoFile : undefined,
          },
          logoFile: logoFile instanceof File ? logoFile : undefined,
          advancedOptions,
        };
        
        // Get SVG preview content with standardized API call (SVG-only)
        const svgContent = await QRService.generatePreviewSvg(previewRequest);
        setPreviewUrl(svgContent);
        
        // If we're in the final phase, generate the actual QR
        if (phase === 'use' && !createdQR) {
          const qrCreateRequest: QRCodeCreateRequest = {
            qr_type: qrType,
            name: qrData.name || `${QR_TYPES[qrType]?.label} QR - ${new Date().toLocaleDateString()}`,
            description: qrData.description || `Created on ${new Date().toLocaleString()}`,
            data: transformedData,
            design_options: {
              ...designOptions,
              logo_url: typeof logoFile === 'string' ? logoFile : undefined,
            },
            options: advancedOptions,
          };
          
          const response = await QRService.create(qrCreateRequest);
          
          if (!response || !response.id) {
            throw new Error('Invalid response from QR creation service');
          }
          
          // Handle successful QR creation - transform the QRCodeResource to match our state type
          setCreatedQR({
            id: response.id,
            url: response.url || `https://qrvibe.com/q/${response.id}`,
            imageUrl: response.imageUrl || response.image_url,
            qrType: response.type || response.qr_type || qrType
          });
        }
      } catch (error: any) {
        console.error('Error generating QR preview:', error);
        // Don't show error toasts for preview failures - it's annoying during typing
      }
    };

    // Debounce the preview generation to avoid overwhelming the API
    const timeoutId = setTimeout(() => {
      generateQRPreview();
    }, 300); // 300ms debounce

    return () => clearTimeout(timeoutId);
  }, [qrType, qrData, designOptions, isCreating]);

  // Convert logo files to appropriate format based on context
  const getLogoForPreview = useCallback((logoValue: File | string | null) => {
    // For QRPreview component, we can only pass File instances
    if (logoValue instanceof File) {
      return logoValue;
    }
    // Other types (string, null) should be undefined for QRPreview
    return undefined;
  }, []);

  return (
    <div className="relative min-h-screen bg-gray-50">
      {/* Main container */}
      <div className="container max-w-[1920px] mx-auto px-4 sm:px-6">
        {/* Phase indicator - Apple style subtle progress */}
        <div className="pt-8 pb-4">
          <div className="bg-white rounded-full h-1.5 w-full max-w-md mx-auto overflow-hidden">
            <motion.div 
              className="bg-primary h-full rounded-full"
              initial={{ width: '0%' }}
              animate={{ 
                width: phase === 'purpose' ? '25%' : 
                         phase === 'advanced' ? '50%' : 
                         phase === 'design' ? '75%' : '100%' 
              }}
              transition={{ duration: 0.3, ease: "easeInOut" }}
            />
          </div>
          
          {/* Phase labels */}
          <div className="flex justify-between max-w-md mx-auto mt-2">
            {(['purpose', 'advanced', 'design', 'use'] as CreationPhase[]).map((phaseItem) => (
              <div 
                key={phaseItem}
                className={`text-xs font-medium ${
                  phase === phaseItem ? 'text-primary' : 
                  phases.indexOf(phaseItem as CreationPhase) < phases.indexOf(phase) ? 
                  'text-gray-600' : 'text-gray-400'
                }`}
              >
                {getPhaseName(phaseItem)}
              </div>
            ))}
          </div>
        </div>
        
        {/* Main phase content */}
        <div className="pb-24">
          {renderPhaseContent()}
        </div>
      
        
      </div>
      
      {/* Back button - only show if not in first phase */}
      {phase !== 'purpose' && (
        <motion.div
          initial={{ opacity: 0, x: -20 }}
          animate={{ opacity: 1, x: 0 }}
          exit={{ opacity: 0, x: -20 }}
          transition={{ type: "spring", stiffness: 300, damping: 30 }}
          className="fixed bottom-6 left-6 z-40"
        >
          <Button
            onClick={handleBack}
            variant="secondary"
            size="icon"
            className="rounded-full w-12 h-12 shadow-md"
          >
            <ArrowLeftIcon className="h-5 w-5" />
          </Button>
        </motion.div>
      )}
      
      {/* Floating action button with contextual tools */}
      <QRContextualTools
        mode="create"
        onAction={handleContextualAction}
        primaryAction={getPrimaryAction()}
        secondaryActions={getSecondaryActions()}
        position="bottom-right"
      />
      
      {/* Add developer toolbar (only visible in development) */}
      <DevToolbar />
    </div>
  );
}
