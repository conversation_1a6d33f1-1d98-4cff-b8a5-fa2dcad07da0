// DesignOptionsTemplates.ts
// Contains the QRDesignTemplate type and DESIGN_TEMPLATES array for QRDesignOptions

import type { QRDesignOptions } from '@/types/qr';

export interface QRDesignTemplate {
  id: string;
  name: string;
  tier: 'starter' | 'growth' | 'enterprise';
  preview?: string;
  config: Partial<QRDesignOptions>;
}

export const DESIGN_TEMPLATES: QRDesignTemplate[] = [
  {
    id: 'minimal',
    name: 'Minimal',
    tier: 'starter',
    preview: '/assets/templates/qr/minimal-preview.png',
    config: {
      foreground_color: '#000000', 
      background_color: '#FFFFFF',
      color_mode: 'solid',
      pattern: 'classic',
      marker_border_style: 'square',
      marker_center_style: 'circle',
      error_correction: 'M',
    }
  },
  {
    id: 'professional',
    name: 'Professional',
    tier: 'starter',
    preview: '/assets/templates/qr/professional-preview.png',
    config: {
      foreground_color: '#0A3D62', 
      background_color: '#FFFFFF',
      color_mode: 'solid',
      pattern: 'rounded',
      marker_border_style: 'rounded',
      marker_center_style: 'circle',
      error_correction: 'M',
    }
  },
  {
    id: 'sf-pro',
    name: 'SF Pro',
    tier: 'starter',
    preview: '/assets/templates/qr/sf-pro-preview.png',
    config: {
      foreground_color: '#147EFB', 
      background_color: '#F5F5F7',
      color_mode: 'radial_gradient',
      pattern: 'rounded',
      marker_border_style: 'rounded',
      marker_center_style: 'circle',
      error_correction: 'M',
    }
  },
  {
    id: 'creative',
    name: 'Creative',
    tier: 'growth',
    preview: '/assets/templates/qr/creative-preview.png',
    config: {
      foreground_color: '#8E44AD', 
      background_color: '#F5F5F7',
      color_mode: 'solid',
      pattern: 'dots',
      marker_border_style: 'circle',
      marker_center_style: 'dot',
      error_correction: 'Q',
      ai_enhance: true,
      ai_enhance_options: { style: 'artistic', prompt: 'organic pattern with artistic elements' },
    }
  },
  {
    id: 'modern',
    name: 'Modern',
    tier: 'growth',
    preview: '/assets/templates/qr/modern-preview.png',
    config: {
      foreground_color: '#1ABC9C', 
      background_color: '#FFFFFF',
      color_mode: 'radial_gradient',
      pattern: 'mosaic',
      marker_border_style: 'rounded',
      marker_center_style: 'circle',
      error_correction: 'Q',
      ai_enhance: true,
      ai_enhance_options: { style: 'modern', prompt: 'modern gradient pattern with depth' },
    }
  },
  {
    id: 'brand',
    name: 'Brand Identity',
    tier: 'enterprise',
    preview: '/assets/templates/qr/brand-preview.png',
    config: {
      foreground_color: '#3366FF', 
      background_color: '#FFFFFF',
      color_mode: 'solid',
      pattern: 'classic',
      marker_border_style: 'square',
      marker_center_style: 'circle',
      error_correction: 'H',
      logo_size: 96,
      logo_opacity: 1,
    }
  }
];
