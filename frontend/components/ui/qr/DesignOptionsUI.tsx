// Presentational UI for QR Design Options
// This component receives all design state and update handlers as props, and renders the Apple-style UI.

import React from 'react';
import type { QRDesignOptions } from '@/types/qr';
import { Label } from '@/components/ui/label';
import { Slider } from '@/components/ui/slider';
import { Switch } from '@/components/ui/switch';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Button } from '@/components/ui/button';
import { Popover, PopoverContent, PopoverTrigger } from '@/components/ui/popover';
import { HexColorPicker } from 'react-colorful';
import { cn } from '@/lib/utils';

// Helper for contrast
function getContrastColor(hexColor: string): string {
  const hex = hexColor.replace('#', '');
  const r = parseInt(hex.substr(0, 2), 16);
  const g = parseInt(hex.substr(2, 2), 16);
  const b = parseInt(hex.substr(4, 2), 16);
  const luminance = (0.299 * r + 0.587 * g + 0.114 * b) / 255;
  return luminance > 0.5 ? '#000000' : '#FFFFFF';
}

import type { QRDesignTemplate } from './DesignOptionsTemplates';

export interface DesignOptionsUIProps {
  design: QRDesignOptions;
  updateDesign: <K extends keyof QRDesignOptions>(property: K, value: QRDesignOptions[K]) => void;
  activeTab: string;
  setActiveTab: (tab: string) => void;
  availableTemplates: QRDesignTemplate[];
  selectedTemplate: string | null;
  applyTemplate: (templateId: string) => void;
  handleLogoUpload: (e: React.ChangeEvent<HTMLInputElement>) => void;
  removeLogo: () => void;
  handleLogoSizeChange: (value: number) => void;
  isFeatureAvailable: (feature: 'ai' | 'gradient' | 'custom-eye' | 'large-logo') => boolean;
  onSave?: () => void;
  onBack?: () => void;
}

export const DesignOptionsUI: React.FC<DesignOptionsUIProps> = ({
  design,
  updateDesign,
  activeTab,
  setActiveTab,
  availableTemplates,
  selectedTemplate,
  applyTemplate,
  handleLogoUpload,
  removeLogo,
  handleLogoSizeChange,
  isFeatureAvailable,
  onSave,
  onBack,
}) => {
  return (
    <Tabs value={activeTab} onValueChange={setActiveTab} className="w-full">
      <TabsList className="grid grid-cols-6 mb-6">
        <TabsTrigger value="style" className="text-sm">Style</TabsTrigger>
        <TabsTrigger value="color" className="text-sm">Color</TabsTrigger>
        <TabsTrigger value="marker" className="text-sm">Marker</TabsTrigger>
        <TabsTrigger value="logo" className="text-sm">Logo</TabsTrigger>
        <TabsTrigger value="frame" className="text-sm">Frame</TabsTrigger>
        <TabsTrigger value="advanced" className="text-sm">Advanced</TabsTrigger>
        {/* Add Animation, Overlay, AI as needed */}
      </TabsList>
      {/* --- Style Tab --- */}
      <TabsContent value="style" className="space-y-6">
        {/* Pattern (Module Style) */}
        <div className="space-y-3">
          <Label className="text-sm font-medium">Pattern Style</Label>
          <div className="grid grid-cols-4 gap-2 mb-2">
            {[
              'square', 'rounded', 'dots', 'classy', 'classy-rounded',
              'horizontal', 'vertical', 'diagonal', 'mosaic', 'tiny-dots',
              'diamond', 'blocks', 'circles', 'mini'
            ].map(pattern => (
              <Button
                key={pattern}
                onClick={() => updateDesign('pattern', pattern)}
                className={cn(
                  "p-2 border rounded-lg flex flex-col items-center justify-center gap-1 h-16 w-16",
                  design.pattern === pattern ? "border-primary bg-primary/5" : "border-gray-200"
                )}
                variant="outline"
              >
                <div className={cn(
                  "w-8 h-8 flex items-center justify-center",
                  pattern === 'square' && "bg-black",
                  pattern === 'rounded' && "bg-black rounded-lg",
                  pattern === 'dots' && "bg-black rounded-full",
                  pattern === 'classy' && "bg-black border-2 border-white",
                  pattern === 'classy-rounded' && "bg-black border-2 border-white rounded-lg",
                  pattern === 'horizontal' && "bg-black h-[4px] w-full",
                  pattern === 'vertical' && "bg-black w-[4px] h-full",
                  pattern === 'diagonal' && "bg-black rotate-45 w-8 h-[4px]",
                  pattern === 'mosaic' && "bg-black border border-white w-6 h-6",
                  pattern === 'tiny-dots' && "bg-black rounded-full w-3 h-3",
                  pattern === 'diamond' && "bg-black rotate-45 w-6 h-6",
                  pattern === 'blocks' && "bg-black w-7 h-7",
                  pattern === 'circles' && "bg-black rounded-full border-2 border-white",
                  pattern === 'mini' && "bg-black w-4 h-4"
                )}></div>
                <span className="text-[9px] capitalize truncate">{pattern}</span>
              </Button>
            ))}
          </div>
        </div>
        {/* Add more style controls as needed */}
      </TabsContent>
      {/* --- Color Tab --- */}
      <TabsContent value="color" className="space-y-6">
        {/* Colors (foreground/background) */}
        <div className="space-y-3">
          <Label className="text-sm font-medium">Foreground Color</Label>
          <Popover>
            <PopoverTrigger asChild>
              <Button 
                variant="outline" 
                className="w-full flex justify-between items-center"
                style={{ backgroundColor: design.foreground_color || '#000000' }}
              >
                <span className="text-xs" style={{ color: getContrastColor(design.foreground_color || '#000000') }}>
                  {design.foreground_color || '#000000'}
                </span>
                <div className="h-4 w-4 rounded-full border" style={{ backgroundColor: design.foreground_color || '#000000' }}></div>
              </Button>
            </PopoverTrigger>
            <PopoverContent className="w-auto p-3">
              <HexColorPicker 
                color={design.foreground_color || '#000000'} 
                onChange={(color) => updateDesign('foreground_color', color)} 
              />
            </PopoverContent>
          </Popover>
        </div>
        <div className="space-y-3">
          <Label className="text-sm font-medium">Background Color</Label>
          <Popover>
            <PopoverTrigger asChild>
              <Button 
                variant="outline" 
                className="w-full flex justify-between items-center"
                style={{ backgroundColor: design.background_color || '#FFFFFF' }}
              >
                <span className="text-xs" style={{ color: getContrastColor(design.background_color || '#FFFFFF') }}>
                  {design.background_color || '#FFFFFF'}
                </span>
                <div className="h-4 w-4 rounded-full border" style={{ backgroundColor: design.background_color || '#FFFFFF' }}></div>
              </Button>
            </PopoverTrigger>
            <PopoverContent className="w-auto p-3">
              <HexColorPicker 
                color={design.background_color || '#FFFFFF'} 
                onChange={(color) => updateDesign('background_color', color)} 
              />
            </PopoverContent>
          </Popover>
        </div>
        {/* Color mode (solid, radial_gradient, etc.) */}
        <div className="space-y-3">
          <Label className="text-sm font-medium">Color Mode</Label>
          <Select value={design.color_mode || 'solid'} onValueChange={val => updateDesign('color_mode', val)}>
            <SelectTrigger className="w-full">
              <SelectValue placeholder="Color Mode" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="solid">Solid</SelectItem>
              <SelectItem value="radial_gradient">Radial Gradient</SelectItem>
              {/* Add more modes as supported */}
            </SelectContent>
          </Select>
        </div>
      </TabsContent>
      {/* --- Marker Tab --- */}
      <TabsContent value="marker" className="space-y-6">
        {/* Marker Border Style */}
        <div className="space-y-3">
          <Label className="text-sm font-medium">Marker border</Label>
          <div className="flex flex-wrap gap-2">
            {[
              'square', 'rounded', 'circle', 'diamond', 'cut-corner',
              'dotted', 'dashed', 'extra-rounded'
            ].map(style => (
              <Button
                key={style}
                onClick={() => updateDesign('marker_border_style', style)}
                className={cn(
                  'p-2 border rounded-lg flex flex-col items-center justify-center w-14 h-14',
                  design.marker_border_style === style ? 'border-primary bg-primary/5 shadow-lg' : 'border-gray-200'
                )}
                variant="outline"
              >
                {/* Visual representation of each marker style */}
                <div className={cn(
                  'w-8 h-8',
                  style === 'square' && 'border-2 border-black',
                  style === 'rounded' && 'border-2 border-black rounded-lg',
                  style === 'circle' && 'border-2 border-black rounded-full',
                  style === 'diamond' && 'border-2 border-black rotate-45',
                  style === 'cut-corner' && 'border-2 border-black',
                  style === 'dotted' && 'border-2 border-dotted border-black',
                  style === 'dashed' && 'border-2 border-dashed border-black',
                  style === 'extra-rounded' && 'border-2 border-black rounded-2xl'
                )}></div>
                <span className="text-[8px] capitalize">{style}</span>
              </Button>
            ))}
          </div>
        </div>
        {/* Marker Center Style */}
        <div className="space-y-3">
          <Label className="text-sm font-medium">Marker center</Label>
          <div className="flex flex-wrap gap-2">
            {[
              'square', 'circle', 'dot', 'diamond', 'star', 'plus', 'heart',
              'cross', 'rhombus', 'triangle', 'ring'
            ].map(center => (
              <Button
                key={center}
                onClick={() => updateDesign('marker_center_style', center)}
                className={cn(
                  'p-2 border rounded-lg flex flex-col items-center justify-center w-14 h-14',
                  design.marker_center_style === center ? 'border-primary bg-primary/5 shadow-lg' : 'border-gray-200'
                )}
                variant="outline"
              >
                {/* Visual representation of each center style */}
                <div className={cn(
                  'w-8 h-8 flex items-center justify-center',
                  center === 'square' && 'bg-black w-5 h-5',
                  center === 'circle' && 'bg-black rounded-full w-5 h-5',
                  center === 'dot' && 'bg-black rounded-full w-3 h-3',
                  center === 'diamond' && 'bg-black rotate-45 w-4 h-4',
                  center === 'rhombus' && 'bg-black transform rotate-45 w-4 h-4'
                )}>
                  {center === 'star' && (
                    <svg width="16" height="16" viewBox="0 0 24 24" fill="currentColor" className="text-black text-xl leading-none">
                      <path d="M12 2l2.4 7.4h7.6l-6 4.6 2.3 7.5-6.3-4.5-6.3 4.5 2.3-7.5-6-4.6h7.6z"/>
                    </svg>
                  )}
                  {center === 'plus' && (
                    <svg width="16" height="16" viewBox="0 0 24 24" fill="currentColor" className="text-black text-xl leading-none">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1.5} d="M12 2v20M2 12h20"/>
                    </svg>
                  )}
                  {center === 'heart' && (
                    <svg width="16" height="16" viewBox="0 0 24 24" fill="currentColor" className="text-black text-xl leading-none">
                      <path d="M12 21.35l-1.45-1.32C5.4 15.36 2 12.28 2 8.5 2 5.42 4.42 3 7.5 3c1.74 0 3.41.81 4.5 2.09C13.09 3.81 14.76 3 16.5 3 19.58 3 22 5.42 22 8.5c0 3.78-3.4 6.86-8.55 11.54L12 21.35z"/>
                    </svg>
                  )}
                  {center === 'cross' && (
                    <>
                      <div className="absolute w-full h-[2px] bg-black rotate-45 top-1/2 -translate-y-1/2"></div>
                      <div className="absolute w-full h-[2px] bg-black -rotate-45 top-1/2 -translate-y-1/2"></div>
                    </>
                  )}
                  {center === 'triangle' && (
                    <div className="border-solid border-black border-t-0 border-l-[12px] border-r-[12px] border-b-[20px] border-l-transparent border-r-transparent"></div>
                  )}
                  {center === 'ring' && <div className="border-2 border-black rounded-full w-5 h-5"></div>}
                </div>
                <span className="text-[8px] capitalize">{center}</span>
              </Button>
            ))}
          </div>
        </div>
        {/* Custom Marker Color Toggle */}
        <div className="flex items-center gap-3">
          <Switch
            checked={!!design.custom_marker_color}
            onCheckedChange={checked => updateDesign('custom_marker_color', checked)}
            id="custom-marker-color"
          />
          <Label htmlFor="custom-marker-color" className="text-sm">Custom marker color</Label>
        </div>
        {/* Marker Border Color Picker */}
        {design.custom_marker_color && (
          <div className="space-y-3">
            <Label className="text-sm font-medium">Marker border color</Label>
            <Popover>
              <PopoverTrigger asChild>
                <Button
                  variant="outline"
                  className="w-full flex justify-between items-center"
                  style={{ backgroundColor: design.marker_border_color || '#000000' }}
                >
                  <span className="text-xs" style={{ color: getContrastColor(design.marker_border_color || '#000000') }}>
                    {design.marker_border_color || '#000000'}
                  </span>
                  <div className="h-4 w-4 rounded-full border" style={{ backgroundColor: design.marker_border_color || '#000000' }}></div>
                </Button>
              </PopoverTrigger>
              <PopoverContent className="w-auto p-3">
                <HexColorPicker
                  color={design.marker_border_color || '#000000'}
                  onChange={color => updateDesign('marker_border_color', color)}
                />
              </PopoverContent>
            </Popover>
          </div>
        )}
        {/* Marker Center Color Picker */}
        {design.custom_marker_color && (
          <div className="space-y-3">
            <Label className="text-sm font-medium">Marker center color</Label>
            <Popover>
              <PopoverTrigger asChild>
                <Button
                  variant="outline"
                  className="w-full flex justify-between items-center"
                  style={{ backgroundColor: design.marker_center_color || '#000000' }}
                >
                  <span className="text-xs" style={{ color: getContrastColor(design.marker_center_color || '#000000') }}>
                    {design.marker_center_color || '#000000'}
                  </span>
                  <div className="h-4 w-4 rounded-full border" style={{ backgroundColor: design.marker_center_color || '#000000' }}></div>
                </Button>
              </PopoverTrigger>
              <PopoverContent className="w-auto p-3">
                <HexColorPicker
                  color={design.marker_center_color || '#000000'}
                  onChange={color => updateDesign('marker_center_color', color)}
                />
              </PopoverContent>
            </Popover>
          </div>
        )}
      </TabsContent>
      {/* --- Logo Tab --- */}
      <TabsContent value="logo" className="space-y-6">
        <div className="space-y-6">
          {/* Logo Upload */}
          <div className="space-y-3">
            <Label className="text-sm font-medium">Upload Logo (PNG only)</Label>
            <input
              type="file"
              accept="image/png"
              onChange={handleLogoUpload}
              className="block w-full text-sm text-gray-500 file:mr-4 file:py-2 file:px-4 file:rounded-full file:border-0 file:text-sm file:font-semibold file:bg-primary/10 file:text-primary hover:file:bg-primary/20"
              key={design.logo_url ? 'with-logo' : 'no-logo'} // Force re-render when logo changes
            />

            {/* Logo Preview */}
            {design.logo_url && (
              <div className="mt-3 p-3 border border-gray-200 rounded-lg bg-gray-50">
                <Label className="text-sm font-medium text-gray-700 mb-2 block">Logo Preview</Label>
                <div className="flex items-center justify-center bg-white border border-gray-200 rounded-lg p-4">
                  <img
                    src={design.logo_url}
                    alt="Logo preview"
                    className="max-w-[80px] max-h-[80px] object-contain"
                    style={{
                      opacity: design.logo_opacity || 1,
                    }}
                  />
                </div>
                <p className="text-xs text-gray-500 mt-2 text-center">
                  This is how your logo will appear in the QR code
                </p>
              </div>
            )}
          </div>
          {/* Logo Size (Fractional) */}
          <div className="space-y-3">
            <Label className="text-sm font-medium flex items-center gap-2">
              Logo Size
              <span className="ml-2 text-xs text-gray-500">{((design.logo_size ?? 0.2) * 100).toFixed(0)}%</span>
            </Label>
            <Slider
              min={0}
              max={1}
              step={0.01}
              value={[design.logo_size ?? 0.2]}
              onValueChange={([val]) => handleLogoSizeChange(val)}
              className="w-full"
              aria-label="Logo size"
            />
          </div>
          {/* Logo Opacity */}
          <div className="space-y-3">
            <Label className="text-sm font-medium flex items-center gap-2">
              Logo Opacity
              <span className="ml-2 text-xs text-gray-500">{Math.round((design.logo_opacity ?? 1) * 100)}%</span>
            </Label>
            <Slider
              min={0}
              max={1}
              step={0.01}
              value={[design.logo_opacity ?? 1]}
              onValueChange={([val]) => updateDesign('logo_opacity', val)}
              className="w-full"
              aria-label="Logo opacity"
            />
          </div>
          {/* Logo Position */}
          <div className="space-y-3">
            <Label className="text-sm font-medium">Logo Position</Label>
            <Select
              value={design.logo_position || 'center'}
              onValueChange={val => updateDesign('logo_position', val)}
            >
              <SelectTrigger className="w-full">
                <SelectValue placeholder="Logo Position" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="center">Center</SelectItem>
                <SelectItem value="top">Top</SelectItem>
                <SelectItem value="bottom">Bottom</SelectItem>
                <SelectItem value="left">Left</SelectItem>
                <SelectItem value="right">Right</SelectItem>
              </SelectContent>
            </Select>
          </div>
          {/* Remove Logo Button */}
          {design.logo_url && (
            <div>
              <Button variant="destructive" onClick={removeLogo} className="mt-2">
                Remove Logo
              </Button>
            </div>
          )}
          {/* Logo Best Practices */}
          <div className="p-3 bg-blue-50 rounded-md mt-4">
            <h3 className="text-sm font-medium text-blue-800">Logo Best Practices</h3>
            <ul className="text-xs text-blue-700 mt-2 space-y-1">
              <li>• Use transparent background for best results</li>
              <li>• Simple logos scan better than complex ones</li>
              <li>• PNG format with transparency is recommended</li>
              <li>• SVG logos work best with SVG QR codes</li>
            </ul>
          </div>
        </div>
      </TabsContent>
      {/* --- Frame Tab --- */}
      <TabsContent value="frame" className="space-y-6">
        {/* Frame Style Selection */}
        <div className="space-y-3">
          <Label className="text-sm font-medium">Frame Style</Label>
          <div className="grid grid-cols-3 gap-2">
            {['classic', 'modern', 'rounded', 'bold', 'minimal', 'none'].map(style => (
              <Button
                key={style}
                onClick={() => {
                  updateDesign('frame_style', style);
                  // Reset frame-related properties when 'none' is selected
                  if (style === 'none') {
                    updateDesign('frame_text', '');
                    updateDesign('frame_font', 'SF Pro');
                    updateDesign('frame_font_size', 16);
                    updateDesign('frame_color', '#000000');
                    updateDesign('frame_thickness', 4);
                  }
                }}
                className={cn(
                  'p-3 border rounded-lg flex flex-col items-center justify-center gap-1 h-20',
                  design.frame_style === style ? 'border-primary bg-primary/5 shadow-lg' : 'border-gray-200',
                  style === 'none' && 'opacity-60'
                )}
                variant="outline"
              >
                <div className={cn(
                  'w-10 h-10 flex items-center justify-center',
                  style === 'classic' && 'bg-gray-900 rounded-md',
                  style === 'modern' && 'bg-gradient-to-tr from-gray-700 to-gray-300 rounded-xl',
                  style === 'rounded' && 'bg-gray-900 rounded-full',
                  style === 'bold' && 'bg-gray-900 border-4 border-primary rounded-lg',
                  style === 'minimal' && 'bg-gray-100 border border-gray-300',
                  style === 'none' && 'bg-transparent border border-dashed border-gray-300'
                )}></div>
                <span className="text-xs capitalize">{style}</span>
              </Button>
            ))}
          </div>
        </div>
        {/* Frame Label Input */}
        <div className="space-y-3">
          <Label className="text-sm font-medium">Frame Label</Label>
          <input
            type="text"
            className="input input-bordered w-full rounded-lg px-3 py-2 text-base focus:ring-2 focus:ring-primary"
            placeholder="Add a label (e.g. Scan Me)"
            value={design.frame_text || ''}
            maxLength={32}
            onChange={e => updateDesign('frame_text', e.target.value)}
            style={{ fontFamily: design.frame_font || 'inherit', fontSize: design.frame_font_size || 16 }}
            disabled={design.frame_style === 'none'}
          />
        </div>
        {/* Frame Font Selection */}
        <div className="space-y-3">
          <Label className="text-sm font-medium">Frame Font</Label>
          <Select
            value={design.frame_font || 'SF Pro'}
            onValueChange={val => updateDesign('frame_font', val)}
            disabled={design.frame_style === 'none'}
          >
            <SelectTrigger className="w-full">
              <SelectValue placeholder="Font" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="SF Pro">SF Pro (Apple)</SelectItem>
              <SelectItem value="Inter">Inter</SelectItem>
              <SelectItem value="system-ui">System Default</SelectItem>
              <SelectItem value="Arial">Arial</SelectItem>
            </SelectContent>
          </Select>
        </div>
        {/* Frame Color Picker */}
        <div className="space-y-3">
          <Label className="text-sm font-medium">Frame Color</Label>
          <Popover>
            <PopoverTrigger asChild>
              <Button
                variant="outline"
                className="w-full flex justify-between items-center"
                style={{ backgroundColor: design.frame_color || '#000000' }}
                disabled={design.frame_style === 'none'}
              >
                <span className="text-xs" style={{ color: getContrastColor(design.frame_color || '#000000') }}>
                  {design.frame_color || '#000000'}
                </span>
                <div className="h-4 w-4 rounded-full border" style={{ backgroundColor: design.frame_color || '#000000' }}></div>
              </Button>
            </PopoverTrigger>
            <PopoverContent className="w-auto p-3">
              <HexColorPicker
                color={design.frame_color || '#000000'}
                onChange={color => updateDesign('frame_color', color)}
              />
            </PopoverContent>
          </Popover>
        </div>
        {/* Frame Text Size */}
        <div className="space-y-3">
          <Label className="text-sm font-medium">Label Text Size</Label>
          <Slider
            min={10}
            max={32}
            step={1}
            value={[design.frame_font_size || 16]}
            onValueChange={([val]) => updateDesign('frame_font_size', val)}
            disabled={design.frame_style === 'none'}
          />
          <span className="text-xs text-gray-500">{design.frame_font_size || 16}px</span>
        </div>
        {/* Frame Thickness */}
        <div className="space-y-3">
          <Label className="text-sm font-medium">Frame Border Thickness</Label>
          <Slider
            min={1}
            max={16}
            step={1}
            value={[design.frame_thickness || 4]}
            onValueChange={([val]) => updateDesign('frame_thickness', val)}
            disabled={design.frame_style === 'none'}
          />
          <span className="text-xs text-gray-500">{design.frame_thickness || 4}px</span>
        </div>
      </TabsContent>
      {/* --- Advanced Tab --- */}
      <TabsContent value="advanced" className="space-y-6">
        {/* SVG Rendering Options */}
        <div className="space-y-3">
          <div className="flex items-center justify-between">
            <Label className="text-sm font-medium">Use SVG Rendering</Label>
            <Switch 
              checked={design.use_svg !== false}
              onCheckedChange={(checked) => updateDesign('use_svg', checked)}
            />
          </div>
          <p className="text-xs text-gray-500">
            SVG provides higher quality QR codes that scale perfectly at any size without pixelation.
            Recommended for printing and high-resolution displays.
          </p>
        </div>
        
        {/* SVG Render Quality */}
        {design.use_svg !== false && (
          <div className="space-y-3">
            <Label className="text-sm font-medium">SVG Render Quality</Label>
            <Slider
              min={150}
              max={600}
              step={50}
              value={[design.svg_render_dpi || 300]}
              onValueChange={([val]) => updateDesign('svg_render_dpi', val)}
            />
            <div className="flex justify-between text-xs text-gray-500">
              <span>Standard</span>
              <span>{design.svg_render_dpi || 300} DPI</span>
              <span>Ultra HD</span>
            </div>
            <p className="text-xs text-gray-500">
              Higher DPI creates more detailed SVGs but larger file sizes. 300 DPI is good for most uses.
            </p>
          </div>
        )}
        
        {/* SVG Optimization Option */}
        {design.use_svg !== false && (
          <div className="space-y-2">
            <div className="flex items-center justify-between">
              <Label className="text-sm font-medium">Optimize SVG</Label>
              <Switch 
                checked={design.svg_optimize !== false}
                onCheckedChange={(checked) => updateDesign('svg_optimize', checked)}
              />
            </div>
            <p className="text-xs text-gray-500">
              Optimizes SVG for faster loading on web pages. Keep enabled unless you need specific SVG features.
            </p>
          </div>
        )}
        
        {/* Error Correction Level */}
        <div className="space-y-3">
          <Label className="text-sm font-medium">Error Correction Level</Label>
          <Select 
            value={design.error_correction || 'M'} 
            onValueChange={(val) => updateDesign('error_correction', val)}
          >
            <SelectTrigger className="w-full">
              <SelectValue placeholder="Error Correction" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="L">Low (7%)</SelectItem>
              <SelectItem value="M">Medium (15%)</SelectItem>
              <SelectItem value="Q">Quartile (25%)</SelectItem>
              <SelectItem value="H">High (30%)</SelectItem>
            </SelectContent>
          </Select>
          <p className="text-xs text-gray-500">
            Higher levels allow QR codes to be readable even when partially damaged or obscured.
            Higher error correction is recommended when using logos.
          </p>
        </div>
        
        {/* Quiet Zone / Margin */}
        <div className="space-y-3">
          <Label className="text-sm font-medium">Quiet Zone (Margin)</Label>
          <Slider
            min={0}
            max={10}
            step={1}
            value={[design.margin || 4]}
            onValueChange={([val]) => updateDesign('margin', val)}
          />
          <span className="text-xs text-gray-500">{design.margin || 4} modules</span>
          <p className="text-xs text-gray-500">
            The white space around the QR code. Most scanners need at least 4 modules.
          </p>
        </div>
        
        {/* SVG Format Best Practices */}
        {design.use_svg !== false && (
          <div className="p-3 bg-blue-50 rounded-md mt-4">
            <h3 className="text-sm font-medium text-blue-800">SVG QR Code Benefits</h3>
            <ul className="text-xs text-blue-700 mt-2 space-y-1">
              <li>• Perfect scaling to any size without quality loss</li>
              <li>• Sharper edges and cleaner appearance</li>
              <li>• Better for printing and high-resolution displays</li>
              <li>• Compatible with vector editing software</li>
              <li>• Smaller file size for simple designs</li>
            </ul>
          </div>
        )}
      </TabsContent>
      {/* Add Animation, Overlay, AI tabs as needed */}
    </Tabs>
  );
};
