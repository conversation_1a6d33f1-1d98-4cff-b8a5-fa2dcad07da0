'use client';

import React, { useState, useEffect } from 'react';
import type { QRType, QRDesignOptions } from '@/types/qr';
import { DesignOptionsUI } from './DesignOptionsUI';
import { DESIGN_TEMPLATES } from './DesignOptionsTemplates';
import { toast } from '@/components/ui/use-toast';

export interface QRDesignOptionsProps {
  qrType: QRType;
  data: Record<string, any>;
  initialDesign?: Partial<QRDesignOptions>;
  logo?: string | File;
  subscriptionTier?: 'free' | 'starter' | 'growth' | 'enterprise';
  onDesignChange?: (design: QRDesignOptions) => void;
  onSave?: () => void;
  onBack?: () => void;
  onLogoChange?: (logo: string | File | null) => void;
}

export function QRDesignOptions({
  qrType,
  data,
  initialDesign = {},
  logo,
  subscriptionTier = 'starter',
  onDesignChange,
  onSave,
  onBack,
  onLogoChange,
}: QRDesignOptionsProps) {
  // State for QR design options, initialized with new schema fields only
  const [design, setDesign] = useState<QRDesignOptions>({
    size: initialDesign?.size ?? 256,
    margin: initialDesign?.margin ?? 10,
    pattern: initialDesign?.pattern ?? 'classic',
    marker_border_style: initialDesign?.marker_border_style ?? 'square',
    marker_border_color: initialDesign?.marker_border_color ?? '#000000',
    marker_center_style: initialDesign?.marker_center_style ?? 'circle',
    marker_center_color: initialDesign?.marker_center_color ?? '#000000',
    custom_marker_color: initialDesign?.custom_marker_color ?? false,
    different_markers_colors: initialDesign?.different_markers_colors ?? false,
    logo_url: initialDesign?.logo_url ?? '',
    logo_size: initialDesign?.logo_size ?? 0.3, // Default to 30%
    logo_position: initialDesign?.logo_position ?? 'center',
    logo_opacity: initialDesign?.logo_opacity ?? 1.0,
    logo_remove_background: (initialDesign as any)?.logo_remove_background ?? false,
    frame_style: initialDesign?.frame_style ?? 'classic',
    frame_color: initialDesign?.frame_color ?? '#000000',
    frame_text: initialDesign?.frame_text ?? '',
    frame_thickness: initialDesign?.frame_thickness ?? 4,
    animation_type: initialDesign?.animation_type ?? '',
    animation_speed: initialDesign?.animation_speed ?? 1.0,
    animation_loop: initialDesign?.animation_loop ?? false,
    overlay_url: initialDesign?.overlay_url ?? '',
    overlay_blend_mode: initialDesign?.overlay_blend_mode ?? 'normal',
    foreground_color: initialDesign?.foreground_color ?? '#000000',
    background_color: initialDesign?.background_color ?? '#FFFFFF',
    color_mode: initialDesign?.color_mode ?? 'solid',
    error_correction: initialDesign?.error_correction ?? 'M',
    ai_enhance: initialDesign?.ai_enhance ?? false,
    ai_enhance_options: initialDesign?.ai_enhance_options ?? {},
    // SVG-specific options
    use_svg: initialDesign?.use_svg ?? true, // Default to SVG rendering
    svg_render_dpi: initialDesign?.svg_render_dpi ?? 300,
    svg_optimize: initialDesign?.svg_optimize ?? true,
    corner_radius: initialDesign?.corner_radius ?? 0,
  });

  // Update a single design property
  const updateDesign = <K extends keyof QRDesignOptions>(property: K, value: QRDesignOptions[K]) => {
    setDesign(prev => ({ ...prev, [property]: value }));
  };

  // Tab state for UI
  const [activeTab, setActiveTab] = useState('style');

  // Notify parent when design changes
  useEffect(() => {
    if (onDesignChange) {
      onDesignChange(design);
    }
  }, [design, onDesignChange]);

  // Template application (type-safe)
  const [selectedTemplate, setSelectedTemplate] = useState<string | null>(null);
  const availableTemplates = DESIGN_TEMPLATES.filter(template => {
    if (subscriptionTier === 'free') return false;
    if (subscriptionTier === 'starter') return template.tier === 'starter';
    if (subscriptionTier === 'growth') return ['starter', 'growth'].includes(template.tier);
    return true;
  });
  const applyTemplate = (templateId: string) => {
    const template = DESIGN_TEMPLATES.find(t => t.id === templateId);
    if (template) {
      setDesign(prev => ({ ...prev, ...template.config }));
      setSelectedTemplate(templateId);
    }
  };

  // Logo upload handler (for logo_url as DataURL)
  const handleLogoUpload = (e: React.ChangeEvent<HTMLInputElement>) => {
    const file = e.target.files?.[0];
    if (file) {
      // Check file size (max 5MB)
      if (file.size > 5 * 1024 * 1024) {
        toast({
          title: 'Logo file too large',
          description: 'Please select a PNG image under 5MB.',
          variant: 'destructive',
        });
        return;
      }
      // Strict PNG-only validation
      if (file.type !== 'image/png') {
        toast({
          title: 'Invalid logo file',
          description: 'Only PNG images are supported for QR logos.',
          variant: 'destructive',
        });
        return;
      }
      const reader = new FileReader();
      reader.onload = () => {
        const dataUrl = reader.result as string;
        updateDesign('logo_url', dataUrl);
        if (onLogoChange) {
          onLogoChange(file);
          if (onDesignChange) {
            onDesignChange({ ...design, logo_url: dataUrl });
          }
        }
      };
      reader.onerror = (error) => {
        toast({
          title: 'Error uploading logo',
          description: 'Failed to process the PNG file.',
          variant: 'destructive',
        });
      };
      reader.readAsDataURL(file);
    }
  };

  
  // Function to remove the logo
  const removeLogo = () => {
    // Update the local design state
    updateDesign('logo_url', '');
    
    // Notify the parent component
    if (onLogoChange) {
      onLogoChange(null);
      
      // If the design changes callback exists, also notify it to immediately update preview
      if (onDesignChange) {
        onDesignChange({
          ...design,
          logo_url: ''
        });
      }
    }
  };

  // Feature availability by subscription
  const isFeatureAvailable = (feature: 'ai' | 'gradient' | 'custom-eye' | 'large-logo'): boolean => {
    switch (feature) {
      case 'ai':
        return ['growth', 'enterprise'].includes(subscriptionTier);
      case 'gradient':
        return ['starter', 'growth', 'enterprise'].includes(subscriptionTier);
      case 'custom-eye':
        return subscriptionTier === 'enterprise';
      case 'large-logo':
        return ['growth', 'enterprise'].includes(subscriptionTier);
      default:
        return true;
    }
  };

  // Logo size slider handler (fractional)
  const handleLogoSizeChange = (value: number) => {
    // Clamp between 0.0 and 1.0
    const clamped = Math.max(0, Math.min(1, value));
    updateDesign('logo_size', clamped);
  };

  return (
    <DesignOptionsUI
      design={design}
      updateDesign={updateDesign}
      activeTab={activeTab}
      setActiveTab={setActiveTab}
      availableTemplates={availableTemplates}
      selectedTemplate={selectedTemplate}
      applyTemplate={applyTemplate}
      handleLogoUpload={handleLogoUpload}
      removeLogo={removeLogo}
      handleLogoSizeChange={handleLogoSizeChange}
      isFeatureAvailable={feature => {
        if (feature === 'ai') return subscriptionTier === 'enterprise';
        if (feature === 'gradient') return subscriptionTier === 'growth' || subscriptionTier === 'enterprise';
        if (feature === 'custom-eye') return subscriptionTier !== 'free';
        if (feature === 'large-logo') return subscriptionTier !== 'free';
        return false;
      }}
      onSave={onSave}
      onBack={onBack}
    />
  );
}

export default QRDesignOptions;