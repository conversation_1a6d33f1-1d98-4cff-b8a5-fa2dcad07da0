// QR Preview component that displays a real-time preview
// of a QR code with customization options using the API.
'use client';

import React, { useEffect, useState, useMemo, useRef } from 'react';
import Image from 'next/image';
import { Skeleton } from '@/components/ui/skeleton';
import { QRService } from '@/services/qrService.modern';
import type { QRDesignOptions } from '@/types/qr';
import { getDefaultDataForType } from '@/lib/qr/DefaultDataProvider';
import { normalizeQRDesignOptions } from './qrDesignNormalization';


/**
 * Enhanced QR Preview Component
 * 
 * Supports both SVG and PNG preview rendering with design options
 * and logo support. Handles all normalization and API integration.
 */
interface QRPreviewProps {
  qrType: string;
  data: Record<string, any>;
  designOptions?: Partial<QRDesignOptions>;
  logo?: File | string;
  size?: number;
  isPreview?: boolean;
  loading?: boolean;
  error?: string;
  contentKey?: string; // Key that changes when content changes
  useSvg?: boolean; // Whether to use SVG rendering
  className?: string;
}

export const QRPreview: React.FC<QRPreviewProps> = ({
  qrType,
  data,
  designOptions = {},
  logo,
  size = 350,
  isPreview = true,
  loading: externalLoading,
  error: externalError,
  contentKey = '',
  useSvg = true, // Default to SVG rendering
  className = '',
}) => {
  // State for storing the preview URL (for PNG) or SVG content
  const [previewUrl, setPreviewUrl] = useState<string | null>(null);
  const [svgContent, setSvgContent] = useState<string | null>(null);
  const [isLoading, setIsLoading] = useState<boolean>(false);
  const [error, setError] = useState<string | null>(null);
  const [debounceTimer, setDebounceTimer] = useState<NodeJS.Timeout | null>(null);

  // Refs for DOM elements
  const qrRef = useRef<HTMLDivElement>(null);

  // Set default size in designOptions if not provided
  useEffect(() => {
    if (designOptions && !designOptions.size) {
      designOptions.size = size;
    }
  }, [designOptions, size]);

  // Create a unique content key to detect changes
  const contentKeyMemo = useMemo(() => {
    // Stringify the data and design options to track all changes
    const dataString = JSON.stringify(data);
    const designString = JSON.stringify(designOptions);
    const logoKey = logo instanceof File
      ? `file-${logo.name}-${logo.size}-${logo.lastModified}`
      : typeof logo === 'string'
      ? `dataurl-${logo.substring(0, 50)}-${logo.length}` // Use first 50 chars + length for data URLs
      : 'no-logo';

    console.log('QRPreview: Content key updated', {
      qrType,
      dataChanged: Boolean(data),
      designChanged: Boolean(designOptions),
      logoChanged: Boolean(logo),
      useSvg,
    });

    return `${qrType}-${dataString}-${designString}-${logoKey}-${size}-${useSvg}`;
  }, [qrType, data, designOptions, logo, size, useSvg]);

  // Generate preview when content changes
  useEffect(() => {
    // Clear any existing debounce timer
    if (debounceTimer) {
      clearTimeout(debounceTimer);
    }

    // Set a new debounce timer (300ms delay)
    const timer = setTimeout(async () => {
      if (!qrType || !data) {
        setError('Missing QR type or data');
        setIsLoading(false);
        return;
      }

      setIsLoading(true);
      setError(null);

      try {
        // Determine if we should use SVG rendering based on props and designOptions
        const shouldUseSvg = useSvg || (designOptions?.use_svg ?? true);

        if (shouldUseSvg) {
          // Use SVG generation
          const { QRService } = await import('@/services/qrService.modern');

          // Import the normalization utility
          const { normalizeQRDesignOptions } = await import('@/components/ui/qr/qrDesignNormalization');

          // Pass ALL design options to the backend for proper rendering
          const backendOptions = {
            ...designOptions, // Include ALL design options
            size: designOptions?.size || size,
            margin: designOptions?.margin || 10,
            error_correction: designOptions?.error_correction || 'M',
            svg_optimize: true,
            svg_render_dpi: 300,
            use_svg: true
          };
          
          // Normalize options for backend
          const normalizedOptions = normalizeQRDesignOptions(backendOptions, true);

          console.log('QRPreview: Generating basic SVG for frontend styling');

          // Handle logo - support both File objects and data URL strings
          let logoFile: File | undefined;
          if (logo instanceof File) {
            logoFile = logo;
          } else if (typeof logo === 'string' && logo.startsWith('data:')) {
            // Convert data URL to File object
            try {
              const response = await fetch(logo);
              const blob = await response.blob();
              logoFile = new File([blob], 'logo.png', { type: 'image/png' });
              console.log('QRPreview: Converted data URL to File object for logo');
            } catch (error) {
              console.error('QRPreview: Failed to convert logo data URL to File:', error);
            }
          }

          // Verify the data is valid for the QR type
          const verifiedData = verifyValidData(qrType, data);

          // Generate the SVG preview from backend
          const svgString = await QRService.generatePreviewSvg({
            qrType,
            data: verifiedData,
            designOptions: normalizedOptions,
            logoFile
          });

          if (!svgString) {
            throw new Error('Failed to generate SVG preview');
          }

          // Debug: Log the raw SVG content
          console.log('Raw SVG from backend:', svgString.substring(0, 500) + '...');
          console.log('SVG length:', svgString.length);
          console.log('SVG contains <svg>:', svgString.includes('<svg'));

          // Check for finder pattern structure in raw SVG
          const rectCount = (svgString.match(/<rect/g) || []).length;
          const pathCount = (svgString.match(/<path/g) || []).length;
          console.log('Raw SVG structure:', { rectCount, pathCount });

          // Check if SVG is valid
          if (!svgString || svgString.length < 50 || !svgString.includes('<svg')) {
            console.error('Invalid SVG received from backend');
            setError('Invalid QR code generated');
            return;
          }

          // Debug: Check SVG content for QR code structure
          const hasRect = svgString.includes('<rect');
          const hasPath = svgString.includes('<path');
          const hasCircle = svgString.includes('<circle');
          const hasBackground = svgString.includes('fill="white"') || svgString.includes('fill="#ffffff"') || svgString.includes('fill="#FFFFFF"');
          const hasForeground = svgString.includes('fill="black"') || svgString.includes('fill="#000000"') || svgString.includes('fill="#000"');

          // Check for finder patterns (QR corners) - critical for scannability
          const rectMatches = svgString.match(/<rect[^>]*>/g) || [];
          const largeRects = rectMatches.filter(rect => {
            const widthMatch = rect.match(/width="([^"]+)"/);
            const heightMatch = rect.match(/height="([^"]+)"/);
            if (widthMatch && heightMatch) {
              const width = parseFloat(widthMatch[1]);
              const height = parseFloat(heightMatch[1]);
              return width >= 20 && height >= 20; // Finder patterns are typically large
            }
            return false;
          });

          console.log('SVG analysis:', {
            hasRect,
            hasPath,
            hasCircle,
            hasBackground,
            hasForeground,
            totalRects: rectMatches.length,
            largeRects: largeRects.length,
            finderPatternsDetected: largeRects.length >= 3,
            originalDesignOptions: designOptions || {},
            designOptionsKeys: Object.keys(designOptions || {})
          });

          // Log first few large rectangles to check for finder patterns
          if (largeRects.length > 0) {
            console.log('Large rectangles (potential finder patterns):', largeRects.slice(0, 5));
          }

          // Use the SVG directly from backend (backend handles all styling)
          console.log('Using SVG from backend with all styling applied');
          setSvgContent(svgString);
        }
      } catch (err) {
        console.error('QR Preview generation error:', err);
        setError(err instanceof Error ? err.message : 'Failed to generate QR preview');
      } finally {
        setIsLoading(false);
      }
    }, 800); // Increased debounce to reduce API calls

    // Cleanup function
    return () => {
      if (timer) clearTimeout(timer);
    };
  }, [qrType, data, designOptions, logo, contentKeyMemo, useSvg]);



  // Helper function to verify and clean data
  const verifyValidData = (type: string, inputData: Record<string, any>): Record<string, any> => {
    console.log('QRPreview: Verifying data for', type, inputData);

    // If the data is empty or has no keys, use defaults
    if (!inputData || Object.keys(inputData).length === 0) {
      const defaults = getDefaultDataForType(type as any);
      console.log('QRPreview: Using complete defaults for empty data');
      return defaults;
    }

    // Create a copy of the input data to avoid mutations
    const cleanData = { ...inputData };

    // Handle different QR types
    switch (type) {
      case 'url':
        // If there's no URL field or it's empty, use a valid URL from defaults
        if (!cleanData.url || cleanData.url === '') {
          const defaults = getDefaultDataForType('url' as any);
          cleanData.url = defaults.url;
          console.log('QRPreview: Using default URL');
        }
        break;

      case 'text':
        // If there's no text field or it's empty, use default text
        if (!cleanData.text || cleanData.text === '') {
          const defaults = getDefaultDataForType('text' as any);
          cleanData.text = defaults.text;
          console.log('QRPreview: Using default text');
        }
        break;

      case 'email':
        // Ensure email has required fields
        if (!cleanData.email || cleanData.email === '') {
          const defaults = getDefaultDataForType('email' as any);
          cleanData.email = defaults.email;
          console.log('QRPreview: Using default email');
        }
        break;

      case 'phone':
        // Ensure phone has required fields
        if (!cleanData.phone || cleanData.phone === '') {
          const defaults = getDefaultDataForType('phone' as any);
          cleanData.phone = defaults.phone;
          console.log('QRPreview: Using default phone');
        }
        break;

      case 'vcard':
        // Ensure vcard has at least some required fields
        if (!cleanData.first_name || cleanData.first_name === '') {
          const defaults = getDefaultDataForType('vcard' as any);
          cleanData.first_name = defaults.first_name;
          console.log('QRPreview: Using default first name for vcard');
        }
        break;

      case 'wifi':
        // Ensure wifi has required fields
        if (!cleanData.ssid || cleanData.ssid === '') {
          const defaults = getDefaultDataForType('wifi' as any);
          cleanData.ssid = defaults.ssid;
          console.log('QRPreview: Using default SSID for WiFi');
        }
        break;

      // Media types validation
      case 'audio':
        // Ensure audio has required fields
        if (!cleanData.audio_url || cleanData.audio_url === '') {
          const defaults = getDefaultDataForType('audio' as any);
          cleanData.audio_url = defaults.audio_url;
          console.log('QRPreview: Using default audio URL');
        }
        break;

      case 'video':
        // Ensure video has required fields
        if (!cleanData.video_url || cleanData.video_url === '') {
          const defaults = getDefaultDataForType('video' as any);
          cleanData.video_url = defaults.video_url;
          console.log('QRPreview: Using default video URL');
        }
        break;

      case 'document':
        // Ensure document has required fields
        if (!cleanData.document_url || cleanData.document_url === '') {
          const defaults = getDefaultDataForType('document' as any);
          cleanData.document_url = defaults.document_url;
          console.log('QRPreview: Using default document URL');
        }
        break;

      case 'file':
        // Ensure file has required fields
        if (!cleanData.file_url || cleanData.file_url === '') {
          const defaults = getDefaultDataForType('file' as any);
          cleanData.file_url = defaults.file_url;
          console.log('QRPreview: Using default file URL');
        }
        break;

      // Add more cases for other QR types as needed

      default:
        // For all other types, trust the form to provide valid data
        // Just make sure we have something to display
        if (Object.keys(cleanData).length === 0) {
          const defaults = getDefaultDataForType(type as any);
          console.log('QRPreview: Using defaults for unknown type', type);
          return defaults;
        }
    }

    console.log('QRPreview: Verified data', cleanData);
    return cleanData;
  };

  return (
    <div className={`qr-preview relative ${className || ''}`} data-qr-type={qrType}>
      {isLoading ? (
        <div
          className="flex items-center justify-center"
          style={{ width: size, height: size }}
        >
          <Skeleton className="w-full h-full" />
        </div>
      ) : error ? (
        <div
          className="flex items-center justify-center bg-gray-100 text-gray-500 text-sm text-center p-4"
          style={{ width: size, height: size }}
        >
          Failed to generate preview. Please check your data and try again.
        </div>
      ) : svgContent ? (
        <div
          className="relative rounded-lg bg-white border border-gray-200"
          style={{
            width: size,
            height: 'auto', // Allow height to adjust for label
            minHeight: size,
            maxHeight: size + 100, // Allow extra height for label
            overflow: 'visible' // Don't clip the label
          }}
        >
          <div
            dangerouslySetInnerHTML={{ __html: svgContent }}
            title="QR Code SVG Preview"
            style={{
              width: '100%',
              height: 'auto'
            }}
          />
        </div>
      ) : previewUrl ? (
        <div className="relative">
          <Image
            src={previewUrl}
            alt="QR Code Preview"
            width={size}
            height={size}
            className="rounded-lg"
            priority
          />
        </div>
      ) : (
        <div
          className="flex items-center justify-center bg-gray-100 text-gray-500 text-sm text-center p-4"
          style={{ width: size, height: size }}
        >
          Enter valid data to generate a preview
        </div>
      )}
    </div>
  );
};

export default QRPreview;