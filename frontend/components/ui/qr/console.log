Rendering /app/(user)/layout.tsx (user layout) layout.tsx:16:9
Rendering /app/layout.tsx (global layout) layout.tsx:25:9
[DashboardLayout] Checking token: Not found layout.tsx:89:29
[DashboardLayout] Waiting for hydration... layout.tsx:150:25
[DashboardLayout] Setting authentication timeout layout.tsx:216:25
[CryptoSession] Found session key in storage, attempting to restore cryptoSession.ts:46:17
[CryptoSession] Session key successfully restored from storage cryptoSession.ts:55:17
Session crypto initialized context.tsx:79:37
[CryptoSession] Found session key in storage, attempting to restore cryptoSession.ts:46:17
[CryptoSession] Session key successfully restored from storage cryptoSession.ts:55:17
[CryptoSession] Found encrypted token, attempting to decrypt, length: 565 cryptoSession.ts:181:17
[CryptoSession] Base64 successfully decoded - IV length: 12 CT length: 411 cryptoSession.ts:208:21
[DashboardLayout] Checking token: Not found layout.tsx:89:29
[DashboardLayout] No authentication detected, performing delayed check... layout.tsx:171:21
[CryptoSession] Token successfully decrypted from session storage: {"access_token":"eyJ... cryptoSession.ts:222:21
[TokenStore] Token restored from encrypted sessionStorage tokenStore.ts:56:21
[DashboardLayout] Checking token: Present (327 chars) layout.tsx:89:29
[DashboardLayout] Valid token found on initial check layout.tsx:96:25
[DashboardLayout] Setting up auth headers layout.tsx:101:29
[DashboardLayout] Adding X-Has-Auth-Token meta tag layout.tsx:107:37
[DashboardLayout] Setting up fetch interceptor layout.tsx:116:37
[DashboardLayout] Setting authentication timeout layout.tsx:216:25
[Auth] Getting current user with Bearer token authentication authService.ts:315:21
[Auth] Token available: true Length: 327 authService.ts:316:21
[Auth] Making /auth/me request with Bearer token: eyJhbGciOiJIUzI... authService.ts:332:21
[Auth] Adding Bearer token to request: /auth/me layout.tsx:133:49
[DashboardLayout] Checking token: Present (327 chars) layout.tsx:89:29
[DashboardLayout] Valid token found on initial check layout.tsx:96:25
[DashboardLayout] Setting up auth headers layout.tsx:101:29
[DashboardLayout] Setting authentication timeout layout.tsx:216:25
[Auth] Health check successful context.tsx:123:37
Using UNSAFE_componentWillReceiveProps in strict mode is not recommended and may indicate bugs in your code. See https://react.dev/link/unsafe-component-lifecycles for details.

* Move data fetching code or side effects to componentDidUpdate.
* If you're updating state whenever props change, refactor your code to use memoization techniques or move it to static getDerivedStateFromProps. Learn more at: https://react.dev/link/derived-state

Please update the following components: Tour intercept-console-error.js:50:32
[Auth] Adding Bearer token to request: /__nextjs_original-stack-frames layout.tsx:133:49
Request for font "Geist" blocked at visibility level 2 (requires 3)
dashboard
[Auth] Auth check response: 200 authService.ts:342:21
[Auth] User data retrieved successfully: <EMAIL> authService.ts:355:21
[Auth] Adding Bearer token to request: /health layout.tsx:133:49
[DashboardLayout] Checking token: Present (327 chars) layout.tsx:89:29
[DashboardLayout] Valid token found on initial check layout.tsx:96:25
[DashboardLayout] Setting up auth headers layout.tsx:101:29
[Auth] Adding Bearer token to request: /health dashboard:1:596
[API] Request: GET /subscription/features modernClient.ts:27:24
[API] Added auth token to request modernClient.ts:27:24
[API] Request: GET /subscription/current modernClient.ts:27:24
[API] Added auth token to request modernClient.ts:27:24
[API] Request: GET /notifications modernClient.ts:27:24
[API] Added auth token to request modernClient.ts:27:24
[API] Request: GET /notifications/unread-count modernClient.ts:27:24
[API] Added auth token to request modernClient.ts:27:24
[API] Request: GET /dashboard/stats modernClient.ts:27:24
[API] Added auth token to request modernClient.ts:27:24
[API] Response: 200 OK - GET /subscription/current modernClient.ts:27:24
[API] Response: 200 OK - GET /notifications/unread-count modernClient.ts:27:24
[API] Response: 200 OK - GET /notifications modernClient.ts:27:24
[API] Response: 200 OK - GET /subscription/features modernClient.ts:27:24
[API] Response: 200 OK - GET /dashboard/stats modernClient.ts:27:24
MouseEvent.mozInputSource is deprecated. Use PointerEvent.pointerType instead. layout.js line 2405 > eval:22:9
[Auth] Adding Bearer token to request: Request layout.tsx:133:49
[Fast Refresh] rebuilding hot-reloader-client.js:197:29
[Auth] Adding Bearer token to request: /_next/static/webpack/46bfb021f015de03.webpack.hot-update.json layout.tsx:133:49
[Fast Refresh] done in 3610ms report-hmr-latency.js:14:13
The resource at “https://localhost:3000/_next/static/media/0484562807a97172-s.p.woff2” preloaded with link preload was not used within a few seconds. Make sure all attributes of the preload tag are set correctly. creator
The resource at “https://localhost:3000/_next/static/media/8888a3826f4a3af4-s.p.woff2” preloaded with link preload was not used within a few seconds. Make sure all attributes of the preload tag are set correctly. creator
The resource at “https://localhost:3000/_next/static/media/b957ea75a84b6ea7-s.p.woff2” preloaded with link preload was not used within a few seconds. Make sure all attributes of the preload tag are set correctly. creator
The resource at “https://localhost:3000/_next/static/media/eafabf029ad39a43-s.p.woff2” preloaded with link preload was not used within a few seconds. Make sure all attributes of the preload tag are set correctly. creator
Frontend QR preview SVG request: 
Object { qrType: "url", dataSize: 160, designOptions: (19) […], hasLogo: false, dimensions: 300 }
qrService.modern.ts:376:21
Cleaned content for SVG preview: 
Object { url: "https://example.com", utm_source: "", utm_medium: "", utm_campaign: "", short_url: false, title: "Url QR Code", description: "Scan to view url content" }
qrService.modern.ts:396:21
[API] Request: POST /qr/preview/svg modernClient.ts:27:24
[API] Added auth token to request modernClient.ts:27:24
[API] Response: 200 OK - POST /qr/preview/svg modernClient.ts:27:24
Successfully received SVG QR preview qrService.modern.ts:429:25
QRPreview: Content key updated 
Object { qrType: "url", dataChanged: true, designChanged: true, logoChanged: false, useSvg: true }
QRPreview.tsx:50:21
QRPreview: Content key updated  
Object { qrType: "url", dataChanged: true, designChanged: true, logoChanged: false, useSvg: true }
dashboard:1:596
QRDataForm: initializing url with provided data + defaults: 
Object { url: "https://example.com", utm_source: "", utm_medium: "", utm_campaign: "", short_url: false, title: "Url QR Code", description: "Scan to view url content" }
QRDataForm.tsx:2072:33
QR data updated: 
Object { url: "https://example.com", utm_source: "", utm_medium: "", utm_campaign: "", short_url: false, title: "Url QR Code", description: "Scan to view url content" }
page.tsx:464:21
QRDataForm: initializing url with provided data + defaults:  
Object { url: "https://example.com", utm_source: "", utm_medium: "", utm_campaign: "", short_url: false, title: "Url QR Code", description: "Scan to view url content" }
dashboard:1:596
QR data updated:  
Object { url: "https://example.com", utm_source: "", utm_medium: "", utm_campaign: "", short_url: false, title: "Url QR Code", description: "Scan to view url content" }
dashboard:1:596
QRPreview: Content key updated 
Object { qrType: "url", dataChanged: true, designChanged: true, logoChanged: false, useSvg: true }
QRPreview.tsx:50:21
QRPreview: Content key updated  
Object { qrType: "url", dataChanged: true, designChanged: true, logoChanged: false, useSvg: true }
dashboard:1:596
QRDataForm (url): Notifying parent of data change: 
Object { url: "https://example.com", utm_source: "", utm_medium: "", utm_campaign: "", short_url: false, title: "Url QR Code", description: "Scan to view url content" }
QRDataForm.tsx:2153:21
QR data updated: 
Object { url: "https://example.com", utm_source: "", utm_medium: "", utm_campaign: "", short_url: false, title: "Url QR Code", description: "Scan to view url content" }
page.tsx:464:21
QRPreview: Content key updated 
Object { qrType: "url", dataChanged: true, designChanged: true, logoChanged: false, useSvg: true }
QRPreview.tsx:50:21
QRPreview: Content key updated  
Object { qrType: "url", dataChanged: true, designChanged: true, logoChanged: false, useSvg: true }
dashboard:1:596
Frontend QR preview SVG request: 
Object { qrType: "url", dataSize: 321, designOptions: (34) […], hasLogo: false, dimensions: 300 }
qrService.modern.ts:376:21
Cleaned content for SVG preview: 
Object { url: "https://example.com", utm_source: "", utm_medium: "", utm_campaign: "", short_url: false, title: "Url QR Code", description: "Scan to view url content" }
qrService.modern.ts:396:21
[API] Request: POST /qr/preview/svg modernClient.ts:27:24
[API] Added auth token to request modernClient.ts:27:24
QRFormState: Initialized with 
Object { hasInitialData: true, mergedDataKeys: (7) […] }
useQRFormState.ts:26:21
QRFormState: Initialized with  
Object { hasInitialData: true, mergedDataKeys: (7) […] }
dashboard:1:596
QRFormState: First render, notifying parent of initial state useQRFormState.ts:150:25
QRDataForm (url): Received data update: 
Object { url: "https://example.com", utm_source: "", utm_medium: "", utm_campaign: "", short_url: false, title: "Url QR Code", description: "Scan to view url content" }
QRDataForm.tsx:2100:21
QRDataForm (url): Updated form data: 
Object { url: "https://example.com", utm_source: "", utm_medium: "", utm_campaign: "", short_url: false, title: "Url QR Code", description: "Scan to view url content" }
QRDataForm.tsx:2133:29
QRFormState: External initialData changed, updating form state useQRFormState.ts:166:25
QRFormState: External initialData changed, updating form state dashboard:1:596
QRDataForm (url): Updated form data: 
Object { url: "https://example.com", utm_source: "", utm_medium: "", utm_campaign: "", short_url: false, title: "Url QR Code", description: "Scan to view url content" }
QRDataForm.tsx:2133:29
QRFormState: External initialData changed, updating form state useQRFormState.ts:166:25
QRDataForm (url): Notifying parent of data change: 
Object { url: "https://example.com", utm_source: "", utm_medium: "", utm_campaign: "", short_url: false, title: "Url QR Code", description: "Scan to view url content" }
QRDataForm.tsx:2153:21
QR data updated: 
Object { url: "https://example.com", utm_source: "", utm_medium: "", utm_campaign: "", short_url: false, title: "Url QR Code", description: "Scan to view url content" }
page.tsx:464:21
QRPreview: Content key updated 
Object { qrType: "url", dataChanged: true, designChanged: true, logoChanged: false, useSvg: true }
QRPreview.tsx:50:21
QRPreview: Content key updated  
Object { qrType: "url", dataChanged: true, designChanged: true, logoChanged: false, useSvg: true }
dashboard:1:596
[API] Response: 200 OK - POST /qr/preview/svg modernClient.ts:27:24
Successfully received SVG QR preview qrService.modern.ts:429:25
Frontend QR preview SVG request: 
Object { qrType: "url", dataSize: 321, designOptions: (34) […], hasLogo: false, dimensions: 300 }
qrService.modern.ts:376:21
Cleaned content for SVG preview: 
Object { url: "https://example.com", utm_source: "", utm_medium: "", utm_campaign: "", short_url: false, title: "Url QR Code", description: "Scan to view url content" }
qrService.modern.ts:396:21
[API] Request: POST /qr/preview/svg modernClient.ts:27:24
[API] Added auth token to request modernClient.ts:27:24
[API] Response: 200 OK - POST /qr/preview/svg modernClient.ts:27:24
Successfully received SVG QR preview qrService.modern.ts:429:25
QRPreview: Generating basic SVG for frontend styling QRPreview.tsx:105:37
QRPreview: Processing logo: 
Object { logoType: "undefined", isFile: false, isDataUrl: false, logoLength: "N/A" }
QRPreview.tsx:108:37
QRPreview: Verifying data for url 
Object { 0: "h", 1: "t", 2: "t", 3: "p", 4: "s", 5: ":", 6: "/", 7: "/", 8: "e", 9: "x", … }
QRPreview.tsx:236:17
QRPreview: Verified data 
Object { 0: "h", 1: "t", 2: "t", 3: "p", 4: "s", 5: ":", 6: "/", 7: "/", 8: "e", 9: "x", … }
QRPreview.tsx:340:17
QRPreview: Sending to backend: 
Object { qrType: "url", dataKeys: (26) […], designOptionsKeys: (25) […], hasLogoFile: false, logoFileSize: undefined, logoFileName: undefined }
QRPreview.tsx:137:37
Frontend QR preview SVG request: 
Object { qrType: "url", dataSize: 321, designOptions: (25) […], hasLogo: false, dimensions: 300 }
qrService.modern.ts:376:21
Cleaned content for SVG preview: 
Object { url: "https://example.com", utm_source: "", utm_medium: "", utm_campaign: "", short_url: false, title: "Url QR Code", description: "Scan to view url content" }
qrService.modern.ts:396:21
[API] Request: POST /qr/preview/svg modernClient.ts:27:24
[API] Added auth token to request modernClient.ts:27:24
[API] Response: 200 OK - POST /qr/preview/svg modernClient.ts:27:24
Successfully received SVG QR preview qrService.modern.ts:429:25
Raw SVG from backend: <svg baseProfile="full" height="340" preserveAspectRatio="xMidYMid meet" version="1.1" viewBox="0 0 340 340" width="340" xmlns="http://www.w3.org/2000/svg" xmlns:ev="http://www.w3.org/2001/xml-events" xmlns:xlink="http://www.w3.org/1999/xlink"><defs /><rect class="qr-bg" fill="#FFFFFF" height="340" id="qr-bg" width="340" x="0" y="0" /><rect class="qr-module" fill="#000000" height="11.6" id="module-8-0" width="11.6" x="112.8" y="20.0" /><rect class="qr-module" fill="#000000" height="11.6" id="mod... QRPreview.tsx:155:37
SVG length: 26602 QRPreview.tsx:156:37
SVG contains <svg>: true QRPreview.tsx:157:37
Raw SVG structure: 
Object { rectCount: 241, pathCount: 0 }
QRPreview.tsx:161:37
SVG analysis: 
Object { hasRect: true, hasPath: false, hasCircle: false, hasBackground: true, hasForeground: true, totalRects: 241, largeRects: 10, finderPatternsDetected: true, originalDesignOptions: {…}, designOptionsKeys: (23) […] }
QRPreview.tsx:191:37
Large rectangles (potential finder patterns): 
Array(5) [ '<rect class="qr-bg" fill="#FFFFFF" height="340" id="qr-bg" width="340" x="0" y="0" />', '<rect class="qr-marker-outer" fill="#000000" height="81.2" width="81.2" x="20.0" y="20.0" />', '<rect class="qr-marker-inner" fill="#fff" height="53.35999999999999" width="53.35999999999999" x="33.92" y="33.92" />', '<rect class="qr-marker-center" fill="#000000" height="23.2" width="23.2" x="49.0" y="49.0" />', '<rect class="qr-marker-outer" fill="#000000" height="81.2" width="81.2" x="228.79999999999998" y="20.0" />' ]
QRPreview.tsx:205:41
Using SVG from backend with all styling applied QRPreview.tsx:208:37
QRPreview: Content key updated 
Object { qrType: "url", dataChanged: true, designChanged: true, logoChanged: true, useSvg: true }
QRPreview.tsx:50:21
QRPreview: Content key updated  
Object { qrType: "url", dataChanged: true, designChanged: true, logoChanged: true, useSvg: true }
dashboard:1:596
QRPreview: Content key updated 
Object { qrType: "url", dataChanged: true, designChanged: true, logoChanged: true, useSvg: true }
QRPreview.tsx:50:21
QRPreview: Content key updated  
Object { qrType: "url", dataChanged: true, designChanged: true, logoChanged: true, useSvg: true }
dashboard:1:596
QRPreview: Content key updated 
Object { qrType: "url", dataChanged: true, designChanged: true, logoChanged: true, useSvg: true }
QRPreview.tsx:50:21
QRPreview: Content key updated  
Object { qrType: "url", dataChanged: true, designChanged: true, logoChanged: true, useSvg: true }
dashboard:1:596
Frontend QR preview SVG request: 
Object { qrType: "url", dataSize: 321, designOptions: (34) […], hasLogo: true, dimensions: 300 }
qrService.modern.ts:376:21
Cleaned content for SVG preview: 
Object { url: "https://example.com", utm_source: "", utm_medium: "", utm_campaign: "", short_url: false, title: "Url QR Code", description: "Scan to view url content" }
qrService.modern.ts:396:21
[API] Request: POST /qr/preview/svg modernClient.ts:27:24
[API] Added auth token to request modernClient.ts:27:24
[API] Response: 200 OK - POST /qr/preview/svg modernClient.ts:27:24
Successfully received SVG QR preview qrService.modern.ts:429:25
QRPreview: Generating basic SVG for frontend styling QRPreview.tsx:105:37
QRPreview: Processing logo: 
Object { logoType: "object", isFile: true, isDataUrl: false, logoLength: "N/A" }
QRPreview.tsx:108:37
QRPreview: Using File object for logo: LOGO - 1080 - BLACK.png 127619 QRPreview.tsx:116:41
QRPreview: Verifying data for url 
Object { 0: "h", 1: "t", 2: "t", 3: "p", 4: "s", 5: ":", 6: "/", 7: "/", 8: "e", 9: "x", … }
QRPreview.tsx:236:17
QRPreview: Verified data 
Object { 0: "h", 1: "t", 2: "t", 3: "p", 4: "s", 5: ":", 6: "/", 7: "/", 8: "e", 9: "x", … }
QRPreview.tsx:340:17
QRPreview: Sending to backend: 
Object { qrType: "url", dataKeys: (26) […], designOptionsKeys: (25) […], hasLogoFile: true, logoFileSize: 127619, logoFileName: "LOGO - 1080 - BLACK.png" }
QRPreview.tsx:137:37
Frontend QR preview SVG request: 
Object { qrType: "url", dataSize: 321, designOptions: (25) […], hasLogo: true, dimensions: 300 }
qrService.modern.ts:376:21
Cleaned content for SVG preview: 
Object { url: "https://example.com", utm_source: "", utm_medium: "", utm_campaign: "", short_url: false, title: "Url QR Code", description: "Scan to view url content" }
qrService.modern.ts:396:21
[API] Request: POST /qr/preview/svg modernClient.ts:27:24
[API] Added auth token to request modernClient.ts:27:24
[API] Response: 200 OK - POST /qr/preview/svg modernClient.ts:27:24
Successfully received SVG QR preview qrService.modern.ts:429:25
Raw SVG from backend: <svg baseProfile="full" height="340" preserveAspectRatio="xMidYMid meet" version="1.1" viewBox="0 0 340 340" width="340" xmlns="http://www.w3.org/2000/svg" xmlns:ev="http://www.w3.org/2001/xml-events" xmlns:xlink="http://www.w3.org/1999/xlink"><defs /><rect class="qr-bg" fill="#FFFFFF" height="340" id="qr-bg" width="340" x="0" y="0" /><rect class="qr-module" fill="#000000" height="11.6" id="module-8-0" width="11.6" x="112.8" y="20.0" /><rect class="qr-module" fill="#000000" height="11.6" id="mod... QRPreview.tsx:155:37
SVG length: 26602 QRPreview.tsx:156:37
SVG contains <svg>: true QRPreview.tsx:157:37
Raw SVG structure: 
Object { rectCount: 241, pathCount: 0 }
QRPreview.tsx:161:37
SVG analysis: 
Object { hasRect: true, hasPath: false, hasCircle: false, hasBackground: true, hasForeground: true, totalRects: 241, largeRects: 10, finderPatternsDetected: true, originalDesignOptions: {…}, designOptionsKeys: (23) […] }
QRPreview.tsx:191:37
Large rectangles (potential finder patterns): 
Array(5) [ '<rect class="qr-bg" fill="#FFFFFF" height="340" id="qr-bg" width="340" x="0" y="0" />', '<rect class="qr-marker-outer" fill="#000000" height="81.2" width="81.2" x="20.0" y="20.0" />', '<rect class="qr-marker-inner" fill="#fff" height="53.35999999999999" width="53.35999999999999" x="33.92" y="33.92" />', '<rect class="qr-marker-center" fill="#000000" height="23.2" width="23.2" x="49.0" y="49.0" />', '<rect class="qr-marker-outer" fill="#000000" height="81.2" width="81.2" x="228.79999999999998" y="20.0" />' ]
QRPreview.tsx:205:41
Using SVG from backend with all styling applied QRPreview.tsx:208:37
[Auth] Adding Bearer token to request: /health layout.tsx:133:49
QRPreview: Content key updated 
Object { qrType: "url", dataChanged: true, designChanged: true, logoChanged: true, useSvg: true }
QRPreview.tsx:50:21
QRPreview: Content key updated  
Object { qrType: "url", dataChanged: true, designChanged: true, logoChanged: true, useSvg: true }
dashboard:1:596
QRPreview: Content key updated 
Object { qrType: "url", dataChanged: true, designChanged: true, logoChanged: true, useSvg: true }
QRPreview.tsx:50:21
QRPreview: Content key updated  
Object { qrType: "url", dataChanged: true, designChanged: true, logoChanged: true, useSvg: true }
dashboard:1:596
QRPreview: Content key updated 
Object { qrType: "url", dataChanged: true, designChanged: true, logoChanged: true, useSvg: true }
QRPreview.tsx:50:21
QRPreview: Content key updated  
Object { qrType: "url", dataChanged: true, designChanged: true, logoChanged: true, useSvg: true }
dashboard:1:596
QRPreview: Content key updated 
Object { qrType: "url", dataChanged: true, designChanged: true, logoChanged: true, useSvg: true }
QRPreview.tsx:50:21
QRPreview: Content key updated  
Object { qrType: "url", dataChanged: true, designChanged: true, logoChanged: true, useSvg: true }
dashboard:1:596
QRPreview: Content key updated 
Object { qrType: "url", dataChanged: true, designChanged: true, logoChanged: true, useSvg: true }
QRPreview.tsx:50:21
QRPreview: Content key updated  
Object { qrType: "url", dataChanged: true, designChanged: true, logoChanged: true, useSvg: true }
dashboard:1:596
QRPreview: Content key updated 
Object { qrType: "url", dataChanged: true, designChanged: true, logoChanged: true, useSvg: true }
QRPreview.tsx:50:21
QRPreview: Content key updated  
Object { qrType: "url", dataChanged: true, designChanged: true, logoChanged: true, useSvg: true }
dashboard:1:596
QRPreview: Content key updated 
Object { qrType: "url", dataChanged: true, designChanged: true, logoChanged: true, useSvg: true }
QRPreview.tsx:50:21
QRPreview: Content key updated  
Object { qrType: "url", dataChanged: true, designChanged: true, logoChanged: true, useSvg: true }
dashboard:1:596
QRPreview: Content key updated 
Object { qrType: "url", dataChanged: true, designChanged: true, logoChanged: true, useSvg: true }
QRPreview.tsx:50:21
QRPreview: Content key updated  
Object { qrType: "url", dataChanged: true, designChanged: true, logoChanged: true, useSvg: true }
dashboard:1:596
QRPreview: Content key updated 
Object { qrType: "url", dataChanged: true, designChanged: true, logoChanged: true, useSvg: true }
QRPreview.tsx:50:21
QRPreview: Content key updated  
Object { qrType: "url", dataChanged: true, designChanged: true, logoChanged: true, useSvg: true }
dashboard:1:596
QRPreview: Content key updated 
Object { qrType: "url", dataChanged: true, designChanged: true, logoChanged: true, useSvg: true }
QRPreview.tsx:50:21
QRPreview: Content key updated  
Object { qrType: "url", dataChanged: true, designChanged: true, logoChanged: true, useSvg: true }
dashboard:1:596
QRPreview: Content key updated 
Object { qrType: "url", dataChanged: true, designChanged: true, logoChanged: true, useSvg: true }
QRPreview.tsx:50:21
QRPreview: Content key updated  
Object { qrType: "url", dataChanged: true, designChanged: true, logoChanged: true, useSvg: true }
dashboard:1:596
Frontend QR preview SVG request: 
Object { qrType: "url", dataSize: 321, designOptions: (34) […], hasLogo: true, dimensions: 300 }
qrService.modern.ts:376:21
Cleaned content for SVG preview: 
Object { url: "https://example.com", utm_source: "", utm_medium: "", utm_campaign: "", short_url: false, title: "Url QR Code", description: "Scan to view url content" }
qrService.modern.ts:396:21
[API] Request: POST /qr/preview/svg modernClient.ts:27:24
[API] Added auth token to request modernClient.ts:27:24
[API] Response: 200 OK - POST /qr/preview/svg modernClient.ts:27:24
Successfully received SVG QR preview qrService.modern.ts:429:25
QRPreview: Content key updated 
Object { qrType: "url", dataChanged: true, designChanged: true, logoChanged: true, useSvg: true }
QRPreview.tsx:50:21
QRPreview: Content key updated  
Object { qrType: "url", dataChanged: true, designChanged: true, logoChanged: true, useSvg: true }
dashboard:1:596
QRPreview: Content key updated 
Object { qrType: "url", dataChanged: true, designChanged: true, logoChanged: true, useSvg: true }
QRPreview.tsx:50:21
QRPreview: Content key updated  
Object { qrType: "url", dataChanged: true, designChanged: true, logoChanged: true, useSvg: true }
dashboard:1:596
QRPreview: Content key updated 
Object { qrType: "url", dataChanged: true, designChanged: true, logoChanged: true, useSvg: true }
QRPreview.tsx:50:21
QRPreview: Content key updated  
Object { qrType: "url", dataChanged: true, designChanged: true, logoChanged: true, useSvg: true }
dashboard:1:596
QRPreview: Content key updated 
Object { qrType: "url", dataChanged: true, designChanged: true, logoChanged: true, useSvg: true }
QRPreview.tsx:50:21
QRPreview: Content key updated  
Object { qrType: "url", dataChanged: true, designChanged: true, logoChanged: true, useSvg: true }
dashboard:1:596
QRPreview: Content key updated 
Object { qrType: "url", dataChanged: true, designChanged: true, logoChanged: true, useSvg: true }
QRPreview.tsx:50:21
QRPreview: Content key updated  
Object { qrType: "url", dataChanged: true, designChanged: true, logoChanged: true, useSvg: true }
dashboard:1:596
Frontend QR preview SVG request: 
Object { qrType: "url", dataSize: 321, designOptions: (34) […], hasLogo: true, dimensions: 300 }
qrService.modern.ts:376:21
Cleaned content for SVG preview: 
Object { url: "https://example.com", utm_source: "", utm_medium: "", utm_campaign: "", short_url: false, title: "Url QR Code", description: "Scan to view url content" }
qrService.modern.ts:396:21
[API] Request: POST /qr/preview/svg modernClient.ts:27:24
[API] Added auth token to request modernClient.ts:27:24
QRPreview: Content key updated 
Object { qrType: "url", dataChanged: true, designChanged: true, logoChanged: true, useSvg: true }
QRPreview.tsx:50:21
QRPreview: Content key updated  
Object { qrType: "url", dataChanged: true, designChanged: true, logoChanged: true, useSvg: true }
dashboard:1:596
QRPreview: Content key updated 
Object { qrType: "url", dataChanged: true, designChanged: true, logoChanged: true, useSvg: true }
QRPreview.tsx:50:21
QRPreview: Content key updated  
Object { qrType: "url", dataChanged: true, designChanged: true, logoChanged: true, useSvg: true }
dashboard:1:596
QRPreview: Content key updated 
Object { qrType: "url", dataChanged: true, designChanged: true, logoChanged: true, useSvg: true }
QRPreview.tsx:50:21
QRPreview: Content key updated  
Object { qrType: "url", dataChanged: true, designChanged: true, logoChanged: true, useSvg: true }
dashboard:1:596
[API] Response: 200 OK - POST /qr/preview/svg modernClient.ts:27:24
Successfully received SVG QR preview qrService.modern.ts:429:25
QRPreview: Content key updated 
Object { qrType: "url", dataChanged: true, designChanged: true, logoChanged: true, useSvg: true }
QRPreview.tsx:50:21
QRPreview: Content key updated  
Object { qrType: "url", dataChanged: true, designChanged: true, logoChanged: true, useSvg: true }
dashboard:1:596
QRPreview: Content key updated 
Object { qrType: "url", dataChanged: true, designChanged: true, logoChanged: true, useSvg: true }
QRPreview.tsx:50:21
QRPreview: Content key updated  
Object { qrType: "url", dataChanged: true, designChanged: true, logoChanged: true, useSvg: true }
dashboard:1:596
QRPreview: Content key updated 
Object { qrType: "url", dataChanged: true, designChanged: true, logoChanged: true, useSvg: true }
QRPreview.tsx:50:21
QRPreview: Content key updated  
Object { qrType: "url", dataChanged: true, designChanged: true, logoChanged: true, useSvg: true }
dashboard:1:596
QRPreview: Content key updated 
Object { qrType: "url", dataChanged: true, designChanged: true, logoChanged: true, useSvg: true }
QRPreview.tsx:50:21
QRPreview: Content key updated  
Object { qrType: "url", dataChanged: true, designChanged: true, logoChanged: true, useSvg: true }
dashboard:1:596
QRPreview: Content key updated 
Object { qrType: "url", dataChanged: true, designChanged: true, logoChanged: true, useSvg: true }
QRPreview.tsx:50:21
QRPreview: Content key updated  
Object { qrType: "url", dataChanged: true, designChanged: true, logoChanged: true, useSvg: true }
dashboard:1:596
QRPreview: Content key updated 
Object { qrType: "url", dataChanged: true, designChanged: true, logoChanged: true, useSvg: true }
QRPreview.tsx:50:21
QRPreview: Content key updated  
Object { qrType: "url", dataChanged: true, designChanged: true, logoChanged: true, useSvg: true }
dashboard:1:596
QRPreview: Content key updated 
Object { qrType: "url", dataChanged: true, designChanged: true, logoChanged: true, useSvg: true }
QRPreview.tsx:50:21
QRPreview: Content key updated  
Object { qrType: "url", dataChanged: true, designChanged: true, logoChanged: true, useSvg: true }
dashboard:1:596
QRPreview: Content key updated 
Object { qrType: "url", dataChanged: true, designChanged: true, logoChanged: true, useSvg: true }
QRPreview.tsx:50:21
QRPreview: Content key updated  
Object { qrType: "url", dataChanged: true, designChanged: true, logoChanged: true, useSvg: true }
dashboard:1:596
QRPreview: Content key updated 
Object { qrType: "url", dataChanged: true, designChanged: true, logoChanged: true, useSvg: true }
QRPreview.tsx:50:21
QRPreview: Content key updated  
Object { qrType: "url", dataChanged: true, designChanged: true, logoChanged: true, useSvg: true }
dashboard:1:596
QRPreview: Content key updated 
Object { qrType: "url", dataChanged: true, designChanged: true, logoChanged: true, useSvg: true }
QRPreview.tsx:50:21
QRPreview: Content key updated  
Object { qrType: "url", dataChanged: true, designChanged: true, logoChanged: true, useSvg: true }
dashboard:1:596
QRPreview: Content key updated 
Object { qrType: "url", dataChanged: true, designChanged: true, logoChanged: true, useSvg: true }
QRPreview.tsx:50:21
QRPreview: Content key updated  
Object { qrType: "url", dataChanged: true, designChanged: true, logoChanged: true, useSvg: true }
dashboard:1:596
QRPreview: Content key updated 
Object { qrType: "url", dataChanged: true, designChanged: true, logoChanged: true, useSvg: true }
QRPreview.tsx:50:21
QRPreview: Content key updated  
Object { qrType: "url", dataChanged: true, designChanged: true, logoChanged: true, useSvg: true }
dashboard:1:596
QRPreview: Content key updated 
Object { qrType: "url", dataChanged: true, designChanged: true, logoChanged: true, useSvg: true }
QRPreview.tsx:50:21
QRPreview: Content key updated  
Object { qrType: "url", dataChanged: true, designChanged: true, logoChanged: true, useSvg: true }
dashboard:1:596
Frontend QR preview SVG request: 
Object { qrType: "url", dataSize: 321, designOptions: (34) […], hasLogo: true, dimensions: 300 }
qrService.modern.ts:376:21
Cleaned content for SVG preview: 
Object { url: "https://example.com", utm_source: "", utm_medium: "", utm_campaign: "", short_url: false, title: "Url QR Code", description: "Scan to view url content" }
qrService.modern.ts:396:21
[API] Request: POST /qr/preview/svg modernClient.ts:27:24
[API] Added auth token to request modernClient.ts:27:24
[API] Response: 200 OK - POST /qr/preview/svg modernClient.ts:27:24
Successfully received SVG QR preview qrService.modern.ts:429:25
QRPreview: Content key updated 
Object { qrType: "url", dataChanged: true, designChanged: true, logoChanged: true, useSvg: true }
QRPreview.tsx:50:21
QRPreview: Content key updated  
Object { qrType: "url", dataChanged: true, designChanged: true, logoChanged: true, useSvg: true }
dashboard:1:596
QRPreview: Content key updated 
Object { qrType: "url", dataChanged: true, designChanged: true, logoChanged: true, useSvg: true }
QRPreview.tsx:50:21
QRPreview: Content key updated  
Object { qrType: "url", dataChanged: true, designChanged: true, logoChanged: true, useSvg: true }
dashboard:1:596
Frontend QR preview SVG request: 
Object { qrType: "url", dataSize: 321, designOptions: (34) […], hasLogo: true, dimensions: 300 }
qrService.modern.ts:376:21
Cleaned content for SVG preview: 
Object { url: "https://example.com", utm_source: "", utm_medium: "", utm_campaign: "", short_url: false, title: "Url QR Code", description: "Scan to view url content" }
qrService.modern.ts:396:21
[API] Request: POST /qr/preview/svg modernClient.ts:27:24
[API] Added auth token to request modernClient.ts:27:24
[API] Response: 200 OK - POST /qr/preview/svg modernClient.ts:27:24
Successfully received SVG QR preview qrService.modern.ts:429:25
QRPreview: Generating basic SVG for frontend styling QRPreview.tsx:105:37
QRPreview: Processing logo: 
Object { logoType: "object", isFile: true, isDataUrl: false, logoLength: "N/A" }
QRPreview.tsx:108:37
QRPreview: Using File object for logo: LOGO - 1080 - BLACK.png 127619 QRPreview.tsx:116:41
QRPreview: Verifying data for url 
Object { 0: "h", 1: "t", 2: "t", 3: "p", 4: "s", 5: ":", 6: "/", 7: "/", 8: "e", 9: "x", … }
QRPreview.tsx:236:17
QRPreview: Verified data 
Object { 0: "h", 1: "t", 2: "t", 3: "p", 4: "s", 5: ":", 6: "/", 7: "/", 8: "e", 9: "x", … }
QRPreview.tsx:340:17
QRPreview: Sending to backend: 
Object { qrType: "url", dataKeys: (26) […], designOptionsKeys: (25) […], hasLogoFile: true, logoFileSize: 127619, logoFileName: "LOGO - 1080 - BLACK.png" }
QRPreview.tsx:137:37
Frontend QR preview SVG request: 
Object { qrType: "url", dataSize: 321, designOptions: (25) […], hasLogo: true, dimensions: 300 }
qrService.modern.ts:376:21
Cleaned content for SVG preview: 
Object { url: "https://example.com", utm_source: "", utm_medium: "", utm_campaign: "", short_url: false, title: "Url QR Code", description: "Scan to view url content" }
qrService.modern.ts:396:21
[API] Request: POST /qr/preview/svg modernClient.ts:27:24
[API] Added auth token to request modernClient.ts:27:24
QRPreview: Content key updated 
Object { qrType: "url", dataChanged: true, designChanged: true, logoChanged: true, useSvg: true }
QRPreview.tsx:50:21
QRPreview: Content key updated  
Object { qrType: "url", dataChanged: true, designChanged: true, logoChanged: true, useSvg: true }
dashboard:1:596
[API] Response: 200 OK - POST /qr/preview/svg modernClient.ts:27:24
Successfully received SVG QR preview qrService.modern.ts:429:25
Raw SVG from backend: <svg baseProfile="full" height="340" preserveAspectRatio="xMidYMid meet" version="1.1" viewBox="0 0 340 340" width="340" xmlns="http://www.w3.org/2000/svg" xmlns:ev="http://www.w3.org/2001/xml-events" xmlns:xlink="http://www.w3.org/1999/xlink"><defs /><rect class="qr-bg" fill="#FFFFFF" height="340" id="qr-bg" width="340" x="0" y="0" /><rect class="qr-module" fill="#000000" height="11.6" id="module-8-0" width="11.6" x="112.8" y="20.0" /><rect class="qr-module" fill="#000000" height="11.6" id="mod... QRPreview.tsx:155:37
SVG length: 26602 QRPreview.tsx:156:37
SVG contains <svg>: true QRPreview.tsx:157:37
Raw SVG structure: 
Object { rectCount: 241, pathCount: 0 }
QRPreview.tsx:161:37
SVG analysis: 
Object { hasRect: true, hasPath: false, hasCircle: false, hasBackground: true, hasForeground: true, totalRects: 241, largeRects: 10, finderPatternsDetected: true, originalDesignOptions: {…}, designOptionsKeys: (23) […] }
QRPreview.tsx:191:37
Large rectangles (potential finder patterns): 
Array(5) [ '<rect class="qr-bg" fill="#FFFFFF" height="340" id="qr-bg" width="340" x="0" y="0" />', '<rect class="qr-marker-outer" fill="#000000" height="81.2" width="81.2" x="20.0" y="20.0" />', '<rect class="qr-marker-inner" fill="#fff" height="53.35999999999999" width="53.35999999999999" x="33.92" y="33.92" />', '<rect class="qr-marker-center" fill="#000000" height="23.2" width="23.2" x="49.0" y="49.0" />', '<rect class="qr-marker-outer" fill="#000000" height="81.2" width="81.2" x="228.79999999999998" y="20.0" />' ]
QRPreview.tsx:205:41
Using SVG from backend with all styling applied QRPreview.tsx:208:37
QRPreview: Content key updated 
Object { qrType: "url", dataChanged: true, designChanged: true, logoChanged: true, useSvg: true }
QRPreview.tsx:50:21
QRPreview: Content key updated  
Object { qrType: "url", dataChanged: true, designChanged: true, logoChanged: true, useSvg: true }
dashboard:1:596
Frontend QR preview SVG request: 
Object { qrType: "url", dataSize: 321, designOptions: (34) […], hasLogo: true, dimensions: 300 }
qrService.modern.ts:376:21
Cleaned content for SVG preview: 
Object { url: "https://example.com", utm_source: "", utm_medium: "", utm_campaign: "", short_url: false, title: "Url QR Code", description: "Scan to view url content" }
qrService.modern.ts:396:21
[API] Request: POST /qr/preview/svg modernClient.ts:27:24
[API] Added auth token to request modernClient.ts:27:24
[API] Response: 200 OK - POST /qr/preview/svg modernClient.ts:27:24
Successfully received SVG QR preview qrService.modern.ts:429:25
QRPreview: Generating basic SVG for frontend styling QRPreview.tsx:105:37
QRPreview: Processing logo: 
Object { logoType: "object", isFile: true, isDataUrl: false, logoLength: "N/A" }
QRPreview.tsx:108:37
QRPreview: Using File object for logo: LOGO - 1080 - BLACK.png 127619 QRPreview.tsx:116:41
QRPreview: Verifying data for url 
Object { 0: "h", 1: "t", 2: "t", 3: "p", 4: "s", 5: ":", 6: "/", 7: "/", 8: "e", 9: "x", … }
QRPreview.tsx:236:17
QRPreview: Verified data 
Object { 0: "h", 1: "t", 2: "t", 3: "p", 4: "s", 5: ":", 6: "/", 7: "/", 8: "e", 9: "x", … }
QRPreview.tsx:340:17
QRPreview: Sending to backend: 
Object { qrType: "url", dataKeys: (26) […], designOptionsKeys: (25) […], hasLogoFile: true, logoFileSize: 127619, logoFileName: "LOGO - 1080 - BLACK.png" }
QRPreview.tsx:137:37
Frontend QR preview SVG request: 
Object { qrType: "url", dataSize: 321, designOptions: (25) […], hasLogo: true, dimensions: 300 }
qrService.modern.ts:376:21
Cleaned content for SVG preview: 
Object { url: "https://example.com", utm_source: "", utm_medium: "", utm_campaign: "", short_url: false, title: "Url QR Code", description: "Scan to view url content" }
qrService.modern.ts:396:21
[API] Request: POST /qr/preview/svg modernClient.ts:27:24
[API] Added auth token to request modernClient.ts:27:24
[API] Response: 200 OK - POST /qr/preview/svg modernClient.ts:27:24
Successfully received SVG QR preview qrService.modern.ts:429:25
Raw SVG from backend: <svg baseProfile="full" height="340" preserveAspectRatio="xMidYMid meet" version="1.1" viewBox="0 0 340 340" width="340" xmlns="http://www.w3.org/2000/svg" xmlns:ev="http://www.w3.org/2001/xml-events" xmlns:xlink="http://www.w3.org/1999/xlink"><defs /><rect class="qr-bg" fill="#FFFFFF" height="340" id="qr-bg" width="340" x="0" y="0" /><rect class="qr-module" fill="#000000" height="11.6" id="module-8-0" width="11.6" x="112.8" y="20.0" /><rect class="qr-module" fill="#000000" height="11.6" id="mod... QRPreview.tsx:155:37
SVG length: 26602 QRPreview.tsx:156:37
SVG contains <svg>: true QRPreview.tsx:157:37
Raw SVG structure: 
Object { rectCount: 241, pathCount: 0 }
QRPreview.tsx:161:37
SVG analysis: 
Object { hasRect: true, hasPath: false, hasCircle: false, hasBackground: true, hasForeground: true, totalRects: 241, largeRects: 10, finderPatternsDetected: true, originalDesignOptions: {…}, designOptionsKeys: (23) […] }
QRPreview.tsx:191:37
Large rectangles (potential finder patterns): 
Array(5) [ '<rect class="qr-bg" fill="#FFFFFF" height="340" id="qr-bg" width="340" x="0" y="0" />', '<rect class="qr-marker-outer" fill="#000000" height="81.2" width="81.2" x="20.0" y="20.0" />', '<rect class="qr-marker-inner" fill="#fff" height="53.35999999999999" width="53.35999999999999" x="33.92" y="33.92" />', '<rect class="qr-marker-center" fill="#000000" height="23.2" width="23.2" x="49.0" y="49.0" />', '<rect class="qr-marker-outer" fill="#000000" height="81.2" width="81.2" x="228.79999999999998" y="20.0" />' ]
QRPreview.tsx:205:41
Using SVG from backend with all styling applied QRPreview.tsx:208:37
QRPreview: Content key updated 
Object { qrType: "url", dataChanged: true, designChanged: true, logoChanged: true, useSvg: true }
QRPreview.tsx:50:21
QRPreview: Content key updated  
Object { qrType: "url", dataChanged: true, designChanged: true, logoChanged: true, useSvg: true }
dashboard:1:596
QRPreview: Content key updated 
Object { qrType: "url", dataChanged: true, designChanged: true, logoChanged: true, useSvg: true }
QRPreview.tsx:50:21
QRPreview: Content key updated  
Object { qrType: "url", dataChanged: true, designChanged: true, logoChanged: true, useSvg: true }
dashboard:1:596
QRPreview: Content key updated 
Object { qrType: "url", dataChanged: true, designChanged: true, logoChanged: true, useSvg: true }
QRPreview.tsx:50:21
QRPreview: Content key updated  
Object { qrType: "url", dataChanged: true, designChanged: true, logoChanged: true, useSvg: true }
dashboard:1:596
QRPreview: Content key updated 
Object { qrType: "url", dataChanged: true, designChanged: true, logoChanged: true, useSvg: true }
QRPreview.tsx:50:21
QRPreview: Content key updated  
Object { qrType: "url", dataChanged: true, designChanged: true, logoChanged: true, useSvg: true }
dashboard:1:596
QRPreview: Content key updated 
Object { qrType: "url", dataChanged: true, designChanged: true, logoChanged: true, useSvg: true }
QRPreview.tsx:50:21
QRPreview: Content key updated  
Object { qrType: "url", dataChanged: true, designChanged: true, logoChanged: true, useSvg: true }
dashboard:1:596
QRPreview: Content key updated 
Object { qrType: "url", dataChanged: true, designChanged: true, logoChanged: true, useSvg: true }
QRPreview.tsx:50:21
QRPreview: Content key updated  
Object { qrType: "url", dataChanged: true, designChanged: true, logoChanged: true, useSvg: true }
dashboard:1:596
QRPreview: Content key updated 
Object { qrType: "url", dataChanged: true, designChanged: true, logoChanged: true, useSvg: true }
QRPreview.tsx:50:21
QRPreview: Content key updated  
Object { qrType: "url", dataChanged: true, designChanged: true, logoChanged: true, useSvg: true }
dashboard:1:596
QRPreview: Content key updated 
Object { qrType: "url", dataChanged: true, designChanged: true, logoChanged: true, useSvg: true }
QRPreview.tsx:50:21
QRPreview: Content key updated  
Object { qrType: "url", dataChanged: true, designChanged: true, logoChanged: true, useSvg: true }
dashboard:1:596
QRPreview: Content key updated 
Object { qrType: "url", dataChanged: true, designChanged: true, logoChanged: true, useSvg: true }
QRPreview.tsx:50:21
QRPreview: Content key updated  
Object { qrType: "url", dataChanged: true, designChanged: true, logoChanged: true, useSvg: true }
dashboard:1:596
QRPreview: Content key updated 
Object { qrType: "url", dataChanged: true, designChanged: true, logoChanged: true, useSvg: true }
QRPreview.tsx:50:21
QRPreview: Content key updated  
Object { qrType: "url", dataChanged: true, designChanged: true, logoChanged: true, useSvg: true }
dashboard:1:596
QRPreview: Content key updated 
Object { qrType: "url", dataChanged: true, designChanged: true, logoChanged: true, useSvg: true }
QRPreview.tsx:50:21
QRPreview: Content key updated  
Object { qrType: "url", dataChanged: true, designChanged: true, logoChanged: true, useSvg: true }
dashboard:1:596
QRPreview: Content key updated 
Object { qrType: "url", dataChanged: true, designChanged: true, logoChanged: true, useSvg: true }
QRPreview.tsx:50:21
QRPreview: Content key updated  
Object { qrType: "url", dataChanged: true, designChanged: true, logoChanged: true, useSvg: true }
dashboard:1:596
QRPreview: Content key updated 
Object { qrType: "url", dataChanged: true, designChanged: true, logoChanged: true, useSvg: true }
QRPreview.tsx:50:21
QRPreview: Content key updated  
Object { qrType: "url", dataChanged: true, designChanged: true, logoChanged: true, useSvg: true }
dashboard:1:596
QRPreview: Content key updated 
Object { qrType: "url", dataChanged: true, designChanged: true, logoChanged: true, useSvg: true }
QRPreview.tsx:50:21
QRPreview: Content key updated  
Object { qrType: "url", dataChanged: true, designChanged: true, logoChanged: true, useSvg: true }
dashboard:1:596
QRPreview: Content key updated 
Object { qrType: "url", dataChanged: true, designChanged: true, logoChanged: true, useSvg: true }
QRPreview.tsx:50:21
QRPreview: Content key updated  
Object { qrType: "url", dataChanged: true, designChanged: true, logoChanged: true, useSvg: true }
dashboard:1:596
QRPreview: Content key updated 
Object { qrType: "url", dataChanged: true, designChanged: true, logoChanged: true, useSvg: true }
QRPreview.tsx:50:21
QRPreview: Content key updated  
Object { qrType: "url", dataChanged: true, designChanged: true, logoChanged: true, useSvg: true }
dashboard:1:596
QRPreview: Content key updated 
Object { qrType: "url", dataChanged: true, designChanged: true, logoChanged: true, useSvg: true }
QRPreview.tsx:50:21
QRPreview: Content key updated  
Object { qrType: "url", dataChanged: true, designChanged: true, logoChanged: true, useSvg: true }
dashboard:1:596
QRPreview: Content key updated 
Object { qrType: "url", dataChanged: true, designChanged: true, logoChanged: true, useSvg: true }
QRPreview.tsx:50:21
QRPreview: Content key updated  
Object { qrType: "url", dataChanged: true, designChanged: true, logoChanged: true, useSvg: true }
dashboard:1:596
QRPreview: Content key updated 
Object { qrType: "url", dataChanged: true, designChanged: true, logoChanged: true, useSvg: true }
QRPreview.tsx:50:21
QRPreview: Content key updated  
Object { qrType: "url", dataChanged: true, designChanged: true, logoChanged: true, useSvg: true }
dashboard:1:596
QRPreview: Content key updated 
Object { qrType: "url", dataChanged: true, designChanged: true, logoChanged: true, useSvg: true }
QRPreview.tsx:50:21
QRPreview: Content key updated  
Object { qrType: "url", dataChanged: true, designChanged: true, logoChanged: true, useSvg: true }
dashboard:1:596
QRPreview: Content key updated 
Object { qrType: "url", dataChanged: true, designChanged: true, logoChanged: true, useSvg: true }
QRPreview.tsx:50:21
QRPreview: Content key updated  
Object { qrType: "url", dataChanged: true, designChanged: true, logoChanged: true, useSvg: true }
dashboard:1:596
Frontend QR preview SVG request: 
Object { qrType: "url", dataSize: 321, designOptions: (34) […], hasLogo: true, dimensions: 300 }
qrService.modern.ts:376:21
Cleaned content for SVG preview: 
Object { url: "https://example.com", utm_source: "", utm_medium: "", utm_campaign: "", short_url: false, title: "Url QR Code", description: "Scan to view url content" }
qrService.modern.ts:396:21
[API] Request: POST /qr/preview/svg modernClient.ts:27:24
[API] Added auth token to request modernClient.ts:27:24
[API] Response: 200 OK - POST /qr/preview/svg modernClient.ts:27:24
Successfully received SVG QR preview qrService.modern.ts:429:25
QRPreview: Content key updated 
Object { qrType: "url", dataChanged: true, designChanged: true, logoChanged: true, useSvg: true }
QRPreview.tsx:50:21
QRPreview: Content key updated  
Object { qrType: "url", dataChanged: true, designChanged: true, logoChanged: true, useSvg: true }
dashboard:1:596
QRPreview: Content key updated 
Object { qrType: "url", dataChanged: true, designChanged: true, logoChanged: true, useSvg: true }
QRPreview.tsx:50:21
QRPreview: Content key updated  
Object { qrType: "url", dataChanged: true, designChanged: true, logoChanged: true, useSvg: true }
dashboard:1:596
QRPreview: Content key updated 
Object { qrType: "url", dataChanged: true, designChanged: true, logoChanged: true, useSvg: true }
QRPreview.tsx:50:21
QRPreview: Content key updated  
Object { qrType: "url", dataChanged: true, designChanged: true, logoChanged: true, useSvg: true }
dashboard:1:596
QRPreview: Content key updated 
Object { qrType: "url", dataChanged: true, designChanged: true, logoChanged: true, useSvg: true }
QRPreview.tsx:50:21
QRPreview: Content key updated  
Object { qrType: "url", dataChanged: true, designChanged: true, logoChanged: true, useSvg: true }
dashboard:1:596
QRPreview: Content key updated 
Object { qrType: "url", dataChanged: true, designChanged: true, logoChanged: true, useSvg: true }
QRPreview.tsx:50:21
QRPreview: Content key updated  
Object { qrType: "url", dataChanged: true, designChanged: true, logoChanged: true, useSvg: true }
dashboard:1:596
QRPreview: Content key updated 
Object { qrType: "url", dataChanged: true, designChanged: true, logoChanged: true, useSvg: true }
QRPreview.tsx:50:21
QRPreview: Content key updated  
Object { qrType: "url", dataChanged: true, designChanged: true, logoChanged: true, useSvg: true }
dashboard:1:596
QRPreview: Content key updated 
Object { qrType: "url", dataChanged: true, designChanged: true, logoChanged: true, useSvg: true }
QRPreview.tsx:50:21
QRPreview: Content key updated  
Object { qrType: "url", dataChanged: true, designChanged: true, logoChanged: true, useSvg: true }
dashboard:1:596
QRPreview: Content key updated 
Object { qrType: "url", dataChanged: true, designChanged: true, logoChanged: true, useSvg: true }
QRPreview.tsx:50:21
QRPreview: Content key updated  
Object { qrType: "url", dataChanged: true, designChanged: true, logoChanged: true, useSvg: true }
dashboard:1:596
QRPreview: Content key updated 
Object { qrType: "url", dataChanged: true, designChanged: true, logoChanged: true, useSvg: true }
QRPreview.tsx:50:21
QRPreview: Content key updated  
Object { qrType: "url", dataChanged: true, designChanged: true, logoChanged: true, useSvg: true }
dashboard:1:596
QRPreview: Content key updated 
Object { qrType: "url", dataChanged: true, designChanged: true, logoChanged: true, useSvg: true }
QRPreview.tsx:50:21
QRPreview: Content key updated  
Object { qrType: "url", dataChanged: true, designChanged: true, logoChanged: true, useSvg: true }
dashboard:1:596
QRPreview: Content key updated 
Object { qrType: "url", dataChanged: true, designChanged: true, logoChanged: true, useSvg: true }
QRPreview.tsx:50:21
QRPreview: Content key updated  
Object { qrType: "url", dataChanged: true, designChanged: true, logoChanged: true, useSvg: true }
dashboard:1:596
QRPreview: Content key updated 
Object { qrType: "url", dataChanged: true, designChanged: true, logoChanged: true, useSvg: true }
QRPreview.tsx:50:21
QRPreview: Content key updated  
Object { qrType: "url", dataChanged: true, designChanged: true, logoChanged: true, useSvg: true }
dashboard:1:596
QRPreview: Content key updated 
Object { qrType: "url", dataChanged: true, designChanged: true, logoChanged: true, useSvg: true }
QRPreview.tsx:50:21
QRPreview: Content key updated  
Object { qrType: "url", dataChanged: true, designChanged: true, logoChanged: true, useSvg: true }
dashboard:1:596
QRPreview: Content key updated 
Object { qrType: "url", dataChanged: true, designChanged: true, logoChanged: true, useSvg: true }
QRPreview.tsx:50:21
QRPreview: Content key updated  
Object { qrType: "url", dataChanged: true, designChanged: true, logoChanged: true, useSvg: true }
dashboard:1:596
QRPreview: Content key updated 
Object { qrType: "url", dataChanged: true, designChanged: true, logoChanged: true, useSvg: true }
QRPreview.tsx:50:21
QRPreview: Content key updated  
Object { qrType: "url", dataChanged: true, designChanged: true, logoChanged: true, useSvg: true }
dashboard:1:596
Frontend QR preview SVG request: 
Object { qrType: "url", dataSize: 321, designOptions: (34) […], hasLogo: true, dimensions: 300 }
qrService.modern.ts:376:21
Cleaned content for SVG preview: 
Object { url: "https://example.com", utm_source: "", utm_medium: "", utm_campaign: "", short_url: false, title: "Url QR Code", description: "Scan to view url content" }
qrService.modern.ts:396:21
[API] Request: POST /qr/preview/svg modernClient.ts:27:24
[API] Added auth token to request modernClient.ts:27:24
[API] Response: 200 OK - POST /qr/preview/svg modernClient.ts:27:24
Successfully received SVG QR preview qrService.modern.ts:429:25
QRPreview: Generating basic SVG for frontend styling QRPreview.tsx:105:37
QRPreview: Processing logo: 
Object { logoType: "object", isFile: true, isDataUrl: false, logoLength: "N/A" }
QRPreview.tsx:108:37
QRPreview: Using File object for logo: LOGO - 1080 - BLACK.png 127619 QRPreview.tsx:116:41
QRPreview: Verifying data for url 
Object { 0: "h", 1: "t", 2: "t", 3: "p", 4: "s", 5: ":", 6: "/", 7: "/", 8: "e", 9: "x", … }
QRPreview.tsx:236:17
QRPreview: Verified data 
Object { 0: "h", 1: "t", 2: "t", 3: "p", 4: "s", 5: ":", 6: "/", 7: "/", 8: "e", 9: "x", … }
QRPreview.tsx:340:17
QRPreview: Sending to backend: 
Object { qrType: "url", dataKeys: (26) […], designOptionsKeys: (25) […], hasLogoFile: true, logoFileSize: 127619, logoFileName: "LOGO - 1080 - BLACK.png" }
QRPreview.tsx:137:37
Frontend QR preview SVG request: 
Object { qrType: "url", dataSize: 321, designOptions: (25) […], hasLogo: true, dimensions: 300 }
qrService.modern.ts:376:21
Cleaned content for SVG preview: 
Object { url: "https://example.com", utm_source: "", utm_medium: "", utm_campaign: "", short_url: false, title: "Url QR Code", description: "Scan to view url content" }
qrService.modern.ts:396:21
[API] Request: POST /qr/preview/svg modernClient.ts:27:24
[API] Added auth token to request modernClient.ts:27:24
[API] Response: 200 OK - POST /qr/preview/svg modernClient.ts:27:24
Successfully received SVG QR preview qrService.modern.ts:429:25
Raw SVG from backend: <svg baseProfile="full" height="340" preserveAspectRatio="xMidYMid meet" version="1.1" viewBox="0 0 340 340" width="340" xmlns="http://www.w3.org/2000/svg" xmlns:ev="http://www.w3.org/2001/xml-events" xmlns:xlink="http://www.w3.org/1999/xlink"><defs /><rect class="qr-bg" fill="#FFFFFF" height="340" id="qr-bg" width="340" x="0" y="0" /><rect class="qr-module" fill="#000000" height="11.6" id="module-8-0" width="11.6" x="112.8" y="20.0" /><rect class="qr-module" fill="#000000" height="11.6" id="mod... QRPreview.tsx:155:37
SVG length: 26602 QRPreview.tsx:156:37
SVG contains <svg>: true QRPreview.tsx:157:37
Raw SVG structure: 
Object { rectCount: 241, pathCount: 0 }
QRPreview.tsx:161:37
SVG analysis: 
Object { hasRect: true, hasPath: false, hasCircle: false, hasBackground: true, hasForeground: true, totalRects: 241, largeRects: 10, finderPatternsDetected: true, originalDesignOptions: {…}, designOptionsKeys: (23) […] }
QRPreview.tsx:191:37
Large rectangles (potential finder patterns): 
Array(5) [ '<rect class="qr-bg" fill="#FFFFFF" height="340" id="qr-bg" width="340" x="0" y="0" />', '<rect class="qr-marker-outer" fill="#000000" height="81.2" width="81.2" x="20.0" y="20.0" />', '<rect class="qr-marker-inner" fill="#fff" height="53.35999999999999" width="53.35999999999999" x="33.92" y="33.92" />', '<rect class="qr-marker-center" fill="#000000" height="23.2" width="23.2" x="49.0" y="49.0" />', '<rect class="qr-marker-outer" fill="#000000" height="81.2" width="81.2" x="228.79999999999998" y="20.0" />' ]
QRPreview.tsx:205:41
Using SVG from backend with all styling applied QRPreview.tsx:208:37
[Auth] Adding Bearer token to request: /health layout.tsx:133:49
[Auth] Health check successful context.tsx:123:37
[Auth] Adding Bearer token to request: /health 3 layout.tsx:133:49
[Auth] Health check successful context.tsx:123:37
[Auth] Adding Bearer token to request: /health layout.tsx:133:49
[Fast Refresh] rebuilding hot-reloader-client.js:197:29
[Auth] Adding Bearer token to request: /_next/static/webpack/d598afa8317568bc.webpack.hot-update.json layout.tsx:133:49
Source map error: Error: request failed with status 404
Stack in the worker:networkRequest@resource://devtools/client/shared/source-map-loader/utils/network-request.js:43:9

Resource URL: null
Source Map URL: installHook.js.map
[Fast Refresh] done in 1519ms report-hmr-latency.js:14:13
