Rendering /app/(user)/layout.tsx (user layout) layout.tsx:16:9
Rendering /app/layout.tsx (global layout) layout.tsx:25:9
[DashboardLayout] Checking token: Not found layout.tsx:89:29
[DashboardLayout] Waiting for hydration... layout.tsx:150:25
[DashboardLayout] Setting authentication timeout layout.tsx:216:25
[CryptoSession] Found session key in storage, attempting to restore cryptoSession.ts:46:17
[CryptoSession] Session key successfully restored from storage cryptoSession.ts:55:17
Session crypto initialized context.tsx:79:37
[CryptoSession] Found session key in storage, attempting to restore cryptoSession.ts:46:17
[CryptoSession] Session key successfully restored from storage cryptoSession.ts:55:17
[CryptoSession] Found encrypted token, attempting to decrypt, length: 565 cryptoSession.ts:181:17
[CryptoSession] Base64 successfully decoded - IV length: 12 CT length: 411 cryptoSession.ts:208:21
[CryptoSession] Token successfully decrypted from session storage: {"access_token":"eyJ... cryptoSession.ts:222:21
[TokenStore] Token restored from encrypted sessionStorage tokenStore.ts:56:21
[DashboardLayout] Checking token: Present (327 chars) layout.tsx:89:29
[DashboardLayout] Valid token found on initial check layout.tsx:96:25
[DashboardLayout] Setting up auth headers layout.tsx:101:29
[DashboardLayout] Adding X-Has-Auth-Token meta tag layout.tsx:107:37
[DashboardLayout] Setting up fetch interceptor layout.tsx:116:37
[DashboardLayout] Checking token: Present (327 chars) layout.tsx:89:29
[DashboardLayout] Valid token found on initial check layout.tsx:96:25
[DashboardLayout] Setting up auth headers layout.tsx:101:29
[DashboardLayout] Setting authentication timeout layout.tsx:216:25
[Auth] Getting current user with Bearer token authentication authService.ts:315:21
[Auth] Token available: true Length: 327 authService.ts:316:21
[Auth] Making /auth/me request with Bearer token: eyJhbGciOiJIUzI... authService.ts:332:21
[Auth] Adding Bearer token to request: /auth/me layout.tsx:133:49
[Auth] Health check successful context.tsx:123:37
[Auth] Auth check response: 200 authService.ts:342:21
Using UNSAFE_componentWillReceiveProps in strict mode is not recommended and may indicate bugs in your code. See https://react.dev/link/unsafe-component-lifecycles for details.

* Move data fetching code or side effects to componentDidUpdate.
* If you're updating state whenever props change, refactor your code to use memoization techniques or move it to static getDerivedStateFromProps. Learn more at: https://react.dev/link/derived-state

Please update the following components: Tour intercept-console-error.js:50:32
[Auth] Adding Bearer token to request: /__nextjs_original-stack-frames layout.tsx:133:49
[Auth] User data retrieved successfully: <EMAIL> authService.ts:355:21
Request for font "Geist" blocked at visibility level 2 (requires 3)
layout.js line 1976 > eval:165:35
[Auth] Adding Bearer token to request: /health layout.tsx:133:49
[DashboardLayout] Checking token: Present (327 chars) layout.tsx:89:29
[DashboardLayout] Valid token found on initial check layout.tsx:96:25
[DashboardLayout] Setting up auth headers layout.tsx:101:29
[Auth] Adding Bearer token to request: /health dashboard:1:596
[API] Request: GET /subscription/features modernClient.ts:27:24
[API] Added auth token to request modernClient.ts:27:24
[API] Request: GET /subscription/current modernClient.ts:27:24
[API] Added auth token to request modernClient.ts:27:24
[API] Request: GET /notifications modernClient.ts:27:24
[API] Added auth token to request modernClient.ts:27:24
[API] Request: GET /notifications/unread-count modernClient.ts:27:24
[API] Added auth token to request modernClient.ts:27:24
[API] Request: GET /dashboard/stats modernClient.ts:27:24
[API] Added auth token to request modernClient.ts:27:24
[API] Response: 200 OK - GET /notifications/unread-count modernClient.ts:27:24
[API] Response: 200 OK - GET /notifications modernClient.ts:27:24
[API] Response: 200 OK - GET /subscription/current modernClient.ts:27:24
[API] Response: 200 OK - GET /subscription/features modernClient.ts:27:24
[API] Response: 200 OK - GET /dashboard/stats modernClient.ts:27:24
MouseEvent.mozInputSource is deprecated. Use PointerEvent.pointerType instead. layout.js line 2405 > eval:22:9
[Auth] Adding Bearer token to request: Request layout.tsx:133:49
[Fast Refresh] rebuilding hot-reloader-client.js:197:29
[Auth] Adding Bearer token to request: /_next/static/webpack/eeac9acc9d44fcf6.webpack.hot-update.json layout.tsx:133:49
[Fast Refresh] done in 3066ms report-hmr-latency.js:14:13
unreachable code after return statement page.js:324:5
Frontend QR preview SVG request: 
Object { qrType: "url", dataSize: 160, designOptions: (17) […], hasLogo: false, dimensions: 300 }
qrService.modern.ts:376:21
Cleaned content for SVG preview: 
Object { url: "https://example.com", utm_source: "", utm_medium: "", utm_campaign: "", short_url: false, title: "Url QR Code", description: "Scan to view url content" }
qrService.modern.ts:396:21
[API] Request: POST /qr/preview/svg modernClient.ts:27:24
[API] Added auth token to request modernClient.ts:27:24
[API] Response: 200 OK - POST /qr/preview/svg modernClient.ts:27:24
Successfully received SVG QR preview qrService.modern.ts:429:25
The resource at “https://localhost:3000/_next/static/media/8888a3826f4a3af4-s.p.woff2” preloaded with link preload was not used within a few seconds. Make sure all attributes of the preload tag are set correctly. creator
The resource at “https://localhost:3000/_next/static/media/b957ea75a84b6ea7-s.p.woff2” preloaded with link preload was not used within a few seconds. Make sure all attributes of the preload tag are set correctly. creator
The resource at “https://localhost:3000/_next/static/media/eafabf029ad39a43-s.p.woff2” preloaded with link preload was not used within a few seconds. Make sure all attributes of the preload tag are set correctly. creator
Frontend QR preview SVG request: 
Object { qrType: "scooter_unlock", dataSize: 135, designOptions: (17) […], hasLogo: false, dimensions: 300 }
qrService.modern.ts:376:21
Cleaned content for SVG preview: 
Object { scooter_id: "SCOOT123", unlock_code: "UNLOCK42", title: "Scooter_unlock QR Code", description: "Scan to view scooter_unlock content" }
qrService.modern.ts:396:21
[API] Request: POST /qr/preview/svg modernClient.ts:27:24
[API] Added auth token to request modernClient.ts:27:24
[API] Response: 200 OK - POST /qr/preview/svg modernClient.ts:27:24
Successfully received SVG QR preview qrService.modern.ts:429:25
QRPreview: Content key updated 
Object { qrType: "scooter_unlock", dataChanged: true, designChanged: true, logoChanged: false, useSvg: true }
QRPreview.tsx:51:21
QRPreview: Content key updated  
Object { qrType: "scooter_unlock", dataChanged: true, designChanged: true, logoChanged: false, useSvg: true }
dashboard:1:596
QRDataForm: initializing scooter_unlock with provided data + defaults: 
Object { scooter_id: "SCOOT123", unlock_code: "UNLOCK42", title: "Scooter_unlock QR Code", description: "Scan to view scooter_unlock content" }
QRDataForm.tsx:2072:33
QR data updated: 
Object { scooter_id: "SCOOT123", unlock_code: "UNLOCK42", title: "Scooter_unlock QR Code", description: "Scan to view scooter_unlock content" }
page.tsx:462:21
QRDataForm: initializing scooter_unlock with provided data + defaults:  
Object { scooter_id: "SCOOT123", unlock_code: "UNLOCK42", title: "Scooter_unlock QR Code", description: "Scan to view scooter_unlock content" }
dashboard:1:596
QR data updated:  
Object { scooter_id: "SCOOT123", unlock_code: "UNLOCK42", title: "Scooter_unlock QR Code", description: "Scan to view scooter_unlock content" }
dashboard:1:596
QRPreview: Content key updated 
Object { qrType: "scooter_unlock", dataChanged: true, designChanged: true, logoChanged: false, useSvg: true }
QRPreview.tsx:51:21
QRPreview: Content key updated  
Object { qrType: "scooter_unlock", dataChanged: true, designChanged: true, logoChanged: false, useSvg: true }
dashboard:1:596
QRDataForm (scooter_unlock): Notifying parent of data change: 
Object { scooter_id: "SCOOT123", unlock_code: "UNLOCK42", title: "Scooter_unlock QR Code", description: "Scan to view scooter_unlock content" }
QRDataForm.tsx:2153:21
QR data updated: 
Object { scooter_id: "SCOOT123", unlock_code: "UNLOCK42", title: "Scooter_unlock QR Code", description: "Scan to view scooter_unlock content" }
page.tsx:462:21
QRPreview: Content key updated 
Object { qrType: "scooter_unlock", dataChanged: true, designChanged: true, logoChanged: false, useSvg: true }
QRPreview.tsx:51:21
QRPreview: Content key updated  
Object { qrType: "scooter_unlock", dataChanged: true, designChanged: true, logoChanged: false, useSvg: true }
dashboard:1:596
QRPreview: Generating basic SVG for frontend styling QRPreview.tsx:108:37
QRPreview: Verifying data for scooter_unlock 
Object { scooter_id: "SCOOT123", unlock_code: "UNLOCK42", title: "Scooter_unlock QR Code", description: "Scan to view scooter_unlock content" }
QRPreview.tsx:260:17
QRPreview: Verified data 
Object { scooter_id: "SCOOT123", unlock_code: "UNLOCK42", title: "Scooter_unlock QR Code", description: "Scan to view scooter_unlock content" }
QRPreview.tsx:364:17
Frontend QR preview SVG request: 
Object { qrType: "scooter_unlock", dataSize: 135, designOptions: (6) […], hasLogo: false, dimensions: 300 }
qrService.modern.ts:376:21
Cleaned content for SVG preview: 
Object { scooter_id: "SCOOT123", unlock_code: "UNLOCK42", title: "Scooter_unlock QR Code", description: "Scan to view scooter_unlock content" }
qrService.modern.ts:396:21
[API] Request: POST /qr/preview/svg modernClient.ts:27:24
[API] Added auth token to request modernClient.ts:27:24
Frontend QR preview SVG request: 
Object { qrType: "scooter_unlock", dataSize: 135, designOptions: (32) […], hasLogo: false, dimensions: 300 }
qrService.modern.ts:376:21
Cleaned content for SVG preview: 
Object { scooter_id: "SCOOT123", unlock_code: "UNLOCK42", title: "Scooter_unlock QR Code", description: "Scan to view scooter_unlock content" }
qrService.modern.ts:396:21
[API] Request: POST /qr/preview/svg modernClient.ts:27:24
[API] Added auth token to request modernClient.ts:27:24
QRFormState: Initialized with 
Object { hasInitialData: true, mergedDataKeys: (4) […] }
useQRFormState.ts:26:21
QRFormState: Initialized with  
Object { hasInitialData: true, mergedDataKeys: (4) […] }
dashboard:1:596
QRFormState: First render, notifying parent of initial state useQRFormState.ts:150:25
QRDataForm (scooter_unlock): Received data update: 
Object { scooter_id: "SCOOT123", unlock_code: "UNLOCK42", title: "Scooter_unlock QR Code", description: "Scan to view scooter_unlock content" }
QRDataForm.tsx:2100:21
QRDataForm (scooter_unlock): Updated form data: 
Object { scooter_id: "SCOOT123", unlock_code: "UNLOCK42", title: "Scooter_unlock QR Code", description: "Scan to view scooter_unlock content" }
QRDataForm.tsx:2133:29
QRFormState: External initialData changed, updating form state useQRFormState.ts:166:25
QRFormState: External initialData changed, updating form state dashboard:1:596
QRDataForm (scooter_unlock): Updated form data: 
Object { scooter_id: "SCOOT123", unlock_code: "UNLOCK42", title: "Scooter_unlock QR Code", description: "Scan to view scooter_unlock content" }
QRDataForm.tsx:2133:29
QRFormState: External initialData changed, updating form state useQRFormState.ts:166:25
QRDataForm (scooter_unlock): Notifying parent of data change: 
Object { scooter_id: "SCOOT123", unlock_code: "UNLOCK42", title: "Scooter_unlock QR Code", description: "Scan to view scooter_unlock content" }
QRDataForm.tsx:2153:21
QR data updated: 
Object { scooter_id: "SCOOT123", unlock_code: "UNLOCK42", title: "Scooter_unlock QR Code", description: "Scan to view scooter_unlock content" }
page.tsx:462:21
QRPreview: Content key updated 
Object { qrType: "scooter_unlock", dataChanged: true, designChanged: true, logoChanged: false, useSvg: true }
QRPreview.tsx:51:21
QRPreview: Content key updated  
Object { qrType: "scooter_unlock", dataChanged: true, designChanged: true, logoChanged: false, useSvg: true }
dashboard:1:596
[API] Response: 200 OK - POST /qr/preview/svg modernClient.ts:27:24
Successfully received SVG QR preview qrService.modern.ts:429:25
Raw SVG from backend: <svg baseProfile="full" height="300" version="1.1" width="300" xmlns="http://www.w3.org/2000/svg" xmlns:ev="http://www.w3.org/2001/xml-events" xmlns:xlink="http://www.w3.org/1999/xlink"><defs /><rect class="qr-bg" fill="#FFFFFF" height="300" id="qr-bg" width="300" x="0" y="0" /><rect class="qr-module" fill="#000000" height="6.122448979591836" id="module-8-0" width="6.122448979591836" x="48.97959183673469" y="0.0" /><rect class="qr-module" fill="#000000" height="6.122448979591836" id="module-9-0"... QRPreview.tsx:127:37
SVG length: 176126 QRPreview.tsx:128:37
SVG contains <svg>: true QRPreview.tsx:129:37
Raw SVG structure: 
Object { rectCount: 1127, pathCount: 0 }
QRPreview.tsx:133:37
SVG analysis: 
Object { hasRect: true, hasPath: false, hasCircle: false, hasBackground: true, hasForeground: true, totalRects: 1127, largeRects: 7, finderPatternsDetected: true, originalDesignOptions: {…}, designOptionsKeys: (21) […] }
QRPreview.tsx:163:37
Large rectangles (potential finder patterns): 
Array(5) [ '<rect class="qr-bg" fill="#FFFFFF" height="300" id="qr-bg" width="300" x="0" y="0" />', '<rect class="qr-marker-outer" fill="#000000" height="42.857142857142854" width="42.857142857142854" x="0.0" y="0.0" />', '<rect class="qr-marker-inner" fill="#fff" height="28.163265306122444" width="28.163265306122444" x="7.346938775510203" y="7.346938775510203" />', '<rect class="qr-marker-outer" fill="#000000" height="42.857142857142854" width="42.857142857142854" x="257.1428571428571" y="0.0" />', '<rect class="qr-marker-inner" fill="#fff" height="28.163265306122444" width="28.163265306122444" x="264.4897959183673" y="7.346938775510203" />' ]
QRPreview.tsx:177:41
Original design options: 
Object { size: 256, margin: 10, pattern: "square", marker_border_style: "square", marker_border_color: "#000000", marker_center_style: "square", marker_center_color: "#000000", logo_size: 48, logo_position: "center", logo_opacity: 1, … }
QRPreview.tsx:183:37
Filtered design options for basic preview: 
Object { size: 256, margin: 10, error_correction: "M" }
QRPreview.tsx:184:37
Filtered options count: 3 QRPreview.tsx:185:37
SVG Styler: Parsing SVG, element count: 1122 svgStyler.ts:23:13
SVG Styler: Design options: 
Object { size: 256, margin: 10, error_correction: "M" }
svgStyler.ts:24:13
SVG Styler: Using default background color svgStyler.ts:48:17
SVG Styler: Using default foreground color svgStyler.ts:80:17
SVG Styler: Using default square pattern svgStyler.ts:97:17
SVG Styler: Final SVG length: 174998 svgStyler.ts:126:13
Styled SVG: <svg baseProfile="full" height="300" version="1.1" width="300" xmlns="http://www.w3.org/2000/svg" xmlns:ev="http://www.w3.org/2001/xml-events" xmlns:xlink="http://www.w3.org/1999/xlink"><defs/><rect c... QRPreview.tsx:193:41
[API] Response: 200 OK - POST /qr/preview/svg modernClient.ts:27:24
Successfully received SVG QR preview qrService.modern.ts:429:25
QRPreview: Generating basic SVG for frontend styling QRPreview.tsx:108:37
QRPreview: Verifying data for scooter_unlock 
Object { scooter_id: "SCOOT123", unlock_code: "UNLOCK42", title: "Scooter_unlock QR Code", description: "Scan to view scooter_unlock content" }
QRPreview.tsx:260:17
QRPreview: Verified data 
Object { scooter_id: "SCOOT123", unlock_code: "UNLOCK42", title: "Scooter_unlock QR Code", description: "Scan to view scooter_unlock content" }
QRPreview.tsx:364:17
Frontend QR preview SVG request: 
Object { qrType: "scooter_unlock", dataSize: 135, designOptions: (6) […], hasLogo: false, dimensions: 300 }
qrService.modern.ts:376:21
Cleaned content for SVG preview: 
Object { scooter_id: "SCOOT123", unlock_code: "UNLOCK42", title: "Scooter_unlock QR Code", description: "Scan to view scooter_unlock content" }
qrService.modern.ts:396:21
[API] Request: POST /qr/preview/svg modernClient.ts:27:24
[API] Added auth token to request modernClient.ts:27:24
Frontend QR preview SVG request: 
Object { qrType: "scooter_unlock", dataSize: 135, designOptions: (32) […], hasLogo: false, dimensions: 300 }
qrService.modern.ts:376:21
Cleaned content for SVG preview: 
Object { scooter_id: "SCOOT123", unlock_code: "UNLOCK42", title: "Scooter_unlock QR Code", description: "Scan to view scooter_unlock content" }
qrService.modern.ts:396:21
[API] Request: POST /qr/preview/svg modernClient.ts:27:24
[API] Added auth token to request modernClient.ts:27:24
[API] Response: 200 OK - POST /qr/preview/svg modernClient.ts:27:24
Successfully received SVG QR preview qrService.modern.ts:429:25
Raw SVG from backend: <svg baseProfile="full" height="300" version="1.1" width="300" xmlns="http://www.w3.org/2000/svg" xmlns:ev="http://www.w3.org/2001/xml-events" xmlns:xlink="http://www.w3.org/1999/xlink"><defs /><rect class="qr-bg" fill="#FFFFFF" height="300" id="qr-bg" width="300" x="0" y="0" /><rect class="qr-module" fill="#000000" height="6.122448979591836" id="module-8-0" width="6.122448979591836" x="48.97959183673469" y="0.0" /><rect class="qr-module" fill="#000000" height="6.122448979591836" id="module-9-0"... QRPreview.tsx:127:37
SVG length: 176126 QRPreview.tsx:128:37
SVG contains <svg>: true QRPreview.tsx:129:37
Raw SVG structure: 
Object { rectCount: 1127, pathCount: 0 }
QRPreview.tsx:133:37
SVG analysis: 
Object { hasRect: true, hasPath: false, hasCircle: false, hasBackground: true, hasForeground: true, totalRects: 1127, largeRects: 7, finderPatternsDetected: true, originalDesignOptions: {…}, designOptionsKeys: (21) […] }
QRPreview.tsx:163:37
Large rectangles (potential finder patterns): 
Array(5) [ '<rect class="qr-bg" fill="#FFFFFF" height="300" id="qr-bg" width="300" x="0" y="0" />', '<rect class="qr-marker-outer" fill="#000000" height="42.857142857142854" width="42.857142857142854" x="0.0" y="0.0" />', '<rect class="qr-marker-inner" fill="#fff" height="28.163265306122444" width="28.163265306122444" x="7.346938775510203" y="7.346938775510203" />', '<rect class="qr-marker-outer" fill="#000000" height="42.857142857142854" width="42.857142857142854" x="257.1428571428571" y="0.0" />', '<rect class="qr-marker-inner" fill="#fff" height="28.163265306122444" width="28.163265306122444" x="264.4897959183673" y="7.346938775510203" />' ]
QRPreview.tsx:177:41
Original design options: 
Object { size: 256, margin: 10, pattern: "square", marker_border_style: "square", marker_border_color: "#000000", marker_center_style: "square", marker_center_color: "#000000", logo_size: 48, logo_position: "center", logo_opacity: 1, … }
QRPreview.tsx:183:37
Filtered design options for basic preview: 
Object { size: 256, margin: 10, error_correction: "M" }
QRPreview.tsx:184:37
Filtered options count: 3 QRPreview.tsx:185:37
SVG Styler: Parsing SVG, element count: 1122 svgStyler.ts:23:13
SVG Styler: Design options: 
Object { size: 256, margin: 10, error_correction: "M" }
svgStyler.ts:24:13
SVG Styler: Using default background color svgStyler.ts:48:17
SVG Styler: Using default foreground color svgStyler.ts:80:17
SVG Styler: Using default square pattern svgStyler.ts:97:17
SVG Styler: Final SVG length: 174998 svgStyler.ts:126:13
Styled SVG: <svg baseProfile="full" height="300" version="1.1" width="300" xmlns="http://www.w3.org/2000/svg" xmlns:ev="http://www.w3.org/2001/xml-events" xmlns:xlink="http://www.w3.org/1999/xlink"><defs/><rect c... QRPreview.tsx:193:41
[API] Response: 200 OK - POST /qr/preview/svg modernClient.ts:27:24
Successfully received SVG QR preview qrService.modern.ts:429:25
[Auth] Adding Bearer token to request: /health layout.tsx:133:49
QRFormState: Field updated 
Object { field: "service_provider", previousValue: undefined, newValue: "b" }
useQRFormState.ts:66:29
QRFormState: Field updated 
Object { field: "service_provider", previousValue: undefined, newValue: "b" }
useQRFormState.ts:66:29
A component is changing an uncontrolled input to be controlled. This is likely caused by the value changing from undefined to a defined value, which should not happen. Decide between using a controlled or uncontrolled input element for the lifetime of the component. More info: https://react.dev/link/controlled-components intercept-console-error.js:50:32
[Auth] Adding Bearer token to request: /__nextjs_original-stack-frames layout.tsx:133:49
QRFormState: Field updated 
Object { field: "service_provider", previousValue: "b", newValue: "bu" }
useQRFormState.ts:66:29
QRFormState: Field updated 
Object { field: "service_provider", previousValue: "b", newValue: "bu" }
useQRFormState.ts:66:29
QRFormState: Notifying parent of changes (debounced) useQRFormState.ts:39:29
QRDataForm (scooter_unlock): Received data update: 
Object { scooter_id: "SCOOT123", unlock_code: "UNLOCK42", title: "Scooter_unlock QR Code", description: "Scan to view scooter_unlock content", service_provider: "bu" }
QRDataForm.tsx:2100:21
QRDataForm (scooter_unlock): Updated form data: 
Object { scooter_id: "SCOOT123", unlock_code: "UNLOCK42", title: "Scooter_unlock QR Code", description: "Scan to view scooter_unlock content", service_provider: "bu" }
QRDataForm.tsx:2133:29
QRDataForm (scooter_unlock): Updated form data: 
Object { scooter_id: "SCOOT123", unlock_code: "UNLOCK42", title: "Scooter_unlock QR Code", description: "Scan to view scooter_unlock content", service_provider: "bu" }
QRDataForm.tsx:2133:29
QRFormState: Initialized with 
Object { hasInitialData: true, mergedDataKeys: (5) […] }
useQRFormState.ts:26:21
QRFormState: Initialized with  
Object { hasInitialData: true, mergedDataKeys: (5) […] }
dashboard:1:596
QRFormState: First render, notifying parent of initial state useQRFormState.ts:150:25
QRDataForm (scooter_unlock): Received data update: 
Object { scooter_id: "SCOOT123", unlock_code: "UNLOCK42", title: "Scooter_unlock QR Code", description: "Scan to view scooter_unlock content", service_provider: "bu" }
QRDataForm.tsx:2100:21
QRFormState: External initialData changed, updating form state useQRFormState.ts:166:25
QRDataForm (scooter_unlock): Notifying parent of data change: 
Object { scooter_id: "SCOOT123", unlock_code: "UNLOCK42", title: "Scooter_unlock QR Code", description: "Scan to view scooter_unlock content", service_provider: "bu" }
QRDataForm.tsx:2153:21
QR data updated: 
Object { scooter_id: "SCOOT123", unlock_code: "UNLOCK42", title: "Scooter_unlock QR Code", description: "Scan to view scooter_unlock content", service_provider: "bu" }
page.tsx:462:21
QRFormState: External initialData changed, updating form state dashboard:1:596
QRDataForm (scooter_unlock): Updated form data: 
Object { scooter_id: "SCOOT123", unlock_code: "UNLOCK42", title: "Scooter_unlock QR Code", description: "Scan to view scooter_unlock content", service_provider: "bu" }
QRDataForm.tsx:2133:29
QRDataForm (scooter_unlock): Updated form data: 
Object { scooter_id: "SCOOT123", unlock_code: "UNLOCK42", title: "Scooter_unlock QR Code", description: "Scan to view scooter_unlock content", service_provider: "bu" }
QRDataForm.tsx:2133:29
QRPreview: Content key updated 
Object { qrType: "scooter_unlock", dataChanged: true, designChanged: true, logoChanged: false, useSvg: true }
QRPreview.tsx:51:21
QRPreview: Content key updated  
Object { qrType: "scooter_unlock", dataChanged: true, designChanged: true, logoChanged: false, useSvg: true }
dashboard:1:596
QRFormState: External initialData changed, updating form state useQRFormState.ts:166:25
QRDataForm (scooter_unlock): Notifying parent of data change: 
Object { scooter_id: "SCOOT123", unlock_code: "UNLOCK42", title: "Scooter_unlock QR Code", description: "Scan to view scooter_unlock content", service_provider: "bu" }
QRDataForm.tsx:2153:21
QR data updated: 
Object { scooter_id: "SCOOT123", unlock_code: "UNLOCK42", title: "Scooter_unlock QR Code", description: "Scan to view scooter_unlock content", service_provider: "bu" }
page.tsx:462:21
QRPreview: Content key updated 
Object { qrType: "scooter_unlock", dataChanged: true, designChanged: true, logoChanged: false, useSvg: true }
QRPreview.tsx:51:21
QRPreview: Content key updated  
Object { qrType: "scooter_unlock", dataChanged: true, designChanged: true, logoChanged: false, useSvg: true }
dashboard:1:596
QRPreview: Generating basic SVG for frontend styling QRPreview.tsx:108:37
QRPreview: Verifying data for scooter_unlock 
Object { scooter_id: "SCOOT123", unlock_code: "UNLOCK42", title: "Scooter_unlock QR Code", description: "Scan to view scooter_unlock content", service_provider: "bu" }
QRPreview.tsx:260:17
QRPreview: Verified data 
Object { scooter_id: "SCOOT123", unlock_code: "UNLOCK42", title: "Scooter_unlock QR Code", description: "Scan to view scooter_unlock content", service_provider: "bu" }
QRPreview.tsx:364:17
Frontend QR preview SVG request: 
Object { qrType: "scooter_unlock", dataSize: 159, designOptions: (6) […], hasLogo: false, dimensions: 300 }
qrService.modern.ts:376:21
Cleaned content for SVG preview: 
Object { scooter_id: "SCOOT123", unlock_code: "UNLOCK42", title: "Scooter_unlock QR Code", description: "Scan to view scooter_unlock content", service_provider: "bu" }
qrService.modern.ts:396:21
[API] Request: POST /qr/preview/svg modernClient.ts:27:24
[API] Added auth token to request modernClient.ts:27:24
Frontend QR preview SVG request: 
Object { qrType: "scooter_unlock", dataSize: 159, designOptions: (32) […], hasLogo: false, dimensions: 300 }
qrService.modern.ts:376:21
Cleaned content for SVG preview: 
Object { scooter_id: "SCOOT123", unlock_code: "UNLOCK42", title: "Scooter_unlock QR Code", description: "Scan to view scooter_unlock content", service_provider: "bu" }
qrService.modern.ts:396:21
[API] Request: POST /qr/preview/svg modernClient.ts:27:24
[API] Added auth token to request modernClient.ts:27:24
[API] Response: 200 OK - POST /qr/preview/svg modernClient.ts:27:24
Successfully received SVG QR preview qrService.modern.ts:429:25
Raw SVG from backend: <svg baseProfile="full" height="300" version="1.1" width="300" xmlns="http://www.w3.org/2000/svg" xmlns:ev="http://www.w3.org/2001/xml-events" xmlns:xlink="http://www.w3.org/1999/xlink"><defs /><rect class="qr-bg" fill="#FFFFFF" height="300" id="qr-bg" width="300" x="0" y="0" /><rect class="qr-module" fill="#000000" height="5.660377358490566" id="module-9-0" width="5.660377358490566" x="50.94339622641509" y="0.0" /><rect class="qr-module" fill="#000000" height="5.660377358490566" id="module-10-0... QRPreview.tsx:127:37
SVG length: 203839 QRPreview.tsx:128:37
SVG contains <svg>: true QRPreview.tsx:129:37
Raw SVG structure: 
Object { rectCount: 1305, pathCount: 0 }
QRPreview.tsx:133:37
SVG analysis: 
Object { hasRect: true, hasPath: false, hasCircle: false, hasBackground: true, hasForeground: true, totalRects: 1305, largeRects: 7, finderPatternsDetected: true, originalDesignOptions: {…}, designOptionsKeys: (21) […] }
QRPreview.tsx:163:37
Large rectangles (potential finder patterns): 
Array(5) [ '<rect class="qr-bg" fill="#FFFFFF" height="300" id="qr-bg" width="300" x="0" y="0" />', '<rect class="qr-marker-outer" fill="#000000" height="39.62264150943396" width="39.62264150943396" x="0.0" y="0.0" />', '<rect class="qr-marker-inner" fill="#fff" height="26.0377358490566" width="26.0377358490566" x="6.792452830188679" y="6.792452830188679" />', '<rect class="qr-marker-outer" fill="#000000" height="39.62264150943396" width="39.62264150943396" x="260.377358490566" y="0.0" />', '<rect class="qr-marker-inner" fill="#fff" height="26.0377358490566" width="26.0377358490566" x="267.16981132075466" y="6.792452830188679" />' ]
QRPreview.tsx:177:41
Original design options: 
Object { size: 256, margin: 10, pattern: "square", marker_border_style: "square", marker_border_color: "#000000", marker_center_style: "square", marker_center_color: "#000000", logo_size: 48, logo_position: "center", logo_opacity: 1, … }
QRPreview.tsx:183:37
Filtered design options for basic preview: 
Object { size: 256, margin: 10, error_correction: "M" }
QRPreview.tsx:184:37
Filtered options count: 3 QRPreview.tsx:185:37
SVG Styler: Parsing SVG, element count: 1300 svgStyler.ts:23:13
SVG Styler: Design options: 
Object { size: 256, margin: 10, error_correction: "M" }
svgStyler.ts:24:13
SVG Styler: Using default background color svgStyler.ts:48:17
SVG Styler: Using default foreground color svgStyler.ts:80:17
SVG Styler: Using default square pattern svgStyler.ts:97:17
SVG Styler: Final SVG length: 202533 svgStyler.ts:126:13
Styled SVG: <svg baseProfile="full" height="300" version="1.1" width="300" xmlns="http://www.w3.org/2000/svg" xmlns:ev="http://www.w3.org/2001/xml-events" xmlns:xlink="http://www.w3.org/1999/xlink"><defs/><rect c... QRPreview.tsx:193:41
[API] Response: 200 OK - POST /qr/preview/svg modernClient.ts:27:24
Successfully received SVG QR preview qrService.modern.ts:429:25
QRPreview: Content key updated 
Object { qrType: "scooter_unlock", dataChanged: true, designChanged: true, logoChanged: false, useSvg: true }
QRPreview.tsx:51:21
QRPreview: Content key updated  
Object { qrType: "scooter_unlock", dataChanged: true, designChanged: true, logoChanged: false, useSvg: true }
dashboard:1:596
QRPreview: Generating basic SVG for frontend styling QRPreview.tsx:108:37
QRPreview: Verifying data for scooter_unlock 
Object { scooter_id: "SCOOT123", unlock_code: "UNLOCK42", title: "Scooter_unlock QR Code", description: "Scan to view scooter_unlock content", service_provider: "bu" }
QRPreview.tsx:260:17
QRPreview: Verified data 
Object { scooter_id: "SCOOT123", unlock_code: "UNLOCK42", title: "Scooter_unlock QR Code", description: "Scan to view scooter_unlock content", service_provider: "bu" }
QRPreview.tsx:364:17
Frontend QR preview SVG request: 
Object { qrType: "scooter_unlock", dataSize: 159, designOptions: (6) […], hasLogo: false, dimensions: 300 }
qrService.modern.ts:376:21
Cleaned content for SVG preview: 
Object { scooter_id: "SCOOT123", unlock_code: "UNLOCK42", title: "Scooter_unlock QR Code", description: "Scan to view scooter_unlock content", service_provider: "bu" }
qrService.modern.ts:396:21
[API] Request: POST /qr/preview/svg modernClient.ts:27:24
[API] Added auth token to request modernClient.ts:27:24
[API] Response: 200 OK - POST /qr/preview/svg modernClient.ts:27:24
Successfully received SVG QR preview qrService.modern.ts:429:25
Raw SVG from backend: <svg baseProfile="full" height="300" version="1.1" width="300" xmlns="http://www.w3.org/2000/svg" xmlns:ev="http://www.w3.org/2001/xml-events" xmlns:xlink="http://www.w3.org/1999/xlink"><defs /><rect class="qr-bg" fill="#FFFFFF" height="300" id="qr-bg" width="300" x="0" y="0" /><rect class="qr-module" fill="#000000" height="5.660377358490566" id="module-9-0" width="5.660377358490566" x="50.94339622641509" y="0.0" /><rect class="qr-module" fill="#000000" height="5.660377358490566" id="module-10-0... QRPreview.tsx:127:37
SVG length: 203839 QRPreview.tsx:128:37
SVG contains <svg>: true QRPreview.tsx:129:37
Raw SVG structure: 
Object { rectCount: 1305, pathCount: 0 }
QRPreview.tsx:133:37
SVG analysis: 
Object { hasRect: true, hasPath: false, hasCircle: false, hasBackground: true, hasForeground: true, totalRects: 1305, largeRects: 7, finderPatternsDetected: true, originalDesignOptions: {…}, designOptionsKeys: (21) […] }
QRPreview.tsx:163:37
Large rectangles (potential finder patterns): 
Array(5) [ '<rect class="qr-bg" fill="#FFFFFF" height="300" id="qr-bg" width="300" x="0" y="0" />', '<rect class="qr-marker-outer" fill="#000000" height="39.62264150943396" width="39.62264150943396" x="0.0" y="0.0" />', '<rect class="qr-marker-inner" fill="#fff" height="26.0377358490566" width="26.0377358490566" x="6.792452830188679" y="6.792452830188679" />', '<rect class="qr-marker-outer" fill="#000000" height="39.62264150943396" width="39.62264150943396" x="260.377358490566" y="0.0" />', '<rect class="qr-marker-inner" fill="#fff" height="26.0377358490566" width="26.0377358490566" x="267.16981132075466" y="6.792452830188679" />' ]
QRPreview.tsx:177:41
Original design options: 
Object { size: 256, margin: 10, pattern: "square", marker_border_style: "square", marker_border_color: "#000000", marker_center_style: "square", marker_center_color: "#000000", logo_size: 48, logo_position: "center", logo_opacity: 1, … }
QRPreview.tsx:183:37
Filtered design options for basic preview: 
Object { size: 256, margin: 10, error_correction: "M" }
QRPreview.tsx:184:37
Filtered options count: 3 QRPreview.tsx:185:37
SVG Styler: Parsing SVG, element count: 1300 svgStyler.ts:23:13
SVG Styler: Design options: 
Object { size: 256, margin: 10, error_correction: "M" }
svgStyler.ts:24:13
SVG Styler: Using default background color svgStyler.ts:48:17
SVG Styler: Using default foreground color svgStyler.ts:80:17
SVG Styler: Using default square pattern svgStyler.ts:97:17
SVG Styler: Final SVG length: 202533 svgStyler.ts:126:13
Styled SVG: <svg baseProfile="full" height="300" version="1.1" width="300" xmlns="http://www.w3.org/2000/svg" xmlns:ev="http://www.w3.org/2001/xml-events" xmlns:xlink="http://www.w3.org/1999/xlink"><defs/><rect c... QRPreview.tsx:193:41
QRFormState: Field updated 
Object { field: "service_provider", previousValue: "bu", newValue: "b" }
useQRFormState.ts:66:29
QRFormState: Field updated 
Object { field: "service_provider", previousValue: "bu", newValue: "b" }
useQRFormState.ts:66:29
QRFormState: Notifying parent of changes (debounced) useQRFormState.ts:39:29
QRDataForm (scooter_unlock): Received data update: 
Object { scooter_id: "SCOOT123", unlock_code: "UNLOCK42", title: "Scooter_unlock QR Code", description: "Scan to view scooter_unlock content", service_provider: "b" }
QRDataForm.tsx:2100:21
QRDataForm (scooter_unlock): Updated form data: 
Object { scooter_id: "SCOOT123", unlock_code: "UNLOCK42", title: "Scooter_unlock QR Code", description: "Scan to view scooter_unlock content", service_provider: "b" }
QRDataForm.tsx:2133:29
QRDataForm (scooter_unlock): Updated form data: 
Object { scooter_id: "SCOOT123", unlock_code: "UNLOCK42", title: "Scooter_unlock QR Code", description: "Scan to view scooter_unlock content", service_provider: "b" }
QRDataForm.tsx:2133:29
QRFormState: External initialData changed, updating form state useQRFormState.ts:166:25
QRDataForm (scooter_unlock): Notifying parent of data change: 
Object { scooter_id: "SCOOT123", unlock_code: "UNLOCK42", title: "Scooter_unlock QR Code", description: "Scan to view scooter_unlock content", service_provider: "b" }
QRDataForm.tsx:2153:21
QR data updated: 
Object { scooter_id: "SCOOT123", unlock_code: "UNLOCK42", title: "Scooter_unlock QR Code", description: "Scan to view scooter_unlock content", service_provider: "b" }
page.tsx:462:21
QRPreview: Content key updated 
Object { qrType: "scooter_unlock", dataChanged: true, designChanged: true, logoChanged: false, useSvg: true }
QRPreview.tsx:51:21
QRPreview: Content key updated  
Object { qrType: "scooter_unlock", dataChanged: true, designChanged: true, logoChanged: false, useSvg: true }
dashboard:1:596
QRPreview: Generating basic SVG for frontend styling QRPreview.tsx:108:37
QRPreview: Verifying data for scooter_unlock 
Object { scooter_id: "SCOOT123", unlock_code: "UNLOCK42", title: "Scooter_unlock QR Code", description: "Scan to view scooter_unlock content", service_provider: "b" }
QRPreview.tsx:260:17
QRPreview: Verified data 
Object { scooter_id: "SCOOT123", unlock_code: "UNLOCK42", title: "Scooter_unlock QR Code", description: "Scan to view scooter_unlock content", service_provider: "b" }
QRPreview.tsx:364:17
Frontend QR preview SVG request: 
Object { qrType: "scooter_unlock", dataSize: 158, designOptions: (6) […], hasLogo: false, dimensions: 300 }
qrService.modern.ts:376:21
Cleaned content for SVG preview: 
Object { scooter_id: "SCOOT123", unlock_code: "UNLOCK42", title: "Scooter_unlock QR Code", description: "Scan to view scooter_unlock content", service_provider: "b" }
qrService.modern.ts:396:21
[API] Request: POST /qr/preview/svg modernClient.ts:27:24
[API] Added auth token to request modernClient.ts:27:24
Frontend QR preview SVG request: 
Object { qrType: "scooter_unlock", dataSize: 158, designOptions: (32) […], hasLogo: false, dimensions: 300 }
qrService.modern.ts:376:21
Cleaned content for SVG preview: 
Object { scooter_id: "SCOOT123", unlock_code: "UNLOCK42", title: "Scooter_unlock QR Code", description: "Scan to view scooter_unlock content", service_provider: "b" }
qrService.modern.ts:396:21
[API] Request: POST /qr/preview/svg modernClient.ts:27:24
[API] Added auth token to request modernClient.ts:27:24
QRFormState: Field updated 
Object { field: "service_provider", previousValue: "b", newValue: "bi" }
useQRFormState.ts:66:29
QRFormState: Field updated 
Object { field: "service_provider", previousValue: "b", newValue: "bi" }
useQRFormState.ts:66:29
[API] Response: 200 OK - POST /qr/preview/svg modernClient.ts:27:24
Successfully received SVG QR preview qrService.modern.ts:429:25
[API] Response: 200 OK - POST /qr/preview/svg modernClient.ts:27:24
Successfully received SVG QR preview qrService.modern.ts:429:25
Raw SVG from backend: <svg baseProfile="full" height="300" version="1.1" width="300" xmlns="http://www.w3.org/2000/svg" xmlns:ev="http://www.w3.org/2001/xml-events" xmlns:xlink="http://www.w3.org/1999/xlink"><defs /><rect class="qr-bg" fill="#FFFFFF" height="300" id="qr-bg" width="300" x="0" y="0" /><rect class="qr-module" fill="#000000" height="5.660377358490566" id="module-9-0" width="5.660377358490566" x="50.94339622641509" y="0.0" /><rect class="qr-module" fill="#000000" height="5.660377358490566" id="module-11-0... QRPreview.tsx:127:37
SVG length: 205022 QRPreview.tsx:128:37
SVG contains <svg>: true QRPreview.tsx:129:37
Raw SVG structure: 
Object { rectCount: 1313, pathCount: 0 }
QRPreview.tsx:133:37
SVG analysis: 
Object { hasRect: true, hasPath: false, hasCircle: false, hasBackground: true, hasForeground: true, totalRects: 1313, largeRects: 7, finderPatternsDetected: true, originalDesignOptions: {…}, designOptionsKeys: (21) […] }
QRPreview.tsx:163:37
Large rectangles (potential finder patterns): 
Array(5) [ '<rect class="qr-bg" fill="#FFFFFF" height="300" id="qr-bg" width="300" x="0" y="0" />', '<rect class="qr-marker-outer" fill="#000000" height="39.62264150943396" width="39.62264150943396" x="0.0" y="0.0" />', '<rect class="qr-marker-inner" fill="#fff" height="26.0377358490566" width="26.0377358490566" x="6.792452830188679" y="6.792452830188679" />', '<rect class="qr-marker-outer" fill="#000000" height="39.62264150943396" width="39.62264150943396" x="260.377358490566" y="0.0" />', '<rect class="qr-marker-inner" fill="#fff" height="26.0377358490566" width="26.0377358490566" x="267.16981132075466" y="6.792452830188679" />' ]
QRPreview.tsx:177:41
Original design options: 
Object { size: 256, margin: 10, pattern: "square", marker_border_style: "square", marker_border_color: "#000000", marker_center_style: "square", marker_center_color: "#000000", logo_size: 48, logo_position: "center", logo_opacity: 1, … }
QRPreview.tsx:183:37
Filtered design options for basic preview: 
Object { size: 256, margin: 10, error_correction: "M" }
QRPreview.tsx:184:37
Filtered options count: 3 QRPreview.tsx:185:37
SVG Styler: Parsing SVG, element count: 1308 svgStyler.ts:23:13
SVG Styler: Design options: 
Object { size: 256, margin: 10, error_correction: "M" }
svgStyler.ts:24:13
SVG Styler: Using default background color svgStyler.ts:48:17
SVG Styler: Using default foreground color svgStyler.ts:80:17
SVG Styler: Using default square pattern svgStyler.ts:97:17
SVG Styler: Final SVG length: 203708 svgStyler.ts:126:13
Styled SVG: <svg baseProfile="full" height="300" version="1.1" width="300" xmlns="http://www.w3.org/2000/svg" xmlns:ev="http://www.w3.org/2001/xml-events" xmlns:xlink="http://www.w3.org/1999/xlink"><defs/><rect c... QRPreview.tsx:193:41
QRPreview: Content key updated 
Object { qrType: "scooter_unlock", dataChanged: true, designChanged: true, logoChanged: false, useSvg: true }
QRPreview.tsx:51:21
QRPreview: Content key updated  
Object { qrType: "scooter_unlock", dataChanged: true, designChanged: true, logoChanged: false, useSvg: true }
dashboard:1:596
QRFormState: Notifying parent of changes (debounced) useQRFormState.ts:39:29
QRDataForm (scooter_unlock): Received data update: 
Object { scooter_id: "SCOOT123", unlock_code: "UNLOCK42", title: "Scooter_unlock QR Code", description: "Scan to view scooter_unlock content", service_provider: "bi" }
QRDataForm.tsx:2100:21
QRDataForm (scooter_unlock): Updated form data: 
Object { scooter_id: "SCOOT123", unlock_code: "UNLOCK42", title: "Scooter_unlock QR Code", description: "Scan to view scooter_unlock content", service_provider: "bi" }
QRDataForm.tsx:2133:29
QRDataForm (scooter_unlock): Updated form data: 
Object { scooter_id: "SCOOT123", unlock_code: "UNLOCK42", title: "Scooter_unlock QR Code", description: "Scan to view scooter_unlock content", service_provider: "bi" }
QRDataForm.tsx:2133:29
QRFormState: External initialData changed, updating form state useQRFormState.ts:166:25
QRDataForm (scooter_unlock): Notifying parent of data change: 
Object { scooter_id: "SCOOT123", unlock_code: "UNLOCK42", title: "Scooter_unlock QR Code", description: "Scan to view scooter_unlock content", service_provider: "bi" }
QRDataForm.tsx:2153:21
QR data updated: 
Object { scooter_id: "SCOOT123", unlock_code: "UNLOCK42", title: "Scooter_unlock QR Code", description: "Scan to view scooter_unlock content", service_provider: "bi" }
page.tsx:462:21
QRPreview: Content key updated 
Object { qrType: "scooter_unlock", dataChanged: true, designChanged: true, logoChanged: false, useSvg: true }
QRPreview.tsx:51:21
QRPreview: Content key updated  
Object { qrType: "scooter_unlock", dataChanged: true, designChanged: true, logoChanged: false, useSvg: true }
dashboard:1:596
QRFormState: Field updated 
Object { field: "service_provider", previousValue: "bi", newValue: "bir" }
useQRFormState.ts:66:29
QRFormState: Field updated 
Object { field: "service_provider", previousValue: "bi", newValue: "bir" }
useQRFormState.ts:66:29
QRFormState: Field updated 
Object { field: "service_provider", previousValue: "bir", newValue: "bird" }
useQRFormState.ts:66:29
QRFormState: Field updated 
Object { field: "service_provider", previousValue: "bir", newValue: "bird" }
useQRFormState.ts:66:29
QRPreview: Generating basic SVG for frontend styling QRPreview.tsx:108:37
QRPreview: Verifying data for scooter_unlock 
Object { scooter_id: "SCOOT123", unlock_code: "UNLOCK42", title: "Scooter_unlock QR Code", description: "Scan to view scooter_unlock content", service_provider: "bi" }
QRPreview.tsx:260:17
QRPreview: Verified data 
Object { scooter_id: "SCOOT123", unlock_code: "UNLOCK42", title: "Scooter_unlock QR Code", description: "Scan to view scooter_unlock content", service_provider: "bi" }
QRPreview.tsx:364:17
Frontend QR preview SVG request: 
Object { qrType: "scooter_unlock", dataSize: 159, designOptions: (6) […], hasLogo: false, dimensions: 300 }
qrService.modern.ts:376:21
Cleaned content for SVG preview: 
Object { scooter_id: "SCOOT123", unlock_code: "UNLOCK42", title: "Scooter_unlock QR Code", description: "Scan to view scooter_unlock content", service_provider: "bi" }
qrService.modern.ts:396:21
[API] Request: POST /qr/preview/svg modernClient.ts:27:24
[API] Added auth token to request modernClient.ts:27:24
Frontend QR preview SVG request: 
Object { qrType: "scooter_unlock", dataSize: 159, designOptions: (32) […], hasLogo: false, dimensions: 300 }
qrService.modern.ts:376:21
Cleaned content for SVG preview: 
Object { scooter_id: "SCOOT123", unlock_code: "UNLOCK42", title: "Scooter_unlock QR Code", description: "Scan to view scooter_unlock content", service_provider: "bi" }
qrService.modern.ts:396:21
[API] Request: POST /qr/preview/svg modernClient.ts:27:24
[API] Added auth token to request modernClient.ts:27:24
[API] Response: 200 OK - POST /qr/preview/svg modernClient.ts:27:24
Successfully received SVG QR preview qrService.modern.ts:429:25
Raw SVG from backend: <svg baseProfile="full" height="300" version="1.1" width="300" xmlns="http://www.w3.org/2000/svg" xmlns:ev="http://www.w3.org/2001/xml-events" xmlns:xlink="http://www.w3.org/1999/xlink"><defs /><rect class="qr-bg" fill="#FFFFFF" height="300" id="qr-bg" width="300" x="0" y="0" /><rect class="qr-module" fill="#000000" height="5.660377358490566" id="module-9-0" width="5.660377358490566" x="50.94339622641509" y="0.0" /><rect class="qr-module" fill="#000000" height="5.660377358490566" id="module-10-0... QRPreview.tsx:127:37
SVG length: 204070 QRPreview.tsx:128:37
SVG contains <svg>: true QRPreview.tsx:129:37
Raw SVG structure: 
Object { rectCount: 1307, pathCount: 0 }
QRPreview.tsx:133:37
SVG analysis: 
Object { hasRect: true, hasPath: false, hasCircle: false, hasBackground: true, hasForeground: true, totalRects: 1307, largeRects: 7, finderPatternsDetected: true, originalDesignOptions: {…}, designOptionsKeys: (21) […] }
QRPreview.tsx:163:37
Large rectangles (potential finder patterns): 
Array(5) [ '<rect class="qr-bg" fill="#FFFFFF" height="300" id="qr-bg" width="300" x="0" y="0" />', '<rect class="qr-marker-outer" fill="#000000" height="39.62264150943396" width="39.62264150943396" x="0.0" y="0.0" />', '<rect class="qr-marker-inner" fill="#fff" height="26.0377358490566" width="26.0377358490566" x="6.792452830188679" y="6.792452830188679" />', '<rect class="qr-marker-outer" fill="#000000" height="39.62264150943396" width="39.62264150943396" x="260.377358490566" y="0.0" />', '<rect class="qr-marker-inner" fill="#fff" height="26.0377358490566" width="26.0377358490566" x="267.16981132075466" y="6.792452830188679" />' ]
QRPreview.tsx:177:41
Original design options: 
Object { size: 256, margin: 10, pattern: "square", marker_border_style: "square", marker_border_color: "#000000", marker_center_style: "square", marker_center_color: "#000000", logo_size: 48, logo_position: "center", logo_opacity: 1, … }
QRPreview.tsx:183:37
Filtered design options for basic preview: 
Object { size: 256, margin: 10, error_correction: "M" }
QRPreview.tsx:184:37
Filtered options count: 3 QRPreview.tsx:185:37
SVG Styler: Parsing SVG, element count: 1302 svgStyler.ts:23:13
SVG Styler: Design options: 
Object { size: 256, margin: 10, error_correction: "M" }
svgStyler.ts:24:13
SVG Styler: Using default background color svgStyler.ts:48:17
SVG Styler: Using default foreground color svgStyler.ts:80:17
SVG Styler: Using default square pattern svgStyler.ts:97:17
SVG Styler: Final SVG length: 202762 svgStyler.ts:126:13
Styled SVG: <svg baseProfile="full" height="300" version="1.1" width="300" xmlns="http://www.w3.org/2000/svg" xmlns:ev="http://www.w3.org/2001/xml-events" xmlns:xlink="http://www.w3.org/1999/xlink"><defs/><rect c... QRPreview.tsx:193:41
[API] Response: 200 OK - POST /qr/preview/svg modernClient.ts:27:24
Successfully received SVG QR preview qrService.modern.ts:429:25
QRPreview: Content key updated 
Object { qrType: "scooter_unlock", dataChanged: true, designChanged: true, logoChanged: false, useSvg: true }
QRPreview.tsx:51:21
QRPreview: Content key updated  
Object { qrType: "scooter_unlock", dataChanged: true, designChanged: true, logoChanged: false, useSvg: true }
dashboard:1:596
QRFormState: Notifying parent of changes (debounced) useQRFormState.ts:39:29
QRDataForm (scooter_unlock): Received data update: 
Object { scooter_id: "SCOOT123", unlock_code: "UNLOCK42", title: "Scooter_unlock QR Code", description: "Scan to view scooter_unlock content", service_provider: "bird" }
QRDataForm.tsx:2100:21
QRDataForm (scooter_unlock): Updated form data: 
Object { scooter_id: "SCOOT123", unlock_code: "UNLOCK42", title: "Scooter_unlock QR Code", description: "Scan to view scooter_unlock content", service_provider: "bird" }
QRDataForm.tsx:2133:29
QRDataForm (scooter_unlock): Updated form data: 
Object { scooter_id: "SCOOT123", unlock_code: "UNLOCK42", title: "Scooter_unlock QR Code", description: "Scan to view scooter_unlock content", service_provider: "bird" }
QRDataForm.tsx:2133:29
QRFormState: External initialData changed, updating form state useQRFormState.ts:166:25
QRDataForm (scooter_unlock): Notifying parent of data change: 
Object { scooter_id: "SCOOT123", unlock_code: "UNLOCK42", title: "Scooter_unlock QR Code", description: "Scan to view scooter_unlock content", service_provider: "bird" }
QRDataForm.tsx:2153:21
QR data updated: 
Object { scooter_id: "SCOOT123", unlock_code: "UNLOCK42", title: "Scooter_unlock QR Code", description: "Scan to view scooter_unlock content", service_provider: "bird" }
page.tsx:462:21
QRPreview: Content key updated 
Object { qrType: "scooter_unlock", dataChanged: true, designChanged: true, logoChanged: false, useSvg: true }
QRPreview.tsx:51:21
QRPreview: Content key updated  
Object { qrType: "scooter_unlock", dataChanged: true, designChanged: true, logoChanged: false, useSvg: true }
dashboard:1:596
QRPreview: Generating basic SVG for frontend styling QRPreview.tsx:108:37
QRPreview: Verifying data for scooter_unlock 
Object { scooter_id: "SCOOT123", unlock_code: "UNLOCK42", title: "Scooter_unlock QR Code", description: "Scan to view scooter_unlock content", service_provider: "bird" }
QRPreview.tsx:260:17
QRPreview: Verified data 
Object { scooter_id: "SCOOT123", unlock_code: "UNLOCK42", title: "Scooter_unlock QR Code", description: "Scan to view scooter_unlock content", service_provider: "bird" }
QRPreview.tsx:364:17
Frontend QR preview SVG request: 
Object { qrType: "scooter_unlock", dataSize: 161, designOptions: (6) […], hasLogo: false, dimensions: 300 }
qrService.modern.ts:376:21
Cleaned content for SVG preview: 
Object { scooter_id: "SCOOT123", unlock_code: "UNLOCK42", title: "Scooter_unlock QR Code", description: "Scan to view scooter_unlock content", service_provider: "bird" }
qrService.modern.ts:396:21
[API] Request: POST /qr/preview/svg modernClient.ts:27:24
[API] Added auth token to request modernClient.ts:27:24
Frontend QR preview SVG request: 
Object { qrType: "scooter_unlock", dataSize: 161, designOptions: (32) […], hasLogo: false, dimensions: 300 }
qrService.modern.ts:376:21
Cleaned content for SVG preview: 
Object { scooter_id: "SCOOT123", unlock_code: "UNLOCK42", title: "Scooter_unlock QR Code", description: "Scan to view scooter_unlock content", service_provider: "bird" }
qrService.modern.ts:396:21
[API] Request: POST /qr/preview/svg modernClient.ts:27:24
[API] Added auth token to request modernClient.ts:27:24
[API] Response: 200 OK - POST /qr/preview/svg modernClient.ts:27:24
Successfully received SVG QR preview qrService.modern.ts:429:25
[API] Response: 200 OK - POST /qr/preview/svg modernClient.ts:27:24
Successfully received SVG QR preview qrService.modern.ts:429:25
Raw SVG from backend: <svg baseProfile="full" height="300" version="1.1" width="300" xmlns="http://www.w3.org/2000/svg" xmlns:ev="http://www.w3.org/2001/xml-events" xmlns:xlink="http://www.w3.org/1999/xlink"><defs /><rect class="qr-bg" fill="#FFFFFF" height="300" id="qr-bg" width="300" x="0" y="0" /><rect class="qr-module" fill="#000000" height="5.660377358490566" id="module-15-0" width="5.660377358490566" x="84.90566037735849" y="0.0" /><rect class="qr-module" fill="#000000" height="5.660377358490566" id="module-16-... QRPreview.tsx:127:37
SVG length: 203170 QRPreview.tsx:128:37
SVG contains <svg>: true QRPreview.tsx:129:37
Raw SVG structure: 
Object { rectCount: 1301, pathCount: 0 }
QRPreview.tsx:133:37
SVG analysis: 
Object { hasRect: true, hasPath: false, hasCircle: false, hasBackground: true, hasForeground: true, totalRects: 1301, largeRects: 7, finderPatternsDetected: true, originalDesignOptions: {…}, designOptionsKeys: (21) […] }
QRPreview.tsx:163:37
Large rectangles (potential finder patterns): 
Array(5) [ '<rect class="qr-bg" fill="#FFFFFF" height="300" id="qr-bg" width="300" x="0" y="0" />', '<rect class="qr-marker-outer" fill="#000000" height="39.62264150943396" width="39.62264150943396" x="0.0" y="0.0" />', '<rect class="qr-marker-inner" fill="#fff" height="26.0377358490566" width="26.0377358490566" x="6.792452830188679" y="6.792452830188679" />', '<rect class="qr-marker-outer" fill="#000000" height="39.62264150943396" width="39.62264150943396" x="260.377358490566" y="0.0" />', '<rect class="qr-marker-inner" fill="#fff" height="26.0377358490566" width="26.0377358490566" x="267.16981132075466" y="6.792452830188679" />' ]
QRPreview.tsx:177:41
Original design options: 
Object { size: 256, margin: 10, pattern: "square", marker_border_style: "square", marker_border_color: "#000000", marker_center_style: "square", marker_center_color: "#000000", logo_size: 48, logo_position: "center", logo_opacity: 1, … }
QRPreview.tsx:183:37
Filtered design options for basic preview: 
Object { size: 256, margin: 10, error_correction: "M" }
QRPreview.tsx:184:37
Filtered options count: 3 QRPreview.tsx:185:37
SVG Styler: Parsing SVG, element count: 1296 svgStyler.ts:23:13
SVG Styler: Design options: 
Object { size: 256, margin: 10, error_correction: "M" }
svgStyler.ts:24:13
SVG Styler: Using default background color svgStyler.ts:48:17
SVG Styler: Using default foreground color svgStyler.ts:80:17
SVG Styler: Using default square pattern svgStyler.ts:97:17
SVG Styler: Final SVG length: 201868 svgStyler.ts:126:13
Styled SVG: <svg baseProfile="full" height="300" version="1.1" width="300" xmlns="http://www.w3.org/2000/svg" xmlns:ev="http://www.w3.org/2001/xml-events" xmlns:xlink="http://www.w3.org/1999/xlink"><defs/><rect c... QRPreview.tsx:193:41
QRPreview: Content key updated 
Object { qrType: "scooter_unlock", dataChanged: true, designChanged: true, logoChanged: false, useSvg: true }
QRPreview.tsx:51:21
QRPreview: Content key updated  
Object { qrType: "scooter_unlock", dataChanged: true, designChanged: true, logoChanged: false, useSvg: true }
dashboard:1:596
QRPreview: Generating basic SVG for frontend styling QRPreview.tsx:108:37
QRPreview: Verifying data for scooter_unlock 
Object { scooter_id: "SCOOT123", unlock_code: "UNLOCK42", title: "Scooter_unlock QR Code", description: "Scan to view scooter_unlock content", service_provider: "bird" }
QRPreview.tsx:260:17
QRPreview: Verified data 
Object { scooter_id: "SCOOT123", unlock_code: "UNLOCK42", title: "Scooter_unlock QR Code", description: "Scan to view scooter_unlock content", service_provider: "bird" }
QRPreview.tsx:364:17
Frontend QR preview SVG request: 
Object { qrType: "scooter_unlock", dataSize: 161, designOptions: (6) […], hasLogo: false, dimensions: 300 }
qrService.modern.ts:376:21
Cleaned content for SVG preview: 
Object { scooter_id: "SCOOT123", unlock_code: "UNLOCK42", title: "Scooter_unlock QR Code", description: "Scan to view scooter_unlock content", service_provider: "bird" }
qrService.modern.ts:396:21
[API] Request: POST /qr/preview/svg modernClient.ts:27:24
[API] Added auth token to request modernClient.ts:27:24
[API] Response: 200 OK - POST /qr/preview/svg modernClient.ts:27:24
Successfully received SVG QR preview qrService.modern.ts:429:25
Raw SVG from backend: <svg baseProfile="full" height="300" version="1.1" width="300" xmlns="http://www.w3.org/2000/svg" xmlns:ev="http://www.w3.org/2001/xml-events" xmlns:xlink="http://www.w3.org/1999/xlink"><defs /><rect class="qr-bg" fill="#FFFFFF" height="300" id="qr-bg" width="300" x="0" y="0" /><rect class="qr-module" fill="#000000" height="5.660377358490566" id="module-15-0" width="5.660377358490566" x="84.90566037735849" y="0.0" /><rect class="qr-module" fill="#000000" height="5.660377358490566" id="module-16-... QRPreview.tsx:127:37
SVG length: 203170 QRPreview.tsx:128:37
SVG contains <svg>: true QRPreview.tsx:129:37
Raw SVG structure: 
Object { rectCount: 1301, pathCount: 0 }
QRPreview.tsx:133:37
SVG analysis: 
Object { hasRect: true, hasPath: false, hasCircle: false, hasBackground: true, hasForeground: true, totalRects: 1301, largeRects: 7, finderPatternsDetected: true, originalDesignOptions: {…}, designOptionsKeys: (21) […] }
QRPreview.tsx:163:37
Large rectangles (potential finder patterns): 
Array(5) [ '<rect class="qr-bg" fill="#FFFFFF" height="300" id="qr-bg" width="300" x="0" y="0" />', '<rect class="qr-marker-outer" fill="#000000" height="39.62264150943396" width="39.62264150943396" x="0.0" y="0.0" />', '<rect class="qr-marker-inner" fill="#fff" height="26.0377358490566" width="26.0377358490566" x="6.792452830188679" y="6.792452830188679" />', '<rect class="qr-marker-outer" fill="#000000" height="39.62264150943396" width="39.62264150943396" x="260.377358490566" y="0.0" />', '<rect class="qr-marker-inner" fill="#fff" height="26.0377358490566" width="26.0377358490566" x="267.16981132075466" y="6.792452830188679" />' ]
QRPreview.tsx:177:41
Original design options: 
Object { size: 256, margin: 10, pattern: "square", marker_border_style: "square", marker_border_color: "#000000", marker_center_style: "square", marker_center_color: "#000000", logo_size: 48, logo_position: "center", logo_opacity: 1, … }
QRPreview.tsx:183:37
Filtered design options for basic preview: 
Object { size: 256, margin: 10, error_correction: "M" }
QRPreview.tsx:184:37
Filtered options count: 3 QRPreview.tsx:185:37
SVG Styler: Parsing SVG, element count: 1296 svgStyler.ts:23:13
SVG Styler: Design options: 
Object { size: 256, margin: 10, error_correction: "M" }
svgStyler.ts:24:13
SVG Styler: Using default background color svgStyler.ts:48:17
SVG Styler: Using default foreground color svgStyler.ts:80:17
SVG Styler: Using default square pattern svgStyler.ts:97:17
SVG Styler: Final SVG length: 201868 svgStyler.ts:126:13
Styled SVG: <svg baseProfile="full" height="300" version="1.1" width="300" xmlns="http://www.w3.org/2000/svg" xmlns:ev="http://www.w3.org/2001/xml-events" xmlns:xlink="http://www.w3.org/1999/xlink"><defs/><rect c... QRPreview.tsx:193:41
[Auth] Adding Bearer token to request: /health 2 layout.tsx:133:49
[Auth] Health check successful context.tsx:123:37
[Auth] Adding Bearer token to request: /health layout.tsx:133:49
[Fast Refresh] rebuilding hot-reloader-client.js:197:29
[Auth] Adding Bearer token to request: Request layout.tsx:133:49
[Auth] Adding Bearer token to request: /_next/static/webpack/d8980d56324a4b1c.webpack.hot-update.json layout.tsx:133:49
[Fast Refresh] done in 2939ms report-hmr-latency.js:14:13
QRPreview: Content key updated 
Object { qrType: "scooter_unlock", dataChanged: true, designChanged: true, logoChanged: false, useSvg: true }
QRPreview.tsx:51:21
QRPreview: Content key updated  
Object { qrType: "scooter_unlock", dataChanged: true, designChanged: true, logoChanged: false, useSvg: true }
dashboard:1:596
[DashboardLayout] Checking token: Present (327 chars) layout.tsx:89:29
[DashboardLayout] Valid token found on initial check layout.tsx:96:25
[DashboardLayout] Setting up auth headers layout.tsx:101:29
QRPreview: Generating basic SVG for frontend styling QRPreview.tsx:108:37
QRPreview: Verifying data for scooter_unlock 
Object { scooter_id: "SCOOT123", unlock_code: "UNLOCK42", title: "Scooter_unlock QR Code", description: "Scan to view scooter_unlock content", service_provider: "bird" }
QRPreview.tsx:260:17
QRPreview: Verified data 
Object { scooter_id: "SCOOT123", unlock_code: "UNLOCK42", title: "Scooter_unlock QR Code", description: "Scan to view scooter_unlock content", service_provider: "bird" }
QRPreview.tsx:364:17
Frontend QR preview SVG request: 
Object { qrType: "scooter_unlock", dataSize: 161, designOptions: (6) […], hasLogo: false, dimensions: 300 }
qrService.modern.ts:376:21
Cleaned content for SVG preview: 
Object { scooter_id: "SCOOT123", unlock_code: "UNLOCK42", title: "Scooter_unlock QR Code", description: "Scan to view scooter_unlock content", service_provider: "bird" }
qrService.modern.ts:396:21
[API] Request: POST /qr/preview/svg modernClient.ts:27:24
[API] Added auth token to request modernClient.ts:27:24
[API] Response: 200 OK - POST /qr/preview/svg modernClient.ts:27:24
Successfully received SVG QR preview qrService.modern.ts:429:25
Raw SVG from backend: <svg baseProfile="full" height="300" version="1.1" width="300" xmlns="http://www.w3.org/2000/svg" xmlns:ev="http://www.w3.org/2001/xml-events" xmlns:xlink="http://www.w3.org/1999/xlink"><defs /><rect class="qr-bg" fill="#FFFFFF" height="300" id="qr-bg" width="300" x="0" y="0" /><rect class="qr-module" fill="#000000" height="5.660377358490566" id="module-15-0" width="5.660377358490566" x="84.90566037735849" y="0.0" /><rect class="qr-module" fill="#000000" height="5.660377358490566" id="module-16-... QRPreview.tsx:127:37
SVG length: 203170 QRPreview.tsx:128:37
SVG contains <svg>: true QRPreview.tsx:129:37
Raw SVG structure: 
Object { rectCount: 1301, pathCount: 0 }
QRPreview.tsx:133:37
SVG analysis: 
Object { hasRect: true, hasPath: false, hasCircle: false, hasBackground: true, hasForeground: true, totalRects: 1301, largeRects: 7, finderPatternsDetected: true, originalDesignOptions: {…}, designOptionsKeys: (21) […] }
QRPreview.tsx:163:37
Large rectangles (potential finder patterns): 
Array(5) [ '<rect class="qr-bg" fill="#FFFFFF" height="300" id="qr-bg" width="300" x="0" y="0" />', '<rect class="qr-marker-outer" fill="#000000" height="39.62264150943396" width="39.62264150943396" x="0.0" y="0.0" />', '<rect class="qr-marker-inner" fill="#fff" height="26.0377358490566" width="26.0377358490566" x="6.792452830188679" y="6.792452830188679" />', '<rect class="qr-marker-outer" fill="#000000" height="39.62264150943396" width="39.62264150943396" x="260.377358490566" y="0.0" />', '<rect class="qr-marker-inner" fill="#fff" height="26.0377358490566" width="26.0377358490566" x="267.16981132075466" y="6.792452830188679" />' ]
QRPreview.tsx:177:41
Original design options: 
Object { size: 256, margin: 10, pattern: "square", marker_border_style: "square", marker_border_color: "#000000", marker_center_style: "square", marker_center_color: "#000000", logo_size: 48, logo_position: "center", logo_opacity: 1, … }
QRPreview.tsx:183:37
Filtered design options for basic preview: 
Object { size: 256, margin: 10, error_correction: "M" }
QRPreview.tsx:184:37
Filtered options count: 3 QRPreview.tsx:185:37
SVG Styler: Parsing SVG, element count: 1296 svgStyler.ts:23:13
SVG Styler: Design options: 
Object { size: 256, margin: 10, error_correction: "M" }
svgStyler.ts:24:13
SVG Styler: Using default background color svgStyler.ts:48:17
SVG Styler: Using default foreground color svgStyler.ts:80:17
SVG Styler: Using default square pattern svgStyler.ts:97:17
SVG Styler: Final SVG length: 201868 svgStyler.ts:126:13
Styled SVG: <svg baseProfile="full" height="300" version="1.1" width="300" xmlns="http://www.w3.org/2000/svg" xmlns:ev="http://www.w3.org/2001/xml-events" xmlns:xlink="http://www.w3.org/1999/xlink"><defs/><rect c... QRPreview.tsx:193:41
[Auth] Adding Bearer token to request: /health 2 layout.tsx:133:49
[Auth] Health check successful context.tsx:123:37
[Fast Refresh] rebuilding hot-reloader-client.js:197:29
[Auth] Adding Bearer token to request: /_next/static/webpack/dcf737b17adec970.webpack.hot-update.json layout.tsx:133:49
Source map error: Error: request failed with status 404
Stack in the worker:networkRequest@resource://devtools/client/shared/source-map-loader/utils/network-request.js:43:9

Resource URL: null
Source Map URL: installHook.js.map
[Fast Refresh] done in 1200ms report-hmr-latency.js:14:13
