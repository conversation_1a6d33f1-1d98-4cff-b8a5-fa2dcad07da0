Rendering /app/(user)/layout.tsx (user layout) layout.tsx:16:9
Rendering /app/layout.tsx (global layout) layout.tsx:25:9
[DashboardLayout] Checking token: Not found layout.tsx:89:29
[DashboardLayout] Waiting for hydration... layout.tsx:150:25
[DashboardLayout] Setting authentication timeout layout.tsx:216:25
[CryptoSession] Found session key in storage, attempting to restore cryptoSession.ts:46:17
[CryptoSession] Session key successfully restored from storage cryptoSession.ts:55:17
Session crypto initialized context.tsx:79:37
[CryptoSession] Found session key in storage, attempting to restore cryptoSession.ts:46:17
[CryptoSession] Session key successfully restored from storage cryptoSession.ts:55:17
[CryptoSession] Found encrypted token, attempting to decrypt, length: 565 cryptoSession.ts:181:17
[CryptoSession] Base64 successfully decoded - IV length: 12 CT length: 409 cryptoSession.ts:208:21
[DashboardLayout] Checking token: Not found layout.tsx:89:29
[DashboardLayout] No authentication detected, performing delayed check... layout.tsx:171:21
[CryptoSession] Token successfully decrypted from session storage: {"access_token":"eyJ... cryptoSession.ts:222:21
[TokenStore] Token restored from encrypted sessionStorage tokenStore.ts:56:21
[Auth] Health check successful context.tsx:123:37
[DashboardLayout] Checking token: Present (325 chars) layout.tsx:89:29
[DashboardLayout] Valid token found on initial check layout.tsx:96:25
[DashboardLayout] Setting up auth headers layout.tsx:101:29
[DashboardLayout] Adding X-Has-Auth-Token meta tag layout.tsx:107:37
[DashboardLayout] Setting up fetch interceptor layout.tsx:116:37
[DashboardLayout] Setting authentication timeout layout.tsx:216:25
[Auth] Getting current user with Bearer token authentication authService.ts:315:21
[Auth] Token available: true Length: 325 authService.ts:316:21
[Auth] Making /auth/me request with Bearer token: eyJhbGciOiJIUzI... authService.ts:332:21
[Auth] Adding Bearer token to request: /auth/me layout.tsx:133:49
[DashboardLayout] Checking token: Present (325 chars) layout.tsx:89:29
[DashboardLayout] Valid token found on initial check layout.tsx:96:25
[DashboardLayout] Setting up auth headers layout.tsx:101:29
[DashboardLayout] Setting authentication timeout layout.tsx:216:25
[Auth] Auth check response: 200 authService.ts:342:21
Using UNSAFE_componentWillReceiveProps in strict mode is not recommended and may indicate bugs in your code. See https://react.dev/link/unsafe-component-lifecycles for details.

* Move data fetching code or side effects to componentDidUpdate.
* If you're updating state whenever props change, refactor your code to use memoization techniques or move it to static getDerivedStateFromProps. Learn more at: https://react.dev/link/derived-state

Please update the following components: Tour intercept-console-error.js:50:32
[Auth] Adding Bearer token to request: /__nextjs_original-stack-frames layout.tsx:133:49
[Auth] User data retrieved successfully: <EMAIL> authService.ts:355:21
Request for font "Geist" blocked at visibility level 2 (requires 3)
layout.js line 1976 > eval:165:35
[Auth] Adding Bearer token to request: /health layout.tsx:133:49
[DashboardLayout] Checking token: Present (325 chars) layout.tsx:89:29
[DashboardLayout] Valid token found on initial check layout.tsx:96:25
[DashboardLayout] Setting up auth headers layout.tsx:101:29
[Auth] Adding Bearer token to request: /health dashboard:1:596
[API] Request: GET /subscription/features modernClient.ts:27:24
[API] Added auth token to request modernClient.ts:27:24
[API] Request: GET /subscription/current modernClient.ts:27:24
[API] Added auth token to request modernClient.ts:27:24
[API] Request: GET /notifications modernClient.ts:27:24
[API] Added auth token to request modernClient.ts:27:24
[API] Request: GET /notifications/unread-count modernClient.ts:27:24
[API] Added auth token to request modernClient.ts:27:24
[API] Request: GET /dashboard/stats modernClient.ts:27:24
[API] Added auth token to request modernClient.ts:27:24
[API] Response: 200 OK - GET /dashboard/stats modernClient.ts:27:24
[API] Response: 200 OK - GET /notifications/unread-count modernClient.ts:27:24
[API] Response: 200 OK - GET /notifications modernClient.ts:27:24
[API] Response: 200 OK - GET /subscription/current modernClient.ts:27:24
[API] Response: 200 OK - GET /subscription/features modernClient.ts:27:24
The resource at “https://localhost:3000/_next/static/media/0484562807a97172-s.p.woff2” preloaded with link preload was not used within a few seconds. Make sure all attributes of the preload tag are set correctly. dashboard
The resource at “https://localhost:3000/_next/static/media/8888a3826f4a3af4-s.p.woff2” preloaded with link preload was not used within a few seconds. Make sure all attributes of the preload tag are set correctly. dashboard
The resource at “https://localhost:3000/_next/static/media/b957ea75a84b6ea7-s.p.woff2” preloaded with link preload was not used within a few seconds. Make sure all attributes of the preload tag are set correctly. dashboard
The resource at “https://localhost:3000/_next/static/media/eafabf029ad39a43-s.p.woff2” preloaded with link preload was not used within a few seconds. Make sure all attributes of the preload tag are set correctly. dashboard
[Auth] Adding Bearer token to request: /health layout.tsx:133:49
MouseEvent.mozInputSource is deprecated. Use PointerEvent.pointerType instead. layout.js line 2405 > eval:22:9
[Auth] Adding Bearer token to request: Request layout.tsx:133:49
[Auth] Adding Bearer token to request: /health 3 layout.tsx:133:49
[Auth] Health check successful context.tsx:123:37
Frontend QR preview SVG request: 
Object { qrType: "url", dataSize: 160, designOptions: (17) […], hasLogo: false, dimensions: 300 }
qrService.modern.ts:376:21
Cleaned content for SVG preview: 
Object { url: "https://example.com", utm_source: "", utm_medium: "", utm_campaign: "", short_url: false, title: "Url QR Code", description: "Scan to view url content" }
qrService.modern.ts:396:21
[API] Request: POST /qr/preview/svg modernClient.ts:27:24
[API] Added auth token to request modernClient.ts:27:24
[API] Response: 200 OK - POST /qr/preview/svg modernClient.ts:27:24
Successfully received SVG QR preview qrService.modern.ts:429:25
Frontend QR preview SVG request: 
Object { qrType: "email", dataSize: 240, designOptions: (17) […], hasLogo: false, dimensions: 300 }
qrService.modern.ts:376:21
Cleaned content for SVG preview: 
Object { email: "<EMAIL>", subject: "Hello from QRVibe", body: "I scanned your QR code and wanted to reach out.", cc: "", bcc: "", title: "Email QR Code", description: "Scan to view email content", email_address: "<EMAIL>" }
qrService.modern.ts:396:21
[API] Request: POST /qr/preview/svg modernClient.ts:27:24
[API] Added auth token to request modernClient.ts:27:24
[API] Response: 200 OK - POST /qr/preview/svg modernClient.ts:27:24
Successfully received SVG QR preview qrService.modern.ts:429:25
QRPreview: Content key updated 
Object { qrType: "email", dataChanged: true, designChanged: true, logoChanged: false, useSvg: true }
QRPreview.tsx:51:21
QRPreview: Content key updated  
Object { qrType: "email", dataChanged: true, designChanged: true, logoChanged: false, useSvg: true }
dashboard:1:596
QRDataForm: initializing email with provided data + defaults: 
Object { email: "<EMAIL>", subject: "Hello from QRVibe", body: "I scanned your QR code and wanted to reach out.", cc: "", bcc: "", title: "Email QR Code", description: "Scan to view email content" }
QRDataForm.tsx:2072:33
QR data updated: 
Object { email: "<EMAIL>", subject: "Hello from QRVibe", body: "I scanned your QR code and wanted to reach out.", cc: "", bcc: "", title: "Email QR Code", description: "Scan to view email content" }
page.tsx:462:21
QRDataForm: initializing email with provided data + defaults:  
Object { email: "<EMAIL>", subject: "Hello from QRVibe", body: "I scanned your QR code and wanted to reach out.", cc: "", bcc: "", title: "Email QR Code", description: "Scan to view email content" }
dashboard:1:596
QR data updated:  
Object { email: "<EMAIL>", subject: "Hello from QRVibe", body: "I scanned your QR code and wanted to reach out.", cc: "", bcc: "", title: "Email QR Code", description: "Scan to view email content" }
dashboard:1:596
QRPreview: Content key updated 
Object { qrType: "email", dataChanged: true, designChanged: true, logoChanged: false, useSvg: true }
QRPreview.tsx:51:21
QRPreview: Content key updated  
Object { qrType: "email", dataChanged: true, designChanged: true, logoChanged: false, useSvg: true }
dashboard:1:596
QRDataForm (email): Notifying parent of data change: 
Object { email: "<EMAIL>", subject: "Hello from QRVibe", body: "I scanned your QR code and wanted to reach out.", cc: "", bcc: "", title: "Email QR Code", description: "Scan to view email content" }
QRDataForm.tsx:2153:21
QR data updated: 
Object { email: "<EMAIL>", subject: "Hello from QRVibe", body: "I scanned your QR code and wanted to reach out.", cc: "", bcc: "", title: "Email QR Code", description: "Scan to view email content" }
page.tsx:462:21
QRPreview: Content key updated 
Object { qrType: "email", dataChanged: true, designChanged: true, logoChanged: false, useSvg: true }
QRPreview.tsx:51:21
QRPreview: Content key updated  
Object { qrType: "email", dataChanged: true, designChanged: true, logoChanged: false, useSvg: true }
dashboard:1:596
QRPreview: Generating basic SVG for frontend styling QRPreview.tsx:108:37
QRPreview: Verifying data for email 
Object { 0: "c", 1: "o", 2: "n", 3: "t", 4: "a", 5: "c", 6: "t", 7: "@", 8: "e", 9: "x", … }
QRPreview.tsx:260:17
QRPreview: Verified data 
Object { 0: "c", 1: "o", 2: "n", 3: "t", 4: "a", 5: "c", 6: "t", 7: "@", 8: "e", 9: "x", … }
QRPreview.tsx:364:17
Frontend QR preview SVG request: 
Object { qrType: "email", dataSize: 363, designOptions: (6) […], hasLogo: false, dimensions: 300 }
qrService.modern.ts:376:21
Cleaned content for SVG preview: 
Object { email: "<EMAIL>", subject: "Hello from QRVibe", body: "I scanned your QR code and wanted to reach out.", cc: "", bcc: "", title: "Email QR Code", description: "Scan to view email content" }
qrService.modern.ts:396:21
[API] Request: POST /qr/preview/svg modernClient.ts:27:24
[API] Added auth token to request modernClient.ts:27:24
Frontend QR preview SVG request: 
Object { qrType: "email", dataSize: 401, designOptions: (32) […], hasLogo: false, dimensions: 300 }
qrService.modern.ts:376:21
Cleaned content for SVG preview: 
Object { email: "<EMAIL>", subject: "Hello from QRVibe", body: "I scanned your QR code and wanted to reach out.", cc: "", bcc: "", title: "Email QR Code", description: "Scan to view email content", email_address: "<EMAIL>" }
qrService.modern.ts:396:21
[API] Request: POST /qr/preview/svg modernClient.ts:27:24
[API] Added auth token to request modernClient.ts:27:24
[API] Response: 200 OK - POST /qr/preview/svg modernClient.ts:27:24
Successfully received SVG QR preview qrService.modern.ts:429:25
Raw SVG from backend: <svg baseProfile="full" height="300" version="1.1" width="300" xmlns="http://www.w3.org/2000/svg" xmlns:ev="http://www.w3.org/2001/xml-events" xmlns:xlink="http://www.w3.org/1999/xlink"><defs /><rect fill="#FFFFFF" height="300" width="300" x="0" y="0" /><rect fill="#000000" height="10" width="10" x="0" y="0" /><rect fill="#000000" height="10" width="10" x="0" y="10" /><rect fill="#000000" height="10" width="10" x="0" y="20" /><rect fill="#000000" height="10" width="10" x="0" y="30" /><rect fill=... QRPreview.tsx:127:37
SVG length: 114394 QRPreview.tsx:128:37
SVG contains <svg>: true QRPreview.tsx:129:37
Raw SVG structure: 
Object { rectCount: 1853, pathCount: 0 }
QRPreview.tsx:133:37
SVG analysis: 
Object { hasRect: true, hasPath: false, hasCircle: false, hasBackground: true, hasForeground: true, totalRects: 1853, largeRects: 1, finderPatternsDetected: false, originalDesignOptions: {…}, designOptionsKeys: (21) […] }
QRPreview.tsx:163:37
Large rectangles (potential finder patterns): 
Array [ '<rect fill="#FFFFFF" height="300" width="300" x="0" y="0" />' ]
QRPreview.tsx:177:41
Original design options: 
Object { size: 256, margin: 10, pattern: "square", marker_border_style: "square", marker_border_color: "#000000", marker_center_style: "square", marker_center_color: "#000000", logo_size: 48, logo_position: "center", logo_opacity: 1, … }
QRPreview.tsx:183:37
Filtered design options for basic preview: 
Object { size: 256, margin: 10, error_correction: "M" }
QRPreview.tsx:184:37
Filtered options count: 3 QRPreview.tsx:185:37
SVG Styler: Parsing SVG, element count: 1854 svgStyler.ts:23:13
SVG Styler: Design options: 
Object { size: 256, margin: 10, error_correction: "M" }
svgStyler.ts:24:13
SVG Styler: Using default background color svgStyler.ts:48:17
SVG Styler: Using default foreground color svgStyler.ts:80:17
SVG Styler: Using default square pattern svgStyler.ts:97:17
SVG Styler: Final SVG length: 112540 svgStyler.ts:126:13
Styled SVG: <svg baseProfile="full" height="300" version="1.1" width="300" xmlns="http://www.w3.org/2000/svg" xmlns:ev="http://www.w3.org/2001/xml-events" xmlns:xlink="http://www.w3.org/1999/xlink"><defs/><rect f... QRPreview.tsx:193:41
[API] Response: 200 OK - POST /qr/preview/svg modernClient.ts:27:24
Successfully received SVG QR preview qrService.modern.ts:429:25
QRFormState: Initialized with 
Object { hasInitialData: true, mergedDataKeys: (7) […] }
useQRFormState.ts:26:21
QRFormState: Initialized with  
Object { hasInitialData: true, mergedDataKeys: (7) […] }
dashboard:1:596
QRFormState: First render, notifying parent of initial state useQRFormState.ts:150:25
QRDataForm (email): Received data update: 
Object { email: "<EMAIL>", subject: "Hello from QRVibe", body: "I scanned your QR code and wanted to reach out.", cc: "", bcc: "", title: "Email QR Code", description: "Scan to view email content" }
QRDataForm.tsx:2100:21
QRDataForm (email): Updated form data: 
Object { email: "<EMAIL>", subject: "Hello from QRVibe", body: "I scanned your QR code and wanted to reach out.", cc: "", bcc: "", title: "Email QR Code", description: "Scan to view email content" }
QRDataForm.tsx:2133:29
QRFormState: External initialData changed, updating form state useQRFormState.ts:166:25
QRFormState: External initialData changed, updating form state dashboard:1:596
QRDataForm (email): Updated form data: 
Object { email: "<EMAIL>", subject: "Hello from QRVibe", body: "I scanned your QR code and wanted to reach out.", cc: "", bcc: "", title: "Email QR Code", description: "Scan to view email content" }
QRDataForm.tsx:2133:29
QRFormState: External initialData changed, updating form state useQRFormState.ts:166:25
QRDataForm (email): Notifying parent of data change: 
Object { email: "<EMAIL>", subject: "Hello from QRVibe", body: "I scanned your QR code and wanted to reach out.", cc: "", bcc: "", title: "Email QR Code", description: "Scan to view email content" }
QRDataForm.tsx:2153:21
QR data updated: 
Object { email: "<EMAIL>", subject: "Hello from QRVibe", body: "I scanned your QR code and wanted to reach out.", cc: "", bcc: "", title: "Email QR Code", description: "Scan to view email content" }
page.tsx:462:21
QRPreview: Content key updated 
Object { qrType: "email", dataChanged: true, designChanged: true, logoChanged: false, useSvg: true }
QRPreview.tsx:51:21
QRPreview: Content key updated  
Object { qrType: "email", dataChanged: true, designChanged: true, logoChanged: false, useSvg: true }
dashboard:1:596
QRPreview: Generating basic SVG for frontend styling QRPreview.tsx:108:37
QRPreview: Verifying data for email 
Object { 0: "c", 1: "o", 2: "n", 3: "t", 4: "a", 5: "c", 6: "t", 7: "@", 8: "e", 9: "x", … }
QRPreview.tsx:260:17
QRPreview: Verified data 
Object { 0: "c", 1: "o", 2: "n", 3: "t", 4: "a", 5: "c", 6: "t", 7: "@", 8: "e", 9: "x", … }
QRPreview.tsx:364:17
Frontend QR preview SVG request: 
Object { qrType: "email", dataSize: 363, designOptions: (6) […], hasLogo: false, dimensions: 300 }
qrService.modern.ts:376:21
Cleaned content for SVG preview: 
Object { email: "<EMAIL>", subject: "Hello from QRVibe", body: "I scanned your QR code and wanted to reach out.", cc: "", bcc: "", title: "Email QR Code", description: "Scan to view email content" }
qrService.modern.ts:396:21
[API] Request: POST /qr/preview/svg modernClient.ts:27:24
[API] Added auth token to request modernClient.ts:27:24
Frontend QR preview SVG request: 
Object { qrType: "email", dataSize: 401, designOptions: (32) […], hasLogo: false, dimensions: 300 }
qrService.modern.ts:376:21
Cleaned content for SVG preview: 
Object { email: "<EMAIL>", subject: "Hello from QRVibe", body: "I scanned your QR code and wanted to reach out.", cc: "", bcc: "", title: "Email QR Code", description: "Scan to view email content", email_address: "<EMAIL>" }
qrService.modern.ts:396:21
[API] Request: POST /qr/preview/svg modernClient.ts:27:24
[API] Added auth token to request modernClient.ts:27:24
[API] Response: 200 OK - POST /qr/preview/svg modernClient.ts:27:24
Successfully received SVG QR preview qrService.modern.ts:429:25
Raw SVG from backend: <svg baseProfile="full" height="300" version="1.1" width="300" xmlns="http://www.w3.org/2000/svg" xmlns:ev="http://www.w3.org/2001/xml-events" xmlns:xlink="http://www.w3.org/1999/xlink"><defs /><rect fill="#FFFFFF" height="300" width="300" x="0" y="0" /><rect fill="#000000" height="10" width="10" x="0" y="0" /><rect fill="#000000" height="10" width="10" x="0" y="10" /><rect fill="#000000" height="10" width="10" x="0" y="20" /><rect fill="#000000" height="10" width="10" x="0" y="30" /><rect fill=... QRPreview.tsx:127:37
SVG length: 114394 QRPreview.tsx:128:37
SVG contains <svg>: true QRPreview.tsx:129:37
Raw SVG structure: 
Object { rectCount: 1853, pathCount: 0 }
QRPreview.tsx:133:37
SVG analysis: 
Object { hasRect: true, hasPath: false, hasCircle: false, hasBackground: true, hasForeground: true, totalRects: 1853, largeRects: 1, finderPatternsDetected: false, originalDesignOptions: {…}, designOptionsKeys: (21) […] }
QRPreview.tsx:163:37
Large rectangles (potential finder patterns): 
Array [ '<rect fill="#FFFFFF" height="300" width="300" x="0" y="0" />' ]
QRPreview.tsx:177:41
Original design options: 
Object { size: 256, margin: 10, pattern: "square", marker_border_style: "square", marker_border_color: "#000000", marker_center_style: "square", marker_center_color: "#000000", logo_size: 48, logo_position: "center", logo_opacity: 1, … }
QRPreview.tsx:183:37
Filtered design options for basic preview: 
Object { size: 256, margin: 10, error_correction: "M" }
QRPreview.tsx:184:37
Filtered options count: 3 QRPreview.tsx:185:37
SVG Styler: Parsing SVG, element count: 1854 svgStyler.ts:23:13
SVG Styler: Design options: 
Object { size: 256, margin: 10, error_correction: "M" }
svgStyler.ts:24:13
SVG Styler: Using default background color svgStyler.ts:48:17
SVG Styler: Using default foreground color svgStyler.ts:80:17
SVG Styler: Using default square pattern svgStyler.ts:97:17
SVG Styler: Final SVG length: 112540 svgStyler.ts:126:13
Styled SVG: <svg baseProfile="full" height="300" version="1.1" width="300" xmlns="http://www.w3.org/2000/svg" xmlns:ev="http://www.w3.org/2001/xml-events" xmlns:xlink="http://www.w3.org/1999/xlink"><defs/><rect f... QRPreview.tsx:193:41
[API] Response: 200 OK - POST /qr/preview/svg modernClient.ts:27:24
Successfully received SVG QR preview qrService.modern.ts:429:25
[Auth] Adding Bearer token to request: Request 2 layout.tsx:133:49
Frontend QR preview SVG request: 
Object { qrType: "url", dataSize: 160, designOptions: (17) […], hasLogo: false, dimensions: 300 }
qrService.modern.ts:376:21
Cleaned content for SVG preview: 
Object { url: "https://example.com", utm_source: "", utm_medium: "", utm_campaign: "", short_url: false, title: "Url QR Code", description: "Scan to view url content" }
qrService.modern.ts:396:21
[API] Request: POST /qr/preview/svg modernClient.ts:27:24
[API] Added auth token to request modernClient.ts:27:24
[API] Response: 200 OK - POST /qr/preview/svg modernClient.ts:27:24
Successfully received SVG QR preview qrService.modern.ts:429:25
Frontend QR preview SVG request: 
Object { qrType: "scooter_unlock", dataSize: 135, designOptions: (17) […], hasLogo: false, dimensions: 300 }
qrService.modern.ts:376:21
Cleaned content for SVG preview: 
Object { scooter_id: "SCOOT123", unlock_code: "UNLOCK42", title: "Scooter_unlock QR Code", description: "Scan to view scooter_unlock content" }
qrService.modern.ts:396:21
[API] Request: POST /qr/preview/svg modernClient.ts:27:24
[API] Added auth token to request modernClient.ts:27:24
[API] Response: 200 OK - POST /qr/preview/svg modernClient.ts:27:24
Successfully received SVG QR preview qrService.modern.ts:429:25
[Auth] Adding Bearer token to request: /health layout.tsx:133:49
QRPreview: Content key updated 
Object { qrType: "scooter_unlock", dataChanged: true, designChanged: true, logoChanged: false, useSvg: true }
QRPreview.tsx:51:21
QRPreview: Content key updated  
Object { qrType: "scooter_unlock", dataChanged: true, designChanged: true, logoChanged: false, useSvg: true }
dashboard:1:596
QRDataForm: initializing scooter_unlock with provided data + defaults: 
Object { scooter_id: "SCOOT123", unlock_code: "UNLOCK42", title: "Scooter_unlock QR Code", description: "Scan to view scooter_unlock content" }
QRDataForm.tsx:2072:33
QR data updated: 
Object { scooter_id: "SCOOT123", unlock_code: "UNLOCK42", title: "Scooter_unlock QR Code", description: "Scan to view scooter_unlock content" }
page.tsx:462:21
QRDataForm: initializing scooter_unlock with provided data + defaults:  
Object { scooter_id: "SCOOT123", unlock_code: "UNLOCK42", title: "Scooter_unlock QR Code", description: "Scan to view scooter_unlock content" }
dashboard:1:596
QR data updated:  
Object { scooter_id: "SCOOT123", unlock_code: "UNLOCK42", title: "Scooter_unlock QR Code", description: "Scan to view scooter_unlock content" }
dashboard:1:596
QRPreview: Content key updated 
Object { qrType: "scooter_unlock", dataChanged: true, designChanged: true, logoChanged: false, useSvg: true }
QRPreview.tsx:51:21
QRPreview: Content key updated  
Object { qrType: "scooter_unlock", dataChanged: true, designChanged: true, logoChanged: false, useSvg: true }
dashboard:1:596
QRDataForm (scooter_unlock): Notifying parent of data change: 
Object { scooter_id: "SCOOT123", unlock_code: "UNLOCK42", title: "Scooter_unlock QR Code", description: "Scan to view scooter_unlock content" }
QRDataForm.tsx:2153:21
QR data updated: 
Object { scooter_id: "SCOOT123", unlock_code: "UNLOCK42", title: "Scooter_unlock QR Code", description: "Scan to view scooter_unlock content" }
page.tsx:462:21
QRFormState: Initialized with 
Object { hasInitialData: true, mergedDataKeys: (4) […] }
useQRFormState.ts:26:21
QRFormState: Initialized with  
Object { hasInitialData: true, mergedDataKeys: (4) […] }
dashboard:1:596
QRPreview: Content key updated 
Object { qrType: "scooter_unlock", dataChanged: true, designChanged: true, logoChanged: false, useSvg: true }
QRPreview.tsx:51:21
QRPreview: Content key updated  
Object { qrType: "scooter_unlock", dataChanged: true, designChanged: true, logoChanged: false, useSvg: true }
dashboard:1:596
QRFormState: First render, notifying parent of initial state useQRFormState.ts:150:25
QRDataForm (scooter_unlock): Received data update: 
Object { scooter_id: "SCOOT123", unlock_code: "UNLOCK42", title: "Scooter_unlock QR Code", description: "Scan to view scooter_unlock content" }
QRDataForm.tsx:2100:21
QRDataForm (scooter_unlock): Updated form data: 
Object { scooter_id: "SCOOT123", unlock_code: "UNLOCK42", title: "Scooter_unlock QR Code", description: "Scan to view scooter_unlock content" }
QRDataForm.tsx:2133:29
QRFormState: External initialData changed, updating form state useQRFormState.ts:166:25
QRFormState: External initialData changed, updating form state dashboard:1:596
QRDataForm (scooter_unlock): Updated form data: 
Object { scooter_id: "SCOOT123", unlock_code: "UNLOCK42", title: "Scooter_unlock QR Code", description: "Scan to view scooter_unlock content" }
QRDataForm.tsx:2133:29
QRFormState: External initialData changed, updating form state useQRFormState.ts:166:25
QRDataForm (scooter_unlock): Notifying parent of data change: 
Object { scooter_id: "SCOOT123", unlock_code: "UNLOCK42", title: "Scooter_unlock QR Code", description: "Scan to view scooter_unlock content" }
QRDataForm.tsx:2153:21
QR data updated: 
Object { scooter_id: "SCOOT123", unlock_code: "UNLOCK42", title: "Scooter_unlock QR Code", description: "Scan to view scooter_unlock content" }
page.tsx:462:21
QRPreview: Content key updated 
Object { qrType: "scooter_unlock", dataChanged: true, designChanged: true, logoChanged: false, useSvg: true }
QRPreview.tsx:51:21
QRPreview: Content key updated  
Object { qrType: "scooter_unlock", dataChanged: true, designChanged: true, logoChanged: false, useSvg: true }
dashboard:1:596
QRPreview: Generating basic SVG for frontend styling QRPreview.tsx:108:37
QRPreview: Verifying data for scooter_unlock 
Object { scooter_id: "SCOOT123", unlock_code: "UNLOCK42", title: "Scooter_unlock QR Code", description: "Scan to view scooter_unlock content" }
QRPreview.tsx:260:17
QRPreview: Verified data 
Object { scooter_id: "SCOOT123", unlock_code: "UNLOCK42", title: "Scooter_unlock QR Code", description: "Scan to view scooter_unlock content" }
QRPreview.tsx:364:17
Frontend QR preview SVG request: 
Object { qrType: "scooter_unlock", dataSize: 135, designOptions: (6) […], hasLogo: false, dimensions: 300 }
qrService.modern.ts:376:21
Cleaned content for SVG preview: 
Object { scooter_id: "SCOOT123", unlock_code: "UNLOCK42", title: "Scooter_unlock QR Code", description: "Scan to view scooter_unlock content" }
qrService.modern.ts:396:21
[API] Request: POST /qr/preview/svg modernClient.ts:27:24
[API] Added auth token to request modernClient.ts:27:24
Frontend QR preview SVG request: 
Object { qrType: "scooter_unlock", dataSize: 135, designOptions: (32) […], hasLogo: false, dimensions: 300 }
qrService.modern.ts:376:21
Cleaned content for SVG preview: 
Object { scooter_id: "SCOOT123", unlock_code: "UNLOCK42", title: "Scooter_unlock QR Code", description: "Scan to view scooter_unlock content" }
qrService.modern.ts:396:21
[API] Request: POST /qr/preview/svg modernClient.ts:27:24
[API] Added auth token to request modernClient.ts:27:24
[API] Response: 200 OK - POST /qr/preview/svg modernClient.ts:27:24
Successfully received SVG QR preview qrService.modern.ts:429:25
Raw SVG from backend: <svg baseProfile="full" height="300" version="1.1" width="300" xmlns="http://www.w3.org/2000/svg" xmlns:ev="http://www.w3.org/2001/xml-events" xmlns:xlink="http://www.w3.org/1999/xlink"><defs /><rect fill="#FFFFFF" height="300" width="300" x="0" y="0" /><rect fill="#000000" height="10" width="10" x="0" y="0" /><rect fill="#000000" height="10" width="10" x="0" y="10" /><rect fill="#000000" height="10" width="10" x="0" y="20" /><rect fill="#000000" height="10" width="10" x="0" y="30" /><rect fill=... QRPreview.tsx:127:37
SVG length: 75089 QRPreview.tsx:128:37
SVG contains <svg>: true QRPreview.tsx:129:37
Raw SVG structure: 
Object { rectCount: 1217, pathCount: 0 }
QRPreview.tsx:133:37
SVG analysis: 
Object { hasRect: true, hasPath: false, hasCircle: false, hasBackground: true, hasForeground: true, totalRects: 1217, largeRects: 1, finderPatternsDetected: false, originalDesignOptions: {…}, designOptionsKeys: (21) […] }
QRPreview.tsx:163:37
Large rectangles (potential finder patterns): 
Array [ '<rect fill="#FFFFFF" height="300" width="300" x="0" y="0" />' ]
QRPreview.tsx:177:41
Original design options: 
Object { size: 256, margin: 10, pattern: "square", marker_border_style: "square", marker_border_color: "#000000", marker_center_style: "square", marker_center_color: "#000000", logo_size: 48, logo_position: "center", logo_opacity: 1, … }
QRPreview.tsx:183:37
Filtered design options for basic preview: 
Object { size: 256, margin: 10, error_correction: "M" }
QRPreview.tsx:184:37
Filtered options count: 3 QRPreview.tsx:185:37
SVG Styler: Parsing SVG, element count: 1218 svgStyler.ts:23:13
SVG Styler: Design options: 
Object { size: 256, margin: 10, error_correction: "M" }
svgStyler.ts:24:13
SVG Styler: Using default background color svgStyler.ts:48:17
SVG Styler: Using default foreground color svgStyler.ts:80:17
SVG Styler: Using default square pattern svgStyler.ts:97:17
SVG Styler: Final SVG length: 73871 svgStyler.ts:126:13
Styled SVG: <svg baseProfile="full" height="300" version="1.1" width="300" xmlns="http://www.w3.org/2000/svg" xmlns:ev="http://www.w3.org/2001/xml-events" xmlns:xlink="http://www.w3.org/1999/xlink"><defs/><rect f... QRPreview.tsx:193:41
[API] Response: 200 OK - POST /qr/preview/svg modernClient.ts:27:24
Successfully received SVG QR preview qrService.modern.ts:429:25
QRFormState: Field updated 
Object { field: "service_provider", previousValue: undefined, newValue: "l" }
useQRFormState.ts:66:29
QRFormState: Field updated 
Object { field: "service_provider", previousValue: undefined, newValue: "l" }
useQRFormState.ts:66:29
A component is changing an uncontrolled input to be controlled. This is likely caused by the value changing from undefined to a defined value, which should not happen. Decide between using a controlled or uncontrolled input element for the lifetime of the component. More info: https://react.dev/link/controlled-components intercept-console-error.js:50:32
[Auth] Adding Bearer token to request: /__nextjs_original-stack-frames layout.tsx:133:49
QRFormState: Field updated 
Object { field: "service_provider", previousValue: "l", newValue: "li" }
useQRFormState.ts:66:29
QRFormState: Field updated 
Object { field: "service_provider", previousValue: "l", newValue: "li" }
useQRFormState.ts:66:29
QRFormState: Field updated 
Object { field: "service_provider", previousValue: "li", newValue: "lim" }
useQRFormState.ts:66:29
QRFormState: Field updated 
Object { field: "service_provider", previousValue: "li", newValue: "lim" }
useQRFormState.ts:66:29
QRFormState: Notifying parent of changes (debounced) useQRFormState.ts:39:29
QRDataForm (scooter_unlock): Received data update: 
Object { scooter_id: "SCOOT123", unlock_code: "UNLOCK42", title: "Scooter_unlock QR Code", description: "Scan to view scooter_unlock content", service_provider: "lim" }
QRDataForm.tsx:2100:21
QRDataForm (scooter_unlock): Updated form data: 
Object { scooter_id: "SCOOT123", unlock_code: "UNLOCK42", title: "Scooter_unlock QR Code", description: "Scan to view scooter_unlock content", service_provider: "lim" }
QRDataForm.tsx:2133:29
QRDataForm (scooter_unlock): Updated form data: 
Object { scooter_id: "SCOOT123", unlock_code: "UNLOCK42", title: "Scooter_unlock QR Code", description: "Scan to view scooter_unlock content", service_provider: "lim" }
QRDataForm.tsx:2133:29
QRFormState: Initialized with 
Object { hasInitialData: true, mergedDataKeys: (5) […] }
useQRFormState.ts:26:21
QRFormState: Initialized with  
Object { hasInitialData: true, mergedDataKeys: (5) […] }
dashboard:1:596
QRFormState: First render, notifying parent of initial state useQRFormState.ts:150:25
QRDataForm (scooter_unlock): Received data update: 
Object { scooter_id: "SCOOT123", unlock_code: "UNLOCK42", title: "Scooter_unlock QR Code", description: "Scan to view scooter_unlock content", service_provider: "lim" }
QRDataForm.tsx:2100:21
QRFormState: External initialData changed, updating form state useQRFormState.ts:166:25
QRDataForm (scooter_unlock): Notifying parent of data change: 
Object { scooter_id: "SCOOT123", unlock_code: "UNLOCK42", title: "Scooter_unlock QR Code", description: "Scan to view scooter_unlock content", service_provider: "lim" }
QRDataForm.tsx:2153:21
QR data updated: 
Object { scooter_id: "SCOOT123", unlock_code: "UNLOCK42", title: "Scooter_unlock QR Code", description: "Scan to view scooter_unlock content", service_provider: "lim" }
page.tsx:462:21
QRFormState: External initialData changed, updating form state dashboard:1:596
QRDataForm (scooter_unlock): Updated form data: 
Object { scooter_id: "SCOOT123", unlock_code: "UNLOCK42", title: "Scooter_unlock QR Code", description: "Scan to view scooter_unlock content", service_provider: "lim" }
QRDataForm.tsx:2133:29
QRDataForm (scooter_unlock): Updated form data: 
Object { scooter_id: "SCOOT123", unlock_code: "UNLOCK42", title: "Scooter_unlock QR Code", description: "Scan to view scooter_unlock content", service_provider: "lim" }
QRDataForm.tsx:2133:29
QRPreview: Content key updated 
Object { qrType: "scooter_unlock", dataChanged: true, designChanged: true, logoChanged: false, useSvg: true }
QRPreview.tsx:51:21
QRPreview: Content key updated  
Object { qrType: "scooter_unlock", dataChanged: true, designChanged: true, logoChanged: false, useSvg: true }
dashboard:1:596
QRFormState: External initialData changed, updating form state useQRFormState.ts:166:25
QRDataForm (scooter_unlock): Notifying parent of data change: 
Object { scooter_id: "SCOOT123", unlock_code: "UNLOCK42", title: "Scooter_unlock QR Code", description: "Scan to view scooter_unlock content", service_provider: "lim" }
QRDataForm.tsx:2153:21
QR data updated: 
Object { scooter_id: "SCOOT123", unlock_code: "UNLOCK42", title: "Scooter_unlock QR Code", description: "Scan to view scooter_unlock content", service_provider: "lim" }
page.tsx:462:21
QRPreview: Content key updated 
Object { qrType: "scooter_unlock", dataChanged: true, designChanged: true, logoChanged: false, useSvg: true }
QRPreview.tsx:51:21
QRPreview: Content key updated  
Object { qrType: "scooter_unlock", dataChanged: true, designChanged: true, logoChanged: false, useSvg: true }
dashboard:1:596
QRPreview: Generating basic SVG for frontend styling QRPreview.tsx:108:37
QRPreview: Verifying data for scooter_unlock 
Object { scooter_id: "SCOOT123", unlock_code: "UNLOCK42", title: "Scooter_unlock QR Code", description: "Scan to view scooter_unlock content", service_provider: "lim" }
QRPreview.tsx:260:17
QRPreview: Verified data 
Object { scooter_id: "SCOOT123", unlock_code: "UNLOCK42", title: "Scooter_unlock QR Code", description: "Scan to view scooter_unlock content", service_provider: "lim" }
QRPreview.tsx:364:17
Frontend QR preview SVG request: 
Object { qrType: "scooter_unlock", dataSize: 160, designOptions: (6) […], hasLogo: false, dimensions: 300 }
qrService.modern.ts:376:21
Cleaned content for SVG preview: 
Object { scooter_id: "SCOOT123", unlock_code: "UNLOCK42", title: "Scooter_unlock QR Code", description: "Scan to view scooter_unlock content", service_provider: "lim" }
qrService.modern.ts:396:21
[API] Request: POST /qr/preview/svg modernClient.ts:27:24
[API] Added auth token to request modernClient.ts:27:24
Frontend QR preview SVG request: 
Object { qrType: "scooter_unlock", dataSize: 160, designOptions: (32) […], hasLogo: false, dimensions: 300 }
qrService.modern.ts:376:21
Cleaned content for SVG preview: 
Object { scooter_id: "SCOOT123", unlock_code: "UNLOCK42", title: "Scooter_unlock QR Code", description: "Scan to view scooter_unlock content", service_provider: "lim" }
qrService.modern.ts:396:21
[API] Request: POST /qr/preview/svg modernClient.ts:27:24
[API] Added auth token to request modernClient.ts:27:24
[API] Response: 200 OK - POST /qr/preview/svg modernClient.ts:27:24
Successfully received SVG QR preview qrService.modern.ts:429:25
[API] Response: 200 OK - POST /qr/preview/svg modernClient.ts:27:24
Successfully received SVG QR preview qrService.modern.ts:429:25
Raw SVG from backend: <svg baseProfile="full" height="300" version="1.1" width="300" xmlns="http://www.w3.org/2000/svg" xmlns:ev="http://www.w3.org/2001/xml-events" xmlns:xlink="http://www.w3.org/1999/xlink"><defs /><rect fill="#FFFFFF" height="300" width="300" x="0" y="0" /><rect fill="#000000" height="10" width="10" x="0" y="0" /><rect fill="#000000" height="10" width="10" x="0" y="10" /><rect fill="#000000" height="10" width="10" x="0" y="20" /><rect fill="#000000" height="10" width="10" x="0" y="30" /><rect fill=... QRPreview.tsx:127:37
SVG length: 86584 QRPreview.tsx:128:37
SVG contains <svg>: true QRPreview.tsx:129:37
Raw SVG structure: 
Object { rectCount: 1403, pathCount: 0 }
QRPreview.tsx:133:37
SVG analysis: 
Object { hasRect: true, hasPath: false, hasCircle: false, hasBackground: true, hasForeground: true, totalRects: 1403, largeRects: 1, finderPatternsDetected: false, originalDesignOptions: {…}, designOptionsKeys: (21) […] }
QRPreview.tsx:163:37
Large rectangles (potential finder patterns): 
Array [ '<rect fill="#FFFFFF" height="300" width="300" x="0" y="0" />' ]
QRPreview.tsx:177:41
Original design options: 
Object { size: 256, margin: 10, pattern: "square", marker_border_style: "square", marker_border_color: "#000000", marker_center_style: "square", marker_center_color: "#000000", logo_size: 48, logo_position: "center", logo_opacity: 1, … }
QRPreview.tsx:183:37
Filtered design options for basic preview: 
Object { size: 256, margin: 10, error_correction: "M" }
QRPreview.tsx:184:37
Filtered options count: 3 QRPreview.tsx:185:37
SVG Styler: Parsing SVG, element count: 1404 svgStyler.ts:23:13
SVG Styler: Design options: 
Object { size: 256, margin: 10, error_correction: "M" }
svgStyler.ts:24:13
SVG Styler: Using default background color svgStyler.ts:48:17
SVG Styler: Using default foreground color svgStyler.ts:80:17
SVG Styler: Using default square pattern svgStyler.ts:97:17
SVG Styler: Final SVG length: 85180 svgStyler.ts:126:13
Styled SVG: <svg baseProfile="full" height="300" version="1.1" width="300" xmlns="http://www.w3.org/2000/svg" xmlns:ev="http://www.w3.org/2001/xml-events" xmlns:xlink="http://www.w3.org/1999/xlink"><defs/><rect f... QRPreview.tsx:193:41
QRPreview: Content key updated 
Object { qrType: "scooter_unlock", dataChanged: true, designChanged: true, logoChanged: false, useSvg: true }
QRPreview.tsx:51:21
QRPreview: Content key updated  
Object { qrType: "scooter_unlock", dataChanged: true, designChanged: true, logoChanged: false, useSvg: true }
dashboard:1:596
QRPreview: Generating basic SVG for frontend styling QRPreview.tsx:108:37
QRPreview: Verifying data for scooter_unlock 
Object { scooter_id: "SCOOT123", unlock_code: "UNLOCK42", title: "Scooter_unlock QR Code", description: "Scan to view scooter_unlock content", service_provider: "lim" }
QRPreview.tsx:260:17
QRPreview: Verified data 
Object { scooter_id: "SCOOT123", unlock_code: "UNLOCK42", title: "Scooter_unlock QR Code", description: "Scan to view scooter_unlock content", service_provider: "lim" }
QRPreview.tsx:364:17
Frontend QR preview SVG request: 
Object { qrType: "scooter_unlock", dataSize: 160, designOptions: (6) […], hasLogo: false, dimensions: 300 }
qrService.modern.ts:376:21
Cleaned content for SVG preview: 
Object { scooter_id: "SCOOT123", unlock_code: "UNLOCK42", title: "Scooter_unlock QR Code", description: "Scan to view scooter_unlock content", service_provider: "lim" }
qrService.modern.ts:396:21
[API] Request: POST /qr/preview/svg modernClient.ts:27:24
[API] Added auth token to request modernClient.ts:27:24
[API] Response: 200 OK - POST /qr/preview/svg modernClient.ts:27:24
Successfully received SVG QR preview qrService.modern.ts:429:25
Raw SVG from backend: <svg baseProfile="full" height="300" version="1.1" width="300" xmlns="http://www.w3.org/2000/svg" xmlns:ev="http://www.w3.org/2001/xml-events" xmlns:xlink="http://www.w3.org/1999/xlink"><defs /><rect fill="#FFFFFF" height="300" width="300" x="0" y="0" /><rect fill="#000000" height="10" width="10" x="0" y="0" /><rect fill="#000000" height="10" width="10" x="0" y="10" /><rect fill="#000000" height="10" width="10" x="0" y="20" /><rect fill="#000000" height="10" width="10" x="0" y="30" /><rect fill=... QRPreview.tsx:127:37
SVG length: 86584 QRPreview.tsx:128:37
SVG contains <svg>: true QRPreview.tsx:129:37
Raw SVG structure: 
Object { rectCount: 1403, pathCount: 0 }
QRPreview.tsx:133:37
SVG analysis: 
Object { hasRect: true, hasPath: false, hasCircle: false, hasBackground: true, hasForeground: true, totalRects: 1403, largeRects: 1, finderPatternsDetected: false, originalDesignOptions: {…}, designOptionsKeys: (21) […] }
QRPreview.tsx:163:37
Large rectangles (potential finder patterns): 
Array [ '<rect fill="#FFFFFF" height="300" width="300" x="0" y="0" />' ]
QRPreview.tsx:177:41
Original design options: 
Object { size: 256, margin: 10, pattern: "square", marker_border_style: "square", marker_border_color: "#000000", marker_center_style: "square", marker_center_color: "#000000", logo_size: 48, logo_position: "center", logo_opacity: 1, … }
QRPreview.tsx:183:37
Filtered design options for basic preview: 
Object { size: 256, margin: 10, error_correction: "M" }
QRPreview.tsx:184:37
Filtered options count: 3 QRPreview.tsx:185:37
SVG Styler: Parsing SVG, element count: 1404 svgStyler.ts:23:13
SVG Styler: Design options: 
Object { size: 256, margin: 10, error_correction: "M" }
svgStyler.ts:24:13
SVG Styler: Using default background color svgStyler.ts:48:17
SVG Styler: Using default foreground color svgStyler.ts:80:17
SVG Styler: Using default square pattern svgStyler.ts:97:17
SVG Styler: Final SVG length: 85180 svgStyler.ts:126:13
Styled SVG: <svg baseProfile="full" height="300" version="1.1" width="300" xmlns="http://www.w3.org/2000/svg" xmlns:ev="http://www.w3.org/2001/xml-events" xmlns:xlink="http://www.w3.org/1999/xlink"><defs/><rect f... QRPreview.tsx:193:41
[Auth] Adding Bearer token to request: /health layout.tsx:133:49
[Auth] Health check successful context.tsx:123:37
[Auth] Adding Bearer token to request: /health 4 layout.tsx:133:49
[Auth] Health check successful context.tsx:123:37
