MouseEvent.mozInputSource is deprecated. Use PointerEvent.pointerType instead. layout.js line 2405 > eval:22:9
[Auth] Adding Bear<PERSON> token to request: Request layout.tsx:133:49
Frontend QR preview SVG request: 
Object { qrType: "url", dataSize: 160, designOptions: (17) […], hasLogo: false, dimensions: 300 }
qrService.modern.ts:376:21
Cleaned content for SVG preview: 
Object { url: "https://example.com", utm_source: "", utm_medium: "", utm_campaign: "", short_url: false, title: "Url QR Code", description: "Scan to view url content" }
qrService.modern.ts:396:21
[API] Request: POST /qr/preview/svg modernClient.ts:27:24
[API] Added auth token to request modernClient.ts:27:24
[API] Response: 200 OK - POST /qr/preview/svg modernClient.ts:27:24
Successfully received SVG QR preview qrService.modern.ts:429:25
Frontend QR preview SVG request: 
Object { qrType: "scooter_unlock", dataSize: 135, designOptions: (17) […], hasLogo: false, dimensions: 300 }
qrService.modern.ts:376:21
Cleaned content for SVG preview: 
Object { scooter_id: "SCOOT123", unlock_code: "UNLOCK42", title: "Scooter_unlock QR Code", description: "Scan to view scooter_unlock content" }
qrService.modern.ts:396:21
[API] Request: POST /qr/preview/svg modernClient.ts:27:24
[API] Added auth token to request modernClient.ts:27:24
[API] Response: 200 OK - POST /qr/preview/svg modernClient.ts:27:24
Successfully received SVG QR preview qrService.modern.ts:429:25
QRPreview: Content key updated 
Object { qrType: "scooter_unlock", dataChanged: true, designChanged: true, logoChanged: false, useSvg: true }
QRPreview.tsx:51:21
QRPreview: Content key updated  
Object { qrType: "scooter_unlock", dataChanged: true, designChanged: true, logoChanged: false, useSvg: true }
dashboard:1:596
QRDataForm: initializing scooter_unlock with provided data + defaults: 
Object { scooter_id: "SCOOT123", unlock_code: "UNLOCK42", title: "Scooter_unlock QR Code", description: "Scan to view scooter_unlock content" }
QRDataForm.tsx:2072:33
QR data updated: 
Object { scooter_id: "SCOOT123", unlock_code: "UNLOCK42", title: "Scooter_unlock QR Code", description: "Scan to view scooter_unlock content" }
page.tsx:462:21
QRDataForm: initializing scooter_unlock with provided data + defaults:  
Object { scooter_id: "SCOOT123", unlock_code: "UNLOCK42", title: "Scooter_unlock QR Code", description: "Scan to view scooter_unlock content" }
dashboard:1:596
QR data updated:  
Object { scooter_id: "SCOOT123", unlock_code: "UNLOCK42", title: "Scooter_unlock QR Code", description: "Scan to view scooter_unlock content" }
dashboard:1:596
QRPreview: Content key updated 
Object { qrType: "scooter_unlock", dataChanged: true, designChanged: true, logoChanged: false, useSvg: true }
QRPreview.tsx:51:21
QRPreview: Content key updated  
Object { qrType: "scooter_unlock", dataChanged: true, designChanged: true, logoChanged: false, useSvg: true }
dashboard:1:596
QRDataForm (scooter_unlock): Notifying parent of data change: 
Object { scooter_id: "SCOOT123", unlock_code: "UNLOCK42", title: "Scooter_unlock QR Code", description: "Scan to view scooter_unlock content" }
QRDataForm.tsx:2153:21
QR data updated: 
Object { scooter_id: "SCOOT123", unlock_code: "UNLOCK42", title: "Scooter_unlock QR Code", description: "Scan to view scooter_unlock content" }
page.tsx:462:21
QRPreview: Content key updated 
Object { qrType: "scooter_unlock", dataChanged: true, designChanged: true, logoChanged: false, useSvg: true }
QRPreview.tsx:51:21
QRPreview: Content key updated  
Object { qrType: "scooter_unlock", dataChanged: true, designChanged: true, logoChanged: false, useSvg: true }
dashboard:1:596
QRPreview: Generating basic SVG for frontend styling QRPreview.tsx:108:37
QRPreview: Verifying data for scooter_unlock 
Object { scooter_id: "SCOOT123", unlock_code: "UNLOCK42", title: "Scooter_unlock QR Code", description: "Scan to view scooter_unlock content" }
QRPreview.tsx:260:17
QRPreview: Verified data 
Object { scooter_id: "SCOOT123", unlock_code: "UNLOCK42", title: "Scooter_unlock QR Code", description: "Scan to view scooter_unlock content" }
QRPreview.tsx:364:17
Frontend QR preview SVG request: 
Object { qrType: "scooter_unlock", dataSize: 135, designOptions: (6) […], hasLogo: false, dimensions: 300 }
qrService.modern.ts:376:21
Cleaned content for SVG preview: 
Object { scooter_id: "SCOOT123", unlock_code: "UNLOCK42", title: "Scooter_unlock QR Code", description: "Scan to view scooter_unlock content" }
qrService.modern.ts:396:21
[API] Request: POST /qr/preview/svg modernClient.ts:27:24
[API] Added auth token to request modernClient.ts:27:24
Frontend QR preview SVG request: 
Object { qrType: "scooter_unlock", dataSize: 135, designOptions: (32) […], hasLogo: false, dimensions: 300 }
qrService.modern.ts:376:21
Cleaned content for SVG preview: 
Object { scooter_id: "SCOOT123", unlock_code: "UNLOCK42", title: "Scooter_unlock QR Code", description: "Scan to view scooter_unlock content" }
qrService.modern.ts:396:21
[API] Request: POST /qr/preview/svg modernClient.ts:27:24
[API] Added auth token to request modernClient.ts:27:24
QRFormState: Initialized with 
Object { hasInitialData: true, mergedDataKeys: (4) […] }
useQRFormState.ts:26:21
QRFormState: Initialized with  
Object { hasInitialData: true, mergedDataKeys: (4) […] }
dashboard:1:596
QRFormState: First render, notifying parent of initial state useQRFormState.ts:150:25
QRDataForm (scooter_unlock): Received data update: 
Object { scooter_id: "SCOOT123", unlock_code: "UNLOCK42", title: "Scooter_unlock QR Code", description: "Scan to view scooter_unlock content" }
QRDataForm.tsx:2100:21
QRDataForm (scooter_unlock): Updated form data: 
Object { scooter_id: "SCOOT123", unlock_code: "UNLOCK42", title: "Scooter_unlock QR Code", description: "Scan to view scooter_unlock content" }
QRDataForm.tsx:2133:29
QRFormState: External initialData changed, updating form state useQRFormState.ts:166:25
QRFormState: External initialData changed, updating form state dashboard:1:596
QRDataForm (scooter_unlock): Updated form data: 
Object { scooter_id: "SCOOT123", unlock_code: "UNLOCK42", title: "Scooter_unlock QR Code", description: "Scan to view scooter_unlock content" }
QRDataForm.tsx:2133:29
QRFormState: External initialData changed, updating form state useQRFormState.ts:166:25
QRDataForm (scooter_unlock): Notifying parent of data change: 
Object { scooter_id: "SCOOT123", unlock_code: "UNLOCK42", title: "Scooter_unlock QR Code", description: "Scan to view scooter_unlock content" }
QRDataForm.tsx:2153:21
QR data updated: 
Object { scooter_id: "SCOOT123", unlock_code: "UNLOCK42", title: "Scooter_unlock QR Code", description: "Scan to view scooter_unlock content" }
page.tsx:462:21
QRPreview: Content key updated 
Object { qrType: "scooter_unlock", dataChanged: true, designChanged: true, logoChanged: false, useSvg: true }
QRPreview.tsx:51:21
QRPreview: Content key updated  
Object { qrType: "scooter_unlock", dataChanged: true, designChanged: true, logoChanged: false, useSvg: true }
dashboard:1:596
[API] Response: 200 OK - POST /qr/preview/svg modernClient.ts:27:24
Successfully received SVG QR preview qrService.modern.ts:429:25
Raw SVG from backend: <svg baseProfile="full" height="300" version="1.1" width="300" xmlns="http://www.w3.org/2000/svg" xmlns:ev="http://www.w3.org/2001/xml-events" xmlns:xlink="http://www.w3.org/1999/xlink"><defs /><rect class="qr-bg" fill="#FFFFFF" height="300" id="qr-bg" width="300" x="0" y="0" /><rect class="qr-module" fill="#000000" height="5.918367346938775" id="module-8-0" width="5.918367346938775" x="52.3469387755102" y="5.0" /><rect class="qr-module" fill="#000000" height="5.918367346938775" id="module-9-0" ... QRPreview.tsx:127:37
SVG length: 176046 QRPreview.tsx:128:37
SVG contains <svg>: true QRPreview.tsx:129:37
Raw SVG structure: 
Object { rectCount: 1127, pathCount: 0 }
QRPreview.tsx:133:37
SVG analysis: 
Object { hasRect: true, hasPath: false, hasCircle: false, hasBackground: true, hasForeground: true, totalRects: 1127, largeRects: 7, finderPatternsDetected: true, originalDesignOptions: {…}, designOptionsKeys: (21) […] }
QRPreview.tsx:163:37
Large rectangles (potential finder patterns): 
Array(5) [ '<rect class="qr-bg" fill="#FFFFFF" height="300" id="qr-bg" width="300" x="0" y="0" />', '<rect class="qr-marker-outer" fill="#000000" height="41.42857142857143" width="41.42857142857143" x="5.0" y="5.0" />', '<rect class="qr-marker-inner" fill="#fff" height="27.224489795918366" width="27.224489795918366" x="12.10204081632653" y="12.10204081632653" />', '<rect class="qr-marker-outer" fill="#000000" height="41.42857142857143" width="41.42857142857143" x="253.57142857142856" y="5.0" />', '<rect class="qr-marker-inner" fill="#fff" height="27.224489795918366" width="27.224489795918366" x="260.67346938775506" y="12.10204081632653" />' ]
QRPreview.tsx:177:41
Original design options: 
Object { size: 256, margin: 10, pattern: "square", marker_border_style: "square", marker_border_color: "#000000", marker_center_style: "square", marker_center_color: "#000000", logo_size: 48, logo_position: "center", logo_opacity: 1, … }
QRPreview.tsx:183:37
Filtered design options for basic preview: 
Object { size: 256, margin: 10, error_correction: "M" }
QRPreview.tsx:184:37
Filtered options count: 3 QRPreview.tsx:185:37
SVG Styler: Parsing SVG, element count: 1122 svgStyler.ts:23:13
SVG Styler: Design options: 
Object { size: 256, margin: 10, error_correction: "M" }
svgStyler.ts:24:13
SVG Styler: Using default background color svgStyler.ts:48:17
SVG Styler: Using default foreground color svgStyler.ts:80:17
SVG Styler: Using default square pattern svgStyler.ts:97:17
SVG Styler: Running finder pattern detection... svgStyler.ts:100:13
Finder pattern detection - backend classes found: 
Object { markerOuters: 3, markerInners: 3, markerDots: 3 }
svgStyler.ts:232:13
Backend finder pattern 1: 
Object { outer: {…}, inner: {…}, dot: {…} }
svgStyler.ts:260:21
Backend finder pattern 2: 
Object { outer: {…}, inner: {…}, dot: {…} }
svgStyler.ts:260:21
Backend finder pattern 3: 
Object { outer: {…}, inner: {…}, dot: {…} }
svgStyler.ts:260:21
Finder pattern detection summary: 
Object { patternsFound: 3, method: "backend-classes" }
svgStyler.ts:343:13
SVG Styler: Finder patterns detected: 3 svgStyler.ts:102:13
SVG Styler: Final SVG length: 174918 svgStyler.ts:129:13
Styled SVG: <svg baseProfile="full" height="300" version="1.1" width="300" xmlns="http://www.w3.org/2000/svg" xmlns:ev="http://www.w3.org/2001/xml-events" xmlns:xlink="http://www.w3.org/1999/xlink"><defs/><rect c... QRPreview.tsx:193:41
[API] Response: 200 OK - POST /qr/preview/svg modernClient.ts:27:24
Successfully received SVG QR preview qrService.modern.ts:429:25
QRPreview: Generating basic SVG for frontend styling QRPreview.tsx:108:37
QRPreview: Verifying data for scooter_unlock 
Object { scooter_id: "SCOOT123", unlock_code: "UNLOCK42", title: "Scooter_unlock QR Code", description: "Scan to view scooter_unlock content" }
QRPreview.tsx:260:17
QRPreview: Verified data 
Object { scooter_id: "SCOOT123", unlock_code: "UNLOCK42", title: "Scooter_unlock QR Code", description: "Scan to view scooter_unlock content" }
QRPreview.tsx:364:17
Frontend QR preview SVG request: 
Object { qrType: "scooter_unlock", dataSize: 135, designOptions: (6) […], hasLogo: false, dimensions: 300 }
qrService.modern.ts:376:21
Cleaned content for SVG preview: 
Object { scooter_id: "SCOOT123", unlock_code: "UNLOCK42", title: "Scooter_unlock QR Code", description: "Scan to view scooter_unlock content" }
qrService.modern.ts:396:21
[API] Request: POST /qr/preview/svg modernClient.ts:27:24
[API] Added auth token to request modernClient.ts:27:24
Frontend QR preview SVG request: 
Object { qrType: "scooter_unlock", dataSize: 135, designOptions: (32) […], hasLogo: false, dimensions: 300 }
qrService.modern.ts:376:21
Cleaned content for SVG preview: 
Object { scooter_id: "SCOOT123", unlock_code: "UNLOCK42", title: "Scooter_unlock QR Code", description: "Scan to view scooter_unlock content" }
qrService.modern.ts:396:21
[API] Request: POST /qr/preview/svg modernClient.ts:27:24
[API] Added auth token to request modernClient.ts:27:24
[API] Response: 200 OK - POST /qr/preview/svg modernClient.ts:27:24
Successfully received SVG QR preview qrService.modern.ts:429:25
[API] Response: 200 OK - POST /qr/preview/svg modernClient.ts:27:24
Successfully received SVG QR preview qrService.modern.ts:429:25
Raw SVG from backend: <svg baseProfile="full" height="300" version="1.1" width="300" xmlns="http://www.w3.org/2000/svg" xmlns:ev="http://www.w3.org/2001/xml-events" xmlns:xlink="http://www.w3.org/1999/xlink"><defs /><rect class="qr-bg" fill="#FFFFFF" height="300" id="qr-bg" width="300" x="0" y="0" /><rect class="qr-module" fill="#000000" height="5.918367346938775" id="module-8-0" width="5.918367346938775" x="52.3469387755102" y="5.0" /><rect class="qr-module" fill="#000000" height="5.918367346938775" id="module-9-0" ... QRPreview.tsx:127:37
SVG length: 176046 QRPreview.tsx:128:37
SVG contains <svg>: true QRPreview.tsx:129:37
Raw SVG structure: 
Object { rectCount: 1127, pathCount: 0 }
QRPreview.tsx:133:37
SVG analysis: 
Object { hasRect: true, hasPath: false, hasCircle: false, hasBackground: true, hasForeground: true, totalRects: 1127, largeRects: 7, finderPatternsDetected: true, originalDesignOptions: {…}, designOptionsKeys: (21) […] }
QRPreview.tsx:163:37
Large rectangles (potential finder patterns): 
Array(5) [ '<rect class="qr-bg" fill="#FFFFFF" height="300" id="qr-bg" width="300" x="0" y="0" />', '<rect class="qr-marker-outer" fill="#000000" height="41.42857142857143" width="41.42857142857143" x="5.0" y="5.0" />', '<rect class="qr-marker-inner" fill="#fff" height="27.224489795918366" width="27.224489795918366" x="12.10204081632653" y="12.10204081632653" />', '<rect class="qr-marker-outer" fill="#000000" height="41.42857142857143" width="41.42857142857143" x="253.57142857142856" y="5.0" />', '<rect class="qr-marker-inner" fill="#fff" height="27.224489795918366" width="27.224489795918366" x="260.67346938775506" y="12.10204081632653" />' ]
QRPreview.tsx:177:41
Original design options: 
Object { size: 256, margin: 10, pattern: "square", marker_border_style: "square", marker_border_color: "#000000", marker_center_style: "square", marker_center_color: "#000000", logo_size: 48, logo_position: "center", logo_opacity: 1, … }
QRPreview.tsx:183:37
Filtered design options for basic preview: 
Object { size: 256, margin: 10, error_correction: "M" }
QRPreview.tsx:184:37
Filtered options count: 3 QRPreview.tsx:185:37
SVG Styler: Parsing SVG, element count: 1122 svgStyler.ts:23:13
SVG Styler: Design options: 
Object { size: 256, margin: 10, error_correction: "M" }
svgStyler.ts:24:13
SVG Styler: Using default background color svgStyler.ts:48:17
SVG Styler: Using default foreground color svgStyler.ts:80:17
SVG Styler: Using default square pattern svgStyler.ts:97:17
SVG Styler: Running finder pattern detection... svgStyler.ts:100:13
Finder pattern detection - backend classes found: 
Object { markerOuters: 3, markerInners: 3, markerDots: 3 }
svgStyler.ts:232:13
Backend finder pattern 1: 
Object { outer: {…}, inner: {…}, dot: {…} }
svgStyler.ts:260:21
Backend finder pattern 2: 
Object { outer: {…}, inner: {…}, dot: {…} }
svgStyler.ts:260:21
Backend finder pattern 3: 
Object { outer: {…}, inner: {…}, dot: {…} }
svgStyler.ts:260:21
Finder pattern detection summary: 
Object { patternsFound: 3, method: "backend-classes" }
svgStyler.ts:343:13
SVG Styler: Finder patterns detected: 3 svgStyler.ts:102:13
SVG Styler: Final SVG length: 174918 svgStyler.ts:129:13
Styled SVG: <svg baseProfile="full" height="300" version="1.1" width="300" xmlns="http://www.w3.org/2000/svg" xmlns:ev="http://www.w3.org/2001/xml-events" xmlns:xlink="http://www.w3.org/1999/xlink"><defs/><rect c... QRPreview.tsx:193:41
[Auth] Adding Bearer token to request: /health layout.tsx:133:49
QRPreview: Content key updated 
Object { qrType: "scooter_unlock", dataChanged: true, designChanged: true, logoChanged: false, useSvg: true }
QRPreview.tsx:51:21
QRPreview: Content key updated  
Object { qrType: "scooter_unlock", dataChanged: true, designChanged: true, logoChanged: false, useSvg: true }
dashboard:1:596
QRPreview: Generating basic SVG for frontend styling QRPreview.tsx:108:37
QRPreview: Verifying data for scooter_unlock 
Object { scooter_id: "SCOOT123", unlock_code: "UNLOCK42", title: "Scooter_unlock QR Code", description: "Scan to view scooter_unlock content" }
QRPreview.tsx:260:17
QRPreview: Verified data 
Object { scooter_id: "SCOOT123", unlock_code: "UNLOCK42", title: "Scooter_unlock QR Code", description: "Scan to view scooter_unlock content" }
QRPreview.tsx:364:17
Frontend QR preview SVG request: 
Object { qrType: "scooter_unlock", dataSize: 135, designOptions: (6) […], hasLogo: false, dimensions: 300 }
qrService.modern.ts:376:21
Cleaned content for SVG preview: 
Object { scooter_id: "SCOOT123", unlock_code: "UNLOCK42", title: "Scooter_unlock QR Code", description: "Scan to view scooter_unlock content" }
qrService.modern.ts:396:21
[API] Request: POST /qr/preview/svg modernClient.ts:27:24
[API] Added auth token to request modernClient.ts:27:24
Frontend QR preview SVG request: 
Object { qrType: "scooter_unlock", dataSize: 135, designOptions: (32) […], hasLogo: false, dimensions: 300 }
qrService.modern.ts:376:21
Cleaned content for SVG preview: 
Object { scooter_id: "SCOOT123", unlock_code: "UNLOCK42", title: "Scooter_unlock QR Code", description: "Scan to view scooter_unlock content" }
qrService.modern.ts:396:21
[API] Request: POST /qr/preview/svg modernClient.ts:27:24
[API] Added auth token to request modernClient.ts:27:24
[API] Response: 200 OK - POST /qr/preview/svg modernClient.ts:27:24
Successfully received SVG QR preview qrService.modern.ts:429:25
Raw SVG from backend: <svg baseProfile="full" height="300" version="1.1" width="300" xmlns="http://www.w3.org/2000/svg" xmlns:ev="http://www.w3.org/2001/xml-events" xmlns:xlink="http://www.w3.org/1999/xlink"><defs /><rect class="qr-bg" fill="#FFFFFF" height="300" id="qr-bg" width="300" x="0" y="0" /><rect class="qr-module" fill="#000000" height="5.918367346938775" id="module-8-0" width="5.918367346938775" x="52.3469387755102" y="5.0" /><rect class="qr-module" fill="#000000" height="5.918367346938775" id="module-9-0" ... QRPreview.tsx:127:37
SVG length: 176046 QRPreview.tsx:128:37
SVG contains <svg>: true QRPreview.tsx:129:37
Raw SVG structure: 
Object { rectCount: 1127, pathCount: 0 }
QRPreview.tsx:133:37
SVG analysis: 
Object { hasRect: true, hasPath: false, hasCircle: false, hasBackground: true, hasForeground: true, totalRects: 1127, largeRects: 7, finderPatternsDetected: true, originalDesignOptions: {…}, designOptionsKeys: (21) […] }
QRPreview.tsx:163:37
Large rectangles (potential finder patterns): 
Array(5) [ '<rect class="qr-bg" fill="#FFFFFF" height="300" id="qr-bg" width="300" x="0" y="0" />', '<rect class="qr-marker-outer" fill="#000000" height="41.42857142857143" width="41.42857142857143" x="5.0" y="5.0" />', '<rect class="qr-marker-inner" fill="#fff" height="27.224489795918366" width="27.224489795918366" x="12.10204081632653" y="12.10204081632653" />', '<rect class="qr-marker-outer" fill="#000000" height="41.42857142857143" width="41.42857142857143" x="253.57142857142856" y="5.0" />', '<rect class="qr-marker-inner" fill="#fff" height="27.224489795918366" width="27.224489795918366" x="260.67346938775506" y="12.10204081632653" />' ]
QRPreview.tsx:177:41
Original design options: 
Object { size: 256, margin: 10, pattern: "horizontal", marker_border_style: "square", marker_border_color: "#000000", marker_center_style: "square", marker_center_color: "#000000", logo_size: 48, logo_position: "center", logo_opacity: 1, … }
QRPreview.tsx:183:37
Filtered design options for basic preview: 
Object { size: 256, margin: 10, error_correction: "M", pattern: "horizontal" }
QRPreview.tsx:184:37
Filtered options count: 4 QRPreview.tsx:185:37
SVG Styler: Parsing SVG, element count: 1122 svgStyler.ts:23:13
SVG Styler: Design options: 
Object { size: 256, margin: 10, error_correction: "M", pattern: "horizontal" }
svgStyler.ts:24:13
SVG Styler: Using default background color svgStyler.ts:48:17
SVG Styler: Using default foreground color svgStyler.ts:80:17
SVG Styler: Applying pattern styling: horizontal svgStyler.ts:94:17
Finder pattern detection - backend classes found: 
Object { markerOuters: 3, markerInners: 3, markerDots: 3 }
svgStyler.ts:232:13
Backend finder pattern 1: 
Object { outer: {…}, inner: {…}, dot: {…} }
svgStyler.ts:260:21
Backend finder pattern 2: 
Object { outer: {…}, inner: {…}, dot: {…} }
svgStyler.ts:260:21
Backend finder pattern 3: 
Object { outer: {…}, inner: {…}, dot: {…} }
svgStyler.ts:260:21
Finder pattern detection summary: 
Object { patternsFound: 3, method: "backend-classes" }
svgStyler.ts:343:13
Pattern styling: 
Object { pattern: "horizontal", totalRects: 1126, finderElements: 9, dataModules: 1117, markerElements: 9 }
svgStyler.ts:369:13
SVG Styler: Running finder pattern detection... svgStyler.ts:100:13
Finder pattern detection - backend classes found: 
Object { markerOuters: 3, markerInners: 3, markerDots: 3 }
svgStyler.ts:232:13
Backend finder pattern 1: 
Object { outer: {…}, inner: {…}, dot: {…} }
svgStyler.ts:260:21
Backend finder pattern 2: 
Object { outer: {…}, inner: {…}, dot: {…} }
svgStyler.ts:260:21
Backend finder pattern 3: 
Object { outer: {…}, inner: {…}, dot: {…} }
svgStyler.ts:260:21
Finder pattern detection summary: 
Object { patternsFound: 3, method: "backend-classes" }
svgStyler.ts:343:13
SVG Styler: Finder patterns detected: 3 svgStyler.ts:102:13
SVG Styler: Final SVG length: 136057 svgStyler.ts:129:13
Styled SVG: <svg baseProfile="full" height="300" version="1.1" width="300" xmlns="http://www.w3.org/2000/svg" xmlns:ev="http://www.w3.org/2001/xml-events" xmlns:xlink="http://www.w3.org/1999/xlink"><defs/><rect c... QRPreview.tsx:193:41
[API] Response: 200 OK - POST /qr/preview/svg modernClient.ts:27:24
Successfully received SVG QR preview qrService.modern.ts:429:25
QRPreview: Content key updated 
Object { qrType: "scooter_unlock", dataChanged: true, designChanged: true, logoChanged: false, useSvg: true }
QRPreview.tsx:51:21
QRPreview: Content key updated  
Object { qrType: "scooter_unlock", dataChanged: true, designChanged: true, logoChanged: false, useSvg: true }
dashboard:1:596
QRPreview: Generating basic SVG for frontend styling QRPreview.tsx:108:37
QRPreview: Verifying data for scooter_unlock 
Object { scooter_id: "SCOOT123", unlock_code: "UNLOCK42", title: "Scooter_unlock QR Code", description: "Scan to view scooter_unlock content" }
QRPreview.tsx:260:17
QRPreview: Verified data 
Object { scooter_id: "SCOOT123", unlock_code: "UNLOCK42", title: "Scooter_unlock QR Code", description: "Scan to view scooter_unlock content" }
QRPreview.tsx:364:17
Frontend QR preview SVG request: 
Object { qrType: "scooter_unlock", dataSize: 135, designOptions: (6) […], hasLogo: false, dimensions: 300 }
qrService.modern.ts:376:21
Cleaned content for SVG preview: 
Object { scooter_id: "SCOOT123", unlock_code: "UNLOCK42", title: "Scooter_unlock QR Code", description: "Scan to view scooter_unlock content" }
qrService.modern.ts:396:21
[API] Request: POST /qr/preview/svg modernClient.ts:27:24
[API] Added auth token to request modernClient.ts:27:24
Frontend QR preview SVG request: 
Object { qrType: "scooter_unlock", dataSize: 135, designOptions: (32) […], hasLogo: false, dimensions: 300 }
qrService.modern.ts:376:21
Cleaned content for SVG preview: 
Object { scooter_id: "SCOOT123", unlock_code: "UNLOCK42", title: "Scooter_unlock QR Code", description: "Scan to view scooter_unlock content" }
qrService.modern.ts:396:21
[API] Request: POST /qr/preview/svg modernClient.ts:27:24
[API] Added auth token to request modernClient.ts:27:24
[API] Response: 200 OK - POST /qr/preview/svg modernClient.ts:27:24
Successfully received SVG QR preview qrService.modern.ts:429:25
Raw SVG from backend: <svg baseProfile="full" height="300" version="1.1" width="300" xmlns="http://www.w3.org/2000/svg" xmlns:ev="http://www.w3.org/2001/xml-events" xmlns:xlink="http://www.w3.org/1999/xlink"><defs /><rect class="qr-bg" fill="#FFFFFF" height="300" id="qr-bg" width="300" x="0" y="0" /><rect class="qr-module" fill="#000000" height="5.918367346938775" id="module-8-0" width="5.918367346938775" x="52.3469387755102" y="5.0" /><rect class="qr-module" fill="#000000" height="5.918367346938775" id="module-9-0" ... QRPreview.tsx:127:37
SVG length: 176046 QRPreview.tsx:128:37
SVG contains <svg>: true QRPreview.tsx:129:37
Raw SVG structure: 
Object { rectCount: 1127, pathCount: 0 }
QRPreview.tsx:133:37
SVG analysis: 
Object { hasRect: true, hasPath: false, hasCircle: false, hasBackground: true, hasForeground: true, totalRects: 1127, largeRects: 7, finderPatternsDetected: true, originalDesignOptions: {…}, designOptionsKeys: (21) […] }
QRPreview.tsx:163:37
Large rectangles (potential finder patterns): 
Array(5) [ '<rect class="qr-bg" fill="#FFFFFF" height="300" id="qr-bg" width="300" x="0" y="0" />', '<rect class="qr-marker-outer" fill="#000000" height="41.42857142857143" width="41.42857142857143" x="5.0" y="5.0" />', '<rect class="qr-marker-inner" fill="#fff" height="27.224489795918366" width="27.224489795918366" x="12.10204081632653" y="12.10204081632653" />', '<rect class="qr-marker-outer" fill="#000000" height="41.42857142857143" width="41.42857142857143" x="253.57142857142856" y="5.0" />', '<rect class="qr-marker-inner" fill="#fff" height="27.224489795918366" width="27.224489795918366" x="260.67346938775506" y="12.10204081632653" />' ]
QRPreview.tsx:177:41
Original design options: 
Object { size: 256, margin: 10, pattern: "vertical", marker_border_style: "square", marker_border_color: "#000000", marker_center_style: "square", marker_center_color: "#000000", logo_size: 48, logo_position: "center", logo_opacity: 1, … }
QRPreview.tsx:183:37
Filtered design options for basic preview: 
Object { size: 256, margin: 10, error_correction: "M", pattern: "vertical" }
QRPreview.tsx:184:37
Filtered options count: 4 QRPreview.tsx:185:37
SVG Styler: Parsing SVG, element count: 1122 svgStyler.ts:23:13
SVG Styler: Design options: 
Object { size: 256, margin: 10, error_correction: "M", pattern: "vertical" }
svgStyler.ts:24:13
SVG Styler: Using default background color svgStyler.ts:48:17
SVG Styler: Using default foreground color svgStyler.ts:80:17
SVG Styler: Applying pattern styling: vertical svgStyler.ts:94:17
Finder pattern detection - backend classes found: 
Object { markerOuters: 3, markerInners: 3, markerDots: 3 }
svgStyler.ts:232:13
Backend finder pattern 1: 
Object { outer: {…}, inner: {…}, dot: {…} }
svgStyler.ts:260:21
Backend finder pattern 2: 
Object { outer: {…}, inner: {…}, dot: {…} }
svgStyler.ts:260:21
Backend finder pattern 3: 
Object { outer: {…}, inner: {…}, dot: {…} }
svgStyler.ts:260:21
Finder pattern detection summary: 
Object { patternsFound: 3, method: "backend-classes" }
svgStyler.ts:343:13
Pattern styling: 
Object { pattern: "vertical", totalRects: 1126, finderElements: 9, dataModules: 1117, markerElements: 9 }
svgStyler.ts:369:13
SVG Styler: Running finder pattern detection... svgStyler.ts:100:13
Finder pattern detection - backend classes found: 
Object { markerOuters: 3, markerInners: 3, markerDots: 3 }
svgStyler.ts:232:13
Backend finder pattern 1: 
Object { outer: {…}, inner: {…}, dot: {…} }
svgStyler.ts:260:21
Backend finder pattern 2: 
Object { outer: {…}, inner: {…}, dot: {…} }
svgStyler.ts:260:21
Backend finder pattern 3: 
Object { outer: {…}, inner: {…}, dot: {…} }
svgStyler.ts:260:21
Finder pattern detection summary: 
Object { patternsFound: 3, method: "backend-classes" }
svgStyler.ts:343:13
SVG Styler: Finder patterns detected: 3 svgStyler.ts:102:13
SVG Styler: Final SVG length: 136208 svgStyler.ts:129:13
Styled SVG: <svg baseProfile="full" height="300" version="1.1" width="300" xmlns="http://www.w3.org/2000/svg" xmlns:ev="http://www.w3.org/2001/xml-events" xmlns:xlink="http://www.w3.org/1999/xlink"><defs/><rect c... QRPreview.tsx:193:41
[API] Response: 200 OK - POST /qr/preview/svg modernClient.ts:27:24
Successfully received SVG QR preview qrService.modern.ts:429:25
QRPreview: Content key updated 
Object { qrType: "scooter_unlock", dataChanged: true, designChanged: true, logoChanged: false, useSvg: true }
QRPreview.tsx:51:21
QRPreview: Content key updated  
Object { qrType: "scooter_unlock", dataChanged: true, designChanged: true, logoChanged: false, useSvg: true }
dashboard:1:596
QRPreview: Generating basic SVG for frontend styling QRPreview.tsx:108:37
QRPreview: Verifying data for scooter_unlock 
Object { scooter_id: "SCOOT123", unlock_code: "UNLOCK42", title: "Scooter_unlock QR Code", description: "Scan to view scooter_unlock content" }
QRPreview.tsx:260:17
QRPreview: Verified data 
Object { scooter_id: "SCOOT123", unlock_code: "UNLOCK42", title: "Scooter_unlock QR Code", description: "Scan to view scooter_unlock content" }
QRPreview.tsx:364:17
Frontend QR preview SVG request: 
Object { qrType: "scooter_unlock", dataSize: 135, designOptions: (6) […], hasLogo: false, dimensions: 300 }
qrService.modern.ts:376:21
Cleaned content for SVG preview: 
Object { scooter_id: "SCOOT123", unlock_code: "UNLOCK42", title: "Scooter_unlock QR Code", description: "Scan to view scooter_unlock content" }
qrService.modern.ts:396:21
[API] Request: POST /qr/preview/svg modernClient.ts:27:24
[API] Added auth token to request modernClient.ts:27:24
Frontend QR preview SVG request: 
Object { qrType: "scooter_unlock", dataSize: 135, designOptions: (32) […], hasLogo: false, dimensions: 300 }
qrService.modern.ts:376:21
Cleaned content for SVG preview: 
Object { scooter_id: "SCOOT123", unlock_code: "UNLOCK42", title: "Scooter_unlock QR Code", description: "Scan to view scooter_unlock content" }
qrService.modern.ts:396:21
[API] Request: POST /qr/preview/svg modernClient.ts:27:24
[API] Added auth token to request modernClient.ts:27:24
[API] Response: 200 OK - POST /qr/preview/svg modernClient.ts:27:24
Successfully received SVG QR preview qrService.modern.ts:429:25
Raw SVG from backend: <svg baseProfile="full" height="300" version="1.1" width="300" xmlns="http://www.w3.org/2000/svg" xmlns:ev="http://www.w3.org/2001/xml-events" xmlns:xlink="http://www.w3.org/1999/xlink"><defs /><rect class="qr-bg" fill="#FFFFFF" height="300" id="qr-bg" width="300" x="0" y="0" /><rect class="qr-module" fill="#000000" height="5.918367346938775" id="module-8-0" width="5.918367346938775" x="52.3469387755102" y="5.0" /><rect class="qr-module" fill="#000000" height="5.918367346938775" id="module-9-0" ... QRPreview.tsx:127:37
SVG length: 176046 QRPreview.tsx:128:37
SVG contains <svg>: true QRPreview.tsx:129:37
Raw SVG structure: 
Object { rectCount: 1127, pathCount: 0 }
QRPreview.tsx:133:37
SVG analysis: 
Object { hasRect: true, hasPath: false, hasCircle: false, hasBackground: true, hasForeground: true, totalRects: 1127, largeRects: 7, finderPatternsDetected: true, originalDesignOptions: {…}, designOptionsKeys: (21) […] }
QRPreview.tsx:163:37
Large rectangles (potential finder patterns): 
Array(5) [ '<rect class="qr-bg" fill="#FFFFFF" height="300" id="qr-bg" width="300" x="0" y="0" />', '<rect class="qr-marker-outer" fill="#000000" height="41.42857142857143" width="41.42857142857143" x="5.0" y="5.0" />', '<rect class="qr-marker-inner" fill="#fff" height="27.224489795918366" width="27.224489795918366" x="12.10204081632653" y="12.10204081632653" />', '<rect class="qr-marker-outer" fill="#000000" height="41.42857142857143" width="41.42857142857143" x="253.57142857142856" y="5.0" />', '<rect class="qr-marker-inner" fill="#fff" height="27.224489795918366" width="27.224489795918366" x="260.67346938775506" y="12.10204081632653" />' ]
QRPreview.tsx:177:41
Original design options: 
Object { size: 256, margin: 10, pattern: "diagonal", marker_border_style: "square", marker_border_color: "#000000", marker_center_style: "square", marker_center_color: "#000000", logo_size: 48, logo_position: "center", logo_opacity: 1, … }
QRPreview.tsx:183:37
Filtered design options for basic preview: 
Object { size: 256, margin: 10, error_correction: "M", pattern: "diagonal" }
QRPreview.tsx:184:37
Filtered options count: 4 QRPreview.tsx:185:37
SVG Styler: Parsing SVG, element count: 1122 svgStyler.ts:23:13
SVG Styler: Design options: 
Object { size: 256, margin: 10, error_correction: "M", pattern: "diagonal" }
svgStyler.ts:24:13
SVG Styler: Using default background color svgStyler.ts:48:17
SVG Styler: Using default foreground color svgStyler.ts:80:17
SVG Styler: Applying pattern styling: diagonal svgStyler.ts:94:17
Finder pattern detection - backend classes found: 
Object { markerOuters: 3, markerInners: 3, markerDots: 3 }
svgStyler.ts:232:13
Backend finder pattern 1: 
Object { outer: {…}, inner: {…}, dot: {…} }
svgStyler.ts:260:21
Backend finder pattern 2: 
Object { outer: {…}, inner: {…}, dot: {…} }
svgStyler.ts:260:21
Backend finder pattern 3: 
Object { outer: {…}, inner: {…}, dot: {…} }
svgStyler.ts:260:21
Finder pattern detection summary: 
Object { patternsFound: 3, method: "backend-classes" }
svgStyler.ts:343:13
Pattern styling: 
Object { pattern: "diagonal", totalRects: 1126, finderElements: 9, dataModules: 1117, markerElements: 9 }
svgStyler.ts:369:13
SVG Styler: Running finder pattern detection... svgStyler.ts:100:13
Finder pattern detection - backend classes found: 
Object { markerOuters: 3, markerInners: 3, markerDots: 3 }
svgStyler.ts:232:13
Backend finder pattern 1: 
Object { outer: {…}, inner: {…}, dot: {…} }
svgStyler.ts:260:21
Backend finder pattern 2: 
Object { outer: {…}, inner: {…}, dot: {…} }
svgStyler.ts:260:21
Backend finder pattern 3: 
Object { outer: {…}, inner: {…}, dot: {…} }
svgStyler.ts:260:21
Finder pattern detection summary: 
Object { patternsFound: 3, method: "backend-classes" }
svgStyler.ts:343:13
SVG Styler: Finder patterns detected: 3 svgStyler.ts:102:13
SVG Styler: Final SVG length: 174918 svgStyler.ts:129:13
Styled SVG: <svg baseProfile="full" height="300" version="1.1" width="300" xmlns="http://www.w3.org/2000/svg" xmlns:ev="http://www.w3.org/2001/xml-events" xmlns:xlink="http://www.w3.org/1999/xlink"><defs/><rect c... QRPreview.tsx:193:41
[API] Response: 200 OK - POST /qr/preview/svg modernClient.ts:27:24
Successfully received SVG QR preview qrService.modern.ts:429:25
QRPreview: Content key updated 
Object { qrType: "scooter_unlock", dataChanged: true, designChanged: true, logoChanged: false, useSvg: true }
QRPreview.tsx:51:21
QRPreview: Content key updated  
Object { qrType: "scooter_unlock", dataChanged: true, designChanged: true, logoChanged: false, useSvg: true }
dashboard:1:596
QRPreview: Generating basic SVG for frontend styling QRPreview.tsx:108:37
QRPreview: Verifying data for scooter_unlock 
Object { scooter_id: "SCOOT123", unlock_code: "UNLOCK42", title: "Scooter_unlock QR Code", description: "Scan to view scooter_unlock content" }
QRPreview.tsx:260:17
QRPreview: Verified data 
Object { scooter_id: "SCOOT123", unlock_code: "UNLOCK42", title: "Scooter_unlock QR Code", description: "Scan to view scooter_unlock content" }
QRPreview.tsx:364:17
Frontend QR preview SVG request: 
Object { qrType: "scooter_unlock", dataSize: 135, designOptions: (6) […], hasLogo: false, dimensions: 300 }
qrService.modern.ts:376:21
Cleaned content for SVG preview: 
Object { scooter_id: "SCOOT123", unlock_code: "UNLOCK42", title: "Scooter_unlock QR Code", description: "Scan to view scooter_unlock content" }
qrService.modern.ts:396:21
[API] Request: POST /qr/preview/svg modernClient.ts:27:24
[API] Added auth token to request modernClient.ts:27:24
Frontend QR preview SVG request: 
Object { qrType: "scooter_unlock", dataSize: 135, designOptions: (32) […], hasLogo: false, dimensions: 300 }
qrService.modern.ts:376:21
Cleaned content for SVG preview: 
Object { scooter_id: "SCOOT123", unlock_code: "UNLOCK42", title: "Scooter_unlock QR Code", description: "Scan to view scooter_unlock content" }
qrService.modern.ts:396:21
[API] Request: POST /qr/preview/svg modernClient.ts:27:24
[API] Added auth token to request modernClient.ts:27:24
[API] Response: 200 OK - POST /qr/preview/svg modernClient.ts:27:24
Successfully received SVG QR preview qrService.modern.ts:429:25
Raw SVG from backend: <svg baseProfile="full" height="300" version="1.1" width="300" xmlns="http://www.w3.org/2000/svg" xmlns:ev="http://www.w3.org/2001/xml-events" xmlns:xlink="http://www.w3.org/1999/xlink"><defs /><rect class="qr-bg" fill="#FFFFFF" height="300" id="qr-bg" width="300" x="0" y="0" /><rect class="qr-module" fill="#000000" height="5.918367346938775" id="module-8-0" width="5.918367346938775" x="52.3469387755102" y="5.0" /><rect class="qr-module" fill="#000000" height="5.918367346938775" id="module-9-0" ... QRPreview.tsx:127:37
SVG length: 176046 QRPreview.tsx:128:37
SVG contains <svg>: true QRPreview.tsx:129:37
Raw SVG structure: 
Object { rectCount: 1127, pathCount: 0 }
QRPreview.tsx:133:37
SVG analysis: 
Object { hasRect: true, hasPath: false, hasCircle: false, hasBackground: true, hasForeground: true, totalRects: 1127, largeRects: 7, finderPatternsDetected: true, originalDesignOptions: {…}, designOptionsKeys: (21) […] }
QRPreview.tsx:163:37
Large rectangles (potential finder patterns): 
Array(5) [ '<rect class="qr-bg" fill="#FFFFFF" height="300" id="qr-bg" width="300" x="0" y="0" />', '<rect class="qr-marker-outer" fill="#000000" height="41.42857142857143" width="41.42857142857143" x="5.0" y="5.0" />', '<rect class="qr-marker-inner" fill="#fff" height="27.224489795918366" width="27.224489795918366" x="12.10204081632653" y="12.10204081632653" />', '<rect class="qr-marker-outer" fill="#000000" height="41.42857142857143" width="41.42857142857143" x="253.57142857142856" y="5.0" />', '<rect class="qr-marker-inner" fill="#fff" height="27.224489795918366" width="27.224489795918366" x="260.67346938775506" y="12.10204081632653" />' ]
QRPreview.tsx:177:41
Original design options: 
Object { size: 256, margin: 10, pattern: "vertical", marker_border_style: "square", marker_border_color: "#000000", marker_center_style: "square", marker_center_color: "#000000", logo_size: 48, logo_position: "center", logo_opacity: 1, … }
QRPreview.tsx:183:37
Filtered design options for basic preview: 
Object { size: 256, margin: 10, error_correction: "M", pattern: "vertical" }
QRPreview.tsx:184:37
Filtered options count: 4 QRPreview.tsx:185:37
SVG Styler: Parsing SVG, element count: 1122 svgStyler.ts:23:13
SVG Styler: Design options: 
Object { size: 256, margin: 10, error_correction: "M", pattern: "vertical" }
svgStyler.ts:24:13
SVG Styler: Using default background color svgStyler.ts:48:17
SVG Styler: Using default foreground color svgStyler.ts:80:17
SVG Styler: Applying pattern styling: vertical svgStyler.ts:94:17
Finder pattern detection - backend classes found: 
Object { markerOuters: 3, markerInners: 3, markerDots: 3 }
svgStyler.ts:232:13
Backend finder pattern 1: 
Object { outer: {…}, inner: {…}, dot: {…} }
svgStyler.ts:260:21
Backend finder pattern 2: 
Object { outer: {…}, inner: {…}, dot: {…} }
svgStyler.ts:260:21
Backend finder pattern 3: 
Object { outer: {…}, inner: {…}, dot: {…} }
svgStyler.ts:260:21
Finder pattern detection summary: 
Object { patternsFound: 3, method: "backend-classes" }
svgStyler.ts:343:13
Pattern styling: 
Object { pattern: "vertical", totalRects: 1126, finderElements: 9, dataModules: 1117, markerElements: 9 }
svgStyler.ts:369:13
SVG Styler: Running finder pattern detection... svgStyler.ts:100:13
Finder pattern detection - backend classes found: 
Object { markerOuters: 3, markerInners: 3, markerDots: 3 }
svgStyler.ts:232:13
Backend finder pattern 1: 
Object { outer: {…}, inner: {…}, dot: {…} }
svgStyler.ts:260:21
Backend finder pattern 2: 
Object { outer: {…}, inner: {…}, dot: {…} }
svgStyler.ts:260:21
Backend finder pattern 3: 
Object { outer: {…}, inner: {…}, dot: {…} }
svgStyler.ts:260:21
Finder pattern detection summary: 
Object { patternsFound: 3, method: "backend-classes" }
svgStyler.ts:343:13
SVG Styler: Finder patterns detected: 3 svgStyler.ts:102:13
SVG Styler: Final SVG length: 136208 svgStyler.ts:129:13
Styled SVG: <svg baseProfile="full" height="300" version="1.1" width="300" xmlns="http://www.w3.org/2000/svg" xmlns:ev="http://www.w3.org/2001/xml-events" xmlns:xlink="http://www.w3.org/1999/xlink"><defs/><rect c... QRPreview.tsx:193:41
[API] Response: 200 OK - POST /qr/preview/svg modernClient.ts:27:24
Successfully received SVG QR preview qrService.modern.ts:429:25
[Auth] Adding Bearer token to request: /health 2 layout.tsx:133:49
[Auth] Health check successful context.tsx:123:37
QRPreview: Content key updated 
Object { qrType: "scooter_unlock", dataChanged: true, designChanged: true, logoChanged: false, useSvg: true }
QRPreview.tsx:51:21
QRPreview: Content key updated  
Object { qrType: "scooter_unlock", dataChanged: true, designChanged: true, logoChanged: false, useSvg: true }
dashboard:1:596
QRPreview: Generating basic SVG for frontend styling QRPreview.tsx:108:37
QRPreview: Verifying data for scooter_unlock 
Object { scooter_id: "SCOOT123", unlock_code: "UNLOCK42", title: "Scooter_unlock QR Code", description: "Scan to view scooter_unlock content" }
QRPreview.tsx:260:17
QRPreview: Verified data 
Object { scooter_id: "SCOOT123", unlock_code: "UNLOCK42", title: "Scooter_unlock QR Code", description: "Scan to view scooter_unlock content" }
QRPreview.tsx:364:17
Frontend QR preview SVG request: 
Object { qrType: "scooter_unlock", dataSize: 135, designOptions: (6) […], hasLogo: false, dimensions: 300 }
qrService.modern.ts:376:21
Cleaned content for SVG preview: 
Object { scooter_id: "SCOOT123", unlock_code: "UNLOCK42", title: "Scooter_unlock QR Code", description: "Scan to view scooter_unlock content" }
qrService.modern.ts:396:21
[API] Request: POST /qr/preview/svg modernClient.ts:27:24
[API] Added auth token to request modernClient.ts:27:24
Frontend QR preview SVG request: 
Object { qrType: "scooter_unlock", dataSize: 135, designOptions: (32) […], hasLogo: false, dimensions: 300 }
qrService.modern.ts:376:21
Cleaned content for SVG preview: 
Object { scooter_id: "SCOOT123", unlock_code: "UNLOCK42", title: "Scooter_unlock QR Code", description: "Scan to view scooter_unlock content" }
qrService.modern.ts:396:21
[API] Request: POST /qr/preview/svg modernClient.ts:27:24
[API] Added auth token to request modernClient.ts:27:24
[API] Response: 200 OK - POST /qr/preview/svg modernClient.ts:27:24
Successfully received SVG QR preview qrService.modern.ts:429:25
[API] Response: 200 OK - POST /qr/preview/svg modernClient.ts:27:24
Successfully received SVG QR preview qrService.modern.ts:429:25
Raw SVG from backend: <svg baseProfile="full" height="300" version="1.1" width="300" xmlns="http://www.w3.org/2000/svg" xmlns:ev="http://www.w3.org/2001/xml-events" xmlns:xlink="http://www.w3.org/1999/xlink"><defs /><rect class="qr-bg" fill="#FFFFFF" height="300" id="qr-bg" width="300" x="0" y="0" /><rect class="qr-module" fill="#000000" height="5.918367346938775" id="module-8-0" width="5.918367346938775" x="52.3469387755102" y="5.0" /><rect class="qr-module" fill="#000000" height="5.918367346938775" id="module-9-0" ... QRPreview.tsx:127:37
SVG length: 176046 QRPreview.tsx:128:37
SVG contains <svg>: true QRPreview.tsx:129:37
Raw SVG structure: 
Object { rectCount: 1127, pathCount: 0 }
QRPreview.tsx:133:37
SVG analysis: 
Object { hasRect: true, hasPath: false, hasCircle: false, hasBackground: true, hasForeground: true, totalRects: 1127, largeRects: 7, finderPatternsDetected: true, originalDesignOptions: {…}, designOptionsKeys: (21) […] }
QRPreview.tsx:163:37
Large rectangles (potential finder patterns): 
Array(5) [ '<rect class="qr-bg" fill="#FFFFFF" height="300" id="qr-bg" width="300" x="0" y="0" />', '<rect class="qr-marker-outer" fill="#000000" height="41.42857142857143" width="41.42857142857143" x="5.0" y="5.0" />', '<rect class="qr-marker-inner" fill="#fff" height="27.224489795918366" width="27.224489795918366" x="12.10204081632653" y="12.10204081632653" />', '<rect class="qr-marker-outer" fill="#000000" height="41.42857142857143" width="41.42857142857143" x="253.57142857142856" y="5.0" />', '<rect class="qr-marker-inner" fill="#fff" height="27.224489795918366" width="27.224489795918366" x="260.67346938775506" y="12.10204081632653" />' ]
QRPreview.tsx:177:41
Original design options: 
Object { size: 256, margin: 10, pattern: "diagonal", marker_border_style: "square", marker_border_color: "#000000", marker_center_style: "square", marker_center_color: "#000000", logo_size: 48, logo_position: "center", logo_opacity: 1, … }
QRPreview.tsx:183:37
Filtered design options for basic preview: 
Object { size: 256, margin: 10, error_correction: "M", pattern: "diagonal" }
QRPreview.tsx:184:37
Filtered options count: 4 QRPreview.tsx:185:37
SVG Styler: Parsing SVG, element count: 1122 svgStyler.ts:23:13
SVG Styler: Design options: 
Object { size: 256, margin: 10, error_correction: "M", pattern: "diagonal" }
svgStyler.ts:24:13
SVG Styler: Using default background color svgStyler.ts:48:17
SVG Styler: Using default foreground color svgStyler.ts:80:17
SVG Styler: Applying pattern styling: diagonal svgStyler.ts:94:17
Finder pattern detection - backend classes found: 
Object { markerOuters: 3, markerInners: 3, markerDots: 3 }
svgStyler.ts:232:13
Backend finder pattern 1: 
Object { outer: {…}, inner: {…}, dot: {…} }
svgStyler.ts:260:21
Backend finder pattern 2: 
Object { outer: {…}, inner: {…}, dot: {…} }
svgStyler.ts:260:21
Backend finder pattern 3: 
Object { outer: {…}, inner: {…}, dot: {…} }
svgStyler.ts:260:21
Finder pattern detection summary: 
Object { patternsFound: 3, method: "backend-classes" }
svgStyler.ts:343:13
Pattern styling: 
Object { pattern: "diagonal", totalRects: 1126, finderElements: 9, dataModules: 1117, markerElements: 9 }
svgStyler.ts:369:13
SVG Styler: Running finder pattern detection... svgStyler.ts:100:13
Finder pattern detection - backend classes found: 
Object { markerOuters: 3, markerInners: 3, markerDots: 3 }
svgStyler.ts:232:13
Backend finder pattern 1: 
Object { outer: {…}, inner: {…}, dot: {…} }
svgStyler.ts:260:21
Backend finder pattern 2: 
Object { outer: {…}, inner: {…}, dot: {…} }
svgStyler.ts:260:21
Backend finder pattern 3: 
Object { outer: {…}, inner: {…}, dot: {…} }
svgStyler.ts:260:21
Finder pattern detection summary: 
Object { patternsFound: 3, method: "backend-classes" }
svgStyler.ts:343:13
SVG Styler: Finder patterns detected: 3 svgStyler.ts:102:13
SVG Styler: Final SVG length: 174918 svgStyler.ts:129:13
Styled SVG: <svg baseProfile="full" height="300" version="1.1" width="300" xmlns="http://www.w3.org/2000/svg" xmlns:ev="http://www.w3.org/2001/xml-events" xmlns:xlink="http://www.w3.org/1999/xlink"><defs/><rect c... QRPreview.tsx:193:41
QRPreview: Content key updated 
Object { qrType: "scooter_unlock", dataChanged: true, designChanged: true, logoChanged: false, useSvg: true }
QRPreview.tsx:51:21
QRPreview: Content key updated  
Object { qrType: "scooter_unlock", dataChanged: true, designChanged: true, logoChanged: false, useSvg: true }
dashboard:1:596
QRPreview: Generating basic SVG for frontend styling QRPreview.tsx:108:37
QRPreview: Verifying data for scooter_unlock 
Object { scooter_id: "SCOOT123", unlock_code: "UNLOCK42", title: "Scooter_unlock QR Code", description: "Scan to view scooter_unlock content" }
QRPreview.tsx:260:17
QRPreview: Verified data 
Object { scooter_id: "SCOOT123", unlock_code: "UNLOCK42", title: "Scooter_unlock QR Code", description: "Scan to view scooter_unlock content" }
QRPreview.tsx:364:17
Frontend QR preview SVG request: 
Object { qrType: "scooter_unlock", dataSize: 135, designOptions: (6) […], hasLogo: false, dimensions: 300 }
qrService.modern.ts:376:21
Cleaned content for SVG preview: 
Object { scooter_id: "SCOOT123", unlock_code: "UNLOCK42", title: "Scooter_unlock QR Code", description: "Scan to view scooter_unlock content" }
qrService.modern.ts:396:21
[API] Request: POST /qr/preview/svg modernClient.ts:27:24
[API] Added auth token to request modernClient.ts:27:24
Frontend QR preview SVG request: 
Object { qrType: "scooter_unlock", dataSize: 135, designOptions: (32) […], hasLogo: false, dimensions: 300 }
qrService.modern.ts:376:21
Cleaned content for SVG preview: 
Object { scooter_id: "SCOOT123", unlock_code: "UNLOCK42", title: "Scooter_unlock QR Code", description: "Scan to view scooter_unlock content" }
qrService.modern.ts:396:21
[API] Request: POST /qr/preview/svg modernClient.ts:27:24
[API] Added auth token to request modernClient.ts:27:24
[API] Response: 200 OK - POST /qr/preview/svg modernClient.ts:27:24
Successfully received SVG QR preview qrService.modern.ts:429:25
Raw SVG from backend: <svg baseProfile="full" height="300" version="1.1" width="300" xmlns="http://www.w3.org/2000/svg" xmlns:ev="http://www.w3.org/2001/xml-events" xmlns:xlink="http://www.w3.org/1999/xlink"><defs /><rect class="qr-bg" fill="#FFFFFF" height="300" id="qr-bg" width="300" x="0" y="0" /><rect class="qr-module" fill="#000000" height="5.918367346938775" id="module-8-0" width="5.918367346938775" x="52.3469387755102" y="5.0" /><rect class="qr-module" fill="#000000" height="5.918367346938775" id="module-9-0" ... QRPreview.tsx:127:37
SVG length: 176046 QRPreview.tsx:128:37
SVG contains <svg>: true QRPreview.tsx:129:37
Raw SVG structure: 
Object { rectCount: 1127, pathCount: 0 }
QRPreview.tsx:133:37
SVG analysis: 
Object { hasRect: true, hasPath: false, hasCircle: false, hasBackground: true, hasForeground: true, totalRects: 1127, largeRects: 7, finderPatternsDetected: true, originalDesignOptions: {…}, designOptionsKeys: (21) […] }
QRPreview.tsx:163:37
Large rectangles (potential finder patterns): 
Array(5) [ '<rect class="qr-bg" fill="#FFFFFF" height="300" id="qr-bg" width="300" x="0" y="0" />', '<rect class="qr-marker-outer" fill="#000000" height="41.42857142857143" width="41.42857142857143" x="5.0" y="5.0" />', '<rect class="qr-marker-inner" fill="#fff" height="27.224489795918366" width="27.224489795918366" x="12.10204081632653" y="12.10204081632653" />', '<rect class="qr-marker-outer" fill="#000000" height="41.42857142857143" width="41.42857142857143" x="253.57142857142856" y="5.0" />', '<rect class="qr-marker-inner" fill="#fff" height="27.224489795918366" width="27.224489795918366" x="260.67346938775506" y="12.10204081632653" />' ]
QRPreview.tsx:177:41
Original design options: 
Object { size: 256, margin: 10, pattern: "tiny-dots", marker_border_style: "square", marker_border_color: "#000000", marker_center_style: "square", marker_center_color: "#000000", logo_size: 48, logo_position: "center", logo_opacity: 1, … }
QRPreview.tsx:183:37
Filtered design options for basic preview: 
Object { size: 256, margin: 10, error_correction: "M", pattern: "tiny-dots" }
QRPreview.tsx:184:37
Filtered options count: 4 QRPreview.tsx:185:37
SVG Styler: Parsing SVG, element count: 1122 svgStyler.ts:23:13
SVG Styler: Design options: 
Object { size: 256, margin: 10, error_correction: "M", pattern: "tiny-dots" }
svgStyler.ts:24:13
SVG Styler: Using default background color svgStyler.ts:48:17
SVG Styler: Using default foreground color svgStyler.ts:80:17
SVG Styler: Applying pattern styling: tiny-dots svgStyler.ts:94:17
Finder pattern detection - backend classes found: 
Object { markerOuters: 3, markerInners: 3, markerDots: 3 }
svgStyler.ts:232:13
Backend finder pattern 1: 
Object { outer: {…}, inner: {…}, dot: {…} }
svgStyler.ts:260:21
Backend finder pattern 2: 
Object { outer: {…}, inner: {…}, dot: {…} }
svgStyler.ts:260:21
Backend finder pattern 3: 
Object { outer: {…}, inner: {…}, dot: {…} }
svgStyler.ts:260:21
Finder pattern detection summary: 
Object { patternsFound: 3, method: "backend-classes" }
svgStyler.ts:343:13
Pattern styling: 
Object { pattern: "tiny-dots", totalRects: 1126, finderElements: 9, dataModules: 1117, markerElements: 9 }
svgStyler.ts:369:13
SVG Styler: Running finder pattern detection... svgStyler.ts:100:13
Finder pattern detection - backend classes found: 
Object { markerOuters: 3, markerInners: 3, markerDots: 3 }
svgStyler.ts:232:13
Backend finder pattern 1: 
Object { outer: {…}, inner: {…}, dot: {…} }
svgStyler.ts:260:21
Backend finder pattern 2: 
Object { outer: {…}, inner: {…}, dot: {…} }
svgStyler.ts:260:21
Backend finder pattern 3: 
Object { outer: {…}, inner: {…}, dot: {…} }
svgStyler.ts:260:21
Finder pattern detection summary: 
Object { patternsFound: 3, method: "backend-classes" }
svgStyler.ts:343:13
SVG Styler: Finder patterns detected: 3 svgStyler.ts:102:13
SVG Styler: Final SVG length: 174918 svgStyler.ts:129:13
Styled SVG: <svg baseProfile="full" height="300" version="1.1" width="300" xmlns="http://www.w3.org/2000/svg" xmlns:ev="http://www.w3.org/2001/xml-events" xmlns:xlink="http://www.w3.org/1999/xlink"><defs/><rect c... QRPreview.tsx:193:41
[API] Response: 200 OK - POST /qr/preview/svg modernClient.ts:27:24
Successfully received SVG QR preview qrService.modern.ts:429:25
QRPreview: Content key updated 
Object { qrType: "scooter_unlock", dataChanged: true, designChanged: true, logoChanged: false, useSvg: true }
QRPreview.tsx:51:21
QRPreview: Content key updated  
Object { qrType: "scooter_unlock", dataChanged: true, designChanged: true, logoChanged: false, useSvg: true }
dashboard:1:596
QRPreview: Generating basic SVG for frontend styling QRPreview.tsx:108:37
QRPreview: Verifying data for scooter_unlock 
Object { scooter_id: "SCOOT123", unlock_code: "UNLOCK42", title: "Scooter_unlock QR Code", description: "Scan to view scooter_unlock content" }
QRPreview.tsx:260:17
QRPreview: Verified data 
Object { scooter_id: "SCOOT123", unlock_code: "UNLOCK42", title: "Scooter_unlock QR Code", description: "Scan to view scooter_unlock content" }
QRPreview.tsx:364:17
Frontend QR preview SVG request: 
Object { qrType: "scooter_unlock", dataSize: 135, designOptions: (6) […], hasLogo: false, dimensions: 300 }
qrService.modern.ts:376:21
Cleaned content for SVG preview: 
Object { scooter_id: "SCOOT123", unlock_code: "UNLOCK42", title: "Scooter_unlock QR Code", description: "Scan to view scooter_unlock content" }
qrService.modern.ts:396:21
[API] Request: POST /qr/preview/svg modernClient.ts:27:24
[API] Added auth token to request modernClient.ts:27:24
Frontend QR preview SVG request: 
Object { qrType: "scooter_unlock", dataSize: 135, designOptions: (32) […], hasLogo: false, dimensions: 300 }
qrService.modern.ts:376:21
Cleaned content for SVG preview: 
Object { scooter_id: "SCOOT123", unlock_code: "UNLOCK42", title: "Scooter_unlock QR Code", description: "Scan to view scooter_unlock content" }
qrService.modern.ts:396:21
[API] Request: POST /qr/preview/svg modernClient.ts:27:24
[API] Added auth token to request modernClient.ts:27:24
[API] Response: 200 OK - POST /qr/preview/svg modernClient.ts:27:24
Successfully received SVG QR preview qrService.modern.ts:429:25
[API] Response: 200 OK - POST /qr/preview/svg modernClient.ts:27:24
Successfully received SVG QR preview qrService.modern.ts:429:25
Raw SVG from backend: <svg baseProfile="full" height="300" version="1.1" width="300" xmlns="http://www.w3.org/2000/svg" xmlns:ev="http://www.w3.org/2001/xml-events" xmlns:xlink="http://www.w3.org/1999/xlink"><defs /><rect class="qr-bg" fill="#FFFFFF" height="300" id="qr-bg" width="300" x="0" y="0" /><rect class="qr-module" fill="#000000" height="5.918367346938775" id="module-8-0" width="5.918367346938775" x="52.3469387755102" y="5.0" /><rect class="qr-module" fill="#000000" height="5.918367346938775" id="module-9-0" ... QRPreview.tsx:127:37
SVG length: 176046 QRPreview.tsx:128:37
SVG contains <svg>: true QRPreview.tsx:129:37
Raw SVG structure: 
Object { rectCount: 1127, pathCount: 0 }
QRPreview.tsx:133:37
SVG analysis: 
Object { hasRect: true, hasPath: false, hasCircle: false, hasBackground: true, hasForeground: true, totalRects: 1127, largeRects: 7, finderPatternsDetected: true, originalDesignOptions: {…}, designOptionsKeys: (21) […] }
QRPreview.tsx:163:37
Large rectangles (potential finder patterns): 
Array(5) [ '<rect class="qr-bg" fill="#FFFFFF" height="300" id="qr-bg" width="300" x="0" y="0" />', '<rect class="qr-marker-outer" fill="#000000" height="41.42857142857143" width="41.42857142857143" x="5.0" y="5.0" />', '<rect class="qr-marker-inner" fill="#fff" height="27.224489795918366" width="27.224489795918366" x="12.10204081632653" y="12.10204081632653" />', '<rect class="qr-marker-outer" fill="#000000" height="41.42857142857143" width="41.42857142857143" x="253.57142857142856" y="5.0" />', '<rect class="qr-marker-inner" fill="#fff" height="27.224489795918366" width="27.224489795918366" x="260.67346938775506" y="12.10204081632653" />' ]
QRPreview.tsx:177:41
Original design options: 
Object { size: 256, margin: 10, pattern: "classy-rounded", marker_border_style: "square", marker_border_color: "#000000", marker_center_style: "square", marker_center_color: "#000000", logo_size: 48, logo_position: "center", logo_opacity: 1, … }
QRPreview.tsx:183:37
Filtered design options for basic preview: 
Object { size: 256, margin: 10, error_correction: "M", pattern: "classy-rounded" }
QRPreview.tsx:184:37
Filtered options count: 4 QRPreview.tsx:185:37
SVG Styler: Parsing SVG, element count: 1122 svgStyler.ts:23:13
SVG Styler: Design options: 
Object { size: 256, margin: 10, error_correction: "M", pattern: "classy-rounded" }
svgStyler.ts:24:13
SVG Styler: Using default background color svgStyler.ts:48:17
SVG Styler: Using default foreground color svgStyler.ts:80:17
SVG Styler: Applying pattern styling: classy-rounded svgStyler.ts:94:17
Finder pattern detection - backend classes found: 
Object { markerOuters: 3, markerInners: 3, markerDots: 3 }
svgStyler.ts:232:13
Backend finder pattern 1: 
Object { outer: {…}, inner: {…}, dot: {…} }
svgStyler.ts:260:21
Backend finder pattern 2: 
Object { outer: {…}, inner: {…}, dot: {…} }
svgStyler.ts:260:21
Backend finder pattern 3: 
Object { outer: {…}, inner: {…}, dot: {…} }
svgStyler.ts:260:21
Finder pattern detection summary: 
Object { patternsFound: 3, method: "backend-classes" }
svgStyler.ts:343:13
Pattern styling: 
Object { pattern: "classy-rounded", totalRects: 1126, finderElements: 9, dataModules: 1117, markerElements: 9 }
svgStyler.ts:369:13
SVG Styler: Running finder pattern detection... svgStyler.ts:100:13
Finder pattern detection - backend classes found: 
Object { markerOuters: 3, markerInners: 3, markerDots: 3 }
svgStyler.ts:232:13
Backend finder pattern 1: 
Object { outer: {…}, inner: {…}, dot: {…} }
svgStyler.ts:260:21
Backend finder pattern 2: 
Object { outer: {…}, inner: {…}, dot: {…} }
svgStyler.ts:260:21
Backend finder pattern 3: 
Object { outer: {…}, inner: {…}, dot: {…} }
svgStyler.ts:260:21
Finder pattern detection summary: 
Object { patternsFound: 3, method: "backend-classes" }
svgStyler.ts:343:13
SVG Styler: Finder patterns detected: 3 svgStyler.ts:102:13
SVG Styler: Final SVG length: 174918 svgStyler.ts:129:13
Styled SVG: <svg baseProfile="full" height="300" version="1.1" width="300" xmlns="http://www.w3.org/2000/svg" xmlns:ev="http://www.w3.org/2001/xml-events" xmlns:xlink="http://www.w3.org/1999/xlink"><defs/><rect c... QRPreview.tsx:193:41
QRPreview: Content key updated 
Object { qrType: "scooter_unlock", dataChanged: true, designChanged: true, logoChanged: false, useSvg: true }
QRPreview.tsx:51:21
QRPreview: Content key updated  
Object { qrType: "scooter_unlock", dataChanged: true, designChanged: true, logoChanged: false, useSvg: true }
dashboard:1:596
QRPreview: Generating basic SVG for frontend styling QRPreview.tsx:108:37
QRPreview: Verifying data for scooter_unlock 
Object { scooter_id: "SCOOT123", unlock_code: "UNLOCK42", title: "Scooter_unlock QR Code", description: "Scan to view scooter_unlock content" }
QRPreview.tsx:260:17
QRPreview: Verified data 
Object { scooter_id: "SCOOT123", unlock_code: "UNLOCK42", title: "Scooter_unlock QR Code", description: "Scan to view scooter_unlock content" }
QRPreview.tsx:364:17
Frontend QR preview SVG request: 
Object { qrType: "scooter_unlock", dataSize: 135, designOptions: (6) […], hasLogo: false, dimensions: 300 }
qrService.modern.ts:376:21
Cleaned content for SVG preview: 
Object { scooter_id: "SCOOT123", unlock_code: "UNLOCK42", title: "Scooter_unlock QR Code", description: "Scan to view scooter_unlock content" }
qrService.modern.ts:396:21
[API] Request: POST /qr/preview/svg modernClient.ts:27:24
[API] Added auth token to request modernClient.ts:27:24
Frontend QR preview SVG request: 
Object { qrType: "scooter_unlock", dataSize: 135, designOptions: (32) […], hasLogo: false, dimensions: 300 }
qrService.modern.ts:376:21
Cleaned content for SVG preview: 
Object { scooter_id: "SCOOT123", unlock_code: "UNLOCK42", title: "Scooter_unlock QR Code", description: "Scan to view scooter_unlock content" }
qrService.modern.ts:396:21
[API] Request: POST /qr/preview/svg modernClient.ts:27:24
[API] Added auth token to request modernClient.ts:27:24
[API] Response: 200 OK - POST /qr/preview/svg modernClient.ts:27:24
Successfully received SVG QR preview qrService.modern.ts:429:25
Raw SVG from backend: <svg baseProfile="full" height="300" version="1.1" width="300" xmlns="http://www.w3.org/2000/svg" xmlns:ev="http://www.w3.org/2001/xml-events" xmlns:xlink="http://www.w3.org/1999/xlink"><defs /><rect class="qr-bg" fill="#FFFFFF" height="300" id="qr-bg" width="300" x="0" y="0" /><rect class="qr-module" fill="#000000" height="5.918367346938775" id="module-8-0" width="5.918367346938775" x="52.3469387755102" y="5.0" /><rect class="qr-module" fill="#000000" height="5.918367346938775" id="module-9-0" ... QRPreview.tsx:127:37
SVG length: 176046 QRPreview.tsx:128:37
SVG contains <svg>: true QRPreview.tsx:129:37
Raw SVG structure: 
Object { rectCount: 1127, pathCount: 0 }
QRPreview.tsx:133:37
SVG analysis: 
Object { hasRect: true, hasPath: false, hasCircle: false, hasBackground: true, hasForeground: true, totalRects: 1127, largeRects: 7, finderPatternsDetected: true, originalDesignOptions: {…}, designOptionsKeys: (21) […] }
QRPreview.tsx:163:37
Large rectangles (potential finder patterns): 
Array(5) [ '<rect class="qr-bg" fill="#FFFFFF" height="300" id="qr-bg" width="300" x="0" y="0" />', '<rect class="qr-marker-outer" fill="#000000" height="41.42857142857143" width="41.42857142857143" x="5.0" y="5.0" />', '<rect class="qr-marker-inner" fill="#fff" height="27.224489795918366" width="27.224489795918366" x="12.10204081632653" y="12.10204081632653" />', '<rect class="qr-marker-outer" fill="#000000" height="41.42857142857143" width="41.42857142857143" x="253.57142857142856" y="5.0" />', '<rect class="qr-marker-inner" fill="#fff" height="27.224489795918366" width="27.224489795918366" x="260.67346938775506" y="12.10204081632653" />' ]
QRPreview.tsx:177:41
Original design options: 
Object { size: 256, margin: 10, pattern: "square", marker_border_style: "square", marker_border_color: "#000000", marker_center_style: "square", marker_center_color: "#000000", logo_size: 48, logo_position: "center", logo_opacity: 1, … }
QRPreview.tsx:183:37
Filtered design options for basic preview: 
Object { size: 256, margin: 10, error_correction: "M" }
QRPreview.tsx:184:37
Filtered options count: 3 QRPreview.tsx:185:37
SVG Styler: Parsing SVG, element count: 1122 svgStyler.ts:23:13
SVG Styler: Design options: 
Object { size: 256, margin: 10, error_correction: "M" }
svgStyler.ts:24:13
SVG Styler: Using default background color svgStyler.ts:48:17
SVG Styler: Using default foreground color svgStyler.ts:80:17
SVG Styler: Using default square pattern svgStyler.ts:97:17
SVG Styler: Running finder pattern detection... svgStyler.ts:100:13
Finder pattern detection - backend classes found: 
Object { markerOuters: 3, markerInners: 3, markerDots: 3 }
svgStyler.ts:232:13
Backend finder pattern 1: 
Object { outer: {…}, inner: {…}, dot: {…} }
svgStyler.ts:260:21
Backend finder pattern 2: 
Object { outer: {…}, inner: {…}, dot: {…} }
svgStyler.ts:260:21
Backend finder pattern 3: 
Object { outer: {…}, inner: {…}, dot: {…} }
svgStyler.ts:260:21
Finder pattern detection summary: 
Object { patternsFound: 3, method: "backend-classes" }
svgStyler.ts:343:13
SVG Styler: Finder patterns detected: 3 svgStyler.ts:102:13
SVG Styler: Final SVG length: 174918 svgStyler.ts:129:13
Styled SVG: <svg baseProfile="full" height="300" version="1.1" width="300" xmlns="http://www.w3.org/2000/svg" xmlns:ev="http://www.w3.org/2001/xml-events" xmlns:xlink="http://www.w3.org/1999/xlink"><defs/><rect c... QRPreview.tsx:193:41
[API] Response: 200 OK - POST /qr/preview/svg modernClient.ts:27:24
Successfully received SVG QR preview qrService.modern.ts:429:25
QRPreview: Content key updated 
Object { qrType: "scooter_unlock", dataChanged: true, designChanged: true, logoChanged: false, useSvg: true }
QRPreview.tsx:51:21
QRPreview: Content key updated  
Object { qrType: "scooter_unlock", dataChanged: true, designChanged: true, logoChanged: false, useSvg: true }
dashboard:1:596
QRPreview: Generating basic SVG for frontend styling QRPreview.tsx:108:37
QRPreview: Verifying data for scooter_unlock 
Object { scooter_id: "SCOOT123", unlock_code: "UNLOCK42", title: "Scooter_unlock QR Code", description: "Scan to view scooter_unlock content" }
QRPreview.tsx:260:17
QRPreview: Verified data 
Object { scooter_id: "SCOOT123", unlock_code: "UNLOCK42", title: "Scooter_unlock QR Code", description: "Scan to view scooter_unlock content" }
QRPreview.tsx:364:17
Frontend QR preview SVG request: 
Object { qrType: "scooter_unlock", dataSize: 135, designOptions: (6) […], hasLogo: false, dimensions: 300 }
qrService.modern.ts:376:21
Cleaned content for SVG preview: 
Object { scooter_id: "SCOOT123", unlock_code: "UNLOCK42", title: "Scooter_unlock QR Code", description: "Scan to view scooter_unlock content" }
qrService.modern.ts:396:21
[API] Request: POST /qr/preview/svg modernClient.ts:27:24
[API] Added auth token to request modernClient.ts:27:24
Frontend QR preview SVG request: 
Object { qrType: "scooter_unlock", dataSize: 135, designOptions: (32) […], hasLogo: false, dimensions: 300 }
qrService.modern.ts:376:21
Cleaned content for SVG preview: 
Object { scooter_id: "SCOOT123", unlock_code: "UNLOCK42", title: "Scooter_unlock QR Code", description: "Scan to view scooter_unlock content" }
qrService.modern.ts:396:21
[API] Request: POST /qr/preview/svg modernClient.ts:27:24
[API] Added auth token to request modernClient.ts:27:24
[API] Response: 200 OK - POST /qr/preview/svg modernClient.ts:27:24
Successfully received SVG QR preview qrService.modern.ts:429:25
Raw SVG from backend: <svg baseProfile="full" height="300" version="1.1" width="300" xmlns="http://www.w3.org/2000/svg" xmlns:ev="http://www.w3.org/2001/xml-events" xmlns:xlink="http://www.w3.org/1999/xlink"><defs /><rect class="qr-bg" fill="#FFFFFF" height="300" id="qr-bg" width="300" x="0" y="0" /><rect class="qr-module" fill="#000000" height="5.918367346938775" id="module-8-0" width="5.918367346938775" x="52.3469387755102" y="5.0" /><rect class="qr-module" fill="#000000" height="5.918367346938775" id="module-9-0" ... QRPreview.tsx:127:37
SVG length: 176046 QRPreview.tsx:128:37
SVG contains <svg>: true QRPreview.tsx:129:37
Raw SVG structure: 
Object { rectCount: 1127, pathCount: 0 }
QRPreview.tsx:133:37
SVG analysis: 
Object { hasRect: true, hasPath: false, hasCircle: false, hasBackground: true, hasForeground: true, totalRects: 1127, largeRects: 7, finderPatternsDetected: true, originalDesignOptions: {…}, designOptionsKeys: (21) […] }
QRPreview.tsx:163:37
Large rectangles (potential finder patterns): 
Array(5) [ '<rect class="qr-bg" fill="#FFFFFF" height="300" id="qr-bg" width="300" x="0" y="0" />', '<rect class="qr-marker-outer" fill="#000000" height="41.42857142857143" width="41.42857142857143" x="5.0" y="5.0" />', '<rect class="qr-marker-inner" fill="#fff" height="27.224489795918366" width="27.224489795918366" x="12.10204081632653" y="12.10204081632653" />', '<rect class="qr-marker-outer" fill="#000000" height="41.42857142857143" width="41.42857142857143" x="253.57142857142856" y="5.0" />', '<rect class="qr-marker-inner" fill="#fff" height="27.224489795918366" width="27.224489795918366" x="260.67346938775506" y="12.10204081632653" />' ]
QRPreview.tsx:177:41
Original design options: 
Object { size: 256, margin: 10, pattern: "rounded", marker_border_style: "square", marker_border_color: "#000000", marker_center_style: "square", marker_center_color: "#000000", logo_size: 48, logo_position: "center", logo_opacity: 1, … }
QRPreview.tsx:183:37
Filtered design options for basic preview: 
Object { size: 256, margin: 10, error_correction: "M", pattern: "rounded" }
QRPreview.tsx:184:37
Filtered options count: 4 QRPreview.tsx:185:37
SVG Styler: Parsing SVG, element count: 1122 svgStyler.ts:23:13
SVG Styler: Design options: 
Object { size: 256, margin: 10, error_correction: "M", pattern: "rounded" }
svgStyler.ts:24:13
SVG Styler: Using default background color svgStyler.ts:48:17
SVG Styler: Using default foreground color svgStyler.ts:80:17
SVG Styler: Applying pattern styling: rounded svgStyler.ts:94:17
Finder pattern detection - backend classes found: 
Object { markerOuters: 3, markerInners: 3, markerDots: 3 }
svgStyler.ts:232:13
Backend finder pattern 1: 
Object { outer: {…}, inner: {…}, dot: {…} }
svgStyler.ts:260:21
Backend finder pattern 2: 
Object { outer: {…}, inner: {…}, dot: {…} }
svgStyler.ts:260:21
Backend finder pattern 3: 
Object { outer: {…}, inner: {…}, dot: {…} }
svgStyler.ts:260:21
Finder pattern detection summary: 
Object { patternsFound: 3, method: "backend-classes" }
svgStyler.ts:343:13
Pattern styling: 
Object { pattern: "rounded", totalRects: 1126, finderElements: 9, dataModules: 1117, markerElements: 9 }
svgStyler.ts:369:13
SVG Styler: Running finder pattern detection... svgStyler.ts:100:13
Finder pattern detection - backend classes found: 
Object { markerOuters: 3, markerInners: 3, markerDots: 3 }
svgStyler.ts:232:13
Backend finder pattern 1: 
Object { outer: {…}, inner: {…}, dot: {…} }
svgStyler.ts:260:21
Backend finder pattern 2: 
Object { outer: {…}, inner: {…}, dot: {…} }
svgStyler.ts:260:21
Backend finder pattern 3: 
Object { outer: {…}, inner: {…}, dot: {…} }
svgStyler.ts:260:21
Finder pattern detection summary: 
Object { patternsFound: 3, method: "backend-classes" }
svgStyler.ts:343:13
SVG Styler: Finder patterns detected: 3 svgStyler.ts:102:13
SVG Styler: Final SVG length: 228534 svgStyler.ts:129:13
Styled SVG: <svg baseProfile="full" height="300" version="1.1" width="300" xmlns="http://www.w3.org/2000/svg" xmlns:ev="http://www.w3.org/2001/xml-events" xmlns:xlink="http://www.w3.org/1999/xlink"><defs/><rect c... QRPreview.tsx:193:41
[API] Response: 200 OK - POST /qr/preview/svg modernClient.ts:27:24
Successfully received SVG QR preview qrService.modern.ts:429:25
[Auth] Adding Bearer token to request: /health layout.tsx:133:49
Source map error: Error: request failed with status 404
Stack in the worker:networkRequest@resource://devtools/client/shared/source-map-loader/utils/network-request.js:43:9

Resource URL: null
Source Map URL: installHook.js.map
