This site appears to use a scroll-linked positioning effect. This may not work well with asynchronous panning; see https://firefox-source-docs.mozilla.org/performance/scroll-linked_effects.html for further details and to join the discussion on related tools and features! creator
Filtered options count: 6 QRPreview.tsx:185:37
SVG Styler: Parsing SVG, element count: 1560 svgStyler.ts:23:13
SVG Styler: Design options: 
Object { size: 256, margin: 10, error_correction: "M", foreground_color: "#210404", background_color: "#bb0000", pattern: "diamond" }
svgStyler.ts:24:13
SVG Styler: Applied background color: #bb0000 svgStyler.ts:46:17
SVG Styler: Found 1565 QR modules to style with foreground color svgStyler.ts:55:17
SVG Styler: Module 1 (rect) - changed fill from "#000000" to "#210404" svgStyler.ts:75:29
SVG Styler: Module 2 (rect) - changed fill from "#000000" to "#210404" svgStyler.ts:75:29
SVG Styler: Module 3 (rect) - changed fill from "#000000" to "#210404" svgStyler.ts:75:29
SVG Styler: Module 4 (rect) - changed fill from "#000000" to "#210404" svgStyler.ts:75:29
SVG Styler: Applying pattern styling: diamond svgStyler.ts:94:17
Finder pattern detection - backend classes found: 
Object { markerOuters: 3, markerInners: 3, markerDots: 3 }
svgStyler.ts:232:13
Backend finder pattern 1: 
Object { outer: {…}, inner: {…}, dot: {…} }
svgStyler.ts:260:21
Backend finder pattern 2: 
Object { outer: {…}, inner: {…}, dot: {…} }
svgStyler.ts:260:21
Backend finder pattern 3: 
Object { outer: {…}, inner: {…}, dot: {…} }
svgStyler.ts:260:21
Finder pattern detection summary: 
Object { patternsFound: 3, method: "backend-classes" }
svgStyler.ts:343:13
Pattern styling: 
Object { pattern: "diamond", totalRects: 1564, finderElements: 9, dataModules: 1555, markerElements: 9 }
svgStyler.ts:369:13
SVG Styler: Running finder pattern detection... svgStyler.ts:100:13
Finder pattern detection - backend classes found: 
Object { markerOuters: 3, markerInners: 3, markerDots: 3 }
svgStyler.ts:232:13
Backend finder pattern 1: 
Object { outer: {…}, inner: {…}, dot: {…} }
svgStyler.ts:260:21
Backend finder pattern 2: 
Object { outer: {…}, inner: {…}, dot: {…} }
svgStyler.ts:260:21
Backend finder pattern 3: 
Object { outer: {…}, inner: {…}, dot: {…} }
svgStyler.ts:260:21
Finder pattern detection summary: 
Object { patternsFound: 3, method: "backend-classes" }
svgStyler.ts:343:13
SVG Styler: Finder patterns detected: 3 svgStyler.ts:102:13
SVG Styler: Final SVG length: 284401 svgStyler.ts:129:13
Styled SVG: <svg baseProfile="full" height="300" version="1.1" width="300" xmlns="http://www.w3.org/2000/svg" xmlns:ev="http://www.w3.org/2001/xml-events" xmlns:xlink="http://www.w3.org/1999/xlink"><rect class="q... QRPreview.tsx:193:41
[API] Response: 200 OK - POST /qr/preview/svg modernClient.ts:27:24
Successfully received SVG QR preview qrService.modern.ts:429:25
QRPreview: Content key updated 
Object { qrType: "scooter_unlock", dataChanged: true, designChanged: true, logoChanged: true, useSvg: true }
QRPreview.tsx:51:21
QRPreview: Content key updated  
Object { qrType: "scooter_unlock", dataChanged: true, designChanged: true, logoChanged: true, useSvg: true }
dashboard:1:596
QRPreview: Generating basic SVG for frontend styling QRPreview.tsx:108:37
QRPreview: Verifying data for scooter_unlock 
Object { scooter_id: "SCOOT123", unlock_code: "UNLOCK42", title: "Scooter_unlock QR Code", description: "Scan to view scooter_unlock content", service_provider: "lime", unlock_url: "https://tamensah.com" }
QRPreview.tsx:260:17
QRPreview: Verified data 
Object { scooter_id: "SCOOT123", unlock_code: "UNLOCK42", title: "Scooter_unlock QR Code", description: "Scan to view scooter_unlock content", service_provider: "lime", unlock_url: "https://tamensah.com" }
QRPreview.tsx:364:17
Frontend QR preview SVG request: 
Object { qrType: "scooter_unlock", dataSize: 197, designOptions: (6) […], hasLogo: true, dimensions: 300 }
qrService.modern.ts:376:21
Cleaned content for SVG preview: 
Object { scooter_id: "SCOOT123", unlock_code: "UNLOCK42", title: "Scooter_unlock QR Code", description: "Scan to view scooter_unlock content", service_provider: "lime", unlock_url: "https://tamensah.com" }
qrService.modern.ts:396:21
[API] Request: POST /qr/preview/svg modernClient.ts:27:24
[API] Added auth token to request modernClient.ts:27:24
Frontend QR preview SVG request: 
Object { qrType: "scooter_unlock", dataSize: 197, designOptions: (32) […], hasLogo: true, dimensions: 300 }
qrService.modern.ts:376:21
Cleaned content for SVG preview: 
Object { scooter_id: "SCOOT123", unlock_code: "UNLOCK42", title: "Scooter_unlock QR Code", description: "Scan to view scooter_unlock content", service_provider: "lime", unlock_url: "https://tamensah.com" }
qrService.modern.ts:396:21
[API] Request: POST /qr/preview/svg modernClient.ts:27:24
[API] Added auth token to request modernClient.ts:27:24
QRPreview: Content key updated 
Object { qrType: "scooter_unlock", dataChanged: true, designChanged: true, logoChanged: true, useSvg: true }
QRPreview.tsx:51:21
QRPreview: Content key updated  
Object { qrType: "scooter_unlock", dataChanged: true, designChanged: true, logoChanged: true, useSvg: true }
dashboard:1:596
[API] Response: 200 OK - POST /qr/preview/svg modernClient.ts:27:24
Successfully received SVG QR preview qrService.modern.ts:429:25
[API] Response: 200 OK - POST /qr/preview/svg modernClient.ts:27:24
Successfully received SVG QR preview qrService.modern.ts:429:25
Raw SVG from backend: <svg baseProfile="full" height="300" version="1.1" width="300" xmlns="http://www.w3.org/2000/svg" xmlns:ev="http://www.w3.org/2001/xml-events" xmlns:xlink="http://www.w3.org/1999/xlink"><defs /><rect class="qr-bg" fill="#FFFFFF" height="300" id="qr-bg" width="300" x="0" y="0" /><rect class="qr-module" fill="#000000" height="5.087719298245614" id="module-9-0" width="5.087719298245614" x="50.78947368421053" y="5.0" /><rect class="qr-module" fill="#000000" height="5.087719298245614" id="module-12-0... QRPreview.tsx:127:37
SVG length: 244975 QRPreview.tsx:128:37
SVG contains <svg>: true QRPreview.tsx:129:37
Raw SVG structure: 
Object { rectCount: 1565, pathCount: 0 }
QRPreview.tsx:133:37
SVG analysis: 
Object { hasRect: true, hasPath: false, hasCircle: false, hasBackground: true, hasForeground: true, totalRects: 1565, largeRects: 7, finderPatternsDetected: true, originalDesignOptions: {…}, designOptionsKeys: (21) […] }
QRPreview.tsx:163:37
Large rectangles (potential finder patterns): 
Array(5) [ '<rect class="qr-bg" fill="#FFFFFF" height="300" id="qr-bg" width="300" x="0" y="0" />', '<rect class="qr-marker-outer" fill="#000000" height="35.614035087719294" width="35.614035087719294" x="5.0" y="5.0" />', '<rect class="qr-marker-inner" fill="#fff" height="23.403508771929822" width="23.403508771929822" x="11.105263157894736" y="11.105263157894736" />', '<rect class="qr-marker-outer" fill="#000000" height="35.614035087719294" width="35.614035087719294" x="259.38596491228066" y="5.0" />', '<rect class="qr-marker-inner" fill="#fff" height="23.403508771929822" width="23.403508771929822" x="265.4912280701754" y="11.105263157894736" />' ]
QRPreview.tsx:177:41
Original design options: 
Object { size: 256, margin: 10, pattern: "diamond", marker_border_style: "rays", marker_border_color: "#f21212", marker_center_style: "rounded-square", marker_center_color: "#be1313", logo_size: 48, logo_position: "center", logo_opacity: 1, … }
QRPreview.tsx:183:37
Filtered design options for basic preview: 
Object { size: 256, margin: 10, error_correction: "M", foreground_color: "#210404", background_color: "#bb0000", pattern: "diamond" }
QRPreview.tsx:184:37
Filtered options count: 6 QRPreview.tsx:185:37
SVG Styler: Parsing SVG, element count: 1560 svgStyler.ts:23:13
SVG Styler: Design options: 
Object { size: 256, margin: 10, error_correction: "M", foreground_color: "#210404", background_color: "#bb0000", pattern: "diamond" }
svgStyler.ts:24:13
SVG Styler: Applied background color: #bb0000 svgStyler.ts:46:17
SVG Styler: Found 1565 QR modules to style with foreground color svgStyler.ts:55:17
SVG Styler: Module 1 (rect) - changed fill from "#000000" to "#210404" svgStyler.ts:75:29
SVG Styler: Module 2 (rect) - changed fill from "#000000" to "#210404" svgStyler.ts:75:29
SVG Styler: Module 3 (rect) - changed fill from "#000000" to "#210404" svgStyler.ts:75:29
SVG Styler: Module 4 (rect) - changed fill from "#000000" to "#210404" svgStyler.ts:75:29
SVG Styler: Applying pattern styling: diamond svgStyler.ts:94:17
Finder pattern detection - backend classes found: 
Object { markerOuters: 3, markerInners: 3, markerDots: 3 }
svgStyler.ts:232:13
Backend finder pattern 1: 
Object { outer: {…}, inner: {…}, dot: {…} }
svgStyler.ts:260:21
Backend finder pattern 2: 
Object { outer: {…}, inner: {…}, dot: {…} }
svgStyler.ts:260:21
Backend finder pattern 3: 
Object { outer: {…}, inner: {…}, dot: {…} }
svgStyler.ts:260:21
Finder pattern detection summary: 
Object { patternsFound: 3, method: "backend-classes" }
svgStyler.ts:343:13
Pattern styling: 
Object { pattern: "diamond", totalRects: 1564, finderElements: 9, dataModules: 1555, markerElements: 9 }
svgStyler.ts:369:13
SVG Styler: Running finder pattern detection... svgStyler.ts:100:13
Finder pattern detection - backend classes found: 
Object { markerOuters: 3, markerInners: 3, markerDots: 3 }
svgStyler.ts:232:13
Backend finder pattern 1: 
Object { outer: {…}, inner: {…}, dot: {…} }
svgStyler.ts:260:21
Backend finder pattern 2: 
Object { outer: {…}, inner: {…}, dot: {…} }
svgStyler.ts:260:21
Backend finder pattern 3: 
Object { outer: {…}, inner: {…}, dot: {…} }
svgStyler.ts:260:21
Finder pattern detection summary: 
Object { patternsFound: 3, method: "backend-classes" }
svgStyler.ts:343:13
SVG Styler: Finder patterns detected: 3 svgStyler.ts:102:13
SVG Styler: Final SVG length: 284401 svgStyler.ts:129:13
Styled SVG: <svg baseProfile="full" height="300" version="1.1" width="300" xmlns="http://www.w3.org/2000/svg" xmlns:ev="http://www.w3.org/2001/xml-events" xmlns:xlink="http://www.w3.org/1999/xlink"><rect class="q... QRPreview.tsx:193:41
QRPreview: Generating basic SVG for frontend styling QRPreview.tsx:108:37
QRPreview: Verifying data for scooter_unlock 
Object { scooter_id: "SCOOT123", unlock_code: "UNLOCK42", title: "Scooter_unlock QR Code", description: "Scan to view scooter_unlock content", service_provider: "lime", unlock_url: "https://tamensah.com" }
QRPreview.tsx:260:17
QRPreview: Verified data 
Object { scooter_id: "SCOOT123", unlock_code: "UNLOCK42", title: "Scooter_unlock QR Code", description: "Scan to view scooter_unlock content", service_provider: "lime", unlock_url: "https://tamensah.com" }
QRPreview.tsx:364:17
Frontend QR preview SVG request: 
Object { qrType: "scooter_unlock", dataSize: 197, designOptions: (6) […], hasLogo: true, dimensions: 300 }
qrService.modern.ts:376:21
Cleaned content for SVG preview: 
Object { scooter_id: "SCOOT123", unlock_code: "UNLOCK42", title: "Scooter_unlock QR Code", description: "Scan to view scooter_unlock content", service_provider: "lime", unlock_url: "https://tamensah.com" }
qrService.modern.ts:396:21
[API] Request: POST /qr/preview/svg modernClient.ts:27:24
[API] Added auth token to request modernClient.ts:27:24
Frontend QR preview SVG request: 
Object { qrType: "scooter_unlock", dataSize: 197, designOptions: (32) […], hasLogo: true, dimensions: 300 }
qrService.modern.ts:376:21
Cleaned content for SVG preview: 
Object { scooter_id: "SCOOT123", unlock_code: "UNLOCK42", title: "Scooter_unlock QR Code", description: "Scan to view scooter_unlock content", service_provider: "lime", unlock_url: "https://tamensah.com" }
qrService.modern.ts:396:21
[API] Request: POST /qr/preview/svg modernClient.ts:27:24
[API] Added auth token to request modernClient.ts:27:24
[API] Response: 200 OK - POST /qr/preview/svg modernClient.ts:27:24
Successfully received SVG QR preview qrService.modern.ts:429:25
Raw SVG from backend: <svg baseProfile="full" height="300" version="1.1" width="300" xmlns="http://www.w3.org/2000/svg" xmlns:ev="http://www.w3.org/2001/xml-events" xmlns:xlink="http://www.w3.org/1999/xlink"><defs /><rect class="qr-bg" fill="#FFFFFF" height="300" id="qr-bg" width="300" x="0" y="0" /><rect class="qr-module" fill="#000000" height="5.087719298245614" id="module-9-0" width="5.087719298245614" x="50.78947368421053" y="5.0" /><rect class="qr-module" fill="#000000" height="5.087719298245614" id="module-12-0... QRPreview.tsx:127:37
SVG length: 244975 QRPreview.tsx:128:37
SVG contains <svg>: true QRPreview.tsx:129:37
Raw SVG structure: 
Object { rectCount: 1565, pathCount: 0 }
QRPreview.tsx:133:37
SVG analysis: 
Object { hasRect: true, hasPath: false, hasCircle: false, hasBackground: true, hasForeground: true, totalRects: 1565, largeRects: 7, finderPatternsDetected: true, originalDesignOptions: {…}, designOptionsKeys: (21) […] }
QRPreview.tsx:163:37
Large rectangles (potential finder patterns): 
Array(5) [ '<rect class="qr-bg" fill="#FFFFFF" height="300" id="qr-bg" width="300" x="0" y="0" />', '<rect class="qr-marker-outer" fill="#000000" height="35.614035087719294" width="35.614035087719294" x="5.0" y="5.0" />', '<rect class="qr-marker-inner" fill="#fff" height="23.403508771929822" width="23.403508771929822" x="11.105263157894736" y="11.105263157894736" />', '<rect class="qr-marker-outer" fill="#000000" height="35.614035087719294" width="35.614035087719294" x="259.38596491228066" y="5.0" />', '<rect class="qr-marker-inner" fill="#fff" height="23.403508771929822" width="23.403508771929822" x="265.4912280701754" y="11.105263157894736" />' ]
QRPreview.tsx:177:41
Original design options: 
Object { size: 256, margin: 10, pattern: "diamond", marker_border_style: "zebra", marker_border_color: "#f21212", marker_center_style: "rounded-square", marker_center_color: "#be1313", logo_size: 48, logo_position: "center", logo_opacity: 1, … }
QRPreview.tsx:183:37
Filtered design options for basic preview: 
Object { size: 256, margin: 10, error_correction: "M", foreground_color: "#210404", background_color: "#bb0000", pattern: "diamond" }
QRPreview.tsx:184:37
Filtered options count: 6 QRPreview.tsx:185:37
SVG Styler: Parsing SVG, element count: 1560 svgStyler.ts:23:13
SVG Styler: Design options: 
Object { size: 256, margin: 10, error_correction: "M", foreground_color: "#210404", background_color: "#bb0000", pattern: "diamond" }
svgStyler.ts:24:13
SVG Styler: Applied background color: #bb0000 svgStyler.ts:46:17
SVG Styler: Found 1565 QR modules to style with foreground color svgStyler.ts:55:17
SVG Styler: Module 1 (rect) - changed fill from "#000000" to "#210404" svgStyler.ts:75:29
SVG Styler: Module 2 (rect) - changed fill from "#000000" to "#210404" svgStyler.ts:75:29
SVG Styler: Module 3 (rect) - changed fill from "#000000" to "#210404" svgStyler.ts:75:29
SVG Styler: Module 4 (rect) - changed fill from "#000000" to "#210404" svgStyler.ts:75:29
SVG Styler: Applying pattern styling: diamond svgStyler.ts:94:17
Finder pattern detection - backend classes found: 
Object { markerOuters: 3, markerInners: 3, markerDots: 3 }
svgStyler.ts:232:13
Backend finder pattern 1: 
Object { outer: {…}, inner: {…}, dot: {…} }
svgStyler.ts:260:21
Backend finder pattern 2: 
Object { outer: {…}, inner: {…}, dot: {…} }
svgStyler.ts:260:21
Backend finder pattern 3: 
Object { outer: {…}, inner: {…}, dot: {…} }
svgStyler.ts:260:21
Finder pattern detection summary: 
Object { patternsFound: 3, method: "backend-classes" }
svgStyler.ts:343:13
Pattern styling: 
Object { pattern: "diamond", totalRects: 1564, finderElements: 9, dataModules: 1555, markerElements: 9 }
svgStyler.ts:369:13
SVG Styler: Running finder pattern detection... svgStyler.ts:100:13
Finder pattern detection - backend classes found: 
Object { markerOuters: 3, markerInners: 3, markerDots: 3 }
svgStyler.ts:232:13
Backend finder pattern 1: 
Object { outer: {…}, inner: {…}, dot: {…} }
svgStyler.ts:260:21
Backend finder pattern 2: 
Object { outer: {…}, inner: {…}, dot: {…} }
svgStyler.ts:260:21
Backend finder pattern 3: 
Object { outer: {…}, inner: {…}, dot: {…} }
svgStyler.ts:260:21
Finder pattern detection summary: 
Object { patternsFound: 3, method: "backend-classes" }
svgStyler.ts:343:13
SVG Styler: Finder patterns detected: 3 svgStyler.ts:102:13
SVG Styler: Final SVG length: 284401 svgStyler.ts:129:13
Styled SVG: <svg baseProfile="full" height="300" version="1.1" width="300" xmlns="http://www.w3.org/2000/svg" xmlns:ev="http://www.w3.org/2001/xml-events" xmlns:xlink="http://www.w3.org/1999/xlink"><rect class="q... QRPreview.tsx:193:41
[API] Response: 200 OK - POST /qr/preview/svg modernClient.ts:27:24
Successfully received SVG QR preview qrService.modern.ts:429:25
QRPreview: Content key updated 
Object { qrType: "scooter_unlock", dataChanged: true, designChanged: true, logoChanged: true, useSvg: true }
QRPreview.tsx:51:21
QRPreview: Content key updated  
Object { qrType: "scooter_unlock", dataChanged: true, designChanged: true, logoChanged: true, useSvg: true }
dashboard:1:596
QRPreview: Generating basic SVG for frontend styling QRPreview.tsx:108:37
QRPreview: Verifying data for scooter_unlock 
Object { scooter_id: "SCOOT123", unlock_code: "UNLOCK42", title: "Scooter_unlock QR Code", description: "Scan to view scooter_unlock content", service_provider: "lime", unlock_url: "https://tamensah.com" }
QRPreview.tsx:260:17
QRPreview: Verified data 
Object { scooter_id: "SCOOT123", unlock_code: "UNLOCK42", title: "Scooter_unlock QR Code", description: "Scan to view scooter_unlock content", service_provider: "lime", unlock_url: "https://tamensah.com" }
QRPreview.tsx:364:17
Frontend QR preview SVG request: 
Object { qrType: "scooter_unlock", dataSize: 197, designOptions: (6) […], hasLogo: true, dimensions: 300 }
qrService.modern.ts:376:21
Cleaned content for SVG preview: 
Object { scooter_id: "SCOOT123", unlock_code: "UNLOCK42", title: "Scooter_unlock QR Code", description: "Scan to view scooter_unlock content", service_provider: "lime", unlock_url: "https://tamensah.com" }
qrService.modern.ts:396:21
[API] Request: POST /qr/preview/svg modernClient.ts:27:24
[API] Added auth token to request modernClient.ts:27:24
Frontend QR preview SVG request: 
Object { qrType: "scooter_unlock", dataSize: 197, designOptions: (32) […], hasLogo: true, dimensions: 300 }
qrService.modern.ts:376:21
Cleaned content for SVG preview: 
Object { scooter_id: "SCOOT123", unlock_code: "UNLOCK42", title: "Scooter_unlock QR Code", description: "Scan to view scooter_unlock content", service_provider: "lime", unlock_url: "https://tamensah.com" }
qrService.modern.ts:396:21
[API] Request: POST /qr/preview/svg modernClient.ts:27:24
[API] Added auth token to request modernClient.ts:27:24
[API Error] API Error: 
Object { status: 429, message: "Too many requests. Please try again later.", isNetworkError: false }
intercept-console-error.js:50:32
QR preview SVG error: 
Object { status: 429, message: "Too many requests. Please try again later.", isNetworkError: false }
intercept-console-error.js:50:32
Error generating QR preview: 
Object { status: 429, message: "Too many requests. Please try again later.", isNetworkError: false }
intercept-console-error.js:50:32
[API Error] API Error: 
Object { status: 429, message: "Too many requests. Please try again later.", isNetworkError: false }
intercept-console-error.js:50:32
QR preview SVG error: 
Object { status: 429, message: "Too many requests. Please try again later.", isNetworkError: false }
intercept-console-error.js:50:32
QR Preview generation error: 
Object { status: 429, message: "Too many requests. Please try again later.", isNetworkError: false }
intercept-console-error.js:50:32
[Auth] Adding Bearer token to request: /__nextjs_original-stack-frames 4 layout.tsx:133:49
QRPreview: Content key updated 
Object { qrType: "scooter_unlock", dataChanged: true, designChanged: true, logoChanged: true, useSvg: true }
QRPreview.tsx:51:21
QRPreview: Content key updated  
Object { qrType: "scooter_unlock", dataChanged: true, designChanged: true, logoChanged: true, useSvg: true }
dashboard:1:596
QRPreview: Generating basic SVG for frontend styling QRPreview.tsx:108:37
QRPreview: Verifying data for scooter_unlock 
Object { scooter_id: "SCOOT123", unlock_code: "UNLOCK42", title: "Scooter_unlock QR Code", description: "Scan to view scooter_unlock content", service_provider: "lime", unlock_url: "https://tamensah.com" }
QRPreview.tsx:260:17
QRPreview: Verified data 
Object { scooter_id: "SCOOT123", unlock_code: "UNLOCK42", title: "Scooter_unlock QR Code", description: "Scan to view scooter_unlock content", service_provider: "lime", unlock_url: "https://tamensah.com" }
QRPreview.tsx:364:17
Frontend QR preview SVG request: 
Object { qrType: "scooter_unlock", dataSize: 197, designOptions: (6) […], hasLogo: true, dimensions: 300 }
qrService.modern.ts:376:21
Cleaned content for SVG preview: 
Object { scooter_id: "SCOOT123", unlock_code: "UNLOCK42", title: "Scooter_unlock QR Code", description: "Scan to view scooter_unlock content", service_provider: "lime", unlock_url: "https://tamensah.com" }
qrService.modern.ts:396:21
[API] Request: POST /qr/preview/svg modernClient.ts:27:24
[API] Added auth token to request modernClient.ts:27:24
Frontend QR preview SVG request: 
Object { qrType: "scooter_unlock", dataSize: 197, designOptions: (32) […], hasLogo: true, dimensions: 300 }
qrService.modern.ts:376:21
Cleaned content for SVG preview: 
Object { scooter_id: "SCOOT123", unlock_code: "UNLOCK42", title: "Scooter_unlock QR Code", description: "Scan to view scooter_unlock content", service_provider: "lime", unlock_url: "https://tamensah.com" }
qrService.modern.ts:396:21
[API] Request: POST /qr/preview/svg modernClient.ts:27:24
[API] Added auth token to request modernClient.ts:27:24
[API Error] API Error: 
Object { status: 429, message: "Too many requests. Please try again later.", isNetworkError: false }
intercept-console-error.js:50:32
QR preview SVG error: 
Object { status: 429, message: "Too many requests. Please try again later.", isNetworkError: false }
intercept-console-error.js:50:32
Error generating QR preview: 
Object { status: 429, message: "Too many requests. Please try again later.", isNetworkError: false }
intercept-console-error.js:50:32
[API Error] API Error: 
Object { status: 429, message: "Too many requests. Please try again later.", isNetworkError: false }
intercept-console-error.js:50:32
QR preview SVG error: 
Object { status: 429, message: "Too many requests. Please try again later.", isNetworkError: false }
intercept-console-error.js:50:32
QR Preview generation error: 
Object { status: 429, message: "Too many requests. Please try again later.", isNetworkError: false }
intercept-console-error.js:50:32
[Auth] Adding Bearer token to request: /__nextjs_original-stack-frames 4 layout.tsx:133:49
QRPreview: Content key updated 
Object { qrType: "scooter_unlock", dataChanged: true, designChanged: true, logoChanged: true, useSvg: true }
QRPreview.tsx:51:21
QRPreview: Content key updated  
Object { qrType: "scooter_unlock", dataChanged: true, designChanged: true, logoChanged: true, useSvg: true }
dashboard:1:596
QRPreview: Generating basic SVG for frontend styling QRPreview.tsx:108:37
QRPreview: Verifying data for scooter_unlock 
Object { scooter_id: "SCOOT123", unlock_code: "UNLOCK42", title: "Scooter_unlock QR Code", description: "Scan to view scooter_unlock content", service_provider: "lime", unlock_url: "https://tamensah.com" }
QRPreview.tsx:260:17
QRPreview: Verified data 
Object { scooter_id: "SCOOT123", unlock_code: "UNLOCK42", title: "Scooter_unlock QR Code", description: "Scan to view scooter_unlock content", service_provider: "lime", unlock_url: "https://tamensah.com" }
QRPreview.tsx:364:17
Frontend QR preview SVG request: 
Object { qrType: "scooter_unlock", dataSize: 197, designOptions: (6) […], hasLogo: true, dimensions: 300 }
qrService.modern.ts:376:21
Cleaned content for SVG preview: 
Object { scooter_id: "SCOOT123", unlock_code: "UNLOCK42", title: "Scooter_unlock QR Code", description: "Scan to view scooter_unlock content", service_provider: "lime", unlock_url: "https://tamensah.com" }
qrService.modern.ts:396:21
[API] Request: POST /qr/preview/svg modernClient.ts:27:24
[API] Added auth token to request modernClient.ts:27:24
Frontend QR preview SVG request: 
Object { qrType: "scooter_unlock", dataSize: 197, designOptions: (32) […], hasLogo: true, dimensions: 300 }
qrService.modern.ts:376:21
Cleaned content for SVG preview: 
Object { scooter_id: "SCOOT123", unlock_code: "UNLOCK42", title: "Scooter_unlock QR Code", description: "Scan to view scooter_unlock content", service_provider: "lime", unlock_url: "https://tamensah.com" }
qrService.modern.ts:396:21
[API] Request: POST /qr/preview/svg modernClient.ts:27:24
[API] Added auth token to request modernClient.ts:27:24
[API Error] API Error: 
Object { status: 429, message: "Too many requests. Please try again later.", isNetworkError: false }
intercept-console-error.js:50:32
QR preview SVG error: 
Object { status: 429, message: "Too many requests. Please try again later.", isNetworkError: false }
intercept-console-error.js:50:32
Error generating QR preview: 
Object { status: 429, message: "Too many requests. Please try again later.", isNetworkError: false }
intercept-console-error.js:50:32
[API Error] API Error: 
Object { status: 429, message: "Too many requests. Please try again later.", isNetworkError: false }
intercept-console-error.js:50:32
QR preview SVG error: 
Object { status: 429, message: "Too many requests. Please try again later.", isNetworkError: false }
intercept-console-error.js:50:32
QR Preview generation error: 
Object { status: 429, message: "Too many requests. Please try again later.", isNetworkError: false }
intercept-console-error.js:50:32
[Auth] Adding Bearer token to request: /__nextjs_original-stack-frames 4 layout.tsx:133:49
QRPreview: Content key updated 
Object { qrType: "scooter_unlock", dataChanged: true, designChanged: true, logoChanged: true, useSvg: true }
QRPreview.tsx:51:21
QRPreview: Content key updated  
Object { qrType: "scooter_unlock", dataChanged: true, designChanged: true, logoChanged: true, useSvg: true }
dashboard:1:596
QRPreview: Generating basic SVG for frontend styling QRPreview.tsx:108:37
QRPreview: Verifying data for scooter_unlock 
Object { scooter_id: "SCOOT123", unlock_code: "UNLOCK42", title: "Scooter_unlock QR Code", description: "Scan to view scooter_unlock content", service_provider: "lime", unlock_url: "https://tamensah.com" }
QRPreview.tsx:260:17
QRPreview: Verified data 
Object { scooter_id: "SCOOT123", unlock_code: "UNLOCK42", title: "Scooter_unlock QR Code", description: "Scan to view scooter_unlock content", service_provider: "lime", unlock_url: "https://tamensah.com" }
QRPreview.tsx:364:17
Frontend QR preview SVG request: 
Object { qrType: "scooter_unlock", dataSize: 197, designOptions: (6) […], hasLogo: true, dimensions: 300 }
qrService.modern.ts:376:21
Cleaned content for SVG preview: 
Object { scooter_id: "SCOOT123", unlock_code: "UNLOCK42", title: "Scooter_unlock QR Code", description: "Scan to view scooter_unlock content", service_provider: "lime", unlock_url: "https://tamensah.com" }
qrService.modern.ts:396:21
[API] Request: POST /qr/preview/svg modernClient.ts:27:24
[API] Added auth token to request modernClient.ts:27:24
Frontend QR preview SVG request: 
Object { qrType: "scooter_unlock", dataSize: 197, designOptions: (32) […], hasLogo: true, dimensions: 300 }
qrService.modern.ts:376:21
Cleaned content for SVG preview: 
Object { scooter_id: "SCOOT123", unlock_code: "UNLOCK42", title: "Scooter_unlock QR Code", description: "Scan to view scooter_unlock content", service_provider: "lime", unlock_url: "https://tamensah.com" }
qrService.modern.ts:396:21
[API] Request: POST /qr/preview/svg modernClient.ts:27:24
[API] Added auth token to request modernClient.ts:27:24
[API Error] API Error: 
Object { status: 429, message: "Too many requests. Please try again later.", isNetworkError: false }
intercept-console-error.js:50:32
QR preview SVG error: 
Object { status: 429, message: "Too many requests. Please try again later.", isNetworkError: false }
intercept-console-error.js:50:32
QR Preview generation error: 
Object { status: 429, message: "Too many requests. Please try again later.", isNetworkError: false }
intercept-console-error.js:50:32
[API Error] API Error: 
Object { status: 429, message: "Too many requests. Please try again later.", isNetworkError: false }
intercept-console-error.js:50:32
QR preview SVG error: 
Object { status: 429, message: "Too many requests. Please try again later.", isNetworkError: false }
intercept-console-error.js:50:32
Error generating QR preview: 
Object { status: 429, message: "Too many requests. Please try again later.", isNetworkError: false }
intercept-console-error.js:50:32
[Auth] Adding Bearer token to request: /__nextjs_original-stack-frames 4 layout.tsx:133:49
QRPreview: Content key updated 
Object { qrType: "scooter_unlock", dataChanged: true, designChanged: true, logoChanged: true, useSvg: true }
QRPreview.tsx:51:21
QRPreview: Content key updated  
Object { qrType: "scooter_unlock", dataChanged: true, designChanged: true, logoChanged: true, useSvg: true }
dashboard:1:596
QRPreview: Generating basic SVG for frontend styling QRPreview.tsx:108:37
QRPreview: Verifying data for scooter_unlock 
Object { scooter_id: "SCOOT123", unlock_code: "UNLOCK42", title: "Scooter_unlock QR Code", description: "Scan to view scooter_unlock content", service_provider: "lime", unlock_url: "https://tamensah.com" }
QRPreview.tsx:260:17
QRPreview: Verified data 
Object { scooter_id: "SCOOT123", unlock_code: "UNLOCK42", title: "Scooter_unlock QR Code", description: "Scan to view scooter_unlock content", service_provider: "lime", unlock_url: "https://tamensah.com" }
QRPreview.tsx:364:17
Frontend QR preview SVG request: 
Object { qrType: "scooter_unlock", dataSize: 197, designOptions: (6) […], hasLogo: true, dimensions: 300 }
qrService.modern.ts:376:21
Cleaned content for SVG preview: 
Object { scooter_id: "SCOOT123", unlock_code: "UNLOCK42", title: "Scooter_unlock QR Code", description: "Scan to view scooter_unlock content", service_provider: "lime", unlock_url: "https://tamensah.com" }
qrService.modern.ts:396:21
[API] Request: POST /qr/preview/svg modernClient.ts:27:24
[API] Added auth token to request modernClient.ts:27:24
Frontend QR preview SVG request: 
Object { qrType: "scooter_unlock", dataSize: 197, designOptions: (32) […], hasLogo: true, dimensions: 300 }
qrService.modern.ts:376:21
Cleaned content for SVG preview: 
Object { scooter_id: "SCOOT123", unlock_code: "UNLOCK42", title: "Scooter_unlock QR Code", description: "Scan to view scooter_unlock content", service_provider: "lime", unlock_url: "https://tamensah.com" }
qrService.modern.ts:396:21
[API] Request: POST /qr/preview/svg modernClient.ts:27:24
[API] Added auth token to request modernClient.ts:27:24
[API Error] API Error: 
Object { status: 429, message: "Too many requests. Please try again later.", isNetworkError: false }
intercept-console-error.js:50:32
QR preview SVG error: 
Object { status: 429, message: "Too many requests. Please try again later.", isNetworkError: false }
intercept-console-error.js:50:32
Error generating QR preview: 
Object { status: 429, message: "Too many requests. Please try again later.", isNetworkError: false }
intercept-console-error.js:50:32
[API Error] API Error: 
Object { status: 429, message: "Too many requests. Please try again later.", isNetworkError: false }
intercept-console-error.js:50:32
QR preview SVG error: 
Object { status: 429, message: "Too many requests. Please try again later.", isNetworkError: false }
intercept-console-error.js:50:32
QR Preview generation error: 
Object { status: 429, message: "Too many requests. Please try again later.", isNetworkError: false }
intercept-console-error.js:50:32
[Auth] Adding Bearer token to request: /__nextjs_original-stack-frames 4 layout.tsx:133:49
QRPreview: Content key updated 
Object { qrType: "scooter_unlock", dataChanged: true, designChanged: true, logoChanged: true, useSvg: true }
QRPreview.tsx:51:21
QRPreview: Content key updated  
Object { qrType: "scooter_unlock", dataChanged: true, designChanged: true, logoChanged: true, useSvg: true }
dashboard:1:596
QRPreview: Generating basic SVG for frontend styling QRPreview.tsx:108:37
QRPreview: Verifying data for scooter_unlock 
Object { scooter_id: "SCOOT123", unlock_code: "UNLOCK42", title: "Scooter_unlock QR Code", description: "Scan to view scooter_unlock content", service_provider: "lime", unlock_url: "https://tamensah.com" }
QRPreview.tsx:260:17
QRPreview: Verified data 
Object { scooter_id: "SCOOT123", unlock_code: "UNLOCK42", title: "Scooter_unlock QR Code", description: "Scan to view scooter_unlock content", service_provider: "lime", unlock_url: "https://tamensah.com" }
QRPreview.tsx:364:17
Frontend QR preview SVG request: 
Object { qrType: "scooter_unlock", dataSize: 197, designOptions: (6) […], hasLogo: true, dimensions: 300 }
qrService.modern.ts:376:21
Cleaned content for SVG preview: 
Object { scooter_id: "SCOOT123", unlock_code: "UNLOCK42", title: "Scooter_unlock QR Code", description: "Scan to view scooter_unlock content", service_provider: "lime", unlock_url: "https://tamensah.com" }
qrService.modern.ts:396:21
[API] Request: POST /qr/preview/svg modernClient.ts:27:24
[API] Added auth token to request modernClient.ts:27:24
Frontend QR preview SVG request: 
Object { qrType: "scooter_unlock", dataSize: 197, designOptions: (32) […], hasLogo: true, dimensions: 300 }
qrService.modern.ts:376:21
Cleaned content for SVG preview: 
Object { scooter_id: "SCOOT123", unlock_code: "UNLOCK42", title: "Scooter_unlock QR Code", description: "Scan to view scooter_unlock content", service_provider: "lime", unlock_url: "https://tamensah.com" }
qrService.modern.ts:396:21
[API] Request: POST /qr/preview/svg modernClient.ts:27:24
[API] Added auth token to request modernClient.ts:27:24
[API] Response: 200 OK - POST /qr/preview/svg modernClient.ts:27:24
Successfully received SVG QR preview qrService.modern.ts:429:25
Raw SVG from backend: <svg baseProfile="full" height="300" version="1.1" width="300" xmlns="http://www.w3.org/2000/svg" xmlns:ev="http://www.w3.org/2001/xml-events" xmlns:xlink="http://www.w3.org/1999/xlink"><defs /><rect class="qr-bg" fill="#FFFFFF" height="300" id="qr-bg" width="300" x="0" y="0" /><rect class="qr-module" fill="#000000" height="5.087719298245614" id="module-9-0" width="5.087719298245614" x="50.78947368421053" y="5.0" /><rect class="qr-module" fill="#000000" height="5.087719298245614" id="module-12-0... QRPreview.tsx:127:37
SVG length: 244975 QRPreview.tsx:128:37
SVG contains <svg>: true QRPreview.tsx:129:37
Raw SVG structure: 
Object { rectCount: 1565, pathCount: 0 }
QRPreview.tsx:133:37
SVG analysis: 
Object { hasRect: true, hasPath: false, hasCircle: false, hasBackground: true, hasForeground: true, totalRects: 1565, largeRects: 7, finderPatternsDetected: true, originalDesignOptions: {…}, designOptionsKeys: (21) […] }
QRPreview.tsx:163:37
Large rectangles (potential finder patterns): 
Array(5) [ '<rect class="qr-bg" fill="#FFFFFF" height="300" id="qr-bg" width="300" x="0" y="0" />', '<rect class="qr-marker-outer" fill="#000000" height="35.614035087719294" width="35.614035087719294" x="5.0" y="5.0" />', '<rect class="qr-marker-inner" fill="#fff" height="23.403508771929822" width="23.403508771929822" x="11.105263157894736" y="11.105263157894736" />', '<rect class="qr-marker-outer" fill="#000000" height="35.614035087719294" width="35.614035087719294" x="259.38596491228066" y="5.0" />', '<rect class="qr-marker-inner" fill="#fff" height="23.403508771929822" width="23.403508771929822" x="265.4912280701754" y="11.105263157894736" />' ]
QRPreview.tsx:177:41
Original design options: 
Object { size: 256, margin: 10, pattern: "diamond", marker_border_style: "dotted", marker_border_color: "#f21212", marker_center_style: "rounded-square", marker_center_color: "#be1313", logo_size: 48, logo_position: "center", logo_opacity: 1, … }
QRPreview.tsx:183:37
Filtered design options for basic preview: 
Object { size: 256, margin: 10, error_correction: "M", foreground_color: "#210404", background_color: "#bb0000", pattern: "diamond" }
QRPreview.tsx:184:37
Filtered options count: 6 QRPreview.tsx:185:37
SVG Styler: Parsing SVG, element count: 1560 svgStyler.ts:23:13
SVG Styler: Design options: 
Object { size: 256, margin: 10, error_correction: "M", foreground_color: "#210404", background_color: "#bb0000", pattern: "diamond" }
svgStyler.ts:24:13
SVG Styler: Applied background color: #bb0000 svgStyler.ts:46:17
SVG Styler: Found 1565 QR modules to style with foreground color svgStyler.ts:55:17
SVG Styler: Module 1 (rect) - changed fill from "#000000" to "#210404" svgStyler.ts:75:29
SVG Styler: Module 2 (rect) - changed fill from "#000000" to "#210404" svgStyler.ts:75:29
SVG Styler: Module 3 (rect) - changed fill from "#000000" to "#210404" svgStyler.ts:75:29
SVG Styler: Module 4 (rect) - changed fill from "#000000" to "#210404" svgStyler.ts:75:29
SVG Styler: Applying pattern styling: diamond svgStyler.ts:94:17
Finder pattern detection - backend classes found: 
Object { markerOuters: 3, markerInners: 3, markerDots: 3 }
svgStyler.ts:232:13
Backend finder pattern 1: 
Object { outer: {…}, inner: {…}, dot: {…} }
svgStyler.ts:260:21
Backend finder pattern 2: 
Object { outer: {…}, inner: {…}, dot: {…} }
svgStyler.ts:260:21
Backend finder pattern 3: 
Object { outer: {…}, inner: {…}, dot: {…} }
svgStyler.ts:260:21
Finder pattern detection summary: 
Object { patternsFound: 3, method: "backend-classes" }
svgStyler.ts:343:13
Pattern styling: 
Object { pattern: "diamond", totalRects: 1564, finderElements: 9, dataModules: 1555, markerElements: 9 }
svgStyler.ts:369:13
SVG Styler: Running finder pattern detection... svgStyler.ts:100:13
Finder pattern detection - backend classes found: 
Object { markerOuters: 3, markerInners: 3, markerDots: 3 }
svgStyler.ts:232:13
Backend finder pattern 1: 
Object { outer: {…}, inner: {…}, dot: {…} }
svgStyler.ts:260:21
Backend finder pattern 2: 
Object { outer: {…}, inner: {…}, dot: {…} }
svgStyler.ts:260:21
Backend finder pattern 3: 
Object { outer: {…}, inner: {…}, dot: {…} }
svgStyler.ts:260:21
Finder pattern detection summary: 
Object { patternsFound: 3, method: "backend-classes" }
svgStyler.ts:343:13
SVG Styler: Finder patterns detected: 3 svgStyler.ts:102:13
SVG Styler: Final SVG length: 284401 svgStyler.ts:129:13
Styled SVG: <svg baseProfile="full" height="300" version="1.1" width="300" xmlns="http://www.w3.org/2000/svg" xmlns:ev="http://www.w3.org/2001/xml-events" xmlns:xlink="http://www.w3.org/1999/xlink"><rect class="q... QRPreview.tsx:193:41
[API] Response: 200 OK - POST /qr/preview/svg modernClient.ts:27:24
Successfully received SVG QR preview qrService.modern.ts:429:25
QRPreview: Content key updated 
Object { qrType: "scooter_unlock", dataChanged: true, designChanged: true, logoChanged: true, useSvg: true }
QRPreview.tsx:51:21
QRPreview: Content key updated  
Object { qrType: "scooter_unlock", dataChanged: true, designChanged: true, logoChanged: true, useSvg: true }
dashboard:1:596
[Auth] Adding Bearer token to request: /health layout.tsx:133:49
QRPreview: Generating basic SVG for frontend styling QRPreview.tsx:108:37
QRPreview: Verifying data for scooter_unlock 
Object { scooter_id: "SCOOT123", unlock_code: "UNLOCK42", title: "Scooter_unlock QR Code", description: "Scan to view scooter_unlock content", service_provider: "lime", unlock_url: "https://tamensah.com" }
QRPreview.tsx:260:17
QRPreview: Verified data 
Object { scooter_id: "SCOOT123", unlock_code: "UNLOCK42", title: "Scooter_unlock QR Code", description: "Scan to view scooter_unlock content", service_provider: "lime", unlock_url: "https://tamensah.com" }
QRPreview.tsx:364:17
Frontend QR preview SVG request: 
Object { qrType: "scooter_unlock", dataSize: 197, designOptions: (6) […], hasLogo: true, dimensions: 300 }
qrService.modern.ts:376:21
Cleaned content for SVG preview: 
Object { scooter_id: "SCOOT123", unlock_code: "UNLOCK42", title: "Scooter_unlock QR Code", description: "Scan to view scooter_unlock content", service_provider: "lime", unlock_url: "https://tamensah.com" }
qrService.modern.ts:396:21
[API] Request: POST /qr/preview/svg modernClient.ts:27:24
[API] Added auth token to request modernClient.ts:27:24
Frontend QR preview SVG request: 
Object { qrType: "scooter_unlock", dataSize: 197, designOptions: (32) […], hasLogo: true, dimensions: 300 }
qrService.modern.ts:376:21
Cleaned content for SVG preview: 
Object { scooter_id: "SCOOT123", unlock_code: "UNLOCK42", title: "Scooter_unlock QR Code", description: "Scan to view scooter_unlock content", service_provider: "lime", unlock_url: "https://tamensah.com" }
qrService.modern.ts:396:21
[API] Request: POST /qr/preview/svg modernClient.ts:27:24
[API] Added auth token to request modernClient.ts:27:24
[API] Response: 200 OK - POST /qr/preview/svg modernClient.ts:27:24
Successfully received SVG QR preview qrService.modern.ts:429:25
Raw SVG from backend: <svg baseProfile="full" height="300" version="1.1" width="300" xmlns="http://www.w3.org/2000/svg" xmlns:ev="http://www.w3.org/2001/xml-events" xmlns:xlink="http://www.w3.org/1999/xlink"><defs /><rect class="qr-bg" fill="#FFFFFF" height="300" id="qr-bg" width="300" x="0" y="0" /><rect class="qr-module" fill="#000000" height="5.087719298245614" id="module-9-0" width="5.087719298245614" x="50.78947368421053" y="5.0" /><rect class="qr-module" fill="#000000" height="5.087719298245614" id="module-12-0... QRPreview.tsx:127:37
SVG length: 244975 QRPreview.tsx:128:37
SVG contains <svg>: true QRPreview.tsx:129:37
Raw SVG structure: 
Object { rectCount: 1565, pathCount: 0 }
QRPreview.tsx:133:37
SVG analysis: 
Object { hasRect: true, hasPath: false, hasCircle: false, hasBackground: true, hasForeground: true, totalRects: 1565, largeRects: 7, finderPatternsDetected: true, originalDesignOptions: {…}, designOptionsKeys: (21) […] }
QRPreview.tsx:163:37
Large rectangles (potential finder patterns): 
Array(5) [ '<rect class="qr-bg" fill="#FFFFFF" height="300" id="qr-bg" width="300" x="0" y="0" />', '<rect class="qr-marker-outer" fill="#000000" height="35.614035087719294" width="35.614035087719294" x="5.0" y="5.0" />', '<rect class="qr-marker-inner" fill="#fff" height="23.403508771929822" width="23.403508771929822" x="11.105263157894736" y="11.105263157894736" />', '<rect class="qr-marker-outer" fill="#000000" height="35.614035087719294" width="35.614035087719294" x="259.38596491228066" y="5.0" />', '<rect class="qr-marker-inner" fill="#fff" height="23.403508771929822" width="23.403508771929822" x="265.4912280701754" y="11.105263157894736" />' ]
QRPreview.tsx:177:41
Original design options: 
Object { size: 256, margin: 10, pattern: "diamond", marker_border_style: "frame", marker_border_color: "#f21212", marker_center_style: "rounded-square", marker_center_color: "#be1313", logo_size: 48, logo_position: "center", logo_opacity: 1, … }
QRPreview.tsx:183:37
Filtered design options for basic preview: 
Object { size: 256, margin: 10, error_correction: "M", foreground_color: "#210404", background_color: "#bb0000", pattern: "diamond" }
QRPreview.tsx:184:37
Filtered options count: 6 QRPreview.tsx:185:37
SVG Styler: Parsing SVG, element count: 1560 svgStyler.ts:23:13
SVG Styler: Design options: 
Object { size: 256, margin: 10, error_correction: "M", foreground_color: "#210404", background_color: "#bb0000", pattern: "diamond" }
svgStyler.ts:24:13
SVG Styler: Applied background color: #bb0000 svgStyler.ts:46:17
SVG Styler: Found 1565 QR modules to style with foreground color svgStyler.ts:55:17
SVG Styler: Module 1 (rect) - changed fill from "#000000" to "#210404" svgStyler.ts:75:29
SVG Styler: Module 2 (rect) - changed fill from "#000000" to "#210404" svgStyler.ts:75:29
SVG Styler: Module 3 (rect) - changed fill from "#000000" to "#210404" svgStyler.ts:75:29
SVG Styler: Module 4 (rect) - changed fill from "#000000" to "#210404" svgStyler.ts:75:29
SVG Styler: Applying pattern styling: diamond svgStyler.ts:94:17
Finder pattern detection - backend classes found: 
Object { markerOuters: 3, markerInners: 3, markerDots: 3 }
svgStyler.ts:232:13
Backend finder pattern 1: 
Object { outer: {…}, inner: {…}, dot: {…} }
svgStyler.ts:260:21
Backend finder pattern 2: 
Object { outer: {…}, inner: {…}, dot: {…} }
svgStyler.ts:260:21
Backend finder pattern 3: 
Object { outer: {…}, inner: {…}, dot: {…} }
svgStyler.ts:260:21
Finder pattern detection summary: 
Object { patternsFound: 3, method: "backend-classes" }
svgStyler.ts:343:13
Pattern styling: 
Object { pattern: "diamond", totalRects: 1564, finderElements: 9, dataModules: 1555, markerElements: 9 }
svgStyler.ts:369:13
SVG Styler: Running finder pattern detection... svgStyler.ts:100:13
Finder pattern detection - backend classes found: 
Object { markerOuters: 3, markerInners: 3, markerDots: 3 }
svgStyler.ts:232:13
Backend finder pattern 1: 
Object { outer: {…}, inner: {…}, dot: {…} }
svgStyler.ts:260:21
Backend finder pattern 2: 
Object { outer: {…}, inner: {…}, dot: {…} }
svgStyler.ts:260:21
Backend finder pattern 3: 
Object { outer: {…}, inner: {…}, dot: {…} }
svgStyler.ts:260:21
Finder pattern detection summary: 
Object { patternsFound: 3, method: "backend-classes" }
svgStyler.ts:343:13
SVG Styler: Finder patterns detected: 3 svgStyler.ts:102:13
SVG Styler: Final SVG length: 284401 svgStyler.ts:129:13
Styled SVG: <svg baseProfile="full" height="300" version="1.1" width="300" xmlns="http://www.w3.org/2000/svg" xmlns:ev="http://www.w3.org/2001/xml-events" xmlns:xlink="http://www.w3.org/1999/xlink"><rect class="q... QRPreview.tsx:193:41
[API] Response: 200 OK - POST /qr/preview/svg modernClient.ts:27:24
Successfully received SVG QR preview qrService.modern.ts:429:25
QRPreview: Content key updated 
Object { qrType: "scooter_unlock", dataChanged: true, designChanged: true, logoChanged: true, useSvg: true }
QRPreview.tsx:51:21
QRPreview: Content key updated  
Object { qrType: "scooter_unlock", dataChanged: true, designChanged: true, logoChanged: true, useSvg: true }
dashboard:1:596
QRPreview: Generating basic SVG for frontend styling QRPreview.tsx:108:37
QRPreview: Verifying data for scooter_unlock 
Object { scooter_id: "SCOOT123", unlock_code: "UNLOCK42", title: "Scooter_unlock QR Code", description: "Scan to view scooter_unlock content", service_provider: "lime", unlock_url: "https://tamensah.com" }
QRPreview.tsx:260:17
QRPreview: Verified data 
Object { scooter_id: "SCOOT123", unlock_code: "UNLOCK42", title: "Scooter_unlock QR Code", description: "Scan to view scooter_unlock content", service_provider: "lime", unlock_url: "https://tamensah.com" }
QRPreview.tsx:364:17
Frontend QR preview SVG request: 
Object { qrType: "scooter_unlock", dataSize: 197, designOptions: (6) […], hasLogo: true, dimensions: 300 }
qrService.modern.ts:376:21
Cleaned content for SVG preview: 
Object { scooter_id: "SCOOT123", unlock_code: "UNLOCK42", title: "Scooter_unlock QR Code", description: "Scan to view scooter_unlock content", service_provider: "lime", unlock_url: "https://tamensah.com" }
qrService.modern.ts:396:21
[API] Request: POST /qr/preview/svg modernClient.ts:27:24
[API] Added auth token to request modernClient.ts:27:24
Frontend QR preview SVG request: 
Object { qrType: "scooter_unlock", dataSize: 197, designOptions: (32) […], hasLogo: true, dimensions: 300 }
qrService.modern.ts:376:21
Cleaned content for SVG preview: 
Object { scooter_id: "SCOOT123", unlock_code: "UNLOCK42", title: "Scooter_unlock QR Code", description: "Scan to view scooter_unlock content", service_provider: "lime", unlock_url: "https://tamensah.com" }
qrService.modern.ts:396:21
[API] Request: POST /qr/preview/svg modernClient.ts:27:24
[API] Added auth token to request modernClient.ts:27:24
[API] Response: 200 OK - POST /qr/preview/svg modernClient.ts:27:24
Successfully received SVG QR preview qrService.modern.ts:429:25
[API] Response: 200 OK - POST /qr/preview/svg modernClient.ts:27:24
Successfully received SVG QR preview qrService.modern.ts:429:25
Raw SVG from backend: <svg baseProfile="full" height="300" version="1.1" width="300" xmlns="http://www.w3.org/2000/svg" xmlns:ev="http://www.w3.org/2001/xml-events" xmlns:xlink="http://www.w3.org/1999/xlink"><defs /><rect class="qr-bg" fill="#FFFFFF" height="300" id="qr-bg" width="300" x="0" y="0" /><rect class="qr-module" fill="#000000" height="5.087719298245614" id="module-9-0" width="5.087719298245614" x="50.78947368421053" y="5.0" /><rect class="qr-module" fill="#000000" height="5.087719298245614" id="module-12-0... QRPreview.tsx:127:37
SVG length: 244975 QRPreview.tsx:128:37
SVG contains <svg>: true QRPreview.tsx:129:37
Raw SVG structure: 
Object { rectCount: 1565, pathCount: 0 }
QRPreview.tsx:133:37
SVG analysis: 
Object { hasRect: true, hasPath: false, hasCircle: false, hasBackground: true, hasForeground: true, totalRects: 1565, largeRects: 7, finderPatternsDetected: true, originalDesignOptions: {…}, designOptionsKeys: (21) […] }
QRPreview.tsx:163:37
Large rectangles (potential finder patterns): 
Array(5) [ '<rect class="qr-bg" fill="#FFFFFF" height="300" id="qr-bg" width="300" x="0" y="0" />', '<rect class="qr-marker-outer" fill="#000000" height="35.614035087719294" width="35.614035087719294" x="5.0" y="5.0" />', '<rect class="qr-marker-inner" fill="#fff" height="23.403508771929822" width="23.403508771929822" x="11.105263157894736" y="11.105263157894736" />', '<rect class="qr-marker-outer" fill="#000000" height="35.614035087719294" width="35.614035087719294" x="259.38596491228066" y="5.0" />', '<rect class="qr-marker-inner" fill="#fff" height="23.403508771929822" width="23.403508771929822" x="265.4912280701754" y="11.105263157894736" />' ]
QRPreview.tsx:177:41
Original design options: 
Object { size: 256, margin: 10, pattern: "diamond", marker_border_style: "octagon", marker_border_color: "#f21212", marker_center_style: "rounded-square", marker_center_color: "#be1313", logo_size: 48, logo_position: "center", logo_opacity: 1, … }
QRPreview.tsx:183:37
Filtered design options for basic preview: 
Object { size: 256, margin: 10, error_correction: "M", foreground_color: "#210404", background_color: "#bb0000", pattern: "diamond" }
QRPreview.tsx:184:37
Filtered options count: 6 QRPreview.tsx:185:37
SVG Styler: Parsing SVG, element count: 1560 svgStyler.ts:23:13
SVG Styler: Design options: 
Object { size: 256, margin: 10, error_correction: "M", foreground_color: "#210404", background_color: "#bb0000", pattern: "diamond" }
svgStyler.ts:24:13
SVG Styler: Applied background color: #bb0000 svgStyler.ts:46:17
SVG Styler: Found 1565 QR modules to style with foreground color svgStyler.ts:55:17
SVG Styler: Module 1 (rect) - changed fill from "#000000" to "#210404" svgStyler.ts:75:29
SVG Styler: Module 2 (rect) - changed fill from "#000000" to "#210404" svgStyler.ts:75:29
SVG Styler: Module 3 (rect) - changed fill from "#000000" to "#210404" svgStyler.ts:75:29
SVG Styler: Module 4 (rect) - changed fill from "#000000" to "#210404" svgStyler.ts:75:29
SVG Styler: Applying pattern styling: diamond svgStyler.ts:94:17
Finder pattern detection - backend classes found: 
Object { markerOuters: 3, markerInners: 3, markerDots: 3 }
svgStyler.ts:232:13
Backend finder pattern 1: 
Object { outer: {…}, inner: {…}, dot: {…} }
svgStyler.ts:260:21
Backend finder pattern 2: 
Object { outer: {…}, inner: {…}, dot: {…} }
svgStyler.ts:260:21
Backend finder pattern 3: 
Object { outer: {…}, inner: {…}, dot: {…} }
svgStyler.ts:260:21
Finder pattern detection summary: 
Object { patternsFound: 3, method: "backend-classes" }
svgStyler.ts:343:13
Pattern styling: 
Object { pattern: "diamond", totalRects: 1564, finderElements: 9, dataModules: 1555, markerElements: 9 }
svgStyler.ts:369:13
SVG Styler: Running finder pattern detection... svgStyler.ts:100:13
Finder pattern detection - backend classes found: 
Object { markerOuters: 3, markerInners: 3, markerDots: 3 }
svgStyler.ts:232:13
Backend finder pattern 1: 
Object { outer: {…}, inner: {…}, dot: {…} }
svgStyler.ts:260:21
Backend finder pattern 2: 
Object { outer: {…}, inner: {…}, dot: {…} }
svgStyler.ts:260:21
Backend finder pattern 3: 
Object { outer: {…}, inner: {…}, dot: {…} }
svgStyler.ts:260:21
Finder pattern detection summary: 
Object { patternsFound: 3, method: "backend-classes" }
svgStyler.ts:343:13
SVG Styler: Finder patterns detected: 3 svgStyler.ts:102:13
SVG Styler: Final SVG length: 284401 svgStyler.ts:129:13
Styled SVG: <svg baseProfile="full" height="300" version="1.1" width="300" xmlns="http://www.w3.org/2000/svg" xmlns:ev="http://www.w3.org/2001/xml-events" xmlns:xlink="http://www.w3.org/1999/xlink"><rect class="q... QRPreview.tsx:193:41
QRPreview: Content key updated 
Object { qrType: "scooter_unlock", dataChanged: true, designChanged: true, logoChanged: true, useSvg: true }
QRPreview.tsx:51:21
QRPreview: Content key updated  
Object { qrType: "scooter_unlock", dataChanged: true, designChanged: true, logoChanged: true, useSvg: true }
dashboard:1:596
QRPreview: Generating basic SVG for frontend styling QRPreview.tsx:108:37
QRPreview: Verifying data for scooter_unlock 
Object { scooter_id: "SCOOT123", unlock_code: "UNLOCK42", title: "Scooter_unlock QR Code", description: "Scan to view scooter_unlock content", service_provider: "lime", unlock_url: "https://tamensah.com" }
QRPreview.tsx:260:17
QRPreview: Verified data 
Object { scooter_id: "SCOOT123", unlock_code: "UNLOCK42", title: "Scooter_unlock QR Code", description: "Scan to view scooter_unlock content", service_provider: "lime", unlock_url: "https://tamensah.com" }
QRPreview.tsx:364:17
Frontend QR preview SVG request: 
Object { qrType: "scooter_unlock", dataSize: 197, designOptions: (6) […], hasLogo: true, dimensions: 300 }
qrService.modern.ts:376:21
Cleaned content for SVG preview: 
Object { scooter_id: "SCOOT123", unlock_code: "UNLOCK42", title: "Scooter_unlock QR Code", description: "Scan to view scooter_unlock content", service_provider: "lime", unlock_url: "https://tamensah.com" }
qrService.modern.ts:396:21
[API] Request: POST /qr/preview/svg modernClient.ts:27:24
[API] Added auth token to request modernClient.ts:27:24
Frontend QR preview SVG request: 
Object { qrType: "scooter_unlock", dataSize: 197, designOptions: (32) […], hasLogo: true, dimensions: 300 }
qrService.modern.ts:376:21
Cleaned content for SVG preview: 
Object { scooter_id: "SCOOT123", unlock_code: "UNLOCK42", title: "Scooter_unlock QR Code", description: "Scan to view scooter_unlock content", service_provider: "lime", unlock_url: "https://tamensah.com" }
qrService.modern.ts:396:21
[API] Request: POST /qr/preview/svg modernClient.ts:27:24
[API] Added auth token to request modernClient.ts:27:24
[API] Response: 200 OK - POST /qr/preview/svg modernClient.ts:27:24
Successfully received SVG QR preview qrService.modern.ts:429:25
[API] Response: 200 OK - POST /qr/preview/svg modernClient.ts:27:24
Successfully received SVG QR preview qrService.modern.ts:429:25
Raw SVG from backend: <svg baseProfile="full" height="300" version="1.1" width="300" xmlns="http://www.w3.org/2000/svg" xmlns:ev="http://www.w3.org/2001/xml-events" xmlns:xlink="http://www.w3.org/1999/xlink"><defs /><rect class="qr-bg" fill="#FFFFFF" height="300" id="qr-bg" width="300" x="0" y="0" /><rect class="qr-module" fill="#000000" height="5.087719298245614" id="module-9-0" width="5.087719298245614" x="50.78947368421053" y="5.0" /><rect class="qr-module" fill="#000000" height="5.087719298245614" id="module-12-0... QRPreview.tsx:127:37
SVG length: 244975 QRPreview.tsx:128:37
SVG contains <svg>: true QRPreview.tsx:129:37
Raw SVG structure: 
Object { rectCount: 1565, pathCount: 0 }
QRPreview.tsx:133:37
SVG analysis: 
Object { hasRect: true, hasPath: false, hasCircle: false, hasBackground: true, hasForeground: true, totalRects: 1565, largeRects: 7, finderPatternsDetected: true, originalDesignOptions: {…}, designOptionsKeys: (21) […] }
QRPreview.tsx:163:37
Large rectangles (potential finder patterns): 
Array(5) [ '<rect class="qr-bg" fill="#FFFFFF" height="300" id="qr-bg" width="300" x="0" y="0" />', '<rect class="qr-marker-outer" fill="#000000" height="35.614035087719294" width="35.614035087719294" x="5.0" y="5.0" />', '<rect class="qr-marker-inner" fill="#fff" height="23.403508771929822" width="23.403508771929822" x="11.105263157894736" y="11.105263157894736" />', '<rect class="qr-marker-outer" fill="#000000" height="35.614035087719294" width="35.614035087719294" x="259.38596491228066" y="5.0" />', '<rect class="qr-marker-inner" fill="#fff" height="23.403508771929822" width="23.403508771929822" x="265.4912280701754" y="11.105263157894736" />' ]
QRPreview.tsx:177:41
Original design options: 
Object { size: 256, margin: 10, pattern: "diamond", marker_border_style: "octagon", marker_border_color: "#f21212", marker_center_style: "octagon", marker_center_color: "#be1313", logo_size: 48, logo_position: "center", logo_opacity: 1, … }
QRPreview.tsx:183:37
Filtered design options for basic preview: 
Object { size: 256, margin: 10, error_correction: "M", foreground_color: "#210404", background_color: "#bb0000", pattern: "diamond" }
QRPreview.tsx:184:37
Filtered options count: 6 QRPreview.tsx:185:37
SVG Styler: Parsing SVG, element count: 1560 svgStyler.ts:23:13
SVG Styler: Design options: 
Object { size: 256, margin: 10, error_correction: "M", foreground_color: "#210404", background_color: "#bb0000", pattern: "diamond" }
svgStyler.ts:24:13
SVG Styler: Applied background color: #bb0000 svgStyler.ts:46:17
SVG Styler: Found 1565 QR modules to style with foreground color svgStyler.ts:55:17
SVG Styler: Module 1 (rect) - changed fill from "#000000" to "#210404" svgStyler.ts:75:29
SVG Styler: Module 2 (rect) - changed fill from "#000000" to "#210404" svgStyler.ts:75:29
SVG Styler: Module 3 (rect) - changed fill from "#000000" to "#210404" svgStyler.ts:75:29
SVG Styler: Module 4 (rect) - changed fill from "#000000" to "#210404" svgStyler.ts:75:29
SVG Styler: Applying pattern styling: diamond svgStyler.ts:94:17
Finder pattern detection - backend classes found: 
Object { markerOuters: 3, markerInners: 3, markerDots: 3 }
svgStyler.ts:232:13
Backend finder pattern 1: 
Object { outer: {…}, inner: {…}, dot: {…} }
svgStyler.ts:260:21
Backend finder pattern 2: 
Object { outer: {…}, inner: {…}, dot: {…} }
svgStyler.ts:260:21
Backend finder pattern 3: 
Object { outer: {…}, inner: {…}, dot: {…} }
svgStyler.ts:260:21
Finder pattern detection summary: 
Object { patternsFound: 3, method: "backend-classes" }
svgStyler.ts:343:13
Pattern styling: 
Object { pattern: "diamond", totalRects: 1564, finderElements: 9, dataModules: 1555, markerElements: 9 }
svgStyler.ts:369:13
SVG Styler: Running finder pattern detection... svgStyler.ts:100:13
Finder pattern detection - backend classes found: 
Object { markerOuters: 3, markerInners: 3, markerDots: 3 }
svgStyler.ts:232:13
Backend finder pattern 1: 
Object { outer: {…}, inner: {…}, dot: {…} }
svgStyler.ts:260:21
Backend finder pattern 2: 
Object { outer: {…}, inner: {…}, dot: {…} }
svgStyler.ts:260:21
Backend finder pattern 3: 
Object { outer: {…}, inner: {…}, dot: {…} }
svgStyler.ts:260:21
Finder pattern detection summary: 
Object { patternsFound: 3, method: "backend-classes" }
svgStyler.ts:343:13
SVG Styler: Finder patterns detected: 3 svgStyler.ts:102:13
SVG Styler: Final SVG length: 284401 svgStyler.ts:129:13
Styled SVG: <svg baseProfile="full" height="300" version="1.1" width="300" xmlns="http://www.w3.org/2000/svg" xmlns:ev="http://www.w3.org/2001/xml-events" xmlns:xlink="http://www.w3.org/1999/xlink"><rect class="q... QRPreview.tsx:193:41
QRPreview: Content key updated 
Object { qrType: "scooter_unlock", dataChanged: true, designChanged: true, logoChanged: true, useSvg: true }
QRPreview.tsx:51:21
QRPreview: Content key updated  
Object { qrType: "scooter_unlock", dataChanged: true, designChanged: true, logoChanged: true, useSvg: true }
dashboard:1:596
QRPreview: Generating basic SVG for frontend styling QRPreview.tsx:108:37
QRPreview: Verifying data for scooter_unlock 
Object { scooter_id: "SCOOT123", unlock_code: "UNLOCK42", title: "Scooter_unlock QR Code", description: "Scan to view scooter_unlock content", service_provider: "lime", unlock_url: "https://tamensah.com" }
QRPreview.tsx:260:17
QRPreview: Verified data 
Object { scooter_id: "SCOOT123", unlock_code: "UNLOCK42", title: "Scooter_unlock QR Code", description: "Scan to view scooter_unlock content", service_provider: "lime", unlock_url: "https://tamensah.com" }
QRPreview.tsx:364:17
Frontend QR preview SVG request: 
Object { qrType: "scooter_unlock", dataSize: 197, designOptions: (6) […], hasLogo: true, dimensions: 300 }
qrService.modern.ts:376:21
Cleaned content for SVG preview: 
Object { scooter_id: "SCOOT123", unlock_code: "UNLOCK42", title: "Scooter_unlock QR Code", description: "Scan to view scooter_unlock content", service_provider: "lime", unlock_url: "https://tamensah.com" }
qrService.modern.ts:396:21
[API] Request: POST /qr/preview/svg modernClient.ts:27:24
[API] Added auth token to request modernClient.ts:27:24
Frontend QR preview SVG request: 
Object { qrType: "scooter_unlock", dataSize: 197, designOptions: (32) […], hasLogo: true, dimensions: 300 }
qrService.modern.ts:376:21
Cleaned content for SVG preview: 
Object { scooter_id: "SCOOT123", unlock_code: "UNLOCK42", title: "Scooter_unlock QR Code", description: "Scan to view scooter_unlock content", service_provider: "lime", unlock_url: "https://tamensah.com" }
qrService.modern.ts:396:21
[API] Request: POST /qr/preview/svg modernClient.ts:27:24
[API] Added auth token to request modernClient.ts:27:24
[API] Response: 200 OK - POST /qr/preview/svg modernClient.ts:27:24
Successfully received SVG QR preview qrService.modern.ts:429:25
[API] Response: 200 OK - POST /qr/preview/svg modernClient.ts:27:24
Successfully received SVG QR preview qrService.modern.ts:429:25
Raw SVG from backend: <svg baseProfile="full" height="300" version="1.1" width="300" xmlns="http://www.w3.org/2000/svg" xmlns:ev="http://www.w3.org/2001/xml-events" xmlns:xlink="http://www.w3.org/1999/xlink"><defs /><rect class="qr-bg" fill="#FFFFFF" height="300" id="qr-bg" width="300" x="0" y="0" /><rect class="qr-module" fill="#000000" height="5.087719298245614" id="module-9-0" width="5.087719298245614" x="50.78947368421053" y="5.0" /><rect class="qr-module" fill="#000000" height="5.087719298245614" id="module-12-0... QRPreview.tsx:127:37
SVG length: 244975 QRPreview.tsx:128:37
SVG contains <svg>: true QRPreview.tsx:129:37
Raw SVG structure: 
Object { rectCount: 1565, pathCount: 0 }
QRPreview.tsx:133:37
SVG analysis: 
Object { hasRect: true, hasPath: false, hasCircle: false, hasBackground: true, hasForeground: true, totalRects: 1565, largeRects: 7, finderPatternsDetected: true, originalDesignOptions: {…}, designOptionsKeys: (21) […] }
QRPreview.tsx:163:37
Large rectangles (potential finder patterns): 
Array(5) [ '<rect class="qr-bg" fill="#FFFFFF" height="300" id="qr-bg" width="300" x="0" y="0" />', '<rect class="qr-marker-outer" fill="#000000" height="35.614035087719294" width="35.614035087719294" x="5.0" y="5.0" />', '<rect class="qr-marker-inner" fill="#fff" height="23.403508771929822" width="23.403508771929822" x="11.105263157894736" y="11.105263157894736" />', '<rect class="qr-marker-outer" fill="#000000" height="35.614035087719294" width="35.614035087719294" x="259.38596491228066" y="5.0" />', '<rect class="qr-marker-inner" fill="#fff" height="23.403508771929822" width="23.403508771929822" x="265.4912280701754" y="11.105263157894736" />' ]
QRPreview.tsx:177:41
Original design options: 
Object { size: 256, margin: 10, pattern: "diamond", marker_border_style: "octagon", marker_border_color: "#f21212", marker_center_style: "clover", marker_center_color: "#be1313", logo_size: 48, logo_position: "center", logo_opacity: 1, … }
QRPreview.tsx:183:37
Filtered design options for basic preview: 
Object { size: 256, margin: 10, error_correction: "M", foreground_color: "#210404", background_color: "#bb0000", pattern: "diamond" }
QRPreview.tsx:184:37
Filtered options count: 6 QRPreview.tsx:185:37
SVG Styler: Parsing SVG, element count: 1560 svgStyler.ts:23:13
SVG Styler: Design options: 
Object { size: 256, margin: 10, error_correction: "M", foreground_color: "#210404", background_color: "#bb0000", pattern: "diamond" }
svgStyler.ts:24:13
SVG Styler: Applied background color: #bb0000 svgStyler.ts:46:17
SVG Styler: Found 1565 QR modules to style with foreground color svgStyler.ts:55:17
SVG Styler: Module 1 (rect) - changed fill from "#000000" to "#210404" svgStyler.ts:75:29
SVG Styler: Module 2 (rect) - changed fill from "#000000" to "#210404" svgStyler.ts:75:29
SVG Styler: Module 3 (rect) - changed fill from "#000000" to "#210404" svgStyler.ts:75:29
SVG Styler: Module 4 (rect) - changed fill from "#000000" to "#210404" svgStyler.ts:75:29
SVG Styler: Applying pattern styling: diamond svgStyler.ts:94:17
Finder pattern detection - backend classes found: 
Object { markerOuters: 3, markerInners: 3, markerDots: 3 }
svgStyler.ts:232:13
Backend finder pattern 1: 
Object { outer: {…}, inner: {…}, dot: {…} }
svgStyler.ts:260:21
Backend finder pattern 2: 
Object { outer: {…}, inner: {…}, dot: {…} }
svgStyler.ts:260:21
Backend finder pattern 3: 
Object { outer: {…}, inner: {…}, dot: {…} }
svgStyler.ts:260:21
Finder pattern detection summary: 
Object { patternsFound: 3, method: "backend-classes" }
svgStyler.ts:343:13
Pattern styling: 
Object { pattern: "diamond", totalRects: 1564, finderElements: 9, dataModules: 1555, markerElements: 9 }
svgStyler.ts:369:13
SVG Styler: Running finder pattern detection... svgStyler.ts:100:13
Finder pattern detection - backend classes found: 
Object { markerOuters: 3, markerInners: 3, markerDots: 3 }
svgStyler.ts:232:13
Backend finder pattern 1: 
Object { outer: {…}, inner: {…}, dot: {…} }
svgStyler.ts:260:21
Backend finder pattern 2: 
Object { outer: {…}, inner: {…}, dot: {…} }
svgStyler.ts:260:21
Backend finder pattern 3: 
Object { outer: {…}, inner: {…}, dot: {…} }
svgStyler.ts:260:21
Finder pattern detection summary: 
Object { patternsFound: 3, method: "backend-classes" }
svgStyler.ts:343:13
SVG Styler: Finder patterns detected: 3 svgStyler.ts:102:13
SVG Styler: Final SVG length: 284401 svgStyler.ts:129:13
Styled SVG: <svg baseProfile="full" height="300" version="1.1" width="300" xmlns="http://www.w3.org/2000/svg" xmlns:ev="http://www.w3.org/2001/xml-events" xmlns:xlink="http://www.w3.org/1999/xlink"><rect class="q... QRPreview.tsx:193:41
QRPreview: Content key updated 
Object { qrType: "scooter_unlock", dataChanged: true, designChanged: true, logoChanged: true, useSvg: true }
QRPreview.tsx:51:21
QRPreview: Content key updated  
Object { qrType: "scooter_unlock", dataChanged: true, designChanged: true, logoChanged: true, useSvg: true }
dashboard:1:596
QRPreview: Generating basic SVG for frontend styling QRPreview.tsx:108:37
QRPreview: Verifying data for scooter_unlock 
Object { scooter_id: "SCOOT123", unlock_code: "UNLOCK42", title: "Scooter_unlock QR Code", description: "Scan to view scooter_unlock content", service_provider: "lime", unlock_url: "https://tamensah.com" }
QRPreview.tsx:260:17
QRPreview: Verified data 
Object { scooter_id: "SCOOT123", unlock_code: "UNLOCK42", title: "Scooter_unlock QR Code", description: "Scan to view scooter_unlock content", service_provider: "lime", unlock_url: "https://tamensah.com" }
QRPreview.tsx:364:17
Frontend QR preview SVG request: 
Object { qrType: "scooter_unlock", dataSize: 197, designOptions: (6) […], hasLogo: true, dimensions: 300 }
qrService.modern.ts:376:21
Cleaned content for SVG preview: 
Object { scooter_id: "SCOOT123", unlock_code: "UNLOCK42", title: "Scooter_unlock QR Code", description: "Scan to view scooter_unlock content", service_provider: "lime", unlock_url: "https://tamensah.com" }
qrService.modern.ts:396:21
[API] Request: POST /qr/preview/svg modernClient.ts:27:24
[API] Added auth token to request modernClient.ts:27:24
Frontend QR preview SVG request: 
Object { qrType: "scooter_unlock", dataSize: 197, designOptions: (32) […], hasLogo: true, dimensions: 300 }
qrService.modern.ts:376:21
Cleaned content for SVG preview: 
Object { scooter_id: "SCOOT123", unlock_code: "UNLOCK42", title: "Scooter_unlock QR Code", description: "Scan to view scooter_unlock content", service_provider: "lime", unlock_url: "https://tamensah.com" }
qrService.modern.ts:396:21
[API] Request: POST /qr/preview/svg modernClient.ts:27:24
[API] Added auth token to request modernClient.ts:27:24
[API] Response: 200 OK - POST /qr/preview/svg modernClient.ts:27:24
Successfully received SVG QR preview qrService.modern.ts:429:25
Raw SVG from backend: <svg baseProfile="full" height="300" version="1.1" width="300" xmlns="http://www.w3.org/2000/svg" xmlns:ev="http://www.w3.org/2001/xml-events" xmlns:xlink="http://www.w3.org/1999/xlink"><defs /><rect class="qr-bg" fill="#FFFFFF" height="300" id="qr-bg" width="300" x="0" y="0" /><rect class="qr-module" fill="#000000" height="5.087719298245614" id="module-9-0" width="5.087719298245614" x="50.78947368421053" y="5.0" /><rect class="qr-module" fill="#000000" height="5.087719298245614" id="module-12-0... QRPreview.tsx:127:37
SVG length: 244975 QRPreview.tsx:128:37
SVG contains <svg>: true QRPreview.tsx:129:37
Raw SVG structure: 
Object { rectCount: 1565, pathCount: 0 }
QRPreview.tsx:133:37
SVG analysis: 
Object { hasRect: true, hasPath: false, hasCircle: false, hasBackground: true, hasForeground: true, totalRects: 1565, largeRects: 7, finderPatternsDetected: true, originalDesignOptions: {…}, designOptionsKeys: (21) […] }
QRPreview.tsx:163:37
Large rectangles (potential finder patterns): 
Array(5) [ '<rect class="qr-bg" fill="#FFFFFF" height="300" id="qr-bg" width="300" x="0" y="0" />', '<rect class="qr-marker-outer" fill="#000000" height="35.614035087719294" width="35.614035087719294" x="5.0" y="5.0" />', '<rect class="qr-marker-inner" fill="#fff" height="23.403508771929822" width="23.403508771929822" x="11.105263157894736" y="11.105263157894736" />', '<rect class="qr-marker-outer" fill="#000000" height="35.614035087719294" width="35.614035087719294" x="259.38596491228066" y="5.0" />', '<rect class="qr-marker-inner" fill="#fff" height="23.403508771929822" width="23.403508771929822" x="265.4912280701754" y="11.105263157894736" />' ]
QRPreview.tsx:177:41
Original design options: 
Object { size: 256, margin: 10, pattern: "diamond", marker_border_style: "octagon", marker_border_color: "#f21212", marker_center_style: "shield", marker_center_color: "#be1313", logo_size: 48, logo_position: "center", logo_opacity: 1, … }
QRPreview.tsx:183:37
Filtered design options for basic preview: 
Object { size: 256, margin: 10, error_correction: "M", foreground_color: "#210404", background_color: "#bb0000", pattern: "diamond" }
QRPreview.tsx:184:37
Filtered options count: 6 QRPreview.tsx:185:37
SVG Styler: Parsing SVG, element count: 1560 svgStyler.ts:23:13
SVG Styler: Design options: 
Object { size: 256, margin: 10, error_correction: "M", foreground_color: "#210404", background_color: "#bb0000", pattern: "diamond" }
svgStyler.ts:24:13
SVG Styler: Applied background color: #bb0000 svgStyler.ts:46:17
SVG Styler: Found 1565 QR modules to style with foreground color svgStyler.ts:55:17
SVG Styler: Module 1 (rect) - changed fill from "#000000" to "#210404" svgStyler.ts:75:29
SVG Styler: Module 2 (rect) - changed fill from "#000000" to "#210404" svgStyler.ts:75:29
SVG Styler: Module 3 (rect) - changed fill from "#000000" to "#210404" svgStyler.ts:75:29
SVG Styler: Module 4 (rect) - changed fill from "#000000" to "#210404" svgStyler.ts:75:29
SVG Styler: Applying pattern styling: diamond svgStyler.ts:94:17
Finder pattern detection - backend classes found: 
Object { markerOuters: 3, markerInners: 3, markerDots: 3 }
svgStyler.ts:232:13
Backend finder pattern 1: 
Object { outer: {…}, inner: {…}, dot: {…} }
svgStyler.ts:260:21
Backend finder pattern 2: 
Object { outer: {…}, inner: {…}, dot: {…} }
svgStyler.ts:260:21
Backend finder pattern 3: 
Object { outer: {…}, inner: {…}, dot: {…} }
svgStyler.ts:260:21
Finder pattern detection summary: 
Object { patternsFound: 3, method: "backend-classes" }
svgStyler.ts:343:13
Pattern styling: 
Object { pattern: "diamond", totalRects: 1564, finderElements: 9, dataModules: 1555, markerElements: 9 }
svgStyler.ts:369:13
SVG Styler: Running finder pattern detection... svgStyler.ts:100:13
Finder pattern detection - backend classes found: 
Object { markerOuters: 3, markerInners: 3, markerDots: 3 }
svgStyler.ts:232:13
Backend finder pattern 1: 
Object { outer: {…}, inner: {…}, dot: {…} }
svgStyler.ts:260:21
Backend finder pattern 2: 
Object { outer: {…}, inner: {…}, dot: {…} }
svgStyler.ts:260:21
Backend finder pattern 3: 
Object { outer: {…}, inner: {…}, dot: {…} }
svgStyler.ts:260:21
Finder pattern detection summary: 
Object { patternsFound: 3, method: "backend-classes" }
svgStyler.ts:343:13
SVG Styler: Finder patterns detected: 3 svgStyler.ts:102:13
SVG Styler: Final SVG length: 284401 svgStyler.ts:129:13
Styled SVG: <svg baseProfile="full" height="300" version="1.1" width="300" xmlns="http://www.w3.org/2000/svg" xmlns:ev="http://www.w3.org/2001/xml-events" xmlns:xlink="http://www.w3.org/1999/xlink"><rect class="q... QRPreview.tsx:193:41
[API] Response: 200 OK - POST /qr/preview/svg modernClient.ts:27:24
Successfully received SVG QR preview qrService.modern.ts:429:25
QRPreview: Content key updated 
Object { qrType: "scooter_unlock", dataChanged: true, designChanged: true, logoChanged: true, useSvg: true }
QRPreview.tsx:51:21
QRPreview: Content key updated  
Object { qrType: "scooter_unlock", dataChanged: true, designChanged: true, logoChanged: true, useSvg: true }
dashboard:1:596
QRPreview: Generating basic SVG for frontend styling QRPreview.tsx:108:37
QRPreview: Verifying data for scooter_unlock 
Object { scooter_id: "SCOOT123", unlock_code: "UNLOCK42", title: "Scooter_unlock QR Code", description: "Scan to view scooter_unlock content", service_provider: "lime", unlock_url: "https://tamensah.com" }
QRPreview.tsx:260:17
QRPreview: Verified data 
Object { scooter_id: "SCOOT123", unlock_code: "UNLOCK42", title: "Scooter_unlock QR Code", description: "Scan to view scooter_unlock content", service_provider: "lime", unlock_url: "https://tamensah.com" }
QRPreview.tsx:364:17
Frontend QR preview SVG request: 
Object { qrType: "scooter_unlock", dataSize: 197, designOptions: (6) […], hasLogo: true, dimensions: 300 }
qrService.modern.ts:376:21
Cleaned content for SVG preview: 
Object { scooter_id: "SCOOT123", unlock_code: "UNLOCK42", title: "Scooter_unlock QR Code", description: "Scan to view scooter_unlock content", service_provider: "lime", unlock_url: "https://tamensah.com" }
qrService.modern.ts:396:21
[API] Request: POST /qr/preview/svg modernClient.ts:27:24
[API] Added auth token to request modernClient.ts:27:24
Frontend QR preview SVG request: 
Object { qrType: "scooter_unlock", dataSize: 197, designOptions: (32) […], hasLogo: true, dimensions: 300 }
qrService.modern.ts:376:21
Cleaned content for SVG preview: 
Object { scooter_id: "SCOOT123", unlock_code: "UNLOCK42", title: "Scooter_unlock QR Code", description: "Scan to view scooter_unlock content", service_provider: "lime", unlock_url: "https://tamensah.com" }
qrService.modern.ts:396:21
[API] Request: POST /qr/preview/svg modernClient.ts:27:24
[API] Added auth token to request modernClient.ts:27:24
[API] Response: 200 OK - POST /qr/preview/svg modernClient.ts:27:24
Successfully received SVG QR preview qrService.modern.ts:429:25
Raw SVG from backend: <svg baseProfile="full" height="300" version="1.1" width="300" xmlns="http://www.w3.org/2000/svg" xmlns:ev="http://www.w3.org/2001/xml-events" xmlns:xlink="http://www.w3.org/1999/xlink"><defs /><rect class="qr-bg" fill="#FFFFFF" height="300" id="qr-bg" width="300" x="0" y="0" /><rect class="qr-module" fill="#000000" height="5.087719298245614" id="module-9-0" width="5.087719298245614" x="50.78947368421053" y="5.0" /><rect class="qr-module" fill="#000000" height="5.087719298245614" id="module-12-0... QRPreview.tsx:127:37
SVG length: 244975 QRPreview.tsx:128:37
SVG contains <svg>: true QRPreview.tsx:129:37
Raw SVG structure: 
Object { rectCount: 1565, pathCount: 0 }
QRPreview.tsx:133:37
SVG analysis: 
Object { hasRect: true, hasPath: false, hasCircle: false, hasBackground: true, hasForeground: true, totalRects: 1565, largeRects: 7, finderPatternsDetected: true, originalDesignOptions: {…}, designOptionsKeys: (21) […] }
QRPreview.tsx:163:37
Large rectangles (potential finder patterns): 
Array(5) [ '<rect class="qr-bg" fill="#FFFFFF" height="300" id="qr-bg" width="300" x="0" y="0" />', '<rect class="qr-marker-outer" fill="#000000" height="35.614035087719294" width="35.614035087719294" x="5.0" y="5.0" />', '<rect class="qr-marker-inner" fill="#fff" height="23.403508771929822" width="23.403508771929822" x="11.105263157894736" y="11.105263157894736" />', '<rect class="qr-marker-outer" fill="#000000" height="35.614035087719294" width="35.614035087719294" x="259.38596491228066" y="5.0" />', '<rect class="qr-marker-inner" fill="#fff" height="23.403508771929822" width="23.403508771929822" x="265.4912280701754" y="11.105263157894736" />' ]
QRPreview.tsx:177:41
Original design options: 
Object { size: 256, margin: 10, pattern: "diamond", marker_border_style: "octagon", marker_border_color: "#f21212", marker_center_style: "diagonal", marker_center_color: "#be1313", logo_size: 48, logo_position: "center", logo_opacity: 1, … }
QRPreview.tsx:183:37
Filtered design options for basic preview: 
Object { size: 256, margin: 10, error_correction: "M", foreground_color: "#210404", background_color: "#bb0000", pattern: "diamond" }
QRPreview.tsx:184:37
Filtered options count: 6 QRPreview.tsx:185:37
SVG Styler: Parsing SVG, element count: 1560 svgStyler.ts:23:13
SVG Styler: Design options: 
Object { size: 256, margin: 10, error_correction: "M", foreground_color: "#210404", background_color: "#bb0000", pattern: "diamond" }
svgStyler.ts:24:13
SVG Styler: Applied background color: #bb0000 svgStyler.ts:46:17
SVG Styler: Found 1565 QR modules to style with foreground color svgStyler.ts:55:17
SVG Styler: Module 1 (rect) - changed fill from "#000000" to "#210404" svgStyler.ts:75:29
SVG Styler: Module 2 (rect) - changed fill from "#000000" to "#210404" svgStyler.ts:75:29
SVG Styler: Module 3 (rect) - changed fill from "#000000" to "#210404" svgStyler.ts:75:29
SVG Styler: Module 4 (rect) - changed fill from "#000000" to "#210404" svgStyler.ts:75:29
SVG Styler: Applying pattern styling: diamond svgStyler.ts:94:17
Finder pattern detection - backend classes found: 
Object { markerOuters: 3, markerInners: 3, markerDots: 3 }
svgStyler.ts:232:13
Backend finder pattern 1: 
Object { outer: {…}, inner: {…}, dot: {…} }
svgStyler.ts:260:21
Backend finder pattern 2: 
Object { outer: {…}, inner: {…}, dot: {…} }
svgStyler.ts:260:21
Backend finder pattern 3: 
Object { outer: {…}, inner: {…}, dot: {…} }
svgStyler.ts:260:21
Finder pattern detection summary: 
Object { patternsFound: 3, method: "backend-classes" }
svgStyler.ts:343:13
Pattern styling: 
Object { pattern: "diamond", totalRects: 1564, finderElements: 9, dataModules: 1555, markerElements: 9 }
svgStyler.ts:369:13
SVG Styler: Running finder pattern detection... svgStyler.ts:100:13
Finder pattern detection - backend classes found: 
Object { markerOuters: 3, markerInners: 3, markerDots: 3 }
svgStyler.ts:232:13
Backend finder pattern 1: 
Object { outer: {…}, inner: {…}, dot: {…} }
svgStyler.ts:260:21
Backend finder pattern 2: 
Object { outer: {…}, inner: {…}, dot: {…} }
svgStyler.ts:260:21
Backend finder pattern 3: 
Object { outer: {…}, inner: {…}, dot: {…} }
svgStyler.ts:260:21
Finder pattern detection summary: 
Object { patternsFound: 3, method: "backend-classes" }
svgStyler.ts:343:13
SVG Styler: Finder patterns detected: 3 svgStyler.ts:102:13
SVG Styler: Final SVG length: 284401 svgStyler.ts:129:13
Styled SVG: <svg baseProfile="full" height="300" version="1.1" width="300" xmlns="http://www.w3.org/2000/svg" xmlns:ev="http://www.w3.org/2001/xml-events" xmlns:xlink="http://www.w3.org/1999/xlink"><rect class="q... QRPreview.tsx:193:41
[API] Response: 200 OK - POST /qr/preview/svg modernClient.ts:27:24
Successfully received SVG QR preview qrService.modern.ts:429:25
QRPreview: Content key updated 
Object { qrType: "scooter_unlock", dataChanged: true, designChanged: true, logoChanged: true, useSvg: true }
QRPreview.tsx:51:21
QRPreview: Content key updated  
Object { qrType: "scooter_unlock", dataChanged: true, designChanged: true, logoChanged: true, useSvg: true }
dashboard:1:596
QRPreview: Generating basic SVG for frontend styling QRPreview.tsx:108:37
QRPreview: Verifying data for scooter_unlock 
Object { scooter_id: "SCOOT123", unlock_code: "UNLOCK42", title: "Scooter_unlock QR Code", description: "Scan to view scooter_unlock content", service_provider: "lime", unlock_url: "https://tamensah.com" }
QRPreview.tsx:260:17
QRPreview: Verified data 
Object { scooter_id: "SCOOT123", unlock_code: "UNLOCK42", title: "Scooter_unlock QR Code", description: "Scan to view scooter_unlock content", service_provider: "lime", unlock_url: "https://tamensah.com" }
QRPreview.tsx:364:17
Frontend QR preview SVG request: 
Object { qrType: "scooter_unlock", dataSize: 197, designOptions: (6) […], hasLogo: true, dimensions: 300 }
qrService.modern.ts:376:21
Cleaned content for SVG preview: 
Object { scooter_id: "SCOOT123", unlock_code: "UNLOCK42", title: "Scooter_unlock QR Code", description: "Scan to view scooter_unlock content", service_provider: "lime", unlock_url: "https://tamensah.com" }
qrService.modern.ts:396:21
[API] Request: POST /qr/preview/svg modernClient.ts:27:24
[API] Added auth token to request modernClient.ts:27:24
Frontend QR preview SVG request: 
Object { qrType: "scooter_unlock", dataSize: 197, designOptions: (32) […], hasLogo: true, dimensions: 300 }
qrService.modern.ts:376:21
Cleaned content for SVG preview: 
Object { scooter_id: "SCOOT123", unlock_code: "UNLOCK42", title: "Scooter_unlock QR Code", description: "Scan to view scooter_unlock content", service_provider: "lime", unlock_url: "https://tamensah.com" }
qrService.modern.ts:396:21
[API] Request: POST /qr/preview/svg modernClient.ts:27:24
[API] Added auth token to request modernClient.ts:27:24
[API] Response: 200 OK - POST /qr/preview/svg modernClient.ts:27:24
Successfully received SVG QR preview qrService.modern.ts:429:25
Raw SVG from backend: <svg baseProfile="full" height="300" version="1.1" width="300" xmlns="http://www.w3.org/2000/svg" xmlns:ev="http://www.w3.org/2001/xml-events" xmlns:xlink="http://www.w3.org/1999/xlink"><defs /><rect class="qr-bg" fill="#FFFFFF" height="300" id="qr-bg" width="300" x="0" y="0" /><rect class="qr-module" fill="#000000" height="5.087719298245614" id="module-9-0" width="5.087719298245614" x="50.78947368421053" y="5.0" /><rect class="qr-module" fill="#000000" height="5.087719298245614" id="module-12-0... QRPreview.tsx:127:37
SVG length: 244975 QRPreview.tsx:128:37
SVG contains <svg>: true QRPreview.tsx:129:37
Raw SVG structure: 
Object { rectCount: 1565, pathCount: 0 }
QRPreview.tsx:133:37
SVG analysis: 
Object { hasRect: true, hasPath: false, hasCircle: false, hasBackground: true, hasForeground: true, totalRects: 1565, largeRects: 7, finderPatternsDetected: true, originalDesignOptions: {…}, designOptionsKeys: (21) […] }
QRPreview.tsx:163:37
Large rectangles (potential finder patterns): 
Array(5) [ '<rect class="qr-bg" fill="#FFFFFF" height="300" id="qr-bg" width="300" x="0" y="0" />', '<rect class="qr-marker-outer" fill="#000000" height="35.614035087719294" width="35.614035087719294" x="5.0" y="5.0" />', '<rect class="qr-marker-inner" fill="#fff" height="23.403508771929822" width="23.403508771929822" x="11.105263157894736" y="11.105263157894736" />', '<rect class="qr-marker-outer" fill="#000000" height="35.614035087719294" width="35.614035087719294" x="259.38596491228066" y="5.0" />', '<rect class="qr-marker-inner" fill="#fff" height="23.403508771929822" width="23.403508771929822" x="265.4912280701754" y="11.105263157894736" />' ]
QRPreview.tsx:177:41
Original design options: 
Object { size: 256, margin: 10, pattern: "diamond", marker_border_style: "octagon", marker_border_color: "#f21212", marker_center_style: "grid", marker_center_color: "#be1313", logo_size: 48, logo_position: "center", logo_opacity: 1, … }
QRPreview.tsx:183:37
Filtered design options for basic preview: 
Object { size: 256, margin: 10, error_correction: "M", foreground_color: "#210404", background_color: "#bb0000", pattern: "diamond" }
QRPreview.tsx:184:37
Filtered options count: 6 QRPreview.tsx:185:37
SVG Styler: Parsing SVG, element count: 1560 svgStyler.ts:23:13
SVG Styler: Design options: 
Object { size: 256, margin: 10, error_correction: "M", foreground_color: "#210404", background_color: "#bb0000", pattern: "diamond" }
svgStyler.ts:24:13
SVG Styler: Applied background color: #bb0000 svgStyler.ts:46:17
SVG Styler: Found 1565 QR modules to style with foreground color svgStyler.ts:55:17
SVG Styler: Module 1 (rect) - changed fill from "#000000" to "#210404" svgStyler.ts:75:29
SVG Styler: Module 2 (rect) - changed fill from "#000000" to "#210404" svgStyler.ts:75:29
SVG Styler: Module 3 (rect) - changed fill from "#000000" to "#210404" svgStyler.ts:75:29
SVG Styler: Module 4 (rect) - changed fill from "#000000" to "#210404" svgStyler.ts:75:29
SVG Styler: Applying pattern styling: diamond svgStyler.ts:94:17
Finder pattern detection - backend classes found: 
Object { markerOuters: 3, markerInners: 3, markerDots: 3 }
svgStyler.ts:232:13
Backend finder pattern 1: 
Object { outer: {…}, inner: {…}, dot: {…} }
svgStyler.ts:260:21
Backend finder pattern 2: 
Object { outer: {…}, inner: {…}, dot: {…} }
svgStyler.ts:260:21
Backend finder pattern 3: 
Object { outer: {…}, inner: {…}, dot: {…} }
svgStyler.ts:260:21
Finder pattern detection summary: 
Object { patternsFound: 3, method: "backend-classes" }
svgStyler.ts:343:13
Pattern styling: 
Object { pattern: "diamond", totalRects: 1564, finderElements: 9, dataModules: 1555, markerElements: 9 }
svgStyler.ts:369:13
SVG Styler: Running finder pattern detection... svgStyler.ts:100:13
Finder pattern detection - backend classes found: 
Object { markerOuters: 3, markerInners: 3, markerDots: 3 }
svgStyler.ts:232:13
Backend finder pattern 1: 
Object { outer: {…}, inner: {…}, dot: {…} }
svgStyler.ts:260:21
Backend finder pattern 2: 
Object { outer: {…}, inner: {…}, dot: {…} }
svgStyler.ts:260:21
Backend finder pattern 3: 
Object { outer: {…}, inner: {…}, dot: {…} }
svgStyler.ts:260:21
Finder pattern detection summary: 
Object { patternsFound: 3, method: "backend-classes" }
svgStyler.ts:343:13
SVG Styler: Finder patterns detected: 3 svgStyler.ts:102:13
SVG Styler: Final SVG length: 284401 svgStyler.ts:129:13
Styled SVG: <svg baseProfile="full" height="300" version="1.1" width="300" xmlns="http://www.w3.org/2000/svg" xmlns:ev="http://www.w3.org/2001/xml-events" xmlns:xlink="http://www.w3.org/1999/xlink"><rect class="q... QRPreview.tsx:193:41
[API] Response: 200 OK - POST /qr/preview/svg modernClient.ts:27:24
Successfully received SVG QR preview qrService.modern.ts:429:25
QRPreview: Content key updated 
Object { qrType: "scooter_unlock", dataChanged: true, designChanged: true, logoChanged: true, useSvg: true }
QRPreview.tsx:51:21
QRPreview: Content key updated  
Object { qrType: "scooter_unlock", dataChanged: true, designChanged: true, logoChanged: true, useSvg: true }
dashboard:1:596
QRPreview: Generating basic SVG for frontend styling QRPreview.tsx:108:37
QRPreview: Verifying data for scooter_unlock 
Object { scooter_id: "SCOOT123", unlock_code: "UNLOCK42", title: "Scooter_unlock QR Code", description: "Scan to view scooter_unlock content", service_provider: "lime", unlock_url: "https://tamensah.com" }
QRPreview.tsx:260:17
QRPreview: Verified data 
Object { scooter_id: "SCOOT123", unlock_code: "UNLOCK42", title: "Scooter_unlock QR Code", description: "Scan to view scooter_unlock content", service_provider: "lime", unlock_url: "https://tamensah.com" }
QRPreview.tsx:364:17
Frontend QR preview SVG request: 
Object { qrType: "scooter_unlock", dataSize: 197, designOptions: (6) […], hasLogo: true, dimensions: 300 }
qrService.modern.ts:376:21
Cleaned content for SVG preview: 
Object { scooter_id: "SCOOT123", unlock_code: "UNLOCK42", title: "Scooter_unlock QR Code", description: "Scan to view scooter_unlock content", service_provider: "lime", unlock_url: "https://tamensah.com" }
qrService.modern.ts:396:21
[API] Request: POST /qr/preview/svg modernClient.ts:27:24
[API] Added auth token to request modernClient.ts:27:24
Frontend QR preview SVG request: 
Object { qrType: "scooter_unlock", dataSize: 197, designOptions: (32) […], hasLogo: true, dimensions: 300 }
qrService.modern.ts:376:21
Cleaned content for SVG preview: 
Object { scooter_id: "SCOOT123", unlock_code: "UNLOCK42", title: "Scooter_unlock QR Code", description: "Scan to view scooter_unlock content", service_provider: "lime", unlock_url: "https://tamensah.com" }
qrService.modern.ts:396:21
[API] Request: POST /qr/preview/svg modernClient.ts:27:24
[API] Added auth token to request modernClient.ts:27:24
[API] Response: 200 OK - POST /qr/preview/svg modernClient.ts:27:24
Successfully received SVG QR preview qrService.modern.ts:429:25
Raw SVG from backend: <svg baseProfile="full" height="300" version="1.1" width="300" xmlns="http://www.w3.org/2000/svg" xmlns:ev="http://www.w3.org/2001/xml-events" xmlns:xlink="http://www.w3.org/1999/xlink"><defs /><rect class="qr-bg" fill="#FFFFFF" height="300" id="qr-bg" width="300" x="0" y="0" /><rect class="qr-module" fill="#000000" height="5.087719298245614" id="module-9-0" width="5.087719298245614" x="50.78947368421053" y="5.0" /><rect class="qr-module" fill="#000000" height="5.087719298245614" id="module-12-0... QRPreview.tsx:127:37
SVG length: 244975 QRPreview.tsx:128:37
SVG contains <svg>: true QRPreview.tsx:129:37
Raw SVG structure: 
Object { rectCount: 1565, pathCount: 0 }
QRPreview.tsx:133:37
SVG analysis: 
Object { hasRect: true, hasPath: false, hasCircle: false, hasBackground: true, hasForeground: true, totalRects: 1565, largeRects: 7, finderPatternsDetected: true, originalDesignOptions: {…}, designOptionsKeys: (21) […] }
QRPreview.tsx:163:37
Large rectangles (potential finder patterns): 
Array(5) [ '<rect class="qr-bg" fill="#FFFFFF" height="300" id="qr-bg" width="300" x="0" y="0" />', '<rect class="qr-marker-outer" fill="#000000" height="35.614035087719294" width="35.614035087719294" x="5.0" y="5.0" />', '<rect class="qr-marker-inner" fill="#fff" height="23.403508771929822" width="23.403508771929822" x="11.105263157894736" y="11.105263157894736" />', '<rect class="qr-marker-outer" fill="#000000" height="35.614035087719294" width="35.614035087719294" x="259.38596491228066" y="5.0" />', '<rect class="qr-marker-inner" fill="#fff" height="23.403508771929822" width="23.403508771929822" x="265.4912280701754" y="11.105263157894736" />' ]
QRPreview.tsx:177:41
Original design options: 
Object { size: 256, margin: 10, pattern: "diamond", marker_border_style: "octagon", marker_border_color: "#f21212", marker_center_style: "x-shape", marker_center_color: "#be1313", logo_size: 48, logo_position: "center", logo_opacity: 1, … }
QRPreview.tsx:183:37
Filtered design options for basic preview: 
Object { size: 256, margin: 10, error_correction: "M", foreground_color: "#210404", background_color: "#bb0000", pattern: "diamond" }
QRPreview.tsx:184:37
Filtered options count: 6 QRPreview.tsx:185:37
SVG Styler: Parsing SVG, element count: 1560 svgStyler.ts:23:13
SVG Styler: Design options: 
Object { size: 256, margin: 10, error_correction: "M", foreground_color: "#210404", background_color: "#bb0000", pattern: "diamond" }
svgStyler.ts:24:13
SVG Styler: Applied background color: #bb0000 svgStyler.ts:46:17
SVG Styler: Found 1565 QR modules to style with foreground color svgStyler.ts:55:17
SVG Styler: Module 1 (rect) - changed fill from "#000000" to "#210404" svgStyler.ts:75:29
SVG Styler: Module 2 (rect) - changed fill from "#000000" to "#210404" svgStyler.ts:75:29
SVG Styler: Module 3 (rect) - changed fill from "#000000" to "#210404" svgStyler.ts:75:29
SVG Styler: Module 4 (rect) - changed fill from "#000000" to "#210404" svgStyler.ts:75:29
SVG Styler: Applying pattern styling: diamond svgStyler.ts:94:17
Finder pattern detection - backend classes found: 
Object { markerOuters: 3, markerInners: 3, markerDots: 3 }
svgStyler.ts:232:13
Backend finder pattern 1: 
Object { outer: {…}, inner: {…}, dot: {…} }
svgStyler.ts:260:21
Backend finder pattern 2: 
Object { outer: {…}, inner: {…}, dot: {…} }
svgStyler.ts:260:21
Backend finder pattern 3: 
Object { outer: {…}, inner: {…}, dot: {…} }
svgStyler.ts:260:21
Finder pattern detection summary: 
Object { patternsFound: 3, method: "backend-classes" }
svgStyler.ts:343:13
Pattern styling: 
Object { pattern: "diamond", totalRects: 1564, finderElements: 9, dataModules: 1555, markerElements: 9 }
svgStyler.ts:369:13
SVG Styler: Running finder pattern detection... svgStyler.ts:100:13
Finder pattern detection - backend classes found: 
Object { markerOuters: 3, markerInners: 3, markerDots: 3 }
svgStyler.ts:232:13
Backend finder pattern 1: 
Object { outer: {…}, inner: {…}, dot: {…} }
svgStyler.ts:260:21
Backend finder pattern 2: 
Object { outer: {…}, inner: {…}, dot: {…} }
svgStyler.ts:260:21
Backend finder pattern 3: 
Object { outer: {…}, inner: {…}, dot: {…} }
svgStyler.ts:260:21
Finder pattern detection summary: 
Object { patternsFound: 3, method: "backend-classes" }
svgStyler.ts:343:13
SVG Styler: Finder patterns detected: 3 svgStyler.ts:102:13
SVG Styler: Final SVG length: 284401 svgStyler.ts:129:13
Styled SVG: <svg baseProfile="full" height="300" version="1.1" width="300" xmlns="http://www.w3.org/2000/svg" xmlns:ev="http://www.w3.org/2001/xml-events" xmlns:xlink="http://www.w3.org/1999/xlink"><rect class="q... QRPreview.tsx:193:41
[API] Response: 200 OK - POST /qr/preview/svg modernClient.ts:27:24
Successfully received SVG QR preview qrService.modern.ts:429:25
QRPreview: Content key updated 
Object { qrType: "scooter_unlock", dataChanged: true, designChanged: true, logoChanged: true, useSvg: true }
QRPreview.tsx:51:21
QRPreview: Content key updated  
Object { qrType: "scooter_unlock", dataChanged: true, designChanged: true, logoChanged: true, useSvg: true }
dashboard:1:596
QRPreview: Generating basic SVG for frontend styling QRPreview.tsx:108:37
QRPreview: Verifying data for scooter_unlock 
Object { scooter_id: "SCOOT123", unlock_code: "UNLOCK42", title: "Scooter_unlock QR Code", description: "Scan to view scooter_unlock content", service_provider: "lime", unlock_url: "https://tamensah.com" }
QRPreview.tsx:260:17
QRPreview: Verified data 
Object { scooter_id: "SCOOT123", unlock_code: "UNLOCK42", title: "Scooter_unlock QR Code", description: "Scan to view scooter_unlock content", service_provider: "lime", unlock_url: "https://tamensah.com" }
QRPreview.tsx:364:17
Frontend QR preview SVG request: 
Object { qrType: "scooter_unlock", dataSize: 197, designOptions: (6) […], hasLogo: true, dimensions: 300 }
qrService.modern.ts:376:21
Cleaned content for SVG preview: 
Object { scooter_id: "SCOOT123", unlock_code: "UNLOCK42", title: "Scooter_unlock QR Code", description: "Scan to view scooter_unlock content", service_provider: "lime", unlock_url: "https://tamensah.com" }
qrService.modern.ts:396:21
[API] Request: POST /qr/preview/svg modernClient.ts:27:24
[API] Added auth token to request modernClient.ts:27:24
Frontend QR preview SVG request: 
Object { qrType: "scooter_unlock", dataSize: 197, designOptions: (32) […], hasLogo: true, dimensions: 300 }
qrService.modern.ts:376:21
Cleaned content for SVG preview: 
Object { scooter_id: "SCOOT123", unlock_code: "UNLOCK42", title: "Scooter_unlock QR Code", description: "Scan to view scooter_unlock content", service_provider: "lime", unlock_url: "https://tamensah.com" }
qrService.modern.ts:396:21
[API] Request: POST /qr/preview/svg modernClient.ts:27:24
[API] Added auth token to request modernClient.ts:27:24
[API] Response: 200 OK - POST /qr/preview/svg modernClient.ts:27:24
Successfully received SVG QR preview qrService.modern.ts:429:25
Raw SVG from backend: <svg baseProfile="full" height="300" version="1.1" width="300" xmlns="http://www.w3.org/2000/svg" xmlns:ev="http://www.w3.org/2001/xml-events" xmlns:xlink="http://www.w3.org/1999/xlink"><defs /><rect class="qr-bg" fill="#FFFFFF" height="300" id="qr-bg" width="300" x="0" y="0" /><rect class="qr-module" fill="#000000" height="5.087719298245614" id="module-9-0" width="5.087719298245614" x="50.78947368421053" y="5.0" /><rect class="qr-module" fill="#000000" height="5.087719298245614" id="module-12-0... QRPreview.tsx:127:37
SVG length: 244975 QRPreview.tsx:128:37
SVG contains <svg>: true QRPreview.tsx:129:37
Raw SVG structure: 
Object { rectCount: 1565, pathCount: 0 }
QRPreview.tsx:133:37
SVG analysis: 
Object { hasRect: true, hasPath: false, hasCircle: false, hasBackground: true, hasForeground: true, totalRects: 1565, largeRects: 7, finderPatternsDetected: true, originalDesignOptions: {…}, designOptionsKeys: (21) […] }
QRPreview.tsx:163:37
Large rectangles (potential finder patterns): 
Array(5) [ '<rect class="qr-bg" fill="#FFFFFF" height="300" id="qr-bg" width="300" x="0" y="0" />', '<rect class="qr-marker-outer" fill="#000000" height="35.614035087719294" width="35.614035087719294" x="5.0" y="5.0" />', '<rect class="qr-marker-inner" fill="#fff" height="23.403508771929822" width="23.403508771929822" x="11.105263157894736" y="11.105263157894736" />', '<rect class="qr-marker-outer" fill="#000000" height="35.614035087719294" width="35.614035087719294" x="259.38596491228066" y="5.0" />', '<rect class="qr-marker-inner" fill="#fff" height="23.403508771929822" width="23.403508771929822" x="265.4912280701754" y="11.105263157894736" />' ]
QRPreview.tsx:177:41
Original design options: 
Object { size: 256, margin: 10, pattern: "diamond", marker_border_style: "octagon", marker_border_color: "#f21212", marker_center_style: "ring", marker_center_color: "#be1313", logo_size: 48, logo_position: "center", logo_opacity: 1, … }
QRPreview.tsx:183:37
Filtered design options for basic preview: 
Object { size: 256, margin: 10, error_correction: "M", foreground_color: "#210404", background_color: "#bb0000", pattern: "diamond" }
QRPreview.tsx:184:37
Filtered options count: 6 QRPreview.tsx:185:37
SVG Styler: Parsing SVG, element count: 1560 svgStyler.ts:23:13
SVG Styler: Design options: 
Object { size: 256, margin: 10, error_correction: "M", foreground_color: "#210404", background_color: "#bb0000", pattern: "diamond" }
svgStyler.ts:24:13
SVG Styler: Applied background color: #bb0000 svgStyler.ts:46:17
SVG Styler: Found 1565 QR modules to style with foreground color svgStyler.ts:55:17
SVG Styler: Module 1 (rect) - changed fill from "#000000" to "#210404" svgStyler.ts:75:29
SVG Styler: Module 2 (rect) - changed fill from "#000000" to "#210404" svgStyler.ts:75:29
SVG Styler: Module 3 (rect) - changed fill from "#000000" to "#210404" svgStyler.ts:75:29
SVG Styler: Module 4 (rect) - changed fill from "#000000" to "#210404" svgStyler.ts:75:29
SVG Styler: Applying pattern styling: diamond svgStyler.ts:94:17
Finder pattern detection - backend classes found: 
Object { markerOuters: 3, markerInners: 3, markerDots: 3 }
svgStyler.ts:232:13
Backend finder pattern 1: 
Object { outer: {…}, inner: {…}, dot: {…} }
svgStyler.ts:260:21
Backend finder pattern 2: 
Object { outer: {…}, inner: {…}, dot: {…} }
svgStyler.ts:260:21
Backend finder pattern 3: 
Object { outer: {…}, inner: {…}, dot: {…} }
svgStyler.ts:260:21
Finder pattern detection summary: 
Object { patternsFound: 3, method: "backend-classes" }
svgStyler.ts:343:13
Pattern styling: 
Object { pattern: "diamond", totalRects: 1564, finderElements: 9, dataModules: 1555, markerElements: 9 }
svgStyler.ts:369:13
SVG Styler: Running finder pattern detection... svgStyler.ts:100:13
Finder pattern detection - backend classes found: 
Object { markerOuters: 3, markerInners: 3, markerDots: 3 }
svgStyler.ts:232:13
Backend finder pattern 1: 
Object { outer: {…}, inner: {…}, dot: {…} }
svgStyler.ts:260:21
Backend finder pattern 2: 
Object { outer: {…}, inner: {…}, dot: {…} }
svgStyler.ts:260:21
Backend finder pattern 3: 
Object { outer: {…}, inner: {…}, dot: {…} }
svgStyler.ts:260:21
Finder pattern detection summary: 
Object { patternsFound: 3, method: "backend-classes" }
svgStyler.ts:343:13
SVG Styler: Finder patterns detected: 3 svgStyler.ts:102:13
SVG Styler: Final SVG length: 284401 svgStyler.ts:129:13
Styled SVG: <svg baseProfile="full" height="300" version="1.1" width="300" xmlns="http://www.w3.org/2000/svg" xmlns:ev="http://www.w3.org/2001/xml-events" xmlns:xlink="http://www.w3.org/1999/xlink"><rect class="q... QRPreview.tsx:193:41
[API] Response: 200 OK - POST /qr/preview/svg modernClient.ts:27:24
Successfully received SVG QR preview qrService.modern.ts:429:25
QRPreview: Content key updated 
Object { qrType: "scooter_unlock", dataChanged: true, designChanged: true, logoChanged: true, useSvg: true }
QRPreview.tsx:51:21
QRPreview: Content key updated  
Object { qrType: "scooter_unlock", dataChanged: true, designChanged: true, logoChanged: true, useSvg: true }
dashboard:1:596
QRPreview: Generating basic SVG for frontend styling QRPreview.tsx:108:37
QRPreview: Verifying data for scooter_unlock 
Object { scooter_id: "SCOOT123", unlock_code: "UNLOCK42", title: "Scooter_unlock QR Code", description: "Scan to view scooter_unlock content", service_provider: "lime", unlock_url: "https://tamensah.com" }
QRPreview.tsx:260:17
QRPreview: Verified data 
Object { scooter_id: "SCOOT123", unlock_code: "UNLOCK42", title: "Scooter_unlock QR Code", description: "Scan to view scooter_unlock content", service_provider: "lime", unlock_url: "https://tamensah.com" }
QRPreview.tsx:364:17
Frontend QR preview SVG request: 
Object { qrType: "scooter_unlock", dataSize: 197, designOptions: (6) […], hasLogo: true, dimensions: 300 }
qrService.modern.ts:376:21
Cleaned content for SVG preview: 
Object { scooter_id: "SCOOT123", unlock_code: "UNLOCK42", title: "Scooter_unlock QR Code", description: "Scan to view scooter_unlock content", service_provider: "lime", unlock_url: "https://tamensah.com" }
qrService.modern.ts:396:21
[API] Request: POST /qr/preview/svg modernClient.ts:27:24
[API] Added auth token to request modernClient.ts:27:24
Frontend QR preview SVG request: 
Object { qrType: "scooter_unlock", dataSize: 197, designOptions: (32) […], hasLogo: true, dimensions: 300 }
qrService.modern.ts:376:21
Cleaned content for SVG preview: 
Object { scooter_id: "SCOOT123", unlock_code: "UNLOCK42", title: "Scooter_unlock QR Code", description: "Scan to view scooter_unlock content", service_provider: "lime", unlock_url: "https://tamensah.com" }
qrService.modern.ts:396:21
[API] Request: POST /qr/preview/svg modernClient.ts:27:24
[API] Added auth token to request modernClient.ts:27:24
[API] Response: 200 OK - POST /qr/preview/svg modernClient.ts:27:24
Successfully received SVG QR preview qrService.modern.ts:429:25
[API] Response: 200 OK - POST /qr/preview/svg modernClient.ts:27:24
Successfully received SVG QR preview qrService.modern.ts:429:25
Raw SVG from backend: <svg baseProfile="full" height="300" version="1.1" width="300" xmlns="http://www.w3.org/2000/svg" xmlns:ev="http://www.w3.org/2001/xml-events" xmlns:xlink="http://www.w3.org/1999/xlink"><defs /><rect class="qr-bg" fill="#FFFFFF" height="300" id="qr-bg" width="300" x="0" y="0" /><rect class="qr-module" fill="#000000" height="5.087719298245614" id="module-9-0" width="5.087719298245614" x="50.78947368421053" y="5.0" /><rect class="qr-module" fill="#000000" height="5.087719298245614" id="module-12-0... QRPreview.tsx:127:37
SVG length: 244975 QRPreview.tsx:128:37
SVG contains <svg>: true QRPreview.tsx:129:37
Raw SVG structure: 
Object { rectCount: 1565, pathCount: 0 }
QRPreview.tsx:133:37
SVG analysis: 
Object { hasRect: true, hasPath: false, hasCircle: false, hasBackground: true, hasForeground: true, totalRects: 1565, largeRects: 7, finderPatternsDetected: true, originalDesignOptions: {…}, designOptionsKeys: (21) […] }
QRPreview.tsx:163:37
Large rectangles (potential finder patterns): 
Array(5) [ '<rect class="qr-bg" fill="#FFFFFF" height="300" id="qr-bg" width="300" x="0" y="0" />', '<rect class="qr-marker-outer" fill="#000000" height="35.614035087719294" width="35.614035087719294" x="5.0" y="5.0" />', '<rect class="qr-marker-inner" fill="#fff" height="23.403508771929822" width="23.403508771929822" x="11.105263157894736" y="11.105263157894736" />', '<rect class="qr-marker-outer" fill="#000000" height="35.614035087719294" width="35.614035087719294" x="259.38596491228066" y="5.0" />', '<rect class="qr-marker-inner" fill="#fff" height="23.403508771929822" width="23.403508771929822" x="265.4912280701754" y="11.105263157894736" />' ]
QRPreview.tsx:177:41
Original design options: 
Object { size: 256, margin: 10, pattern: "diamond", marker_border_style: "octagon", marker_border_color: "#f21212", marker_center_style: "rhombus", marker_center_color: "#be1313", logo_size: 48, logo_position: "center", logo_opacity: 1, … }
QRPreview.tsx:183:37
Filtered design options for basic preview: 
Object { size: 256, margin: 10, error_correction: "M", foreground_color: "#210404", background_color: "#bb0000", pattern: "diamond" }
QRPreview.tsx:184:37
Filtered options count: 6 QRPreview.tsx:185:37
SVG Styler: Parsing SVG, element count: 1560 svgStyler.ts:23:13
SVG Styler: Design options: 
Object { size: 256, margin: 10, error_correction: "M", foreground_color: "#210404", background_color: "#bb0000", pattern: "diamond" }
svgStyler.ts:24:13
SVG Styler: Applied background color: #bb0000 svgStyler.ts:46:17
SVG Styler: Found 1565 QR modules to style with foreground color svgStyler.ts:55:17
SVG Styler: Module 1 (rect) - changed fill from "#000000" to "#210404" svgStyler.ts:75:29
SVG Styler: Module 2 (rect) - changed fill from "#000000" to "#210404" svgStyler.ts:75:29
SVG Styler: Module 3 (rect) - changed fill from "#000000" to "#210404" svgStyler.ts:75:29
SVG Styler: Module 4 (rect) - changed fill from "#000000" to "#210404" svgStyler.ts:75:29
SVG Styler: Applying pattern styling: diamond svgStyler.ts:94:17
Finder pattern detection - backend classes found: 
Object { markerOuters: 3, markerInners: 3, markerDots: 3 }
svgStyler.ts:232:13
Backend finder pattern 1: 
Object { outer: {…}, inner: {…}, dot: {…} }
svgStyler.ts:260:21
Backend finder pattern 2: 
Object { outer: {…}, inner: {…}, dot: {…} }
svgStyler.ts:260:21
Backend finder pattern 3: 
Object { outer: {…}, inner: {…}, dot: {…} }
svgStyler.ts:260:21
Finder pattern detection summary: 
Object { patternsFound: 3, method: "backend-classes" }
svgStyler.ts:343:13
Pattern styling: 
Object { pattern: "diamond", totalRects: 1564, finderElements: 9, dataModules: 1555, markerElements: 9 }
svgStyler.ts:369:13
SVG Styler: Running finder pattern detection... svgStyler.ts:100:13
Finder pattern detection - backend classes found: 
Object { markerOuters: 3, markerInners: 3, markerDots: 3 }
svgStyler.ts:232:13
Backend finder pattern 1: 
Object { outer: {…}, inner: {…}, dot: {…} }
svgStyler.ts:260:21
Backend finder pattern 2: 
Object { outer: {…}, inner: {…}, dot: {…} }
svgStyler.ts:260:21
Backend finder pattern 3: 
Object { outer: {…}, inner: {…}, dot: {…} }
svgStyler.ts:260:21
Finder pattern detection summary: 
Object { patternsFound: 3, method: "backend-classes" }
svgStyler.ts:343:13
SVG Styler: Finder patterns detected: 3 svgStyler.ts:102:13
SVG Styler: Final SVG length: 284401 svgStyler.ts:129:13
Styled SVG: <svg baseProfile="full" height="300" version="1.1" width="300" xmlns="http://www.w3.org/2000/svg" xmlns:ev="http://www.w3.org/2001/xml-events" xmlns:xlink="http://www.w3.org/1999/xlink"><rect class="q... QRPreview.tsx:193:41
[Auth] Adding Bearer token to request: /health layout.tsx:133:49
[Auth] Health check successful context.tsx:123:37
[Auth] Adding Bearer token to request: /health 3 layout.tsx:133:49
[Auth] Health check successful context.tsx:123:37
[Auth] Adding Bearer token to request: /health 3 layout.tsx:133:49
[Auth] Health check successful context.tsx:123:37
[Auth] Adding Bearer token to request: /health 3 layout.tsx:133:49
[Auth] Health check successful context.tsx:123:37
[Auth] Adding Bearer token to request: /health 2 layout.tsx:133:49
QRPreview: Content key updated 
Object { qrType: "scooter_unlock", dataChanged: true, designChanged: true, logoChanged: true, useSvg: true }
QRPreview.tsx:51:21
QRPreview: Content key updated  
Object { qrType: "scooter_unlock", dataChanged: true, designChanged: true, logoChanged: true, useSvg: true }
dashboard:1:596
QRPreview: Generating basic SVG for frontend styling QRPreview.tsx:108:37
QRPreview: Verifying data for scooter_unlock 
Object { scooter_id: "SCOOT123", unlock_code: "UNLOCK42", title: "Scooter_unlock QR Code", description: "Scan to view scooter_unlock content", service_provider: "lime", unlock_url: "https://tamensah.com" }
QRPreview.tsx:260:17
QRPreview: Verified data 
Object { scooter_id: "SCOOT123", unlock_code: "UNLOCK42", title: "Scooter_unlock QR Code", description: "Scan to view scooter_unlock content", service_provider: "lime", unlock_url: "https://tamensah.com" }
QRPreview.tsx:364:17
Frontend QR preview SVG request: 
Object { qrType: "scooter_unlock", dataSize: 197, designOptions: (6) […], hasLogo: true, dimensions: 300 }
qrService.modern.ts:376:21
Cleaned content for SVG preview: 
Object { scooter_id: "SCOOT123", unlock_code: "UNLOCK42", title: "Scooter_unlock QR Code", description: "Scan to view scooter_unlock content", service_provider: "lime", unlock_url: "https://tamensah.com" }
qrService.modern.ts:396:21
[API] Request: POST /qr/preview/svg modernClient.ts:27:24
[API] Added auth token to request modernClient.ts:27:24
Frontend QR preview SVG request: 
Object { qrType: "scooter_unlock", dataSize: 197, designOptions: (32) […], hasLogo: true, dimensions: 300 }
qrService.modern.ts:376:21
Cleaned content for SVG preview: 
Object { scooter_id: "SCOOT123", unlock_code: "UNLOCK42", title: "Scooter_unlock QR Code", description: "Scan to view scooter_unlock content", service_provider: "lime", unlock_url: "https://tamensah.com" }
qrService.modern.ts:396:21
[API] Request: POST /qr/preview/svg modernClient.ts:27:24
[API] Added auth token to request modernClient.ts:27:24
[API] Response: 200 OK - POST /qr/preview/svg modernClient.ts:27:24
Successfully received SVG QR preview qrService.modern.ts:429:25
[API] Response: 200 OK - POST /qr/preview/svg modernClient.ts:27:24
Successfully received SVG QR preview qrService.modern.ts:429:25
Raw SVG from backend: <svg baseProfile="full" height="300" version="1.1" width="300" xmlns="http://www.w3.org/2000/svg" xmlns:ev="http://www.w3.org/2001/xml-events" xmlns:xlink="http://www.w3.org/1999/xlink"><defs /><rect class="qr-bg" fill="#FFFFFF" height="300" id="qr-bg" width="300" x="0" y="0" /><rect class="qr-module" fill="#000000" height="5.087719298245614" id="module-9-0" width="5.087719298245614" x="50.78947368421053" y="5.0" /><rect class="qr-module" fill="#000000" height="5.087719298245614" id="module-12-0... QRPreview.tsx:127:37
SVG length: 244975 QRPreview.tsx:128:37
SVG contains <svg>: true QRPreview.tsx:129:37
Raw SVG structure: 
Object { rectCount: 1565, pathCount: 0 }
QRPreview.tsx:133:37
SVG analysis: 
Object { hasRect: true, hasPath: false, hasCircle: false, hasBackground: true, hasForeground: true, totalRects: 1565, largeRects: 7, finderPatternsDetected: true, originalDesignOptions: {…}, designOptionsKeys: (21) […] }
QRPreview.tsx:163:37
Large rectangles (potential finder patterns): 
Array(5) [ '<rect class="qr-bg" fill="#FFFFFF" height="300" id="qr-bg" width="300" x="0" y="0" />', '<rect class="qr-marker-outer" fill="#000000" height="35.614035087719294" width="35.614035087719294" x="5.0" y="5.0" />', '<rect class="qr-marker-inner" fill="#fff" height="23.403508771929822" width="23.403508771929822" x="11.105263157894736" y="11.105263157894736" />', '<rect class="qr-marker-outer" fill="#000000" height="35.614035087719294" width="35.614035087719294" x="259.38596491228066" y="5.0" />', '<rect class="qr-marker-inner" fill="#fff" height="23.403508771929822" width="23.403508771929822" x="265.4912280701754" y="11.105263157894736" />' ]
QRPreview.tsx:177:41
Original design options: 
Object { size: 256, margin: 10, pattern: "diamond", marker_border_style: "octagon", marker_border_color: "#f21212", marker_center_style: "rhombus", marker_center_color: "#be1313", logo_size: 48, logo_position: "center", logo_opacity: 1, … }
QRPreview.tsx:183:37
Filtered design options for basic preview: 
Object { size: 256, margin: 10, error_correction: "M", foreground_color: "#e73232", background_color: "#bb0000", pattern: "diamond" }
QRPreview.tsx:184:37
Filtered options count: 6 QRPreview.tsx:185:37
SVG Styler: Parsing SVG, element count: 1560 svgStyler.ts:23:13
SVG Styler: Design options: 
Object { size: 256, margin: 10, error_correction: "M", foreground_color: "#e73232", background_color: "#bb0000", pattern: "diamond" }
svgStyler.ts:24:13
SVG Styler: Applied background color: #bb0000 svgStyler.ts:46:17
SVG Styler: Found 1565 QR modules to style with foreground color svgStyler.ts:55:17
SVG Styler: Module 1 (rect) - changed fill from "#000000" to "#e73232" svgStyler.ts:75:29
SVG Styler: Module 2 (rect) - changed fill from "#000000" to "#e73232" svgStyler.ts:75:29
SVG Styler: Module 3 (rect) - changed fill from "#000000" to "#e73232" svgStyler.ts:75:29
SVG Styler: Module 4 (rect) - changed fill from "#000000" to "#e73232" svgStyler.ts:75:29
SVG Styler: Applying pattern styling: diamond svgStyler.ts:94:17
Finder pattern detection - backend classes found: 
Object { markerOuters: 3, markerInners: 3, markerDots: 3 }
svgStyler.ts:232:13
Backend finder pattern 1: 
Object { outer: {…}, inner: {…}, dot: {…} }
svgStyler.ts:260:21
Backend finder pattern 2: 
Object { outer: {…}, inner: {…}, dot: {…} }
svgStyler.ts:260:21
Backend finder pattern 3: 
Object { outer: {…}, inner: {…}, dot: {…} }
svgStyler.ts:260:21
Finder pattern detection summary: 
Object { patternsFound: 3, method: "backend-classes" }
svgStyler.ts:343:13
Pattern styling: 
Object { pattern: "diamond", totalRects: 1564, finderElements: 9, dataModules: 1555, markerElements: 9 }
svgStyler.ts:369:13
SVG Styler: Running finder pattern detection... svgStyler.ts:100:13
Finder pattern detection - backend classes found: 
Object { markerOuters: 3, markerInners: 3, markerDots: 3 }
svgStyler.ts:232:13
Backend finder pattern 1: 
Object { outer: {…}, inner: {…}, dot: {…} }
svgStyler.ts:260:21
Backend finder pattern 2: 
Object { outer: {…}, inner: {…}, dot: {…} }
svgStyler.ts:260:21
Backend finder pattern 3: 
Object { outer: {…}, inner: {…}, dot: {…} }
svgStyler.ts:260:21
Finder pattern detection summary: 
Object { patternsFound: 3, method: "backend-classes" }
svgStyler.ts:343:13
SVG Styler: Finder patterns detected: 3 svgStyler.ts:102:13
SVG Styler: Final SVG length: 284401 svgStyler.ts:129:13
Styled SVG: <svg baseProfile="full" height="300" version="1.1" width="300" xmlns="http://www.w3.org/2000/svg" xmlns:ev="http://www.w3.org/2001/xml-events" xmlns:xlink="http://www.w3.org/1999/xlink"><rect class="q... QRPreview.tsx:193:41
QRPreview: Content key updated 
Object { qrType: "scooter_unlock", dataChanged: true, designChanged: true, logoChanged: true, useSvg: true }
QRPreview.tsx:51:21
QRPreview: Content key updated  
Object { qrType: "scooter_unlock", dataChanged: true, designChanged: true, logoChanged: true, useSvg: true }
dashboard:1:596
QRPreview: Generating basic SVG for frontend styling QRPreview.tsx:108:37
QRPreview: Verifying data for scooter_unlock 
Object { scooter_id: "SCOOT123", unlock_code: "UNLOCK42", title: "Scooter_unlock QR Code", description: "Scan to view scooter_unlock content", service_provider: "lime", unlock_url: "https://tamensah.com" }
QRPreview.tsx:260:17
QRPreview: Verified data 
Object { scooter_id: "SCOOT123", unlock_code: "UNLOCK42", title: "Scooter_unlock QR Code", description: "Scan to view scooter_unlock content", service_provider: "lime", unlock_url: "https://tamensah.com" }
QRPreview.tsx:364:17
Frontend QR preview SVG request: 
Object { qrType: "scooter_unlock", dataSize: 197, designOptions: (6) […], hasLogo: true, dimensions: 300 }
qrService.modern.ts:376:21
Cleaned content for SVG preview: 
Object { scooter_id: "SCOOT123", unlock_code: "UNLOCK42", title: "Scooter_unlock QR Code", description: "Scan to view scooter_unlock content", service_provider: "lime", unlock_url: "https://tamensah.com" }
qrService.modern.ts:396:21
[API] Request: POST /qr/preview/svg modernClient.ts:27:24
[API] Added auth token to request modernClient.ts:27:24
[API] Response: 200 OK - POST /qr/preview/svg modernClient.ts:27:24
Successfully received SVG QR preview qrService.modern.ts:429:25
Raw SVG from backend: <svg baseProfile="full" height="300" version="1.1" width="300" xmlns="http://www.w3.org/2000/svg" xmlns:ev="http://www.w3.org/2001/xml-events" xmlns:xlink="http://www.w3.org/1999/xlink"><defs /><rect class="qr-bg" fill="#FFFFFF" height="300" id="qr-bg" width="300" x="0" y="0" /><rect class="qr-module" fill="#000000" height="5.087719298245614" id="module-9-0" width="5.087719298245614" x="50.78947368421053" y="5.0" /><rect class="qr-module" fill="#000000" height="5.087719298245614" id="module-12-0... QRPreview.tsx:127:37
SVG length: 244975 QRPreview.tsx:128:37
SVG contains <svg>: true QRPreview.tsx:129:37
Raw SVG structure: 
Object { rectCount: 1565, pathCount: 0 }
QRPreview.tsx:133:37
SVG analysis: 
Object { hasRect: true, hasPath: false, hasCircle: false, hasBackground: true, hasForeground: true, totalRects: 1565, largeRects: 7, finderPatternsDetected: true, originalDesignOptions: {…}, designOptionsKeys: (21) […] }
QRPreview.tsx:163:37
Large rectangles (potential finder patterns): 
Array(5) [ '<rect class="qr-bg" fill="#FFFFFF" height="300" id="qr-bg" width="300" x="0" y="0" />', '<rect class="qr-marker-outer" fill="#000000" height="35.614035087719294" width="35.614035087719294" x="5.0" y="5.0" />', '<rect class="qr-marker-inner" fill="#fff" height="23.403508771929822" width="23.403508771929822" x="11.105263157894736" y="11.105263157894736" />', '<rect class="qr-marker-outer" fill="#000000" height="35.614035087719294" width="35.614035087719294" x="259.38596491228066" y="5.0" />', '<rect class="qr-marker-inner" fill="#fff" height="23.403508771929822" width="23.403508771929822" x="265.4912280701754" y="11.105263157894736" />' ]
QRPreview.tsx:177:41
Original design options: 
Object { size: 256, margin: 10, pattern: "diamond", marker_border_style: "octagon", marker_border_color: "#f21212", marker_center_style: "rhombus", marker_center_color: "#be1313", logo_size: 48, logo_position: "center", logo_opacity: 1, … }
QRPreview.tsx:183:37
Filtered design options for basic preview: 
Object { size: 256, margin: 10, error_correction: "M", foreground_color: "#e73232", background_color: "#bb0000", pattern: "diamond" }
QRPreview.tsx:184:37
Filtered options count: 6 QRPreview.tsx:185:37
SVG Styler: Parsing SVG, element count: 1560 svgStyler.ts:23:13
SVG Styler: Design options: 
Object { size: 256, margin: 10, error_correction: "M", foreground_color: "#e73232", background_color: "#bb0000", pattern: "diamond" }
svgStyler.ts:24:13
SVG Styler: Applied background color: #bb0000 svgStyler.ts:46:17
SVG Styler: Found 1565 QR modules to style with foreground color svgStyler.ts:55:17
SVG Styler: Module 1 (rect) - changed fill from "#000000" to "#e73232" svgStyler.ts:75:29
SVG Styler: Module 2 (rect) - changed fill from "#000000" to "#e73232" svgStyler.ts:75:29
SVG Styler: Module 3 (rect) - changed fill from "#000000" to "#e73232" svgStyler.ts:75:29
SVG Styler: Module 4 (rect) - changed fill from "#000000" to "#e73232" svgStyler.ts:75:29
SVG Styler: Applying pattern styling: diamond svgStyler.ts:94:17
Finder pattern detection - backend classes found: 
Object { markerOuters: 3, markerInners: 3, markerDots: 3 }
svgStyler.ts:232:13
Backend finder pattern 1: 
Object { outer: {…}, inner: {…}, dot: {…} }
svgStyler.ts:260:21
Backend finder pattern 2: 
Object { outer: {…}, inner: {…}, dot: {…} }
svgStyler.ts:260:21
Backend finder pattern 3: 
Object { outer: {…}, inner: {…}, dot: {…} }
svgStyler.ts:260:21
Finder pattern detection summary: 
Object { patternsFound: 3, method: "backend-classes" }
svgStyler.ts:343:13
Pattern styling: 
Object { pattern: "diamond", totalRects: 1564, finderElements: 9, dataModules: 1555, markerElements: 9 }
svgStyler.ts:369:13
SVG Styler: Running finder pattern detection... svgStyler.ts:100:13
Finder pattern detection - backend classes found: 
Object { markerOuters: 3, markerInners: 3, markerDots: 3 }
svgStyler.ts:232:13
Backend finder pattern 1: 
Object { outer: {…}, inner: {…}, dot: {…} }
svgStyler.ts:260:21
Backend finder pattern 2: 
Object { outer: {…}, inner: {…}, dot: {…} }
svgStyler.ts:260:21
Backend finder pattern 3: 
Object { outer: {…}, inner: {…}, dot: {…} }
svgStyler.ts:260:21
Finder pattern detection summary: 
Object { patternsFound: 3, method: "backend-classes" }
svgStyler.ts:343:13
SVG Styler: Finder patterns detected: 3 svgStyler.ts:102:13
SVG Styler: Final SVG length: 284401 svgStyler.ts:129:13
Styled SVG: <svg baseProfile="full" height="300" version="1.1" width="300" xmlns="http://www.w3.org/2000/svg" xmlns:ev="http://www.w3.org/2001/xml-events" xmlns:xlink="http://www.w3.org/1999/xlink"><rect class="q... QRPreview.tsx:193:41
QRPreview: Content key updated 
Object { qrType: "scooter_unlock", dataChanged: true, designChanged: true, logoChanged: true, useSvg: true }
QRPreview.tsx:51:21
QRPreview: Content key updated  
Object { qrType: "scooter_unlock", dataChanged: true, designChanged: true, logoChanged: true, useSvg: true }
dashboard:1:596
QRPreview: Generating basic SVG for frontend styling QRPreview.tsx:108:37
QRPreview: Verifying data for scooter_unlock 
Object { scooter_id: "SCOOT123", unlock_code: "UNLOCK42", title: "Scooter_unlock QR Code", description: "Scan to view scooter_unlock content", service_provider: "lime", unlock_url: "https://tamensah.com" }
QRPreview.tsx:260:17
QRPreview: Verified data 
Object { scooter_id: "SCOOT123", unlock_code: "UNLOCK42", title: "Scooter_unlock QR Code", description: "Scan to view scooter_unlock content", service_provider: "lime", unlock_url: "https://tamensah.com" }
QRPreview.tsx:364:17
Frontend QR preview SVG request: 
Object { qrType: "scooter_unlock", dataSize: 197, designOptions: (6) […], hasLogo: true, dimensions: 300 }
qrService.modern.ts:376:21
Cleaned content for SVG preview: 
Object { scooter_id: "SCOOT123", unlock_code: "UNLOCK42", title: "Scooter_unlock QR Code", description: "Scan to view scooter_unlock content", service_provider: "lime", unlock_url: "https://tamensah.com" }
qrService.modern.ts:396:21
[API] Request: POST /qr/preview/svg modernClient.ts:27:24
[API] Added auth token to request modernClient.ts:27:24
Frontend QR preview SVG request: 
Object { qrType: "scooter_unlock", dataSize: 197, designOptions: (32) […], hasLogo: true, dimensions: 300 }
qrService.modern.ts:376:21
Cleaned content for SVG preview: 
Object { scooter_id: "SCOOT123", unlock_code: "UNLOCK42", title: "Scooter_unlock QR Code", description: "Scan to view scooter_unlock content", service_provider: "lime", unlock_url: "https://tamensah.com" }
qrService.modern.ts:396:21
[API] Request: POST /qr/preview/svg modernClient.ts:27:24
[API] Added auth token to request modernClient.ts:27:24
[API] Response: 200 OK - POST /qr/preview/svg modernClient.ts:27:24
Successfully received SVG QR preview qrService.modern.ts:429:25
QRPreview: Content key updated 
Object { qrType: "scooter_unlock", dataChanged: true, designChanged: true, logoChanged: true, useSvg: true }
QRPreview.tsx:51:21
QRPreview: Content key updated  
Object { qrType: "scooter_unlock", dataChanged: true, designChanged: true, logoChanged: true, useSvg: true }
dashboard:1:596
[API] Response: 200 OK - POST /qr/preview/svg modernClient.ts:27:24
Successfully received SVG QR preview qrService.modern.ts:429:25
Raw SVG from backend: <svg baseProfile="full" height="300" version="1.1" width="300" xmlns="http://www.w3.org/2000/svg" xmlns:ev="http://www.w3.org/2001/xml-events" xmlns:xlink="http://www.w3.org/1999/xlink"><defs /><rect class="qr-bg" fill="#FFFFFF" height="300" id="qr-bg" width="300" x="0" y="0" /><rect class="qr-module" fill="#000000" height="5.087719298245614" id="module-9-0" width="5.087719298245614" x="50.78947368421053" y="5.0" /><rect class="qr-module" fill="#000000" height="5.087719298245614" id="module-12-0... QRPreview.tsx:127:37
SVG length: 244975 QRPreview.tsx:128:37
SVG contains <svg>: true QRPreview.tsx:129:37
Raw SVG structure: 
Object { rectCount: 1565, pathCount: 0 }
QRPreview.tsx:133:37
SVG analysis: 
Object { hasRect: true, hasPath: false, hasCircle: false, hasBackground: true, hasForeground: true, totalRects: 1565, largeRects: 7, finderPatternsDetected: true, originalDesignOptions: {…}, designOptionsKeys: (21) […] }
QRPreview.tsx:163:37
Large rectangles (potential finder patterns): 
Array(5) [ '<rect class="qr-bg" fill="#FFFFFF" height="300" id="qr-bg" width="300" x="0" y="0" />', '<rect class="qr-marker-outer" fill="#000000" height="35.614035087719294" width="35.614035087719294" x="5.0" y="5.0" />', '<rect class="qr-marker-inner" fill="#fff" height="23.403508771929822" width="23.403508771929822" x="11.105263157894736" y="11.105263157894736" />', '<rect class="qr-marker-outer" fill="#000000" height="35.614035087719294" width="35.614035087719294" x="259.38596491228066" y="5.0" />', '<rect class="qr-marker-inner" fill="#fff" height="23.403508771929822" width="23.403508771929822" x="265.4912280701754" y="11.105263157894736" />' ]
QRPreview.tsx:177:41
Original design options: 
Object { size: 256, margin: 10, pattern: "diamond", marker_border_style: "octagon", marker_border_color: "#f21212", marker_center_style: "rhombus", marker_center_color: "#be1313", logo_size: 48, logo_position: "center", logo_opacity: 1, … }
QRPreview.tsx:183:37
Filtered design options for basic preview: 
Object { size: 256, margin: 10, error_correction: "M", foreground_color: "#e73232", background_color: "#130202", pattern: "diamond" }
QRPreview.tsx:184:37
Filtered options count: 6 QRPreview.tsx:185:37
SVG Styler: Parsing SVG, element count: 1560 svgStyler.ts:23:13
SVG Styler: Design options: 
Object { size: 256, margin: 10, error_correction: "M", foreground_color: "#e73232", background_color: "#130202", pattern: "diamond" }
svgStyler.ts:24:13
SVG Styler: Applied background color: #130202 svgStyler.ts:46:17
SVG Styler: Found 1565 QR modules to style with foreground color svgStyler.ts:55:17
SVG Styler: Module 1 (rect) - changed fill from "#000000" to "#e73232" svgStyler.ts:75:29
SVG Styler: Module 2 (rect) - changed fill from "#000000" to "#e73232" svgStyler.ts:75:29
SVG Styler: Module 3 (rect) - changed fill from "#000000" to "#e73232" svgStyler.ts:75:29
SVG Styler: Module 4 (rect) - changed fill from "#000000" to "#e73232" svgStyler.ts:75:29
SVG Styler: Applying pattern styling: diamond svgStyler.ts:94:17
Finder pattern detection - backend classes found: 
Object { markerOuters: 3, markerInners: 3, markerDots: 3 }
svgStyler.ts:232:13
Backend finder pattern 1: 
Object { outer: {…}, inner: {…}, dot: {…} }
svgStyler.ts:260:21
Backend finder pattern 2: 
Object { outer: {…}, inner: {…}, dot: {…} }
svgStyler.ts:260:21
Backend finder pattern 3: 
Object { outer: {…}, inner: {…}, dot: {…} }
svgStyler.ts:260:21
Finder pattern detection summary: 
Object { patternsFound: 3, method: "backend-classes" }
svgStyler.ts:343:13
Pattern styling: 
Object { pattern: "diamond", totalRects: 1564, finderElements: 9, dataModules: 1555, markerElements: 9 }
svgStyler.ts:369:13
SVG Styler: Running finder pattern detection... svgStyler.ts:100:13
Finder pattern detection - backend classes found: 
Object { markerOuters: 3, markerInners: 3, markerDots: 3 }
svgStyler.ts:232:13
Backend finder pattern 1: 
Object { outer: {…}, inner: {…}, dot: {…} }
svgStyler.ts:260:21
Backend finder pattern 2: 
Object { outer: {…}, inner: {…}, dot: {…} }
svgStyler.ts:260:21
Backend finder pattern 3: 
Object { outer: {…}, inner: {…}, dot: {…} }
svgStyler.ts:260:21
Finder pattern detection summary: 
Object { patternsFound: 3, method: "backend-classes" }
svgStyler.ts:343:13
SVG Styler: Finder patterns detected: 3 svgStyler.ts:102:13
SVG Styler: Final SVG length: 284401 svgStyler.ts:129:13
Styled SVG: <svg baseProfile="full" height="300" version="1.1" width="300" xmlns="http://www.w3.org/2000/svg" xmlns:ev="http://www.w3.org/2001/xml-events" xmlns:xlink="http://www.w3.org/1999/xlink"><rect class="q... QRPreview.tsx:193:41
QRPreview: Generating basic SVG for frontend styling QRPreview.tsx:108:37
QRPreview: Verifying data for scooter_unlock 
Object { scooter_id: "SCOOT123", unlock_code: "UNLOCK42", title: "Scooter_unlock QR Code", description: "Scan to view scooter_unlock content", service_provider: "lime", unlock_url: "https://tamensah.com" }
QRPreview.tsx:260:17
QRPreview: Verified data 
Object { scooter_id: "SCOOT123", unlock_code: "UNLOCK42", title: "Scooter_unlock QR Code", description: "Scan to view scooter_unlock content", service_provider: "lime", unlock_url: "https://tamensah.com" }
QRPreview.tsx:364:17
Frontend QR preview SVG request: 
Object { qrType: "scooter_unlock", dataSize: 197, designOptions: (6) […], hasLogo: true, dimensions: 300 }
qrService.modern.ts:376:21
Cleaned content for SVG preview: 
Object { scooter_id: "SCOOT123", unlock_code: "UNLOCK42", title: "Scooter_unlock QR Code", description: "Scan to view scooter_unlock content", service_provider: "lime", unlock_url: "https://tamensah.com" }
qrService.modern.ts:396:21
[API] Request: POST /qr/preview/svg modernClient.ts:27:24
[API] Added auth token to request modernClient.ts:27:24
[API] Response: 200 OK - POST /qr/preview/svg modernClient.ts:27:24
Successfully received SVG QR preview qrService.modern.ts:429:25
Raw SVG from backend: <svg baseProfile="full" height="300" version="1.1" width="300" xmlns="http://www.w3.org/2000/svg" xmlns:ev="http://www.w3.org/2001/xml-events" xmlns:xlink="http://www.w3.org/1999/xlink"><defs /><rect class="qr-bg" fill="#FFFFFF" height="300" id="qr-bg" width="300" x="0" y="0" /><rect class="qr-module" fill="#000000" height="5.087719298245614" id="module-9-0" width="5.087719298245614" x="50.78947368421053" y="5.0" /><rect class="qr-module" fill="#000000" height="5.087719298245614" id="module-12-0... QRPreview.tsx:127:37
SVG length: 244975 QRPreview.tsx:128:37
SVG contains <svg>: true QRPreview.tsx:129:37
Raw SVG structure: 
Object { rectCount: 1565, pathCount: 0 }
QRPreview.tsx:133:37
SVG analysis: 
Object { hasRect: true, hasPath: false, hasCircle: false, hasBackground: true, hasForeground: true, totalRects: 1565, largeRects: 7, finderPatternsDetected: true, originalDesignOptions: {…}, designOptionsKeys: (21) […] }
QRPreview.tsx:163:37
Large rectangles (potential finder patterns): 
Array(5) [ '<rect class="qr-bg" fill="#FFFFFF" height="300" id="qr-bg" width="300" x="0" y="0" />', '<rect class="qr-marker-outer" fill="#000000" height="35.614035087719294" width="35.614035087719294" x="5.0" y="5.0" />', '<rect class="qr-marker-inner" fill="#fff" height="23.403508771929822" width="23.403508771929822" x="11.105263157894736" y="11.105263157894736" />', '<rect class="qr-marker-outer" fill="#000000" height="35.614035087719294" width="35.614035087719294" x="259.38596491228066" y="5.0" />', '<rect class="qr-marker-inner" fill="#fff" height="23.403508771929822" width="23.403508771929822" x="265.4912280701754" y="11.105263157894736" />' ]
QRPreview.tsx:177:41
Original design options: 
Object { size: 256, margin: 10, pattern: "diamond", marker_border_style: "octagon", marker_border_color: "#f21212", marker_center_style: "rhombus", marker_center_color: "#be1313", logo_size: 48, logo_position: "center", logo_opacity: 1, … }
QRPreview.tsx:183:37
Filtered design options for basic preview: 
Object { size: 256, margin: 10, error_correction: "M", foreground_color: "#e73232", background_color: "#130202", pattern: "diamond" }
QRPreview.tsx:184:37
Filtered options count: 6 QRPreview.tsx:185:37
SVG Styler: Parsing SVG, element count: 1560 svgStyler.ts:23:13
SVG Styler: Design options: 
Object { size: 256, margin: 10, error_correction: "M", foreground_color: "#e73232", background_color: "#130202", pattern: "diamond" }
svgStyler.ts:24:13
SVG Styler: Applied background color: #130202 svgStyler.ts:46:17
SVG Styler: Found 1565 QR modules to style with foreground color svgStyler.ts:55:17
SVG Styler: Module 1 (rect) - changed fill from "#000000" to "#e73232" svgStyler.ts:75:29
SVG Styler: Module 2 (rect) - changed fill from "#000000" to "#e73232" svgStyler.ts:75:29
SVG Styler: Module 3 (rect) - changed fill from "#000000" to "#e73232" svgStyler.ts:75:29
SVG Styler: Module 4 (rect) - changed fill from "#000000" to "#e73232" svgStyler.ts:75:29
SVG Styler: Applying pattern styling: diamond svgStyler.ts:94:17
Finder pattern detection - backend classes found: 
Object { markerOuters: 3, markerInners: 3, markerDots: 3 }
svgStyler.ts:232:13
Backend finder pattern 1: 
Object { outer: {…}, inner: {…}, dot: {…} }
svgStyler.ts:260:21
Backend finder pattern 2: 
Object { outer: {…}, inner: {…}, dot: {…} }
svgStyler.ts:260:21
Backend finder pattern 3: 
Object { outer: {…}, inner: {…}, dot: {…} }
svgStyler.ts:260:21
Finder pattern detection summary: 
Object { patternsFound: 3, method: "backend-classes" }
svgStyler.ts:343:13
Pattern styling: 
Object { pattern: "diamond", totalRects: 1564, finderElements: 9, dataModules: 1555, markerElements: 9 }
svgStyler.ts:369:13
SVG Styler: Running finder pattern detection... svgStyler.ts:100:13
Finder pattern detection - backend classes found: 
Object { markerOuters: 3, markerInners: 3, markerDots: 3 }
svgStyler.ts:232:13
Backend finder pattern 1: 
Object { outer: {…}, inner: {…}, dot: {…} }
svgStyler.ts:260:21
Backend finder pattern 2: 
Object { outer: {…}, inner: {…}, dot: {…} }
svgStyler.ts:260:21
Backend finder pattern 3: 
Object { outer: {…}, inner: {…}, dot: {…} }
svgStyler.ts:260:21
Finder pattern detection summary: 
Object { patternsFound: 3, method: "backend-classes" }
svgStyler.ts:343:13
SVG Styler: Finder patterns detected: 3 svgStyler.ts:102:13
SVG Styler: Final SVG length: 284401 svgStyler.ts:129:13
Styled SVG: <svg baseProfile="full" height="300" version="1.1" width="300" xmlns="http://www.w3.org/2000/svg" xmlns:ev="http://www.w3.org/2001/xml-events" xmlns:xlink="http://www.w3.org/1999/xlink"><rect class="q... QRPreview.tsx:193:41
[Auth] Adding Bearer token to request: /health layout.tsx:133:49
[Auth] Health check successful context.tsx:123:37
[Auth] Adding Bearer token to request: /health layout.tsx:133:49
