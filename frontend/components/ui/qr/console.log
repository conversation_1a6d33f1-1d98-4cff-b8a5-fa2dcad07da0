Request for font "<PERSON>eist" blocked at visibility level 2 (requires 3)
layout.js line 1976 > eval:165:35
MouseEvent.mozInputSource is deprecated. Use PointerEvent.pointerType instead. layout.js line 2405 > eval:22:9
The resource at “https://localhost:3000/_next/static/media/0484562807a97172-s.p.woff2” preloaded with link preload was not used within a few seconds. Make sure all attributes of the preload tag are set correctly. creator
The resource at “https://localhost:3000/_next/static/media/8888a3826f4a3af4-s.p.woff2” preloaded with link preload was not used within a few seconds. Make sure all attributes of the preload tag are set correctly. creator
The resource at “https://localhost:3000/_next/static/media/b957ea75a84b6ea7-s.p.woff2” preloaded with link preload was not used within a few seconds. Make sure all attributes of the preload tag are set correctly. creator
The resource at “https://localhost:3000/_next/static/media/eafabf029ad39a43-s.p.woff2” preloaded with link preload was not used within a few seconds. Make sure all attributes of the preload tag are set correctly. creator
Source map error: Error: request failed with status 404
Stack in the worker:networkRequest@resource://devtools/client/shared/source-map-loader/utils/network-request.js:43:9

Resource URL: null
Source Map URL: installHook.js.map
[API] Request: POST /qr/preview/svg modernClient.ts:27:24
[API] Added auth token to request modernClient.ts:27:24
[API] Response: 200 OK - POST /qr/preview/svg modernClient.ts:27:24
Successfully received SVG QR preview qrService.modern.ts:429:25
Raw SVG from backend: <svg baseProfile="full" height="300" version="1.1" width="300" xmlns="http://www.w3.org/2000/svg" xmlns:ev="http://www.w3.org/2001/xml-events" xmlns:xlink="http://www.w3.org/1999/xlink"><defs /><rect class="qr-bg" fill="#FFFFFF" height="300" id="qr-bg" width="300" x="0" y="0" /><rect class="qr-module" fill="#000000" height="4.918032786885246" id="module-10-0" width="4.918032786885246" x="49.18032786885246" y="0.0" /><rect class="qr-module" fill="#000000" height="4.918032786885246" id="module-14-... QRPreview.tsx:127:37
SVG length: 276037 QRPreview.tsx:128:37
SVG contains <svg>: true QRPreview.tsx:129:37
Raw SVG structure: 
Object { rectCount: 1763, pathCount: 0 }
QRPreview.tsx:133:37
SVG analysis: 
Object { hasRect: true, hasPath: false, hasCircle: false, hasBackground: true, hasForeground: true, totalRects: 1763, largeRects: 7, finderPatternsDetected: true, originalDesignOptions: {…}, designOptionsKeys: (21) […] }
QRPreview.tsx:163:37
Large rectangles (potential finder patterns): 
Array(5) [ '<rect class="qr-bg" fill="#FFFFFF" height="300" id="qr-bg" width="300" x="0" y="0" />', '<rect class="qr-marker-outer" fill="#000000" height="34.42622950819672" width="34.42622950819672" x="0.0" y="0.0" />', '<rect class="qr-marker-inner" fill="#fff" height="22.62295081967213" width="22.62295081967213" x="5.901639344262295" y="5.901639344262295" />', '<rect class="qr-marker-outer" fill="#000000" height="34.42622950819672" width="34.42622950819672" x="265.57377049180326" y="0.0" />', '<rect class="qr-marker-inner" fill="#fff" height="22.62295081967213" width="22.62295081967213" x="271.4754098360656" y="5.901639344262295" />' ]
QRPreview.tsx:177:41
Original design options: 
Object { size: 256, margin: 10, pattern: "dots", marker_border_style: "octagon", marker_border_color: "#000000", marker_center_style: "grid", marker_center_color: "#000000", logo_size: 48, logo_position: "center", logo_opacity: 1, … }
QRPreview.tsx:183:37
Filtered design options for basic preview: 
Object { size: 256, margin: 10, error_correction: "M", foreground_color: "#030000", background_color: "#210303", pattern: "dots" }
QRPreview.tsx:184:37
Filtered options count: 6 QRPreview.tsx:185:37
SVG Styler: Parsing SVG, element count: 1758 svgStyler.ts:23:13
SVG Styler: Design options: 
Object { size: 256, margin: 10, error_correction: "M", foreground_color: "#030000", background_color: "#210303", pattern: "dots" }
svgStyler.ts:24:13
SVG Styler: Applied background color: #210303 svgStyler.ts:46:17
SVG Styler: Found 1763 QR modules to style with foreground color svgStyler.ts:55:17
SVG Styler: Module 1 (rect) - changed fill from "#000000" to "#030000" svgStyler.ts:75:29
SVG Styler: Module 2 (rect) - changed fill from "#000000" to "#030000" svgStyler.ts:75:29
SVG Styler: Module 3 (rect) - changed fill from "#000000" to "#030000" svgStyler.ts:75:29
SVG Styler: Module 4 (rect) - changed fill from "#000000" to "#030000" svgStyler.ts:75:29
SVG Styler: Applying pattern styling: dots svgStyler.ts:94:17
Finder pattern detection - backend classes found: 
Object { markerOuters: 3, markerInners: 3, markerDots: 3 }
svgStyler.ts:232:13
Backend finder pattern 1: 
Object { outer: {…}, inner: {…}, dot: {…} }
svgStyler.ts:260:21
Backend finder pattern 2: 
Object { outer: {…}, inner: {…}, dot: {…} }
svgStyler.ts:260:21
Backend finder pattern 3: 
Object { outer: {…}, inner: {…}, dot: {…} }
svgStyler.ts:260:21
Finder pattern detection summary: 
Object { patternsFound: 3, method: "backend-classes" }
svgStyler.ts:343:13
Pattern styling: 
Object { pattern: "dots", totalRects: 1762, finderElements: 9, dataModules: 1753, markerElements: 9 }
svgStyler.ts:369:13
SVG Styler: Running finder pattern detection... svgStyler.ts:100:13
Finder pattern detection - backend classes found: 
Object { markerOuters: 3, markerInners: 3, markerDots: 3 }
svgStyler.ts:232:13
Backend finder pattern 1: 
Object { outer: {…}, inner: {…}, dot: {…} }
svgStyler.ts:260:21
Backend finder pattern 2: 
Object { outer: {…}, inner: {…}, dot: {…} }
svgStyler.ts:260:21
Backend finder pattern 3: 
Object { outer: {…}, inner: {…}, dot: {…} }
svgStyler.ts:260:21
Finder pattern detection summary: 
Object { patternsFound: 3, method: "backend-classes" }
svgStyler.ts:343:13
SVG Styler: Finder patterns detected: 3 svgStyler.ts:102:13
SVG Styler: Final SVG length: 163666 svgStyler.ts:129:13
Styled SVG: <svg baseProfile="full" height="300" version="1.1" width="300" xmlns="http://www.w3.org/2000/svg" xmlns:ev="http://www.w3.org/2001/xml-events" xmlns:xlink="http://www.w3.org/1999/xlink"><rect class="q... QRPreview.tsx:193:41
[API] Response: 200 OK - POST /qr/preview/svg modernClient.ts:27:24
Successfully received SVG QR preview qrService.modern.ts:429:25
[Auth] Token still valid, no need to refresh auth-helper.ts:66:37
[Auth] Adding Bearer token to request: /health layout.tsx:133:49
[Auth] Health check successful context.tsx:123:37
[Auth] Adding Bearer token to request: /health layout.tsx:133:49
QRPreview: Content key updated 
Object { qrType: "email", dataChanged: true, designChanged: true, logoChanged: false, useSvg: true }
QRPreview.tsx:51:21
QRPreview: Content key updated  
Object { qrType: "email", dataChanged: true, designChanged: true, logoChanged: false, useSvg: true }
dashboard:1:596
QRPreview: Generating basic SVG for frontend styling QRPreview.tsx:108:37
QRPreview: Verifying data for email 
Object { 0: "c", 1: "o", 2: "n", 3: "t", 4: "a", 5: "c", 6: "t", 7: "@", 8: "e", 9: "x", … }
QRPreview.tsx:260:17
QRPreview: Verified data 
Object { 0: "c", 1: "o", 2: "n", 3: "t", 4: "a", 5: "c", 6: "t", 7: "@", 8: "e", 9: "x", … }
QRPreview.tsx:364:17
Frontend QR preview SVG request: 
Object { qrType: "email", dataSize: 363, designOptions: (6) […], hasLogo: false, dimensions: 300 }
qrService.modern.ts:376:21
Cleaned content for SVG preview: 
Object { email: "<EMAIL>", subject: "Hello from QRVibe", body: "I scanned your QR code and wanted to reach out.", cc: "", bcc: "", title: "Email QR Code", description: "Scan to view email content" }
qrService.modern.ts:396:21
[API] Request: POST /qr/preview/svg modernClient.ts:27:24
[API] Added auth token to request modernClient.ts:27:24
Frontend QR preview SVG request: 
Object { qrType: "email", dataSize: 401, designOptions: (32) […], hasLogo: false, dimensions: 300 }
qrService.modern.ts:376:21
Cleaned content for SVG preview: 
Object { email: "<EMAIL>", subject: "Hello from QRVibe", body: "I scanned your QR code and wanted to reach out.", cc: "", bcc: "", title: "Email QR Code", description: "Scan to view email content", email_address: "<EMAIL>" }
qrService.modern.ts:396:21
[API] Request: POST /qr/preview/svg modernClient.ts:27:24
[API] Added auth token to request modernClient.ts:27:24
[API] Response: 200 OK - POST /qr/preview/svg modernClient.ts:27:24
Successfully received SVG QR preview qrService.modern.ts:429:25
Raw SVG from backend: <svg baseProfile="full" height="300" version="1.1" width="300" xmlns="http://www.w3.org/2000/svg" xmlns:ev="http://www.w3.org/2001/xml-events" xmlns:xlink="http://www.w3.org/1999/xlink"><defs /><rect class="qr-bg" fill="#FFFFFF" height="300" id="qr-bg" width="300" x="0" y="0" /><rect class="qr-module" fill="#000000" height="4.918032786885246" id="module-10-0" width="4.918032786885246" x="49.18032786885246" y="0.0" /><rect class="qr-module" fill="#000000" height="4.918032786885246" id="module-14-... QRPreview.tsx:127:37
SVG length: 276037 QRPreview.tsx:128:37
SVG contains <svg>: true QRPreview.tsx:129:37
Raw SVG structure: 
Object { rectCount: 1763, pathCount: 0 }
QRPreview.tsx:133:37
SVG analysis: 
Object { hasRect: true, hasPath: false, hasCircle: false, hasBackground: true, hasForeground: true, totalRects: 1763, largeRects: 7, finderPatternsDetected: true, originalDesignOptions: {…}, designOptionsKeys: (21) […] }
QRPreview.tsx:163:37
Large rectangles (potential finder patterns): 
Array(5) [ '<rect class="qr-bg" fill="#FFFFFF" height="300" id="qr-bg" width="300" x="0" y="0" />', '<rect class="qr-marker-outer" fill="#000000" height="34.42622950819672" width="34.42622950819672" x="0.0" y="0.0" />', '<rect class="qr-marker-inner" fill="#fff" height="22.62295081967213" width="22.62295081967213" x="5.901639344262295" y="5.901639344262295" />', '<rect class="qr-marker-outer" fill="#000000" height="34.42622950819672" width="34.42622950819672" x="265.57377049180326" y="0.0" />', '<rect class="qr-marker-inner" fill="#fff" height="22.62295081967213" width="22.62295081967213" x="271.4754098360656" y="5.901639344262295" />' ]
QRPreview.tsx:177:41
Original design options: 
Object { size: 256, margin: 10, pattern: "dots", marker_border_style: "octagon", marker_border_color: "#000000", marker_center_style: "grid", marker_center_color: "#000000", logo_size: 48, logo_position: "center", logo_opacity: 1, … }
QRPreview.tsx:183:37
Filtered design options for basic preview: 
Object { size: 256, margin: 10, error_correction: "M", foreground_color: "#030000", background_color: "#210303", pattern: "dots" }
QRPreview.tsx:184:37
Filtered options count: 6 QRPreview.tsx:185:37
SVG Styler: Parsing SVG, element count: 1758 svgStyler.ts:23:13
SVG Styler: Design options: 
Object { size: 256, margin: 10, error_correction: "M", foreground_color: "#030000", background_color: "#210303", pattern: "dots" }
svgStyler.ts:24:13
SVG Styler: Applied background color: #210303 svgStyler.ts:46:17
SVG Styler: Found 1763 QR modules to style with foreground color svgStyler.ts:55:17
SVG Styler: Module 1 (rect) - changed fill from "#000000" to "#030000" svgStyler.ts:75:29
SVG Styler: Module 2 (rect) - changed fill from "#000000" to "#030000" svgStyler.ts:75:29
SVG Styler: Module 3 (rect) - changed fill from "#000000" to "#030000" svgStyler.ts:75:29
SVG Styler: Module 4 (rect) - changed fill from "#000000" to "#030000" svgStyler.ts:75:29
SVG Styler: Applying pattern styling: dots svgStyler.ts:94:17
Finder pattern detection - backend classes found: 
Object { markerOuters: 3, markerInners: 3, markerDots: 3 }
svgStyler.ts:232:13
Backend finder pattern 1: 
Object { outer: {…}, inner: {…}, dot: {…} }
svgStyler.ts:260:21
Backend finder pattern 2: 
Object { outer: {…}, inner: {…}, dot: {…} }
svgStyler.ts:260:21
Backend finder pattern 3: 
Object { outer: {…}, inner: {…}, dot: {…} }
svgStyler.ts:260:21
Finder pattern detection summary: 
Object { patternsFound: 3, method: "backend-classes" }
svgStyler.ts:343:13
Pattern styling: 
Object { pattern: "dots", totalRects: 1762, finderElements: 9, dataModules: 1753, markerElements: 9 }
svgStyler.ts:369:13
SVG Styler: Running finder pattern detection... svgStyler.ts:100:13
Finder pattern detection - backend classes found: 
Object { markerOuters: 3, markerInners: 3, markerDots: 3 }
svgStyler.ts:232:13
Backend finder pattern 1: 
Object { outer: {…}, inner: {…}, dot: {…} }
svgStyler.ts:260:21
Backend finder pattern 2: 
Object { outer: {…}, inner: {…}, dot: {…} }
svgStyler.ts:260:21
Backend finder pattern 3: 
Object { outer: {…}, inner: {…}, dot: {…} }
svgStyler.ts:260:21
Finder pattern detection summary: 
Object { patternsFound: 3, method: "backend-classes" }
svgStyler.ts:343:13
SVG Styler: Finder patterns detected: 3 svgStyler.ts:102:13
SVG Styler: Final SVG length: 163666 svgStyler.ts:129:13
Styled SVG: <svg baseProfile="full" height="300" version="1.1" width="300" xmlns="http://www.w3.org/2000/svg" xmlns:ev="http://www.w3.org/2001/xml-events" xmlns:xlink="http://www.w3.org/1999/xlink"><rect class="q... QRPreview.tsx:193:41
[API] Response: 200 OK - POST /qr/preview/svg modernClient.ts:27:24
Successfully received SVG QR preview qrService.modern.ts:429:25
QRPreview: Content key updated 
Object { qrType: "email", dataChanged: true, designChanged: true, logoChanged: false, useSvg: true }
QRPreview.tsx:51:21
QRPreview: Content key updated  
Object { qrType: "email", dataChanged: true, designChanged: true, logoChanged: false, useSvg: true }
dashboard:1:596
QRPreview: Generating basic SVG for frontend styling QRPreview.tsx:108:37
QRPreview: Verifying data for email 
Object { 0: "c", 1: "o", 2: "n", 3: "t", 4: "a", 5: "c", 6: "t", 7: "@", 8: "e", 9: "x", … }
QRPreview.tsx:260:17
QRPreview: Verified data 
Object { 0: "c", 1: "o", 2: "n", 3: "t", 4: "a", 5: "c", 6: "t", 7: "@", 8: "e", 9: "x", … }
QRPreview.tsx:364:17
Frontend QR preview SVG request: 
Object { qrType: "email", dataSize: 363, designOptions: (6) […], hasLogo: false, dimensions: 300 }
qrService.modern.ts:376:21
Cleaned content for SVG preview: 
Object { email: "<EMAIL>", subject: "Hello from QRVibe", body: "I scanned your QR code and wanted to reach out.", cc: "", bcc: "", title: "Email QR Code", description: "Scan to view email content" }
qrService.modern.ts:396:21
[API] Request: POST /qr/preview/svg modernClient.ts:27:24
[API] Added auth token to request modernClient.ts:27:24
Frontend QR preview SVG request: 
Object { qrType: "email", dataSize: 401, designOptions: (32) […], hasLogo: false, dimensions: 300 }
qrService.modern.ts:376:21
Cleaned content for SVG preview: 
Object { email: "<EMAIL>", subject: "Hello from QRVibe", body: "I scanned your QR code and wanted to reach out.", cc: "", bcc: "", title: "Email QR Code", description: "Scan to view email content", email_address: "<EMAIL>" }
qrService.modern.ts:396:21
[API] Request: POST /qr/preview/svg modernClient.ts:27:24
[API] Added auth token to request modernClient.ts:27:24
[API] Response: 200 OK - POST /qr/preview/svg modernClient.ts:27:24
Successfully received SVG QR preview qrService.modern.ts:429:25
Raw SVG from backend: <svg baseProfile="full" height="300" version="1.1" width="300" xmlns="http://www.w3.org/2000/svg" xmlns:ev="http://www.w3.org/2001/xml-events" xmlns:xlink="http://www.w3.org/1999/xlink"><defs /><rect class="qr-bg" fill="#FFFFFF" height="300" id="qr-bg" width="300" x="0" y="0" /><rect class="qr-module" fill="#000000" height="4.918032786885246" id="module-10-0" width="4.918032786885246" x="49.18032786885246" y="0.0" /><rect class="qr-module" fill="#000000" height="4.918032786885246" id="module-14-... QRPreview.tsx:127:37
SVG length: 276037 QRPreview.tsx:128:37
SVG contains <svg>: true QRPreview.tsx:129:37
Raw SVG structure: 
Object { rectCount: 1763, pathCount: 0 }
QRPreview.tsx:133:37
SVG analysis: 
Object { hasRect: true, hasPath: false, hasCircle: false, hasBackground: true, hasForeground: true, totalRects: 1763, largeRects: 7, finderPatternsDetected: true, originalDesignOptions: {…}, designOptionsKeys: (21) […] }
QRPreview.tsx:163:37
Large rectangles (potential finder patterns): 
Array(5) [ '<rect class="qr-bg" fill="#FFFFFF" height="300" id="qr-bg" width="300" x="0" y="0" />', '<rect class="qr-marker-outer" fill="#000000" height="34.42622950819672" width="34.42622950819672" x="0.0" y="0.0" />', '<rect class="qr-marker-inner" fill="#fff" height="22.62295081967213" width="22.62295081967213" x="5.901639344262295" y="5.901639344262295" />', '<rect class="qr-marker-outer" fill="#000000" height="34.42622950819672" width="34.42622950819672" x="265.57377049180326" y="0.0" />', '<rect class="qr-marker-inner" fill="#fff" height="22.62295081967213" width="22.62295081967213" x="271.4754098360656" y="5.901639344262295" />' ]
QRPreview.tsx:177:41
Original design options: 
Object { size: 256, margin: 10, pattern: "dots", marker_border_style: "octagon", marker_border_color: "#9b2121", marker_center_style: "grid", marker_center_color: "#000000", logo_size: 48, logo_position: "center", logo_opacity: 1, … }
QRPreview.tsx:183:37
Filtered design options for basic preview: 
Object { size: 256, margin: 10, error_correction: "M", foreground_color: "#030000", background_color: "#210303", pattern: "dots" }
QRPreview.tsx:184:37
Filtered options count: 6 QRPreview.tsx:185:37
SVG Styler: Parsing SVG, element count: 1758 svgStyler.ts:23:13
SVG Styler: Design options: 
Object { size: 256, margin: 10, error_correction: "M", foreground_color: "#030000", background_color: "#210303", pattern: "dots" }
svgStyler.ts:24:13
SVG Styler: Applied background color: #210303 svgStyler.ts:46:17
SVG Styler: Found 1763 QR modules to style with foreground color svgStyler.ts:55:17
SVG Styler: Module 1 (rect) - changed fill from "#000000" to "#030000" svgStyler.ts:75:29
SVG Styler: Module 2 (rect) - changed fill from "#000000" to "#030000" svgStyler.ts:75:29
SVG Styler: Module 3 (rect) - changed fill from "#000000" to "#030000" svgStyler.ts:75:29
SVG Styler: Module 4 (rect) - changed fill from "#000000" to "#030000" svgStyler.ts:75:29
SVG Styler: Applying pattern styling: dots svgStyler.ts:94:17
Finder pattern detection - backend classes found: 
Object { markerOuters: 3, markerInners: 3, markerDots: 3 }
svgStyler.ts:232:13
Backend finder pattern 1: 
Object { outer: {…}, inner: {…}, dot: {…} }
svgStyler.ts:260:21
Backend finder pattern 2: 
Object { outer: {…}, inner: {…}, dot: {…} }
svgStyler.ts:260:21
Backend finder pattern 3: 
Object { outer: {…}, inner: {…}, dot: {…} }
svgStyler.ts:260:21
Finder pattern detection summary: 
Object { patternsFound: 3, method: "backend-classes" }
svgStyler.ts:343:13
Pattern styling: 
Object { pattern: "dots", totalRects: 1762, finderElements: 9, dataModules: 1753, markerElements: 9 }
svgStyler.ts:369:13
SVG Styler: Running finder pattern detection... svgStyler.ts:100:13
Finder pattern detection - backend classes found: 
Object { markerOuters: 3, markerInners: 3, markerDots: 3 }
svgStyler.ts:232:13
Backend finder pattern 1: 
Object { outer: {…}, inner: {…}, dot: {…} }
svgStyler.ts:260:21
Backend finder pattern 2: 
Object { outer: {…}, inner: {…}, dot: {…} }
svgStyler.ts:260:21
Backend finder pattern 3: 
Object { outer: {…}, inner: {…}, dot: {…} }
svgStyler.ts:260:21
Finder pattern detection summary: 
Object { patternsFound: 3, method: "backend-classes" }
svgStyler.ts:343:13
SVG Styler: Finder patterns detected: 3 svgStyler.ts:102:13
SVG Styler: Final SVG length: 163666 svgStyler.ts:129:13
Styled SVG: <svg baseProfile="full" height="300" version="1.1" width="300" xmlns="http://www.w3.org/2000/svg" xmlns:ev="http://www.w3.org/2001/xml-events" xmlns:xlink="http://www.w3.org/1999/xlink"><rect class="q... QRPreview.tsx:193:41
[API] Response: 200 OK - POST /qr/preview/svg modernClient.ts:27:24
Successfully received SVG QR preview qrService.modern.ts:429:25
QRPreview: Content key updated 
Object { qrType: "email", dataChanged: true, designChanged: true, logoChanged: false, useSvg: true }
QRPreview.tsx:51:21
QRPreview: Content key updated  
Object { qrType: "email", dataChanged: true, designChanged: true, logoChanged: false, useSvg: true }
dashboard:1:596
QRPreview: Generating basic SVG for frontend styling QRPreview.tsx:108:37
QRPreview: Verifying data for email 
Object { 0: "c", 1: "o", 2: "n", 3: "t", 4: "a", 5: "c", 6: "t", 7: "@", 8: "e", 9: "x", … }
QRPreview.tsx:260:17
QRPreview: Verified data 
Object { 0: "c", 1: "o", 2: "n", 3: "t", 4: "a", 5: "c", 6: "t", 7: "@", 8: "e", 9: "x", … }
QRPreview.tsx:364:17
Frontend QR preview SVG request: 
Object { qrType: "email", dataSize: 363, designOptions: (6) […], hasLogo: false, dimensions: 300 }
qrService.modern.ts:376:21
Cleaned content for SVG preview: 
Object { email: "<EMAIL>", subject: "Hello from QRVibe", body: "I scanned your QR code and wanted to reach out.", cc: "", bcc: "", title: "Email QR Code", description: "Scan to view email content" }
qrService.modern.ts:396:21
[API] Request: POST /qr/preview/svg modernClient.ts:27:24
[API] Added auth token to request modernClient.ts:27:24
Frontend QR preview SVG request: 
Object { qrType: "email", dataSize: 401, designOptions: (32) […], hasLogo: false, dimensions: 300 }
qrService.modern.ts:376:21
Cleaned content for SVG preview: 
Object { email: "<EMAIL>", subject: "Hello from QRVibe", body: "I scanned your QR code and wanted to reach out.", cc: "", bcc: "", title: "Email QR Code", description: "Scan to view email content", email_address: "<EMAIL>" }
qrService.modern.ts:396:21
[API] Request: POST /qr/preview/svg modernClient.ts:27:24
[API] Added auth token to request modernClient.ts:27:24
This site appears to use a scroll-linked positioning effect. This may not work well with asynchronous panning; see https://firefox-source-docs.mozilla.org/performance/scroll-linked_effects.html for further details and to join the discussion on related tools and features! creator
[API] Response: 200 OK - POST /qr/preview/svg modernClient.ts:27:24
Successfully received SVG QR preview qrService.modern.ts:429:25
Raw SVG from backend: <svg baseProfile="full" height="300" version="1.1" width="300" xmlns="http://www.w3.org/2000/svg" xmlns:ev="http://www.w3.org/2001/xml-events" xmlns:xlink="http://www.w3.org/1999/xlink"><defs /><rect class="qr-bg" fill="#FFFFFF" height="300" id="qr-bg" width="300" x="0" y="0" /><rect class="qr-module" fill="#000000" height="4.918032786885246" id="module-10-0" width="4.918032786885246" x="49.18032786885246" y="0.0" /><rect class="qr-module" fill="#000000" height="4.918032786885246" id="module-14-... QRPreview.tsx:127:37
SVG length: 276037 QRPreview.tsx:128:37
SVG contains <svg>: true QRPreview.tsx:129:37
Raw SVG structure: 
Object { rectCount: 1763, pathCount: 0 }
QRPreview.tsx:133:37
SVG analysis: 
Object { hasRect: true, hasPath: false, hasCircle: false, hasBackground: true, hasForeground: true, totalRects: 1763, largeRects: 7, finderPatternsDetected: true, originalDesignOptions: {…}, designOptionsKeys: (21) […] }
QRPreview.tsx:163:37
Large rectangles (potential finder patterns): 
Array(5) [ '<rect class="qr-bg" fill="#FFFFFF" height="300" id="qr-bg" width="300" x="0" y="0" />', '<rect class="qr-marker-outer" fill="#000000" height="34.42622950819672" width="34.42622950819672" x="0.0" y="0.0" />', '<rect class="qr-marker-inner" fill="#fff" height="22.62295081967213" width="22.62295081967213" x="5.901639344262295" y="5.901639344262295" />', '<rect class="qr-marker-outer" fill="#000000" height="34.42622950819672" width="34.42622950819672" x="265.57377049180326" y="0.0" />', '<rect class="qr-marker-inner" fill="#fff" height="22.62295081967213" width="22.62295081967213" x="271.4754098360656" y="5.901639344262295" />' ]
QRPreview.tsx:177:41
Original design options: 
Object { size: 256, margin: 10, pattern: "dots", marker_border_style: "octagon", marker_border_color: "#cc1313", marker_center_style: "grid", marker_center_color: "#000000", logo_size: 48, logo_position: "center", logo_opacity: 1, … }
QRPreview.tsx:183:37
Filtered design options for basic preview: 
Object { size: 256, margin: 10, error_correction: "M", foreground_color: "#030000", background_color: "#210303", pattern: "dots" }
QRPreview.tsx:184:37
Filtered options count: 6 QRPreview.tsx:185:37
SVG Styler: Parsing SVG, element count: 1758 svgStyler.ts:23:13
SVG Styler: Design options: 
Object { size: 256, margin: 10, error_correction: "M", foreground_color: "#030000", background_color: "#210303", pattern: "dots" }
svgStyler.ts:24:13
SVG Styler: Applied background color: #210303 svgStyler.ts:46:17
SVG Styler: Found 1763 QR modules to style with foreground color svgStyler.ts:55:17
SVG Styler: Module 1 (rect) - changed fill from "#000000" to "#030000" svgStyler.ts:75:29
SVG Styler: Module 2 (rect) - changed fill from "#000000" to "#030000" svgStyler.ts:75:29
SVG Styler: Module 3 (rect) - changed fill from "#000000" to "#030000" svgStyler.ts:75:29
SVG Styler: Module 4 (rect) - changed fill from "#000000" to "#030000" svgStyler.ts:75:29
SVG Styler: Applying pattern styling: dots svgStyler.ts:94:17
Finder pattern detection - backend classes found: 
Object { markerOuters: 3, markerInners: 3, markerDots: 3 }
svgStyler.ts:232:13
Backend finder pattern 1: 
Object { outer: {…}, inner: {…}, dot: {…} }
svgStyler.ts:260:21
Backend finder pattern 2: 
Object { outer: {…}, inner: {…}, dot: {…} }
svgStyler.ts:260:21
Backend finder pattern 3: 
Object { outer: {…}, inner: {…}, dot: {…} }
svgStyler.ts:260:21
Finder pattern detection summary: 
Object { patternsFound: 3, method: "backend-classes" }
svgStyler.ts:343:13
Pattern styling: 
Object { pattern: "dots", totalRects: 1762, finderElements: 9, dataModules: 1753, markerElements: 9 }
svgStyler.ts:369:13
SVG Styler: Running finder pattern detection... svgStyler.ts:100:13
Finder pattern detection - backend classes found: 
Object { markerOuters: 3, markerInners: 3, markerDots: 3 }
svgStyler.ts:232:13
Backend finder pattern 1: 
Object { outer: {…}, inner: {…}, dot: {…} }
svgStyler.ts:260:21
Backend finder pattern 2: 
Object { outer: {…}, inner: {…}, dot: {…} }
svgStyler.ts:260:21
Backend finder pattern 3: 
Object { outer: {…}, inner: {…}, dot: {…} }
svgStyler.ts:260:21
Finder pattern detection summary: 
Object { patternsFound: 3, method: "backend-classes" }
svgStyler.ts:343:13
SVG Styler: Finder patterns detected: 3 svgStyler.ts:102:13
SVG Styler: Final SVG length: 163666 svgStyler.ts:129:13
Styled SVG: <svg baseProfile="full" height="300" version="1.1" width="300" xmlns="http://www.w3.org/2000/svg" xmlns:ev="http://www.w3.org/2001/xml-events" xmlns:xlink="http://www.w3.org/1999/xlink"><rect class="q... QRPreview.tsx:193:41
[API] Response: 200 OK - POST /qr/preview/svg modernClient.ts:27:24
Successfully received SVG QR preview qrService.modern.ts:429:25
QRPreview: Content key updated 
Object { qrType: "email", dataChanged: true, designChanged: true, logoChanged: false, useSvg: true }
QRPreview.tsx:51:21
QRPreview: Content key updated  
Object { qrType: "email", dataChanged: true, designChanged: true, logoChanged: false, useSvg: true }
dashboard:1:596
QRPreview: Generating basic SVG for frontend styling QRPreview.tsx:108:37
QRPreview: Verifying data for email 
Object { 0: "c", 1: "o", 2: "n", 3: "t", 4: "a", 5: "c", 6: "t", 7: "@", 8: "e", 9: "x", … }
QRPreview.tsx:260:17
QRPreview: Verified data 
Object { 0: "c", 1: "o", 2: "n", 3: "t", 4: "a", 5: "c", 6: "t", 7: "@", 8: "e", 9: "x", … }
QRPreview.tsx:364:17
Frontend QR preview SVG request: 
Object { qrType: "email", dataSize: 363, designOptions: (6) […], hasLogo: false, dimensions: 300 }
qrService.modern.ts:376:21
Cleaned content for SVG preview: 
Object { email: "<EMAIL>", subject: "Hello from QRVibe", body: "I scanned your QR code and wanted to reach out.", cc: "", bcc: "", title: "Email QR Code", description: "Scan to view email content" }
qrService.modern.ts:396:21
[API] Request: POST /qr/preview/svg modernClient.ts:27:24
[API] Added auth token to request modernClient.ts:27:24
Frontend QR preview SVG request: 
Object { qrType: "email", dataSize: 401, designOptions: (32) […], hasLogo: false, dimensions: 300 }
qrService.modern.ts:376:21
Cleaned content for SVG preview: 
Object { email: "<EMAIL>", subject: "Hello from QRVibe", body: "I scanned your QR code and wanted to reach out.", cc: "", bcc: "", title: "Email QR Code", description: "Scan to view email content", email_address: "<EMAIL>" }
qrService.modern.ts:396:21
[API] Request: POST /qr/preview/svg modernClient.ts:27:24
[API] Added auth token to request modernClient.ts:27:24
[API] Response: 200 OK - POST /qr/preview/svg modernClient.ts:27:24
Successfully received SVG QR preview qrService.modern.ts:429:25
Raw SVG from backend: <svg baseProfile="full" height="300" version="1.1" width="300" xmlns="http://www.w3.org/2000/svg" xmlns:ev="http://www.w3.org/2001/xml-events" xmlns:xlink="http://www.w3.org/1999/xlink"><defs /><rect class="qr-bg" fill="#FFFFFF" height="300" id="qr-bg" width="300" x="0" y="0" /><rect class="qr-module" fill="#000000" height="4.918032786885246" id="module-10-0" width="4.918032786885246" x="49.18032786885246" y="0.0" /><rect class="qr-module" fill="#000000" height="4.918032786885246" id="module-14-... QRPreview.tsx:127:37
SVG length: 276037 QRPreview.tsx:128:37
SVG contains <svg>: true QRPreview.tsx:129:37
Raw SVG structure: 
Object { rectCount: 1763, pathCount: 0 }
QRPreview.tsx:133:37
SVG analysis: 
Object { hasRect: true, hasPath: false, hasCircle: false, hasBackground: true, hasForeground: true, totalRects: 1763, largeRects: 7, finderPatternsDetected: true, originalDesignOptions: {…}, designOptionsKeys: (21) […] }
QRPreview.tsx:163:37
Large rectangles (potential finder patterns): 
Array(5) [ '<rect class="qr-bg" fill="#FFFFFF" height="300" id="qr-bg" width="300" x="0" y="0" />', '<rect class="qr-marker-outer" fill="#000000" height="34.42622950819672" width="34.42622950819672" x="0.0" y="0.0" />', '<rect class="qr-marker-inner" fill="#fff" height="22.62295081967213" width="22.62295081967213" x="5.901639344262295" y="5.901639344262295" />', '<rect class="qr-marker-outer" fill="#000000" height="34.42622950819672" width="34.42622950819672" x="265.57377049180326" y="0.0" />', '<rect class="qr-marker-inner" fill="#fff" height="22.62295081967213" width="22.62295081967213" x="271.4754098360656" y="5.901639344262295" />' ]
QRPreview.tsx:177:41
Original design options: 
Object { size: 256, margin: 10, pattern: "dots", marker_border_style: "octagon", marker_border_color: "#cc1313", marker_center_style: "grid", marker_center_color: "#e91414", logo_size: 48, logo_position: "center", logo_opacity: 1, … }
QRPreview.tsx:183:37
Filtered design options for basic preview: 
Object { size: 256, margin: 10, error_correction: "M", foreground_color: "#030000", background_color: "#210303", pattern: "dots" }
QRPreview.tsx:184:37
Filtered options count: 6 QRPreview.tsx:185:37
SVG Styler: Parsing SVG, element count: 1758 svgStyler.ts:23:13
SVG Styler: Design options: 
Object { size: 256, margin: 10, error_correction: "M", foreground_color: "#030000", background_color: "#210303", pattern: "dots" }
svgStyler.ts:24:13
SVG Styler: Applied background color: #210303 svgStyler.ts:46:17
SVG Styler: Found 1763 QR modules to style with foreground color svgStyler.ts:55:17
SVG Styler: Module 1 (rect) - changed fill from "#000000" to "#030000" svgStyler.ts:75:29
SVG Styler: Module 2 (rect) - changed fill from "#000000" to "#030000" svgStyler.ts:75:29
SVG Styler: Module 3 (rect) - changed fill from "#000000" to "#030000" svgStyler.ts:75:29
SVG Styler: Module 4 (rect) - changed fill from "#000000" to "#030000" svgStyler.ts:75:29
SVG Styler: Applying pattern styling: dots svgStyler.ts:94:17
Finder pattern detection - backend classes found: 
Object { markerOuters: 3, markerInners: 3, markerDots: 3 }
svgStyler.ts:232:13
Backend finder pattern 1: 
Object { outer: {…}, inner: {…}, dot: {…} }
svgStyler.ts:260:21
Backend finder pattern 2: 
Object { outer: {…}, inner: {…}, dot: {…} }
svgStyler.ts:260:21
Backend finder pattern 3: 
Object { outer: {…}, inner: {…}, dot: {…} }
svgStyler.ts:260:21
Finder pattern detection summary: 
Object { patternsFound: 3, method: "backend-classes" }
svgStyler.ts:343:13
Pattern styling: 
Object { pattern: "dots", totalRects: 1762, finderElements: 9, dataModules: 1753, markerElements: 9 }
svgStyler.ts:369:13
SVG Styler: Running finder pattern detection... svgStyler.ts:100:13
Finder pattern detection - backend classes found: 
Object { markerOuters: 3, markerInners: 3, markerDots: 3 }
svgStyler.ts:232:13
Backend finder pattern 1: 
Object { outer: {…}, inner: {…}, dot: {…} }
svgStyler.ts:260:21
Backend finder pattern 2: 
Object { outer: {…}, inner: {…}, dot: {…} }
svgStyler.ts:260:21
Backend finder pattern 3: 
Object { outer: {…}, inner: {…}, dot: {…} }
svgStyler.ts:260:21
Finder pattern detection summary: 
Object { patternsFound: 3, method: "backend-classes" }
svgStyler.ts:343:13
SVG Styler: Finder patterns detected: 3 svgStyler.ts:102:13
SVG Styler: Final SVG length: 163666 svgStyler.ts:129:13
Styled SVG: <svg baseProfile="full" height="300" version="1.1" width="300" xmlns="http://www.w3.org/2000/svg" xmlns:ev="http://www.w3.org/2001/xml-events" xmlns:xlink="http://www.w3.org/1999/xlink"><rect class="q... QRPreview.tsx:193:41
[API] Response: 200 OK - POST /qr/preview/svg modernClient.ts:27:24
Successfully received SVG QR preview qrService.modern.ts:429:25
QRPreview: Content key updated 
Object { qrType: "email", dataChanged: true, designChanged: true, logoChanged: true, useSvg: true }
QRPreview.tsx:51:21
QRPreview: Content key updated  
Object { qrType: "email", dataChanged: true, designChanged: true, logoChanged: true, useSvg: true }
dashboard:1:596
QRPreview: Content key updated 
Object { qrType: "email", dataChanged: true, designChanged: true, logoChanged: true, useSvg: true }
QRPreview.tsx:51:21
QRPreview: Content key updated  
Object { qrType: "email", dataChanged: true, designChanged: true, logoChanged: true, useSvg: true }
dashboard:1:596
QRPreview: Content key updated 
Object { qrType: "email", dataChanged: true, designChanged: true, logoChanged: true, useSvg: true }
QRPreview.tsx:51:21
QRPreview: Content key updated  
Object { qrType: "email", dataChanged: true, designChanged: true, logoChanged: true, useSvg: true }
dashboard:1:596
QRPreview: Generating basic SVG for frontend styling QRPreview.tsx:108:37
QRPreview: Verifying data for email 
Object { 0: "c", 1: "o", 2: "n", 3: "t", 4: "a", 5: "c", 6: "t", 7: "@", 8: "e", 9: "x", … }
QRPreview.tsx:260:17
QRPreview: Verified data 
Object { 0: "c", 1: "o", 2: "n", 3: "t", 4: "a", 5: "c", 6: "t", 7: "@", 8: "e", 9: "x", … }
QRPreview.tsx:364:17
Frontend QR preview SVG request: 
Object { qrType: "email", dataSize: 363, designOptions: (6) […], hasLogo: true, dimensions: 300 }
qrService.modern.ts:376:21
Cleaned content for SVG preview: 
Object { email: "<EMAIL>", subject: "Hello from QRVibe", body: "I scanned your QR code and wanted to reach out.", cc: "", bcc: "", title: "Email QR Code", description: "Scan to view email content" }
qrService.modern.ts:396:21
[API] Request: POST /qr/preview/svg modernClient.ts:27:24
[API] Added auth token to request modernClient.ts:27:24
Frontend QR preview SVG request: 
Object { qrType: "email", dataSize: 401, designOptions: (32) […], hasLogo: true, dimensions: 300 }
qrService.modern.ts:376:21
Cleaned content for SVG preview: 
Object { email: "<EMAIL>", subject: "Hello from QRVibe", body: "I scanned your QR code and wanted to reach out.", cc: "", bcc: "", title: "Email QR Code", description: "Scan to view email content", email_address: "<EMAIL>" }
qrService.modern.ts:396:21
[API] Request: POST /qr/preview/svg modernClient.ts:27:24
[API] Added auth token to request modernClient.ts:27:24
[API] Response: 200 OK - POST /qr/preview/svg modernClient.ts:27:24
Successfully received SVG QR preview qrService.modern.ts:429:25
Raw SVG from backend: <svg baseProfile="full" height="300" version="1.1" width="300" xmlns="http://www.w3.org/2000/svg" xmlns:ev="http://www.w3.org/2001/xml-events" xmlns:xlink="http://www.w3.org/1999/xlink"><defs /><rect class="qr-bg" fill="#FFFFFF" height="300" id="qr-bg" width="300" x="0" y="0" /><rect class="qr-module" fill="#000000" height="4.918032786885246" id="module-10-0" width="4.918032786885246" x="49.18032786885246" y="0.0" /><rect class="qr-module" fill="#000000" height="4.918032786885246" id="module-14-... QRPreview.tsx:127:37
SVG length: 276037 QRPreview.tsx:128:37
SVG contains <svg>: true QRPreview.tsx:129:37
Raw SVG structure: 
Object { rectCount: 1763, pathCount: 0 }
QRPreview.tsx:133:37
SVG analysis: 
Object { hasRect: true, hasPath: false, hasCircle: false, hasBackground: true, hasForeground: true, totalRects: 1763, largeRects: 7, finderPatternsDetected: true, originalDesignOptions: {…}, designOptionsKeys: (21) […] }
QRPreview.tsx:163:37
Large rectangles (potential finder patterns): 
Array(5) [ '<rect class="qr-bg" fill="#FFFFFF" height="300" id="qr-bg" width="300" x="0" y="0" />', '<rect class="qr-marker-outer" fill="#000000" height="34.42622950819672" width="34.42622950819672" x="0.0" y="0.0" />', '<rect class="qr-marker-inner" fill="#fff" height="22.62295081967213" width="22.62295081967213" x="5.901639344262295" y="5.901639344262295" />', '<rect class="qr-marker-outer" fill="#000000" height="34.42622950819672" width="34.42622950819672" x="265.57377049180326" y="0.0" />', '<rect class="qr-marker-inner" fill="#fff" height="22.62295081967213" width="22.62295081967213" x="271.4754098360656" y="5.901639344262295" />' ]
QRPreview.tsx:177:41
Original design options: 
Object { size: 256, margin: 10, pattern: "dots", marker_border_style: "octagon", marker_border_color: "#cc1313", marker_center_style: "grid", marker_center_color: "#e91414", logo_size: 48, logo_position: "center", logo_opacity: 1, … }
QRPreview.tsx:183:37
Filtered design options for basic preview: 
Object { size: 256, margin: 10, error_correction: "M", foreground_color: "#030000", background_color: "#210303", pattern: "dots" }
QRPreview.tsx:184:37
Filtered options count: 6 QRPreview.tsx:185:37
SVG Styler: Parsing SVG, element count: 1758 svgStyler.ts:23:13
SVG Styler: Design options: 
Object { size: 256, margin: 10, error_correction: "M", foreground_color: "#030000", background_color: "#210303", pattern: "dots" }
svgStyler.ts:24:13
SVG Styler: Applied background color: #210303 svgStyler.ts:46:17
SVG Styler: Found 1763 QR modules to style with foreground color svgStyler.ts:55:17
SVG Styler: Module 1 (rect) - changed fill from "#000000" to "#030000" svgStyler.ts:75:29
SVG Styler: Module 2 (rect) - changed fill from "#000000" to "#030000" svgStyler.ts:75:29
SVG Styler: Module 3 (rect) - changed fill from "#000000" to "#030000" svgStyler.ts:75:29
SVG Styler: Module 4 (rect) - changed fill from "#000000" to "#030000" svgStyler.ts:75:29
SVG Styler: Applying pattern styling: dots svgStyler.ts:94:17
Finder pattern detection - backend classes found: 
Object { markerOuters: 3, markerInners: 3, markerDots: 3 }
svgStyler.ts:232:13
Backend finder pattern 1: 
Object { outer: {…}, inner: {…}, dot: {…} }
svgStyler.ts:260:21
Backend finder pattern 2: 
Object { outer: {…}, inner: {…}, dot: {…} }
svgStyler.ts:260:21
Backend finder pattern 3: 
Object { outer: {…}, inner: {…}, dot: {…} }
svgStyler.ts:260:21
Finder pattern detection summary: 
Object { patternsFound: 3, method: "backend-classes" }
svgStyler.ts:343:13
Pattern styling: 
Object { pattern: "dots", totalRects: 1762, finderElements: 9, dataModules: 1753, markerElements: 9 }
svgStyler.ts:369:13
SVG Styler: Running finder pattern detection... svgStyler.ts:100:13
Finder pattern detection - backend classes found: 
Object { markerOuters: 3, markerInners: 3, markerDots: 3 }
svgStyler.ts:232:13
Backend finder pattern 1: 
Object { outer: {…}, inner: {…}, dot: {…} }
svgStyler.ts:260:21
Backend finder pattern 2: 
Object { outer: {…}, inner: {…}, dot: {…} }
svgStyler.ts:260:21
Backend finder pattern 3: 
Object { outer: {…}, inner: {…}, dot: {…} }
svgStyler.ts:260:21
Finder pattern detection summary: 
Object { patternsFound: 3, method: "backend-classes" }
svgStyler.ts:343:13
SVG Styler: Finder patterns detected: 3 svgStyler.ts:102:13
SVG Styler: Final SVG length: 163666 svgStyler.ts:129:13
Styled SVG: <svg baseProfile="full" height="300" version="1.1" width="300" xmlns="http://www.w3.org/2000/svg" xmlns:ev="http://www.w3.org/2001/xml-events" xmlns:xlink="http://www.w3.org/1999/xlink"><rect class="q... QRPreview.tsx:193:41
[API] Response: 200 OK - POST /qr/preview/svg modernClient.ts:27:24
Successfully received SVG QR preview qrService.modern.ts:429:25
QRPreview: Content key updated 
Object { qrType: "email", dataChanged: true, designChanged: true, logoChanged: true, useSvg: true }
QRPreview.tsx:51:21
QRPreview: Content key updated  
Object { qrType: "email", dataChanged: true, designChanged: true, logoChanged: true, useSvg: true }
dashboard:1:596
QRPreview: Generating basic SVG for frontend styling QRPreview.tsx:108:37
QRPreview: Verifying data for email 
Object { 0: "c", 1: "o", 2: "n", 3: "t", 4: "a", 5: "c", 6: "t", 7: "@", 8: "e", 9: "x", … }
QRPreview.tsx:260:17
QRPreview: Verified data 
Object { 0: "c", 1: "o", 2: "n", 3: "t", 4: "a", 5: "c", 6: "t", 7: "@", 8: "e", 9: "x", … }
QRPreview.tsx:364:17
Frontend QR preview SVG request: 
Object { qrType: "email", dataSize: 363, designOptions: (6) […], hasLogo: true, dimensions: 300 }
qrService.modern.ts:376:21
Cleaned content for SVG preview: 
Object { email: "<EMAIL>", subject: "Hello from QRVibe", body: "I scanned your QR code and wanted to reach out.", cc: "", bcc: "", title: "Email QR Code", description: "Scan to view email content" }
qrService.modern.ts:396:21
[API] Request: POST /qr/preview/svg modernClient.ts:27:24
[API] Added auth token to request modernClient.ts:27:24
Frontend QR preview SVG request: 
Object { qrType: "email", dataSize: 401, designOptions: (32) […], hasLogo: true, dimensions: 300 }
qrService.modern.ts:376:21
Cleaned content for SVG preview: 
Object { email: "<EMAIL>", subject: "Hello from QRVibe", body: "I scanned your QR code and wanted to reach out.", cc: "", bcc: "", title: "Email QR Code", description: "Scan to view email content", email_address: "<EMAIL>" }
qrService.modern.ts:396:21
[API] Request: POST /qr/preview/svg modernClient.ts:27:24
[API] Added auth token to request modernClient.ts:27:24
[API] Response: 200 OK - POST /qr/preview/svg modernClient.ts:27:24
Successfully received SVG QR preview qrService.modern.ts:429:25
Raw SVG from backend: <svg baseProfile="full" height="300" version="1.1" width="300" xmlns="http://www.w3.org/2000/svg" xmlns:ev="http://www.w3.org/2001/xml-events" xmlns:xlink="http://www.w3.org/1999/xlink"><defs /><rect class="qr-bg" fill="#FFFFFF" height="300" id="qr-bg" width="300" x="0" y="0" /><rect class="qr-module" fill="#000000" height="4.918032786885246" id="module-10-0" width="4.918032786885246" x="49.18032786885246" y="0.0" /><rect class="qr-module" fill="#000000" height="4.918032786885246" id="module-14-... QRPreview.tsx:127:37
SVG length: 276037 QRPreview.tsx:128:37
SVG contains <svg>: true QRPreview.tsx:129:37
Raw SVG structure: 
Object { rectCount: 1763, pathCount: 0 }
QRPreview.tsx:133:37
SVG analysis: 
Object { hasRect: true, hasPath: false, hasCircle: false, hasBackground: true, hasForeground: true, totalRects: 1763, largeRects: 7, finderPatternsDetected: true, originalDesignOptions: {…}, designOptionsKeys: (21) […] }
QRPreview.tsx:163:37
Large rectangles (potential finder patterns): 
Array(5) [ '<rect class="qr-bg" fill="#FFFFFF" height="300" id="qr-bg" width="300" x="0" y="0" />', '<rect class="qr-marker-outer" fill="#000000" height="34.42622950819672" width="34.42622950819672" x="0.0" y="0.0" />', '<rect class="qr-marker-inner" fill="#fff" height="22.62295081967213" width="22.62295081967213" x="5.901639344262295" y="5.901639344262295" />', '<rect class="qr-marker-outer" fill="#000000" height="34.42622950819672" width="34.42622950819672" x="265.57377049180326" y="0.0" />', '<rect class="qr-marker-inner" fill="#fff" height="22.62295081967213" width="22.62295081967213" x="271.4754098360656" y="5.901639344262295" />' ]
QRPreview.tsx:177:41
Original design options: 
Object { size: 256, margin: 10, pattern: "dots", marker_border_style: "octagon", marker_border_color: "#cc1313", marker_center_style: "grid", marker_center_color: "#e91414", logo_size: 48, logo_position: "center", logo_opacity: 1, … }
QRPreview.tsx:183:37
Filtered design options for basic preview: 
Object { size: 256, margin: 10, error_correction: "M", foreground_color: "#030000", background_color: "#210303", pattern: "dots", frame_style: "rounded", frame_color: "#000000", frame_thickness: 4 }
QRPreview.tsx:184:37
Filtered options count: 9 QRPreview.tsx:185:37
SVG Styler: Parsing SVG, element count: 1758 svgStyler.ts:23:13
SVG Styler: Design options: 
Object { size: 256, margin: 10, error_correction: "M", foreground_color: "#030000", background_color: "#210303", pattern: "dots", frame_style: "rounded", frame_color: "#000000", frame_thickness: 4 }
svgStyler.ts:24:13
SVG Styler: Applied background color: #210303 svgStyler.ts:46:17
SVG Styler: Found 1763 QR modules to style with foreground color svgStyler.ts:55:17
SVG Styler: Module 1 (rect) - changed fill from "#000000" to "#030000" svgStyler.ts:75:29
SVG Styler: Module 2 (rect) - changed fill from "#000000" to "#030000" svgStyler.ts:75:29
SVG Styler: Module 3 (rect) - changed fill from "#000000" to "#030000" svgStyler.ts:75:29
SVG Styler: Module 4 (rect) - changed fill from "#000000" to "#030000" svgStyler.ts:75:29
SVG Styler: Applying pattern styling: dots svgStyler.ts:94:17
Finder pattern detection - backend classes found: 
Object { markerOuters: 3, markerInners: 3, markerDots: 3 }
svgStyler.ts:232:13
Backend finder pattern 1: 
Object { outer: {…}, inner: {…}, dot: {…} }
svgStyler.ts:260:21
Backend finder pattern 2: 
Object { outer: {…}, inner: {…}, dot: {…} }
svgStyler.ts:260:21
Backend finder pattern 3: 
Object { outer: {…}, inner: {…}, dot: {…} }
svgStyler.ts:260:21
Finder pattern detection summary: 
Object { patternsFound: 3, method: "backend-classes" }
svgStyler.ts:343:13
Pattern styling: 
Object { pattern: "dots", totalRects: 1762, finderElements: 9, dataModules: 1753, markerElements: 9 }
svgStyler.ts:369:13
SVG Styler: Running finder pattern detection... svgStyler.ts:100:13
Finder pattern detection - backend classes found: 
Object { markerOuters: 3, markerInners: 3, markerDots: 3 }
svgStyler.ts:232:13
Backend finder pattern 1: 
Object { outer: {…}, inner: {…}, dot: {…} }
svgStyler.ts:260:21
Backend finder pattern 2: 
Object { outer: {…}, inner: {…}, dot: {…} }
svgStyler.ts:260:21
Backend finder pattern 3: 
Object { outer: {…}, inner: {…}, dot: {…} }
svgStyler.ts:260:21
Finder pattern detection summary: 
Object { patternsFound: 3, method: "backend-classes" }
svgStyler.ts:343:13
SVG Styler: Finder patterns detected: 3 svgStyler.ts:102:13
SVG Styler: Final SVG length: 163796 svgStyler.ts:129:13
Styled SVG: <svg baseProfile="full" height="300" version="1.1" width="300" xmlns="http://www.w3.org/2000/svg" xmlns:ev="http://www.w3.org/2001/xml-events" xmlns:xlink="http://www.w3.org/1999/xlink"><rect class="q... QRPreview.tsx:193:41
[API] Response: 200 OK - POST /qr/preview/svg modernClient.ts:27:24
Successfully received SVG QR preview qrService.modern.ts:429:25
QRPreview: Content key updated 
Object { qrType: "email", dataChanged: true, designChanged: true, logoChanged: true, useSvg: true }
QRPreview.tsx:51:21
QRPreview: Content key updated  
Object { qrType: "email", dataChanged: true, designChanged: true, logoChanged: true, useSvg: true }
dashboard:1:596
QRPreview: Generating basic SVG for frontend styling QRPreview.tsx:108:37
QRPreview: Verifying data for email 
Object { 0: "c", 1: "o", 2: "n", 3: "t", 4: "a", 5: "c", 6: "t", 7: "@", 8: "e", 9: "x", … }
QRPreview.tsx:260:17
QRPreview: Verified data 
Object { 0: "c", 1: "o", 2: "n", 3: "t", 4: "a", 5: "c", 6: "t", 7: "@", 8: "e", 9: "x", … }
QRPreview.tsx:364:17
Frontend QR preview SVG request: 
Object { qrType: "email", dataSize: 363, designOptions: (6) […], hasLogo: true, dimensions: 300 }
qrService.modern.ts:376:21
Cleaned content for SVG preview: 
Object { email: "<EMAIL>", subject: "Hello from QRVibe", body: "I scanned your QR code and wanted to reach out.", cc: "", bcc: "", title: "Email QR Code", description: "Scan to view email content" }
qrService.modern.ts:396:21
[API] Request: POST /qr/preview/svg modernClient.ts:27:24
[API] Added auth token to request modernClient.ts:27:24
Frontend QR preview SVG request: 
Object { qrType: "email", dataSize: 401, designOptions: (32) […], hasLogo: true, dimensions: 300 }
qrService.modern.ts:376:21
Cleaned content for SVG preview: 
Object { email: "<EMAIL>", subject: "Hello from QRVibe", body: "I scanned your QR code and wanted to reach out.", cc: "", bcc: "", title: "Email QR Code", description: "Scan to view email content", email_address: "<EMAIL>" }
qrService.modern.ts:396:21
[API] Request: POST /qr/preview/svg modernClient.ts:27:24
[API] Added auth token to request modernClient.ts:27:24
[API] Response: 200 OK - POST /qr/preview/svg modernClient.ts:27:24
Successfully received SVG QR preview qrService.modern.ts:429:25
Raw SVG from backend: <svg baseProfile="full" height="300" version="1.1" width="300" xmlns="http://www.w3.org/2000/svg" xmlns:ev="http://www.w3.org/2001/xml-events" xmlns:xlink="http://www.w3.org/1999/xlink"><defs /><rect class="qr-bg" fill="#FFFFFF" height="300" id="qr-bg" width="300" x="0" y="0" /><rect class="qr-module" fill="#000000" height="4.918032786885246" id="module-10-0" width="4.918032786885246" x="49.18032786885246" y="0.0" /><rect class="qr-module" fill="#000000" height="4.918032786885246" id="module-14-... QRPreview.tsx:127:37
SVG length: 276037 QRPreview.tsx:128:37
SVG contains <svg>: true QRPreview.tsx:129:37
Raw SVG structure: 
Object { rectCount: 1763, pathCount: 0 }
QRPreview.tsx:133:37
SVG analysis: 
Object { hasRect: true, hasPath: false, hasCircle: false, hasBackground: true, hasForeground: true, totalRects: 1763, largeRects: 7, finderPatternsDetected: true, originalDesignOptions: {…}, designOptionsKeys: (21) […] }
QRPreview.tsx:163:37
Large rectangles (potential finder patterns): 
Array(5) [ '<rect class="qr-bg" fill="#FFFFFF" height="300" id="qr-bg" width="300" x="0" y="0" />', '<rect class="qr-marker-outer" fill="#000000" height="34.42622950819672" width="34.42622950819672" x="0.0" y="0.0" />', '<rect class="qr-marker-inner" fill="#fff" height="22.62295081967213" width="22.62295081967213" x="5.901639344262295" y="5.901639344262295" />', '<rect class="qr-marker-outer" fill="#000000" height="34.42622950819672" width="34.42622950819672" x="265.57377049180326" y="0.0" />', '<rect class="qr-marker-inner" fill="#fff" height="22.62295081967213" width="22.62295081967213" x="271.4754098360656" y="5.901639344262295" />' ]
QRPreview.tsx:177:41
Original design options: 
Object { size: 256, margin: 10, pattern: "dots", marker_border_style: "octagon", marker_border_color: "#cc1313", marker_center_style: "grid", marker_center_color: "#e91414", logo_size: 48, logo_position: "center", logo_opacity: 1, … }
QRPreview.tsx:183:37
Filtered design options for basic preview: 
Object { size: 256, margin: 10, error_correction: "M", foreground_color: "#030000", background_color: "#210303", pattern: "dots", frame_style: "modern", frame_color: "#000000", frame_thickness: 4 }
QRPreview.tsx:184:37
Filtered options count: 9 QRPreview.tsx:185:37
SVG Styler: Parsing SVG, element count: 1758 svgStyler.ts:23:13
SVG Styler: Design options: 
Object { size: 256, margin: 10, error_correction: "M", foreground_color: "#030000", background_color: "#210303", pattern: "dots", frame_style: "modern", frame_color: "#000000", frame_thickness: 4 }
svgStyler.ts:24:13
SVG Styler: Applied background color: #210303 svgStyler.ts:46:17
SVG Styler: Found 1763 QR modules to style with foreground color svgStyler.ts:55:17
SVG Styler: Module 1 (rect) - changed fill from "#000000" to "#030000" svgStyler.ts:75:29
SVG Styler: Module 2 (rect) - changed fill from "#000000" to "#030000" svgStyler.ts:75:29
SVG Styler: Module 3 (rect) - changed fill from "#000000" to "#030000" svgStyler.ts:75:29
SVG Styler: Module 4 (rect) - changed fill from "#000000" to "#030000" svgStyler.ts:75:29
SVG Styler: Applying pattern styling: dots svgStyler.ts:94:17
Finder pattern detection - backend classes found: 
Object { markerOuters: 3, markerInners: 3, markerDots: 3 }
svgStyler.ts:232:13
Backend finder pattern 1: 
Object { outer: {…}, inner: {…}, dot: {…} }
svgStyler.ts:260:21
Backend finder pattern 2: 
Object { outer: {…}, inner: {…}, dot: {…} }
svgStyler.ts:260:21
Backend finder pattern 3: 
Object { outer: {…}, inner: {…}, dot: {…} }
svgStyler.ts:260:21
Finder pattern detection summary: 
Object { patternsFound: 3, method: "backend-classes" }
svgStyler.ts:343:13
Pattern styling: 
Object { pattern: "dots", totalRects: 1762, finderElements: 9, dataModules: 1753, markerElements: 9 }
svgStyler.ts:369:13
SVG Styler: Running finder pattern detection... svgStyler.ts:100:13
Finder pattern detection - backend classes found: 
Object { markerOuters: 3, markerInners: 3, markerDots: 3 }
svgStyler.ts:232:13
Backend finder pattern 1: 
Object { outer: {…}, inner: {…}, dot: {…} }
svgStyler.ts:260:21
Backend finder pattern 2: 
Object { outer: {…}, inner: {…}, dot: {…} }
svgStyler.ts:260:21
Backend finder pattern 3: 
Object { outer: {…}, inner: {…}, dot: {…} }
svgStyler.ts:260:21
Finder pattern detection summary: 
Object { patternsFound: 3, method: "backend-classes" }
svgStyler.ts:343:13
SVG Styler: Finder patterns detected: 3 svgStyler.ts:102:13
SVG Styler: Final SVG length: 163780 svgStyler.ts:129:13
Styled SVG: <svg baseProfile="full" height="300" version="1.1" width="300" xmlns="http://www.w3.org/2000/svg" xmlns:ev="http://www.w3.org/2001/xml-events" xmlns:xlink="http://www.w3.org/1999/xlink"><rect class="q... QRPreview.tsx:193:41
[API] Response: 200 OK - POST /qr/preview/svg modernClient.ts:27:24
Successfully received SVG QR preview qrService.modern.ts:429:25
QRPreview: Content key updated 
Object { qrType: "email", dataChanged: true, designChanged: true, logoChanged: true, useSvg: true }
QRPreview.tsx:51:21
QRPreview: Content key updated  
Object { qrType: "email", dataChanged: true, designChanged: true, logoChanged: true, useSvg: true }
dashboard:1:596
QRPreview: Generating basic SVG for frontend styling QRPreview.tsx:108:37
QRPreview: Verifying data for email 
Object { 0: "c", 1: "o", 2: "n", 3: "t", 4: "a", 5: "c", 6: "t", 7: "@", 8: "e", 9: "x", … }
QRPreview.tsx:260:17
QRPreview: Verified data 
Object { 0: "c", 1: "o", 2: "n", 3: "t", 4: "a", 5: "c", 6: "t", 7: "@", 8: "e", 9: "x", … }
QRPreview.tsx:364:17
Frontend QR preview SVG request: 
Object { qrType: "email", dataSize: 363, designOptions: (6) […], hasLogo: true, dimensions: 300 }
qrService.modern.ts:376:21
Cleaned content for SVG preview: 
Object { email: "<EMAIL>", subject: "Hello from QRVibe", body: "I scanned your QR code and wanted to reach out.", cc: "", bcc: "", title: "Email QR Code", description: "Scan to view email content" }
qrService.modern.ts:396:21
[API] Request: POST /qr/preview/svg modernClient.ts:27:24
[API] Added auth token to request modernClient.ts:27:24
Frontend QR preview SVG request: 
Object { qrType: "email", dataSize: 401, designOptions: (32) […], hasLogo: true, dimensions: 300 }
qrService.modern.ts:376:21
Cleaned content for SVG preview: 
Object { email: "<EMAIL>", subject: "Hello from QRVibe", body: "I scanned your QR code and wanted to reach out.", cc: "", bcc: "", title: "Email QR Code", description: "Scan to view email content", email_address: "<EMAIL>" }
qrService.modern.ts:396:21
[API] Request: POST /qr/preview/svg modernClient.ts:27:24
[API] Added auth token to request modernClient.ts:27:24
[API] Response: 200 OK - POST /qr/preview/svg modernClient.ts:27:24
Successfully received SVG QR preview qrService.modern.ts:429:25
Raw SVG from backend: <svg baseProfile="full" height="300" version="1.1" width="300" xmlns="http://www.w3.org/2000/svg" xmlns:ev="http://www.w3.org/2001/xml-events" xmlns:xlink="http://www.w3.org/1999/xlink"><defs /><rect class="qr-bg" fill="#FFFFFF" height="300" id="qr-bg" width="300" x="0" y="0" /><rect class="qr-module" fill="#000000" height="4.918032786885246" id="module-10-0" width="4.918032786885246" x="49.18032786885246" y="0.0" /><rect class="qr-module" fill="#000000" height="4.918032786885246" id="module-14-... QRPreview.tsx:127:37
SVG length: 276037 QRPreview.tsx:128:37
SVG contains <svg>: true QRPreview.tsx:129:37
Raw SVG structure: 
Object { rectCount: 1763, pathCount: 0 }
QRPreview.tsx:133:37
SVG analysis: 
Object { hasRect: true, hasPath: false, hasCircle: false, hasBackground: true, hasForeground: true, totalRects: 1763, largeRects: 7, finderPatternsDetected: true, originalDesignOptions: {…}, designOptionsKeys: (21) […] }
QRPreview.tsx:163:37
Large rectangles (potential finder patterns): 
Array(5) [ '<rect class="qr-bg" fill="#FFFFFF" height="300" id="qr-bg" width="300" x="0" y="0" />', '<rect class="qr-marker-outer" fill="#000000" height="34.42622950819672" width="34.42622950819672" x="0.0" y="0.0" />', '<rect class="qr-marker-inner" fill="#fff" height="22.62295081967213" width="22.62295081967213" x="5.901639344262295" y="5.901639344262295" />', '<rect class="qr-marker-outer" fill="#000000" height="34.42622950819672" width="34.42622950819672" x="265.57377049180326" y="0.0" />', '<rect class="qr-marker-inner" fill="#fff" height="22.62295081967213" width="22.62295081967213" x="271.4754098360656" y="5.901639344262295" />' ]
QRPreview.tsx:177:41
Original design options: 
Object { size: 256, margin: 10, pattern: "dots", marker_border_style: "octagon", marker_border_color: "#cc1313", marker_center_style: "grid", marker_center_color: "#e91414", logo_size: 48, logo_position: "center", logo_opacity: 1, … }
QRPreview.tsx:183:37
Filtered design options for basic preview: 
Object { size: 256, margin: 10, error_correction: "M", foreground_color: "#030000", background_color: "#210303", pattern: "dots" }
QRPreview.tsx:184:37
Filtered options count: 6 QRPreview.tsx:185:37
SVG Styler: Parsing SVG, element count: 1758 svgStyler.ts:23:13
SVG Styler: Design options: 
Object { size: 256, margin: 10, error_correction: "M", foreground_color: "#030000", background_color: "#210303", pattern: "dots" }
svgStyler.ts:24:13
SVG Styler: Applied background color: #210303 svgStyler.ts:46:17
SVG Styler: Found 1763 QR modules to style with foreground color svgStyler.ts:55:17
SVG Styler: Module 1 (rect) - changed fill from "#000000" to "#030000" svgStyler.ts:75:29
SVG Styler: Module 2 (rect) - changed fill from "#000000" to "#030000" svgStyler.ts:75:29
SVG Styler: Module 3 (rect) - changed fill from "#000000" to "#030000" svgStyler.ts:75:29
SVG Styler: Module 4 (rect) - changed fill from "#000000" to "#030000" svgStyler.ts:75:29
SVG Styler: Applying pattern styling: dots svgStyler.ts:94:17
Finder pattern detection - backend classes found: 
Object { markerOuters: 3, markerInners: 3, markerDots: 3 }
svgStyler.ts:232:13
Backend finder pattern 1: 
Object { outer: {…}, inner: {…}, dot: {…} }
svgStyler.ts:260:21
Backend finder pattern 2: 
Object { outer: {…}, inner: {…}, dot: {…} }
svgStyler.ts:260:21
Backend finder pattern 3: 
Object { outer: {…}, inner: {…}, dot: {…} }
svgStyler.ts:260:21
Finder pattern detection summary: 
Object { patternsFound: 3, method: "backend-classes" }
svgStyler.ts:343:13
Pattern styling: 
Object { pattern: "dots", totalRects: 1762, finderElements: 9, dataModules: 1753, markerElements: 9 }
svgStyler.ts:369:13
SVG Styler: Running finder pattern detection... svgStyler.ts:100:13
Finder pattern detection - backend classes found: 
Object { markerOuters: 3, markerInners: 3, markerDots: 3 }
svgStyler.ts:232:13
Backend finder pattern 1: 
Object { outer: {…}, inner: {…}, dot: {…} }
svgStyler.ts:260:21
Backend finder pattern 2: 
Object { outer: {…}, inner: {…}, dot: {…} }
svgStyler.ts:260:21
Backend finder pattern 3: 
Object { outer: {…}, inner: {…}, dot: {…} }
svgStyler.ts:260:21
Finder pattern detection summary: 
Object { patternsFound: 3, method: "backend-classes" }
svgStyler.ts:343:13
SVG Styler: Finder patterns detected: 3 svgStyler.ts:102:13
SVG Styler: Final SVG length: 163666 svgStyler.ts:129:13
Styled SVG: <svg baseProfile="full" height="300" version="1.1" width="300" xmlns="http://www.w3.org/2000/svg" xmlns:ev="http://www.w3.org/2001/xml-events" xmlns:xlink="http://www.w3.org/1999/xlink"><rect class="q... QRPreview.tsx:193:41
[API] Response: 200 OK - POST /qr/preview/svg modernClient.ts:27:24
Successfully received SVG QR preview qrService.modern.ts:429:25
QRPreview: Content key updated 
Object { qrType: "email", dataChanged: true, designChanged: true, logoChanged: true, useSvg: true }
QRPreview.tsx:51:21
QRPreview: Content key updated  
Object { qrType: "email", dataChanged: true, designChanged: true, logoChanged: true, useSvg: true }
dashboard:1:596
QRPreview: Generating basic SVG for frontend styling QRPreview.tsx:108:37
QRPreview: Verifying data for email 
Object { 0: "c", 1: "o", 2: "n", 3: "t", 4: "a", 5: "c", 6: "t", 7: "@", 8: "e", 9: "x", … }
QRPreview.tsx:260:17
QRPreview: Verified data 
Object { 0: "c", 1: "o", 2: "n", 3: "t", 4: "a", 5: "c", 6: "t", 7: "@", 8: "e", 9: "x", … }
QRPreview.tsx:364:17
Frontend QR preview SVG request: 
Object { qrType: "email", dataSize: 363, designOptions: (6) […], hasLogo: true, dimensions: 300 }
qrService.modern.ts:376:21
Cleaned content for SVG preview: 
Object { email: "<EMAIL>", subject: "Hello from QRVibe", body: "I scanned your QR code and wanted to reach out.", cc: "", bcc: "", title: "Email QR Code", description: "Scan to view email content" }
qrService.modern.ts:396:21
[API] Request: POST /qr/preview/svg modernClient.ts:27:24
[API] Added auth token to request modernClient.ts:27:24
Frontend QR preview SVG request: 
Object { qrType: "email", dataSize: 401, designOptions: (32) […], hasLogo: true, dimensions: 300 }
qrService.modern.ts:376:21
Cleaned content for SVG preview: 
Object { email: "<EMAIL>", subject: "Hello from QRVibe", body: "I scanned your QR code and wanted to reach out.", cc: "", bcc: "", title: "Email QR Code", description: "Scan to view email content", email_address: "<EMAIL>" }
qrService.modern.ts:396:21
[API] Request: POST /qr/preview/svg modernClient.ts:27:24
[API] Added auth token to request modernClient.ts:27:24
[API] Response: 200 OK - POST /qr/preview/svg modernClient.ts:27:24
Successfully received SVG QR preview qrService.modern.ts:429:25
[API] Response: 200 OK - POST /qr/preview/svg modernClient.ts:27:24
Successfully received SVG QR preview qrService.modern.ts:429:25
Raw SVG from backend: <svg baseProfile="full" height="300" version="1.1" width="300" xmlns="http://www.w3.org/2000/svg" xmlns:ev="http://www.w3.org/2001/xml-events" xmlns:xlink="http://www.w3.org/1999/xlink"><defs /><rect class="qr-bg" fill="#FFFFFF" height="300" id="qr-bg" width="300" x="0" y="0" /><rect class="qr-module" fill="#000000" height="4.918032786885246" id="module-10-0" width="4.918032786885246" x="49.18032786885246" y="0.0" /><rect class="qr-module" fill="#000000" height="4.918032786885246" id="module-14-... QRPreview.tsx:127:37
SVG length: 276037 QRPreview.tsx:128:37
SVG contains <svg>: true QRPreview.tsx:129:37
Raw SVG structure: 
Object { rectCount: 1763, pathCount: 0 }
QRPreview.tsx:133:37
SVG analysis: 
Object { hasRect: true, hasPath: false, hasCircle: false, hasBackground: true, hasForeground: true, totalRects: 1763, largeRects: 7, finderPatternsDetected: true, originalDesignOptions: {…}, designOptionsKeys: (21) […] }
QRPreview.tsx:163:37
Large rectangles (potential finder patterns): 
Array(5) [ '<rect class="qr-bg" fill="#FFFFFF" height="300" id="qr-bg" width="300" x="0" y="0" />', '<rect class="qr-marker-outer" fill="#000000" height="34.42622950819672" width="34.42622950819672" x="0.0" y="0.0" />', '<rect class="qr-marker-inner" fill="#fff" height="22.62295081967213" width="22.62295081967213" x="5.901639344262295" y="5.901639344262295" />', '<rect class="qr-marker-outer" fill="#000000" height="34.42622950819672" width="34.42622950819672" x="265.57377049180326" y="0.0" />', '<rect class="qr-marker-inner" fill="#fff" height="22.62295081967213" width="22.62295081967213" x="271.4754098360656" y="5.901639344262295" />' ]
QRPreview.tsx:177:41
Original design options: 
Object { size: 256, margin: 10, pattern: "dots", marker_border_style: "octagon", marker_border_color: "#cc1313", marker_center_style: "grid", marker_center_color: "#e91414", logo_size: 48, logo_position: "center", logo_opacity: 1, … }
QRPreview.tsx:183:37
Filtered design options for basic preview: 
Object { size: 256, margin: 10, error_correction: "M", foreground_color: "#030000", background_color: "#210303", pattern: "dots", frame_style: "minimal", frame_color: "#000000", frame_thickness: 4 }
QRPreview.tsx:184:37
Filtered options count: 9 QRPreview.tsx:185:37
SVG Styler: Parsing SVG, element count: 1758 svgStyler.ts:23:13
SVG Styler: Design options: 
Object { size: 256, margin: 10, error_correction: "M", foreground_color: "#030000", background_color: "#210303", pattern: "dots", frame_style: "minimal", frame_color: "#000000", frame_thickness: 4 }
svgStyler.ts:24:13
SVG Styler: Applied background color: #210303 svgStyler.ts:46:17
SVG Styler: Found 1763 QR modules to style with foreground color svgStyler.ts:55:17
SVG Styler: Module 1 (rect) - changed fill from "#000000" to "#030000" svgStyler.ts:75:29
SVG Styler: Module 2 (rect) - changed fill from "#000000" to "#030000" svgStyler.ts:75:29
SVG Styler: Module 3 (rect) - changed fill from "#000000" to "#030000" svgStyler.ts:75:29
SVG Styler: Module 4 (rect) - changed fill from "#000000" to "#030000" svgStyler.ts:75:29
SVG Styler: Applying pattern styling: dots svgStyler.ts:94:17
Finder pattern detection - backend classes found: 
Object { markerOuters: 3, markerInners: 3, markerDots: 3 }
svgStyler.ts:232:13
Backend finder pattern 1: 
Object { outer: {…}, inner: {…}, dot: {…} }
svgStyler.ts:260:21
Backend finder pattern 2: 
Object { outer: {…}, inner: {…}, dot: {…} }
svgStyler.ts:260:21
Backend finder pattern 3: 
Object { outer: {…}, inner: {…}, dot: {…} }
svgStyler.ts:260:21
Finder pattern detection summary: 
Object { patternsFound: 3, method: "backend-classes" }
svgStyler.ts:343:13
Pattern styling: 
Object { pattern: "dots", totalRects: 1762, finderElements: 9, dataModules: 1753, markerElements: 9 }
svgStyler.ts:369:13
SVG Styler: Running finder pattern detection... svgStyler.ts:100:13
Finder pattern detection - backend classes found: 
Object { markerOuters: 3, markerInners: 3, markerDots: 3 }
svgStyler.ts:232:13
Backend finder pattern 1: 
Object { outer: {…}, inner: {…}, dot: {…} }
svgStyler.ts:260:21
Backend finder pattern 2: 
Object { outer: {…}, inner: {…}, dot: {…} }
svgStyler.ts:260:21
Backend finder pattern 3: 
Object { outer: {…}, inner: {…}, dot: {…} }
svgStyler.ts:260:21
Finder pattern detection summary: 
Object { patternsFound: 3, method: "backend-classes" }
svgStyler.ts:343:13
SVG Styler: Finder patterns detected: 3 svgStyler.ts:102:13
SVG Styler: Final SVG length: 163780 svgStyler.ts:129:13
Styled SVG: <svg baseProfile="full" height="300" version="1.1" width="300" xmlns="http://www.w3.org/2000/svg" xmlns:ev="http://www.w3.org/2001/xml-events" xmlns:xlink="http://www.w3.org/1999/xlink"><rect class="q... QRPreview.tsx:193:41
[Auth] Adding Bearer token to request: /health layout.tsx:133:49
QRPreview: Content key updated 
Object { qrType: "email", dataChanged: true, designChanged: true, logoChanged: true, useSvg: true }
QRPreview.tsx:51:21
QRPreview: Content key updated  
Object { qrType: "email", dataChanged: true, designChanged: true, logoChanged: true, useSvg: true }
dashboard:1:596
QRPreview: Generating basic SVG for frontend styling QRPreview.tsx:108:37
QRPreview: Verifying data for email 
Object { 0: "c", 1: "o", 2: "n", 3: "t", 4: "a", 5: "c", 6: "t", 7: "@", 8: "e", 9: "x", … }
QRPreview.tsx:260:17
QRPreview: Verified data 
Object { 0: "c", 1: "o", 2: "n", 3: "t", 4: "a", 5: "c", 6: "t", 7: "@", 8: "e", 9: "x", … }
QRPreview.tsx:364:17
Frontend QR preview SVG request: 
Object { qrType: "email", dataSize: 363, designOptions: (6) […], hasLogo: true, dimensions: 300 }
qrService.modern.ts:376:21
Cleaned content for SVG preview: 
Object { email: "<EMAIL>", subject: "Hello from QRVibe", body: "I scanned your QR code and wanted to reach out.", cc: "", bcc: "", title: "Email QR Code", description: "Scan to view email content" }
qrService.modern.ts:396:21
[API] Request: POST /qr/preview/svg modernClient.ts:27:24
[API] Added auth token to request modernClient.ts:27:24
Frontend QR preview SVG request: 
Object { qrType: "email", dataSize: 401, designOptions: (32) […], hasLogo: true, dimensions: 300 }
qrService.modern.ts:376:21
Cleaned content for SVG preview: 
Object { email: "<EMAIL>", subject: "Hello from QRVibe", body: "I scanned your QR code and wanted to reach out.", cc: "", bcc: "", title: "Email QR Code", description: "Scan to view email content", email_address: "<EMAIL>" }
qrService.modern.ts:396:21
[API] Request: POST /qr/preview/svg modernClient.ts:27:24
[API] Added auth token to request modernClient.ts:27:24
[API] Response: 200 OK - POST /qr/preview/svg modernClient.ts:27:24
Successfully received SVG QR preview qrService.modern.ts:429:25
Raw SVG from backend: <svg baseProfile="full" height="300" version="1.1" width="300" xmlns="http://www.w3.org/2000/svg" xmlns:ev="http://www.w3.org/2001/xml-events" xmlns:xlink="http://www.w3.org/1999/xlink"><defs /><rect class="qr-bg" fill="#FFFFFF" height="300" id="qr-bg" width="300" x="0" y="0" /><rect class="qr-module" fill="#000000" height="4.918032786885246" id="module-10-0" width="4.918032786885246" x="49.18032786885246" y="0.0" /><rect class="qr-module" fill="#000000" height="4.918032786885246" id="module-14-... QRPreview.tsx:127:37
SVG length: 276037 QRPreview.tsx:128:37
SVG contains <svg>: true QRPreview.tsx:129:37
Raw SVG structure: 
Object { rectCount: 1763, pathCount: 0 }
QRPreview.tsx:133:37
SVG analysis: 
Object { hasRect: true, hasPath: false, hasCircle: false, hasBackground: true, hasForeground: true, totalRects: 1763, largeRects: 7, finderPatternsDetected: true, originalDesignOptions: {…}, designOptionsKeys: (21) […] }
QRPreview.tsx:163:37
Large rectangles (potential finder patterns): 
Array(5) [ '<rect class="qr-bg" fill="#FFFFFF" height="300" id="qr-bg" width="300" x="0" y="0" />', '<rect class="qr-marker-outer" fill="#000000" height="34.42622950819672" width="34.42622950819672" x="0.0" y="0.0" />', '<rect class="qr-marker-inner" fill="#fff" height="22.62295081967213" width="22.62295081967213" x="5.901639344262295" y="5.901639344262295" />', '<rect class="qr-marker-outer" fill="#000000" height="34.42622950819672" width="34.42622950819672" x="265.57377049180326" y="0.0" />', '<rect class="qr-marker-inner" fill="#fff" height="22.62295081967213" width="22.62295081967213" x="271.4754098360656" y="5.901639344262295" />' ]
QRPreview.tsx:177:41
Original design options: 
Object { size: 256, margin: 10, pattern: "dots", marker_border_style: "octagon", marker_border_color: "#cc1313", marker_center_style: "grid", marker_center_color: "#e91414", logo_size: 48, logo_position: "center", logo_opacity: 1, … }
QRPreview.tsx:183:37
Filtered design options for basic preview: 
Object { size: 256, margin: 10, error_correction: "M", foreground_color: "#030000", background_color: "#210303", pattern: "dots", frame_style: "bold", frame_color: "#000000", frame_thickness: 4 }
QRPreview.tsx:184:37
Filtered options count: 9 QRPreview.tsx:185:37
SVG Styler: Parsing SVG, element count: 1758 svgStyler.ts:23:13
SVG Styler: Design options: 
Object { size: 256, margin: 10, error_correction: "M", foreground_color: "#030000", background_color: "#210303", pattern: "dots", frame_style: "bold", frame_color: "#000000", frame_thickness: 4 }
svgStyler.ts:24:13
SVG Styler: Applied background color: #210303 svgStyler.ts:46:17
SVG Styler: Found 1763 QR modules to style with foreground color svgStyler.ts:55:17
SVG Styler: Module 1 (rect) - changed fill from "#000000" to "#030000" svgStyler.ts:75:29
SVG Styler: Module 2 (rect) - changed fill from "#000000" to "#030000" svgStyler.ts:75:29
SVG Styler: Module 3 (rect) - changed fill from "#000000" to "#030000" svgStyler.ts:75:29
SVG Styler: Module 4 (rect) - changed fill from "#000000" to "#030000" svgStyler.ts:75:29
SVG Styler: Applying pattern styling: dots svgStyler.ts:94:17
Finder pattern detection - backend classes found: 
Object { markerOuters: 3, markerInners: 3, markerDots: 3 }
svgStyler.ts:232:13
Backend finder pattern 1: 
Object { outer: {…}, inner: {…}, dot: {…} }
svgStyler.ts:260:21
Backend finder pattern 2: 
Object { outer: {…}, inner: {…}, dot: {…} }
svgStyler.ts:260:21
Backend finder pattern 3: 
Object { outer: {…}, inner: {…}, dot: {…} }
svgStyler.ts:260:21
Finder pattern detection summary: 
Object { patternsFound: 3, method: "backend-classes" }
svgStyler.ts:343:13
Pattern styling: 
Object { pattern: "dots", totalRects: 1762, finderElements: 9, dataModules: 1753, markerElements: 9 }
svgStyler.ts:369:13
SVG Styler: Running finder pattern detection... svgStyler.ts:100:13
Finder pattern detection - backend classes found: 
Object { markerOuters: 3, markerInners: 3, markerDots: 3 }
svgStyler.ts:232:13
Backend finder pattern 1: 
Object { outer: {…}, inner: {…}, dot: {…} }
svgStyler.ts:260:21
Backend finder pattern 2: 
Object { outer: {…}, inner: {…}, dot: {…} }
svgStyler.ts:260:21
Backend finder pattern 3: 
Object { outer: {…}, inner: {…}, dot: {…} }
svgStyler.ts:260:21
Finder pattern detection summary: 
Object { patternsFound: 3, method: "backend-classes" }
svgStyler.ts:343:13
SVG Styler: Finder patterns detected: 3 svgStyler.ts:102:13
SVG Styler: Final SVG length: 163780 svgStyler.ts:129:13
Styled SVG: <svg baseProfile="full" height="300" version="1.1" width="300" xmlns="http://www.w3.org/2000/svg" xmlns:ev="http://www.w3.org/2001/xml-events" xmlns:xlink="http://www.w3.org/1999/xlink"><rect class="q... QRPreview.tsx:193:41
[API] Response: 200 OK - POST /qr/preview/svg modernClient.ts:27:24
Successfully received SVG QR preview qrService.modern.ts:429:25
QRPreview: Content key updated 
Object { qrType: "email", dataChanged: true, designChanged: true, logoChanged: true, useSvg: true }
QRPreview.tsx:51:21
QRPreview: Content key updated  
Object { qrType: "email", dataChanged: true, designChanged: true, logoChanged: true, useSvg: true }
dashboard:1:596
QRPreview: Generating basic SVG for frontend styling QRPreview.tsx:108:37
QRPreview: Verifying data for email 
Object { 0: "c", 1: "o", 2: "n", 3: "t", 4: "a", 5: "c", 6: "t", 7: "@", 8: "e", 9: "x", … }
QRPreview.tsx:260:17
QRPreview: Verified data 
Object { 0: "c", 1: "o", 2: "n", 3: "t", 4: "a", 5: "c", 6: "t", 7: "@", 8: "e", 9: "x", … }
QRPreview.tsx:364:17
Frontend QR preview SVG request: 
Object { qrType: "email", dataSize: 363, designOptions: (6) […], hasLogo: true, dimensions: 300 }
qrService.modern.ts:376:21
Cleaned content for SVG preview: 
Object { email: "<EMAIL>", subject: "Hello from QRVibe", body: "I scanned your QR code and wanted to reach out.", cc: "", bcc: "", title: "Email QR Code", description: "Scan to view email content" }
qrService.modern.ts:396:21
[API] Request: POST /qr/preview/svg modernClient.ts:27:24
[API] Added auth token to request modernClient.ts:27:24
Frontend QR preview SVG request: 
Object { qrType: "email", dataSize: 401, designOptions: (32) […], hasLogo: true, dimensions: 300 }
qrService.modern.ts:376:21
Cleaned content for SVG preview: 
Object { email: "<EMAIL>", subject: "Hello from QRVibe", body: "I scanned your QR code and wanted to reach out.", cc: "", bcc: "", title: "Email QR Code", description: "Scan to view email content", email_address: "<EMAIL>" }
qrService.modern.ts:396:21
[API] Request: POST /qr/preview/svg modernClient.ts:27:24
[API] Added auth token to request modernClient.ts:27:24
[API] Response: 200 OK - POST /qr/preview/svg modernClient.ts:27:24
Successfully received SVG QR preview qrService.modern.ts:429:25
[API] Response: 200 OK - POST /qr/preview/svg modernClient.ts:27:24
Successfully received SVG QR preview qrService.modern.ts:429:25
Raw SVG from backend: <svg baseProfile="full" height="300" version="1.1" width="300" xmlns="http://www.w3.org/2000/svg" xmlns:ev="http://www.w3.org/2001/xml-events" xmlns:xlink="http://www.w3.org/1999/xlink"><defs /><rect class="qr-bg" fill="#FFFFFF" height="300" id="qr-bg" width="300" x="0" y="0" /><rect class="qr-module" fill="#000000" height="4.918032786885246" id="module-10-0" width="4.918032786885246" x="49.18032786885246" y="0.0" /><rect class="qr-module" fill="#000000" height="4.918032786885246" id="module-14-... QRPreview.tsx:127:37
SVG length: 276037 QRPreview.tsx:128:37
SVG contains <svg>: true QRPreview.tsx:129:37
Raw SVG structure: 
Object { rectCount: 1763, pathCount: 0 }
QRPreview.tsx:133:37
SVG analysis: 
Object { hasRect: true, hasPath: false, hasCircle: false, hasBackground: true, hasForeground: true, totalRects: 1763, largeRects: 7, finderPatternsDetected: true, originalDesignOptions: {…}, designOptionsKeys: (21) […] }
QRPreview.tsx:163:37
Large rectangles (potential finder patterns): 
Array(5) [ '<rect class="qr-bg" fill="#FFFFFF" height="300" id="qr-bg" width="300" x="0" y="0" />', '<rect class="qr-marker-outer" fill="#000000" height="34.42622950819672" width="34.42622950819672" x="0.0" y="0.0" />', '<rect class="qr-marker-inner" fill="#fff" height="22.62295081967213" width="22.62295081967213" x="5.901639344262295" y="5.901639344262295" />', '<rect class="qr-marker-outer" fill="#000000" height="34.42622950819672" width="34.42622950819672" x="265.57377049180326" y="0.0" />', '<rect class="qr-marker-inner" fill="#fff" height="22.62295081967213" width="22.62295081967213" x="271.4754098360656" y="5.901639344262295" />' ]
QRPreview.tsx:177:41
Original design options: 
Object { size: 256, margin: 10, pattern: "dots", marker_border_style: "octagon", marker_border_color: "#cc1313", marker_center_style: "grid", marker_center_color: "#e91414", logo_size: 48, logo_position: "center", logo_opacity: 1, … }
QRPreview.tsx:183:37
Filtered design options for basic preview: 
Object { size: 256, margin: 10, error_correction: "M", foreground_color: "#030000", background_color: "#210303", pattern: "dots", frame_style: "classic", frame_color: "#000000", frame_thickness: 4 }
QRPreview.tsx:184:37
Filtered options count: 9 QRPreview.tsx:185:37
SVG Styler: Parsing SVG, element count: 1758 svgStyler.ts:23:13
SVG Styler: Design options: 
Object { size: 256, margin: 10, error_correction: "M", foreground_color: "#030000", background_color: "#210303", pattern: "dots", frame_style: "classic", frame_color: "#000000", frame_thickness: 4 }
svgStyler.ts:24:13
SVG Styler: Applied background color: #210303 svgStyler.ts:46:17
SVG Styler: Found 1763 QR modules to style with foreground color svgStyler.ts:55:17
SVG Styler: Module 1 (rect) - changed fill from "#000000" to "#030000" svgStyler.ts:75:29
SVG Styler: Module 2 (rect) - changed fill from "#000000" to "#030000" svgStyler.ts:75:29
SVG Styler: Module 3 (rect) - changed fill from "#000000" to "#030000" svgStyler.ts:75:29
SVG Styler: Module 4 (rect) - changed fill from "#000000" to "#030000" svgStyler.ts:75:29
SVG Styler: Applying pattern styling: dots svgStyler.ts:94:17
Finder pattern detection - backend classes found: 
Object { markerOuters: 3, markerInners: 3, markerDots: 3 }
svgStyler.ts:232:13
Backend finder pattern 1: 
Object { outer: {…}, inner: {…}, dot: {…} }
svgStyler.ts:260:21
Backend finder pattern 2: 
Object { outer: {…}, inner: {…}, dot: {…} }
svgStyler.ts:260:21
Backend finder pattern 3: 
Object { outer: {…}, inner: {…}, dot: {…} }
svgStyler.ts:260:21
Finder pattern detection summary: 
Object { patternsFound: 3, method: "backend-classes" }
svgStyler.ts:343:13
Pattern styling: 
Object { pattern: "dots", totalRects: 1762, finderElements: 9, dataModules: 1753, markerElements: 9 }
svgStyler.ts:369:13
SVG Styler: Running finder pattern detection... svgStyler.ts:100:13
Finder pattern detection - backend classes found: 
Object { markerOuters: 3, markerInners: 3, markerDots: 3 }
svgStyler.ts:232:13
Backend finder pattern 1: 
Object { outer: {…}, inner: {…}, dot: {…} }
svgStyler.ts:260:21
Backend finder pattern 2: 
Object { outer: {…}, inner: {…}, dot: {…} }
svgStyler.ts:260:21
Backend finder pattern 3: 
Object { outer: {…}, inner: {…}, dot: {…} }
svgStyler.ts:260:21
Finder pattern detection summary: 
Object { patternsFound: 3, method: "backend-classes" }
svgStyler.ts:343:13
SVG Styler: Finder patterns detected: 3 svgStyler.ts:102:13
SVG Styler: Final SVG length: 163780 svgStyler.ts:129:13
Styled SVG: <svg baseProfile="full" height="300" version="1.1" width="300" xmlns="http://www.w3.org/2000/svg" xmlns:ev="http://www.w3.org/2001/xml-events" xmlns:xlink="http://www.w3.org/1999/xlink"><rect class="q... QRPreview.tsx:193:41
QRPreview: Content key updated 
Object { qrType: "email", dataChanged: true, designChanged: true, logoChanged: true, useSvg: true }
QRPreview.tsx:51:21
QRPreview: Content key updated  
Object { qrType: "email", dataChanged: true, designChanged: true, logoChanged: true, useSvg: true }
dashboard:1:596
QRPreview: Generating basic SVG for frontend styling QRPreview.tsx:108:37
QRPreview: Verifying data for email 
Object { 0: "c", 1: "o", 2: "n", 3: "t", 4: "a", 5: "c", 6: "t", 7: "@", 8: "e", 9: "x", … }
QRPreview.tsx:260:17
QRPreview: Verified data 
Object { 0: "c", 1: "o", 2: "n", 3: "t", 4: "a", 5: "c", 6: "t", 7: "@", 8: "e", 9: "x", … }
QRPreview.tsx:364:17
Frontend QR preview SVG request: 
Object { qrType: "email", dataSize: 363, designOptions: (6) […], hasLogo: true, dimensions: 300 }
qrService.modern.ts:376:21
Cleaned content for SVG preview: 
Object { email: "<EMAIL>", subject: "Hello from QRVibe", body: "I scanned your QR code and wanted to reach out.", cc: "", bcc: "", title: "Email QR Code", description: "Scan to view email content" }
qrService.modern.ts:396:21
[API] Request: POST /qr/preview/svg modernClient.ts:27:24
[API] Added auth token to request modernClient.ts:27:24
Frontend QR preview SVG request: 
Object { qrType: "email", dataSize: 401, designOptions: (32) […], hasLogo: true, dimensions: 300 }
qrService.modern.ts:376:21
Cleaned content for SVG preview: 
Object { email: "<EMAIL>", subject: "Hello from QRVibe", body: "I scanned your QR code and wanted to reach out.", cc: "", bcc: "", title: "Email QR Code", description: "Scan to view email content", email_address: "<EMAIL>" }
qrService.modern.ts:396:21
[API] Request: POST /qr/preview/svg modernClient.ts:27:24
[API] Added auth token to request modernClient.ts:27:24
[API] Response: 200 OK - POST /qr/preview/svg modernClient.ts:27:24
Successfully received SVG QR preview qrService.modern.ts:429:25
Raw SVG from backend: <svg baseProfile="full" height="300" version="1.1" width="300" xmlns="http://www.w3.org/2000/svg" xmlns:ev="http://www.w3.org/2001/xml-events" xmlns:xlink="http://www.w3.org/1999/xlink"><defs /><rect class="qr-bg" fill="#FFFFFF" height="300" id="qr-bg" width="300" x="0" y="0" /><rect class="qr-module" fill="#000000" height="4.918032786885246" id="module-10-0" width="4.918032786885246" x="49.18032786885246" y="0.0" /><rect class="qr-module" fill="#000000" height="4.918032786885246" id="module-14-... QRPreview.tsx:127:37
SVG length: 276037 QRPreview.tsx:128:37
SVG contains <svg>: true QRPreview.tsx:129:37
Raw SVG structure: 
Object { rectCount: 1763, pathCount: 0 }
QRPreview.tsx:133:37
SVG analysis: 
Object { hasRect: true, hasPath: false, hasCircle: false, hasBackground: true, hasForeground: true, totalRects: 1763, largeRects: 7, finderPatternsDetected: true, originalDesignOptions: {…}, designOptionsKeys: (21) […] }
QRPreview.tsx:163:37
Large rectangles (potential finder patterns): 
Array(5) [ '<rect class="qr-bg" fill="#FFFFFF" height="300" id="qr-bg" width="300" x="0" y="0" />', '<rect class="qr-marker-outer" fill="#000000" height="34.42622950819672" width="34.42622950819672" x="0.0" y="0.0" />', '<rect class="qr-marker-inner" fill="#fff" height="22.62295081967213" width="22.62295081967213" x="5.901639344262295" y="5.901639344262295" />', '<rect class="qr-marker-outer" fill="#000000" height="34.42622950819672" width="34.42622950819672" x="265.57377049180326" y="0.0" />', '<rect class="qr-marker-inner" fill="#fff" height="22.62295081967213" width="22.62295081967213" x="271.4754098360656" y="5.901639344262295" />' ]
QRPreview.tsx:177:41
Original design options: 
Object { size: 256, margin: 10, pattern: "dots", marker_border_style: "octagon", marker_border_color: "#cc1313", marker_center_style: "grid", marker_center_color: "#e91414", logo_size: 48, logo_position: "center", logo_opacity: 1, … }
QRPreview.tsx:183:37
Filtered design options for basic preview: 
Object { size: 256, margin: 10, error_correction: "M", foreground_color: "#030000", background_color: "#210303", pattern: "dots" }
QRPreview.tsx:184:37
Filtered options count: 6 QRPreview.tsx:185:37
SVG Styler: Parsing SVG, element count: 1758 svgStyler.ts:23:13
SVG Styler: Design options: 
Object { size: 256, margin: 10, error_correction: "M", foreground_color: "#030000", background_color: "#210303", pattern: "dots" }
svgStyler.ts:24:13
SVG Styler: Applied background color: #210303 svgStyler.ts:46:17
SVG Styler: Found 1763 QR modules to style with foreground color svgStyler.ts:55:17
SVG Styler: Module 1 (rect) - changed fill from "#000000" to "#030000" svgStyler.ts:75:29
SVG Styler: Module 2 (rect) - changed fill from "#000000" to "#030000" svgStyler.ts:75:29
SVG Styler: Module 3 (rect) - changed fill from "#000000" to "#030000" svgStyler.ts:75:29
SVG Styler: Module 4 (rect) - changed fill from "#000000" to "#030000" svgStyler.ts:75:29
SVG Styler: Applying pattern styling: dots svgStyler.ts:94:17
Finder pattern detection - backend classes found: 
Object { markerOuters: 3, markerInners: 3, markerDots: 3 }
svgStyler.ts:232:13
Backend finder pattern 1: 
Object { outer: {…}, inner: {…}, dot: {…} }
svgStyler.ts:260:21
Backend finder pattern 2: 
Object { outer: {…}, inner: {…}, dot: {…} }
svgStyler.ts:260:21
Backend finder pattern 3: 
Object { outer: {…}, inner: {…}, dot: {…} }
svgStyler.ts:260:21
Finder pattern detection summary: 
Object { patternsFound: 3, method: "backend-classes" }
svgStyler.ts:343:13
Pattern styling: 
Object { pattern: "dots", totalRects: 1762, finderElements: 9, dataModules: 1753, markerElements: 9 }
svgStyler.ts:369:13
SVG Styler: Running finder pattern detection... svgStyler.ts:100:13
Finder pattern detection - backend classes found: 
Object { markerOuters: 3, markerInners: 3, markerDots: 3 }
svgStyler.ts:232:13
Backend finder pattern 1: 
Object { outer: {…}, inner: {…}, dot: {…} }
svgStyler.ts:260:21
Backend finder pattern 2: 
Object { outer: {…}, inner: {…}, dot: {…} }
svgStyler.ts:260:21
Backend finder pattern 3: 
Object { outer: {…}, inner: {…}, dot: {…} }
svgStyler.ts:260:21
Finder pattern detection summary: 
Object { patternsFound: 3, method: "backend-classes" }
svgStyler.ts:343:13
SVG Styler: Finder patterns detected: 3 svgStyler.ts:102:13
SVG Styler: Final SVG length: 163666 svgStyler.ts:129:13
Styled SVG: <svg baseProfile="full" height="300" version="1.1" width="300" xmlns="http://www.w3.org/2000/svg" xmlns:ev="http://www.w3.org/2001/xml-events" xmlns:xlink="http://www.w3.org/1999/xlink"><rect class="q... QRPreview.tsx:193:41
[API] Response: 200 OK - POST /qr/preview/svg modernClient.ts:27:24
Successfully received SVG QR preview qrService.modern.ts:429:25
QRPreview: Content key updated 
Object { qrType: "email", dataChanged: true, designChanged: true, logoChanged: true, useSvg: true }
QRPreview.tsx:51:21
QRPreview: Content key updated  
Object { qrType: "email", dataChanged: true, designChanged: true, logoChanged: true, useSvg: true }
dashboard:1:596
QRPreview: Content key updated 
Object { qrType: "email", dataChanged: true, designChanged: true, logoChanged: true, useSvg: true }
QRPreview.tsx:51:21
QRPreview: Content key updated  
Object { qrType: "email", dataChanged: true, designChanged: true, logoChanged: true, useSvg: true }
dashboard:1:596
QRPreview: Content key updated 
Object { qrType: "email", dataChanged: true, designChanged: true, logoChanged: true, useSvg: true }
QRPreview.tsx:51:21
QRPreview: Content key updated  
Object { qrType: "email", dataChanged: true, designChanged: true, logoChanged: true, useSvg: true }
dashboard:1:596
QRPreview: Content key updated 
Object { qrType: "email", dataChanged: true, designChanged: true, logoChanged: true, useSvg: true }
QRPreview.tsx:51:21
QRPreview: Content key updated  
Object { qrType: "email", dataChanged: true, designChanged: true, logoChanged: true, useSvg: true }
dashboard:1:596
QRPreview: Content key updated 
Object { qrType: "email", dataChanged: true, designChanged: true, logoChanged: true, useSvg: true }
QRPreview.tsx:51:21
QRPreview: Content key updated  
Object { qrType: "email", dataChanged: true, designChanged: true, logoChanged: true, useSvg: true }
dashboard:1:596
QRPreview: Generating basic SVG for frontend styling QRPreview.tsx:108:37
QRPreview: Verifying data for email 
Object { 0: "c", 1: "o", 2: "n", 3: "t", 4: "a", 5: "c", 6: "t", 7: "@", 8: "e", 9: "x", … }
QRPreview.tsx:260:17
QRPreview: Verified data 
Object { 0: "c", 1: "o", 2: "n", 3: "t", 4: "a", 5: "c", 6: "t", 7: "@", 8: "e", 9: "x", … }
QRPreview.tsx:364:17
Frontend QR preview SVG request: 
Object { qrType: "email", dataSize: 363, designOptions: (6) […], hasLogo: true, dimensions: 300 }
qrService.modern.ts:376:21
Cleaned content for SVG preview: 
Object { email: "<EMAIL>", subject: "Hello from QRVibe", body: "I scanned your QR code and wanted to reach out.", cc: "", bcc: "", title: "Email QR Code", description: "Scan to view email content" }
qrService.modern.ts:396:21
[API] Request: POST /qr/preview/svg modernClient.ts:27:24
[API] Added auth token to request modernClient.ts:27:24
Frontend QR preview SVG request: 
Object { qrType: "email", dataSize: 401, designOptions: (32) […], hasLogo: true, dimensions: 300 }
qrService.modern.ts:376:21
Cleaned content for SVG preview: 
Object { email: "<EMAIL>", subject: "Hello from QRVibe", body: "I scanned your QR code and wanted to reach out.", cc: "", bcc: "", title: "Email QR Code", description: "Scan to view email content", email_address: "<EMAIL>" }
qrService.modern.ts:396:21
[API] Request: POST /qr/preview/svg modernClient.ts:27:24
[API] Added auth token to request modernClient.ts:27:24
[API] Response: 200 OK - POST /qr/preview/svg modernClient.ts:27:24
Successfully received SVG QR preview qrService.modern.ts:429:25
Raw SVG from backend: <svg baseProfile="full" height="300" version="1.1" width="300" xmlns="http://www.w3.org/2000/svg" xmlns:ev="http://www.w3.org/2001/xml-events" xmlns:xlink="http://www.w3.org/1999/xlink"><defs /><rect class="qr-bg" fill="#FFFFFF" height="300" id="qr-bg" width="300" x="0" y="0" /><rect class="qr-module" fill="#000000" height="4.918032786885246" id="module-10-0" width="4.918032786885246" x="49.18032786885246" y="0.0" /><rect class="qr-module" fill="#000000" height="4.918032786885246" id="module-14-... QRPreview.tsx:127:37
SVG length: 276037 QRPreview.tsx:128:37
SVG contains <svg>: true QRPreview.tsx:129:37
Raw SVG structure: 
Object { rectCount: 1763, pathCount: 0 }
QRPreview.tsx:133:37
SVG analysis: 
Object { hasRect: true, hasPath: false, hasCircle: false, hasBackground: true, hasForeground: true, totalRects: 1763, largeRects: 7, finderPatternsDetected: true, originalDesignOptions: {…}, designOptionsKeys: (21) […] }
QRPreview.tsx:163:37
Large rectangles (potential finder patterns): 
Array(5) [ '<rect class="qr-bg" fill="#FFFFFF" height="300" id="qr-bg" width="300" x="0" y="0" />', '<rect class="qr-marker-outer" fill="#000000" height="34.42622950819672" width="34.42622950819672" x="0.0" y="0.0" />', '<rect class="qr-marker-inner" fill="#fff" height="22.62295081967213" width="22.62295081967213" x="5.901639344262295" y="5.901639344262295" />', '<rect class="qr-marker-outer" fill="#000000" height="34.42622950819672" width="34.42622950819672" x="265.57377049180326" y="0.0" />', '<rect class="qr-marker-inner" fill="#fff" height="22.62295081967213" width="22.62295081967213" x="271.4754098360656" y="5.901639344262295" />' ]
QRPreview.tsx:177:41
Original design options: 
Object { size: 256, margin: 10, pattern: "dots", marker_border_style: "octagon", marker_border_color: "#cc1313", marker_center_style: "grid", marker_center_color: "#e91414", logo_size: 48, logo_position: "center", logo_opacity: 1, … }
QRPreview.tsx:183:37
Filtered design options for basic preview: 
Object { size: 256, margin: 10, error_correction: "M", foreground_color: "#030000", background_color: "#210303", pattern: "dots" }
QRPreview.tsx:184:37
Filtered options count: 6 QRPreview.tsx:185:37
SVG Styler: Parsing SVG, element count: 1758 svgStyler.ts:23:13
SVG Styler: Design options: 
Object { size: 256, margin: 10, error_correction: "M", foreground_color: "#030000", background_color: "#210303", pattern: "dots" }
svgStyler.ts:24:13
SVG Styler: Applied background color: #210303 svgStyler.ts:46:17
SVG Styler: Found 1763 QR modules to style with foreground color svgStyler.ts:55:17
SVG Styler: Module 1 (rect) - changed fill from "#000000" to "#030000" svgStyler.ts:75:29
SVG Styler: Module 2 (rect) - changed fill from "#000000" to "#030000" svgStyler.ts:75:29
SVG Styler: Module 3 (rect) - changed fill from "#000000" to "#030000" svgStyler.ts:75:29
SVG Styler: Module 4 (rect) - changed fill from "#000000" to "#030000" svgStyler.ts:75:29
SVG Styler: Applying pattern styling: dots svgStyler.ts:94:17
Finder pattern detection - backend classes found: 
Object { markerOuters: 3, markerInners: 3, markerDots: 3 }
svgStyler.ts:232:13
Backend finder pattern 1: 
Object { outer: {…}, inner: {…}, dot: {…} }
svgStyler.ts:260:21
Backend finder pattern 2: 
Object { outer: {…}, inner: {…}, dot: {…} }
svgStyler.ts:260:21
Backend finder pattern 3: 
Object { outer: {…}, inner: {…}, dot: {…} }
svgStyler.ts:260:21
Finder pattern detection summary: 
Object { patternsFound: 3, method: "backend-classes" }
svgStyler.ts:343:13
Pattern styling: 
Object { pattern: "dots", totalRects: 1762, finderElements: 9, dataModules: 1753, markerElements: 9 }
svgStyler.ts:369:13
SVG Styler: Running finder pattern detection... svgStyler.ts:100:13
Finder pattern detection - backend classes found: 
Object { markerOuters: 3, markerInners: 3, markerDots: 3 }
svgStyler.ts:232:13
Backend finder pattern 1: 
Object { outer: {…}, inner: {…}, dot: {…} }
svgStyler.ts:260:21
Backend finder pattern 2: 
Object { outer: {…}, inner: {…}, dot: {…} }
svgStyler.ts:260:21
Backend finder pattern 3: 
Object { outer: {…}, inner: {…}, dot: {…} }
svgStyler.ts:260:21
Finder pattern detection summary: 
Object { patternsFound: 3, method: "backend-classes" }
svgStyler.ts:343:13
SVG Styler: Finder patterns detected: 3 svgStyler.ts:102:13
SVG Styler: Final SVG length: 163666 svgStyler.ts:129:13
Styled SVG: <svg baseProfile="full" height="300" version="1.1" width="300" xmlns="http://www.w3.org/2000/svg" xmlns:ev="http://www.w3.org/2001/xml-events" xmlns:xlink="http://www.w3.org/1999/xlink"><rect class="q... QRPreview.tsx:193:41
[API] Response: 200 OK - POST /qr/preview/svg modernClient.ts:27:24
Successfully received SVG QR preview qrService.modern.ts:429:25
QRPreview: Content key updated 
Object { qrType: "email", dataChanged: true, designChanged: true, logoChanged: true, useSvg: true }
QRPreview.tsx:51:21
QRPreview: Content key updated  
Object { qrType: "email", dataChanged: true, designChanged: true, logoChanged: true, useSvg: true }
dashboard:1:596
QRPreview: Generating basic SVG for frontend styling QRPreview.tsx:108:37
QRPreview: Verifying data for email 
Object { 0: "c", 1: "o", 2: "n", 3: "t", 4: "a", 5: "c", 6: "t", 7: "@", 8: "e", 9: "x", … }
QRPreview.tsx:260:17
QRPreview: Verified data 
Object { 0: "c", 1: "o", 2: "n", 3: "t", 4: "a", 5: "c", 6: "t", 7: "@", 8: "e", 9: "x", … }
QRPreview.tsx:364:17
Frontend QR preview SVG request: 
Object { qrType: "email", dataSize: 363, designOptions: (6) […], hasLogo: true, dimensions: 300 }
qrService.modern.ts:376:21
Cleaned content for SVG preview: 
Object { email: "<EMAIL>", subject: "Hello from QRVibe", body: "I scanned your QR code and wanted to reach out.", cc: "", bcc: "", title: "Email QR Code", description: "Scan to view email content" }
qrService.modern.ts:396:21
[API] Request: POST /qr/preview/svg modernClient.ts:27:24
[API] Added auth token to request modernClient.ts:27:24
Frontend QR preview SVG request: 
Object { qrType: "email", dataSize: 401, designOptions: (32) […], hasLogo: true, dimensions: 300 }
qrService.modern.ts:376:21
Cleaned content for SVG preview: 
Object { email: "<EMAIL>", subject: "Hello from QRVibe", body: "I scanned your QR code and wanted to reach out.", cc: "", bcc: "", title: "Email QR Code", description: "Scan to view email content", email_address: "<EMAIL>" }
qrService.modern.ts:396:21
[API] Request: POST /qr/preview/svg modernClient.ts:27:24
[API] Added auth token to request modernClient.ts:27:24
[API] Response: 200 OK - POST /qr/preview/svg modernClient.ts:27:24
Successfully received SVG QR preview qrService.modern.ts:429:25
[API] Response: 200 OK - POST /qr/preview/svg modernClient.ts:27:24
Successfully received SVG QR preview qrService.modern.ts:429:25
Raw SVG from backend: <svg baseProfile="full" height="300" version="1.1" width="300" xmlns="http://www.w3.org/2000/svg" xmlns:ev="http://www.w3.org/2001/xml-events" xmlns:xlink="http://www.w3.org/1999/xlink"><defs /><rect class="qr-bg" fill="#FFFFFF" height="300" id="qr-bg" width="300" x="0" y="0" /><rect class="qr-module" fill="#000000" height="4.918032786885246" id="module-10-0" width="4.918032786885246" x="49.18032786885246" y="0.0" /><rect class="qr-module" fill="#000000" height="4.918032786885246" id="module-14-... QRPreview.tsx:127:37
SVG length: 276037 QRPreview.tsx:128:37
SVG contains <svg>: true QRPreview.tsx:129:37
Raw SVG structure: 
Object { rectCount: 1763, pathCount: 0 }
QRPreview.tsx:133:37
SVG analysis: 
Object { hasRect: true, hasPath: false, hasCircle: false, hasBackground: true, hasForeground: true, totalRects: 1763, largeRects: 7, finderPatternsDetected: true, originalDesignOptions: {…}, designOptionsKeys: (21) […] }
QRPreview.tsx:163:37
Large rectangles (potential finder patterns): 
Array(5) [ '<rect class="qr-bg" fill="#FFFFFF" height="300" id="qr-bg" width="300" x="0" y="0" />', '<rect class="qr-marker-outer" fill="#000000" height="34.42622950819672" width="34.42622950819672" x="0.0" y="0.0" />', '<rect class="qr-marker-inner" fill="#fff" height="22.62295081967213" width="22.62295081967213" x="5.901639344262295" y="5.901639344262295" />', '<rect class="qr-marker-outer" fill="#000000" height="34.42622950819672" width="34.42622950819672" x="265.57377049180326" y="0.0" />', '<rect class="qr-marker-inner" fill="#fff" height="22.62295081967213" width="22.62295081967213" x="271.4754098360656" y="5.901639344262295" />' ]
QRPreview.tsx:177:41
Original design options: 
Object { size: 256, margin: 10, pattern: "square", marker_border_style: "octagon", marker_border_color: "#cc1313", marker_center_style: "grid", marker_center_color: "#e91414", logo_size: 48, logo_position: "center", logo_opacity: 1, … }
QRPreview.tsx:183:37
Filtered design options for basic preview: 
Object { size: 256, margin: 10, error_correction: "M", foreground_color: "#030000", background_color: "#210303" }
QRPreview.tsx:184:37
Filtered options count: 5 QRPreview.tsx:185:37
SVG Styler: Parsing SVG, element count: 1758 svgStyler.ts:23:13
SVG Styler: Design options: 
Object { size: 256, margin: 10, error_correction: "M", foreground_color: "#030000", background_color: "#210303" }
svgStyler.ts:24:13
SVG Styler: Applied background color: #210303 svgStyler.ts:46:17
SVG Styler: Found 1763 QR modules to style with foreground color svgStyler.ts:55:17
SVG Styler: Module 1 (rect) - changed fill from "#000000" to "#030000" svgStyler.ts:75:29
SVG Styler: Module 2 (rect) - changed fill from "#000000" to "#030000" svgStyler.ts:75:29
SVG Styler: Module 3 (rect) - changed fill from "#000000" to "#030000" svgStyler.ts:75:29
SVG Styler: Module 4 (rect) - changed fill from "#000000" to "#030000" svgStyler.ts:75:29
SVG Styler: Using default square pattern svgStyler.ts:97:17
SVG Styler: Running finder pattern detection... svgStyler.ts:100:13
Finder pattern detection - backend classes found: 
Object { markerOuters: 3, markerInners: 3, markerDots: 3 }
svgStyler.ts:232:13
Backend finder pattern 1: 
Object { outer: {…}, inner: {…}, dot: {…} }
svgStyler.ts:260:21
Backend finder pattern 2: 
Object { outer: {…}, inner: {…}, dot: {…} }
svgStyler.ts:260:21
Backend finder pattern 3: 
Object { outer: {…}, inner: {…}, dot: {…} }
svgStyler.ts:260:21
Finder pattern detection summary: 
Object { patternsFound: 3, method: "backend-classes" }
svgStyler.ts:343:13
SVG Styler: Finder patterns detected: 3 svgStyler.ts:102:13
SVG Styler: Final SVG length: 274356 svgStyler.ts:129:13
Styled SVG: <svg baseProfile="full" height="300" version="1.1" width="300" xmlns="http://www.w3.org/2000/svg" xmlns:ev="http://www.w3.org/2001/xml-events" xmlns:xlink="http://www.w3.org/1999/xlink"><rect class="q... QRPreview.tsx:193:41
QRPreview: Content key updated 
Object { qrType: "email", dataChanged: true, designChanged: true, logoChanged: true, useSvg: true }
QRPreview.tsx:51:21
QRPreview: Content key updated  
Object { qrType: "email", dataChanged: true, designChanged: true, logoChanged: true, useSvg: true }
dashboard:1:596
QRPreview: Generating basic SVG for frontend styling QRPreview.tsx:108:37
QRPreview: Verifying data for email 
Object { 0: "c", 1: "o", 2: "n", 3: "t", 4: "a", 5: "c", 6: "t", 7: "@", 8: "e", 9: "x", … }
QRPreview.tsx:260:17
QRPreview: Verified data 
Object { 0: "c", 1: "o", 2: "n", 3: "t", 4: "a", 5: "c", 6: "t", 7: "@", 8: "e", 9: "x", … }
QRPreview.tsx:364:17
Frontend QR preview SVG request: 
Object { qrType: "email", dataSize: 363, designOptions: (6) […], hasLogo: true, dimensions: 300 }
qrService.modern.ts:376:21
Cleaned content for SVG preview: 
Object { email: "<EMAIL>", subject: "Hello from QRVibe", body: "I scanned your QR code and wanted to reach out.", cc: "", bcc: "", title: "Email QR Code", description: "Scan to view email content" }
qrService.modern.ts:396:21
[API] Request: POST /qr/preview/svg modernClient.ts:27:24
[API] Added auth token to request modernClient.ts:27:24
Frontend QR preview SVG request: 
Object { qrType: "email", dataSize: 401, designOptions: (32) […], hasLogo: true, dimensions: 300 }
qrService.modern.ts:376:21
Cleaned content for SVG preview: 
Object { email: "<EMAIL>", subject: "Hello from QRVibe", body: "I scanned your QR code and wanted to reach out.", cc: "", bcc: "", title: "Email QR Code", description: "Scan to view email content", email_address: "<EMAIL>" }
qrService.modern.ts:396:21
[API] Request: POST /qr/preview/svg modernClient.ts:27:24
[API] Added auth token to request modernClient.ts:27:24
[API] Response: 200 OK - POST /qr/preview/svg modernClient.ts:27:24
Successfully received SVG QR preview qrService.modern.ts:429:25
[API] Response: 200 OK - POST /qr/preview/svg modernClient.ts:27:24
Successfully received SVG QR preview qrService.modern.ts:429:25
Raw SVG from backend: <svg baseProfile="full" height="300" version="1.1" width="300" xmlns="http://www.w3.org/2000/svg" xmlns:ev="http://www.w3.org/2001/xml-events" xmlns:xlink="http://www.w3.org/1999/xlink"><defs /><rect class="qr-bg" fill="#FFFFFF" height="300" id="qr-bg" width="300" x="0" y="0" /><rect class="qr-module" fill="#000000" height="4.918032786885246" id="module-10-0" width="4.918032786885246" x="49.18032786885246" y="0.0" /><rect class="qr-module" fill="#000000" height="4.918032786885246" id="module-14-... QRPreview.tsx:127:37
SVG length: 276037 QRPreview.tsx:128:37
SVG contains <svg>: true QRPreview.tsx:129:37
Raw SVG structure: 
Object { rectCount: 1763, pathCount: 0 }
QRPreview.tsx:133:37
SVG analysis: 
Object { hasRect: true, hasPath: false, hasCircle: false, hasBackground: true, hasForeground: true, totalRects: 1763, largeRects: 7, finderPatternsDetected: true, originalDesignOptions: {…}, designOptionsKeys: (21) […] }
QRPreview.tsx:163:37
Large rectangles (potential finder patterns): 
Array(5) [ '<rect class="qr-bg" fill="#FFFFFF" height="300" id="qr-bg" width="300" x="0" y="0" />', '<rect class="qr-marker-outer" fill="#000000" height="34.42622950819672" width="34.42622950819672" x="0.0" y="0.0" />', '<rect class="qr-marker-inner" fill="#fff" height="22.62295081967213" width="22.62295081967213" x="5.901639344262295" y="5.901639344262295" />', '<rect class="qr-marker-outer" fill="#000000" height="34.42622950819672" width="34.42622950819672" x="265.57377049180326" y="0.0" />', '<rect class="qr-marker-inner" fill="#fff" height="22.62295081967213" width="22.62295081967213" x="271.4754098360656" y="5.901639344262295" />' ]
QRPreview.tsx:177:41
Original design options: 
Object { size: 256, margin: 10, pattern: "rounded", marker_border_style: "octagon", marker_border_color: "#cc1313", marker_center_style: "grid", marker_center_color: "#e91414", logo_size: 48, logo_position: "center", logo_opacity: 1, … }
QRPreview.tsx:183:37
Filtered design options for basic preview: 
Object { size: 256, margin: 10, error_correction: "M", foreground_color: "#030000", background_color: "#210303", pattern: "rounded" }
QRPreview.tsx:184:37
Filtered options count: 6 QRPreview.tsx:185:37
SVG Styler: Parsing SVG, element count: 1758 svgStyler.ts:23:13
SVG Styler: Design options: 
Object { size: 256, margin: 10, error_correction: "M", foreground_color: "#030000", background_color: "#210303", pattern: "rounded" }
svgStyler.ts:24:13
SVG Styler: Applied background color: #210303 svgStyler.ts:46:17
SVG Styler: Found 1763 QR modules to style with foreground color svgStyler.ts:55:17
SVG Styler: Module 1 (rect) - changed fill from "#000000" to "#030000" svgStyler.ts:75:29
SVG Styler: Module 2 (rect) - changed fill from "#000000" to "#030000" svgStyler.ts:75:29
SVG Styler: Module 3 (rect) - changed fill from "#000000" to "#030000" svgStyler.ts:75:29
SVG Styler: Module 4 (rect) - changed fill from "#000000" to "#030000" svgStyler.ts:75:29
SVG Styler: Applying pattern styling: rounded svgStyler.ts:94:17
Finder pattern detection - backend classes found: 
Object { markerOuters: 3, markerInners: 3, markerDots: 3 }
svgStyler.ts:232:13
Backend finder pattern 1: 
Object { outer: {…}, inner: {…}, dot: {…} }
svgStyler.ts:260:21
Backend finder pattern 2: 
Object { outer: {…}, inner: {…}, dot: {…} }
svgStyler.ts:260:21
Backend finder pattern 3: 
Object { outer: {…}, inner: {…}, dot: {…} }
svgStyler.ts:260:21
Finder pattern detection summary: 
Object { patternsFound: 3, method: "backend-classes" }
svgStyler.ts:343:13
Pattern styling: 
Object { pattern: "rounded", totalRects: 1762, finderElements: 9, dataModules: 1753, markerElements: 9 }
svgStyler.ts:369:13
SVG Styler: Running finder pattern detection... svgStyler.ts:100:13
Finder pattern detection - backend classes found: 
Object { markerOuters: 3, markerInners: 3, markerDots: 3 }
svgStyler.ts:232:13
Backend finder pattern 1: 
Object { outer: {…}, inner: {…}, dot: {…} }
svgStyler.ts:260:21
Backend finder pattern 2: 
Object { outer: {…}, inner: {…}, dot: {…} }
svgStyler.ts:260:21
Backend finder pattern 3: 
Object { outer: {…}, inner: {…}, dot: {…} }
svgStyler.ts:260:21
Finder pattern detection summary: 
Object { patternsFound: 3, method: "backend-classes" }
svgStyler.ts:343:13
SVG Styler: Finder patterns detected: 3 svgStyler.ts:102:13
SVG Styler: Final SVG length: 358500 svgStyler.ts:129:13
Styled SVG: <svg baseProfile="full" height="300" version="1.1" width="300" xmlns="http://www.w3.org/2000/svg" xmlns:ev="http://www.w3.org/2001/xml-events" xmlns:xlink="http://www.w3.org/1999/xlink"><rect class="q... QRPreview.tsx:193:41
QRPreview: Content key updated 
Object { qrType: "email", dataChanged: true, designChanged: true, logoChanged: true, useSvg: true }
QRPreview.tsx:51:21
QRPreview: Content key updated  
Object { qrType: "email", dataChanged: true, designChanged: true, logoChanged: true, useSvg: true }
dashboard:1:596
QRPreview: Generating basic SVG for frontend styling QRPreview.tsx:108:37
QRPreview: Verifying data for email 
Object { 0: "c", 1: "o", 2: "n", 3: "t", 4: "a", 5: "c", 6: "t", 7: "@", 8: "e", 9: "x", … }
QRPreview.tsx:260:17
QRPreview: Verified data 
Object { 0: "c", 1: "o", 2: "n", 3: "t", 4: "a", 5: "c", 6: "t", 7: "@", 8: "e", 9: "x", … }
QRPreview.tsx:364:17
Frontend QR preview SVG request: 
Object { qrType: "email", dataSize: 363, designOptions: (6) […], hasLogo: true, dimensions: 300 }
qrService.modern.ts:376:21
Cleaned content for SVG preview: 
Object { email: "<EMAIL>", subject: "Hello from QRVibe", body: "I scanned your QR code and wanted to reach out.", cc: "", bcc: "", title: "Email QR Code", description: "Scan to view email content" }
qrService.modern.ts:396:21
[API] Request: POST /qr/preview/svg modernClient.ts:27:24
[API] Added auth token to request modernClient.ts:27:24
Frontend QR preview SVG request: 
Object { qrType: "email", dataSize: 401, designOptions: (32) […], hasLogo: true, dimensions: 300 }
qrService.modern.ts:376:21
Cleaned content for SVG preview: 
Object { email: "<EMAIL>", subject: "Hello from QRVibe", body: "I scanned your QR code and wanted to reach out.", cc: "", bcc: "", title: "Email QR Code", description: "Scan to view email content", email_address: "<EMAIL>" }
qrService.modern.ts:396:21
[API] Request: POST /qr/preview/svg modernClient.ts:27:24
[API] Added auth token to request modernClient.ts:27:24
[API] Response: 200 OK - POST /qr/preview/svg modernClient.ts:27:24
Successfully received SVG QR preview qrService.modern.ts:429:25
[API] Response: 200 OK - POST /qr/preview/svg modernClient.ts:27:24
Successfully received SVG QR preview qrService.modern.ts:429:25
Raw SVG from backend: <svg baseProfile="full" height="300" version="1.1" width="300" xmlns="http://www.w3.org/2000/svg" xmlns:ev="http://www.w3.org/2001/xml-events" xmlns:xlink="http://www.w3.org/1999/xlink"><defs /><rect class="qr-bg" fill="#FFFFFF" height="300" id="qr-bg" width="300" x="0" y="0" /><rect class="qr-module" fill="#000000" height="4.918032786885246" id="module-10-0" width="4.918032786885246" x="49.18032786885246" y="0.0" /><rect class="qr-module" fill="#000000" height="4.918032786885246" id="module-14-... QRPreview.tsx:127:37
SVG length: 276037 QRPreview.tsx:128:37
SVG contains <svg>: true QRPreview.tsx:129:37
Raw SVG structure: 
Object { rectCount: 1763, pathCount: 0 }
QRPreview.tsx:133:37
SVG analysis: 
Object { hasRect: true, hasPath: false, hasCircle: false, hasBackground: true, hasForeground: true, totalRects: 1763, largeRects: 7, finderPatternsDetected: true, originalDesignOptions: {…}, designOptionsKeys: (21) […] }
QRPreview.tsx:163:37
Large rectangles (potential finder patterns): 
Array(5) [ '<rect class="qr-bg" fill="#FFFFFF" height="300" id="qr-bg" width="300" x="0" y="0" />', '<rect class="qr-marker-outer" fill="#000000" height="34.42622950819672" width="34.42622950819672" x="0.0" y="0.0" />', '<rect class="qr-marker-inner" fill="#fff" height="22.62295081967213" width="22.62295081967213" x="5.901639344262295" y="5.901639344262295" />', '<rect class="qr-marker-outer" fill="#000000" height="34.42622950819672" width="34.42622950819672" x="265.57377049180326" y="0.0" />', '<rect class="qr-marker-inner" fill="#fff" height="22.62295081967213" width="22.62295081967213" x="271.4754098360656" y="5.901639344262295" />' ]
QRPreview.tsx:177:41
Original design options: 
Object { size: 256, margin: 10, pattern: "dots", marker_border_style: "octagon", marker_border_color: "#cc1313", marker_center_style: "grid", marker_center_color: "#e91414", logo_size: 48, logo_position: "center", logo_opacity: 1, … }
QRPreview.tsx:183:37
Filtered design options for basic preview: 
Object { size: 256, margin: 10, error_correction: "M", foreground_color: "#030000", background_color: "#210303", pattern: "dots" }
QRPreview.tsx:184:37
Filtered options count: 6 QRPreview.tsx:185:37
SVG Styler: Parsing SVG, element count: 1758 svgStyler.ts:23:13
SVG Styler: Design options: 
Object { size: 256, margin: 10, error_correction: "M", foreground_color: "#030000", background_color: "#210303", pattern: "dots" }
svgStyler.ts:24:13
SVG Styler: Applied background color: #210303 svgStyler.ts:46:17
SVG Styler: Found 1763 QR modules to style with foreground color svgStyler.ts:55:17
SVG Styler: Module 1 (rect) - changed fill from "#000000" to "#030000" svgStyler.ts:75:29
SVG Styler: Module 2 (rect) - changed fill from "#000000" to "#030000" svgStyler.ts:75:29
SVG Styler: Module 3 (rect) - changed fill from "#000000" to "#030000" svgStyler.ts:75:29
SVG Styler: Module 4 (rect) - changed fill from "#000000" to "#030000" svgStyler.ts:75:29
SVG Styler: Applying pattern styling: dots svgStyler.ts:94:17
Finder pattern detection - backend classes found: 
Object { markerOuters: 3, markerInners: 3, markerDots: 3 }
svgStyler.ts:232:13
Backend finder pattern 1: 
Object { outer: {…}, inner: {…}, dot: {…} }
svgStyler.ts:260:21
Backend finder pattern 2: 
Object { outer: {…}, inner: {…}, dot: {…} }
svgStyler.ts:260:21
Backend finder pattern 3: 
Object { outer: {…}, inner: {…}, dot: {…} }
svgStyler.ts:260:21
Finder pattern detection summary: 
Object { patternsFound: 3, method: "backend-classes" }
svgStyler.ts:343:13
Pattern styling: 
Object { pattern: "dots", totalRects: 1762, finderElements: 9, dataModules: 1753, markerElements: 9 }
svgStyler.ts:369:13
SVG Styler: Running finder pattern detection... svgStyler.ts:100:13
Finder pattern detection - backend classes found: 
Object { markerOuters: 3, markerInners: 3, markerDots: 3 }
svgStyler.ts:232:13
Backend finder pattern 1: 
Object { outer: {…}, inner: {…}, dot: {…} }
svgStyler.ts:260:21
Backend finder pattern 2: 
Object { outer: {…}, inner: {…}, dot: {…} }
svgStyler.ts:260:21
Backend finder pattern 3: 
Object { outer: {…}, inner: {…}, dot: {…} }
svgStyler.ts:260:21
Finder pattern detection summary: 
Object { patternsFound: 3, method: "backend-classes" }
svgStyler.ts:343:13
SVG Styler: Finder patterns detected: 3 svgStyler.ts:102:13
SVG Styler: Final SVG length: 163666 svgStyler.ts:129:13
Styled SVG: <svg baseProfile="full" height="300" version="1.1" width="300" xmlns="http://www.w3.org/2000/svg" xmlns:ev="http://www.w3.org/2001/xml-events" xmlns:xlink="http://www.w3.org/1999/xlink"><rect class="q... QRPreview.tsx:193:41
[Auth] Adding Bearer token to request: Request layout.tsx:133:49
[Fast Refresh] rebuilding hot-reloader-client.js:197:29
[Auth] Adding Bearer token to request: /_next/static/webpack/ab53fcc2b7f331e3.webpack.hot-update.json layout.tsx:133:49
[Fast Refresh] done in 1228ms report-hmr-latency.js:14:13
[API] Request: GET /subscription/features modernClient.ts:27:24
[API] Added auth token to request modernClient.ts:27:24
[API] Request: GET /subscription/current modernClient.ts:27:24
[API] Added auth token to request modernClient.ts:27:24
[API] Response: 200 OK - GET /subscription/current modernClient.ts:27:24
[API] Response: 200 OK - GET /subscription/features modernClient.ts:27:24
[Auth] Adding Bearer token to request: /health 2 layout.tsx:133:49
[Auth] Health check successful context.tsx:123:37
[Fast Refresh] rebuilding hot-reloader-client.js:197:29
[Auth] Adding Bearer token to request: Request layout.tsx:133:49
[Auth] Adding Bearer token to request: /_next/static/webpack/757f4649b7ebccaf.webpack.hot-update.json layout.tsx:133:49
[Fast Refresh] done in 812ms report-hmr-latency.js:14:13
[DashboardLayout] Checking token: Present (327 chars) layout.tsx:89:29
[DashboardLayout] Valid token found on initial check layout.tsx:96:25
[DashboardLayout] Setting up auth headers layout.tsx:101:29
[Auth] Getting current user with Bearer token authentication authService.ts:315:21
[Auth] Token available: true Length: 327 authService.ts:316:21
[Auth] Making /auth/me request with Bearer token: eyJhbGciOiJIUzI... authService.ts:332:21
[Auth] Adding Bearer token to request: /auth/me layout.tsx:133:49
[Auth] Auth check response: 200 authService.ts:342:21
[Auth] User data retrieved successfully: <EMAIL> authService.ts:355:21
[Auth] Adding Bearer token to request: Request layout.tsx:133:49
Frontend QR preview SVG request: 
Object { qrType: "url", dataSize: 160, designOptions: (17) […], hasLogo: false, dimensions: 300 }
qrService.modern.ts:376:21
Cleaned content for SVG preview: 
Object { url: "https://example.com", utm_source: "", utm_medium: "", utm_campaign: "", short_url: false, title: "Url QR Code", description: "Scan to view url content" }
qrService.modern.ts:396:21
[API] Request: POST /qr/preview/svg modernClient.ts:27:24
[API] Added auth token to request modernClient.ts:27:24
[API] Response: 200 OK - POST /qr/preview/svg modernClient.ts:27:24
Successfully received SVG QR preview qrService.modern.ts:429:25
Frontend QR preview SVG request: 
Object { qrType: "email", dataSize: 240, designOptions: (17) […], hasLogo: false, dimensions: 300 }
qrService.modern.ts:376:21
Cleaned content for SVG preview: 
Object { email: "<EMAIL>", subject: "Hello from QRVibe", body: "I scanned your QR code and wanted to reach out.", cc: "", bcc: "", title: "Email QR Code", description: "Scan to view email content", email_address: "<EMAIL>" }
qrService.modern.ts:396:21
[API] Request: POST /qr/preview/svg modernClient.ts:27:24
[API] Added auth token to request modernClient.ts:27:24
[API] Response: 200 OK - POST /qr/preview/svg modernClient.ts:27:24
Successfully received SVG QR preview qrService.modern.ts:429:25
[Auth] Adding Bearer token to request: /health layout.tsx:133:49
QRPreview: Content key updated 
Object { qrType: "email", dataChanged: true, designChanged: true, logoChanged: false, useSvg: true }
QRPreview.tsx:51:21
QRPreview: Content key updated  
Object { qrType: "email", dataChanged: true, designChanged: true, logoChanged: false, useSvg: true }
dashboard:1:596
QRDataForm: initializing email with provided data + defaults: 
Object { email: "<EMAIL>", subject: "Hello from QRVibe", body: "I scanned your QR code and wanted to reach out.", cc: "", bcc: "", title: "Email QR Code", description: "Scan to view email content" }
QRDataForm.tsx:2072:33
QR data updated: 
Object { email: "<EMAIL>", subject: "Hello from QRVibe", body: "I scanned your QR code and wanted to reach out.", cc: "", bcc: "", title: "Email QR Code", description: "Scan to view email content" }
page.tsx:462:21
QRDataForm: initializing email with provided data + defaults:  
Object { email: "<EMAIL>", subject: "Hello from QRVibe", body: "I scanned your QR code and wanted to reach out.", cc: "", bcc: "", title: "Email QR Code", description: "Scan to view email content" }
dashboard:1:596
QR data updated:  
Object { email: "<EMAIL>", subject: "Hello from QRVibe", body: "I scanned your QR code and wanted to reach out.", cc: "", bcc: "", title: "Email QR Code", description: "Scan to view email content" }
dashboard:1:596
QRFormState: Initialized with 
Object { hasInitialData: true, mergedDataKeys: (7) […] }
useQRFormState.ts:26:21
QRFormState: Initialized with  
Object { hasInitialData: true, mergedDataKeys: (7) […] }
dashboard:1:596
QRPreview: Content key updated 
Object { qrType: "email", dataChanged: true, designChanged: true, logoChanged: false, useSvg: true }
QRPreview.tsx:51:21
QRPreview: Content key updated  
Object { qrType: "email", dataChanged: true, designChanged: true, logoChanged: false, useSvg: true }
dashboard:1:596
QRFormState: First render, notifying parent of initial state useQRFormState.ts:150:25
QRDataForm (email): Received data update: 
Object { email: "<EMAIL>", subject: "Hello from QRVibe", body: "I scanned your QR code and wanted to reach out.", cc: "", bcc: "", title: "Email QR Code", description: "Scan to view email content" }
QRDataForm.tsx:2100:21
QRFormState: External initialData changed, updating form state useQRFormState.ts:166:25
QRDataForm (email): Notifying parent of data change: 
Object { email: "<EMAIL>", subject: "Hello from QRVibe", body: "I scanned your QR code and wanted to reach out.", cc: "", bcc: "", title: "Email QR Code", description: "Scan to view email content" }
QRDataForm.tsx:2153:21
QR data updated: 
Object { email: "<EMAIL>", subject: "Hello from QRVibe", body: "I scanned your QR code and wanted to reach out.", cc: "", bcc: "", title: "Email QR Code", description: "Scan to view email content" }
page.tsx:462:21
QRFormState: External initialData changed, updating form state dashboard:1:596
QRDataForm (email): Updated form data: 
Object { email: "<EMAIL>", subject: "Hello from QRVibe", body: "I scanned your QR code and wanted to reach out.", cc: "", bcc: "", title: "Email QR Code", description: "Scan to view email content" }
QRDataForm.tsx:2133:29
QRDataForm (email): Updated form data: 
Object { email: "<EMAIL>", subject: "Hello from QRVibe", body: "I scanned your QR code and wanted to reach out.", cc: "", bcc: "", title: "Email QR Code", description: "Scan to view email content" }
QRDataForm.tsx:2133:29
QRPreview: Content key updated 
Object { qrType: "email", dataChanged: true, designChanged: true, logoChanged: false, useSvg: true }
QRPreview.tsx:51:21
QRPreview: Content key updated  
Object { qrType: "email", dataChanged: true, designChanged: true, logoChanged: false, useSvg: true }
dashboard:1:596
QRFormState: External initialData changed, updating form state useQRFormState.ts:166:25
QRDataForm (email): Notifying parent of data change: 
Object { email: "<EMAIL>", subject: "Hello from QRVibe", body: "I scanned your QR code and wanted to reach out.", cc: "", bcc: "", title: "Email QR Code", description: "Scan to view email content" }
QRDataForm.tsx:2153:21
QR data updated: 
Object { email: "<EMAIL>", subject: "Hello from QRVibe", body: "I scanned your QR code and wanted to reach out.", cc: "", bcc: "", title: "Email QR Code", description: "Scan to view email content" }
page.tsx:462:21
QRPreview: Content key updated 
Object { qrType: "email", dataChanged: true, designChanged: true, logoChanged: false, useSvg: true }
QRPreview.tsx:51:21
QRPreview: Content key updated  
Object { qrType: "email", dataChanged: true, designChanged: true, logoChanged: false, useSvg: true }
dashboard:1:596
QRPreview: Generating basic SVG for frontend styling QRPreview.tsx:108:37
QRPreview: Verifying data for email 
Object { 0: "c", 1: "o", 2: "n", 3: "t", 4: "a", 5: "c", 6: "t", 7: "@", 8: "e", 9: "x", … }
QRPreview.tsx:260:17
QRPreview: Verified data 
Object { 0: "c", 1: "o", 2: "n", 3: "t", 4: "a", 5: "c", 6: "t", 7: "@", 8: "e", 9: "x", … }
QRPreview.tsx:364:17
Frontend QR preview SVG request: 
Object { qrType: "email", dataSize: 363, designOptions: (6) […], hasLogo: false, dimensions: 300 }
qrService.modern.ts:376:21
Cleaned content for SVG preview: 
Object { email: "<EMAIL>", subject: "Hello from QRVibe", body: "I scanned your QR code and wanted to reach out.", cc: "", bcc: "", title: "Email QR Code", description: "Scan to view email content" }
qrService.modern.ts:396:21
[API] Request: POST /qr/preview/svg modernClient.ts:27:24
[API] Added auth token to request modernClient.ts:27:24
Frontend QR preview SVG request: 
Object { qrType: "email", dataSize: 401, designOptions: (32) […], hasLogo: false, dimensions: 300 }
qrService.modern.ts:376:21
Cleaned content for SVG preview: 
Object { email: "<EMAIL>", subject: "Hello from QRVibe", body: "I scanned your QR code and wanted to reach out.", cc: "", bcc: "", title: "Email QR Code", description: "Scan to view email content", email_address: "<EMAIL>" }
qrService.modern.ts:396:21
[API] Request: POST /qr/preview/svg modernClient.ts:27:24
[API] Added auth token to request modernClient.ts:27:24
[API] Response: 200 OK - POST /qr/preview/svg modernClient.ts:27:24
Successfully received SVG QR preview qrService.modern.ts:429:25
Raw SVG from backend: <svg baseProfile="full" height="300" version="1.1" width="300" xmlns="http://www.w3.org/2000/svg" xmlns:ev="http://www.w3.org/2001/xml-events" xmlns:xlink="http://www.w3.org/1999/xlink"><defs /><rect class="qr-bg" fill="#FFFFFF" height="300" id="qr-bg" width="300" x="0" y="0" /><rect class="qr-module" fill="#000000" height="4.918032786885246" id="module-10-0" width="4.918032786885246" x="49.18032786885246" y="0.0" /><rect class="qr-module" fill="#000000" height="4.918032786885246" id="module-14-... QRPreview.tsx:127:37
SVG length: 276037 QRPreview.tsx:128:37
SVG contains <svg>: true QRPreview.tsx:129:37
Raw SVG structure: 
Object { rectCount: 1763, pathCount: 0 }
QRPreview.tsx:133:37
SVG analysis: 
Object { hasRect: true, hasPath: false, hasCircle: false, hasBackground: true, hasForeground: true, totalRects: 1763, largeRects: 7, finderPatternsDetected: true, originalDesignOptions: {…}, designOptionsKeys: (21) […] }
QRPreview.tsx:163:37
Large rectangles (potential finder patterns): 
Array(5) [ '<rect class="qr-bg" fill="#FFFFFF" height="300" id="qr-bg" width="300" x="0" y="0" />', '<rect class="qr-marker-outer" fill="#000000" height="34.42622950819672" width="34.42622950819672" x="0.0" y="0.0" />', '<rect class="qr-marker-inner" fill="#fff" height="22.62295081967213" width="22.62295081967213" x="5.901639344262295" y="5.901639344262295" />', '<rect class="qr-marker-outer" fill="#000000" height="34.42622950819672" width="34.42622950819672" x="265.57377049180326" y="0.0" />', '<rect class="qr-marker-inner" fill="#fff" height="22.62295081967213" width="22.62295081967213" x="271.4754098360656" y="5.901639344262295" />' ]
QRPreview.tsx:177:41
Original design options: 
Object { size: 256, margin: 10, pattern: "square", marker_border_style: "square", marker_border_color: "#000000", marker_center_style: "square", marker_center_color: "#000000", logo_size: 48, logo_position: "center", logo_opacity: 1, … }
QRPreview.tsx:183:37
Filtered design options for basic preview: 
Object { size: 256, margin: 10, error_correction: "M" }
QRPreview.tsx:184:37
Filtered options count: 3 QRPreview.tsx:185:37
SVG Styler: Parsing SVG, element count: 1758 svgStyler.ts:23:13
SVG Styler: Design options: 
Object { size: 256, margin: 10, error_correction: "M" }
svgStyler.ts:24:13
SVG Styler: Using default background color svgStyler.ts:48:17
SVG Styler: Using default foreground color svgStyler.ts:80:17
SVG Styler: Using default square pattern svgStyler.ts:97:17
SVG Styler: Running finder pattern detection... svgStyler.ts:100:13
Finder pattern detection - backend classes found: 
Object { markerOuters: 3, markerInners: 3, markerDots: 3 }
svgStyler.ts:232:13
Backend finder pattern 1: 
Object { outer: {…}, inner: {…}, dot: {…} }
svgStyler.ts:260:21
Backend finder pattern 2: 
Object { outer: {…}, inner: {…}, dot: {…} }
svgStyler.ts:260:21
Backend finder pattern 3: 
Object { outer: {…}, inner: {…}, dot: {…} }
svgStyler.ts:260:21
Finder pattern detection summary: 
Object { patternsFound: 3, method: "backend-classes" }
svgStyler.ts:343:13
SVG Styler: Finder patterns detected: 3 svgStyler.ts:102:13
SVG Styler: Final SVG length: 274273 svgStyler.ts:129:13
Styled SVG: <svg baseProfile="full" height="300" version="1.1" width="300" xmlns="http://www.w3.org/2000/svg" xmlns:ev="http://www.w3.org/2001/xml-events" xmlns:xlink="http://www.w3.org/1999/xlink"><defs/><rect c... QRPreview.tsx:193:41
[API] Response: 200 OK - POST /qr/preview/svg modernClient.ts:27:24
Successfully received SVG QR preview qrService.modern.ts:429:25
QRPreview: Content key updated 
Object { qrType: "email", dataChanged: true, designChanged: true, logoChanged: false, useSvg: true }
QRPreview.tsx:51:21
QRPreview: Content key updated  
Object { qrType: "email", dataChanged: true, designChanged: true, logoChanged: false, useSvg: true }
dashboard:1:596
QRPreview: Generating basic SVG for frontend styling QRPreview.tsx:108:37
QRPreview: Verifying data for email 
Object { 0: "c", 1: "o", 2: "n", 3: "t", 4: "a", 5: "c", 6: "t", 7: "@", 8: "e", 9: "x", … }
QRPreview.tsx:260:17
QRPreview: Verified data 
Object { 0: "c", 1: "o", 2: "n", 3: "t", 4: "a", 5: "c", 6: "t", 7: "@", 8: "e", 9: "x", … }
QRPreview.tsx:364:17
Frontend QR preview SVG request: 
Object { qrType: "email", dataSize: 363, designOptions: (6) […], hasLogo: false, dimensions: 300 }
qrService.modern.ts:376:21
Cleaned content for SVG preview: 
Object { email: "<EMAIL>", subject: "Hello from QRVibe", body: "I scanned your QR code and wanted to reach out.", cc: "", bcc: "", title: "Email QR Code", description: "Scan to view email content" }
qrService.modern.ts:396:21
[API] Request: POST /qr/preview/svg modernClient.ts:27:24
[API] Added auth token to request modernClient.ts:27:24
Frontend QR preview SVG request: 
Object { qrType: "email", dataSize: 401, designOptions: (32) […], hasLogo: false, dimensions: 300 }
qrService.modern.ts:376:21
Cleaned content for SVG preview: 
Object { email: "<EMAIL>", subject: "Hello from QRVibe", body: "I scanned your QR code and wanted to reach out.", cc: "", bcc: "", title: "Email QR Code", description: "Scan to view email content", email_address: "<EMAIL>" }
qrService.modern.ts:396:21
[API] Request: POST /qr/preview/svg modernClient.ts:27:24
[API] Added auth token to request modernClient.ts:27:24
[API] Response: 200 OK - POST /qr/preview/svg modernClient.ts:27:24
Successfully received SVG QR preview qrService.modern.ts:429:25
[API] Response: 200 OK - POST /qr/preview/svg modernClient.ts:27:24
Successfully received SVG QR preview qrService.modern.ts:429:25
Raw SVG from backend: <svg baseProfile="full" height="300" version="1.1" width="300" xmlns="http://www.w3.org/2000/svg" xmlns:ev="http://www.w3.org/2001/xml-events" xmlns:xlink="http://www.w3.org/1999/xlink"><defs /><rect class="qr-bg" fill="#FFFFFF" height="300" id="qr-bg" width="300" x="0" y="0" /><rect class="qr-module" fill="#000000" height="4.918032786885246" id="module-10-0" width="4.918032786885246" x="49.18032786885246" y="0.0" /><rect class="qr-module" fill="#000000" height="4.918032786885246" id="module-14-... QRPreview.tsx:127:37
SVG length: 276037 QRPreview.tsx:128:37
SVG contains <svg>: true QRPreview.tsx:129:37
Raw SVG structure: 
Object { rectCount: 1763, pathCount: 0 }
QRPreview.tsx:133:37
SVG analysis: 
Object { hasRect: true, hasPath: false, hasCircle: false, hasBackground: true, hasForeground: true, totalRects: 1763, largeRects: 7, finderPatternsDetected: true, originalDesignOptions: {…}, designOptionsKeys: (21) […] }
QRPreview.tsx:163:37
Large rectangles (potential finder patterns): 
Array(5) [ '<rect class="qr-bg" fill="#FFFFFF" height="300" id="qr-bg" width="300" x="0" y="0" />', '<rect class="qr-marker-outer" fill="#000000" height="34.42622950819672" width="34.42622950819672" x="0.0" y="0.0" />', '<rect class="qr-marker-inner" fill="#fff" height="22.62295081967213" width="22.62295081967213" x="5.901639344262295" y="5.901639344262295" />', '<rect class="qr-marker-outer" fill="#000000" height="34.42622950819672" width="34.42622950819672" x="265.57377049180326" y="0.0" />', '<rect class="qr-marker-inner" fill="#fff" height="22.62295081967213" width="22.62295081967213" x="271.4754098360656" y="5.901639344262295" />' ]
QRPreview.tsx:177:41
Original design options: 
Object { size: 256, margin: 10, pattern: "dots", marker_border_style: "square", marker_border_color: "#000000", marker_center_style: "square", marker_center_color: "#000000", logo_size: 48, logo_position: "center", logo_opacity: 1, … }
QRPreview.tsx:183:37
Filtered design options for basic preview: 
Object { size: 256, margin: 10, error_correction: "M", pattern: "dots" }
QRPreview.tsx:184:37
Filtered options count: 4 QRPreview.tsx:185:37
SVG Styler: Parsing SVG, element count: 1758 svgStyler.ts:23:13
SVG Styler: Design options: 
Object { size: 256, margin: 10, error_correction: "M", pattern: "dots" }
svgStyler.ts:24:13
SVG Styler: Using default background color svgStyler.ts:48:17
SVG Styler: Using default foreground color svgStyler.ts:80:17
SVG Styler: Applying pattern styling: dots svgStyler.ts:94:17
Finder pattern detection - backend classes found: 
Object { markerOuters: 3, markerInners: 3, markerDots: 3 }
svgStyler.ts:232:13
Backend finder pattern 1: 
Object { outer: {…}, inner: {…}, dot: {…} }
svgStyler.ts:260:21
Backend finder pattern 2: 
Object { outer: {…}, inner: {…}, dot: {…} }
svgStyler.ts:260:21
Backend finder pattern 3: 
Object { outer: {…}, inner: {…}, dot: {…} }
svgStyler.ts:260:21
Finder pattern detection summary: 
Object { patternsFound: 3, method: "backend-classes" }
svgStyler.ts:343:13
Pattern styling: 
Object { pattern: "dots", totalRects: 1762, finderElements: 9, dataModules: 1753, markerElements: 9 }
svgStyler.ts:369:13
SVG Styler: Running finder pattern detection... svgStyler.ts:100:13
Finder pattern detection - backend classes found: 
Object { markerOuters: 3, markerInners: 3, markerDots: 3 }
svgStyler.ts:232:13
Backend finder pattern 1: 
Object { outer: {…}, inner: {…}, dot: {…} }
svgStyler.ts:260:21
Backend finder pattern 2: 
Object { outer: {…}, inner: {…}, dot: {…} }
svgStyler.ts:260:21
Backend finder pattern 3: 
Object { outer: {…}, inner: {…}, dot: {…} }
svgStyler.ts:260:21
Finder pattern detection summary: 
Object { patternsFound: 3, method: "backend-classes" }
svgStyler.ts:343:13
SVG Styler: Finder patterns detected: 3 svgStyler.ts:102:13
SVG Styler: Final SVG length: 163583 svgStyler.ts:129:13
Styled SVG: <svg baseProfile="full" height="300" version="1.1" width="300" xmlns="http://www.w3.org/2000/svg" xmlns:ev="http://www.w3.org/2001/xml-events" xmlns:xlink="http://www.w3.org/1999/xlink"><defs/><rect c... QRPreview.tsx:193:41
Source map error: Error: request failed with status 404
Stack in the worker:networkRequest@resource://devtools/client/shared/source-map-loader/utils/network-request.js:43:9

Resource URL: null
Source Map URL: installHook.js.map
