Rendering /app/(user)/layout.tsx (user layout) layout.tsx:16:9
Rendering /app/layout.tsx (global layout) layout.tsx:25:9
[DashboardLayout] Checking token: Not found layout.tsx:89:29
[DashboardLayout] Waiting for hydration... layout.tsx:150:25
[DashboardLayout] Setting authentication timeout layout.tsx:216:25
[CryptoSession] Found session key in storage, attempting to restore cryptoSession.ts:46:17
[CryptoSession] Session key successfully restored from storage cryptoSession.ts:55:17
Session crypto initialized context.tsx:79:37
[CryptoSession] Found session key in storage, attempting to restore cryptoSession.ts:46:17
[CryptoSession] Session key successfully restored from storage cryptoSession.ts:55:17
[CryptoSession] Found encrypted token, attempting to decrypt, length: 561 cryptoSession.ts:181:17
[CryptoSession] Base64 successfully decoded - IV length: 12 CT length: 408 cryptoSession.ts:208:21
[DashboardLayout] Checking token: Not found layout.tsx:89:29
[DashboardLayout] No authentication detected, performing delayed check... layout.tsx:171:21
[CryptoSession] Token successfully decrypted from session storage: {"access_token":"eyJ... cryptoSession.ts:222:21
[TokenStore] Token restored from encrypted sessionStorage tokenStore.ts:56:21
[DashboardLayout] Checking token: Present (324 chars) layout.tsx:89:29
[DashboardLayout] Valid token found on initial check layout.tsx:96:25
[DashboardLayout] Setting up auth headers layout.tsx:101:29
[DashboardLayout] Adding X-Has-Auth-Token meta tag layout.tsx:107:37
[DashboardLayout] Setting up fetch interceptor layout.tsx:116:37
[DashboardLayout] Setting authentication timeout layout.tsx:216:25
[Auth] Getting current user with Bearer token authentication authService.ts:315:21
[Auth] Token available: true Length: 324 authService.ts:316:21
[Auth] Making /auth/me request with Bearer token: eyJhbGciOiJIUzI... authService.ts:332:21
[Auth] Adding Bearer token to request: /auth/me layout.tsx:133:49
[DashboardLayout] Checking token: Present (324 chars) layout.tsx:89:29
[DashboardLayout] Valid token found on initial check layout.tsx:96:25
[DashboardLayout] Setting up auth headers layout.tsx:101:29
[DashboardLayout] Setting authentication timeout layout.tsx:216:25
[Auth] Health check successful context.tsx:123:37
[Auth] Auth check response: 200 authService.ts:342:21
[Auth] User data retrieved successfully: <EMAIL> authService.ts:355:21
[Auth] Adding Bearer token to request: /health layout.tsx:133:49
[DashboardLayout] Checking token: Present (324 chars) layout.tsx:89:29
[DashboardLayout] Valid token found on initial check layout.tsx:96:25
[DashboardLayout] Setting up auth headers layout.tsx:101:29
[Auth] Adding Bearer token to request: /health dashboard:1:596
[API] Request: GET /subscription/features modernClient.ts:27:24
[API] Added auth token to request modernClient.ts:27:24
[API] Request: GET /subscription/current modernClient.ts:27:24
[API] Added auth token to request modernClient.ts:27:24
[API] Request: GET /notifications modernClient.ts:27:24
[API] Added auth token to request modernClient.ts:27:24
[API] Request: GET /notifications/unread-count modernClient.ts:27:24
[API] Added auth token to request modernClient.ts:27:24
[API] Request: GET /dashboard/stats modernClient.ts:27:24
[API] Added auth token to request modernClient.ts:27:24
Using UNSAFE_componentWillReceiveProps in strict mode is not recommended and may indicate bugs in your code. See https://react.dev/link/unsafe-component-lifecycles for details.

* Move data fetching code or side effects to componentDidUpdate.
* If you're updating state whenever props change, refactor your code to use memoization techniques or move it to static getDerivedStateFromProps. Learn more at: https://react.dev/link/derived-state

Please update the following components: Tour intercept-console-error.js:50:32
[Auth] Adding Bearer token to request: /__nextjs_original-stack-frames layout.tsx:133:49
Request for font "Geist" blocked at visibility level 2 (requires 3)
dashboard
[API] Response: 200 OK - GET /subscription/current modernClient.ts:27:24
[API] Response: 200 OK - GET /notifications/unread-count modernClient.ts:27:24
[API] Response: 200 OK - GET /notifications modernClient.ts:27:24
[API] Response: 200 OK - GET /subscription/features modernClient.ts:27:24
[API] Response: 200 OK - GET /dashboard/stats modernClient.ts:27:24
MouseEvent.mozInputSource is deprecated. Use PointerEvent.pointerType instead. layout.js line 2405 > eval:22:9
[Auth] Adding Bearer token to request: Request layout.tsx:133:49
[Fast Refresh] rebuilding hot-reloader-client.js:197:29
[Auth] Adding Bearer token to request: /_next/static/webpack/b66f6108add49106.webpack.hot-update.json layout.tsx:133:49
[Fast Refresh] done in 4934ms report-hmr-latency.js:14:13
Frontend QR preview SVG request: 
Object { qrType: "url", dataSize: 160, designOptions: (20) […], hasLogo: false, dimensions: 300 }
qrService.modern.ts:376:21
Cleaned content for SVG preview: 
Object { url: "https://example.com", utm_source: "", utm_medium: "", utm_campaign: "", short_url: false, title: "Url QR Code", description: "Scan to view url content" }
qrService.modern.ts:396:21
[API] Request: POST /qr/preview/svg modernClient.ts:27:24
[API] Added auth token to request modernClient.ts:27:24
The resource at “https://localhost:3000/_next/static/media/0484562807a97172-s.p.woff2” preloaded with link preload was not used within a few seconds. Make sure all attributes of the preload tag are set correctly. creator
The resource at “https://localhost:3000/_next/static/media/8888a3826f4a3af4-s.p.woff2” preloaded with link preload was not used within a few seconds. Make sure all attributes of the preload tag are set correctly. creator
The resource at “https://localhost:3000/_next/static/media/b957ea75a84b6ea7-s.p.woff2” preloaded with link preload was not used within a few seconds. Make sure all attributes of the preload tag are set correctly. creator
The resource at “https://localhost:3000/_next/static/media/eafabf029ad39a43-s.p.woff2” preloaded with link preload was not used within a few seconds. Make sure all attributes of the preload tag are set correctly. creator
[API] Response: 200 OK - POST /qr/preview/svg modernClient.ts:27:24
Successfully received SVG QR preview qrService.modern.ts:429:25
[Auth] Adding Bearer token to request: /health layout.tsx:133:49
Frontend QR preview SVG request: 
Object { qrType: "scooter_unlock", dataSize: 135, designOptions: (20) […], hasLogo: false, dimensions: 300 }
qrService.modern.ts:376:21
Cleaned content for SVG preview: 
Object { scooter_id: "SCOOT123", unlock_code: "UNLOCK42", title: "Scooter_unlock QR Code", description: "Scan to view scooter_unlock content" }
qrService.modern.ts:396:21
[API] Request: POST /qr/preview/svg modernClient.ts:27:24
[API] Added auth token to request modernClient.ts:27:24
[API] Response: 200 OK - POST /qr/preview/svg modernClient.ts:27:24
Successfully received SVG QR preview qrService.modern.ts:429:25
QRPreview: Content key updated 
Object { qrType: "scooter_unlock", dataChanged: true, designChanged: true, logoChanged: false, useSvg: true }
QRPreview.tsx:50:21
QRPreview: Content key updated  
Object { qrType: "scooter_unlock", dataChanged: true, designChanged: true, logoChanged: false, useSvg: true }
dashboard:1:596
QRDataForm: initializing scooter_unlock with provided data + defaults: 
Object { scooter_id: "SCOOT123", unlock_code: "UNLOCK42", title: "Scooter_unlock QR Code", description: "Scan to view scooter_unlock content" }
QRDataForm.tsx:2072:33
QR data updated: 
Object { scooter_id: "SCOOT123", unlock_code: "UNLOCK42", title: "Scooter_unlock QR Code", description: "Scan to view scooter_unlock content" }
page.tsx:487:21
QRDataForm: initializing scooter_unlock with provided data + defaults:  
Object { scooter_id: "SCOOT123", unlock_code: "UNLOCK42", title: "Scooter_unlock QR Code", description: "Scan to view scooter_unlock content" }
dashboard:1:596
QR data updated:  
Object { scooter_id: "SCOOT123", unlock_code: "UNLOCK42", title: "Scooter_unlock QR Code", description: "Scan to view scooter_unlock content" }
dashboard:1:596
QRPreview: Content key updated 
Object { qrType: "scooter_unlock", dataChanged: true, designChanged: true, logoChanged: false, useSvg: true }
QRPreview.tsx:50:21
QRPreview: Content key updated  
Object { qrType: "scooter_unlock", dataChanged: true, designChanged: true, logoChanged: false, useSvg: true }
dashboard:1:596
QRDataForm (scooter_unlock): Notifying parent of data change: 
Object { scooter_id: "SCOOT123", unlock_code: "UNLOCK42", title: "Scooter_unlock QR Code", description: "Scan to view scooter_unlock content" }
QRDataForm.tsx:2153:21
QR data updated: 
Object { scooter_id: "SCOOT123", unlock_code: "UNLOCK42", title: "Scooter_unlock QR Code", description: "Scan to view scooter_unlock content" }
page.tsx:487:21
QRPreview: Content key updated 
Object { qrType: "scooter_unlock", dataChanged: true, designChanged: true, logoChanged: false, useSvg: true }
QRPreview.tsx:50:21
QRPreview: Content key updated  
Object { qrType: "scooter_unlock", dataChanged: true, designChanged: true, logoChanged: false, useSvg: true }
dashboard:1:596
Frontend QR preview SVG request: 
Object { qrType: "scooter_unlock", dataSize: 135, designOptions: (35) […], hasLogo: false, dimensions: 300 }
qrService.modern.ts:376:21
Cleaned content for SVG preview: 
Object { scooter_id: "SCOOT123", unlock_code: "UNLOCK42", title: "Scooter_unlock QR Code", description: "Scan to view scooter_unlock content" }
qrService.modern.ts:396:21
[API] Request: POST /qr/preview/svg modernClient.ts:27:24
[API] Added auth token to request modernClient.ts:27:24
[API] Response: 200 OK - POST /qr/preview/svg modernClient.ts:27:24
Successfully received SVG QR preview qrService.modern.ts:429:25
QRFormState: Initialized with 
Object { hasInitialData: true, mergedDataKeys: (4) […] }
useQRFormState.ts:26:21
QRFormState: Initialized with  
Object { hasInitialData: true, mergedDataKeys: (4) […] }
dashboard:1:596
QRFormState: First render, notifying parent of initial state useQRFormState.ts:150:25
QRDataForm (scooter_unlock): Received data update: 
Object { scooter_id: "SCOOT123", unlock_code: "UNLOCK42", title: "Scooter_unlock QR Code", description: "Scan to view scooter_unlock content" }
QRDataForm.tsx:2100:21
QRDataForm (scooter_unlock): Updated form data: 
Object { scooter_id: "SCOOT123", unlock_code: "UNLOCK42", title: "Scooter_unlock QR Code", description: "Scan to view scooter_unlock content" }
QRDataForm.tsx:2133:29
QRFormState: External initialData changed, updating form state useQRFormState.ts:166:25
QRFormState: External initialData changed, updating form state dashboard:1:596
QRDataForm (scooter_unlock): Updated form data: 
Object { scooter_id: "SCOOT123", unlock_code: "UNLOCK42", title: "Scooter_unlock QR Code", description: "Scan to view scooter_unlock content" }
QRDataForm.tsx:2133:29
QRFormState: External initialData changed, updating form state useQRFormState.ts:166:25
QRDataForm (scooter_unlock): Notifying parent of data change: 
Object { scooter_id: "SCOOT123", unlock_code: "UNLOCK42", title: "Scooter_unlock QR Code", description: "Scan to view scooter_unlock content" }
QRDataForm.tsx:2153:21
QR data updated: 
Object { scooter_id: "SCOOT123", unlock_code: "UNLOCK42", title: "Scooter_unlock QR Code", description: "Scan to view scooter_unlock content" }
page.tsx:487:21
QRPreview: Content key updated 
Object { qrType: "scooter_unlock", dataChanged: true, designChanged: true, logoChanged: false, useSvg: true }
QRPreview.tsx:50:21
QRPreview: Content key updated  
Object { qrType: "scooter_unlock", dataChanged: true, designChanged: true, logoChanged: false, useSvg: true }
dashboard:1:596
Frontend QR preview SVG request: 
Object { qrType: "scooter_unlock", dataSize: 135, designOptions: (35) […], hasLogo: false, dimensions: 300 }
qrService.modern.ts:376:21
Cleaned content for SVG preview: 
Object { scooter_id: "SCOOT123", unlock_code: "UNLOCK42", title: "Scooter_unlock QR Code", description: "Scan to view scooter_unlock content" }
qrService.modern.ts:396:21
[API] Request: POST /qr/preview/svg modernClient.ts:27:24
[API] Added auth token to request modernClient.ts:27:24
[API] Response: 200 OK - POST /qr/preview/svg modernClient.ts:27:24
Successfully received SVG QR preview qrService.modern.ts:429:25
QRPreview: Generating basic SVG for frontend styling QRPreview.tsx:105:37
QRPreview: Processing logo: 
Object { logoType: "undefined", isFile: false, isDataUrl: false, logoLength: "N/A" }
QRPreview.tsx:108:37
QRPreview: Verifying data for scooter_unlock 
Object { scooter_id: "SCOOT123", unlock_code: "UNLOCK42", title: "Scooter_unlock QR Code", description: "Scan to view scooter_unlock content" }
QRPreview.tsx:236:17
QRPreview: Verified data 
Object { scooter_id: "SCOOT123", unlock_code: "UNLOCK42", title: "Scooter_unlock QR Code", description: "Scan to view scooter_unlock content" }
QRPreview.tsx:340:17
QRPreview: Sending to backend: 
Object { qrType: "scooter_unlock", dataKeys: (4) […], designOptionsKeys: (26) […], hasLogoFile: false, logoFileSize: undefined, logoFileName: undefined }
QRPreview.tsx:137:37
Frontend QR preview SVG request: 
Object { qrType: "scooter_unlock", dataSize: 135, designOptions: (26) […], hasLogo: false, dimensions: 300 }
qrService.modern.ts:376:21
Cleaned content for SVG preview: 
Object { scooter_id: "SCOOT123", unlock_code: "UNLOCK42", title: "Scooter_unlock QR Code", description: "Scan to view scooter_unlock content" }
qrService.modern.ts:396:21
[API] Request: POST /qr/preview/svg modernClient.ts:27:24
[API] Added auth token to request modernClient.ts:27:24
[API] Response: 200 OK - POST /qr/preview/svg modernClient.ts:27:24
Successfully received SVG QR preview qrService.modern.ts:429:25
Raw SVG from backend: <svg baseProfile="full" height="340" preserveAspectRatio="xMidYMid meet" version="1.1" viewBox="0 0 340 340" width="340" xmlns="http://www.w3.org/2000/svg" xmlns:ev="http://www.w3.org/2001/xml-events" xmlns:xlink="http://www.w3.org/1999/xlink"><defs /><rect class="qr-bg" fill="#FFFFFF" height="340" id="qr-bg" width="340" x="0" y="0" /><rect class="qr-module" fill="#000000" height="5.918367346938775" id="module-8-0" width="5.918367346938775" x="67.34693877551021" y="20.0" /><rect class="qr-module... QRPreview.tsx:155:37
SVG length: 176251 QRPreview.tsx:156:37
SVG contains <svg>: true QRPreview.tsx:157:37
Raw SVG structure: 
Object { rectCount: 1127, pathCount: 0 }
QRPreview.tsx:161:37
SVG analysis: 
Object { hasRect: true, hasPath: false, hasCircle: false, hasBackground: true, hasForeground: true, totalRects: 1127, largeRects: 7, finderPatternsDetected: true, originalDesignOptions: {…}, designOptionsKeys: (24) […] }
QRPreview.tsx:191:37
Large rectangles (potential finder patterns): 
Array(5) [ '<rect class="qr-bg" fill="#FFFFFF" height="340" id="qr-bg" width="340" x="0" y="0" />', '<rect class="qr-marker-outer" fill="#000000" height="41.42857142857143" width="41.42857142857143" x="20.0" y="20.0" />', '<rect class="qr-marker-inner" fill="#fff" height="27.224489795918366" width="27.224489795918366" x="27.10204081632653" y="27.10204081632653" />', '<rect class="qr-marker-outer" fill="#000000" height="41.42857142857143" width="41.42857142857143" x="268.57142857142856" y="20.0" />', '<rect class="qr-marker-inner" fill="#fff" height="27.224489795918366" width="27.224489795918366" x="275.67346938775506" y="27.10204081632653" />' ]
QRPreview.tsx:205:41
Using SVG from backend with all styling applied QRPreview.tsx:208:37
QRFormState: Field updated 
Object { field: "service_provider", previousValue: undefined, newValue: "L" }
useQRFormState.ts:66:29
QRFormState: Field updated 
Object { field: "service_provider", previousValue: undefined, newValue: "L" }
useQRFormState.ts:66:29
A component is changing an uncontrolled input to be controlled. This is likely caused by the value changing from undefined to a defined value, which should not happen. Decide between using a controlled or uncontrolled input element for the lifetime of the component. More info: https://react.dev/link/controlled-components intercept-console-error.js:50:32
[Auth] Adding Bearer token to request: /__nextjs_original-stack-frames layout.tsx:133:49
QRFormState: Field updated 
Object { field: "service_provider", previousValue: "L", newValue: "Li" }
useQRFormState.ts:66:29
QRFormState: Field updated 
Object { field: "service_provider", previousValue: "L", newValue: "Li" }
useQRFormState.ts:66:29
QRFormState: Notifying parent of changes (debounced) useQRFormState.ts:39:29
QRDataForm (scooter_unlock): Received data update: 
Object { scooter_id: "SCOOT123", unlock_code: "UNLOCK42", title: "Scooter_unlock QR Code", description: "Scan to view scooter_unlock content", service_provider: "Li" }
QRDataForm.tsx:2100:21
QRDataForm (scooter_unlock): Updated form data: 
Object { scooter_id: "SCOOT123", unlock_code: "UNLOCK42", title: "Scooter_unlock QR Code", description: "Scan to view scooter_unlock content", service_provider: "Li" }
QRDataForm.tsx:2133:29
QRDataForm (scooter_unlock): Updated form data: 
Object { scooter_id: "SCOOT123", unlock_code: "UNLOCK42", title: "Scooter_unlock QR Code", description: "Scan to view scooter_unlock content", service_provider: "Li" }
QRDataForm.tsx:2133:29
QRFormState: Initialized with 
Object { hasInitialData: true, mergedDataKeys: (5) […] }
useQRFormState.ts:26:21
QRFormState: Initialized with  
Object { hasInitialData: true, mergedDataKeys: (5) […] }
dashboard:1:596
QRFormState: First render, notifying parent of initial state useQRFormState.ts:150:25
QRDataForm (scooter_unlock): Received data update: 
Object { scooter_id: "SCOOT123", unlock_code: "UNLOCK42", title: "Scooter_unlock QR Code", description: "Scan to view scooter_unlock content", service_provider: "Li" }
QRDataForm.tsx:2100:21
QRFormState: External initialData changed, updating form state useQRFormState.ts:166:25
QRDataForm (scooter_unlock): Notifying parent of data change: 
Object { scooter_id: "SCOOT123", unlock_code: "UNLOCK42", title: "Scooter_unlock QR Code", description: "Scan to view scooter_unlock content", service_provider: "Li" }
QRDataForm.tsx:2153:21
QR data updated: 
Object { scooter_id: "SCOOT123", unlock_code: "UNLOCK42", title: "Scooter_unlock QR Code", description: "Scan to view scooter_unlock content", service_provider: "Li" }
page.tsx:487:21
QRFormState: External initialData changed, updating form state dashboard:1:596
QRDataForm (scooter_unlock): Updated form data: 
Object { scooter_id: "SCOOT123", unlock_code: "UNLOCK42", title: "Scooter_unlock QR Code", description: "Scan to view scooter_unlock content", service_provider: "Li" }
QRDataForm.tsx:2133:29
QRDataForm (scooter_unlock): Updated form data: 
Object { scooter_id: "SCOOT123", unlock_code: "UNLOCK42", title: "Scooter_unlock QR Code", description: "Scan to view scooter_unlock content", service_provider: "Li" }
QRDataForm.tsx:2133:29
QRPreview: Content key updated 
Object { qrType: "scooter_unlock", dataChanged: true, designChanged: true, logoChanged: false, useSvg: true }
QRPreview.tsx:50:21
QRPreview: Content key updated  
Object { qrType: "scooter_unlock", dataChanged: true, designChanged: true, logoChanged: false, useSvg: true }
dashboard:1:596
QRFormState: External initialData changed, updating form state useQRFormState.ts:166:25
QRDataForm (scooter_unlock): Notifying parent of data change: 
Object { scooter_id: "SCOOT123", unlock_code: "UNLOCK42", title: "Scooter_unlock QR Code", description: "Scan to view scooter_unlock content", service_provider: "Li" }
QRDataForm.tsx:2153:21
QR data updated: 
Object { scooter_id: "SCOOT123", unlock_code: "UNLOCK42", title: "Scooter_unlock QR Code", description: "Scan to view scooter_unlock content", service_provider: "Li" }
page.tsx:487:21
QRPreview: Content key updated 
Object { qrType: "scooter_unlock", dataChanged: true, designChanged: true, logoChanged: false, useSvg: true }
QRPreview.tsx:50:21
QRPreview: Content key updated  
Object { qrType: "scooter_unlock", dataChanged: true, designChanged: true, logoChanged: false, useSvg: true }
dashboard:1:596
Frontend QR preview SVG request: 
Object { qrType: "scooter_unlock", dataSize: 159, designOptions: (35) […], hasLogo: false, dimensions: 300 }
qrService.modern.ts:376:21
Cleaned content for SVG preview: 
Object { scooter_id: "SCOOT123", unlock_code: "UNLOCK42", title: "Scooter_unlock QR Code", description: "Scan to view scooter_unlock content", service_provider: "Li" }
qrService.modern.ts:396:21
[API] Request: POST /qr/preview/svg modernClient.ts:27:24
[API] Added auth token to request modernClient.ts:27:24
[API] Response: 200 OK - POST /qr/preview/svg modernClient.ts:27:24
Successfully received SVG QR preview qrService.modern.ts:429:25
QRPreview: Content key updated 
Object { qrType: "scooter_unlock", dataChanged: true, designChanged: true, logoChanged: false, useSvg: true }
QRPreview.tsx:50:21
QRPreview: Content key updated  
Object { qrType: "scooter_unlock", dataChanged: true, designChanged: true, logoChanged: false, useSvg: true }
dashboard:1:596
QRPreview: Generating basic SVG for frontend styling QRPreview.tsx:105:37
QRPreview: Processing logo: 
Object { logoType: "undefined", isFile: false, isDataUrl: false, logoLength: "N/A" }
QRPreview.tsx:108:37
QRPreview: Verifying data for scooter_unlock 
Object { scooter_id: "SCOOT123", unlock_code: "UNLOCK42", title: "Scooter_unlock QR Code", description: "Scan to view scooter_unlock content", service_provider: "Li" }
QRPreview.tsx:236:17
QRPreview: Verified data 
Object { scooter_id: "SCOOT123", unlock_code: "UNLOCK42", title: "Scooter_unlock QR Code", description: "Scan to view scooter_unlock content", service_provider: "Li" }
QRPreview.tsx:340:17
QRPreview: Sending to backend: 
Object { qrType: "scooter_unlock", dataKeys: (5) […], designOptionsKeys: (26) […], hasLogoFile: false, logoFileSize: undefined, logoFileName: undefined }
QRPreview.tsx:137:37
Frontend QR preview SVG request: 
Object { qrType: "scooter_unlock", dataSize: 159, designOptions: (26) […], hasLogo: false, dimensions: 300 }
qrService.modern.ts:376:21
Cleaned content for SVG preview: 
Object { scooter_id: "SCOOT123", unlock_code: "UNLOCK42", title: "Scooter_unlock QR Code", description: "Scan to view scooter_unlock content", service_provider: "Li" }
qrService.modern.ts:396:21
[API] Request: POST /qr/preview/svg modernClient.ts:27:24
[API] Added auth token to request modernClient.ts:27:24
[API] Response: 200 OK - POST /qr/preview/svg modernClient.ts:27:24
Successfully received SVG QR preview qrService.modern.ts:429:25
Raw SVG from backend: <svg baseProfile="full" height="340" preserveAspectRatio="xMidYMid meet" version="1.1" viewBox="0 0 340 340" width="340" xmlns="http://www.w3.org/2000/svg" xmlns:ev="http://www.w3.org/2001/xml-events" xmlns:xlink="http://www.w3.org/1999/xlink"><defs /><rect class="qr-bg" fill="#FFFFFF" height="340" id="qr-bg" width="340" x="0" y="0" /><rect class="qr-module" fill="#000000" height="5.471698113207547" id="module-9-0" width="5.471698113207547" x="69.24528301886792" y="20.0" /><rect class="qr-module... QRPreview.tsx:155:37
SVG length: 205326 QRPreview.tsx:156:37
SVG contains <svg>: true QRPreview.tsx:157:37
Raw SVG structure: 
Object { rectCount: 1313, pathCount: 0 }
QRPreview.tsx:161:37
SVG analysis: 
Object { hasRect: true, hasPath: false, hasCircle: false, hasBackground: true, hasForeground: true, totalRects: 1313, largeRects: 7, finderPatternsDetected: true, originalDesignOptions: {…}, designOptionsKeys: (24) […] }
QRPreview.tsx:191:37
Large rectangles (potential finder patterns): 
Array(5) [ '<rect class="qr-bg" fill="#FFFFFF" height="340" id="qr-bg" width="340" x="0" y="0" />', '<rect class="qr-marker-outer" fill="#000000" height="38.301886792452834" width="38.301886792452834" x="20.0" y="20.0" />', '<rect class="qr-marker-inner" fill="#fff" height="25.169811320754715" width="25.169811320754715" x="26.566037735849058" y="26.566037735849058" />', '<rect class="qr-marker-outer" fill="#000000" height="38.301886792452834" width="38.301886792452834" x="271.6981132075472" y="20.0" />', '<rect class="qr-marker-inner" fill="#fff" height="25.169811320754715" width="25.169811320754715" x="278.26415094339626" y="26.566037735849058" />' ]
QRPreview.tsx:205:41
Using SVG from backend with all styling applied QRPreview.tsx:208:37
QRFormState: Field updated 
Object { field: "service_provider", previousValue: "Li", newValue: "Lim" }
useQRFormState.ts:66:29
QRFormState: Field updated 
Object { field: "service_provider", previousValue: "Li", newValue: "Lim" }
useQRFormState.ts:66:29
QRFormState: Field updated 
Object { field: "service_provider", previousValue: "Lim", newValue: "Lime" }
useQRFormState.ts:66:29
QRFormState: Field updated 
Object { field: "service_provider", previousValue: "Lim", newValue: "Lime" }
useQRFormState.ts:66:29
QRFormState: Notifying parent of changes (debounced) useQRFormState.ts:39:29
QRDataForm (scooter_unlock): Received data update: 
Object { scooter_id: "SCOOT123", unlock_code: "UNLOCK42", title: "Scooter_unlock QR Code", description: "Scan to view scooter_unlock content", service_provider: "Lime" }
QRDataForm.tsx:2100:21
QRDataForm (scooter_unlock): Updated form data: 
Object { scooter_id: "SCOOT123", unlock_code: "UNLOCK42", title: "Scooter_unlock QR Code", description: "Scan to view scooter_unlock content", service_provider: "Lime" }
QRDataForm.tsx:2133:29
QRDataForm (scooter_unlock): Updated form data: 
Object { scooter_id: "SCOOT123", unlock_code: "UNLOCK42", title: "Scooter_unlock QR Code", description: "Scan to view scooter_unlock content", service_provider: "Lime" }
QRDataForm.tsx:2133:29
QRFormState: External initialData changed, updating form state useQRFormState.ts:166:25
QRDataForm (scooter_unlock): Notifying parent of data change: 
Object { scooter_id: "SCOOT123", unlock_code: "UNLOCK42", title: "Scooter_unlock QR Code", description: "Scan to view scooter_unlock content", service_provider: "Lime" }
QRDataForm.tsx:2153:21
QR data updated: 
Object { scooter_id: "SCOOT123", unlock_code: "UNLOCK42", title: "Scooter_unlock QR Code", description: "Scan to view scooter_unlock content", service_provider: "Lime" }
page.tsx:487:21
QRPreview: Content key updated 
Object { qrType: "scooter_unlock", dataChanged: true, designChanged: true, logoChanged: false, useSvg: true }
QRPreview.tsx:50:21
QRPreview: Content key updated  
Object { qrType: "scooter_unlock", dataChanged: true, designChanged: true, logoChanged: false, useSvg: true }
dashboard:1:596
Frontend QR preview SVG request: 
Object { qrType: "scooter_unlock", dataSize: 161, designOptions: (35) […], hasLogo: false, dimensions: 300 }
qrService.modern.ts:376:21
Cleaned content for SVG preview: 
Object { scooter_id: "SCOOT123", unlock_code: "UNLOCK42", title: "Scooter_unlock QR Code", description: "Scan to view scooter_unlock content", service_provider: "Lime" }
qrService.modern.ts:396:21
[API] Request: POST /qr/preview/svg modernClient.ts:27:24
[API] Added auth token to request modernClient.ts:27:24
[API] Response: 200 OK - POST /qr/preview/svg modernClient.ts:27:24
Successfully received SVG QR preview qrService.modern.ts:429:25
QRPreview: Content key updated 
Object { qrType: "scooter_unlock", dataChanged: true, designChanged: true, logoChanged: false, useSvg: true }
QRPreview.tsx:50:21
QRPreview: Content key updated  
Object { qrType: "scooter_unlock", dataChanged: true, designChanged: true, logoChanged: false, useSvg: true }
dashboard:1:596
QRPreview: Generating basic SVG for frontend styling QRPreview.tsx:105:37
QRPreview: Processing logo: 
Object { logoType: "undefined", isFile: false, isDataUrl: false, logoLength: "N/A" }
QRPreview.tsx:108:37
QRPreview: Verifying data for scooter_unlock 
Object { scooter_id: "SCOOT123", unlock_code: "UNLOCK42", title: "Scooter_unlock QR Code", description: "Scan to view scooter_unlock content", service_provider: "Lime" }
QRPreview.tsx:236:17
QRPreview: Verified data 
Object { scooter_id: "SCOOT123", unlock_code: "UNLOCK42", title: "Scooter_unlock QR Code", description: "Scan to view scooter_unlock content", service_provider: "Lime" }
QRPreview.tsx:340:17
QRPreview: Sending to backend: 
Object { qrType: "scooter_unlock", dataKeys: (5) […], designOptionsKeys: (26) […], hasLogoFile: false, logoFileSize: undefined, logoFileName: undefined }
QRPreview.tsx:137:37
Frontend QR preview SVG request: 
Object { qrType: "scooter_unlock", dataSize: 161, designOptions: (26) […], hasLogo: false, dimensions: 300 }
qrService.modern.ts:376:21
Cleaned content for SVG preview: 
Object { scooter_id: "SCOOT123", unlock_code: "UNLOCK42", title: "Scooter_unlock QR Code", description: "Scan to view scooter_unlock content", service_provider: "Lime" }
qrService.modern.ts:396:21
[API] Request: POST /qr/preview/svg modernClient.ts:27:24
[API] Added auth token to request modernClient.ts:27:24
[API] Response: 200 OK - POST /qr/preview/svg modernClient.ts:27:24
Successfully received SVG QR preview qrService.modern.ts:429:25
Raw SVG from backend: <svg baseProfile="full" height="340" preserveAspectRatio="xMidYMid meet" version="1.1" viewBox="0 0 340 340" width="340" xmlns="http://www.w3.org/2000/svg" xmlns:ev="http://www.w3.org/2001/xml-events" xmlns:xlink="http://www.w3.org/1999/xlink"><defs /><rect class="qr-bg" fill="#FFFFFF" height="340" id="qr-bg" width="340" x="0" y="0" /><rect class="qr-module" fill="#000000" height="5.471698113207547" id="module-13-0" width="5.471698113207547" x="91.13207547169812" y="20.0" /><rect class="qr-modul... QRPreview.tsx:155:37
SVG length: 201904 QRPreview.tsx:156:37
SVG contains <svg>: true QRPreview.tsx:157:37
Raw SVG structure: 
Object { rectCount: 1291, pathCount: 0 }
QRPreview.tsx:161:37
SVG analysis: 
Object { hasRect: true, hasPath: false, hasCircle: false, hasBackground: true, hasForeground: true, totalRects: 1291, largeRects: 7, finderPatternsDetected: true, originalDesignOptions: {…}, designOptionsKeys: (24) […] }
QRPreview.tsx:191:37
Large rectangles (potential finder patterns): 
Array(5) [ '<rect class="qr-bg" fill="#FFFFFF" height="340" id="qr-bg" width="340" x="0" y="0" />', '<rect class="qr-marker-outer" fill="#000000" height="38.301886792452834" width="38.301886792452834" x="20.0" y="20.0" />', '<rect class="qr-marker-inner" fill="#fff" height="25.169811320754715" width="25.169811320754715" x="26.566037735849058" y="26.566037735849058" />', '<rect class="qr-marker-outer" fill="#000000" height="38.301886792452834" width="38.301886792452834" x="271.6981132075472" y="20.0" />', '<rect class="qr-marker-inner" fill="#fff" height="25.169811320754715" width="25.169811320754715" x="278.26415094339626" y="26.566037735849058" />' ]
QRPreview.tsx:205:41
Using SVG from backend with all styling applied QRPreview.tsx:208:37
[Auth] Adding Bearer token to request: /health layout.tsx:133:49
[Auth] Health check successful context.tsx:123:37
[Auth] Adding Bearer token to request: /health layout.tsx:133:49
QRPreview: Content key updated 
Object { qrType: "scooter_unlock", dataChanged: true, designChanged: true, logoChanged: true, useSvg: true }
QRPreview.tsx:50:21
QRPreview: Content key updated  
Object { qrType: "scooter_unlock", dataChanged: true, designChanged: true, logoChanged: true, useSvg: true }
dashboard:1:596
QRPreview: Content key updated 
Object { qrType: "scooter_unlock", dataChanged: true, designChanged: true, logoChanged: true, useSvg: true }
QRPreview.tsx:50:21
QRPreview: Content key updated  
Object { qrType: "scooter_unlock", dataChanged: true, designChanged: true, logoChanged: true, useSvg: true }
dashboard:1:596
QRPreview: Content key updated 
Object { qrType: "scooter_unlock", dataChanged: true, designChanged: true, logoChanged: true, useSvg: true }
QRPreview.tsx:50:21
QRPreview: Content key updated  
Object { qrType: "scooter_unlock", dataChanged: true, designChanged: true, logoChanged: true, useSvg: true }
dashboard:1:596
Frontend QR preview SVG request: 
Object { qrType: "scooter_unlock", dataSize: 161, designOptions: (35) […], hasLogo: true, dimensions: 300 }
qrService.modern.ts:376:21
Cleaned content for SVG preview: 
Object { scooter_id: "SCOOT123", unlock_code: "UNLOCK42", title: "Scooter_unlock QR Code", description: "Scan to view scooter_unlock content", service_provider: "Lime" }
qrService.modern.ts:396:21
[API] Request: POST /qr/preview/svg modernClient.ts:27:24
[API] Added auth token to request modernClient.ts:27:24
[API] Response: 200 OK - POST /qr/preview/svg modernClient.ts:27:24
Successfully received SVG QR preview qrService.modern.ts:429:25
QRPreview: Content key updated 
Object { qrType: "scooter_unlock", dataChanged: true, designChanged: true, logoChanged: true, useSvg: true }
QRPreview.tsx:50:21
QRPreview: Content key updated  
Object { qrType: "scooter_unlock", dataChanged: true, designChanged: true, logoChanged: true, useSvg: true }
dashboard:1:596
QRPreview: Generating basic SVG for frontend styling QRPreview.tsx:105:37
QRPreview: Processing logo: 
Object { logoType: "object", isFile: true, isDataUrl: false, logoLength: "N/A" }
QRPreview.tsx:108:37
QRPreview: Using File object for logo: LOGO - 1080 - WHITE-03-03.png 141655 QRPreview.tsx:116:41
QRPreview: Verifying data for scooter_unlock 
Object { scooter_id: "SCOOT123", unlock_code: "UNLOCK42", title: "Scooter_unlock QR Code", description: "Scan to view scooter_unlock content", service_provider: "Lime" }
QRPreview.tsx:236:17
QRPreview: Verified data 
Object { scooter_id: "SCOOT123", unlock_code: "UNLOCK42", title: "Scooter_unlock QR Code", description: "Scan to view scooter_unlock content", service_provider: "Lime" }
QRPreview.tsx:340:17
QRPreview: Sending to backend: 
Object { qrType: "scooter_unlock", dataKeys: (5) […], designOptionsKeys: (26) […], hasLogoFile: true, logoFileSize: 141655, logoFileName: "LOGO - 1080 - WHITE-03-03.png" }
QRPreview.tsx:137:37
Frontend QR preview SVG request: 
Object { qrType: "scooter_unlock", dataSize: 161, designOptions: (26) […], hasLogo: true, dimensions: 300 }
qrService.modern.ts:376:21
Cleaned content for SVG preview: 
Object { scooter_id: "SCOOT123", unlock_code: "UNLOCK42", title: "Scooter_unlock QR Code", description: "Scan to view scooter_unlock content", service_provider: "Lime" }
qrService.modern.ts:396:21
[API] Request: POST /qr/preview/svg modernClient.ts:27:24
[API] Added auth token to request modernClient.ts:27:24
[API] Response: 200 OK - POST /qr/preview/svg modernClient.ts:27:24
Successfully received SVG QR preview qrService.modern.ts:429:25
Raw SVG from backend: <svg baseProfile="full" height="340" preserveAspectRatio="xMidYMid meet" version="1.1" viewBox="0 0 340 340" width="340" xmlns="http://www.w3.org/2000/svg" xmlns:ev="http://www.w3.org/2001/xml-events" xmlns:xlink="http://www.w3.org/1999/xlink"><defs /><rect class="qr-bg" fill="#FFFFFF" height="340" id="qr-bg" width="340" x="0" y="0" /><rect class="qr-module" fill="#000000" height="5.471698113207547" id="module-13-0" width="5.471698113207547" x="91.13207547169812" y="20.0" /><rect class="qr-modul... QRPreview.tsx:155:37
SVG length: 206474 QRPreview.tsx:156:37
SVG contains <svg>: true QRPreview.tsx:157:37
Raw SVG structure: 
Object { rectCount: 1291, pathCount: 0 }
QRPreview.tsx:161:37
SVG analysis: 
Object { hasRect: true, hasPath: false, hasCircle: false, hasBackground: true, hasForeground: true, totalRects: 1291, largeRects: 7, finderPatternsDetected: true, originalDesignOptions: {…}, designOptionsKeys: (24) […] }
QRPreview.tsx:191:37
Large rectangles (potential finder patterns): 
Array(5) [ '<rect class="qr-bg" fill="#FFFFFF" height="340" id="qr-bg" width="340" x="0" y="0" />', '<rect class="qr-marker-outer" fill="#000000" height="38.301886792452834" width="38.301886792452834" x="20.0" y="20.0" />', '<rect class="qr-marker-inner" fill="#fff" height="25.169811320754715" width="25.169811320754715" x="26.566037735849058" y="26.566037735849058" />', '<rect class="qr-marker-outer" fill="#000000" height="38.301886792452834" width="38.301886792452834" x="271.6981132075472" y="20.0" />', '<rect class="qr-marker-inner" fill="#fff" height="25.169811320754715" width="25.169811320754715" x="278.26415094339626" y="26.566037735849058" />' ]
QRPreview.tsx:205:41
Using SVG from backend with all styling applied QRPreview.tsx:208:37
QRPreview: Content key updated 
Object { qrType: "scooter_unlock", dataChanged: true, designChanged: true, logoChanged: true, useSvg: true }
QRPreview.tsx:50:21
QRPreview: Content key updated  
Object { qrType: "scooter_unlock", dataChanged: true, designChanged: true, logoChanged: true, useSvg: true }
dashboard:1:596
Frontend QR preview SVG request: 
Object { qrType: "scooter_unlock", dataSize: 161, designOptions: (35) […], hasLogo: true, dimensions: 300 }
qrService.modern.ts:376:21
Cleaned content for SVG preview: 
Object { scooter_id: "SCOOT123", unlock_code: "UNLOCK42", title: "Scooter_unlock QR Code", description: "Scan to view scooter_unlock content", service_provider: "Lime" }
qrService.modern.ts:396:21
[API] Request: POST /qr/preview/svg modernClient.ts:27:24
[API] Added auth token to request modernClient.ts:27:24
[API] Response: 200 OK - POST /qr/preview/svg modernClient.ts:27:24
Successfully received SVG QR preview qrService.modern.ts:429:25
QRPreview: Content key updated 
Object { qrType: "scooter_unlock", dataChanged: true, designChanged: true, logoChanged: true, useSvg: true }
QRPreview.tsx:50:21
QRPreview: Content key updated  
Object { qrType: "scooter_unlock", dataChanged: true, designChanged: true, logoChanged: true, useSvg: true }
dashboard:1:596
QRPreview: Generating basic SVG for frontend styling QRPreview.tsx:105:37
QRPreview: Processing logo: 
Object { logoType: "object", isFile: true, isDataUrl: false, logoLength: "N/A" }
QRPreview.tsx:108:37
QRPreview: Using File object for logo: LOGO - 1080 - WHITE-03-03.png 141655 QRPreview.tsx:116:41
QRPreview: Verifying data for scooter_unlock 
Object { scooter_id: "SCOOT123", unlock_code: "UNLOCK42", title: "Scooter_unlock QR Code", description: "Scan to view scooter_unlock content", service_provider: "Lime" }
QRPreview.tsx:236:17
QRPreview: Verified data 
Object { scooter_id: "SCOOT123", unlock_code: "UNLOCK42", title: "Scooter_unlock QR Code", description: "Scan to view scooter_unlock content", service_provider: "Lime" }
QRPreview.tsx:340:17
QRPreview: Sending to backend: 
Object { qrType: "scooter_unlock", dataKeys: (5) […], designOptionsKeys: (26) […], hasLogoFile: true, logoFileSize: 141655, logoFileName: "LOGO - 1080 - WHITE-03-03.png" }
QRPreview.tsx:137:37
Frontend QR preview SVG request: 
Object { qrType: "scooter_unlock", dataSize: 161, designOptions: (26) […], hasLogo: true, dimensions: 300 }
qrService.modern.ts:376:21
Cleaned content for SVG preview: 
Object { scooter_id: "SCOOT123", unlock_code: "UNLOCK42", title: "Scooter_unlock QR Code", description: "Scan to view scooter_unlock content", service_provider: "Lime" }
qrService.modern.ts:396:21
[API] Request: POST /qr/preview/svg modernClient.ts:27:24
[API] Added auth token to request modernClient.ts:27:24
[API] Response: 200 OK - POST /qr/preview/svg modernClient.ts:27:24
Successfully received SVG QR preview qrService.modern.ts:429:25
Raw SVG from backend: <svg baseProfile="full" height="340" preserveAspectRatio="xMidYMid meet" version="1.1" viewBox="0 0 340 340" width="340" xmlns="http://www.w3.org/2000/svg" xmlns:ev="http://www.w3.org/2001/xml-events" xmlns:xlink="http://www.w3.org/1999/xlink"><defs /><rect class="qr-bg" fill="#FFFFFF" height="340" id="qr-bg" width="340" x="0" y="0" /><rect class="qr-module" fill="#000000" height="5.471698113207547" id="module-13-0" width="5.471698113207547" x="91.13207547169812" y="20.0" /><rect class="qr-modul... QRPreview.tsx:155:37
SVG length: 189807 QRPreview.tsx:156:37
SVG contains <svg>: true QRPreview.tsx:157:37
Raw SVG structure: 
Object { rectCount: 1185, pathCount: 0 }
QRPreview.tsx:161:37
SVG analysis: 
Object { hasRect: true, hasPath: false, hasCircle: false, hasBackground: true, hasForeground: true, totalRects: 1185, largeRects: 7, finderPatternsDetected: true, originalDesignOptions: {…}, designOptionsKeys: (24) […] }
QRPreview.tsx:191:37
Large rectangles (potential finder patterns): 
Array(5) [ '<rect class="qr-bg" fill="#FFFFFF" height="340" id="qr-bg" width="340" x="0" y="0" />', '<rect class="qr-marker-outer" fill="#000000" height="38.301886792452834" width="38.301886792452834" x="20.0" y="20.0" />', '<rect class="qr-marker-inner" fill="#fff" height="25.169811320754715" width="25.169811320754715" x="26.566037735849058" y="26.566037735849058" />', '<rect class="qr-marker-outer" fill="#000000" height="38.301886792452834" width="38.301886792452834" x="271.6981132075472" y="20.0" />', '<rect class="qr-marker-inner" fill="#fff" height="25.169811320754715" width="25.169811320754715" x="278.26415094339626" y="26.566037735849058" />' ]
QRPreview.tsx:205:41
Using SVG from backend with all styling applied QRPreview.tsx:208:37
🔄 handleNext called, current phase: design page.tsx:332:21
🔄 canProceed - design phase check: 
Object { qrType: "scooter_unlock", qrData: {…}, hasRequired: false, requiredFields: (3) […] }
page.tsx:240:25
🔄 canProceed(): false page.tsx:333:21
🔄 Current QR data: 
Object { scooter_id: "SCOOT123", unlock_code: "UNLOCK42", title: "Scooter_unlock QR Code", description: "Scan to view scooter_unlock content", service_provider: "Lime" }
page.tsx:334:21
🔄 Current design options: 
Object { size: 256, margin: 10, pattern: "square", marker_border_style: "square", marker_center_style: "square", logo_size: 0.3, logo_position: "center", logo_opacity: 1, logo_remove_background: true, frame_style: "none", … }
page.tsx:335:21
🔄 createdQR: null page.tsx:336:21
🔄 canProceed - design phase check: 
Object { qrType: "scooter_unlock", qrData: {…}, hasRequired: false, requiredFields: (3) […] }
page.tsx:240:25
🔄 Cannot proceed - canProceed() returned false page.tsx:338:25
[Auth] Adding Bearer token to request: /health layout.tsx:133:49
🔄 handleNext called, current phase: design page.tsx:332:21
🔄 canProceed - design phase check: 
Object { qrType: "scooter_unlock", qrData: {…}, hasRequired: false, requiredFields: (3) […] }
page.tsx:240:25
🔄 canProceed(): false page.tsx:333:21
🔄 Current QR data: 
Object { scooter_id: "SCOOT123", unlock_code: "UNLOCK42", title: "Scooter_unlock QR Code", description: "Scan to view scooter_unlock content", service_provider: "Lime" }
page.tsx:334:21
🔄 Current design options: 
Object { size: 256, margin: 10, pattern: "square", marker_border_style: "square", marker_center_style: "square", logo_size: 0.3, logo_position: "center", logo_opacity: 1, logo_remove_background: true, frame_style: "none", … }
page.tsx:335:21
🔄 createdQR: null page.tsx:336:21
🔄 canProceed - design phase check: 
Object { qrType: "scooter_unlock", qrData: {…}, hasRequired: false, requiredFields: (3) […] }
page.tsx:240:25
🔄 Cannot proceed - canProceed() returned false page.tsx:338:25
QRFormState: Field updated 
Object { field: "rate_info", previousValue: undefined, newValue: "$" }
useQRFormState.ts:66:29
QRFormState: Field updated 
Object { field: "rate_info", previousValue: undefined, newValue: "$" }
useQRFormState.ts:66:29
QRFormState: Notifying parent of changes (debounced) useQRFormState.ts:39:29
QRDataForm (scooter_unlock): Received data update: 
Object { scooter_id: "SCOOT123", unlock_code: "UNLOCK42", title: "Scooter_unlock QR Code", description: "Scan to view scooter_unlock content", service_provider: "Lime", rate_info: "$" }
QRDataForm.tsx:2100:21
QRDataForm (scooter_unlock): Updated form data: 
Object { scooter_id: "SCOOT123", unlock_code: "UNLOCK42", title: "Scooter_unlock QR Code", description: "Scan to view scooter_unlock content", service_provider: "Lime", rate_info: "$" }
QRDataForm.tsx:2133:29
QRDataForm (scooter_unlock): Updated form data: 
Object { scooter_id: "SCOOT123", unlock_code: "UNLOCK42", title: "Scooter_unlock QR Code", description: "Scan to view scooter_unlock content", service_provider: "Lime", rate_info: "$" }
QRDataForm.tsx:2133:29
QRFormState: Initialized with 
Object { hasInitialData: true, mergedDataKeys: (6) […] }
useQRFormState.ts:26:21
QRFormState: Initialized with  
Object { hasInitialData: true, mergedDataKeys: (6) […] }
dashboard:1:596
QRFormState: First render, notifying parent of initial state useQRFormState.ts:150:25
QRDataForm (scooter_unlock): Received data update: 
Object { scooter_id: "SCOOT123", unlock_code: "UNLOCK42", title: "Scooter_unlock QR Code", description: "Scan to view scooter_unlock content", service_provider: "Lime", rate_info: "$" }
QRDataForm.tsx:2100:21
QRFormState: External initialData changed, updating form state useQRFormState.ts:166:25
QRDataForm (scooter_unlock): Notifying parent of data change: 
Object { scooter_id: "SCOOT123", unlock_code: "UNLOCK42", title: "Scooter_unlock QR Code", description: "Scan to view scooter_unlock content", service_provider: "Lime", rate_info: "$" }
QRDataForm.tsx:2153:21
QR data updated: 
Object { scooter_id: "SCOOT123", unlock_code: "UNLOCK42", title: "Scooter_unlock QR Code", description: "Scan to view scooter_unlock content", service_provider: "Lime", rate_info: "$" }
page.tsx:487:21
QRFormState: External initialData changed, updating form state dashboard:1:596
QRDataForm (scooter_unlock): Updated form data: 
Object { scooter_id: "SCOOT123", unlock_code: "UNLOCK42", title: "Scooter_unlock QR Code", description: "Scan to view scooter_unlock content", service_provider: "Lime", rate_info: "$" }
QRDataForm.tsx:2133:29
QRDataForm (scooter_unlock): Updated form data: 
Object { scooter_id: "SCOOT123", unlock_code: "UNLOCK42", title: "Scooter_unlock QR Code", description: "Scan to view scooter_unlock content", service_provider: "Lime", rate_info: "$" }
QRDataForm.tsx:2133:29
QRPreview: Content key updated 
Object { qrType: "scooter_unlock", dataChanged: true, designChanged: true, logoChanged: true, useSvg: true }
QRPreview.tsx:50:21
QRPreview: Content key updated  
Object { qrType: "scooter_unlock", dataChanged: true, designChanged: true, logoChanged: true, useSvg: true }
dashboard:1:596
QRFormState: External initialData changed, updating form state useQRFormState.ts:166:25
QRDataForm (scooter_unlock): Notifying parent of data change: 
Object { scooter_id: "SCOOT123", unlock_code: "UNLOCK42", title: "Scooter_unlock QR Code", description: "Scan to view scooter_unlock content", service_provider: "Lime", rate_info: "$" }
QRDataForm.tsx:2153:21
QR data updated: 
Object { scooter_id: "SCOOT123", unlock_code: "UNLOCK42", title: "Scooter_unlock QR Code", description: "Scan to view scooter_unlock content", service_provider: "Lime", rate_info: "$" }
page.tsx:487:21
QRPreview: Content key updated 
Object { qrType: "scooter_unlock", dataChanged: true, designChanged: true, logoChanged: true, useSvg: true }
QRPreview.tsx:50:21
QRPreview: Content key updated  
Object { qrType: "scooter_unlock", dataChanged: true, designChanged: true, logoChanged: true, useSvg: true }
dashboard:1:596
Frontend QR preview SVG request: 
Object { qrType: "scooter_unlock", dataSize: 177, designOptions: (35) […], hasLogo: true, dimensions: 300 }
qrService.modern.ts:376:21
Cleaned content for SVG preview: 
Object { scooter_id: "SCOOT123", unlock_code: "UNLOCK42", title: "Scooter_unlock QR Code", description: "Scan to view scooter_unlock content", service_provider: "Lime", rate_info: "$" }
qrService.modern.ts:396:21
[API] Request: POST /qr/preview/svg modernClient.ts:27:24
[API] Added auth token to request modernClient.ts:27:24
[API] Response: 200 OK - POST /qr/preview/svg modernClient.ts:27:24
Successfully received SVG QR preview qrService.modern.ts:429:25
QRPreview: Content key updated 
Object { qrType: "scooter_unlock", dataChanged: true, designChanged: true, logoChanged: true, useSvg: true }
QRPreview.tsx:50:21
QRPreview: Content key updated  
Object { qrType: "scooter_unlock", dataChanged: true, designChanged: true, logoChanged: true, useSvg: true }
dashboard:1:596
QRPreview: Generating basic SVG for frontend styling QRPreview.tsx:105:37
QRPreview: Processing logo: 
Object { logoType: "object", isFile: true, isDataUrl: false, logoLength: "N/A" }
QRPreview.tsx:108:37
QRPreview: Using File object for logo: LOGO - 1080 - WHITE-03-03.png 141655 QRPreview.tsx:116:41
QRPreview: Verifying data for scooter_unlock 
Object { scooter_id: "SCOOT123", unlock_code: "UNLOCK42", title: "Scooter_unlock QR Code", description: "Scan to view scooter_unlock content", service_provider: "Lime", rate_info: "$" }
QRPreview.tsx:236:17
QRPreview: Verified data 
Object { scooter_id: "SCOOT123", unlock_code: "UNLOCK42", title: "Scooter_unlock QR Code", description: "Scan to view scooter_unlock content", service_provider: "Lime", rate_info: "$" }
QRPreview.tsx:340:17
QRPreview: Sending to backend: 
Object { qrType: "scooter_unlock", dataKeys: (6) […], designOptionsKeys: (26) […], hasLogoFile: true, logoFileSize: 141655, logoFileName: "LOGO - 1080 - WHITE-03-03.png" }
QRPreview.tsx:137:37
Frontend QR preview SVG request: 
Object { qrType: "scooter_unlock", dataSize: 177, designOptions: (26) […], hasLogo: true, dimensions: 300 }
qrService.modern.ts:376:21
Cleaned content for SVG preview: 
Object { scooter_id: "SCOOT123", unlock_code: "UNLOCK42", title: "Scooter_unlock QR Code", description: "Scan to view scooter_unlock content", service_provider: "Lime", rate_info: "$" }
qrService.modern.ts:396:21
[API] Request: POST /qr/preview/svg modernClient.ts:27:24
[API] Added auth token to request modernClient.ts:27:24
[API] Response: 200 OK - POST /qr/preview/svg modernClient.ts:27:24
Successfully received SVG QR preview qrService.modern.ts:429:25
Raw SVG from backend: <svg baseProfile="full" height="340" preserveAspectRatio="xMidYMid meet" version="1.1" viewBox="0 0 340 340" width="340" xmlns="http://www.w3.org/2000/svg" xmlns:ev="http://www.w3.org/2001/xml-events" xmlns:xlink="http://www.w3.org/1999/xlink"><defs /><rect class="qr-bg" fill="#FFFFFF" height="340" id="qr-bg" width="340" x="0" y="0" /><rect class="qr-module" fill="#000000" height="5.087719298245614" id="module-10-0" width="5.087719298245614" x="70.87719298245614" y="20.0" /><rect class="qr-modul... QRPreview.tsx:155:37
SVG length: 224878 QRPreview.tsx:156:37
SVG contains <svg>: true QRPreview.tsx:157:37
Raw SVG structure: 
Object { rectCount: 1408, pathCount: 0 }
QRPreview.tsx:161:37
SVG analysis: 
Object { hasRect: true, hasPath: false, hasCircle: false, hasBackground: true, hasForeground: true, totalRects: 1408, largeRects: 7, finderPatternsDetected: true, originalDesignOptions: {…}, designOptionsKeys: (24) […] }
QRPreview.tsx:191:37
Large rectangles (potential finder patterns): 
Array(5) [ '<rect class="qr-bg" fill="#FFFFFF" height="340" id="qr-bg" width="340" x="0" y="0" />', '<rect class="qr-marker-outer" fill="#000000" height="35.614035087719294" width="35.614035087719294" x="20.0" y="20.0" />', '<rect class="qr-marker-inner" fill="#fff" height="23.403508771929822" width="23.403508771929822" x="26.105263157894736" y="26.105263157894736" />', '<rect class="qr-marker-outer" fill="#000000" height="35.614035087719294" width="35.614035087719294" x="274.38596491228066" y="20.0" />', '<rect class="qr-marker-inner" fill="#fff" height="23.403508771929822" width="23.403508771929822" x="280.4912280701754" y="26.105263157894736" />' ]
QRPreview.tsx:205:41
Using SVG from backend with all styling applied QRPreview.tsx:208:37
QRFormState: Field updated 
Object { field: "rate_info", previousValue: "$", newValue: "$2" }
useQRFormState.ts:66:29
QRFormState: Field updated 
Object { field: "rate_info", previousValue: "$", newValue: "$2" }
useQRFormState.ts:66:29
QRFormState: Field updated 
Object { field: "rate_info", previousValue: "$2", newValue: "$20" }
useQRFormState.ts:66:29
QRFormState: Field updated 
Object { field: "rate_info", previousValue: "$2", newValue: "$20" }
useQRFormState.ts:66:29
QRFormState: Notifying parent of changes (debounced) useQRFormState.ts:39:29
QRDataForm (scooter_unlock): Received data update: 
Object { scooter_id: "SCOOT123", unlock_code: "UNLOCK42", title: "Scooter_unlock QR Code", description: "Scan to view scooter_unlock content", service_provider: "Lime", rate_info: "$20" }
QRDataForm.tsx:2100:21
QRDataForm (scooter_unlock): Updated form data: 
Object { scooter_id: "SCOOT123", unlock_code: "UNLOCK42", title: "Scooter_unlock QR Code", description: "Scan to view scooter_unlock content", service_provider: "Lime", rate_info: "$20" }
QRDataForm.tsx:2133:29
QRDataForm (scooter_unlock): Updated form data: 
Object { scooter_id: "SCOOT123", unlock_code: "UNLOCK42", title: "Scooter_unlock QR Code", description: "Scan to view scooter_unlock content", service_provider: "Lime", rate_info: "$20" }
QRDataForm.tsx:2133:29
QRFormState: External initialData changed, updating form state useQRFormState.ts:166:25
QRDataForm (scooter_unlock): Notifying parent of data change: 
Object { scooter_id: "SCOOT123", unlock_code: "UNLOCK42", title: "Scooter_unlock QR Code", description: "Scan to view scooter_unlock content", service_provider: "Lime", rate_info: "$20" }
QRDataForm.tsx:2153:21
QR data updated: 
Object { scooter_id: "SCOOT123", unlock_code: "UNLOCK42", title: "Scooter_unlock QR Code", description: "Scan to view scooter_unlock content", service_provider: "Lime", rate_info: "$20" }
page.tsx:487:21
QRPreview: Content key updated 
Object { qrType: "scooter_unlock", dataChanged: true, designChanged: true, logoChanged: true, useSvg: true }
QRPreview.tsx:50:21
QRPreview: Content key updated  
Object { qrType: "scooter_unlock", dataChanged: true, designChanged: true, logoChanged: true, useSvg: true }
dashboard:1:596
Frontend QR preview SVG request: 
Object { qrType: "scooter_unlock", dataSize: 179, designOptions: (35) […], hasLogo: true, dimensions: 300 }
qrService.modern.ts:376:21
Cleaned content for SVG preview: 
Object { scooter_id: "SCOOT123", unlock_code: "UNLOCK42", title: "Scooter_unlock QR Code", description: "Scan to view scooter_unlock content", service_provider: "Lime", rate_info: "$20" }
qrService.modern.ts:396:21
[API] Request: POST /qr/preview/svg modernClient.ts:27:24
[API] Added auth token to request modernClient.ts:27:24
QRPreview: Generating basic SVG for frontend styling QRPreview.tsx:105:37
QRPreview: Processing logo: 
Object { logoType: "object", isFile: true, isDataUrl: false, logoLength: "N/A" }
QRPreview.tsx:108:37
QRPreview: Using File object for logo: LOGO - 1080 - WHITE-03-03.png 141655 QRPreview.tsx:116:41
QRPreview: Verifying data for scooter_unlock 
Object { scooter_id: "SCOOT123", unlock_code: "UNLOCK42", title: "Scooter_unlock QR Code", description: "Scan to view scooter_unlock content", service_provider: "Lime", rate_info: "$20" }
QRPreview.tsx:236:17
QRPreview: Verified data 
Object { scooter_id: "SCOOT123", unlock_code: "UNLOCK42", title: "Scooter_unlock QR Code", description: "Scan to view scooter_unlock content", service_provider: "Lime", rate_info: "$20" }
QRPreview.tsx:340:17
QRPreview: Sending to backend: 
Object { qrType: "scooter_unlock", dataKeys: (6) […], designOptionsKeys: (26) […], hasLogoFile: true, logoFileSize: 141655, logoFileName: "LOGO - 1080 - WHITE-03-03.png" }
QRPreview.tsx:137:37
Frontend QR preview SVG request: 
Object { qrType: "scooter_unlock", dataSize: 179, designOptions: (26) […], hasLogo: true, dimensions: 300 }
qrService.modern.ts:376:21
Cleaned content for SVG preview: 
Object { scooter_id: "SCOOT123", unlock_code: "UNLOCK42", title: "Scooter_unlock QR Code", description: "Scan to view scooter_unlock content", service_provider: "Lime", rate_info: "$20" }
qrService.modern.ts:396:21
[API] Request: POST /qr/preview/svg modernClient.ts:27:24
[API] Added auth token to request modernClient.ts:27:24
[API] Response: 200 OK - POST /qr/preview/svg modernClient.ts:27:24
Successfully received SVG QR preview qrService.modern.ts:429:25
QRPreview: Content key updated 
Object { qrType: "scooter_unlock", dataChanged: true, designChanged: true, logoChanged: true, useSvg: true }
QRPreview.tsx:50:21
QRPreview: Content key updated  
Object { qrType: "scooter_unlock", dataChanged: true, designChanged: true, logoChanged: true, useSvg: true }
dashboard:1:596
QRPreview: Generating basic SVG for frontend styling QRPreview.tsx:105:37
QRPreview: Processing logo: 
Object { logoType: "object", isFile: true, isDataUrl: false, logoLength: "N/A" }
QRPreview.tsx:108:37
QRPreview: Using File object for logo: LOGO - 1080 - WHITE-03-03.png 141655 QRPreview.tsx:116:41
QRPreview: Verifying data for scooter_unlock 
Object { scooter_id: "SCOOT123", unlock_code: "UNLOCK42", title: "Scooter_unlock QR Code", description: "Scan to view scooter_unlock content", service_provider: "Lime", rate_info: "$20" }
QRPreview.tsx:236:17
QRPreview: Verified data 
Object { scooter_id: "SCOOT123", unlock_code: "UNLOCK42", title: "Scooter_unlock QR Code", description: "Scan to view scooter_unlock content", service_provider: "Lime", rate_info: "$20" }
QRPreview.tsx:340:17
QRPreview: Sending to backend: 
Object { qrType: "scooter_unlock", dataKeys: (6) […], designOptionsKeys: (26) […], hasLogoFile: true, logoFileSize: 141655, logoFileName: "LOGO - 1080 - WHITE-03-03.png" }
QRPreview.tsx:137:37
Frontend QR preview SVG request: 
Object { qrType: "scooter_unlock", dataSize: 179, designOptions: (26) […], hasLogo: true, dimensions: 300 }
qrService.modern.ts:376:21
Cleaned content for SVG preview: 
Object { scooter_id: "SCOOT123", unlock_code: "UNLOCK42", title: "Scooter_unlock QR Code", description: "Scan to view scooter_unlock content", service_provider: "Lime", rate_info: "$20" }
qrService.modern.ts:396:21
[API] Request: POST /qr/preview/svg modernClient.ts:27:24
[API] Added auth token to request modernClient.ts:27:24
[API] Response: 200 OK - POST /qr/preview/svg modernClient.ts:27:24
Successfully received SVG QR preview qrService.modern.ts:429:25
Raw SVG from backend: <svg baseProfile="full" height="340" preserveAspectRatio="xMidYMid meet" version="1.1" viewBox="0 0 340 340" width="340" xmlns="http://www.w3.org/2000/svg" xmlns:ev="http://www.w3.org/2001/xml-events" xmlns:xlink="http://www.w3.org/1999/xlink"><defs /><rect class="qr-bg" fill="#FFFFFF" height="340" id="qr-bg" width="340" x="0" y="0" /><rect class="qr-module" fill="#000000" height="5.087719298245614" id="module-8-0" width="5.087719298245614" x="60.70175438596491" y="20.0" /><rect class="qr-module... QRPreview.tsx:155:37
SVG length: 228237 QRPreview.tsx:156:37
SVG contains <svg>: true QRPreview.tsx:157:37
Raw SVG structure: 
Object { rectCount: 1428, pathCount: 0 }
QRPreview.tsx:161:37
SVG analysis: 
Object { hasRect: true, hasPath: false, hasCircle: false, hasBackground: true, hasForeground: true, totalRects: 1428, largeRects: 7, finderPatternsDetected: true, originalDesignOptions: {…}, designOptionsKeys: (24) […] }
QRPreview.tsx:191:37
Large rectangles (potential finder patterns): 
Array(5) [ '<rect class="qr-bg" fill="#FFFFFF" height="340" id="qr-bg" width="340" x="0" y="0" />', '<rect class="qr-marker-outer" fill="#000000" height="35.614035087719294" width="35.614035087719294" x="20.0" y="20.0" />', '<rect class="qr-marker-inner" fill="#fff" height="23.403508771929822" width="23.403508771929822" x="26.105263157894736" y="26.105263157894736" />', '<rect class="qr-marker-outer" fill="#000000" height="35.614035087719294" width="35.614035087719294" x="274.38596491228066" y="20.0" />', '<rect class="qr-marker-inner" fill="#fff" height="23.403508771929822" width="23.403508771929822" x="280.4912280701754" y="26.105263157894736" />' ]
QRPreview.tsx:205:41
Using SVG from backend with all styling applied QRPreview.tsx:208:37
[API] Response: 200 OK - POST /qr/preview/svg modernClient.ts:27:24
Successfully received SVG QR preview qrService.modern.ts:429:25
Raw SVG from backend: <svg baseProfile="full" height="340" preserveAspectRatio="xMidYMid meet" version="1.1" viewBox="0 0 340 340" width="340" xmlns="http://www.w3.org/2000/svg" xmlns:ev="http://www.w3.org/2001/xml-events" xmlns:xlink="http://www.w3.org/1999/xlink"><defs /><rect class="qr-bg" fill="#FFFFFF" height="340" id="qr-bg" width="340" x="0" y="0" /><rect class="qr-module" fill="#000000" height="5.087719298245614" id="module-8-0" width="5.087719298245614" x="60.70175438596491" y="20.0" /><rect class="qr-module... QRPreview.tsx:155:37
SVG length: 228237 QRPreview.tsx:156:37
SVG contains <svg>: true QRPreview.tsx:157:37
Raw SVG structure: 
Object { rectCount: 1428, pathCount: 0 }
QRPreview.tsx:161:37
SVG analysis: 
Object { hasRect: true, hasPath: false, hasCircle: false, hasBackground: true, hasForeground: true, totalRects: 1428, largeRects: 7, finderPatternsDetected: true, originalDesignOptions: {…}, designOptionsKeys: (24) […] }
QRPreview.tsx:191:37
Large rectangles (potential finder patterns): 
Array(5) [ '<rect class="qr-bg" fill="#FFFFFF" height="340" id="qr-bg" width="340" x="0" y="0" />', '<rect class="qr-marker-outer" fill="#000000" height="35.614035087719294" width="35.614035087719294" x="20.0" y="20.0" />', '<rect class="qr-marker-inner" fill="#fff" height="23.403508771929822" width="23.403508771929822" x="26.105263157894736" y="26.105263157894736" />', '<rect class="qr-marker-outer" fill="#000000" height="35.614035087719294" width="35.614035087719294" x="274.38596491228066" y="20.0" />', '<rect class="qr-marker-inner" fill="#fff" height="23.403508771929822" width="23.403508771929822" x="280.4912280701754" y="26.105263157894736" />' ]
QRPreview.tsx:205:41
Using SVG from backend with all styling applied QRPreview.tsx:208:37
🔄 handleNext called, current phase: design page.tsx:332:21
🔄 canProceed - design phase check: 
Object { qrType: "scooter_unlock", qrData: {…}, hasRequired: true, requiredFields: (3) […] }
page.tsx:240:25
🔄 canProceed(): true page.tsx:333:21
🔄 Current QR data: 
Object { scooter_id: "SCOOT123", unlock_code: "UNLOCK42", title: "Scooter_unlock QR Code", description: "Scan to view scooter_unlock content", service_provider: "Lime", rate_info: "$20" }
page.tsx:334:21
🔄 Current design options: 
Object { size: 256, margin: 10, pattern: "square", marker_border_style: "square", marker_center_style: "square", logo_size: 0.3, logo_position: "center", logo_opacity: 1, logo_remove_background: true, frame_style: "none", … }
page.tsx:335:21
🔄 createdQR: null page.tsx:336:21
🔄 canProceed - design phase check: 
Object { qrType: "scooter_unlock", qrData: {…}, hasRequired: true, requiredFields: (3) […] }
page.tsx:240:25
🔄 In design phase - checking if QR already created page.tsx:351:25
🔄 Creating QR code... page.tsx:357:29
🔄 canProceed - design phase check: 
Object { qrType: "scooter_unlock", qrData: {…}, hasRequired: true, requiredFields: (3) […] }
page.tsx:240:25
Creating QR code with data: 
Object { qr_type: "scooter_unlock", name: "Scooter Unlock QR - 12/06/2025", description: "Scan to view scooter_unlock content", data: {…}, design_options: {…}, options: {…} }
page.tsx:275:25
Design options being sent: 
Object { size: 256, margin: 10, pattern: "square", marker_border_style: "square", marker_center_style: "square", logo_size: 0.3, logo_position: "center", logo_opacity: 1, logo_remove_background: true, frame_style: "none", … }
page.tsx:276:25
Logo remove background setting: true page.tsx:277:25
QRPreview: Content key updated 
Object { qrType: "scooter_unlock", dataChanged: true, designChanged: true, logoChanged: true, useSvg: true }
QRPreview.tsx:50:21
QRPreview: Content key updated  
Object { qrType: "scooter_unlock", dataChanged: true, designChanged: true, logoChanged: true, useSvg: true }
dashboard:1:596
[API] Request: POST /qr modernClient.ts:27:24
[API] Added auth token to request modernClient.ts:27:24
[API Error] API Error: 
Object { status: 405, message: "Method Not Allowed", isNetworkError: false }
intercept-console-error.js:50:32
Error in Create QR code: 
Object { status: 405, message: "Method Not Allowed", isNetworkError: false }
intercept-console-error.js:50:32
Error creating QR code: Error: Invalid response from QR creation service
    handleCreateQR webpack-internal:///(app-pages-browser)/./app/(user)/dashboard/qr/creator/page.tsx:296
intercept-console-error.js:50:32
QRPreview: Content key updated 
Object { qrType: "scooter_unlock", dataChanged: true, designChanged: true, logoChanged: true, useSvg: true }
QRPreview.tsx:50:21
QRPreview: Content key updated  
Object { qrType: "scooter_unlock", dataChanged: true, designChanged: true, logoChanged: true, useSvg: true }
dashboard:1:596
[DashboardLayout] Checking token: Present (324 chars) layout.tsx:89:29
[DashboardLayout] Valid token found on initial check layout.tsx:96:25
[DashboardLayout] Setting up auth headers layout.tsx:101:29
[Auth] Adding Bearer token to request: /__nextjs_original-stack-frames layout.tsx:133:49
[Auth] Adding Bearer token to request: /health layout.tsx:133:49
[Auth] Adding Bearer token to request: /__nextjs_original-stack-frames 2 layout.tsx:133:49
[Auth] Health check successful context.tsx:123:37
Frontend QR preview SVG request: 
Object { qrType: "scooter_unlock", dataSize: 179, designOptions: (35) […], hasLogo: true, dimensions: 300 }
qrService.modern.ts:376:21
Cleaned content for SVG preview: 
Object { scooter_id: "SCOOT123", unlock_code: "UNLOCK42", title: "Scooter_unlock QR Code", description: "Scan to view scooter_unlock content", service_provider: "Lime", rate_info: "$20" }
qrService.modern.ts:396:21
[API] Request: POST /qr/preview/svg modernClient.ts:27:24
[API] Added auth token to request modernClient.ts:27:24
QRPreview: Generating basic SVG for frontend styling QRPreview.tsx:105:37
QRPreview: Processing logo: 
Object { logoType: "object", isFile: true, isDataUrl: false, logoLength: "N/A" }
QRPreview.tsx:108:37
QRPreview: Using File object for logo: LOGO - 1080 - WHITE-03-03.png 141655 QRPreview.tsx:116:41
QRPreview: Verifying data for scooter_unlock 
Object { scooter_id: "SCOOT123", unlock_code: "UNLOCK42", title: "Scooter_unlock QR Code", description: "Scan to view scooter_unlock content", service_provider: "Lime", rate_info: "$20" }
QRPreview.tsx:236:17
QRPreview: Verified data 
Object { scooter_id: "SCOOT123", unlock_code: "UNLOCK42", title: "Scooter_unlock QR Code", description: "Scan to view scooter_unlock content", service_provider: "Lime", rate_info: "$20" }
QRPreview.tsx:340:17
QRPreview: Sending to backend: 
Object { qrType: "scooter_unlock", dataKeys: (6) […], designOptionsKeys: (26) […], hasLogoFile: true, logoFileSize: 141655, logoFileName: "LOGO - 1080 - WHITE-03-03.png" }
QRPreview.tsx:137:37
Frontend QR preview SVG request: 
Object { qrType: "scooter_unlock", dataSize: 179, designOptions: (26) […], hasLogo: true, dimensions: 300 }
qrService.modern.ts:376:21
Cleaned content for SVG preview: 
Object { scooter_id: "SCOOT123", unlock_code: "UNLOCK42", title: "Scooter_unlock QR Code", description: "Scan to view scooter_unlock content", service_provider: "Lime", rate_info: "$20" }
qrService.modern.ts:396:21
[API] Request: POST /qr/preview/svg modernClient.ts:27:24
[API] Added auth token to request modernClient.ts:27:24
[API] Response: 200 OK - POST /qr/preview/svg modernClient.ts:27:24
Successfully received SVG QR preview qrService.modern.ts:429:25
[API] Response: 200 OK - POST /qr/preview/svg modernClient.ts:27:24
Successfully received SVG QR preview qrService.modern.ts:429:25
Raw SVG from backend: <svg baseProfile="full" height="340" preserveAspectRatio="xMidYMid meet" version="1.1" viewBox="0 0 340 340" width="340" xmlns="http://www.w3.org/2000/svg" xmlns:ev="http://www.w3.org/2001/xml-events" xmlns:xlink="http://www.w3.org/1999/xlink"><defs /><rect class="qr-bg" fill="#FFFFFF" height="340" id="qr-bg" width="340" x="0" y="0" /><rect class="qr-module" fill="#000000" height="5.087719298245614" id="module-8-0" width="5.087719298245614" x="60.70175438596491" y="20.0" /><rect class="qr-module... QRPreview.tsx:155:37
SVG length: 228237 QRPreview.tsx:156:37
SVG contains <svg>: true QRPreview.tsx:157:37
Raw SVG structure: 
Object { rectCount: 1428, pathCount: 0 }
QRPreview.tsx:161:37
SVG analysis: 
Object { hasRect: true, hasPath: false, hasCircle: false, hasBackground: true, hasForeground: true, totalRects: 1428, largeRects: 7, finderPatternsDetected: true, originalDesignOptions: {…}, designOptionsKeys: (24) […] }
QRPreview.tsx:191:37
Large rectangles (potential finder patterns): 
Array(5) [ '<rect class="qr-bg" fill="#FFFFFF" height="340" id="qr-bg" width="340" x="0" y="0" />', '<rect class="qr-marker-outer" fill="#000000" height="35.614035087719294" width="35.614035087719294" x="20.0" y="20.0" />', '<rect class="qr-marker-inner" fill="#fff" height="23.403508771929822" width="23.403508771929822" x="26.105263157894736" y="26.105263157894736" />', '<rect class="qr-marker-outer" fill="#000000" height="35.614035087719294" width="35.614035087719294" x="274.38596491228066" y="20.0" />', '<rect class="qr-marker-inner" fill="#fff" height="23.403508771929822" width="23.403508771929822" x="280.4912280701754" y="26.105263157894736" />' ]
QRPreview.tsx:205:41
Using SVG from backend with all styling applied QRPreview.tsx:208:37
[Auth] Adding Bearer token to request: /health layout.tsx:133:49
🔄 handleNext called, current phase: design page.tsx:332:21
🔄 canProceed - design phase check: 
Object { qrType: "scooter_unlock", qrData: {…}, hasRequired: true, requiredFields: (3) […] }
page.tsx:240:25
🔄 canProceed(): true page.tsx:333:21
🔄 Current QR data: 
Object { scooter_id: "SCOOT123", unlock_code: "UNLOCK42", title: "Scooter_unlock QR Code", description: "Scan to view scooter_unlock content", service_provider: "Lime", rate_info: "$20" }
page.tsx:334:21
🔄 Current design options: 
Object { size: 256, margin: 10, pattern: "square", marker_border_style: "square", marker_center_style: "square", logo_size: 0.3, logo_position: "center", logo_opacity: 1, logo_remove_background: true, frame_style: "none", … }
page.tsx:335:21
🔄 createdQR: null page.tsx:336:21
🔄 canProceed - design phase check: 
Object { qrType: "scooter_unlock", qrData: {…}, hasRequired: true, requiredFields: (3) […] }
page.tsx:240:25
🔄 In design phase - checking if QR already created page.tsx:351:25
🔄 Creating QR code... page.tsx:357:29
🔄 canProceed - design phase check: 
Object { qrType: "scooter_unlock", qrData: {…}, hasRequired: true, requiredFields: (3) […] }
page.tsx:240:25
Creating QR code with data: 
Object { qr_type: "scooter_unlock", name: "Scooter Unlock QR - 12/06/2025", description: "Scan to view scooter_unlock content", data: {…}, design_options: {…}, options: {…} }
page.tsx:275:25
Design options being sent: 
Object { size: 256, margin: 10, pattern: "square", marker_border_style: "square", marker_center_style: "square", logo_size: 0.3, logo_position: "center", logo_opacity: 1, logo_remove_background: true, frame_style: "none", … }
page.tsx:276:25
Logo remove background setting: true page.tsx:277:25
QRPreview: Content key updated 
Object { qrType: "scooter_unlock", dataChanged: true, designChanged: true, logoChanged: true, useSvg: true }
QRPreview.tsx:50:21
QRPreview: Content key updated  
Object { qrType: "scooter_unlock", dataChanged: true, designChanged: true, logoChanged: true, useSvg: true }
dashboard:1:596
[API] Request: POST /qr modernClient.ts:27:24
[API] Added auth token to request modernClient.ts:27:24
[API Error] API Error: 
Object { status: 405, message: "Method Not Allowed", isNetworkError: false }
intercept-console-error.js:50:32
Error in Create QR code: 
Object { status: 405, message: "Method Not Allowed", isNetworkError: false }
intercept-console-error.js:50:32
Error creating QR code: Error: Invalid response from QR creation service
    handleCreateQR webpack-internal:///(app-pages-browser)/./app/(user)/dashboard/qr/creator/page.tsx:296
intercept-console-error.js:50:32
QRPreview: Content key updated 
Object { qrType: "scooter_unlock", dataChanged: true, designChanged: true, logoChanged: true, useSvg: true }
QRPreview.tsx:50:21
QRPreview: Content key updated  
Object { qrType: "scooter_unlock", dataChanged: true, designChanged: true, logoChanged: true, useSvg: true }
dashboard:1:596
[DashboardLayout] Checking token: Present (324 chars) layout.tsx:89:29
[DashboardLayout] Valid token found on initial check layout.tsx:96:25
[DashboardLayout] Setting up auth headers layout.tsx:101:29
[Auth] Adding Bearer token to request: /__nextjs_original-stack-frames 3 layout.tsx:133:49
Frontend QR preview SVG request: 
Object { qrType: "scooter_unlock", dataSize: 179, designOptions: (35) […], hasLogo: true, dimensions: 300 }
qrService.modern.ts:376:21
Cleaned content for SVG preview: 
Object { scooter_id: "SCOOT123", unlock_code: "UNLOCK42", title: "Scooter_unlock QR Code", description: "Scan to view scooter_unlock content", service_provider: "Lime", rate_info: "$20" }
qrService.modern.ts:396:21
[API] Request: POST /qr/preview/svg modernClient.ts:27:24
[API] Added auth token to request modernClient.ts:27:24
[API] Response: 200 OK - POST /qr/preview/svg modernClient.ts:27:24
Successfully received SVG QR preview qrService.modern.ts:429:25
QRPreview: Generating basic SVG for frontend styling QRPreview.tsx:105:37
QRPreview: Processing logo: 
Object { logoType: "object", isFile: true, isDataUrl: false, logoLength: "N/A" }
QRPreview.tsx:108:37
QRPreview: Using File object for logo: LOGO - 1080 - WHITE-03-03.png 141655 QRPreview.tsx:116:41
QRPreview: Verifying data for scooter_unlock 
Object { scooter_id: "SCOOT123", unlock_code: "UNLOCK42", title: "Scooter_unlock QR Code", description: "Scan to view scooter_unlock content", service_provider: "Lime", rate_info: "$20" }
QRPreview.tsx:236:17
QRPreview: Verified data 
Object { scooter_id: "SCOOT123", unlock_code: "UNLOCK42", title: "Scooter_unlock QR Code", description: "Scan to view scooter_unlock content", service_provider: "Lime", rate_info: "$20" }
QRPreview.tsx:340:17
QRPreview: Sending to backend: 
Object { qrType: "scooter_unlock", dataKeys: (6) […], designOptionsKeys: (26) […], hasLogoFile: true, logoFileSize: 141655, logoFileName: "LOGO - 1080 - WHITE-03-03.png" }
QRPreview.tsx:137:37
Frontend QR preview SVG request: 
Object { qrType: "scooter_unlock", dataSize: 179, designOptions: (26) […], hasLogo: true, dimensions: 300 }
qrService.modern.ts:376:21
Cleaned content for SVG preview: 
Object { scooter_id: "SCOOT123", unlock_code: "UNLOCK42", title: "Scooter_unlock QR Code", description: "Scan to view scooter_unlock content", service_provider: "Lime", rate_info: "$20" }
qrService.modern.ts:396:21
[API] Request: POST /qr/preview/svg modernClient.ts:27:24
[API] Added auth token to request modernClient.ts:27:24
[API] Response: 200 OK - POST /qr/preview/svg modernClient.ts:27:24
Successfully received SVG QR preview qrService.modern.ts:429:25
Raw SVG from backend: <svg baseProfile="full" height="340" preserveAspectRatio="xMidYMid meet" version="1.1" viewBox="0 0 340 340" width="340" xmlns="http://www.w3.org/2000/svg" xmlns:ev="http://www.w3.org/2001/xml-events" xmlns:xlink="http://www.w3.org/1999/xlink"><defs /><rect class="qr-bg" fill="#FFFFFF" height="340" id="qr-bg" width="340" x="0" y="0" /><rect class="qr-module" fill="#000000" height="5.087719298245614" id="module-8-0" width="5.087719298245614" x="60.70175438596491" y="20.0" /><rect class="qr-module... QRPreview.tsx:155:37
SVG length: 228237 QRPreview.tsx:156:37
SVG contains <svg>: true QRPreview.tsx:157:37
Raw SVG structure: 
Object { rectCount: 1428, pathCount: 0 }
QRPreview.tsx:161:37
SVG analysis: 
Object { hasRect: true, hasPath: false, hasCircle: false, hasBackground: true, hasForeground: true, totalRects: 1428, largeRects: 7, finderPatternsDetected: true, originalDesignOptions: {…}, designOptionsKeys: (24) […] }
QRPreview.tsx:191:37
Large rectangles (potential finder patterns): 
Array(5) [ '<rect class="qr-bg" fill="#FFFFFF" height="340" id="qr-bg" width="340" x="0" y="0" />', '<rect class="qr-marker-outer" fill="#000000" height="35.614035087719294" width="35.614035087719294" x="20.0" y="20.0" />', '<rect class="qr-marker-inner" fill="#fff" height="23.403508771929822" width="23.403508771929822" x="26.105263157894736" y="26.105263157894736" />', '<rect class="qr-marker-outer" fill="#000000" height="35.614035087719294" width="35.614035087719294" x="274.38596491228066" y="20.0" />', '<rect class="qr-marker-inner" fill="#fff" height="23.403508771929822" width="23.403508771929822" x="280.4912280701754" y="26.105263157894736" />' ]
QRPreview.tsx:205:41
Using SVG from backend with all styling applied QRPreview.tsx:208:37
[Fast Refresh] rebuilding hot-reloader-client.js:197:29
