// qrDesignNormalization.ts
// Utility to normalize QRDesignOptions to canonical snake_case for API/backend compatibility.
// Only snake_case fields will be present in the normalized object.

import type { QRDesignOptions } from '@/types/qr';

/**
 * Converts a QRDesignOptions object (possibly containing legacy/camelCase fields)
 * into a canonical object with only backend-accepted snake_case fields.
 * 
 * @param options The design options to normalize
 * @param forSvg Optional flag to include SVG-specific optimizations
 */
export function normalizeQRDesignOptions(
  options: Partial<QRDesignOptions>, 
  forSvg = false
): Record<string, any> {
  // Accept both camelCase and snake_case for legacy support, output only snake_case
  const normalized = {
    // Core design
    size: options.size,
    margin: options.margin,
    pattern: options.pattern,
    marker_border_style: options.marker_border_style,
    marker_border_color: options.marker_border_color,
    marker_center_style: options.marker_center_style,
    marker_center_color: options.marker_center_color,
    custom_marker_color: options.custom_marker_color,
    different_markers_colors: options.different_markers_colors,
    logo_url: options.logo_url,
    logo_size: options.logo_size,
    logo_position: options.logo_position,
    logo_opacity: options.logo_opacity,
    frame_style: options.frame_style,
    frame_color: options.frame_color,
    frame_text: options.frame_text,
    frame_thickness: options.frame_thickness,
    animation_type: options.animation_type,
    animation_speed: options.animation_speed,
    animation_loop: options.animation_loop,
    overlay_url: options.overlay_url,
    overlay_blend_mode: options.overlay_blend_mode,
    color_mode: options.color_mode,
    error_correction: options.error_correction,
    ai_enhance: options.ai_enhance,
    ai_enhance_options: options.ai_enhance_options,
    corner_radius: options.corner_radius,
   
    // Canonical color fields
    foreground_color: options.foreground_color,
    background_color: options.background_color,
    
    // SVG-specific optimizations
    svg_render_dpi: forSvg ? options.svg_render_dpi || 300 : undefined,
    svg_optimize: forSvg ? true : undefined,
  };
  
  // Remove undefined values
  return Object.fromEntries(
    Object.entries(normalized).filter(([key, value]) => {
      if (key === 'svg_render_dpi' || key === 'svg_optimize') {
        return forSvg && value !== undefined;
      }
      return value !== undefined;
    })
  );
}
