// SVG Styler for QRVibe
// This utility applies styling to QR code SVGs directly in the browser

import type { QRDesignOptions } from '@/types/qr';

/**
 * Applies styling to a QR code SVG based on design options
 * 
 * @param svg Original SVG string from the backend
 * @param designOptions Design options to apply
 * @returns Styled SVG string
 */
export function applySvgStyling(svg: string, designOptions: Partial<QRDesignOptions>): string {
  // If no SVG or it's already styled, return as is
  if (!svg || !svg.includes('<svg')) {
    return svg;
  }

  // Parse the SVG as a DOM element to manipulate it
  const parser = new DOMParser();
  const doc = parser.parseFromString(svg, 'image/svg+xml');
  const svgElement = doc.documentElement as unknown as SVGSVGElement;

  // Debug: Log SVG parsing
  console.log('SVG Styler: Parsing SVG, element count:', svgElement.children.length);
  console.log('SVG Styler: Design options:', designOptions);

  // If no design options are provided or they're empty, return the original SVG
  // This ensures we show a proper black-and-white QR code by default
  if (!designOptions || Object.keys(designOptions).length === 0) {
    console.log('SVG Styler: No design options provided, returning original SVG');
    return svg;
  }
  
  // Apply background color only if explicitly specified
  if (designOptions.background_color) {
    const backgroundColor = designOptions.background_color;

    // Find or create a background rectangle
    let bgRect = svgElement.querySelector('rect.qr-background') as unknown as SVGRectElement;
    if (!bgRect) {
      bgRect = document.createElementNS('http://www.w3.org/2000/svg', 'rect') as SVGRectElement;
      bgRect.setAttribute('class', 'qr-background');
      bgRect.setAttribute('x', '0');
      bgRect.setAttribute('y', '0');
      bgRect.setAttribute('width', '100%');
      bgRect.setAttribute('height', '100%');
      svgElement.insertBefore(bgRect, svgElement.firstChild);
    }
    bgRect.setAttribute('fill', backgroundColor);
    console.log('SVG Styler: Applied background color:', backgroundColor);
  } else {
    console.log('SVG Styler: Using default background color');
  }
  
  // Apply foreground color only if explicitly specified
  if (designOptions.foreground_color) {
    const foregroundColor = designOptions.foreground_color;

    // Select all QR modules (squares, circles, etc.) - but exclude the background
    const modules = svgElement.querySelectorAll('rect:not(.qr-background), circle, path');
    console.log('SVG Styler: Found', modules.length, 'QR modules to style with foreground color');

    modules.forEach((module, index) => {
      const currentFill = module.getAttribute('fill');
      const tagName = module.tagName.toLowerCase();

      // Only style QR modules, not logo or background elements
      // Skip large circles that might be logo backgrounds
      if (tagName === 'circle') {
        const radius = parseFloat(module.getAttribute('r') || '0');
        const cx = parseFloat(module.getAttribute('cx') || '0');
        const cy = parseFloat(module.getAttribute('cy') || '0');

        // Skip large centered circles (likely logo backgrounds)
        if (radius > 50 && cx > 100 && cx < 200 && cy > 100 && cy < 200) {
          console.log(`SVG Styler: Skipping large centered circle (likely logo background) at (${cx}, ${cy}) with radius ${radius}`);
          return;
        }
      }

      // Apply foreground color to QR modules
      if (!currentFill ||
          currentFill === '#000000' ||
          currentFill === '#000' ||
          currentFill === 'black' ||
          currentFill === 'none') {
        module.setAttribute('fill', foregroundColor);
        if (index < 5) { // Log first few for debugging
          console.log(`SVG Styler: Module ${index} (${tagName}) - changed fill from "${currentFill}" to "${foregroundColor}"`);
        }
      }
    });
  } else {
    console.log('SVG Styler: Using default foreground color');
  }
  
  // Apply corner styling if specified
  if (typeof designOptions.corner_radius === 'number' && designOptions.corner_radius > 0) {
    const modules = svgElement.querySelectorAll('rect:not(.qr-background)');
    modules.forEach(module => {
      const size = parseFloat(module.getAttribute('width') || '0');
      const radius = Math.min(designOptions.corner_radius || 0, size / 2);
      module.setAttribute('rx', radius.toString());
      module.setAttribute('ry', radius.toString());
    });
  }
  
  // Apply pattern styling only if explicitly specified and different from default
  if (designOptions.pattern &&
      designOptions.pattern !== 'square' &&
      designOptions.pattern !== 'classic' &&
      designOptions.pattern !== 'default') {
    console.log('SVG Styler: Applying pattern styling:', designOptions.pattern);
    applyPatternStyling(svgElement, designOptions);
  } else {
    console.log('SVG Styler: Using default square pattern');
  }
  
  // Apply styling to finder patterns (eyes) if specified
  if (designOptions.marker_border_color || designOptions.marker_center_color) {
    const finderPatterns = findFinderPatterns(svgElement);
    
    finderPatterns.forEach(pattern => {
      const { border, center } = pattern;
      
      if (border && designOptions.marker_border_color) {
        border.setAttribute('fill', designOptions.marker_border_color);
      }
      
      if (center && designOptions.marker_center_color) {
        center.setAttribute('fill', designOptions.marker_center_color);
      }
      
      // Apply marker border style if specified
      if (border && designOptions.marker_border_style) {
        applyMarkerStyle(border, designOptions.marker_border_style);
      }
      
      // Apply marker center style if specified
      if (center && designOptions.marker_center_style) {
        applyMarkerStyle(center, designOptions.marker_center_style);
      }
    });
  }

  // Apply frame if specified
  if (designOptions.frame_style && designOptions.frame_style !== 'none') {
    applyFrame(svgElement, designOptions);
  }

  // Serialize back to string
  const result = new XMLSerializer().serializeToString(doc);
  console.log('SVG Styler: Final SVG length:', result.length);
  return result;
}

/**
 * Applies styling to marker elements based on style name
 */
function applyMarkerStyle(element: Element, style: string): void {
  switch (style) {
    case 'rounded':
      const width = parseFloat(element.getAttribute('width') || '0');
      element.setAttribute('rx', (width * 0.2).toString());
      element.setAttribute('ry', (width * 0.2).toString());
      break;
      
    case 'circle':
      const x = parseFloat(element.getAttribute('x') || '0');
      const y = parseFloat(element.getAttribute('y') || '0');
      const size = parseFloat(element.getAttribute('width') || '0');
      const fill = element.getAttribute('fill') || '#000000';
      
      // Create a circle to replace the rectangle
      const circle = document.createElementNS('http://www.w3.org/2000/svg', 'circle') as SVGCircleElement;
      circle.setAttribute('cx', (x + size/2).toString());
      circle.setAttribute('cy', (y + size/2).toString());
      circle.setAttribute('r', (size/2).toString());
      circle.setAttribute('fill', fill);
      
      // Replace the rectangle with the circle
      element.parentNode?.replaceChild(circle, element);
      break;
      
    case 'diamond':
      const rectX = parseFloat(element.getAttribute('x') || '0');
      const rectY = parseFloat(element.getAttribute('y') || '0');
      const rectSize = parseFloat(element.getAttribute('width') || '0');
      const rectFill = element.getAttribute('fill') || '#000000';
      
      // Create a diamond shape using a polygon
      const diamond = document.createElementNS('http://www.w3.org/2000/svg', 'polygon') as SVGPolygonElement;
      const centerX = rectX + rectSize/2;
      const centerY = rectY + rectSize/2;
      
      const points = [
        `${centerX},${rectY}`,
        `${rectX + rectSize},${centerY}`,
        `${centerX},${rectY + rectSize}`,
        `${rectX},${centerY}`
      ].join(' ');
      
      diamond.setAttribute('points', points);
      diamond.setAttribute('fill', rectFill);
      
      // Replace the rectangle with the diamond
      element.parentNode?.replaceChild(diamond, element);
      break;
  }
}

/**
 * Adds a frame around the QR code
 */
function applyFrame(svgElement: SVGSVGElement, designOptions: Partial<QRDesignOptions>): void {
  // Get SVG dimensions
  const width = parseFloat(svgElement.getAttribute('width') || '200');
  const height = parseFloat(svgElement.getAttribute('height') || '200');
  
  // Create frame group
  const frameGroup = document.createElementNS('http://www.w3.org/2000/svg', 'g') as SVGGElement;
  frameGroup.setAttribute('class', 'qr-frame');
  
  // Determine frame thickness
  const thickness = designOptions.frame_thickness || 10;
  const frameColor = designOptions.frame_color || '#000000';
  
  // Create frame rectangle
  const frameRect = document.createElementNS('http://www.w3.org/2000/svg', 'rect') as SVGRectElement;
  frameRect.setAttribute('x', '0');
  frameRect.setAttribute('y', '0');
  frameRect.setAttribute('width', width.toString());
  frameRect.setAttribute('height', height.toString());
  frameRect.setAttribute('fill', 'none');
  frameRect.setAttribute('stroke', frameColor);
  frameRect.setAttribute('stroke-width', thickness.toString());
  
  // Apply frame style
  if (designOptions.frame_style === 'rounded') {
    frameRect.setAttribute('rx', '15');
    frameRect.setAttribute('ry', '15');
  } else if (designOptions.frame_style === 'dashed') {
    frameRect.setAttribute('stroke-dasharray', '10,5');
  }
  
  // Add frame to group
  frameGroup.appendChild(frameRect);
  
  // Add frame text if specified
  if (designOptions.frame_text) {
    const text = document.createElementNS('http://www.w3.org/2000/svg', 'text') as SVGTextElement;
    text.setAttribute('x', (width / 2).toString());
    text.setAttribute('y', (height - thickness / 2).toString());
    text.setAttribute('text-anchor', 'middle');
    text.setAttribute('dominant-baseline', 'middle');
    text.setAttribute('fill', frameColor);
    text.setAttribute('font-family', designOptions.frame_font || 'Arial, sans-serif');
    text.setAttribute('font-size', (designOptions.frame_font_size || 12).toString());
    text.textContent = designOptions.frame_text;
    
    frameGroup.appendChild(text);
  }
  
  // Add frame group to SVG
  svgElement.appendChild(frameGroup);
}

/**
 * Finds finder patterns (eyes) in the SVG by analyzing rectangular clusters
 */
function findFinderPatterns(svgElement: SVGSVGElement): Array<{border: Element|null, center: Element|null}> {
  const rects = Array.from(svgElement.querySelectorAll('rect:not(.qr-background)'));

  // Get SVG dimensions
  const svgWidth = parseFloat(svgElement.getAttribute('width') || '200');
  const svgHeight = parseFloat(svgElement.getAttribute('height') || '200');

  // Group rectangles by corner regions
  const cornerRegions = {
    topLeft: { x: 0, y: 0, width: svgWidth * 0.35, height: svgHeight * 0.35 },
    topRight: { x: svgWidth * 0.65, y: 0, width: svgWidth * 0.35, height: svgHeight * 0.35 },
    bottomLeft: { x: 0, y: svgHeight * 0.65, width: svgWidth * 0.35, height: svgHeight * 0.35 }
  };

  const finderElements = new Set<Element>();
  const patterns: Array<{border: Element|null, center: Element|null}> = [];

  // For each corner region, find rectangles that form finder patterns
  Object.entries(cornerRegions).forEach(([corner, region]) => {
    const rectsInRegion = rects.filter(rect => {
      const x = parseFloat(rect.getAttribute('x') || '0');
      const y = parseFloat(rect.getAttribute('y') || '0');

      return x >= region.x && x < (region.x + region.width) &&
             y >= region.y && y < (region.y + region.height);
    });

    if (rectsInRegion.length > 0) {
      // Find the bounding box of rectangles in this region
      const minX = Math.min(...rectsInRegion.map(r => parseFloat(r.getAttribute('x') || '0')));
      const minY = Math.min(...rectsInRegion.map(r => parseFloat(r.getAttribute('y') || '0')));
      const maxX = Math.max(...rectsInRegion.map(r => {
        const x = parseFloat(r.getAttribute('x') || '0');
        const w = parseFloat(r.getAttribute('width') || '0');
        return x + w;
      }));
      const maxY = Math.max(...rectsInRegion.map(r => {
        const y = parseFloat(r.getAttribute('y') || '0');
        const h = parseFloat(r.getAttribute('height') || '0');
        return y + h;
      }));

      const regionWidth = maxX - minX;
      const regionHeight = maxY - minY;

      // Check if this looks like a finder pattern (roughly square, reasonable size)
      const isSquareish = Math.abs(regionWidth - regionHeight) < regionWidth * 0.3;
      const isReasonableSize = regionWidth >= 30 && regionWidth <= 100; // Typical finder pattern size

      if (isSquareish && isReasonableSize && rectsInRegion.length >= 5) {
        // This looks like a finder pattern - mark all rectangles in this region
        rectsInRegion.forEach(rect => finderElements.add(rect));

        // Find the outermost and innermost rectangles as border and center
        const border = rectsInRegion.find(rect => {
          const x = parseFloat(rect.getAttribute('x') || '0');
          const y = parseFloat(rect.getAttribute('y') || '0');
          return Math.abs(x - minX) < 5 && Math.abs(y - minY) < 5;
        });

        const center = rectsInRegion.find(rect => {
          const x = parseFloat(rect.getAttribute('x') || '0');
          const y = parseFloat(rect.getAttribute('y') || '0');
          const centerX = minX + regionWidth / 2;
          const centerY = minY + regionHeight / 2;
          return Math.abs(x - centerX) < regionWidth * 0.3 && Math.abs(y - centerY) < regionHeight * 0.3;
        });

        patterns.push({ border: border || null, center: center || null });

        console.log(`Finder pattern detected in ${corner}:`, {
          region: { minX, minY, width: regionWidth, height: regionHeight },
          rectangles: rectsInRegion.length,
          border: !!border,
          center: !!center
        });
      }
    }
  });

  console.log('Finder pattern detection summary:', {
    totalRects: rects.length,
    patternsFound: patterns.length,
    finderElements: finderElements.size,
    svgDimensions: { width: svgWidth, height: svgHeight }
  });

  // Return the finder elements set for use in pattern styling
  return patterns;

  return patterns;
}

/**
 * Applies pattern styling to QR modules (but NOT finder patterns)
 */
function applyPatternStyling(svgElement: SVGSVGElement, designOptions: Partial<QRDesignOptions>): void {
  const pattern = designOptions.pattern || 'classic';

  // Get all rectangles but exclude finder patterns (corner squares)
  const allRects = Array.from(svgElement.querySelectorAll('rect:not(.qr-background)'));

  // Identify finder patterns first to exclude them from styling
  const finderPatterns = findFinderPatterns(svgElement);

  // Get all finder pattern elements (this now includes all rectangles that make up finder patterns)
  const finderElements = new Set<Element>();

  // The improved findFinderPatterns function marks all finder pattern rectangles
  // We need to get them from the function's internal logic
  // For now, let's re-run the detection to get the elements
  const rects = Array.from(svgElement.querySelectorAll('rect:not(.qr-background)'));
  const svgWidth = parseFloat(svgElement.getAttribute('width') || '200');
  const svgHeight = parseFloat(svgElement.getAttribute('height') || '200');

  const cornerRegions = {
    topLeft: { x: 0, y: 0, width: svgWidth * 0.35, height: svgHeight * 0.35 },
    topRight: { x: svgWidth * 0.65, y: 0, width: svgWidth * 0.35, height: svgHeight * 0.35 },
    bottomLeft: { x: 0, y: svgHeight * 0.65, width: svgWidth * 0.35, height: svgHeight * 0.35 }
  };

  // Mark all rectangles in finder pattern regions as protected
  Object.values(cornerRegions).forEach(region => {
    const rectsInRegion = rects.filter(rect => {
      const x = parseFloat(rect.getAttribute('x') || '0');
      const y = parseFloat(rect.getAttribute('y') || '0');

      return x >= region.x && x < (region.x + region.width) &&
             y >= region.y && y < (region.y + region.height);
    });

    if (rectsInRegion.length >= 5) { // Likely a finder pattern
      rectsInRegion.forEach(rect => finderElements.add(rect));
    }
  });

  // Only style data modules, not finder patterns
  const dataModules = allRects.filter(rect => !finderElements.has(rect));

  console.log('Pattern styling:', {
    pattern,
    totalRects: allRects.length,
    finderElements: finderElements.size,
    dataModules: dataModules.length
  });

  switch (pattern) {
    case 'circle':
      dataModules.forEach(module => {
        const x = parseFloat(module.getAttribute('x') || '0');
        const y = parseFloat(module.getAttribute('y') || '0');
        const width = parseFloat(module.getAttribute('width') || '0');
        const fill = module.getAttribute('fill') || '#000000';

        // Create a circle to replace the rectangle
        const circle = document.createElementNS('http://www.w3.org/2000/svg', 'circle') as SVGCircleElement;
        circle.setAttribute('cx', (x + width/2).toString());
        circle.setAttribute('cy', (y + width/2).toString());
        circle.setAttribute('r', (width/2).toString());
        circle.setAttribute('fill', fill);

        // Replace the rectangle with the circle
        module.parentNode?.replaceChild(circle, module);
      });
      break;

    case 'rounded':
      dataModules.forEach(module => {
        const size = parseFloat(module.getAttribute('width') || '0');
        module.setAttribute('rx', (size * 0.3).toString());
        module.setAttribute('ry', (size * 0.3).toString());
      });
      break;

    case 'dots':
      dataModules.forEach(module => {
        const x = parseFloat(module.getAttribute('x') || '0');
        const y = parseFloat(module.getAttribute('y') || '0');
        const width = parseFloat(module.getAttribute('width') || '0');
        const fill = module.getAttribute('fill') || '#000000';

        // Create a smaller circle
        const circle = document.createElementNS('http://www.w3.org/2000/svg', 'circle') as SVGCircleElement;
        circle.setAttribute('cx', (x + width/2).toString());
        circle.setAttribute('cy', (y + width/2).toString());
        circle.setAttribute('r', (width * 0.35).toString()); // Smaller than regular circle
        circle.setAttribute('fill', fill);

        // Replace the rectangle with the circle
        module.parentNode?.replaceChild(circle, module);
      });
      break;
      
    case 'diamond':
      dataModules.forEach(module => {
        const x = parseFloat(module.getAttribute('x') || '0');
        const y = parseFloat(module.getAttribute('y') || '0');
        const width = parseFloat(module.getAttribute('width') || '0');
        const fill = module.getAttribute('fill') || '#000000';

        // Create a diamond shape using a polygon
        const diamond = document.createElementNS('http://www.w3.org/2000/svg', 'polygon') as SVGPolygonElement;
        const centerX = x + width/2;
        const centerY = y + width/2;

        const points = [
          `${centerX},${y}`,
          `${x + width},${centerY}`,
          `${centerX},${y + width}`,
          `${x},${centerY}`
        ].join(' ');

        diamond.setAttribute('points', points);
        diamond.setAttribute('fill', fill);

        // Replace the rectangle with the diamond
        module.parentNode?.replaceChild(diamond, module);
      });
      break;

    case 'horizontal':
      dataModules.forEach(module => {
        const x = parseFloat(module.getAttribute('x') || '0');
        const y = parseFloat(module.getAttribute('y') || '0');
        const width = parseFloat(module.getAttribute('width') || '0');
        const fill = module.getAttribute('fill') || '#000000';

        // Create a horizontal bar
        const rect = document.createElementNS('http://www.w3.org/2000/svg', 'rect') as SVGRectElement;
        rect.setAttribute('x', x.toString());
        rect.setAttribute('y', (y + width * 0.25).toString());
        rect.setAttribute('width', width.toString());
        rect.setAttribute('height', (width * 0.5).toString());
        rect.setAttribute('fill', fill);

        // Replace the original rectangle
        module.parentNode?.replaceChild(rect, module);
      });
      break;

    case 'vertical':
      dataModules.forEach(module => {
        const x = parseFloat(module.getAttribute('x') || '0');
        const y = parseFloat(module.getAttribute('y') || '0');
        const width = parseFloat(module.getAttribute('width') || '0');
        const fill = module.getAttribute('fill') || '#000000';

        // Create a vertical bar
        const rect = document.createElementNS('http://www.w3.org/2000/svg', 'rect') as SVGRectElement;
        rect.setAttribute('x', (x + width * 0.25).toString());
        rect.setAttribute('y', y.toString());
        rect.setAttribute('width', (width * 0.5).toString());
        rect.setAttribute('height', width.toString());
        rect.setAttribute('fill', fill);

        // Replace the original rectangle
        module.parentNode?.replaceChild(rect, module);
      });
      break;
      
    // Add more pattern cases as needed
      
    default:
      // No special styling for classic/square
      break;
  }
}
