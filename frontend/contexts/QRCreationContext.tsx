'use client';

import React, { createContext, useContext, useReducer, type ReactNode, type Dispatch } from 'react';
import type { QRType, QRTypeEnum, QRDesignOptions } from '@/types/qr';

// --- Modernized QR Creation Context ---
// Uses full QRDesignOptions interface from backend-aligned types/qr.ts

export interface QRAdvancedOptions {
  // Security and Access Control
  dynamic: boolean;
  is_public: boolean; // Added public/private access toggle
  password_protected: boolean;
  password: string;
  
  // Geographic Restrictions
  geo_restriction: string;
  denied_countries: string; // Added denied countries field
  
  // Limits and Expiration
  expiry_date: string;
  max_scans: string;
  
  // Team and User
  team_id: string;
  buyer_email: string;
  
  // Analytics and Tracking
  enable_tracking: boolean;
  enable_analytics: boolean;
  connection_quality_tracking: boolean; // Added connection quality tracking
  
  // Schedule
  schedule_active: boolean;
  schedule_start: string;
  schedule_end: string;
  
  // New dynamic QR properties
  custom_short_url?: string;
  landing_page_template?: string;
  show_landing_preview?: boolean;
}

export interface QRCreationState {
  currentStep: number;
  selectedType: QRType | null;
  content: Record<string, any>;
  // Full backend-aligned QRDesignOptions object
  design: QRDesignOptions;
  branding: {
    profileId: number | null;
    moduleStyle?: string;
    cornerStyle?: string;
    gradientType?: string;
    gradientStart?: string;
    gradientEnd?: string;
    shapePadding?: number;
    quietZone?: number;
    details?: {
      name?: string;
      logo?: string;
      primaryColor?: string;
      secondaryColor?: string;
      fontFamily?: string;
    };
  };
  advanced: QRAdvancedOptions;
  isGenerating: boolean;
  error: string | null;
  qrData?: Record<string, any>;
}

// Define action types to modify the state
export type QRCreationAction =
  | { type: 'SET_STEP'; payload: number }
  | { type: 'SELECT_TYPE'; payload: QRType }
  | { type: 'UPDATE_CONTENT'; payload: Record<string, any> }
  | { type: 'UPDATE_DESIGN'; payload: Partial<QRDesignOptions> }
  | { type: 'UPDATE_BRANDING'; payload: Partial<QRCreationState['branding']> }
  | { type: 'UPDATE_ADVANCED'; payload: QRAdvancedOptions }
  | { type: 'SET_GENERATING'; payload: boolean }
  | { type: 'SET_ERROR'; payload: string | null }
  | { type: 'RESET' }
  | { type: 'UPDATE_QR_DATA'; payload: Record<string, any> }
  | { type: 'UPDATE_PREVIEW_DATA'; payload: Record<string, any> };


// Initial state
const initialState: QRCreationState = {
  currentStep: 1,
  selectedType: null,
  content: {},
  // Backend-aligned defaults for QRDesignOptions - clean black and white defaults
  design: {
    size: 256,
    margin: 10,
    pattern: 'square',
    marker_border_style: 'square',
    marker_center_style: 'square', // Changed from 'circle' to 'square'
    logo_url: undefined,
    logo_position: 'center',
    logo_size: 48,
    logo_opacity: 1.0,
    frame_style: 'none',
    frame_color: '#000000',
    frame_text: '',
    frame_thickness: 4,
    animation_type: undefined,
    animation_speed: undefined,
    overlay_url: undefined,
    overlay_blend_mode: undefined,
    foreground_color: '#000000',
    background_color: '#FFFFFF',
    color_mode: 'solid',
    error_correction: 'M',
    ai_enhance: false,
    ai_enhance_options: {},
    // SVG-specific options
    use_svg: true,
    svg_render_dpi: 300,
    svg_optimize: true,
  },
  branding: {
    profileId: null,
  },
  advanced: {
    dynamic: true,
    is_public: true, // Default to public
    password_protected: false,
    password: '',
    geo_restriction: '',
    denied_countries: '', // Added denied countries field
    expiry_date: '',
    max_scans: '',
    team_id: '',
    buyer_email: '',
    enable_tracking: false,
    enable_analytics: false,
    connection_quality_tracking: false, // Added connection quality tracking
    schedule_active: false,
    schedule_start: '',
    schedule_end: '',
    // Keep existing dynamic QR properties
    custom_short_url: undefined,
    landing_page_template: undefined,
    show_landing_preview: undefined,
  },
  isGenerating: false,
  error: null,
  qrData: {},
};

// Reducer to handle state updates
function qrCreationReducer(state: QRCreationState, action: QRCreationAction): QRCreationState {
  switch (action.type) {
    case 'SET_STEP':
      return { ...state, currentStep: action.payload };
    case 'SELECT_TYPE':
      return { ...state, selectedType: action.payload, content: {} };
    case 'UPDATE_CONTENT':
      return { ...state, content: { ...state.content, ...action.payload } };
    case 'UPDATE_DESIGN':
      return {
        ...state,
        design: {
          ...state.design,
          ...action.payload,
        },
      };
      // Now supports all QRDesignOptions fields
    case 'UPDATE_BRANDING':
      return {
        ...state,
        branding: {
          ...state.branding,
          ...action.payload,
        },
      };
    case 'UPDATE_ADVANCED':
      return { ...state, advanced: { ...state.advanced, ...action.payload } };
    case 'SET_GENERATING':
      return { ...state, isGenerating: action.payload };
    case 'SET_ERROR':
      return { ...state, error: action.payload };
    case 'UPDATE_QR_DATA':
      return { ...state, qrData: { ...state.qrData, ...action.payload } };
    case 'UPDATE_PREVIEW_DATA':
      return { ...state, content: { ...state.content, ...action.payload } };
    case 'RESET':
      return initialState;
    default:
      return state;
  }
}

// Create context with initial state and dispatch function
// Provides full QRDesignOptions object and update actions
export const QRCreationContext = createContext<{
  state: QRCreationState;
  dispatch: Dispatch<QRCreationAction>;
}>({
  state: initialState,
  dispatch: () => null,
});

// Hook to use the QR creation context
export const useQRCreation = () => useContext(QRCreationContext);

// Provider component to wrap parts of the app that need access to QR creation state
export const QRCreationProvider: React.FC<{ children: ReactNode }> = ({ children }) => {
  const [state, dispatch] = useReducer(qrCreationReducer, initialState);

  return (
    <QRCreationContext.Provider value={{ state, dispatch }}>
      {children}
    </QRCreationContext.Provider>
  );
}; 