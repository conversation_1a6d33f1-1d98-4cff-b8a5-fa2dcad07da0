# QR Creator Orchestration: Architecture & Flow

_Last updated: 2025-05-24_

## Overview

This document describes the orchestration of the QR code creation flow in the QRVibe frontend, detailing all major files, their responsibilities, and the data flow. It also includes a visual diagram of the process.

---

## Visual Diagram

```mermaid
graph TD
    A[QR Creator Page (page.tsx)] --> B[QRCreationContext]
    A --> C[QRTypeSelector]
    A --> D[QRContentForm]
    A --> E[QRDesignStep]
    A --> F[QRAdvancedOptions]
    E --> G[QRDesignOptions]
    E --> H[DesignOptionsUI]
    A --> I[QRPreview]
    A --> J[QRExportOptions]
    A --> K[QRContextualTools]
    A --> L[handleCreateQR]
    L --> M[qrService.modern.ts]
    L --> N[qrDesignNormalization.ts]
    L --> O[types/qr.ts & qr-operations.ts]
    subgraph API
      M --> P[Backend API]
    end
```

---

## Key Files & Responsibilities

### 1. Main Orchestrator
- **`app/(user)/dashboard/qr/creator/page.tsx`**
  - Manages QR creation phases and transitions
  - Holds state for QR type, data, design, advanced options
  - Handles QR creation via `handleCreateQR`

### 2. Context & State Management
- **`contexts/QRCreationContext.tsx`**
  - Provides global state/dispatch for QR creation

### 3. Step Components
- **`components/ui/qr/creator/QRTypeSelector.tsx`**: QR type selection UI
- **`components/ui/qr/creator/QRContentForm.tsx`**: Delegates dynamic content form rendering to `QRDataForm` for the selected type (see below)
- **`components/ui/QRDataForm/QRDataForm.tsx`**: **Single source of truth** for QR type-to-form mapping and dynamic loading. Handles all dynamic imports, suspense/loading states, and error boundaries for every QR type form in the app.
- **`components/ui/qr/creator/QRDesignStep.tsx`**: Appearance customization (colors, logo, style, etc.)
- **`components/ui/qr/QRDesignOptions.tsx`**: Core design options logic/state
- **`components/ui/qr/creator/QRAdvancedOptions.tsx`**: Advanced/non-design options (dynamic/static, password, geo, analytics, schedule, etc.)

### 4. Preview & Export
- **`components/ui/qr/QRPreview.tsx`**: Live QR code preview
- **`components/ui/qr/QRExportOptions.tsx`**: Export with all design features

### 5. API Service
- **`services/qrService.modern.ts`**: Handles all API calls for creation, preview, update, export

### 6. Types & Normalization
- **`types/qr.ts`**: `QRType`, `QRDesignOptions`, and data/request/response types
- **`types/qr-operations.ts`**: `QRCodeCreateRequest`, `QRCodeUpdateRequest`, etc.
- **`components/ui/qr/qrDesignNormalization.ts`**: Normalizes design options to canonical snake_case

### 7. Additional UI Utilities
- **`components/ui/qr/DesignOptionsUI.tsx`**: Tabbed UI for design options
- **`components/ui/qr/QRContextualTools.tsx`**: Floating action buttons for create, back, etc.

### 8. Default Data Provider
- **`lib/qr/DefaultDataProvider.ts`**: Provides default example data for every QR type via `DEFAULT_QR_DATA` and `getDefaultDataForType`. Ensures that each form is initialized with valid, user-friendly defaults for instant preview and editing.
  - **Best Practice:** Keep this file up to date with every new QR type added to the app. Missing entries will result in blank or incomplete previews.

### 9. Centralized Required Field Validation
- **`lib/qr-required-fields.ts`**: Defines `QR_REQUIRED_FIELDS`, mapping each QR type to its required fields, and exports `hasRequiredFields` for generic validation.
  - **Purpose:** Ensures all wizard and form validation is consistent and maintainable.
  - **Best Practice:** Always update this file when adding new QR types or changing required fields. This prevents drift between UI, backend, and validation logic.

---

## Dynamic QR Form Loading: Centralization & Best Practices

### Architectural Improvement (2025-05-24)

- **`QRContentForm.tsx` was refactored to delegate all dynamic QR form rendering to `QRDataForm.tsx`.**
- **`QRDataForm.tsx` is now the single source of truth** for mapping QR types to their respective form components and for handling all dynamic imports, suspense boundaries, and error states.
- This eliminates duplicate mappings and logic, ensures every QR type is always supported in the wizard, and dramatically improves maintainability and user experience.
- **If a form is missing for a type, it only needs to be added to `QRDataForm.tsx`—not in multiple places.**
- This approach leverages React 19 and Next.js 15 best practices for dynamic loading and code splitting, ensuring optimal performance and a consistent UI/UX.

---

## Flow Summary

1. **User selects QR type** → `QRTypeSelector.tsx`
2. **User enters content** → `QRContentForm.tsx` (delegates to `QRDataForm.tsx`)
3. **User customizes design** → `QRDesignStep.tsx` & `QRDesignOptions.tsx`
4. **User sets advanced options** → `QRAdvancedOptions.tsx`
5. **User previews QR** → `QRPreview.tsx`
6. **User creates QR** → `handleCreateQR` in `page.tsx`, which:
   - Normalizes and assembles all data
   - Calls `QRService` to create the QR code
7. **User exports QR** → `QRExportOptions.tsx`

---

## Integration Points

- **Normalization:** All design options are normalized before being sent to the backend (`qrDesignNormalization.ts`).
- **Type Safety:** All state and API requests use types from `types/qr.ts` and `types/qr-operations.ts`.
- **Context:** `QRCreationContext` ensures all steps/components have access to the current QR creation state.

---

## References
- [QR Design Normalization Utility](../../components/ui/qr/qrDesignNormalization.ts)
- [QR Types & Operations](../../types/qr.ts)
- [QR Service](../../services/qrService.modern.ts)
