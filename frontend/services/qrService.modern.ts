/**
 * QRVibe Modern QR Service
 * 
 * This service provides methods for interacting with QR code-related endpoints.
 * It follows the standardized API pattern and ensures proper error handling.
 * It includes both standard QR features and enhanced AI-powered capabilities.
 */

import { toast } from '@/components/ui/use-toast';
import api from '@/lib/api/modernClient';
import { ENDPOINTS, AI } from '@/lib/api/endpoints';
import type { 
  QRType, 
  QRCode,
  QRCreateRequest as QRCodeCreateRequest,
  QRResponse, 
  QRDownloadOptions
} from '@/types/qr';
import type {
  QRCodeFilterParams,
  QRCodeUploadOptions,
  QRCodeUpdateRequest
} from '@/types/qr-operations';
import type { 
  SocialQRData, 
  SocialQR, 
  CreateSocialQRInput, 
  UpdateSocialQRInput 
} from '@/types/socialQR';
import type { 
  TweetQRData,
  TweetQR,
  CreateTweetQRInput,
  UpdateTweetQRInput
} from '../types/tweetQR';

import type {
  SocialProfileQRData,
  SocialProfileQR,
  CreateSocialProfileQRInput,
  UpdateSocialProfileQRInput
} from '../types/socialProfileQR';

import type {
  PosterTemplate,
  PosterCategory,
  PosterGenerationOptions,
  PosterGenerationResponse,
  PosterDownloadOptions,
  PosterListResponse
} from '@/types/poster';

import { combineServices, createBatchService, createCrudService } from './base/servicePattern';

// Add Google Analytics types
declare global {
  interface Window {
    gtag?: (command: string, action: string, params: object) => void;
  }
}

// Define a fixed type that satisfies BaseResource for the QRCode
type QRCodeResource = QRCode & {
  // Ensure createdAt and updatedAt are non-optional strings
  createdAt: string;
  updatedAt: string;
};

/**
 * Enhanced QR code interface
 */
export interface EnhancedQR {
  id: string;
  user_id: string;
  name: string;
  description?: string;
  qr_data: string;
  template_id?: string;
  template_settings?: Record<string, any>;
  enhanced_features: EnhancedFeatures;
  created_at: string;
  updated_at: string;
  
  // Camel case versions for UI compatibility
  userId: string;
  qrData: string;
  templateId?: string;
  templateSettings?: Record<string, any>;
  enhancedFeatures: EnhancedFeatures;
  createdAt: string;
  updatedAt: string;
}

/**
 * Enhanced QR features interface
 */
export interface EnhancedFeatures {
  dynamic_content: boolean;
  animation_type?: string;
  gradient_colors?: string[];
  password_protected?: boolean;
  access_limit?: number;
  advanced_analytics?: boolean;
  expiration_date?: string;
  geo_restriction?: {
    enabled: boolean;
    countries?: string[];
    regions?: string[];
    coordinates?: {
      latitude: number;
      longitude: number;
      radius: number; // in km
    };
  };
  device_targeting?: {
    enabled: boolean;
    platforms?: string[];
    browsers?: string[];
  };
  scheduling?: {
    enabled: boolean;
    start_date?: string;
    end_date?: string;
    time_ranges?: {
      day: string;
      start_time: string;
      end_time: string;
    }[];
  };
}

/**
 * Enhanced QR generation request interface
 */
export interface EnhancedQRGenerateRequest {
  name: string;
  description?: string;
  qr_data: string;
  template_id?: string;
  template_settings?: Record<string, any>;
  enhanced_features: Partial<EnhancedFeatures>;
  
  // Camel case versions for UI compatibility
  qrData: string;
  templateId?: string;
  templateSettings?: Record<string, any>;
  enhancedFeatures: Partial<EnhancedFeatures>;
}

/**
 * Enhanced QR update request interface
 */
export interface EnhancedQRUpdateRequest {
  name?: string;
  description?: string;
  template_id?: string;
  template_settings?: Record<string, any>;
  enhanced_features?: Partial<EnhancedFeatures>;
  
  // Camel case versions for UI compatibility
  templateId?: string;
  templateSettings?: Record<string, any>;
  enhancedFeatures?: Partial<EnhancedFeatures>;
}

/**
 * AI enhancement response interface
 */
export interface AIEnhancementResponse {
  enhanced_features: Partial<EnhancedFeatures>;
}

/**
 * Content suggestion interface
 */
export interface ContentSuggestion {
  field: string;
  value: string;
  explanation: string;
}

/**
 * Content suggestion response interface
 */
export interface ContentSuggestionResponse {
  suggestions: ContentSuggestion[];
  error?: string;
}

/**
 * Design colors interface
 */
export interface DesignColors {
  foreground: string;
  background: string;
  accent?: string;
}

/**
 * Design recommendation interface
 */
export interface DesignRecommendation {
  name: string;
  colors: DesignColors;
  pattern: string;
  error_correction: string;
  description: string;
  rationale: string;
}

/**
 * Design recommendation response interface
 */
export interface DesignRecommendationResponse {
  designs: DesignRecommendation[];
  error?: string;
}

/**
 * Scan simulation options interface
 */
export interface ScanSimulationOptions {
  simulate_lighting?: boolean;
  simulate_distance?: boolean;
  simulate_devices?: boolean;
}

/**
 * Scan simulation condition results interface
 */
export interface SimulationConditionResults {
  scannable: boolean;
  conditions?: Record<string, boolean>;
  distances?: Record<string, boolean>;
  devices?: Record<string, boolean>;
  suggestions?: string[];
}

/**
 * Simulation detail interface
 */
export interface SimulationDetail {
  type: string;
  results: SimulationConditionResults;
}

/**
 * Scan simulation response interface
 */
export interface ScanSimulationResponse {
  success: boolean;
  scannable: boolean;
  simulations: SimulationDetail[];
  improvement_suggestions?: string[];
  error?: string;
}

/**
 * Natural language QR creation response interface
 */
export interface NaturalLanguageResponse {
  success: boolean;
  qr_type: string;
  parameters: Record<string, any>;
  confidence: number;
  missing_info?: string[];
  error?: string;
}

/**
 * QR optimization response interface
 */
export interface OptimizationResponse {
  success: boolean;
  optimized_qr: string;
  error?: string;
}

/**
 * API response interface
 */
export interface APIResponse<T> {
  data: T;
  status: number;
}

/**
 * Standardized service error handler
 * 
 * @param error - The error object from the catch block
 * @param operation - Description of the operation that failed
 * @returns Default return value for the calling function (often empty array or null)
 */
const handleServiceError = <T>(error: any, operation: string): T => {
  console.error(`Error in ${operation}:`, error);
  
  // Display toast notification for user feedback
  toast({
    title: `${operation} failed`,
    description: error?.message || `Unable to complete the operation: ${operation}`,
    variant: 'destructive',
  });
  
  // For array returns, return empty array
  if (Array.isArray({})) {
    return [] as unknown as T;
  }
  
  // For void returns
  if (typeof ({} as unknown as T) === 'undefined') {
    return undefined as unknown as T;
  }
  
  // For Blob returns (usually for exports)
  if ({} instanceof Blob) {
    return new Blob([], { type: 'application/octet-stream' }) as unknown as T;
  }
  
  // Return null for object returns
  return null as unknown as T;
};

/**
 * Helper to convert from API format to consistent UI format
 */
const adaptQRCode = (qr: QRCode): QRCodeResource => ({
  ...qr,
  // Convert snake_case dates to camelCase and ensure they're defined
  createdAt: qr.created_at || qr.createdAt || new Date().toISOString(),
  updatedAt: qr.updated_at || qr.updatedAt || new Date().toISOString(),
});

/**
 * Helper to adapt Enhanced QR features to include camelCase properties
 */
function adaptEnhancedFeatures(features: any): EnhancedFeatures {
  if (!features) return { dynamic_content: false };
  
  return {
    ...features,
    // Add camelCase versions for UI compatibility
  };
}

/**
 * Helper to adapt Enhanced QR to include camelCase properties
 */
function adaptEnhancedQR(qr: any): EnhancedQR {
  return {
    ...qr,
    // Add camelCase versions for UI compatibility
    userId: qr.user_id,
    qrData: qr.qr_data,
    templateId: qr.template_id,
    templateSettings: qr.template_settings,
    enhancedFeatures: adaptEnhancedFeatures(qr.enhanced_features),
    createdAt: qr.created_at,
    updatedAt: qr.updated_at
  };
}

/**
 * Helper function for getting default content values based on QR type
 */
function getDefaultContentForType(qrType: string): Record<string, any> {
  switch (qrType.toLowerCase()) {
    case 'url':
      return { url: 'https://qrvibe.app' };
    case 'text':
      return { text: 'Example text' };
    case 'email':
      return { email: '<EMAIL>', subject: 'Subject', body: 'Email body' };
    case 'phone':
      return { phone: '+1234567890' };
    case 'sms':
      return { phone: '+1234567890', message: 'Example SMS message' };
    case 'wifi':
      return { ssid: 'WiFi Network', password: 'password', encryption: 'WPA' };
    case 'vcard':
      return { 
        first_name: 'John',
        last_name: 'Doe',
        organization: 'Example Inc',
        phone: '+1234567890',
        email: '<EMAIL>'
      };
    case 'location':
      return { latitude: '37.7749', longitude: '-122.4194', name: 'San Francisco' };
    case 'event':
      return {
        title: 'Example Event',
        location: 'Example Location',
        description: 'Example Description',
        start_time: new Date().toISOString(),
        end_time: new Date(Date.now() + 3600000).toISOString()
      };
    case 'crypto':
      return { address: '**********************************', amount: '0.001', crypto_type: 'bitcoin' };
    // Add more types as needed based on your 160+ QR types
    default:
      // For unknown types, return a simple text value
      return { value: `Example ${qrType} data` };
  }
}

/**
 * Helper function to check if a QR type's primary field is empty
 */
function isPrimaryFieldEmpty(qrType: string, data: Record<string, any>): boolean {
  // If we have an email_address field but not email field, copy it over
  if (qrType === 'email' && !data.email && data.email_address) {
    data.email = data.email_address;
  }
  
  switch (qrType) {
    case 'url':
      return !data.url || data.url === '';
    case 'text':
      return !data.text || data.text === '';
    case 'email':
      return !data.email || data.email === '';
    case 'phone':
      return !data.phone || data.phone === '';
    case 'wifi':
      return !data.ssid || data.ssid === '';
    case 'vcard':
      return !data.name || data.name === '';
    case 'sms':
      return !data.phone || data.phone === '';
    // For unknown types, check if any primary field is non-empty
    default:
      // Check if there's at least one non-empty string value
      // This is less strict than checking every value
      return !Object.values(data).some(value => 
        value !== undefined && value !== null && value !== ''
      );
  }
}

/**
 * Transforms QR data based on type to match API expectations
 * 
 * @param qrType - Type of QR code
 * @param data - Raw data from the form
 * @returns Transformed data ready for API consumption
 */
export const transformDataForApi = (qrType: QRType, data: Record<string, any>): Record<string, any> | null => {
  // Handle empty data
  if (!data) return null;
  
  // Start with a copy of the data
  const transformedData = { ...data };
  
  // Common helper for date formatting
  const formatDate = (dateStr: string) => {
    if (!dateStr) return dateStr;
    return dateStr.includes('T') ? dateStr : `${dateStr}T00:00:00`;
  };
  
  // Apply transformations based on QR type
  switch (qrType) {
    // Basic types
    case 'url':
      // Ensure URLs have a protocol
      if (transformedData.url && !transformedData.url.match(/^https?:\/\//)) {
        transformedData.url = `https://${transformedData.url}`;
      }
      break;
    
    case 'email':
      // Format email data consistently
      if (transformedData.email) {
        transformedData.email_address = transformedData.email_address || transformedData.email;
        transformedData.subject = transformedData.subject || '';
        transformedData.body = transformedData.body || '';
      }
      break;
    
    case 'phone':
      // Format phone numbers
      if (transformedData.phone) {
        transformedData.phone_number = transformedData.phone_number || transformedData.phone;
      }
      break;
    
    case 'sms':
      // Format SMS data
      if (transformedData.phone) {
        transformedData.phone_number = transformedData.phone_number || transformedData.phone;
        transformedData.message = transformedData.message || '';
      }
      break;
    
    case 'text':
      // Ensure text content is a string
      transformedData.text = String(transformedData.text || '');
      break;
      
    case 'vcard':
      // Ensure vCard fields are correctly formatted
      if (!transformedData.first_name && transformedData.name) {
        const nameParts = transformedData.name.split(' ');
        transformedData.first_name = nameParts[0] || '';
        transformedData.last_name = nameParts.slice(1).join(' ') || '';
      }
      break;
      
    // Network types
    case 'wifi':
      // Ensure wifi encryption is set
      if (!transformedData.encryption) {
        transformedData.encryption = 'WPA';
      }
      // Convert boolean to string for API
      if (typeof transformedData.hidden === 'boolean') {
        transformedData.hidden = transformedData.hidden ? 'true' : 'false';
      }
      break;
    
    case 'zoom':
    case 'meet':
    case 'teams':
    case 'webex':
      // Ensure meeting URLs are properly formatted
      if (transformedData.meeting_url && !transformedData.meeting_url.match(/^https?:\/\//)) {
        transformedData.meeting_url = `https://${transformedData.meeting_url}`;
      }
      break;
      
    // Social media types
    case 'x':
    case 'twitter':
      // Format Twitter/X username
      if (transformedData.username && transformedData.username.startsWith('@')) {
        transformedData.username = transformedData.username.substring(1);
      }
      break;
    
    case 'whatsapp':
    case 'whatsapp_business':
      // Format WhatsApp phone numbers
      if (transformedData.phone) {
        // Remove any non-numeric characters
        transformedData.phone = transformedData.phone.replace(/\D/g, '');
        // Ensure it starts with + if it's an international number
        if (!transformedData.phone.startsWith('+')) {
          transformedData.phone = `+${transformedData.phone}`;
        }
      }
      break;
      
    // Events & calendar
    case 'event':
      // Format dates for calendar events
      if (transformedData.start) {
        transformedData.start_time = formatDate(transformedData.start);
      }
      if (transformedData.end) {
        transformedData.end_time = formatDate(transformedData.end);
      }
      break;
      
    case 'ticket':
      // Format ticket date
      if (transformedData.date) {
        transformedData.date = formatDate(transformedData.date);
      }
      break;
    
    case 'coupon':
      // Format coupon expiration date
      if (transformedData.expiration) {
        transformedData.expiration_date = formatDate(transformedData.expiration);
      }
      break;
      
    // Legal & regulatory types
    case 'regulatory_filing':
    case 'research_protocol' as QRType:
    case 'legal_notice':
    case 'certification' as QRType:
    case 'citation':
    case 'lab_equipment' as QRType:
    case 'consent_form':
    case 'audit_record':
    case 'smart_contract':
      // Format legal dates
      if (transformedData.effective_date) {
        transformedData.effective_date = formatDate(transformedData.effective_date);
      }
      if (transformedData.expiry_date) {
        transformedData.expiry_date = formatDate(transformedData.expiry_date);
      }
      break;
      
    // Medical & healthcare types
    case 'medical_record':
    case 'prescription':
    case 'appointment':
    case 'lab_result':
    case 'vaccination':
      // Format medical dates
      if (transformedData.date) {
        transformedData.date = formatDate(transformedData.date);
      }
      break;
      
    // Generic handling for any type not explicitly handled above
    default:
      // Handle any date fields
      ['date', 'start_date', 'end_date', 'expiry_date', 'effective_date'].forEach(dateField => {
        if (transformedData[dateField]) {
          transformedData[dateField] = formatDate(transformedData[dateField]);
        }
      });
      
      // Handle any URL fields
      ['url', 'website', 'link'].forEach(urlField => {
        if (transformedData[urlField] && typeof transformedData[urlField] === 'string' && 
            !transformedData[urlField].match(/^https?:\/\//)) {
          transformedData[urlField] = `https://${transformedData[urlField]}`;
        }
      });
      break;
  }
  
  return transformedData;
}

const qrPreviewService = {
  /**
   * Generate a preview of a QR code (alias for generatePreviewSvg)
   *
   * @param previewData - QR code preview request containing type, data and design options
   * @returns Promise with SVG string content
   */
  generatePreview: async (previewData: any): Promise<string> => {
    return qrPreviewService.generatePreviewSvg(previewData);
  },

  /**
   * Generate an SVG preview of a QR code
   *
   * @param previewData - QR code preview request containing type, data and design options
   * @returns Promise with SVG string content
   */
  generatePreviewSvg: async (previewData: any): Promise<string> => {
    try {
      console.log('Frontend QR preview SVG request:', {
        qrType: previewData.qrType,
        dataSize: JSON.stringify(previewData.data || {}).length,
        designOptions: previewData.designOptions ? 
          Object.keys(previewData.designOptions) : 'none',
        hasLogo: Boolean(previewData.logoFile || 
          previewData.logoUrl || previewData.logo),
        dimensions: previewData.dimensions || 300
      });
      
      // Standardize content format
      const formattedContent = previewData.data || {};
      
      // Clean up the content object to remove any numeric character keys
      // that may have been added when processing string data
      const cleanContent = { ...formattedContent };
      Object.keys(cleanContent).forEach(key => {
        // Remove any numeric keys that represent individual characters
        if (/^\d+$/.test(key)) {
          delete cleanContent[key];
        }
      });
      
      console.log('Cleaned content for SVG preview:', 
        JSON.stringify(cleanContent).length > 500 ? 
        'Content too large to log' : cleanContent);

      // Construct a FormData object to send both the request parameters and logo
      const formData = new FormData();
      
      // Create standardized request object
      const requestObject = {
        qr_type: previewData.qrType,
        data: cleanContent,
        designOptions: previewData.designOptions || {},
        dimensions: previewData.dimensions || 300
      };
      
      // Add request data as JSON string
      formData.append('request', JSON.stringify(requestObject));
      
      // Add logo if provided
      if (previewData.logoFile) {
        formData.append('logo', previewData.logoFile);
      } else if (previewData.logo && previewData.logo instanceof File) {
        formData.append('logo', previewData.logo);
      }
      
      // Make API request to the SVG preview endpoint
      const response = await api.post<{
        success: boolean;
        data: {
          image_data: string;
          format: string;
          width: number;
          height: number;
        };
        error?: {
          message: string;
          code?: string;
        };
      }>('/qr/preview/svg', formData, {
        headers: {
          'Content-Type': 'multipart/form-data'
        }
      });
      
      // Check for success
      if (!response.success) {
        const errorMessage = response.error?.message || 'Failed to generate SVG QR preview';
        console.error('QR preview SVG error:', errorMessage);
        throw new Error(errorMessage);
      }
      
      // Return the SVG string from the response
      if (response.data && response.data.image_data) {
        console.log('Successfully received SVG QR preview');
        return response.data.image_data;
      } else {
        throw new Error('Invalid response format from API');
      }
    } catch (error) {
      console.error('QR preview SVG error:', error);
      throw error;
    }
  },

};

/**
 * Base QR service for standard QR CRUD operations
 */
const baseQRService = {
  /**
   * Get a list of QR codes
   */
  getAll: async (params?: QRCodeFilterParams): Promise<QRCodeResource[]> => {
    try {
      const response = await api.get<QRCode[]>(ENDPOINTS.QR.BASE, { params });
      return response.map(adaptQRCode);
    } catch (error) {
      return handleServiceError<QRCodeResource[]>(error, 'Get QR codes');
    }
  },
  
  /**
   * Get a QR code by ID
   */
  getById: async (id: string): Promise<QRCodeResource> => {
    try {
      const response = await api.get<QRCode>(ENDPOINTS.QR.BY_ID(id));
      return adaptQRCode(response);
    } catch (error) {
      return handleServiceError<QRCodeResource>(error, 'Get QR code');
    }
  },
  
  /**
   * Create a new QR code
   */
  create: async (data: QRCodeCreateRequest): Promise<QRCodeResource> => {
    try {
      // Use POST request as expected by the backend
      const response = await api.post<QRCode>(ENDPOINTS.QR.BASE, data);
      return adaptQRCode(response);
    } catch (error) {
      return handleServiceError<QRCodeResource>(error, 'Create QR code');
    }
  },
  
  /**
   * Update a QR code (partial update with PATCH)
   */
  update: async (id: string, data: QRCodeUpdateRequest): Promise<QRCodeResource> => {
    try {
      // Accepts backend-aligned design_options field
      const response = await api.patch<QRCode>(ENDPOINTS.QR.BY_ID(id), data);
      return adaptQRCode(response);
    } catch (error) {
      return handleServiceError<QRCodeResource>(error, 'Update QR code');
    }
  },
  
  /**
   * Delete a QR code
   */
  delete: async (id: string): Promise<void> => {
    try {
      await api.delete(ENDPOINTS.QR.BY_ID(id));
    } catch (error) {
      return handleServiceError<void>(error, 'Delete QR code');
    }
  },
};

/**
 * QR Batch operations
 */
const qrBatchService = {
  /**
   * Create multiple QR codes in a single request
   */
  batchCreate: async (requests: QRCodeCreateRequest[]): Promise<QRCodeResource[]> => {
    try {
      const response = await api.post<QRCode[]>(ENDPOINTS.QR.BATCH, { qr_codes: requests });
      return response.map(adaptQRCode);
    } catch (error) {
      return handleServiceError<QRCodeResource[]>(error, 'Batch create QR codes');
    }
  },
  
  /**
   * Update multiple QR codes in a single request
   */
  batchUpdate: async (updates: { id: string; data: QRCodeUpdateRequest }[]): Promise<QRCodeResource[]> => {
    try {
      const payload = updates.map(({ id, data }) => ({ id, ...data }));
      const response = await api.patch<QRCode[]>(ENDPOINTS.QR.BATCH, { qr_codes: payload });
      return response.map(adaptQRCode);
    } catch (error) {
      return handleServiceError<QRCodeResource[]>(error, 'Batch update QR codes');
    }
  },
  
  /**
   * Delete multiple QR codes in a single request
   */
  batchDelete: async (ids: string[]): Promise<void> => {
    try {
      await api.delete(ENDPOINTS.QR.BATCH, { data: { ids } });
    } catch (error) {
      return handleServiceError<void>(error, 'Batch delete QR codes');
    }
  },
  
  /**
   * Get the status of a batch processing job
   * 
   * @param batchId - The ID of the batch to check status for
   * @returns Promise with batch status information
   */
  getBatchStatus: async (batchId: string): Promise<{
    batch_id: string;
    status: 'pending' | 'processing' | 'completed' | 'failed';
    processed_items: number;
    total_items: number;
    errors?: string[];
  }> => {
    try {
      const response = await api.get<{
        batch_id: string;
        status: 'pending' | 'processing' | 'completed' | 'failed';
        processed_items: number;
        total_items: number;
        errors?: string[];
      }>(`${ENDPOINTS.QR.BATCH}/${batchId}/status`);
      return response;
    } catch (error) {
      return handleServiceError<{
        batch_id: string;
        status: 'pending' | 'processing' | 'completed' | 'failed';
        processed_items: number;
        total_items: number;
        errors?: string[];
      }>(error, 'Get batch status');
    }
  },
  
  /**
   * Download a batch of QR codes as a ZIP file
   * 
   * @param batchId - The ID of the batch to download
   * @param format - The format of QR codes to download (png, svg, pdf)
   * @returns Promise with download blob
   */
  downloadBatch: async (batchId: string, format: 'png' | 'svg' | 'pdf' = 'png'): Promise<Blob> => {
    try {
      return await api.get(`${ENDPOINTS.QR.BATCH}/${batchId}/download`, {
        params: { format },
        responseType: 'blob',
      });
    } catch (error) {
      return handleServiceError<Blob>(error, 'Download batch');
    }
  }
};

/**
 * Social QR service with specific endpoints and methods
 */
const socialQRService = {
  /**
   * Get all social QR codes
   */
  getAllSocialQRs: async (): Promise<SocialQR[]> => {
    try {
      return await api.get<SocialQR[]>(ENDPOINTS.QR.SOCIAL.BASE);
    } catch (error) {
      return handleServiceError<SocialQR[]>(error, 'Get social QR codes');
    }
  },
  
  /**
   * Get a social QR code by ID
   */
  getSocialQR: async (id: string): Promise<SocialQR> => {
    try {
      return await api.get<SocialQR>(ENDPOINTS.QR.SOCIAL.BY_ID(id));
    } catch (error) {
      return handleServiceError<SocialQR>(error, 'Get social QR code');
    }
  },
  
  /**
   * Create a new social QR code
   * 
   * @param data - Social QR code data
   * @param profileId - Brand profile ID
   * @returns Promise with created social QR code
   */
  createSocialQR: async (data: SocialQRData, profileId: number): Promise<SocialQR> => {
    try {
      const createData: CreateSocialQRInput = {
        ...data,
        branding: {
          profile_id: profileId
        }
      };
      
      return await api.post<SocialQR>(ENDPOINTS.QR.SOCIAL.BASE, createData);
    } catch (error) {
      return handleServiceError<SocialQR>(error, 'Create social QR code');
    }
  },
  
  /**
   * Update an existing social QR code
   * 
   * @param id - QR code ID
   * @param data - Updated social QR code data
   * @param profileId - Brand profile ID
   * @returns Promise with updated social QR code
   */
  updateSocialQR: async (id: string, data: SocialQRData, profileId: number): Promise<SocialQR> => {
    try {
      const updateData: UpdateSocialQRInput = {
        ...data,
        branding: {
          profile_id: profileId
        }
      };
      
      return await api.patch<SocialQR>(ENDPOINTS.QR.SOCIAL.BY_ID(id), updateData);
    } catch (error) {
      return handleServiceError<SocialQR>(error, 'Update social QR code');
    }
  },
  
  /**
   * Delete a social QR code
   * 
   * @param id - QR code ID
   * @returns Promise resolved when deleted
   */
  deleteSocialQR: async (id: string): Promise<void> => {
    try {
      await api.delete(ENDPOINTS.QR.SOCIAL.BY_ID(id));
    } catch (error) {
      return handleServiceError<void>(error, 'Delete social QR code');
    }
  },
};


/**
 * QR Analytics service
 */
const qrAnalyticsService = {
  /**
   * Get analytics for a QR code
   * 
   * @param id - QR code ID
   * @returns Promise with QR code analytics
   */
  getQRAnalytics: async (id: string): Promise<any> => {
    try {
      return await api.get(ENDPOINTS.QR.ANALYTICS(id));
    } catch (error) {
      return handleServiceError<any>(error, 'Get QR analytics');
    }
  },
  
  /**
   * Get analytics for all QR codes
   * 
   * @returns Promise with QR code analytics overview
   */
  getQRAnalyticsOverview: async (): Promise<any> => {
    try {
      return await api.get(`${ENDPOINTS.QR.BASE}/analytics`);
    } catch (error) {
      return handleServiceError<any>(error, 'Get QR analytics overview');
    }
  }
};

/**
 * QR Metadata service
 */
const qrMetadataService = {
  /**
   * Update QR code metadata (title, description, etc.)
   * 
   * @param id - QR code ID
   * @param metadata - Metadata to update
   * @returns Promise with updated QR code
   */
  async updateQRMetadata(id: string, metadata: { title?: string; description?: string }): Promise<QRCodeResource> {
    try {
      const response = await api.patch<QRResponse>(`${ENDPOINTS.QR}/${id}/metadata`, metadata);
      return adaptQRCode(response.data.data);
    } catch (error) {
      return handleServiceError(error, 'updating QR metadata');
    }
  },
};

/**
 * QR Download service
 */
const qrDownloadService = {
  /**
   * Download a QR code with format options
   * 
   * @param id - QR code ID
   * @param format - Download format (png, svg, pdf)
   * @param options - Additional download options (width, height, dpi, etc.)
   * @returns Promise with download blob
   */
  downloadQR: async (id: string, format: string = 'png', options: QRDownloadOptions = {}): Promise<Blob> => {
    try {
      // Build URL with format and options
      const url = `${ENDPOINTS.QR.EXPORT}/${id}?format=${format}`;
      
      // Add download options as query params
      const params = new URLSearchParams();
      
      if (options.width) {
        params.append('width', options.width.toString());
      }
      
      if (options.height) {
        params.append('height', options.height.toString());
      }
      
      if (options.dpi) {
        params.append('dpi', options.dpi.toString());
      }
      
      // Determine the appropriate responseType based on format
      let responseType: 'blob' | 'arraybuffer' | 'json' = 'blob';
      
      if (format === 'png' || format === 'jpg' || format === 'jpeg' || format === 'webp' || format === 'pdf') {
        responseType = 'arraybuffer';
      } else if (format === 'svg') {
        responseType = 'blob';
      } else if (format === 'json') {
        responseType = 'json';
      }
      
      // Make the request with proper responseType
      const response = await api.get<ArrayBuffer | Blob | any>(`${url}&${params.toString()}`, {
        responseType
      });
      
      // Process response based on type
      if (response instanceof ArrayBuffer) {
        // Convert ArrayBuffer to Blob with appropriate MIME type
        const mimeTypes: Record<string, string> = {
          'png': 'image/png',
          'jpg': 'image/jpeg',
          'jpeg': 'image/jpeg',
          'webp': 'image/webp',
          'pdf': 'application/pdf'
        };
        
        const mimeType = mimeTypes[format] || 'application/octet-stream';
        return new Blob([response], { type: mimeType });
      } else if (response instanceof Blob) {
        // Return Blob directly
        return response;
      } else if (typeof response === 'object' && response !== null) {
        // For JSON responses, convert to Blob
        const jsonStr = JSON.stringify(response);
        return new Blob([jsonStr], { type: 'application/json' });
      } else {
        // Fallback for unexpected response types
        throw new Error(`Unexpected response format for QR download: ${typeof response}`);
      }
    } catch (error) {
      console.error('Error downloading QR code:', error);
      throw error;
    }
  },
  
  /**
   * Download a QR code optimized for social media
   * 
   * @param id - QR code ID
   * @param platform - Social media platform (instagram, twitter, facebook, etc.)
   * @param customOptions - Optional custom export options
   * @returns Promise with download blob
   */
  downloadQRForSocial: async (id: string, platform: string, customOptions?: Record<string, any>): Promise<Blob> => {
    try {
      const params = { platform };
      const body = customOptions || {};
      
      return await api.post<Blob>(
        `${ENDPOINTS.QR.SOCIAL_DOWNLOAD(id)}`,
        body,
        { responseType: 'blob' }
      );
    } catch (error) {
      return handleServiceError<Blob>(error, `Download QR for ${platform}`);
    }
  },
  
  /**
   * Download a QR code optimized for print materials
   * 
   * @param id - QR code ID
   * @param printType - Print type (business_card, flyer, poster, sticker, etc.)
   * @param customOptions - Optional custom print options
   * @returns Promise with download blob
   */
  downloadQRForPrint: async (id: string, printType: string, customOptions?: Record<string, any>): Promise<Blob> => {
    try {
      const params = { print_type: printType };
      const body = customOptions || {};
      
      return await api.post<Blob>(
        `${ENDPOINTS.QR.PRINT_DOWNLOAD(id)}`,
        body,
        { responseType: 'blob' }
      );
    } catch (error) {
      return handleServiceError<Blob>(error, `Download print-ready QR for ${printType}`);
    }
  },
  
  /**
   * Export QR code with advanced options
   * 
   * @param id - QR code ID
   * @param options - Export options including format, resolution and design overrides
   * @returns Promise with export blob
   */
  exportQR: async (id: string, options: {
    format: string;
    resolution?: number;
    overrides?: Record<string, any>;
  }): Promise<Blob> => {
    try {
      return await api.post<Blob>(ENDPOINTS.QR.ENHANCED_EXPORT(id), options, {
        responseType: 'blob',
      });
    } catch (error) {
      return handleServiceError<Blob>(error, 'Export QR code');
    }
  },
  
  /**
   * Export a batch of QR codes
   * 
   * @param id - QR code ID
   * @param options - Batch export options
   * @returns Promise with batch export response
   */
  batchExportQR: async (id: string, options: {
    formats: string[];
    resolution?: number;
    includeMetadata?: boolean;
  }): Promise<Blob> => {
    try {
      return await api.post<Blob>(ENDPOINTS.QR.BATCH_EXPORT(id), options, {
        responseType: 'blob',
      });
    } catch (error) {
      return handleServiceError<Blob>(error, 'Batch export QR codes');
    }
  }
};

/**
 * Enhanced QR Service with AI capabilities
 */
const enhancedQRService = {
  /**
   * Generate an enhanced QR code
   * 
   * @param request - Enhanced QR code generation request
   * @returns Promise with the generated enhanced QR code
   */
  generateEnhancedQR: async (request: EnhancedQRGenerateRequest): Promise<EnhancedQR> => {
    try {
      const response = await api.post<EnhancedQR>('/enhanced-qr', request);
      return adaptEnhancedQR(response);
    } catch (error) {
      return handleServiceError<EnhancedQR>(error, 'Generate enhanced QR code');
    }
  },

  /**
   * Get an enhanced QR code by ID
   * 
   * @param id - Enhanced QR code ID
   * @returns Promise with the enhanced QR code
   */
  getEnhancedQR: async (id: string): Promise<EnhancedQR> => {
    try {
      const response = await api.get<EnhancedQR>(`/enhanced-qr/${id}`);
      return adaptEnhancedQR(response);
    } catch (error) {
      return handleServiceError<EnhancedQR>(error, 'Get enhanced QR code');
    }
  },

  /**
   * Update an enhanced QR code
   * 
   * @param id - Enhanced QR code ID
   * @param request - Enhanced QR code update request
   * @returns Promise with the updated enhanced QR code
   */
  updateEnhancedQR: async (id: string, request: EnhancedQRUpdateRequest): Promise<EnhancedQR> => {
    try {
      const response = await api.patch<EnhancedQR>(`/enhanced-qr/${id}`, request);
      return adaptEnhancedQR(response);
    } catch (error) {
      return handleServiceError<EnhancedQR>(error, 'Update enhanced QR code');
    }
  },

  /**
   * Delete an enhanced QR code
   * 
   * @param id - Enhanced QR code ID
   * @returns Promise with success status
   */
  deleteEnhancedQR: async (id: string): Promise<boolean> => {
    try {
      await api.delete(`/enhanced-qr/${id}`);
      return true;
    } catch (error) {
      return handleServiceError<boolean>(error, 'Delete enhanced QR code');
    }
  },

  /**
   * Enhance a QR code with AI features
   * 
   * @param qrImage - QR code image to enhance
   * @param enhancementOptions - Options for AI enhancement
   * @returns Promise with enhanced QR
   */
  enhanceQR: async (qrImage: File, enhancementOptions: Record<string, any>): Promise<APIResponse<any>> => {
    try {
      const formData = new FormData();
      formData.append('qr_image', qrImage);
      formData.append('enhancement_options', JSON.stringify(enhancementOptions));
      
      const response = await api.post<APIResponse<any>>(ENDPOINTS.QR.AI_QR.ENHANCE, formData, {
        headers: {
          'Content-Type': 'multipart/form-data',
        },
      });
      return response;
    } catch (error) {
      return handleServiceError<APIResponse<any>>(error, 'Enhance QR with AI');
    }
  },

  /**
   * Get AI-powered content suggestions for a specific QR type
   * 
   * @param qrType - Type of QR code (url, vcard, wifi, email, event, etc.)
   * @param partialContent - Partially completed content for the QR code
   * @returns Promise with content suggestions
   */
  getContentSuggestions: async (
    qrType: string,
    partialContent: Record<string, any> = {}
  ): Promise<ContentSuggestionResponse> => {
    try {
      const response = await api.post<ContentSuggestionResponse>(ENDPOINTS.QR.AI_QR.CONTENT_SUGGEST, {
        qr_type: qrType,
        partial_content: partialContent
      });
      return response;
    } catch (error) {
      return handleServiceError<ContentSuggestionResponse>(error, 'Get content suggestions');
    }
  },

  /**
   * Get AI-powered design recommendations for a QR code
   * 
   * @param qrType - Type of QR code (url, vcard, wifi, email, event, etc.)
   * @param qrContent - Content for the QR code
   * @param brandColors - Optional list of brand color hex codes
   * @param purpose - Optional purpose of the QR code (print, digital, etc.)
   * @returns Promise with design recommendations
   */
  getDesignRecommendations: async (
    qrType: string,
    qrContent: Record<string, any>,
    brandColors?: string[],
    purpose?: string
  ): Promise<DesignRecommendationResponse> => {
    try {
      const response = await api.post<DesignRecommendationResponse>(ENDPOINTS.QR.AI_QR.DESIGN_RECOMMEND, {
        qr_type: qrType,
        content: qrContent,
        brand_colors: brandColors,
        purpose
      });
      return response;
    } catch (error) {
      return handleServiceError<DesignRecommendationResponse>(error, 'Get design recommendations');
    }
  },

  /**
   * Simulate QR code scanning in different environments
   * 
   * @param qrImageFile - QR code image file
   * @param options - Simulation options
   * @returns Promise with simulation results
   */
  simulateQRScanning: async (
    qrImageFile: File,
    options: ScanSimulationOptions = {}
  ): Promise<ScanSimulationResponse> => {
    try {
      const formData = new FormData();
      formData.append('qr_image', qrImageFile);
      formData.append('options', JSON.stringify(options));
      
      const response = await api.post<ScanSimulationResponse>(ENDPOINTS.QR.AI_QR.SIMULATE_SCANNING, formData, {
        headers: {
          'Content-Type': 'multipart/form-data'
        }
      });
      return response;
    } catch (error) {
      return handleServiceError<ScanSimulationResponse>(error, 'Simulate QR scanning');
    }
  },

  /**
   * Create a QR code from natural language description
   * 
   * @param text - Natural language description of desired QR code
   * @returns Promise with parsed QR parameters
   */
  createQRFromNaturalLanguage: async (
    text: string
  ): Promise<NaturalLanguageResponse> => {
    try {
      const response = await api.post<NaturalLanguageResponse>(ENDPOINTS.QR.AI_QR.NATURAL_LANGUAGE, {
        text
      });
      return response;
    } catch (error) {
      return handleServiceError<NaturalLanguageResponse>(error, 'Create QR from natural language');
    }
  },

  /**
   * Optimize a QR code based on identified issues
   * 
   * @param qrImageFile - QR code image file
   * @param issues - List of identified issues
   * @returns Promise with optimized QR code
   */
  optimizeQRCode: async (
    qrImageFile: File,
    issues: string[]
  ): Promise<OptimizationResponse> => {
    try {
      const formData = new FormData();
      formData.append('qr_image', qrImageFile);
      formData.append('issues', JSON.stringify(issues));
      
      const response = await api.post<OptimizationResponse>(ENDPOINTS.QR.AI_QR.OPTIMIZE, formData, {
        headers: {
          'Content-Type': 'multipart/form-data'
        }
      });
      return response;
    } catch (error) {
      return handleServiceError<OptimizationResponse>(error, 'Optimize QR code');
    }
  }
};

/**
 * Tweet QR service methods
 */
const tweetQRService = {
  /**
   * Get all Tweet QR codes
   */
  getAllTweetQRs: async (): Promise<TweetQR[]> => {
    try {
      const response = await api.get<{ success: boolean; data: TweetQR[]; meta: any }>(ENDPOINTS.QR.TWEET.BASE);
      
      // Handle standardized response format
      if (response.success) {
        return response.data;
      } else {
        throw new Error(response.meta?.error?.message || 'Failed to get Tweet QR codes');
      }
    } catch (error) {
      return handleServiceError<TweetQR[]>(error, 'Get Tweet QR codes');
    }
  },
  
  /**
   * Get a Tweet QR code by ID
   */
  getTweetQR: async (id: string): Promise<TweetQR> => {
    try {
      const response = await api.get<{ success: boolean; data: TweetQR; meta: any }>(ENDPOINTS.QR.TWEET.BY_ID(id));
      
      // Handle standardized response format
      if (response.success) {
        return response.data;
      } else {
        throw new Error(response.meta?.error?.message || 'Failed to get Tweet QR code');
      }
    } catch (error) {
      return handleServiceError<TweetQR>(error, 'Get Tweet QR code');
    }
  },
  
  /**
   * Create a new Tweet QR code
   * 
   * @param data - Tweet QR code data
   * @returns Promise with created Tweet QR code
   */
  createTweetQR: async (data: TweetQRData): Promise<TweetQR> => {
    try {
      const response = await api.post<{ success: boolean; data: TweetQR; meta: any }>(ENDPOINTS.QR.TWEET.BASE, data);
      
      // Handle standardized response format
      if (response.success) {
        return response.data;
      } else {
        throw new Error(response.meta?.error?.message || 'Failed to create Tweet QR code');
      }
    } catch (error) {
      return handleServiceError<TweetQR>(error, 'Create Tweet QR code');
    }
  },
  
  /**
   * Update an existing Tweet QR code
   * 
   * @param id - QR code ID
   * @param data - Updated Tweet QR code data
   * @returns Promise with updated Tweet QR code
   */
  updateTweetQR: async (id: string, data: Partial<TweetQRData>): Promise<TweetQR> => {
    try {
      const response = await api.patch<{ success: boolean; data: TweetQR; meta: any }>(
        `${ENDPOINTS.QR.TWEET.BY_ID(id)}/update`, 
        data
      );
      
      // Handle standardized response format
      if (response.success) {
        return response.data;
      } else {
        throw new Error(response.meta?.error?.message || 'Failed to update Tweet QR code');
      }
    } catch (error) {
      return handleServiceError<TweetQR>(error, 'Update Tweet QR code');
    }
  },
  
  /**
   * Delete a Tweet QR code
   * 
   * @param id - QR code ID
   * @returns Promise resolved when deleted
   */
  deleteTweetQR: async (id: string): Promise<void> => {
    try {
      const response = await api.delete<{ success: boolean; data: null; meta: any }>(
        `${ENDPOINTS.QR.TWEET.BY_ID(id)}/delete`
      );
      
      // Handle standardized response format
      if (!response.success) {
        throw new Error(response.meta?.error?.message || 'Failed to delete Tweet QR code');
      }
    } catch (error) {
      return handleServiceError<void>(error, 'Delete Tweet QR code');
    }
  },
  
  /**
   * Get analytics for a Tweet QR code
   * 
   * @param id - QR code ID
   * @param startDate - Optional start date for analytics
   * @param endDate - Optional end date for analytics
   * @returns Promise with QR analytics
   */
  getTweetQRAnalytics: async (id: string, startDate?: string, endDate?: string): Promise<any> => {
    try {
      const params: Record<string, string> = {};
      if (startDate) params.start_date = startDate;
      if (endDate) params.end_date = endDate;
      
      const response = await api.get<{ success: boolean; data: any; meta: any }>(
        `${ENDPOINTS.QR.TWEET.ANALYTICS(id)}`,
        { params }
      );
      
      // Handle standardized response format
      if (response.success) {
        return response.data;
      } else {
        throw new Error(response.meta?.error?.message || 'Failed to get Tweet QR analytics');
      }
    } catch (error) {
      return handleServiceError<any>(error, 'Get Tweet QR analytics');
    }
  },
  
  /**
   * Download a Tweet QR code
   * 
   * @param id - QR code ID
   * @param format - Download format (png, svg, pdf)
   * @returns Promise with download URL
   */
  downloadTweetQR: async (id: string, format: 'png' | 'svg' | 'pdf' = 'png'): Promise<string> => {
    try {
      const response = await api.get<{ success: boolean; data: { download_url: string }; meta: any }>(
        `${ENDPOINTS.QR.TWEET.DOWNLOAD(id)}?format=${format}`
      );
      
      // Handle standardized response format
      if (response.success) {
        return response.data.download_url;
      } else {
        throw new Error(response.meta?.error?.message || 'Failed to download Tweet QR code');
      }
    } catch (error) {
      return handleServiceError<string>(error, 'Download Tweet QR code');
    }
  },
  
  /**
   * Export a Tweet QR code with advanced options
   * 
   * @param id - QR code ID
   * @param options - Export options
   * @returns Promise with export result
   */
  exportTweetQR: async (
    id: string, 
    options: { 
      format?: string; 
      size?: number; 
      include_analytics?: boolean 
    }
  ): Promise<Blob> => {
    try {
      const response = await api.post<Blob>(
        `${ENDPOINTS.QR.TWEET.EXPORT(id)}`,
        options,
        { responseType: 'blob' }
      );
      
      return response;
    } catch (error) {
      return handleServiceError<Blob>(error, 'Export Tweet QR code');
    }
  },
};

/**
 * Social Profile QR service methods
 */
const socialProfileQRService = {
  /**
   * Get all Social Profile QR codes
   */
  getAllSocialProfileQRs: async (): Promise<SocialProfileQR[]> => {
    try {
      const response = await api.get<{ success: boolean; data: SocialProfileQR[]; meta: any }>(
        ENDPOINTS.QR.SOCIAL_PROFILE.BASE
      );
      
      // Handle standardized response format
      if (response.success) {
        return response.data;
      } else {
        throw new Error(response.meta?.error?.message || 'Failed to get Social Profile QR codes');
      }
    } catch (error) {
      return handleServiceError<SocialProfileQR[]>(error, 'Get Social Profile QR codes');
    }
  },
  
  /**
   * Get a Social Profile QR code by ID
   */
  getSocialProfileQR: async (id: string): Promise<SocialProfileQR> => {
    try {
      const response = await api.get<{ success: boolean; data: SocialProfileQR; meta: any }>(
        ENDPOINTS.QR.SOCIAL_PROFILE.BY_ID(id)
      );
      
      // Handle standardized response format
      if (response.success) {
        return response.data;
      } else {
        throw new Error(response.meta?.error?.message || 'Failed to get Social Profile QR code');
      }
    } catch (error) {
      return handleServiceError<SocialProfileQR>(error, 'Get Social Profile QR code');
    }
  },
  
  /**
   * Create a new Social Profile QR code
   * 
   * @param data - Social Profile QR code data
   * @returns Promise with created Social Profile QR code
   */
  createSocialProfileQR: async (data: SocialProfileQRData): Promise<SocialProfileQR> => {
    try {
      const response = await api.post<{ success: boolean; data: SocialProfileQR; meta: any }>(
        ENDPOINTS.QR.SOCIAL_PROFILE.BASE,
        data
      );
      
      // Handle standardized response format
      if (response.success) {
        return response.data;
      } else {
        throw new Error(response.meta?.error?.message || 'Failed to create Social Profile QR code');
      }
    } catch (error) {
      return handleServiceError<SocialProfileQR>(error, 'Create Social Profile QR code');
    }
  },
  
  /**
   * Update an existing Social Profile QR code
   * 
   * @param id - QR code ID
   * @param data - Updated Social Profile QR code data
   * @returns Promise with updated Social Profile QR code
   */
  updateSocialProfileQR: async (id: string, data: Partial<SocialProfileQRData>): Promise<SocialProfileQR> => {
    try {
      const response = await api.patch<{ success: boolean; data: SocialProfileQR; meta: any }>(
        `${ENDPOINTS.QR.SOCIAL_PROFILE.BY_ID(id)}/update`,
        data
      );
      
      // Handle standardized response format
      if (response.success) {
        return response.data;
      } else {
        throw new Error(response.meta?.error?.message || 'Failed to update Social Profile QR code');
      }
    } catch (error) {
      return handleServiceError<SocialProfileQR>(error, 'Update Social Profile QR code');
    }
  },
  
  /**
   * Delete a Social Profile QR code
   * 
   * @param id - QR code ID
   * @returns Promise resolved when deleted
   */
  deleteSocialProfileQR: async (id: string): Promise<void> => {
    try {
      const response = await api.delete<{ success: boolean; data: null; meta: any }>(
        `${ENDPOINTS.QR.SOCIAL_PROFILE.BY_ID(id)}/delete`
      );
      
      // Handle standardized response format
      if (!response.success) {
        throw new Error(response.meta?.error?.message || 'Failed to delete Social Profile QR code');
      }
    } catch (error) {
      return handleServiceError<void>(error, 'Delete Social Profile QR code');
    }
  },
  
  /**
   * Get analytics for a Social Profile QR code
   * 
   * @param id - QR code ID
   * @param startDate - Optional start date for analytics
   * @param endDate - Optional end date for analytics
   * @returns Promise with QR analytics
   */
  getSocialProfileQRAnalytics: async (id: string, startDate?: string, endDate?: string): Promise<any> => {
    try {
      const params: Record<string, string> = {};
      if (startDate) params.start_date = startDate;
      if (endDate) params.end_date = endDate;
      
      const response = await api.get<{ success: boolean; data: any; meta: any }>(
        `${ENDPOINTS.QR.SOCIAL_PROFILE.ANALYTICS(id)}`,
        { params }
      );
      
      // Handle standardized response format
      if (response.success) {
        return response.data;
      } else {
        throw new Error(response.meta?.error?.message || 'Failed to get Social Profile QR analytics');
      }
    } catch (error) {
      return handleServiceError<any>(error, 'Get Social Profile QR analytics');
    }
  },
  
  /**
   * Download a Social Profile QR code
   * 
   * @param id - QR code ID
   * @param format - Download format (png, svg, pdf)
   * @returns Promise with download URL
   */
  downloadSocialProfileQR: async (id: string, format: 'png' | 'svg' | 'pdf' = 'png'): Promise<string> => {
    try {
      const response = await api.get<{ success: boolean; data: { download_url: string }; meta: any }>(
        `${ENDPOINTS.QR.SOCIAL_PROFILE.DOWNLOAD(id)}?format=${format}`
      );
      
      // Handle standardized response format
      if (response.success) {
        return response.data.download_url;
      } else {
        throw new Error(response.meta?.error?.message || 'Failed to download Social Profile QR code');
      }
    } catch (error) {
      return handleServiceError<string>(error, 'Download Social Profile QR code');
    }
  },
  
  /**
   * Export a Social Profile QR code with advanced options
   * 
   * @param id - QR code ID
   * @param options - Export options
   * @returns Promise with export result
   */
  exportSocialProfileQR: async (
    id: string, 
    options: { 
      format?: string; 
      size?: number; 
      include_analytics?: boolean 
    }
  ): Promise<Blob> => {
    try {
      const response = await api.post<Blob>(
        `${ENDPOINTS.QR.SOCIAL_PROFILE.EXPORT(id)}`,
        options,
        { responseType: 'blob' }
      );
      
      return response;
    } catch (error) {
      return handleServiceError<Blob>(error, 'Export Social Profile QR code');
    }
  },
};

/**
 * Poster generation service methods
 */
const posterService = {
  /**
   * Generate a branded PDF poster with QR code
   * 
   * @param qrId - QR code ID to include in the poster
   * @param options - Poster customization options
   * @returns Promise with poster URL
   */
  generatePoster: async (
    qrId: string,
    options: {
      title?: string;
      subtitle?: string;
      logo?: File;
      templateId?: string;
    }
  ): Promise<string> => {
    try {
      const formData = new FormData();
      formData.append('qr_id', qrId);
      
      if (options.title) formData.append('title', options.title);
      if (options.subtitle) formData.append('subtitle', options.subtitle);
      if (options.logo) formData.append('logo', options.logo);
      if (options.templateId) formData.append('template_id', options.templateId);
      
      const response = await api.post<{ success: boolean; data: { poster_url: string }; meta: any }>(
        ENDPOINTS.POSTER.GENERATE,
        formData,
        {
          headers: {
            'Content-Type': 'multipart/form-data'
          }
        }
      );
      
      // Handle standardized response format
      if (response.success) {
        return response.data.poster_url;
      } else {
        throw new Error(response.meta?.error?.message || 'Failed to generate poster');
      }
    } catch (error) {
      return handleServiceError<string>(error, 'Generate poster');
    }
  },
  
  /**
   * Get available poster templates
   * 
   * @returns Promise with poster templates
   */
  getPosterTemplates: async (): Promise<any[]> => {
    try {
      const response = await api.get<{ success: boolean; data: any[]; meta: any }>(
        ENDPOINTS.POSTER.TEMPLATES
      );
      
      // Handle standardized response format
      if (response.success) {
        return response.data;
      } else {
        throw new Error(response.meta?.error?.message || 'Failed to get poster templates');
      }
    } catch (error) {
      return handleServiceError<any[]>(error, 'Get poster templates');
    }
  },
  
  /**
   * Get a specific poster template by ID
   * 
   * @param id - Template ID
   * @returns Promise with poster template
   */
  getPosterTemplate: async (id: string): Promise<any> => {
    try {
      const response = await api.get<{ success: boolean; data: any; meta: any }>(
        ENDPOINTS.POSTER.TEMPLATE_BY_ID(id)
      );
      
      // Handle standardized response format
      if (response.success) {
        return response.data;
      } else {
        throw new Error(response.meta?.error?.message || 'Failed to get poster template');
      }
    } catch (error) {
      return handleServiceError<any>(error, 'Get poster template');
    }
  },
  
  /**
   * Download a generated poster
   * 
   * @param id - Poster ID
   * @returns Promise with download URL
   */
  downloadPoster: async (id: string): Promise<string> => {
    try {
      const response = await api.get<{ success: boolean; data: { download_url: string }; meta: any }>(
        ENDPOINTS.POSTER.DOWNLOAD(id)
      );
      
      // Handle standardized response format
      if (response.success) {
        return response.data.download_url;
      } else {
        throw new Error(response.meta?.error?.message || 'Failed to download poster');
      }
    } catch (error) {
      return handleServiceError<string>(error, 'Download poster');
    }
  }
};

/**
 * Combined QR Service with all methods
 */
export const QRService = {
  ...baseQRService,
  ...qrBatchService,
  ...socialQRService,
  ...qrAnalyticsService,
  ...qrDownloadService,
  ...qrMetadataService,
  ...enhancedQRService,
  ...qrPreviewService,
  ...tweetQRService,
  ...socialProfileQRService,
  ...posterService,
  transformDataForApi
};

export default QRService;