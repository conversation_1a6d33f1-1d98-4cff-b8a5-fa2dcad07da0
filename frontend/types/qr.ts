// QR type definition - all available QR code types
// Synced with backend QRType enum in backend/models/qr.py
export type QRType =
  // Basic Types
  | 'url'                  // Standard URL link
  | 'vcard'                // Contact information
  | 'text'                 // Plain text content
  | 'email'                // Email address with optional subject/body
  | 'sms'                  // Text message with number and content
  | 'phone'                // Phone number for calling
    
  // Network Types
  | 'wifi'                 // WiFi network connection details
  | 'zoom'                 // Zoom meeting link
  | 'meet'                 // Google Meet link
  | 'teams'                // Microsoft Teams meeting
  | 'webex'                // Cisco Webex meeting
    
  // Social Media
  | 'x'                    // X (formerly Twitter)
  | 'whatsapp'             // WhatsApp chat or contact
  | 'whatsapp_business'    // WhatsApp Business chat
  | 'instagram'            // Instagram profile
  | 'linkedin'             // LinkedIn profile
  | 'facebook'             // Facebook profile or page
  | 'twitter'              // Legacy support
  | 'tiktok'               // TikTok profile
  | 'discord'              // Discord server or profile
  | 'snapchat'             // Snapchat profile
  | 'pinterest'            // Pinterest profile or pin
  | 'threads'              // Meta's Threads platform
  | 'bereal'               // BeReal profile
  | 'line'                 // Line messaging (popular in Japan, Thailand)
  | 'kakao'                // KakaoTalk (popular in South Korea)
  | 'wechat'               // WeChat (popular in China)
  | 'weibo'                // Weibo (Chinese microblogging)
    
  
  // Business & Events
  | 'event'                // Event details and calendar integration
  | 'ticket'               // Event ticket with verification
  | 'coupon'               // Discount or promotional offer
  | 'menu'                 // Restaurant or service menu
  | 'product'              // Product information and details
  | 'digital_business_card' // Enhanced digital business card
  | 'employee_badge'       // Corporate ID and access
  | 'review'               // Direct to review platform
    
  // App Links
  | 'app_store'            // iOS App Store link
  | 'google_play'          // Google Play Store link
  | 'app_gallery'          // Huawei App Gallery link
  | 'universal_app_link'   // Universal app link with fallbacks
    
  // Location Based
  | 'geo'                  // Geographic coordinates
  | 'navigation'           // Directions to a location
  | 'store_locator'        // Find nearest store/service
  | 'map_route'            // Pre-defined route on a map
  | 'indoor_navigation'    // Indoor building navigation
    
  // Payments & Finance
  | 'payment'              // Payment processing QR
  | 'invoice'              // Invoice details
  | 'bank_transfer'        // Bank transfer details
  | 'crypto_wallet'        // Cryptocurrency wallet address
  | 'nft'                  // Non-fungible token
  | 'smart_contract'       // Blockchain smart contract
  | 'web3_dapp'            // Web3 decentralized application
  | 'loyalty_card'         // Customer loyalty program
  | 'subscription'         // Recurring payment/subscription
    
  // Files & Media
  | 'file'                 // Generic file download
  | 'video'                // Video content
  | 'audio'                // Audio content
  | 'document'             // Document file
  | 'gallery'              // Image gallery or collection
  | 'cloud_storage'        // Link to cloud storage
    
  // Multi-format Codes
  | 'data_matrix'          // Data Matrix code format
  | 'aztec'                // Aztec code format
  | 'pdf417'               // PDF417 barcode format
    
  // Education
  | 'course'               // Course information or enrollment
  | 'certificate'          // Educational certification
  | 'assignment'           // Student assignment
  | 'training_material'    // Educational resources
  | 'student_id'           // Student identification
  | 'library_resource'     // Library catalog or resources
  | 'e_learning'           // Online learning platform link
  | 'quiz'                 // Interactive quiz or assessment
  | 'publication'          // Academic or research publication
  | 'dataset'              // Research or educational dataset
    
  // Healthcare
  | 'medical_record'       // Patient health record
  | 'prescription'         // Medication prescription
  | 'appointment'          // Medical appointment
  | 'lab_result'           // Laboratory test results
  | 'insurance_card'       // Health insurance information
  | 'telemedicine'         // Remote healthcare session
  | 'vaccination'          // Vaccination record
  | 'allergy_info'         // Allergy information
    
  // IoT & Devices
  | 'device_config'        // IoT device configuration
  | 'smart_home_control'   // Home automation
  | 'sensor_data'          // IoT sensor information
  | 'automation_rule'      // Automation recipe/rule
  | 'device_pairing'       // Device pairing information
  | 'ota_update'           // Over-the-air firmware update
    
  // Gaming
  | 'game_asset'           // In-game item or asset
  | 'achievement'          // Game achievement
  | 'virtual_item'         // Virtual goods or items
  | 'game_save'            // Game save data
  | 'tournament_entry'     // Gaming tournament
  | 'ar_experience'        // Augmented reality experience
  | 'vr_content'           // Virtual reality content
  | 'player_profile'       // Gamer profile
  | 'game_pass'            // Game membership or pass
    
  // Legal & Compliance
  | 'document_verification' // Document authenticity
  | 'compliance_cert'      // Compliance certification
  | 'legal_notice'         // Legal notification
  | 'audit_record'         // Audit information
  | 'regulatory_filing'    // Regulatory documents
  | 'contract'             // Contract document
  | 'smart_contract'       // Blockchain smart contract (finance category)
  | 'legal_smart_contract' // Legal blockchain smart contract
  | 'consent_form'         // Consent or waiver
  | 'citation'             // Legal citation
  | 'age_verification'     // Age verification system
    
  // Transportation
  | 'vehicle_registration' // Vehicle documentation
  | 'transit_pass'         // Public transportation pass
  | 'parking'              // Parking payment or information
  | 'maintenance_record'   // Maintenance history
  | 'route_schedule'       // Transportation schedule
  | 'fleet_tracking'       // Fleet vehicle tracking
  | 'boarding_pass'        // Travel boarding pass
  | 'ride_share'           // Ride sharing service
  | 'scooter_unlock'       // Micro-mobility unlock
    
  // International & Multi-language
  | 'multi_language'       // Content in multiple languages
  | 'region_specific'      // Region-dependent content
  | 'translation'          // Translation service
    
  // Enhanced Business Features
  | 'reservation'          // Service or accommodation booking
  | 'portfolio'            // Professional portfolio
  | 'menu_ordering'        // Restaurant ordering system
  | 'appointment_booking'  // Service scheduling
    
  // Advanced
  | 'app'                  // Custom application link/deep link
  | 'api'                  // API endpoint information
  | 'encrypted'            // Encrypted content
  | 'dynamic_logic'        // QR with embedded logic
  | 'multi_session'        // Progressive content delivery
  | 'offline_first'        // Optimized for offline use
  | 'mini_program';        // WeChat/Alipay mini program

// Subscription plan tiers
export type PlanTier = 'free' | 'starter' | 'growth' | 'enterprise';

// QR code categories aligned with backend model categories in backend/models/qr.py
export type QRCategory = 
  | 'basic'                // Basic Types (url, text, email, etc)
  | 'network'              // Network Types (wifi, zoom, meet, etc)
  | 'social_media'         // Social Media (instagram, facebook, x, etc)
  | 'business_basic'       // Business Basic (coupon, menu, product, review)
  | 'events_basic'         // Events Basic (event, ticket)
  | 'business_enhanced'    // Business Enhanced (digital_business_card, etc)
  | 'app_links'            // App Links 
  | 'location'             // Location Based
  | 'finance'              // Payments & Finance
  | 'media'                // Files & Media
  | 'multi_format'         // Multi-format Codes
  | 'education'            // Education
  | 'healthcare'           // Healthcare
  | 'iot'                  // IoT & Devices
  | 'gaming'               // Gaming
  | 'legal'                // Legal & Compliance
  | 'transportation'       // Transportation
  | 'international'        // International & Multi-language
  | 'advanced';            // Advanced

// QR type definition with metadata
export interface QRTypeDefinition {
  id: QRType;
  label: string;
  icon: string;
  description: string;
  category: QRCategory;
  minPlan: PlanTier;
  popularityRank?: number; // For highlighting popular options
}

// Category definition
export interface QRCategoryDefinition {
  id: QRCategory;
  label: string;
  icon: string;
  description: string;
  minPlan: PlanTier;
}

// QRTypeEnum provides constant values for all QR code types
// Synced with backend QRType enum in backend/models/qr.py
export const QRTypeEnum = {
  // Basic Types
  url: 'url' as QRType,
  vcard: 'vcard' as QRType,
  text: 'text' as QRType,
  email: 'email' as QRType,
  sms: 'sms' as QRType,
  phone: 'phone' as QRType,
  
  // Network Types
  wifi: 'wifi' as QRType,
  zoom: 'zoom' as QRType,
  meet: 'meet' as QRType,
  teams: 'teams' as QRType,
  webex: 'webex' as QRType,
  
  // Social Media
  x: 'x' as QRType,
  twitter: 'twitter' as QRType,
  whatsapp: 'whatsapp' as QRType,
  whatsapp_business: 'whatsapp_business' as QRType,
  instagram: 'instagram' as QRType,
  linkedin: 'linkedin' as QRType,
  facebook: 'facebook' as QRType,
  tiktok: 'tiktok' as QRType,
  discord: 'discord' as QRType,
  snapchat: 'snapchat' as QRType,
  pinterest: 'pinterest' as QRType,
  threads: 'threads' as QRType,
  bereal: 'bereal' as QRType,
  line: 'line' as QRType,
  kakao: 'kakao' as QRType,
  wechat: 'wechat' as QRType,
  weibo: 'weibo' as QRType,
  
  // Enhanced Social QR Types
  tweet_qr: 'tweet_qr' as QRType,
  social_profile: 'social_profile' as QRType,
  social_share: 'social_share' as QRType,
  
  // Business & Events
  event: 'event' as QRType,
  ticket: 'ticket' as QRType,
  coupon: 'coupon' as QRType,
  menu: 'menu' as QRType,
  product: 'product' as QRType,
  digital_business_card: 'digital_business_card' as QRType,
  employee_badge: 'employee_badge' as QRType,
  review: 'review' as QRType,
  
  // App Links
  app_store: 'app_store' as QRType,
  google_play: 'google_play' as QRType,
  app_gallery: 'app_gallery' as QRType,
  universal_app_link: 'universal_app_link' as QRType,
  
  // Location Based
  geo: 'geo' as QRType,
  navigation: 'navigation' as QRType,
  store_locator: 'store_locator' as QRType,
  map_route: 'map_route' as QRType,
  indoor_navigation: 'indoor_navigation' as QRType,
  
  // Payments & Finance
  payment: 'payment' as QRType,
  invoice: 'invoice' as QRType,
  bank_transfer: 'bank_transfer' as QRType,
  crypto_wallet: 'crypto_wallet' as QRType,
  nft: 'nft' as QRType,
  smart_contract: 'smart_contract' as QRType,
  web3_dapp: 'web3_dapp' as QRType,
  loyalty_card: 'loyalty_card' as QRType,
  subscription: 'subscription' as QRType,
  
  // Files & Media
  file: 'file' as QRType,
  video: 'video' as QRType,
  audio: 'audio' as QRType,
  document: 'document' as QRType,
  gallery: 'gallery' as QRType,
  cloud_storage: 'cloud_storage' as QRType,
  
  // Multi-format Codes
  data_matrix: 'data_matrix' as QRType,
  aztec: 'aztec' as QRType,
  pdf417: 'pdf417' as QRType,
  
  // Education
  course: 'course' as QRType,
  certificate: 'certificate' as QRType,
  assignment: 'assignment' as QRType,
  training_material: 'training_material' as QRType,
  student_id: 'student_id' as QRType,
  library_resource: 'library_resource' as QRType,
  e_learning: 'e_learning' as QRType,
  quiz: 'quiz' as QRType,
  
  // Healthcare
  medical_record: 'medical_record' as QRType,
  prescription: 'prescription' as QRType,
  appointment: 'appointment' as QRType,
  lab_result: 'lab_result' as QRType,
  insurance_card: 'insurance_card' as QRType,
  telemedicine: 'telemedicine' as QRType,
  vaccination: 'vaccination' as QRType,
  allergy_info: 'allergy_info' as QRType,
  
  // IoT & Devices
  device_config: 'device_config' as QRType,
  smart_home_control: 'smart_home_control' as QRType,
  sensor_data: 'sensor_data' as QRType,
  automation_rule: 'automation_rule' as QRType,
  device_pairing: 'device_pairing' as QRType,
  ota_update: 'ota_update' as QRType,
  
  // Gaming
  game_asset: 'game_asset' as QRType,
  achievement: 'achievement' as QRType,
  virtual_item: 'virtual_item' as QRType,
  game_save: 'game_save' as QRType,
  tournament_entry: 'tournament_entry' as QRType,
  ar_experience: 'ar_experience' as QRType,
  vr_content: 'vr_content' as QRType,
  player_profile: 'player_profile' as QRType,
  game_pass: 'game_pass' as QRType,
  
  // Legal & Compliance
  document_verification: 'document_verification' as QRType,
  compliance_cert: 'compliance_cert' as QRType,
  legal_notice: 'legal_notice' as QRType,
  audit_record: 'audit_record' as QRType,
  regulatory_filing: 'regulatory_filing' as QRType,
  contract: 'contract' as QRType,
  legal_smart_contract: 'legal_smart_contract' as QRType,
  certification: 'certification' as QRType,
  publication: 'publication' as QRType,
  dataset: 'dataset' as QRType,
  lab_equipment: 'lab_equipment' as QRType,
  research_protocol: 'research_protocol' as QRType,
  citation: 'citation' as QRType,
  consent_form: 'consent_form' as QRType,
  age_verification: 'age_verification' as QRType,
  
  // Transportation
  vehicle_registration: 'vehicle_registration' as QRType,
  transit_pass: 'transit_pass' as QRType,
  parking: 'parking' as QRType,
  maintenance_record: 'maintenance_record' as QRType,
  route_schedule: 'route_schedule' as QRType,
  fleet_tracking: 'fleet_tracking' as QRType,
  boarding_pass: 'boarding_pass' as QRType,
  ride_share: 'ride_share' as QRType,
  scooter_unlock: 'scooter_unlock' as QRType,
  
  // International & Multi-language
  multi_language: 'multi_language' as QRType,
  region_specific: 'region_specific' as QRType,
  translation: 'translation' as QRType,
  
  // Enhanced Business Features
  reservation: 'reservation' as QRType,
  portfolio: 'portfolio' as QRType,
  menu_ordering: 'menu_ordering' as QRType,
  appointment_booking: 'appointment_booking' as QRType,
  
  // Advanced
  app: 'app' as QRType,
  api: 'api' as QRType,
  encrypted: 'encrypted' as QRType,
  dynamic_logic: 'dynamic_logic' as QRType,
  multi_session: 'multi_session' as QRType,
  offline_first: 'offline_first' as QRType,
  mini_program: 'mini_program' as QRType,
} as const;

// We now use the comprehensive QRType instead of the legacy QRCodeType

export interface QRCode {
  id: string;
  name: string;
  qr_type: QRType;
  type: QRType;
  data: string;
  image_url?: string;
  imageUrl: string;
  url?: string;
  short_url?: string;
  shortUrl?: string;
  created_at: string;
  createdAt: string;
  updated_at?: string;
  updatedAt?: string;
  scans: number;
  dynamic: boolean;
  user_id: string;
  metadata?: Record<string, any>;
  color?: string;
  logo_url?: string;
  tags?: string[];
  folder_id?: string;
  archived?: boolean;
}

export interface QRCodeFilters {
  search?: string;
  page?: number;
  limit?: number;
  sortBy?: string;
  sortOrder?: 'asc' | 'desc';
  type?: QRType[];
  dateRange?: {
    start: Date | null;
    end: Date | null;
    from: Date | null;
    to: Date | null;
  };
  minScans?: number;
  maxScans?: number;
  scanCount?: {
    min: number | null;
    max: number | null;
  };
  tags?: string[];
  folders?: string[];
}

export interface QRCodeSortOption {
  id: string;
  name: string;
  value: keyof QRCode | 'popularity';
  direction: 'asc' | 'desc';
}

export type SortOption = 
  | 'name_asc'
  | 'name_desc'
  | 'created_asc'
  | 'created_desc'
  | 'scans_asc' 
  | 'scans_desc';

export interface QRCodeStats {
  totalScans: number;
  scansToday: number;
  scansThisWeek: number;
  scansThisMonth: number;
  topPerformers: { id: string; name: string; scans: number }[];
}

export interface QRCodeAnalytics {
  qrId: string;
  totalScans: number;
  scansByDay: { date: string; count: number }[];
  scansByLocation: { location: string; count: number }[];
  scansByDevice: { device: string; count: number }[];
  scansByBrowser: { browser: string; count: number }[];
}

export interface QRDataBase {
  name?: string;
  description?: string;
}

export interface URLData extends QRDataBase {
  url: string;
  utm_source?: string;
  utm_medium?: string;
  utm_campaign?: string;
  short_url?: boolean;
}

export interface TextData extends QRDataBase {
  text: string;
}

export interface EmailData extends QRDataBase {
  email: string;
  subject?: string;
  body?: string;
}

export interface PhoneData extends QRDataBase {
  phone: string;
  contact_name?: string;
}

export interface SMSData extends QRDataBase {
  phone: string;
  message?: string;
}

export interface VCardData extends QRDataBase {
  first_name: string;
  last_name?: string;
  organization?: string;
  job_title?: string;  // Renamed from title to avoid confusion with QR name
  email?: string;
  phone?: string;
  website?: string;
  address?: string;
}

export interface WifiData extends QRDataBase {
  ssid: string;
  password?: string;
  encryption: 'WEP' | 'WPA' | 'WPA2' | 'WPA3' | 'none';
  hidden?: boolean;
}

export interface CouponData extends QRDataBase {
  code: string;
  discount_type: string;
  discount_value: number;
  expiry_date: string;  
  title?: string;
  description?: string;
  terms_and_conditions?: string;
  minimum_spend?: number;
  maximum_spend?: number;
}

export interface MenuData extends MenuItem {
  restaurant_name: string;
  items: MenuItem[];
  categories: string[];
 
}

export interface ProductData extends QRDataBase {
  product_id: string;
  name: string;
  price: number;
  description: string;
  image_url: string;
  sku?: string;
  category?: string;
  stock?: number;
  title?: string;
  additional_info?: string;
  tags?: string[];
}

export interface ZoomData extends QRDataBase {
  meeting_url?: string;
  meeting_id?: string;
  passcode?: string;
  start_time?: string;
  duration?: number;
  host_name?: string;
  join_before_host?: boolean;
  mute_upon_entry?: boolean;
}

export interface TeamsData extends QRDataBase {
  meeting_url: string;
  meeting_id?: string;
  organizer?: string;
  start_time?: string;
  duration?: number;
  channel_name?: string;
  team_name?: string;
  tenant_id?: string;
}

export interface WebexData extends QRDataBase {
  meeting_url?: string;
  meeting_number?: string;
  password?: string;
  host_key?: string;
  host_name?: string;
  start_time?: string;
  duration?: number;
  site_url?: string;
  attendee_email?: string;
}

export interface GoogleMeetData extends QRDataBase {
  meeting_url: string;
  meeting_code?: string;
  organizer?: string;
  start_time?: string;
  duration?: number;
  calendar_event_id?: string;
  requires_google_account?: boolean;
}

export interface EventData extends QRDataBase {
  summary: string;
  start_time: string;
  end_time: string;
  location?: string;
  description?: string;
  organizer?: string;
  title?: string;
  image_url?: string;
}

export interface TicketData extends QRDataBase {
  event_id: string;
  ticket_type: string;
  price: number;
  qr_code: string;
  seat_number?: string;
  gate?: string;
  section?: string;
  row?: string;
  date?: string;
  time?: string;
  venue?: string;
  organizer?: string;
  ticket_holder?: string;
  barcode?: string;
  status?: 'valid' | 'used' | 'expired' | 'cancelled';
}

export interface SocialData extends QRDataBase {
  platform: string;
  username: string;
  action?: "follow" | "connect" | "share";
}

export interface PaymentData extends QRDataBase {
  provider?: string;
  recipient?: string;
  amount: number;
  currency: string;
  description: string;
  payment_method: string;
  reference?: string;
  additional_info?: string;
}

export interface InvoiceItem {
  description: string;
  quantity: number;
  unit_price: number;
  tax_rate?: number;
}

export interface InvoiceData extends QRDataBase {
  invoice_number: string;
  amount: number;
  due_date: string;
  items: InvoiceItem[];
  currency: string;
  additional_info?: string;
}

export interface BankTransferData extends QRDataBase {
  account_number: string;
  bank_code: string;
  amount: number;
  currency: string;
  reference: string;
  additional_info?: string;
}

export interface CryptoWalletData extends QRDataBase {
  wallet_address: string;
  network: string;
  amount?: number;
  additional_info?: string;
}

export interface NFTData extends QRDataBase {
  token_id: string;
  contract_address: string;
  metadata_url: string;
  network: string;
  additional_info?: string;
}

export interface SmartContractParameter {
  name: string;
  type: string;
  value: string;
}

export interface SmartContractData extends QRDataBase {
  contract_address: string;
  function_name: string;
  parameters: SmartContractParameter[];
  network: string;
  additional_info?: string;
}

export interface LegalSmartContractData extends QRDataBase {
  contract_id: string;
  blockchain: string;
  creator: string;
  deployment_date: string;
  contract_address: string;
  description?: string;
  notes?: string;
}

export interface Web3DAppParameter {
  name: string;
  type: string;
  value: string;
}

export interface Web3DAppData extends QRDataBase {
  dapp_url: string;
  function_name: string;
  parameters: Web3DAppParameter[];
  network: string;
  additional_info?: string;
}

export interface DevicePairingData extends QRDataBase {
  device_id: string;
  pairing_code: string;
  type: string;
  manufacturer?: string;
  model?: string;
}

export interface DeviceConfigData extends QRDataBase {
  device_id: string;
  config_data: string;
  version: string;
  device_type?: string;
}

export interface SensorDataItem {
  data_type: string;
  value: string;
  unit?: string;
  timestamp?: string;
}

export interface SensorDataFormData extends QRDataBase {
  sensor_id: string;
  data: SensorDataItem[];
  sensor_type?: string;
  location?: string;
}

export interface Condition {
  device_id: string;
  property: string;
  operator: string;
  value: string;
}

export interface Action {
  device_id: string;
  command: string;
  parameters: { name: string; value: string }[];
}

export interface AutomationRuleData extends QRDataBase {
  rule_id: string;
  conditions: Condition[];
  actions: Action[];
  active?: boolean;
}

export interface CommandParameter {
  name: string;
  value: string;
}

export interface SmartHomeControlData extends QRDataBase {
  device_id: string;
  device_type: string;
  command: string;
  parameters: CommandParameter[];
}

export interface AudioData extends QRDataBase {
  audio_url: string;
  duration?: number;
}

export interface VideoData extends QRDataBase {
  video_url: string;
  thumbnail_url?: string;
}

export interface DocumentData extends QRDataBase {
  document_url: string;
  document_type: string;
}

export interface FileData extends QRDataBase {
  file_url: string;           // Required: URL to the file (internal or external)
  file_name?: string;         // Optional: Display name
  file_type?: string;         // Optional: MIME type or extension (e.g., 'application/pdf')
  source?: 'uploaded' | 'external'; // Optional: Where the file is hosted
  size_bytes?: number;        // Optional: File size (if known)
}

export interface MedicalData extends QRDataBase {
  patient_id: string;
  record_type: string;
  access_level: string;
  provider_id?: string;
  facility?: string;
  date?: string;
  encryption_key?: string;
  emergency_access?: boolean;
}

export interface IoTData extends QRDataBase {
  device_type: string;
  device_id: string;
  config_data: Record<string, any>;
  pairing_mode?: string;
  security_key?: string;
  firmware_version?: string;
}

export interface EducationData extends QRDataBase {
  course_id?: string;
  student_id?: string;
  institution: string;
  type: string;
  valid_until?: string;
  verification_url?: string;
  access_level: string;
}

export interface CertificateData extends QRDataBase {
  certificate_id: string;
  recipient_name: string;
  course_name: string;
  issue_date: string;
  valid_until?: string;
  issuer?: string;
}

export interface CourseData extends QRDataBase {
  course_id: string;
  title: string;
  instructor: string;
  institution: string;
  start_date?: string;
  end_date?: string;
  description?: string;
  credits?: string;
  level?: string;
  enrollment_link?: string;
}

export interface StudentIDData extends QRDataBase {
  student_id: string;
  name: string;
  institution: string;
  valid_until?: string;
  photo_url?: string;
  department?: string;
  program?: string;
  access_level?: string;
}

export interface AssignmentData extends QRDataBase {
  assignment_id: string;
  title: string;
  due_date?: string;
  student_name?: string;
  course_id?: string;
  max_points?: number;
  submission_url?: string;
}

export interface LibraryResourceData extends QRDataBase {
  resource_id: string;
  title: string;
  author?: string;
  url?: string;
  resource_type?: string;
  isbn?: string;
  call_number?: string;
  availability?: boolean;
  location?: string;
}

export interface TrainingMaterialData extends QRDataBase {
  material_id: string;
  title: string;
  url: string;
  type?: string;
  version?: string;
  created_date?: string;
  author?: string;
  access_level?: string;
}

export interface PublicationData extends QRDataBase {
  publication_id: string;
  title: string;
  authors: string;
  doi?: string;
  publication_date?: string;
  url?: string;
  journal?: string;
  volume?: string;
  issue?: string;
  pages?: string;
  publisher?: string;
  abstract?: string;
  notes?: string;
}

export interface DatasetData extends QRDataBase {
  dataset_id: string;
  title: string;
  authors: string;
  url: string;
  version?: string;
  license?: string;
  repository?: string;
  release_date?: string;
  citation?: string;
  format?: string;
  size?: string;
  notes?: string;
}

export interface ELearningData extends QRDataBase {
  platform_name: string;
  course_title?: string;
  instructor?: string;
  enrollment_key?: string;
  course_id?: string;
  direct_url: string;
  start_date?: string;
  end_date?: string;
  language?: string;
  prerequisites?: string;
  learning_objectives?: string[];
  access_type: 'open' | 'enrollment-key' | 'paid' | 'invite-only' | 'institutional';
  pricing_info?: string;
  institution?: string;
  department?: string;
  support_contact?: string;
  certificate_offered?: boolean;
  estimated_hours?: number;
  skill_level?: 'beginner' | 'intermediate' | 'advanced' | 'all-levels';
  additional_resources?: string;
  tags?: string[];
}

export interface QuizData extends QRDataBase {
  quiz_title: string;
  quiz_id?: string;
  creator?: string;
  organization?: string;
  direct_url: string;
  time_limit?: number;
  question_count?: number;
  passing_score?: number;
  instructions?: string;
  topics?: string[];
  difficulty: 'easy' | 'medium' | 'hard' | 'mixed';
  quiz_type: 'assessment' | 'practice' | 'exam' | 'survey' | 'interactive' | 'game';
  access_type: 'open' | 'password-protected' | 'login-required' | 'invite-only' | 'one-time';
  password?: string;
  start_date?: string;
  end_date?: string;
  results_visibility: 'immediate' | 'delayed' | 'after_deadline' | 'instructor_release';
  attempts_allowed?: number;
  certification_offered?: boolean;
  feedback_provided?: boolean;
  randomize_questions?: boolean;
  tags?: string[];
}

export interface TransportData extends QRDataBase {
  vehicle_id?: string;
  registration_type?: string;
  expiry_date?: string;
  route_info?: {
    start: string;
    end: string;
    schedule: string[];
  };
  fleet_data?: {
    company: string;
    vehicle_type: string;
    capacity: number;
  };
}

/**
 * QR Data for Vehicle Registration
 */
export interface VehicleRegistrationData extends QRDataBase {
  vehicle_id: string;
  registration_type: string;
  license_plate: string;
  owner_name: string;
  expiry_date: string;
  make?: string;
  model?: string;
  year?: string;
  additional_info?: string;
  title?: string;
  description?: string;
}

/**
 * QR Data for Fleet Tracking
 */
export interface FleetTrackingData extends QRDataBase {
  fleet_id: string;
  company: string;
  vehicle_type: string;
  vehicle_count: number;
  notes?: string;
}

/**
 * QR Data for Maintenance
 */
export interface MaintenanceData extends QRDataBase {
  maintenance_id: string;
  vehicle_id: string;
  service_type: string;
  date: string;
  provider: string;
  notes?: string;
  title?: string;
  description?: string;
}

/**
 * QR Data for Parking
 */
export interface ParkingData extends QRDataBase {
  parking_id: string;
  vehicle_plate: string;
  lot_number: string;
  valid_from: string;
  valid_until: string;
  notes?: string;
  title?: string;
  description?: string;
}

/**
 * QR Data for Ride Share
 */
export interface RideShareData extends QRDataBase {
  service_provider: string;
  pickup_location: string;
  pickup_coordinates: string;
  destination: string;
  destination_coordinates: string;
  pickup_time: string;
  vehicle_type: string;
  price_estimate?: number;
  driver_name?: string;
  driver_rating?: string;
  driver_phone?: string;
  vehicle_make?: string;
  vehicle_model?: string;
  vehicle_color?: string;
  license_plate?: string;
  ride_id: string;
  payment_method?: string;
  promo_code?: string;
  notes?: string;
  title?: string;
  description?: string;
}

/**
 * QR Data for Route Schedule
 */
export interface RouteScheduleData extends QRDataBase {
  schedule_id: string;
  route_name: string;
  start_location: string;
  end_location: string;
  schedule: string;
  notes?: string;
  title?: string;
  description?: string;
}

/**
 * QR Data for Scooter Unlock
 */
export interface ScooterUnlockData extends QRDataBase {
  service_provider: string;
  scooter_id: string;
  unlock_code: string;
  rate_info?: string;
  location?: string;
  location_coordinates?: string;
  battery_level?: string;
  max_range?: string;
  user_account?: string;
  payment_method?: string;
  rental_terms?: string;
  helmet_required?: boolean;
  parking_rules?: string;
  safety_instructions?: string;
  support_phone?: string;
  unlock_url?: string;
  title?: string;
  description?: string;
}

/**
 * QR Data for Transit Pass
 */
export interface TransitPassData extends QRDataBase {
  pass_id: string;
  passenger_name: string;
  route: string;
  valid_from: string;
  valid_until: string;
  seat_number?: string;
  notes?: string;
  title?: string;
  description?: string;
}

/**
 * QR Data for Boarding Pass
 */
export interface BoardingPassData extends QRDataBase {
  passenger_name: string;
  flight_number: string;
  airline: string;
  departure_airport: string;
  departure_terminal?: string;
  departure_gate?: string;
  departure_date: string;
  departure_time: string;
  arrival_airport: string;
  arrival_terminal?: string;
  seat_number?: string;
  boarding_time?: string;
  boarding_group?: string;
  frequent_flyer_number?: string;
  booking_reference: string;
  class: 'economy' | 'premium_economy' | 'business' | 'first' | 'other';
  title?: string;
  description?: string;
}

export interface LegalDocumentData extends QRDataBase {
  document_type: string;
  document_id: string;
  issuer: string;
  issue_date: string;
  expiry_date?: string;
  verification_method: string;
  blockchain_hash?: string;
  digital_signature?: string;
}

export interface ResearchData extends QRDataBase {
  publication_id?: string;
  doi?: string;
  authors?: string[];
  institution?: string;
  dataset_url?: string;
  equipment_id?: string;
  protocol_version?: string;
  citation_format?: string;
}

export interface GameData extends QRDataBase {
  game_id: string;
  asset_type: string;
  asset_data: Record<string, any>;
  platform?: string;
  virtual_world?: string;
  ar_markers?: string[];
  vr_scene_id?: string;
  player_id?: string;
}

export interface GameSaveData extends QRDataBase {
  save_id: string;
  game: string;
  player_id: string;
  save_data: string | Record<string, any>;
  version: string;
  timestamp: string;
}
export interface TournamentData extends QRDataBase {
  tournament_id: string;
  name: string;
  game: string;
  start_date: string;
  end_date?: string;
  location?: string;
  participants?: string[];
  bracket_url?: string;
  prize_pool?: string;
  title?: string;
  description?: string;
}
export interface TournamentEntryData extends QRDataBase {
  entry_id: string;
  tournament_id: string;
  player_id: string;
  game: string;
  entry_date: string;
  status: string;
  registration_url: string;
  rules: string;
}

export interface VirtualItemData extends QRDataBase {
  item_id: string;
  name: string;
  description?: string;
  rarity?: string;
  game?: string;
  attributes?: Record<string, any>;
  owner_id?: string;
  tradable?: boolean;
}

// International QR types
export interface LanguageContent {
  language: string;
  content: string;
}

export interface MultiLanguageData extends QRDataBase {
  default_language: string;
  languages: LanguageContent[];
  auto_detect?: boolean;
  fallback_language?: string;
  redirect_url?: string;
}

export interface RegionContent {
  region_code: string;
  title: string;
  content: string;
  custom_fields?: Record<string, string>;
}

export interface RegionSpecificData extends QRDataBase {
  default_region: string;
  regions: RegionContent[];
  auto_detect_region?: boolean;
  fallback_region?: string;
  include_region_selector?: boolean;
  currency_localization?: boolean;
  date_format_localization?: boolean;
  measurement_system_localization?: boolean;
}

export interface RegionalSetting {
  region_code: string;
  language_code: string;
  currency_code?: string;
  date_format?: string;
  time_format?: string;
  measurement_system?: 'metric' | 'imperial' | 'custom';
  timezone?: string;
}

export interface TranslationService {
  enabled: boolean;
  service_name?: string;
  api_key?: string;
  target_languages?: string[];
}

export interface TranslationData extends QRDataBase {
  default_language: string;
  default_region: string;
  regional_settings: RegionalSetting[];
  auto_detect_region?: boolean;
  allow_user_override?: boolean;
  translation_service?: TranslationService;
  enable_accessibility_features?: boolean;
  low_bandwidth_mode?: boolean;
  content_delivery_network?: boolean;
}

export interface ARExperienceData extends QRDataBase {
  experience_id: string;
  title: string;
  description?: string;
  launch_url: string;
  supported_devices?: string[];
  marker_image_url?: string;
  location?: string;
  duration?: string;
}

export interface VRContentData extends QRDataBase {
  content_id: string;
  title: string;
  description?: string;
  vr_platform?: string;
  scene_id?: string;
  launch_url: string;
  supported_devices?: string[];
  duration?: string;
}

export interface PlayerProfileData extends QRDataBase {
  player_id: string;
  username: string;
  avatar_url?: string;
  stats?: Record<string, any>;
  achievements?: string[];
  games_played?: string[];
  team?: string;
  bio?: string;
}

// Social Media specific interfaces
export interface DiscordData extends QRDataBase {
  server_id?: string;
  channel_id?: string;
  profile_username?: string;
  invite_code?: string;
  action?: 'join' | 'message' | 'profile';
}

export interface SnapchatData extends QRDataBase {
  username: string;
  action?: 'add' | 'chat' | 'view_story';
}

export interface PinterestData extends QRDataBase {
  username?: string;
  board_id?: string;
  pin_id?: string;
  action?: 'follow' | 'view_board' | 'view_pin';
}

export interface ThreadsData extends QRDataBase {
  username: string;
  post_id?: string;
  action?: 'follow' | 'view_profile' | 'view_post';
}

export interface BeRealData extends QRDataBase {
  username: string;
  action?: 'add_friend' | 'view_profile';
}

export interface LineData extends QRDataBase {
  id: string;
  action?: 'add_friend' | 'chat' | 'official_account';
}

export interface KakaoData extends QRDataBase {
  id: string;
  action?: 'add_friend' | 'chat' | 'channel';
}

export interface WeChatData extends QRDataBase {
  id: string;
  action?: 'add_contact' | 'follow_official' | 'mini_program';
  mini_program_id?: string;
  scene?: number;
}

export interface WeiboData extends QRDataBase {
  profile_id: string;
  post_id?: string;
  action?: 'follow' | 'view_profile' | 'view_post';
}

export interface TikTokData extends QRDataBase {
  username: string;
  video_id?: string;
  action?: 'follow' | 'view_profile' | 'view_video' | 'create';
}

export interface WhatsAppData extends QRDataBase {
  phone_number: string;
  message?: string;
  action?: 'chat' | 'call' | 'video_call' | 'share';
  is_business?: boolean;
}

export interface LinkedInData extends QRDataBase {
  profile_id: string;
  connection_type?: 'connect' | 'follow' | 'share' | 'message';
  action?: 'view_profile' | 'connect' | 'share' | 'message';
  custom_message?: string;
}

export interface InstagramData extends QRDataBase {
  username: string;
  content_id?: string;
  action?: 'profile' | 'post' | 'story' | 'reel' | 'follow';
}

// App Link interfaces
export interface AppStoreData extends QRDataBase {
  app_id: string;
  country_code?: string;
  campaign?: string;
  provider_token?: string;
  title?: string;
  description?: string;
  developer_name?: string;
}

export interface GooglePlayData extends QRDataBase {
  package_name: string;
  referrer?: string;
  utm_campaign?: string;
  title?: string;
  description?: string;
  developer_name?: string;
  country_code?: string;
}

export interface AppGalleryData extends QRDataBase {
  app_id: string;
  country_code?: string;
  campaign?: string;
  title?: string;
  description?: string;
  developer_name?: string;
  custom_parameters?: string;
}

export interface UniversalAppLinkData extends QRDataBase {
  ios_url: string;
  android_url: string;
  fallback_url: string;
  desktop_url?: string;
  app_name?: string;
  preferred_platform?: 'ios' | 'android' | 'detect';
  title?: string;
  description?: string;
}

// Business Type interfaces
export interface DigitalBusinessCardData extends VCardData {
  company_logo?: string;
  profile_image?: string;
  social_links?: Record<string, string>;
  portfolio_url?: string;
  bio?: string;
  meeting_url?: string;
  calendar_url?: string;
  position?: string;
  department?: string;
  custom_fields?: Record<string, string>;
}

export interface EmployeeBadgeData extends QRDataBase {
  employee_id: string;
  name: string;
  department?: string;
  position?: string;
  photo_url?: string;
  company?: string;
  issue_date?: string;
  expiry_date?: string;
  access_level?: string;
  building_codes?: string[];
  issuer?: string;
  signature?: string;
}

export interface ReviewData extends QRDataBase {
  platform: 'google' | 'yelp' | 'tripadvisor' | 'facebook' | 'trustpilot' | string;
  business_id: string;
  business_name?: string;
  review_url: string;
  custom_message?: string;
}

// Location Type interfaces
export interface GeoData extends QRDataBase {
  latitude: number;
  longitude: number;
  name: string;
  description: string;
  zoom?: number;
  additional_info?: string;
}

export interface StoreLocatorData extends QRDataBase {
  business_name: string;
  locations?: {
    name: string;
    address: string;
    coordinates?: [number, number]; // [latitude, longitude]
    phone?: string;
    hours?: Record<string, string>;
  }[];
  search_radius?: number;
  default_location?: string;
  api_key?: string;
  show_directions?: boolean;
}

export interface MapRouteData extends QRDataBase {
  start_location: string;
  end_location: string;
  waypoints?: string[];
  travel_mode?: 'driving' | 'walking' | 'bicycling' | 'transit';
  route_options?: {
    avoid_tolls?: boolean;
    avoid_highways?: boolean;
    avoid_ferries?: boolean;
    units?: 'metric' | 'imperial';
  };
  map_provider?: 'google' | 'apple' | 'waze' | 'osm';
}

export interface NavigationData extends QRDataBase {
  start_point: string;
  end_point: string;
  route_type: string;
  additional_info?: string;
}

export interface IndoorNavigationData extends QRDataBase {
  building_id: string;
  venue_name: string;
  start_point?: string;
  destination_point: string;
  floor_plan_url?: string;
  instructions?: string;
  accessible_route?: boolean;
  provider?: string;
}

// Payment Type interfaces
export interface LoyaltyCardData extends QRDataBase {
  program_name: string;
  customer_id: string;
  customer_name?: string;
  points_balance?: number;
  membership_level?: string;
  expiry_date?: string;
  benefits?: string[];
  terms_url?: string;
  company_logo_url?: string;
  barcode_value?: string;
  program_website?: string;
}

export interface SubscriptionPeriod {
  interval: 'day' | 'week' | 'month' | 'year';
  interval_count: number;
}

export interface SubscriptionData extends QRDataBase {
  service_name: string;
  plan_name: string;
  subscription_id?: string;
  customer_id?: string;
  customer_name?: string;
  amount: number;
  currency: string;
  billing_period: SubscriptionPeriod;
  start_date: string;
  end_date?: string;
  auto_renew?: boolean;
  payment_method?: string;
  next_payment_date?: string;
  terms_url?: string;
  cancel_url?: string;
  features?: string[];
}

export interface AIEnhancementOptions {
  /** Aspects to optimize for (e.g., readability, branding, accessibility) */
  optimize_for?: string[];
  /** Visual style preferences: pattern, marker_shape, overlay_url, logo_position, prompt, model, etc. */
  style_preferences?: {
    pattern?: string;
    marker_shape?: string;
    overlay_url?: string;
    logo_url?: string;
    logo_position?: string;
    prompt?: string;
    model?: string;
    strength?: number;
    steps?: number;
    guidance_scale?: number;
    [key: string]: any;
  };
  /** Color scheme preferences: background, foreground, marker, eye, etc. (hex or CSS) */
  color_scheme?: Record<string, string>;
  /** Overlay image URL to blend with QR */
  overlay_url?: string;
  /** Logo image URL to embed in QR */
  logo_url?: string;
  /** Logo placement (center, top-left, etc.) */
  logo_position?: string;
  /** Marker shape (circle, rounded, square, custom) */
  marker_shape?: string;
  /** Generate multiple variations of the QR code */
  generate_variations?: boolean;
  /** Accessibility features (high_contrast, error_correction, alt_text, etc.) */
  accessibility_features?: string[];
  /** ID of a dynamic design template to apply */
  template_id?: string;
}

/**
 * LandingPageData: Main interface for landing page builder and QR creation.
 * Extended to support advanced QR/landing page options.
 */
export interface LandingPageData {
  id?: string;
  title: string;
  description?: string;
  components: ComponentData[];
  style: {
    fontFamily?: string;
    fontSize?: string | number;
    lineHeight?: string | number;
    colorScheme?: 'light' | 'dark' | 'brand' | 'custom';
    primaryColor?: string;
    backgroundColor?: string;
    textColor?: string;
    spacingScale?: 'compact' | 'comfortable' | 'spacious' | 'custom';
    containerWidth?: string | number;
    borderRadius?: string | number;
    logoOverlay?: string; // base64 or URL
    markerStyle?: 'default' | 'rounded' | 'dots' | 'classic' | string;
    dyslexiaFont?: boolean;
    highContrast?: boolean;
    // legacy fields for compatibility
    colors?: {
      primary: string;
      background: string;
      text: string;
    };
    spacing?: {
      scale: string;
      containerWidth: string;
      borderRadius: string;
    };
  };
  seo?: {
    metaTitle?: string;
    metaDescription?: string;
    keywords?: string[];
    ogImage?: string;
  };
  protection?: {
    password?: string;
    geoRestriction?: {
      countries: string[];
    };
    timeRestriction?: {
      start?: string;
      end?: string;
    };
  };
  trackingIds?: {
    googleAnalytics?: string;
    facebookPixel?: string;
    customScripts?: string[];
  };
  notifications?: {
    formSubmissions?: boolean;
    trafficSpikes?: boolean;
  };

  /** Advanced QR/landing page options */
  /**
   * If true, QR is dynamic (can be updated and tracked)
   */
  dynamic?: boolean;
  /**
   * Custom short URL for dynamic QR codes
   */
  customShortUrl?: string;
  /**
   * Enhanced tracking options
   */
  enableTracking?: boolean;
  /**
   * Enhanced analytics options for detailed metrics
   */
  enableAnalytics?: boolean;
  /**
   * Landing page template selection
   */
  landingPageTemplate?: string;
  /**
   * Expiry date/time (ISO string)
   */
  expiry?: string;
  /**
   * Maximum number of allowed scans
   */
  maxScans?: number;
  /**
   * Team assignment (team ID or name)
   */
  team?: string;
  /**
   * Buyer assignment (user ID or name)
   */
  buyer?: string;
  /**
   * Scheduling for activation/deactivation
   */
  schedule?: {
    start?: string;
    end?: string;
  };
  /**
   * Enable AI/LLM enhancement (true for on, or object for options)
   */
  aiEnhancement?: boolean | {
    [key: string]: any;
  };
}

export interface LandingPageAnalytics {
  viewCount: number;
  uniqueVisitors: number;
  conversionRate: number;
  averageTimeOnPage: number;
  bounceRate: number;
  deviceBreakdown: {
    mobile: number;
    desktop: number;
    tablet: number;
  };
  topLocations: {
    country: string;
    count: number;
  }[];
}

export interface QRCreateRequest {
  qr_type: QRType;
  name?: string;
  description?: string;
  data: URLData | FileData | TextData | EmailData | PhoneData | SMSData | WifiData | VCardData | LandingPageData | GameData | VirtualItemData | TournamentData | ARExperienceData | VRContentData | PlayerProfileData | DiscordData | SnapchatData | PinterestData | ThreadsData | BeRealData | LineData | KakaoData | WeChatData | WeiboData | AppStoreData | GooglePlayData | AppGalleryData | UniversalAppLinkData | DigitalBusinessCardData | EmployeeBadgeData | ReviewData | StoreLocatorData | MapRouteData | IndoorNavigationData | LoyaltyCardData | SubscriptionData | VehicleRegistrationData | BoardingPassData | TransitPassData | ParkingData | RouteScheduleData | RideShareData | ScooterUnlockData | MaintenanceData;
  logo_url?: string;
  overlay_url?: string;
  logo_position?: string;
  marker_shape?: string;
  color_scheme?: Record<string, string>;
  template_id?: string;
  enable_tracking?: boolean;
  enable_analytics?: boolean;
  ai_enhancement?: AIEnhancementOptions;
  preview?: boolean;
}

// Type used by the QRPreview component
export type QRCreateData = Partial<QRCreateRequest>;

export interface QRResponse {
  id: number;
  dynamic_id: string;
  qr_type: QRType;
  data: Record<string, any>;
  design_url: string;
  created_at: string;
  updated_at: string;
  current_scans: number;
  is_active: boolean;
  name?: string;
  description?: string;
  analytics_summary?: Record<string, any>;
  version_history?: Record<string, any>[];
}

export interface LandingPageResponse {
  id: string;
  url: string;
  qr_url: string;
  analytics: LandingPageAnalytics;
  created_at: string;
  updated_at: string;
}

export interface ComponentData {
  id: string;
  type: string;
  props: {
    // Common props
    className?: string;
    style?: React.CSSProperties;
    
    // Text props
    text?: string;
    fontSize?: string;
    fontWeight?: string;
    color?: string;
    alignment?: 'left' | 'center' | 'right';
    
    // Image props
    src?: string;
    alt?: string;
    width?: string | number;
    height?: string | number;
    
    // Button props
    label?: string;
    url?: string;
    variant?: 'primary' | 'secondary' | 'outline' | 'ghost';
    size?: 'sm' | 'md' | 'lg';
    
    // Form props
    fields?: {
      type: string;
      label: string;
      placeholder?: string;
      required?: boolean;
      options?: string[];
    }[];
    submitLabel?: string;
    webhook?: string;
    
    // Container props
    direction?: 'row' | 'column';
    gap?: string;
    padding?: string;
    background?: string;
    borderRadius?: string;
    
    // Social props
    platform?: string;
    icon?: string;
    
    // Video props
    videoUrl?: string;
    autoplay?: boolean;
    controls?: boolean;
    
    // Custom props
    [key: string]: any;
  };
  children?: ComponentData[];
}

// --- GLOBAL QR DESIGN OPTIONS ---
// Unified QR code design options for global, AI-powered QR code generation.
// This should match the backend QRDesignOptions schema exactly.
export interface QRDesignOptions {
  // Core design
  size?: number; // QR code size in pixels
  margin?: number; // Margin around QR code in pixels

  // Pattern (module style)
  pattern?: string; // QR pattern style (classic, dots, squares, etc.)

  // Marker border (finder pattern border)
  marker_border_style?: string; // Style of marker border (square, rounded, circle, etc.)
  marker_border_color?: string; // Color of marker border

  // Marker center (finder pattern center)
  marker_center_style?: string; // Style of marker center (circle, dot, diamond, etc.)
  marker_center_color?: string; // Color of marker center

  // Toggles for marker color customization
  custom_marker_color?: boolean; // Enable custom marker color
  different_markers_colors?: boolean; // Enable different colors for each marker

  // Logo options
  logo_url?: string; // URL of logo image to embed in QR center
  logo_size?: number; // Fractional logo size (0.0-1.0), where 0.3 = 30% of QR area
  logo_position?: string; // Logo position (always center for best scannability)
  logo_opacity?: number; // Logo opacity (0.0-1.0)
  logo_remove_background?: boolean; // Remove background from logo

  // Frame options
  frame_style?: string; // Frame style (classic, rounded, dashed, etc.)
  frame_color?: string; // Frame color (hex)
  frame_text?: string; // Text to display on frame
  frame_font?: string;
  frame_font_size?: number;
  frame_thickness?: number; // Frame thickness in pixels

  // Animation options
  animation_type?: string; // Type of animation (pulse, rotate, bounce, etc.)
  animation_speed?: number; // Animation speed (seconds per cycle)
  animation_loop?: boolean; // Whether animation should loop

  // Overlay options
  overlay_url?: string; // URL of overlay image to blend on top of QR
  overlay_blend_mode?: string; // Blend mode for overlay (multiply, screen, overlay, etc.)

 // Colors for the QR code body
 foreground_color?: string; // Primary foreground color
 background_color?: string; // Primary background color
 color_mode?: string; // Color mode (solid, radial_gradient, etc.)
 corner_radius?: number; // Corner radius in pixels

  // Error correction
  error_correction?: string; // Error correction level (L, M, Q, H)

  // AI enhancement
  ai_enhance?: boolean; // Whether to apply AI enhancement
  ai_enhance_options?: Record<string, any>; // Options for AI enhancement

  // SVG-specific options
  use_svg?: boolean; // Whether to use SVG rendering instead of PNG
  svg_render_dpi?: number; // DPI for SVG rendering (affects quality)
  svg_optimize?: boolean; // Whether to optimize SVG for web performance
}

// --- QR DOWNLOAD OPTIONS ---
// QR code download options for various formats
export interface QRDownloadOptions {
  width?: number;
  height?: number;
  dpi?: number;
  withLogo?: boolean;
  margin?: number;
  color?: string;
  backgroundColor?: string;
  quality?: number;
  errorCorrectionLevel?: 'L' | 'M' | 'Q' | 'H';
}

// --- (rest of file continues unchanged)
export interface AppointmentData extends QRDataBase {
  appointment_id: string;
  patient_name: string;
  provider_name: string;
  appointment_time: string;
  location?: string;
  notes?: string;
}

export interface LabResultData extends QRDataBase {
  lab_result_id: string;
  patient_name: string;
  test_type: string;
  result: string;
  date: string;
  provider?: string;
  notes?: string;
}

export interface InsuranceCardData extends QRDataBase {
  insurance_id: string;
  member_name: string;
  provider: string;
  group_number?: string;
  valid_until?: string;
  notes?: string;
}

export interface TelemedicineData extends QRDataBase {
  telemedicine_id: string;
  patient_name: string;
  provider_name: string;
  session_url: string;
  appointment_time?: string;
  notes?: string;
}

export interface VaccinationData extends QRDataBase {
  patient_name: string;
  patient_id: string;
  date_of_birth: string;
  vaccine_name: string;
  vaccine_type: string;
  manufacturer: string;
  lot_number: string;
  date_administered: string;
  administered_by: string;
  administered_at: string;
  next_dose_due?: string;
  notes?: string;
}

export interface LegalDocumentData extends QRDataBase {
  document_type: string;
  document_id: string;
  issuer: string;
  issue_date: string;
  expiry_date?: string;
  verification_method: string;
  blockchain_hash?: string;
  digital_signature?: string;
}

// Multi-format code interfaces
export interface AztecData extends QRDataBase {
  content: string;
  error_correction: 'auto' | 'low' | 'medium' | 'high';
  symbol_size: 'auto' | 'compact' | 'full';
  is_binary: boolean;
}

export interface PDF417Data extends QRDataBase {
  content: string;
  error_correction: 'auto' | 'level0' | 'level1' | 'level2' | 'level3' | 'level4' | 'level5' | 'level6' | 'level7' | 'level8';
  columns: number;
  rows: number;
  compact: boolean;
  is_binary: boolean;
}

export interface DataMatrixData extends QRDataBase {
  content: string;
  size: 'auto' | '10x10' | '12x12' | '14x14' | '16x16' | '18x18' | '20x20' | '22x22' | '24x24' | '26x26' | '32x32' | '36x36' | '40x40' | '44x44' | '48x48' | '52x52' | '64x64' | '72x72' | '80x80' | '88x88' | '96x96' | '104x104' | '120x120' | '132x132' | '144x144';
  encoding: 'auto' | 'ascii' | 'c40' | 'text' | 'base256' | 'edifact' | 'x12';
  is_gs1: boolean;
}

// Advanced QR code interfaces
export interface APIData extends QRDataBase {
  endpoint_url: string;
  method: 'GET' | 'POST' | 'PUT' | 'DELETE';
  headers: string;
  body: string;
  authentication_required: boolean;
  authorization_type: 'none' | 'basic' | 'bearer' | 'api_key' | 'oauth2';
}

export interface AppData extends QRDataBase {
  app_url: string;
  platform: 'ios' | 'android' | 'web' | 'desktop' | 'cross_platform';
  min_version?: string;
  deep_link?: string;
  fallback_url?: string;
  utm_source?: string;
  utm_medium?: string;
  utm_campaign?: string;
}

export interface DynamicLogicData extends QRDataBase {
  logic_rules: string;
  default_content: string;
  conditional_content: string;
  parameter_mapping: string;
  requires_location: boolean;
  requires_time: boolean;
  requires_user_agent: boolean;
  requires_language: boolean;
}

export interface EncryptedData extends QRDataBase {
  encrypted_content: string;
  encryption_method: 'aes' | 'rsa' | 'custom';
  requires_password: boolean;
  hint?: string;
  expiration_enabled: boolean;
  expiration_date?: string;
  access_limit_enabled: boolean;
  access_limit?: number;
}

export interface MiniProgramData extends QRDataBase {
  app_id: string;
  path: string;
  platform: 'wechat' | 'alipay' | 'baidu' | 'qq' | 'bytedance';
  env_version?: 'release' | 'trial' | 'develop';
  short_link?: string;
  query_parameters?: string;
}

export interface MultiSessionData extends QRDataBase {
  sessions: {
    id: string;
    content: string;
    expiration?: string;
  }[];
  default_session: string;
  rotation_enabled: boolean;
  rotation_interval?: number;
  analytics_enabled: boolean;
  callback_url?: string;
  require_auth?: boolean;
}

export interface OfflineFirstData extends QRDataBase {
  online_content: string;
  offline_content: string;
  cache_strategy: 'none' | 'simple' | 'advanced';
  requires_network_check: boolean;
  fallback_mode: 'graceful' | 'strict';
  time_to_live?: number;
}

// Business Enhanced QR code interfaces
export interface MenuItem {
  name: string;
  description: string;
  price: number;
  category: string;
  image_url: string;
  options: string[];
  available: boolean;
}

export interface PortfolioItem {
  title: string;
  description: string;
  image_url: string;
  category: string;
  link: string;
}

export interface ReservationData extends QRDataBase {
  business_name: string;
  business_type: string;
  location: string;
  phone: string;
  email: string;
  website: string;
  reservation_url: string;
  business_hours: string;
  min_party_size: number;
  max_party_size: number;
  reservation_lead_time: number;
  cancellation_policy: string;
  special_instructions: string;
  photo_url: string;
}

export interface AppointmentBookingData extends QRDataBase {
  business_name: string;
  service_name: string;
  description?: string;
  address?: string;
  location_lat?: string;
  location_lng?: string;
  phone?: string;
  email?: string;
  website?: string;
  booking_url: string;
  availability_start_time?: string;
  availability_end_time?: string;
  business_days?: string[];
  duration_minutes?: number;
  buffer_minutes?: number;
  calendar_integration?: boolean;
  calendar_type?: 'google' | 'outlook' | 'ical' | 'other';
  show_available_slots?: boolean;
  require_confirmation?: boolean;
  reminder_enabled?: boolean;
  reminder_hours?: number;
}

export interface MenuOrderingData extends QRDataBase {
  restaurant_name: string;
  restaurant_logo: string;
  restaurant_description: string;
  contact_phone: string;
  contact_email: string;
  address: string;
  business_hours: string;
  menu_categories: string[];
  menu_items: MenuItem[];
  currency: string;
  accept_orders: boolean;
  delivery_available: boolean;
  takeout_available: boolean;
  estimated_delivery_time: string;
  minimum_order: string;
  payment_methods: string[];
  special_notes: string;
}

export interface PortfolioData extends QRDataBase {
  creator_name: string;
  profession: string;
  bio: string;
  contact_email: string;
  contact_phone: string;
  website: string;
  portfolio_items: PortfolioItem[];
  display_style: string;
  social_links: Array<{platform: string, url: string}>;
  allow_reviews: boolean;
  show_contact_form: boolean;
}

// Legal QR code interfaces
export interface AgeVerificationData extends QRDataBase {
  verification_title: string;
  verification_id: string;
  organization: string;
  purpose: string;
  minimum_age: number;
  verification_method: 'self-declaration' | 'document-upload' | 'third-party' | 'face-recognition' | 'knowledge-based' | 'other';
  other_method_description?: string;
  privacy_policy_url?: string;
  terms_url?: string;
  success_redirect_url?: string;
  failure_redirect_url?: string;
  verification_instructions: string;
  data_retention_policy?: string;
  legal_notice: string;
  additional_requirements?: string;
  authentication_required: boolean;
  logging_enabled: boolean;
  notes?: string;
}

export interface AuditRecordData extends QRDataBase {
  audit_id: string;
  organization: string;
  department: string;
  auditor: string;
  audit_date: string;
  expiry_date?: string;
  audit_type: string;
  audit_scope: string;
  audit_result: 'pass' | 'fail' | 'conditional' | 'in-progress';
  audit_rating?: string;
  findings: string;
  recommendations: string;
  corrective_actions: string;
  next_audit_date?: string;
  compliance_status: string;
  signature_authority: string;
  attachments?: string[];
  confidentiality_level: 'public' | 'internal' | 'confidential' | 'restricted';
}

export interface CertificationData extends QRDataBase {
  certificate_id: string;
  certificate_name: string;
  holder_name: string;
  issuer: string;
  issuer_logo?: string;
  issue_date: string;
  expiry_date?: string;
  certification_type: string;
  certification_standard?: string;
  verification_url?: string;
  certificate_description: string;
  skills_certified?: string[];
  achievement_level?: string;
  signature?: string;
  signature_title?: string;
  qr_verification_instructions?: string;
  certificate_image?: string;
  authorized_by: string;
}

export interface CitationData extends QRDataBase {
  citation_id: string;
  document_title: string;
  author: string;
  publication_date: string;
  publication?: string;
  publisher?: string;
  doi?: string;
  issn?: string;
  isbn?: string;
  url?: string;
  citation_format: 'APA' | 'MLA' | 'Chicago' | 'Harvard' | 'IEEE' | 'Vancouver' | 'custom';
  custom_format?: string;
  abstract?: string;
  keywords?: string[];
  citation_text: string;
  reference_category?: string;
  access_type: 'open' | 'subscription' | 'limited';
  access_instructions?: string;
}

export interface ComplianceCertificateData extends QRDataBase {
  certificate_id: string;
  organization: string;
  standard_name: string;
  issuer: string;
  issue_date: string;
  expiry_date?: string;
  compliance_scope: string;
  compliance_status: 'compliant' | 'conditional' | 'non-compliant' | 'pending';
  certification_body: string;
  certification_body_logo?: string;
  authorized_by: string;
  signature?: string;
  verification_url?: string;
  compliance_details: string;
  restrictions?: string;
  additional_notes?: string;
  confidentiality_level?: string;
}

export interface ConsentFormData extends QRDataBase {
  form_id: string;
  organization: string;
  purpose: string;
  scope: string;
  consent_duration?: string;
  expiry_date?: string;
  revocation_instructions: string;
  data_controller: string;
  data_processor?: string;
  privacy_policy_url: string;
  terms_url?: string;
  consent_text: string;
  required_fields?: string[];
  optional_fields?: string[];
  age_verification_required: boolean;
  minimum_age?: number;
  authorized_by: string;
  jurisdiction?: string;
  legal_basis?: string;
}

export interface ContractData extends QRDataBase {
  contract_id: string;
  contract_title: string;
  parties: string[];
  effective_date: string;
  expiry_date?: string;
  contract_type: string;
  contract_status: 'draft' | 'pending' | 'active' | 'expired' | 'terminated';
  version: string;
  jurisdiction: string;
  governing_law: string;
  contract_value?: string;
  currency?: string;
  terms_summary: string;
  contract_url?: string;
  document_hash?: string;
  signatories: string[];
  digital_signatures?: string[];
  confidentiality_level: 'public' | 'internal' | 'confidential' | 'restricted';
}

export interface DocumentVerificationData extends QRDataBase {
  document_id: string;
  document_type: string;
  issuer: string;
  issue_date: string;
  expiry_date?: string;
  holder_name: string;
  holder_id?: string;
  verification_url: string;
  verification_instructions: string;
  verification_fields?: string[];
  security_features: string[];
  document_status: 'valid' | 'expired' | 'revoked' | 'suspended';
  verification_level: string;
  authorized_verifiers?: string[];
  document_hash?: string;
  revocation_url?: string;
  authentication_required: boolean;
}

export interface LabEquipmentData extends QRDataBase {
  equipment_id: string;
  equipment_name: string;
  model: string;
  manufacturer: string;
  serial_number: string;
  lab_location: string;
  department: string;
  purchase_date?: string;
  warranty_expiry?: string;
  last_maintenance_date?: string;
  next_maintenance_date?: string;
  calibration_date?: string;
  calibration_due_date?: string;
  operational_status: 'operational' | 'maintenance' | 'repair' | 'decommissioned';
  safety_requirements: string;
  operating_instructions_url?: string;
  responsible_person: string;
  contact_information: string;
  hazard_warnings?: string;
  disposal_instructions?: string;
}

export interface LegalNoticeData extends QRDataBase {
  notice_id: string;
  notice_type: string;
  issuer: string;
  effective_date: string;
  expiry_date?: string;
  jurisdiction: string;
  legal_authority: string;
  notice_content: string;
  target_audience: string;
  compliance_requirements: string;
  penalties_for_non_compliance?: string;
  additional_resources?: string;
  contact_information: string;
  notice_url?: string;
  language: string;
  translated_versions?: string[];
  verification_method?: string;
}

export interface RegulatoryFilingData extends QRDataBase {
  filing_id: string;
  filing_type: string;
  organization: string;
  regulatory_body: string;
  filing_date: string;
  due_date?: string;
  status: 'draft' | 'submitted' | 'under-review' | 'approved' | 'rejected' | 'amended';
  jurisdiction: string;
  filing_period?: string;
  authorized_representative: string;
  contact_information: string;
  filing_summary: string;
  filing_url?: string;
  document_references?: string[];
  confidentiality_level: 'public' | 'internal' | 'confidential' | 'restricted';
  supporting_documents?: string[];
  next_filing_date?: string;
}