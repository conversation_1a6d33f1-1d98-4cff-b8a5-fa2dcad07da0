{"timestamp": "2025-06-12T17:05:13.414902", "level": "DEBUG", "logger": "backend.main", "message": "[Auth] Enforced Bearer token authentication for request", "module": "main", "function": "bearer_token_auth_middleware", "line": 274}
{"timestamp": "2025-06-12T17:05:18.256019", "level": "DEBUG", "logger": "backend.core.security.jwt_keys", "message": "Token contains key ID: 8be4a455-8974-4e97-b307-6c8428b2c9ff", "module": "jwt_keys", "function": "decode_token", "line": 188}
{"timestamp": "2025-06-12T17:05:18.256114", "level": "DEBUG", "logger": "backend.core.security.jwt_keys", "message": "Using key ID 8be4a455-8974-4e97-b307-6c8428b2c9ff for verification", "module": "jwt_keys", "function": "decode_token", "line": 195}
{"timestamp": "2025-06-12T17:05:18.256573", "level": "DEBUG", "logger": "backend.services.auth", "message": "JWT decoded successfully with key manager", "module": "auth", "function": "authenticate_token", "line": 683}
{"timestamp": "2025-06-12T17:05:18.274809", "level": "DEBUG", "logger": "backend.services.db_utils", "message": "Query 'auth_lookup_e4ae2d8f-b32f-52d7-99cd-8a33b19652c7' completed in 0.013s", "module": "db_utils", "function": "execute_query_with_timeout", "line": 68}
{"timestamp": "2025-06-12T17:05:18.274879", "level": "DEBUG", "logger": "backend.services.db_utils", "message": "Auth query 'auth_lookup_e4ae2d8f-b32f-52d7-99cd-8a33b19652c7' successful, result format preserved", "module": "db_utils", "function": "execute_query_with_timeout", "line": 72}
{"timestamp": "2025-06-12T17:05:18.282569", "level": "DEBUG", "logger": "backend.services.auth", "message": "Authentication attempt with token prefix: eyJhbGciOi...", "module": "auth", "function": "get_current_user", "line": 727}
{"timestamp": "2025-06-12T17:05:18.282730", "level": "DEBUG", "logger": "backend.core.security.jwt_keys", "message": "Token contains key ID: 8be4a455-8974-4e97-b307-6c8428b2c9ff", "module": "jwt_keys", "function": "decode_token", "line": 188}
{"timestamp": "2025-06-12T17:05:18.282770", "level": "DEBUG", "logger": "backend.core.security.jwt_keys", "message": "Using key ID 8be4a455-8974-4e97-b307-6c8428b2c9ff for verification", "module": "jwt_keys", "function": "decode_token", "line": 195}
{"timestamp": "2025-06-12T17:05:18.283183", "level": "DEBUG", "logger": "backend.services.auth", "message": "JWT decoded successfully with key manager", "module": "auth", "function": "authenticate_token", "line": 683}
{"timestamp": "2025-06-12T17:05:18.285513", "level": "DEBUG", "logger": "backend.services.db_utils", "message": "Query 'auth_lookup_e4ae2d8f-b32f-52d7-99cd-8a33b19652c7' completed in 0.002s", "module": "db_utils", "function": "execute_query_with_timeout", "line": 68}
{"timestamp": "2025-06-12T17:05:18.285565", "level": "DEBUG", "logger": "backend.services.db_utils", "message": "Auth query 'auth_lookup_e4ae2d8f-b32f-52d7-99cd-8a33b19652c7' successful, result format preserved", "module": "db_utils", "function": "execute_query_with_timeout", "line": 72}
{"timestamp": "2025-06-12T17:05:18.285623", "level": "DEBUG", "logger": "backend.services.auth", "message": "Successfully authenticated user: <EMAIL>", "module": "auth", "function": "get_current_user", "line": 732}
{"timestamp": "2025-06-12T17:05:18.285658", "level": "DEBUG", "logger": "backend.services.auth", "message": "Successfully authenticated user: <EMAIL>", "module": "auth", "function": "get_current_user", "line": 738}
{"timestamp": "2025-06-12T17:05:18.290599", "level": "DEBUG", "logger": "backend.routes.qr_preview", "message": "Received QR preview SVG request: {\"qr_type\": \"scooter_unlock\", \"data\": {\"scooter_id\": \"SCOOT123\", \"unlock_code\": \"UNLOCK42\", \"title\": \"Scooter_unlock QR Code\", \"description\": \"Scan to view scooter_unlock content\"}, \"designOptions\": {\"size\": 256, \"margin\": 10, \"pattern\": \"square\", \"marker_border_style\": \"square\", \"marker_center_style\": \"square\", \"logo_size\": 0.3, \"logo_position\": \"center\", \"logo_opacity\": 1, \"frame_style\": \"none\", \"frame_font\": \"SF Pro\", \"frame_font_size\": 16, \"foreground_color\": \"#000000\", \"background_color\": \"", "module": "qr_preview", "function": "preview_qr_svg", "line": 242}
{"timestamp": "2025-06-12T17:05:18.291149", "level": "DEBUG", "logger": "backend.routes.qr_preview", "message": "Form data keys: ['request']", "module": "qr_preview", "function": "preview_qr_svg", "line": 297}
{"timestamp": "2025-06-12T17:05:18.291194", "level": "DEBUG", "logger": "backend.routes.qr_preview", "message": "No logo key found in form data", "module": "qr_preview", "function": "preview_qr_svg", "line": 309}
{"timestamp": "2025-06-12T17:05:18.291297", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Formatting content for QR type: qrtype.scooter_unlock, content keys: ['scooter_id', 'unlock_code', 'title', 'description']", "module": "qr_preview", "function": "_format_qr_data", "line": 876}
{"timestamp": "2025-06-12T17:05:18.312716", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Logo remove background setting: False, has logo: False", "module": "qr_preview", "function": "generate_preview_svg_modular", "line": 727}
{"timestamp": "2025-06-12T17:05:18.330285", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Drawing 3 finder patterns at positions: [(0, 0), (42, 0), (0, 42)]", "module": "qr_preview", "function": "generate_preview_svg_modular", "line": 775}
{"timestamp": "2025-06-12T17:05:18.330354", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "QR size: 49, border_style: square, center_style: square", "module": "qr_preview", "function": "generate_preview_svg_modular", "line": 776}
{"timestamp": "2025-06-12T17:05:18.330393", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Drawing finder pattern 1/3 at position (0, 0)", "module": "qr_preview", "function": "generate_preview_svg_modular", "line": 780}
{"timestamp": "2025-06-12T17:05:18.330765", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "_draw_finder_marker called: mx=0, my=0, module_size=5.918367346938775, border_style=square, center_style=square, border_color=#000000, center_color=#000000, offset=(20, 20)", "module": "qr_preview", "function": "_draw_finder_marker", "line": 97}
{"timestamp": "2025-06-12T17:05:18.331187", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Creating finder pattern group with id='marker-0-0'", "module": "qr_preview", "function": "_draw_finder_marker", "line": 110}
{"timestamp": "2025-06-12T17:05:18.331230", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Drawing square border:", "module": "qr_preview", "function": "_draw_finder_marker", "line": 160}
{"timestamp": "2025-06-12T17:05:18.331277", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "  Outer: insert=(20.0, 20.0), size=(41.42857142857143, 41.42857142857143)", "module": "qr_preview", "function": "_draw_finder_marker", "line": 161}
{"timestamp": "2025-06-12T17:05:18.331451", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "  Inner: insert=(27.10204081632653, 27.10204081632653), size=(27.224489795918366, 27.224489795918366)", "module": "qr_preview", "function": "_draw_finder_marker", "line": 162}
{"timestamp": "2025-06-12T17:05:18.331787", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Drawing marker center: style=square, color=#000000, pos=(34.79591836734694, 34.79591836734694), size=11.83673469387755", "module": "qr_preview", "function": "_draw_marker_center", "line": 179}
{"timestamp": "2025-06-12T17:05:18.331869", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Successfully added finder pattern group to SVG", "module": "qr_preview", "function": "_draw_finder_marker", "line": 171}
{"timestamp": "2025-06-12T17:05:18.331908", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Successfully drew finder pattern 1/3", "module": "qr_preview", "function": "generate_preview_svg_modular", "line": 782}
{"timestamp": "2025-06-12T17:05:18.331943", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Drawing finder pattern 2/3 at position (42, 0)", "module": "qr_preview", "function": "generate_preview_svg_modular", "line": 780}
{"timestamp": "2025-06-12T17:05:18.332039", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "_draw_finder_marker called: mx=42, my=0, module_size=5.918367346938775, border_style=square, center_style=square, border_color=#000000, center_color=#000000, offset=(20, 20)", "module": "qr_preview", "function": "_draw_finder_marker", "line": 97}
{"timestamp": "2025-06-12T17:05:18.332081", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Creating finder pattern group with id='marker-42-0'", "module": "qr_preview", "function": "_draw_finder_marker", "line": 110}
{"timestamp": "2025-06-12T17:05:18.332112", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Drawing square border:", "module": "qr_preview", "function": "_draw_finder_marker", "line": 160}
{"timestamp": "2025-06-12T17:05:18.332145", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "  Outer: insert=(268.57142857142856, 20.0), size=(41.42857142857143, 41.42857142857143)", "module": "qr_preview", "function": "_draw_finder_marker", "line": 161}
{"timestamp": "2025-06-12T17:05:18.332179", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "  Inner: insert=(275.67346938775506, 27.10204081632653), size=(27.224489795918366, 27.224489795918366)", "module": "qr_preview", "function": "_draw_finder_marker", "line": 162}
{"timestamp": "2025-06-12T17:05:18.332241", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Drawing marker center: style=square, color=#000000, pos=(283.3673469387755, 34.79591836734694), size=11.83673469387755", "module": "qr_preview", "function": "_draw_marker_center", "line": 179}
{"timestamp": "2025-06-12T17:05:18.332287", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Successfully added finder pattern group to SVG", "module": "qr_preview", "function": "_draw_finder_marker", "line": 171}
{"timestamp": "2025-06-12T17:05:18.332321", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Successfully drew finder pattern 2/3", "module": "qr_preview", "function": "generate_preview_svg_modular", "line": 782}
{"timestamp": "2025-06-12T17:05:18.332352", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Drawing finder pattern 3/3 at position (0, 42)", "module": "qr_preview", "function": "generate_preview_svg_modular", "line": 780}
{"timestamp": "2025-06-12T17:05:18.332386", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "_draw_finder_marker called: mx=0, my=42, module_size=5.918367346938775, border_style=square, center_style=square, border_color=#000000, center_color=#000000, offset=(20, 20)", "module": "qr_preview", "function": "_draw_finder_marker", "line": 97}
{"timestamp": "2025-06-12T17:05:18.332427", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Creating finder pattern group with id='marker-0-42'", "module": "qr_preview", "function": "_draw_finder_marker", "line": 110}
{"timestamp": "2025-06-12T17:05:18.332456", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Drawing square border:", "module": "qr_preview", "function": "_draw_finder_marker", "line": 160}
{"timestamp": "2025-06-12T17:05:18.332488", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "  Outer: insert=(20.0, 268.57142857142856), size=(41.42857142857143, 41.42857142857143)", "module": "qr_preview", "function": "_draw_finder_marker", "line": 161}
{"timestamp": "2025-06-12T17:05:18.332520", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "  Inner: insert=(27.10204081632653, 275.67346938775506), size=(27.224489795918366, 27.224489795918366)", "module": "qr_preview", "function": "_draw_finder_marker", "line": 162}
{"timestamp": "2025-06-12T17:05:18.332581", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Drawing marker center: style=square, color=#000000, pos=(34.79591836734694, 283.3673469387755), size=11.83673469387755", "module": "qr_preview", "function": "_draw_marker_center", "line": 179}
{"timestamp": "2025-06-12T17:05:18.332643", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Successfully added finder pattern group to SVG", "module": "qr_preview", "function": "_draw_finder_marker", "line": 171}
{"timestamp": "2025-06-12T17:05:18.332673", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Successfully drew finder pattern 3/3", "module": "qr_preview", "function": "generate_preview_svg_modular", "line": 782}
{"timestamp": "2025-06-12T17:05:18.356342", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Generated SVG length: 176251", "module": "qr_preview", "function": "generate_preview_svg_modular", "line": 802}
{"timestamp": "2025-06-12T17:05:18.356482", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "SVG contains marker-0-0: True", "module": "qr_preview", "function": "generate_preview_svg_modular", "line": 803}
{"timestamp": "2025-06-12T17:05:18.356585", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "SVG contains marker-18-0: False", "module": "qr_preview", "function": "generate_preview_svg_modular", "line": 804}
{"timestamp": "2025-06-12T17:05:18.356689", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "SVG contains marker-0-18: False", "module": "qr_preview", "function": "generate_preview_svg_modular", "line": 805}
{"timestamp": "2025-06-12T17:05:18.356976", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "SVG marker counts - outer: 3, inner: 3, dot: 0", "module": "qr_preview", "function": "generate_preview_svg_modular", "line": 813}
{"timestamp": "2025-06-12T17:05:18.357008", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "SVG label counts - text: 0, background: 0", "module": "qr_preview", "function": "generate_preview_svg_modular", "line": 814}
{"timestamp": "2025-06-12T17:05:18.359307", "level": "DEBUG", "logger": "backend.main", "message": "[Auth] Enforced Bearer token authentication for request", "module": "main", "function": "bearer_token_auth_middleware", "line": 274}
{"timestamp": "2025-06-12T17:05:21.092196", "level": "DEBUG", "logger": "backend.core.security.jwt_keys", "message": "Token contains key ID: 8be4a455-8974-4e97-b307-6c8428b2c9ff", "module": "jwt_keys", "function": "decode_token", "line": 188}
{"timestamp": "2025-06-12T17:05:21.092418", "level": "DEBUG", "logger": "backend.core.security.jwt_keys", "message": "Using key ID 8be4a455-8974-4e97-b307-6c8428b2c9ff for verification", "module": "jwt_keys", "function": "decode_token", "line": 195}
{"timestamp": "2025-06-12T17:05:21.092579", "level": "DEBUG", "logger": "backend.services.auth", "message": "JWT decoded successfully with key manager", "module": "auth", "function": "authenticate_token", "line": 683}
{"timestamp": "2025-06-12T17:05:21.094529", "level": "DEBUG", "logger": "backend.services.db_utils", "message": "Query 'auth_lookup_e4ae2d8f-b32f-52d7-99cd-8a33b19652c7' completed in 0.002s", "module": "db_utils", "function": "execute_query_with_timeout", "line": 68}
{"timestamp": "2025-06-12T17:05:21.094598", "level": "DEBUG", "logger": "backend.services.db_utils", "message": "Auth query 'auth_lookup_e4ae2d8f-b32f-52d7-99cd-8a33b19652c7' successful, result format preserved", "module": "db_utils", "function": "execute_query_with_timeout", "line": 72}
{"timestamp": "2025-06-12T17:05:21.106282", "level": "DEBUG", "logger": "backend.services.auth", "message": "Authentication attempt with token prefix: eyJhbGciOi...", "module": "auth", "function": "get_current_user", "line": 727}
{"timestamp": "2025-06-12T17:05:21.106410", "level": "DEBUG", "logger": "backend.core.security.jwt_keys", "message": "Token contains key ID: 8be4a455-8974-4e97-b307-6c8428b2c9ff", "module": "jwt_keys", "function": "decode_token", "line": 188}
{"timestamp": "2025-06-12T17:05:21.106451", "level": "DEBUG", "logger": "backend.core.security.jwt_keys", "message": "Using key ID 8be4a455-8974-4e97-b307-6c8428b2c9ff for verification", "module": "jwt_keys", "function": "decode_token", "line": 195}
{"timestamp": "2025-06-12T17:05:21.106638", "level": "DEBUG", "logger": "backend.services.auth", "message": "JWT decoded successfully with key manager", "module": "auth", "function": "authenticate_token", "line": 683}
{"timestamp": "2025-06-12T17:05:21.109039", "level": "DEBUG", "logger": "backend.services.db_utils", "message": "Query 'auth_lookup_e4ae2d8f-b32f-52d7-99cd-8a33b19652c7' completed in 0.002s", "module": "db_utils", "function": "execute_query_with_timeout", "line": 68}
{"timestamp": "2025-06-12T17:05:21.109109", "level": "DEBUG", "logger": "backend.services.db_utils", "message": "Auth query 'auth_lookup_e4ae2d8f-b32f-52d7-99cd-8a33b19652c7' successful, result format preserved", "module": "db_utils", "function": "execute_query_with_timeout", "line": 72}
{"timestamp": "2025-06-12T17:05:21.109183", "level": "DEBUG", "logger": "backend.services.auth", "message": "Successfully authenticated user: <EMAIL>", "module": "auth", "function": "get_current_user", "line": 732}
{"timestamp": "2025-06-12T17:05:21.109233", "level": "DEBUG", "logger": "backend.services.auth", "message": "Successfully authenticated user: <EMAIL>", "module": "auth", "function": "get_current_user", "line": 738}
{"timestamp": "2025-06-12T17:05:21.111057", "level": "DEBUG", "logger": "backend.routes.qr_preview", "message": "Received QR preview SVG request: {\"qr_type\": \"scooter_unlock\", \"data\": {\"scooter_id\": \"SCOOT123\", \"unlock_code\": \"UNLOCK42\", \"title\": \"Scooter_unlock QR Code\", \"description\": \"Scan to view scooter_unlock content\"}, \"designOptions\": {\"size\": 256, \"margin\": 10, \"pattern\": \"square\", \"marker_border_style\": \"square\", \"marker_center_style\": \"square\", \"logo_size\": 0.3, \"logo_position\": \"center\", \"logo_opacity\": 1, \"frame_style\": \"none\", \"frame_font\": \"SF Pro\", \"frame_font_size\": 16, \"foreground_color\": \"#000000\", \"background_color\": \"", "module": "qr_preview", "function": "preview_qr_svg", "line": 242}
{"timestamp": "2025-06-12T17:05:21.111154", "level": "DEBUG", "logger": "backend.routes.qr_preview", "message": "Form data keys: ['request']", "module": "qr_preview", "function": "preview_qr_svg", "line": 297}
{"timestamp": "2025-06-12T17:05:21.111195", "level": "DEBUG", "logger": "backend.routes.qr_preview", "message": "No logo key found in form data", "module": "qr_preview", "function": "preview_qr_svg", "line": 309}
{"timestamp": "2025-06-12T17:05:21.111245", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Formatting content for QR type: qrtype.scooter_unlock, content keys: ['scooter_id', 'unlock_code', 'title', 'description']", "module": "qr_preview", "function": "_format_qr_data", "line": 876}
{"timestamp": "2025-06-12T17:05:21.127630", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Logo remove background setting: False, has logo: False", "module": "qr_preview", "function": "generate_preview_svg_modular", "line": 727}
{"timestamp": "2025-06-12T17:05:21.140575", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Drawing 3 finder patterns at positions: [(0, 0), (42, 0), (0, 42)]", "module": "qr_preview", "function": "generate_preview_svg_modular", "line": 775}
{"timestamp": "2025-06-12T17:05:21.140634", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "QR size: 49, border_style: square, center_style: square", "module": "qr_preview", "function": "generate_preview_svg_modular", "line": 776}
{"timestamp": "2025-06-12T17:05:21.140678", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Drawing finder pattern 1/3 at position (0, 0)", "module": "qr_preview", "function": "generate_preview_svg_modular", "line": 780}
{"timestamp": "2025-06-12T17:05:21.140735", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "_draw_finder_marker called: mx=0, my=0, module_size=5.918367346938775, border_style=square, center_style=square, border_color=#000000, center_color=#000000, offset=(20, 20)", "module": "qr_preview", "function": "_draw_finder_marker", "line": 97}
{"timestamp": "2025-06-12T17:05:21.140830", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Creating finder pattern group with id='marker-0-0'", "module": "qr_preview", "function": "_draw_finder_marker", "line": 110}
{"timestamp": "2025-06-12T17:05:21.140873", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Drawing square border:", "module": "qr_preview", "function": "_draw_finder_marker", "line": 160}
{"timestamp": "2025-06-12T17:05:21.140915", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "  Outer: insert=(20.0, 20.0), size=(41.42857142857143, 41.42857142857143)", "module": "qr_preview", "function": "_draw_finder_marker", "line": 161}
{"timestamp": "2025-06-12T17:05:21.140958", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "  Inner: insert=(27.10204081632653, 27.10204081632653), size=(27.224489795918366, 27.224489795918366)", "module": "qr_preview", "function": "_draw_finder_marker", "line": 162}
{"timestamp": "2025-06-12T17:05:21.141028", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Drawing marker center: style=square, color=#000000, pos=(34.79591836734694, 34.79591836734694), size=11.83673469387755", "module": "qr_preview", "function": "_draw_marker_center", "line": 179}
{"timestamp": "2025-06-12T17:05:21.141078", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Successfully added finder pattern group to SVG", "module": "qr_preview", "function": "_draw_finder_marker", "line": 171}
{"timestamp": "2025-06-12T17:05:21.141111", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Successfully drew finder pattern 1/3", "module": "qr_preview", "function": "generate_preview_svg_modular", "line": 782}
{"timestamp": "2025-06-12T17:05:21.141143", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Drawing finder pattern 2/3 at position (42, 0)", "module": "qr_preview", "function": "generate_preview_svg_modular", "line": 780}
{"timestamp": "2025-06-12T17:05:21.141181", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "_draw_finder_marker called: mx=42, my=0, module_size=5.918367346938775, border_style=square, center_style=square, border_color=#000000, center_color=#000000, offset=(20, 20)", "module": "qr_preview", "function": "_draw_finder_marker", "line": 97}
{"timestamp": "2025-06-12T17:05:21.141223", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Creating finder pattern group with id='marker-42-0'", "module": "qr_preview", "function": "_draw_finder_marker", "line": 110}
{"timestamp": "2025-06-12T17:05:21.141259", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Drawing square border:", "module": "qr_preview", "function": "_draw_finder_marker", "line": 160}
{"timestamp": "2025-06-12T17:05:21.141294", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "  Outer: insert=(268.57142857142856, 20.0), size=(41.42857142857143, 41.42857142857143)", "module": "qr_preview", "function": "_draw_finder_marker", "line": 161}
{"timestamp": "2025-06-12T17:05:21.141330", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "  Inner: insert=(275.67346938775506, 27.10204081632653), size=(27.224489795918366, 27.224489795918366)", "module": "qr_preview", "function": "_draw_finder_marker", "line": 162}
{"timestamp": "2025-06-12T17:05:21.141548", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Drawing marker center: style=square, color=#000000, pos=(283.3673469387755, 34.79591836734694), size=11.83673469387755", "module": "qr_preview", "function": "_draw_marker_center", "line": 179}
{"timestamp": "2025-06-12T17:05:21.141665", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Successfully added finder pattern group to SVG", "module": "qr_preview", "function": "_draw_finder_marker", "line": 171}
{"timestamp": "2025-06-12T17:05:21.141739", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Successfully drew finder pattern 2/3", "module": "qr_preview", "function": "generate_preview_svg_modular", "line": 782}
{"timestamp": "2025-06-12T17:05:21.141783", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Drawing finder pattern 3/3 at position (0, 42)", "module": "qr_preview", "function": "generate_preview_svg_modular", "line": 780}
{"timestamp": "2025-06-12T17:05:21.141828", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "_draw_finder_marker called: mx=0, my=42, module_size=5.918367346938775, border_style=square, center_style=square, border_color=#000000, center_color=#000000, offset=(20, 20)", "module": "qr_preview", "function": "_draw_finder_marker", "line": 97}
{"timestamp": "2025-06-12T17:05:21.141882", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Creating finder pattern group with id='marker-0-42'", "module": "qr_preview", "function": "_draw_finder_marker", "line": 110}
{"timestamp": "2025-06-12T17:05:21.141926", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Drawing square border:", "module": "qr_preview", "function": "_draw_finder_marker", "line": 160}
{"timestamp": "2025-06-12T17:05:21.141966", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "  Outer: insert=(20.0, 268.57142857142856), size=(41.42857142857143, 41.42857142857143)", "module": "qr_preview", "function": "_draw_finder_marker", "line": 161}
{"timestamp": "2025-06-12T17:05:21.142005", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "  Inner: insert=(27.10204081632653, 275.67346938775506), size=(27.224489795918366, 27.224489795918366)", "module": "qr_preview", "function": "_draw_finder_marker", "line": 162}
{"timestamp": "2025-06-12T17:05:21.142076", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Drawing marker center: style=square, color=#000000, pos=(34.79591836734694, 283.3673469387755), size=11.83673469387755", "module": "qr_preview", "function": "_draw_marker_center", "line": 179}
{"timestamp": "2025-06-12T17:05:21.142130", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Successfully added finder pattern group to SVG", "module": "qr_preview", "function": "_draw_finder_marker", "line": 171}
{"timestamp": "2025-06-12T17:05:21.142167", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Successfully drew finder pattern 3/3", "module": "qr_preview", "function": "generate_preview_svg_modular", "line": 782}
{"timestamp": "2025-06-12T17:05:21.161798", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Generated SVG length: 176251", "module": "qr_preview", "function": "generate_preview_svg_modular", "line": 802}
{"timestamp": "2025-06-12T17:05:21.161933", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "SVG contains marker-0-0: True", "module": "qr_preview", "function": "generate_preview_svg_modular", "line": 803}
{"timestamp": "2025-06-12T17:05:21.162036", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "SVG contains marker-18-0: False", "module": "qr_preview", "function": "generate_preview_svg_modular", "line": 804}
{"timestamp": "2025-06-12T17:05:21.162144", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "SVG contains marker-0-18: False", "module": "qr_preview", "function": "generate_preview_svg_modular", "line": 805}
{"timestamp": "2025-06-12T17:05:21.162431", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "SVG marker counts - outer: 3, inner: 3, dot: 0", "module": "qr_preview", "function": "generate_preview_svg_modular", "line": 813}
{"timestamp": "2025-06-12T17:05:21.162463", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "SVG label counts - text: 0, background: 0", "module": "qr_preview", "function": "generate_preview_svg_modular", "line": 814}
{"timestamp": "2025-06-12T17:05:21.165597", "level": "DEBUG", "logger": "backend.main", "message": "[Auth] Enforced Bearer token authentication for request", "module": "main", "function": "bearer_token_auth_middleware", "line": 274}
{"timestamp": "2025-06-12T17:05:21.408166", "level": "DEBUG", "logger": "backend.core.security.jwt_keys", "message": "Token contains key ID: 8be4a455-8974-4e97-b307-6c8428b2c9ff", "module": "jwt_keys", "function": "decode_token", "line": 188}
{"timestamp": "2025-06-12T17:05:21.408232", "level": "DEBUG", "logger": "backend.core.security.jwt_keys", "message": "Using key ID 8be4a455-8974-4e97-b307-6c8428b2c9ff for verification", "module": "jwt_keys", "function": "decode_token", "line": 195}
{"timestamp": "2025-06-12T17:05:21.408483", "level": "DEBUG", "logger": "backend.services.auth", "message": "JWT decoded successfully with key manager", "module": "auth", "function": "authenticate_token", "line": 683}
{"timestamp": "2025-06-12T17:05:21.410586", "level": "DEBUG", "logger": "backend.services.db_utils", "message": "Query 'auth_lookup_e4ae2d8f-b32f-52d7-99cd-8a33b19652c7' completed in 0.002s", "module": "db_utils", "function": "execute_query_with_timeout", "line": 68}
{"timestamp": "2025-06-12T17:05:21.410642", "level": "DEBUG", "logger": "backend.services.db_utils", "message": "Auth query 'auth_lookup_e4ae2d8f-b32f-52d7-99cd-8a33b19652c7' successful, result format preserved", "module": "db_utils", "function": "execute_query_with_timeout", "line": 72}
{"timestamp": "2025-06-12T17:05:21.460896", "level": "DEBUG", "logger": "backend.services.auth", "message": "Authentication attempt with token prefix: eyJhbGciOi...", "module": "auth", "function": "get_current_user", "line": 727}
{"timestamp": "2025-06-12T17:05:21.461542", "level": "DEBUG", "logger": "backend.core.security.jwt_keys", "message": "Token contains key ID: 8be4a455-8974-4e97-b307-6c8428b2c9ff", "module": "jwt_keys", "function": "decode_token", "line": 188}
{"timestamp": "2025-06-12T17:05:21.462951", "level": "DEBUG", "logger": "backend.core.security.jwt_keys", "message": "Using key ID 8be4a455-8974-4e97-b307-6c8428b2c9ff for verification", "module": "jwt_keys", "function": "decode_token", "line": 195}
{"timestamp": "2025-06-12T17:05:21.463130", "level": "DEBUG", "logger": "backend.services.auth", "message": "JWT decoded successfully with key manager", "module": "auth", "function": "authenticate_token", "line": 683}
{"timestamp": "2025-06-12T17:05:21.464562", "level": "DEBUG", "logger": "backend.services.db_utils", "message": "Query 'auth_lookup_e4ae2d8f-b32f-52d7-99cd-8a33b19652c7' completed in 0.001s", "module": "db_utils", "function": "execute_query_with_timeout", "line": 68}
{"timestamp": "2025-06-12T17:05:21.467541", "level": "DEBUG", "logger": "backend.services.db_utils", "message": "Auth query 'auth_lookup_e4ae2d8f-b32f-52d7-99cd-8a33b19652c7' successful, result format preserved", "module": "db_utils", "function": "execute_query_with_timeout", "line": 72}
{"timestamp": "2025-06-12T17:05:21.467628", "level": "DEBUG", "logger": "backend.services.auth", "message": "Successfully authenticated user: <EMAIL>", "module": "auth", "function": "get_current_user", "line": 732}
{"timestamp": "2025-06-12T17:05:21.467680", "level": "DEBUG", "logger": "backend.services.auth", "message": "Successfully authenticated user: <EMAIL>", "module": "auth", "function": "get_current_user", "line": 738}
{"timestamp": "2025-06-12T17:05:21.468917", "level": "DEBUG", "logger": "backend.routes.qr_preview", "message": "Received QR preview SVG request: {\"qr_type\": \"scooter_unlock\", \"data\": {\"scooter_id\": \"SCOOT123\", \"unlock_code\": \"UNLOCK42\", \"title\": \"Scooter_unlock QR Code\", \"description\": \"Scan to view scooter_unlock content\"}, \"designOptions\": {\"size\": 256, \"margin\": 10, \"pattern\": \"square\", \"marker_border_style\": \"square\", \"marker_center_style\": \"square\", \"logo_size\": 0.3, \"logo_position\": \"center\", \"logo_opacity\": 1, \"frame_style\": \"none\", \"frame_font\": \"SF Pro\", \"frame_font_size\": 16, \"foreground_color\": \"#000000\", \"background_color\": \"", "module": "qr_preview", "function": "preview_qr_svg", "line": 242}
{"timestamp": "2025-06-12T17:05:21.469262", "level": "DEBUG", "logger": "backend.routes.qr_preview", "message": "Form data keys: ['request']", "module": "qr_preview", "function": "preview_qr_svg", "line": 297}
{"timestamp": "2025-06-12T17:05:21.469317", "level": "DEBUG", "logger": "backend.routes.qr_preview", "message": "No logo key found in form data", "module": "qr_preview", "function": "preview_qr_svg", "line": 309}
{"timestamp": "2025-06-12T17:05:21.469371", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Formatting content for QR type: qrtype.scooter_unlock, content keys: ['scooter_id', 'unlock_code', 'title', 'description']", "module": "qr_preview", "function": "_format_qr_data", "line": 876}
{"timestamp": "2025-06-12T17:05:21.485217", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Logo remove background setting: False, has logo: False", "module": "qr_preview", "function": "generate_preview_svg_modular", "line": 727}
{"timestamp": "2025-06-12T17:05:21.498003", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Drawing 3 finder patterns at positions: [(0, 0), (42, 0), (0, 42)]", "module": "qr_preview", "function": "generate_preview_svg_modular", "line": 775}
{"timestamp": "2025-06-12T17:05:21.498053", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "QR size: 49, border_style: square, center_style: square", "module": "qr_preview", "function": "generate_preview_svg_modular", "line": 776}
{"timestamp": "2025-06-12T17:05:21.498090", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Drawing finder pattern 1/3 at position (0, 0)", "module": "qr_preview", "function": "generate_preview_svg_modular", "line": 780}
{"timestamp": "2025-06-12T17:05:21.498131", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "_draw_finder_marker called: mx=0, my=0, module_size=5.918367346938775, border_style=square, center_style=square, border_color=#000000, center_color=#000000, offset=(20, 20)", "module": "qr_preview", "function": "_draw_finder_marker", "line": 97}
{"timestamp": "2025-06-12T17:05:21.498176", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Creating finder pattern group with id='marker-0-0'", "module": "qr_preview", "function": "_draw_finder_marker", "line": 110}
{"timestamp": "2025-06-12T17:05:21.498210", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Drawing square border:", "module": "qr_preview", "function": "_draw_finder_marker", "line": 160}
{"timestamp": "2025-06-12T17:05:21.498243", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "  Outer: insert=(20.0, 20.0), size=(41.42857142857143, 41.42857142857143)", "module": "qr_preview", "function": "_draw_finder_marker", "line": 161}
{"timestamp": "2025-06-12T17:05:21.498279", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "  Inner: insert=(27.10204081632653, 27.10204081632653), size=(27.224489795918366, 27.224489795918366)", "module": "qr_preview", "function": "_draw_finder_marker", "line": 162}
{"timestamp": "2025-06-12T17:05:21.498353", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Drawing marker center: style=square, color=#000000, pos=(34.79591836734694, 34.79591836734694), size=11.83673469387755", "module": "qr_preview", "function": "_draw_marker_center", "line": 179}
{"timestamp": "2025-06-12T17:05:21.498406", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Successfully added finder pattern group to SVG", "module": "qr_preview", "function": "_draw_finder_marker", "line": 171}
{"timestamp": "2025-06-12T17:05:21.498435", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Successfully drew finder pattern 1/3", "module": "qr_preview", "function": "generate_preview_svg_modular", "line": 782}
{"timestamp": "2025-06-12T17:05:21.498463", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Drawing finder pattern 2/3 at position (42, 0)", "module": "qr_preview", "function": "generate_preview_svg_modular", "line": 780}
{"timestamp": "2025-06-12T17:05:21.498523", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "_draw_finder_marker called: mx=42, my=0, module_size=5.918367346938775, border_style=square, center_style=square, border_color=#000000, center_color=#000000, offset=(20, 20)", "module": "qr_preview", "function": "_draw_finder_marker", "line": 97}
{"timestamp": "2025-06-12T17:05:21.498561", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Creating finder pattern group with id='marker-42-0'", "module": "qr_preview", "function": "_draw_finder_marker", "line": 110}
{"timestamp": "2025-06-12T17:05:21.498592", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Drawing square border:", "module": "qr_preview", "function": "_draw_finder_marker", "line": 160}
{"timestamp": "2025-06-12T17:05:21.498625", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "  Outer: insert=(268.57142857142856, 20.0), size=(41.42857142857143, 41.42857142857143)", "module": "qr_preview", "function": "_draw_finder_marker", "line": 161}
{"timestamp": "2025-06-12T17:05:21.498658", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "  Inner: insert=(275.67346938775506, 27.10204081632653), size=(27.224489795918366, 27.224489795918366)", "module": "qr_preview", "function": "_draw_finder_marker", "line": 162}
{"timestamp": "2025-06-12T17:05:21.498714", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Drawing marker center: style=square, color=#000000, pos=(283.3673469387755, 34.79591836734694), size=11.83673469387755", "module": "qr_preview", "function": "_draw_marker_center", "line": 179}
{"timestamp": "2025-06-12T17:05:21.498757", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Successfully added finder pattern group to SVG", "module": "qr_preview", "function": "_draw_finder_marker", "line": 171}
{"timestamp": "2025-06-12T17:05:21.498788", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Successfully drew finder pattern 2/3", "module": "qr_preview", "function": "generate_preview_svg_modular", "line": 782}
{"timestamp": "2025-06-12T17:05:21.498817", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Drawing finder pattern 3/3 at position (0, 42)", "module": "qr_preview", "function": "generate_preview_svg_modular", "line": 780}
{"timestamp": "2025-06-12T17:05:21.498848", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "_draw_finder_marker called: mx=0, my=42, module_size=5.918367346938775, border_style=square, center_style=square, border_color=#000000, center_color=#000000, offset=(20, 20)", "module": "qr_preview", "function": "_draw_finder_marker", "line": 97}
{"timestamp": "2025-06-12T17:05:21.498886", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Creating finder pattern group with id='marker-0-42'", "module": "qr_preview", "function": "_draw_finder_marker", "line": 110}
{"timestamp": "2025-06-12T17:05:21.498917", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Drawing square border:", "module": "qr_preview", "function": "_draw_finder_marker", "line": 160}
{"timestamp": "2025-06-12T17:05:21.498948", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "  Outer: insert=(20.0, 268.57142857142856), size=(41.42857142857143, 41.42857142857143)", "module": "qr_preview", "function": "_draw_finder_marker", "line": 161}
{"timestamp": "2025-06-12T17:05:21.498980", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "  Inner: insert=(27.10204081632653, 275.67346938775506), size=(27.224489795918366, 27.224489795918366)", "module": "qr_preview", "function": "_draw_finder_marker", "line": 162}
{"timestamp": "2025-06-12T17:05:21.499034", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Drawing marker center: style=square, color=#000000, pos=(34.79591836734694, 283.3673469387755), size=11.83673469387755", "module": "qr_preview", "function": "_draw_marker_center", "line": 179}
{"timestamp": "2025-06-12T17:05:21.499089", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Successfully added finder pattern group to SVG", "module": "qr_preview", "function": "_draw_finder_marker", "line": 171}
{"timestamp": "2025-06-12T17:05:21.499120", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Successfully drew finder pattern 3/3", "module": "qr_preview", "function": "generate_preview_svg_modular", "line": 782}
{"timestamp": "2025-06-12T17:05:21.516970", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Generated SVG length: 176251", "module": "qr_preview", "function": "generate_preview_svg_modular", "line": 802}
{"timestamp": "2025-06-12T17:05:21.517090", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "SVG contains marker-0-0: True", "module": "qr_preview", "function": "generate_preview_svg_modular", "line": 803}
{"timestamp": "2025-06-12T17:05:21.517193", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "SVG contains marker-18-0: False", "module": "qr_preview", "function": "generate_preview_svg_modular", "line": 804}
{"timestamp": "2025-06-12T17:05:21.517300", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "SVG contains marker-0-18: False", "module": "qr_preview", "function": "generate_preview_svg_modular", "line": 805}
{"timestamp": "2025-06-12T17:05:21.517587", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "SVG marker counts - outer: 3, inner: 3, dot: 0", "module": "qr_preview", "function": "generate_preview_svg_modular", "line": 813}
{"timestamp": "2025-06-12T17:05:21.517619", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "SVG label counts - text: 0, background: 0", "module": "qr_preview", "function": "generate_preview_svg_modular", "line": 814}
{"timestamp": "2025-06-12T17:05:21.519273", "level": "DEBUG", "logger": "backend.main", "message": "[Auth] Enforced Bearer token authentication for request", "module": "main", "function": "bearer_token_auth_middleware", "line": 274}
{"timestamp": "2025-06-12T17:05:21.914261", "level": "DEBUG", "logger": "backend.core.security.jwt_keys", "message": "Token contains key ID: 8be4a455-8974-4e97-b307-6c8428b2c9ff", "module": "jwt_keys", "function": "decode_token", "line": 188}
{"timestamp": "2025-06-12T17:05:21.914323", "level": "DEBUG", "logger": "backend.core.security.jwt_keys", "message": "Using key ID 8be4a455-8974-4e97-b307-6c8428b2c9ff for verification", "module": "jwt_keys", "function": "decode_token", "line": 195}
{"timestamp": "2025-06-12T17:05:21.914449", "level": "DEBUG", "logger": "backend.services.auth", "message": "JWT decoded successfully with key manager", "module": "auth", "function": "authenticate_token", "line": 683}
{"timestamp": "2025-06-12T17:05:21.918022", "level": "DEBUG", "logger": "backend.services.db_utils", "message": "Query 'auth_lookup_e4ae2d8f-b32f-52d7-99cd-8a33b19652c7' completed in 0.003s", "module": "db_utils", "function": "execute_query_with_timeout", "line": 68}
{"timestamp": "2025-06-12T17:05:21.918076", "level": "DEBUG", "logger": "backend.services.db_utils", "message": "Auth query 'auth_lookup_e4ae2d8f-b32f-52d7-99cd-8a33b19652c7' successful, result format preserved", "module": "db_utils", "function": "execute_query_with_timeout", "line": 72}
{"timestamp": "2025-06-12T17:05:21.929281", "level": "DEBUG", "logger": "backend.services.auth", "message": "Authentication attempt with token prefix: eyJhbGciOi...", "module": "auth", "function": "get_current_user", "line": 727}
{"timestamp": "2025-06-12T17:05:21.929407", "level": "DEBUG", "logger": "backend.core.security.jwt_keys", "message": "Token contains key ID: 8be4a455-8974-4e97-b307-6c8428b2c9ff", "module": "jwt_keys", "function": "decode_token", "line": 188}
{"timestamp": "2025-06-12T17:05:21.929450", "level": "DEBUG", "logger": "backend.core.security.jwt_keys", "message": "Using key ID 8be4a455-8974-4e97-b307-6c8428b2c9ff for verification", "module": "jwt_keys", "function": "decode_token", "line": 195}
{"timestamp": "2025-06-12T17:05:21.929618", "level": "DEBUG", "logger": "backend.services.auth", "message": "JWT decoded successfully with key manager", "module": "auth", "function": "authenticate_token", "line": 683}
{"timestamp": "2025-06-12T17:05:21.933365", "level": "DEBUG", "logger": "backend.services.db_utils", "message": "Query 'auth_lookup_e4ae2d8f-b32f-52d7-99cd-8a33b19652c7' completed in 0.004s", "module": "db_utils", "function": "execute_query_with_timeout", "line": 68}
{"timestamp": "2025-06-12T17:05:21.933424", "level": "DEBUG", "logger": "backend.services.db_utils", "message": "Auth query 'auth_lookup_e4ae2d8f-b32f-52d7-99cd-8a33b19652c7' successful, result format preserved", "module": "db_utils", "function": "execute_query_with_timeout", "line": 72}
{"timestamp": "2025-06-12T17:05:21.933486", "level": "DEBUG", "logger": "backend.services.auth", "message": "Successfully authenticated user: <EMAIL>", "module": "auth", "function": "get_current_user", "line": 732}
{"timestamp": "2025-06-12T17:05:21.933524", "level": "DEBUG", "logger": "backend.services.auth", "message": "Successfully authenticated user: <EMAIL>", "module": "auth", "function": "get_current_user", "line": 738}
{"timestamp": "2025-06-12T17:05:21.934718", "level": "DEBUG", "logger": "backend.routes.qr_preview", "message": "Received QR preview SVG request: {\"qr_type\": \"scooter_unlock\", \"data\": {\"scooter_id\": \"SCOOT123\", \"unlock_code\": \"UNLOCK42\", \"title\": \"Scooter_unlock QR Code\", \"description\": \"Scan to view scooter_unlock content\"}, \"designOptions\": {\"size\": 256, \"margin\": 10, \"pattern\": \"square\", \"marker_border_style\": \"square\", \"marker_border_color\": \"#000000\", \"marker_center_style\": \"square\", \"marker_center_color\": \"#000000\", \"logo_size\": 0.3, \"logo_position\": \"center\", \"logo_opacity\": 1, \"frame_style\": \"none\", \"frame_color\": \"#000000\", \"fram", "module": "qr_preview", "function": "preview_qr_svg", "line": 242}
{"timestamp": "2025-06-12T17:05:21.934797", "level": "DEBUG", "logger": "backend.routes.qr_preview", "message": "Form data keys: ['request']", "module": "qr_preview", "function": "preview_qr_svg", "line": 297}
{"timestamp": "2025-06-12T17:05:21.934923", "level": "DEBUG", "logger": "backend.routes.qr_preview", "message": "No logo key found in form data", "module": "qr_preview", "function": "preview_qr_svg", "line": 309}
{"timestamp": "2025-06-12T17:05:21.935024", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Formatting content for QR type: qrtype.scooter_unlock, content keys: ['scooter_id', 'unlock_code', 'title', 'description']", "module": "qr_preview", "function": "_format_qr_data", "line": 876}
{"timestamp": "2025-06-12T17:05:21.951321", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Logo remove background setting: False, has logo: False", "module": "qr_preview", "function": "generate_preview_svg_modular", "line": 727}
{"timestamp": "2025-06-12T17:05:21.965204", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Drawing 3 finder patterns at positions: [(0, 0), (42, 0), (0, 42)]", "module": "qr_preview", "function": "generate_preview_svg_modular", "line": 775}
{"timestamp": "2025-06-12T17:05:21.965269", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "QR size: 49, border_style: square, center_style: square", "module": "qr_preview", "function": "generate_preview_svg_modular", "line": 776}
{"timestamp": "2025-06-12T17:05:21.965314", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Drawing finder pattern 1/3 at position (0, 0)", "module": "qr_preview", "function": "generate_preview_svg_modular", "line": 780}
{"timestamp": "2025-06-12T17:05:21.965364", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "_draw_finder_marker called: mx=0, my=0, module_size=5.918367346938775, border_style=square, center_style=square, border_color=#000000, center_color=#000000, offset=(20, 20)", "module": "qr_preview", "function": "_draw_finder_marker", "line": 97}
{"timestamp": "2025-06-12T17:05:21.965448", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Creating finder pattern group with id='marker-0-0'", "module": "qr_preview", "function": "_draw_finder_marker", "line": 110}
{"timestamp": "2025-06-12T17:05:21.965489", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Drawing square border:", "module": "qr_preview", "function": "_draw_finder_marker", "line": 160}
{"timestamp": "2025-06-12T17:05:21.965530", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "  Outer: insert=(20.0, 20.0), size=(41.42857142857143, 41.42857142857143)", "module": "qr_preview", "function": "_draw_finder_marker", "line": 161}
{"timestamp": "2025-06-12T17:05:21.965580", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "  Inner: insert=(27.10204081632653, 27.10204081632653), size=(27.224489795918366, 27.224489795918366)", "module": "qr_preview", "function": "_draw_finder_marker", "line": 162}
{"timestamp": "2025-06-12T17:05:21.965662", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Drawing marker center: style=square, color=#000000, pos=(34.79591836734694, 34.79591836734694), size=11.83673469387755", "module": "qr_preview", "function": "_draw_marker_center", "line": 179}
{"timestamp": "2025-06-12T17:05:21.965717", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Successfully added finder pattern group to SVG", "module": "qr_preview", "function": "_draw_finder_marker", "line": 171}
{"timestamp": "2025-06-12T17:05:21.965755", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Successfully drew finder pattern 1/3", "module": "qr_preview", "function": "generate_preview_svg_modular", "line": 782}
{"timestamp": "2025-06-12T17:05:21.965800", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Drawing finder pattern 2/3 at position (42, 0)", "module": "qr_preview", "function": "generate_preview_svg_modular", "line": 780}
{"timestamp": "2025-06-12T17:05:21.965841", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "_draw_finder_marker called: mx=42, my=0, module_size=5.918367346938775, border_style=square, center_style=square, border_color=#000000, center_color=#000000, offset=(20, 20)", "module": "qr_preview", "function": "_draw_finder_marker", "line": 97}
{"timestamp": "2025-06-12T17:05:21.965886", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Creating finder pattern group with id='marker-42-0'", "module": "qr_preview", "function": "_draw_finder_marker", "line": 110}
{"timestamp": "2025-06-12T17:05:21.965921", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Drawing square border:", "module": "qr_preview", "function": "_draw_finder_marker", "line": 160}
{"timestamp": "2025-06-12T17:05:21.965958", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "  Outer: insert=(268.57142857142856, 20.0), size=(41.42857142857143, 41.42857142857143)", "module": "qr_preview", "function": "_draw_finder_marker", "line": 161}
{"timestamp": "2025-06-12T17:05:21.965995", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "  Inner: insert=(275.67346938775506, 27.10204081632653), size=(27.224489795918366, 27.224489795918366)", "module": "qr_preview", "function": "_draw_finder_marker", "line": 162}
{"timestamp": "2025-06-12T17:05:21.966060", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Drawing marker center: style=square, color=#000000, pos=(283.3673469387755, 34.79591836734694), size=11.83673469387755", "module": "qr_preview", "function": "_draw_marker_center", "line": 179}
{"timestamp": "2025-06-12T17:05:21.966109", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Successfully added finder pattern group to SVG", "module": "qr_preview", "function": "_draw_finder_marker", "line": 171}
{"timestamp": "2025-06-12T17:05:21.966160", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Successfully drew finder pattern 2/3", "module": "qr_preview", "function": "generate_preview_svg_modular", "line": 782}
{"timestamp": "2025-06-12T17:05:21.966194", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Drawing finder pattern 3/3 at position (0, 42)", "module": "qr_preview", "function": "generate_preview_svg_modular", "line": 780}
{"timestamp": "2025-06-12T17:05:21.966230", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "_draw_finder_marker called: mx=0, my=42, module_size=5.918367346938775, border_style=square, center_style=square, border_color=#000000, center_color=#000000, offset=(20, 20)", "module": "qr_preview", "function": "_draw_finder_marker", "line": 97}
{"timestamp": "2025-06-12T17:05:21.966274", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Creating finder pattern group with id='marker-0-42'", "module": "qr_preview", "function": "_draw_finder_marker", "line": 110}
{"timestamp": "2025-06-12T17:05:21.966308", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Drawing square border:", "module": "qr_preview", "function": "_draw_finder_marker", "line": 160}
{"timestamp": "2025-06-12T17:05:21.966344", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "  Outer: insert=(20.0, 268.57142857142856), size=(41.42857142857143, 41.42857142857143)", "module": "qr_preview", "function": "_draw_finder_marker", "line": 161}
{"timestamp": "2025-06-12T17:05:21.966381", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "  Inner: insert=(27.10204081632653, 275.67346938775506), size=(27.224489795918366, 27.224489795918366)", "module": "qr_preview", "function": "_draw_finder_marker", "line": 162}
{"timestamp": "2025-06-12T17:05:21.966441", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Drawing marker center: style=square, color=#000000, pos=(34.79591836734694, 283.3673469387755), size=11.83673469387755", "module": "qr_preview", "function": "_draw_marker_center", "line": 179}
{"timestamp": "2025-06-12T17:05:21.966490", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Successfully added finder pattern group to SVG", "module": "qr_preview", "function": "_draw_finder_marker", "line": 171}
{"timestamp": "2025-06-12T17:05:21.966527", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Successfully drew finder pattern 3/3", "module": "qr_preview", "function": "generate_preview_svg_modular", "line": 782}
{"timestamp": "2025-06-12T17:05:21.985584", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Generated SVG length: 176251", "module": "qr_preview", "function": "generate_preview_svg_modular", "line": 802}
{"timestamp": "2025-06-12T17:05:21.985724", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "SVG contains marker-0-0: True", "module": "qr_preview", "function": "generate_preview_svg_modular", "line": 803}
{"timestamp": "2025-06-12T17:05:21.985829", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "SVG contains marker-18-0: False", "module": "qr_preview", "function": "generate_preview_svg_modular", "line": 804}
{"timestamp": "2025-06-12T17:05:21.985937", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "SVG contains marker-0-18: False", "module": "qr_preview", "function": "generate_preview_svg_modular", "line": 805}
{"timestamp": "2025-06-12T17:05:21.986226", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "SVG marker counts - outer: 3, inner: 3, dot: 0", "module": "qr_preview", "function": "generate_preview_svg_modular", "line": 813}
{"timestamp": "2025-06-12T17:05:21.986260", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "SVG label counts - text: 0, background: 0", "module": "qr_preview", "function": "generate_preview_svg_modular", "line": 814}
{"timestamp": "2025-06-12T17:05:21.988405", "level": "DEBUG", "logger": "backend.main", "message": "[Auth] Enforced Bearer token authentication for request", "module": "main", "function": "bearer_token_auth_middleware", "line": 274}
{"timestamp": "2025-06-12T17:05:24.310830", "level": "DEBUG", "logger": "backend.core.security.jwt_keys", "message": "Token contains key ID: 8be4a455-8974-4e97-b307-6c8428b2c9ff", "module": "jwt_keys", "function": "decode_token", "line": 188}
{"timestamp": "2025-06-12T17:05:24.310901", "level": "DEBUG", "logger": "backend.core.security.jwt_keys", "message": "Using key ID 8be4a455-8974-4e97-b307-6c8428b2c9ff for verification", "module": "jwt_keys", "function": "decode_token", "line": 195}
{"timestamp": "2025-06-12T17:05:24.311157", "level": "DEBUG", "logger": "backend.services.auth", "message": "JWT decoded successfully with key manager", "module": "auth", "function": "authenticate_token", "line": 683}
{"timestamp": "2025-06-12T17:05:24.314883", "level": "DEBUG", "logger": "backend.services.db_utils", "message": "Query 'auth_lookup_e4ae2d8f-b32f-52d7-99cd-8a33b19652c7' completed in 0.003s", "module": "db_utils", "function": "execute_query_with_timeout", "line": 68}
{"timestamp": "2025-06-12T17:05:24.314940", "level": "DEBUG", "logger": "backend.services.db_utils", "message": "Auth query 'auth_lookup_e4ae2d8f-b32f-52d7-99cd-8a33b19652c7' successful, result format preserved", "module": "db_utils", "function": "execute_query_with_timeout", "line": 72}
{"timestamp": "2025-06-12T17:05:24.319001", "level": "DEBUG", "logger": "backend.services.auth", "message": "Authentication attempt with token prefix: eyJhbGciOi...", "module": "auth", "function": "get_current_user", "line": 727}
{"timestamp": "2025-06-12T17:05:24.319099", "level": "DEBUG", "logger": "backend.core.security.jwt_keys", "message": "Token contains key ID: 8be4a455-8974-4e97-b307-6c8428b2c9ff", "module": "jwt_keys", "function": "decode_token", "line": 188}
{"timestamp": "2025-06-12T17:05:24.319136", "level": "DEBUG", "logger": "backend.core.security.jwt_keys", "message": "Using key ID 8be4a455-8974-4e97-b307-6c8428b2c9ff for verification", "module": "jwt_keys", "function": "decode_token", "line": 195}
{"timestamp": "2025-06-12T17:05:24.319561", "level": "DEBUG", "logger": "backend.services.auth", "message": "JWT decoded successfully with key manager", "module": "auth", "function": "authenticate_token", "line": 683}
{"timestamp": "2025-06-12T17:05:24.320623", "level": "DEBUG", "logger": "backend.services.db_utils", "message": "Query 'auth_lookup_e4ae2d8f-b32f-52d7-99cd-8a33b19652c7' completed in 0.001s", "module": "db_utils", "function": "execute_query_with_timeout", "line": 68}
{"timestamp": "2025-06-12T17:05:24.320662", "level": "DEBUG", "logger": "backend.services.db_utils", "message": "Auth query 'auth_lookup_e4ae2d8f-b32f-52d7-99cd-8a33b19652c7' successful, result format preserved", "module": "db_utils", "function": "execute_query_with_timeout", "line": 72}
{"timestamp": "2025-06-12T17:05:24.320711", "level": "DEBUG", "logger": "backend.services.auth", "message": "Successfully authenticated user: <EMAIL>", "module": "auth", "function": "get_current_user", "line": 732}
{"timestamp": "2025-06-12T17:05:24.320743", "level": "DEBUG", "logger": "backend.services.auth", "message": "Successfully authenticated user: <EMAIL>", "module": "auth", "function": "get_current_user", "line": 738}
{"timestamp": "2025-06-12T17:05:24.322063", "level": "DEBUG", "logger": "backend.routes.qr_preview", "message": "Received QR preview SVG request: {\"qr_type\": \"scooter_unlock\", \"data\": {\"scooter_id\": \"SCOOT123\", \"unlock_code\": \"UNLOCK42\", \"title\": \"Scooter_unlock QR Code\", \"description\": \"Scan to view scooter_unlock content\", \"service_provider\": \"Lime\"}, \"designOptions\": {\"size\": 256, \"margin\": 10, \"pattern\": \"square\", \"marker_border_style\": \"square\", \"marker_center_style\": \"square\", \"logo_size\": 0.3, \"logo_position\": \"center\", \"logo_opacity\": 1, \"frame_style\": \"none\", \"frame_font\": \"SF Pro\", \"frame_font_size\": 16, \"foreground_color\": \"#00", "module": "qr_preview", "function": "preview_qr_svg", "line": 242}
{"timestamp": "2025-06-12T17:05:24.322215", "level": "DEBUG", "logger": "backend.routes.qr_preview", "message": "Form data keys: ['request']", "module": "qr_preview", "function": "preview_qr_svg", "line": 297}
{"timestamp": "2025-06-12T17:05:24.322256", "level": "DEBUG", "logger": "backend.routes.qr_preview", "message": "No logo key found in form data", "module": "qr_preview", "function": "preview_qr_svg", "line": 309}
{"timestamp": "2025-06-12T17:05:24.322316", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Formatting content for QR type: qrtype.scooter_unlock, content keys: ['scooter_id', 'unlock_code', 'title', 'description', 'service_provider']", "module": "qr_preview", "function": "_format_qr_data", "line": 876}
{"timestamp": "2025-06-12T17:05:24.340686", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Logo remove background setting: False, has logo: False", "module": "qr_preview", "function": "generate_preview_svg_modular", "line": 727}
{"timestamp": "2025-06-12T17:05:24.354866", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Drawing 3 finder patterns at positions: [(0, 0), (46, 0), (0, 46)]", "module": "qr_preview", "function": "generate_preview_svg_modular", "line": 775}
{"timestamp": "2025-06-12T17:05:24.354906", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "QR size: 53, border_style: square, center_style: square", "module": "qr_preview", "function": "generate_preview_svg_modular", "line": 776}
{"timestamp": "2025-06-12T17:05:24.354942", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Drawing finder pattern 1/3 at position (0, 0)", "module": "qr_preview", "function": "generate_preview_svg_modular", "line": 780}
{"timestamp": "2025-06-12T17:05:24.354982", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "_draw_finder_marker called: mx=0, my=0, module_size=5.471698113207547, border_style=square, center_style=square, border_color=#000000, center_color=#000000, offset=(20, 20)", "module": "qr_preview", "function": "_draw_finder_marker", "line": 97}
{"timestamp": "2025-06-12T17:05:24.355032", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Creating finder pattern group with id='marker-0-0'", "module": "qr_preview", "function": "_draw_finder_marker", "line": 110}
{"timestamp": "2025-06-12T17:05:24.355065", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Drawing square border:", "module": "qr_preview", "function": "_draw_finder_marker", "line": 160}
{"timestamp": "2025-06-12T17:05:24.355102", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "  Outer: insert=(20.0, 20.0), size=(38.301886792452834, 38.301886792452834)", "module": "qr_preview", "function": "_draw_finder_marker", "line": 161}
{"timestamp": "2025-06-12T17:05:24.355136", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "  Inner: insert=(26.566037735849058, 26.566037735849058), size=(25.169811320754715, 25.169811320754715)", "module": "qr_preview", "function": "_draw_finder_marker", "line": 162}
{"timestamp": "2025-06-12T17:05:24.355199", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Drawing marker center: style=square, color=#000000, pos=(33.67924528301887, 33.67924528301887), size=10.943396226415095", "module": "qr_preview", "function": "_draw_marker_center", "line": 179}
{"timestamp": "2025-06-12T17:05:24.355247", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Successfully added finder pattern group to SVG", "module": "qr_preview", "function": "_draw_finder_marker", "line": 171}
{"timestamp": "2025-06-12T17:05:24.355279", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Successfully drew finder pattern 1/3", "module": "qr_preview", "function": "generate_preview_svg_modular", "line": 782}
{"timestamp": "2025-06-12T17:05:24.355338", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Drawing finder pattern 2/3 at position (46, 0)", "module": "qr_preview", "function": "generate_preview_svg_modular", "line": 780}
{"timestamp": "2025-06-12T17:05:24.355370", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "_draw_finder_marker called: mx=46, my=0, module_size=5.471698113207547, border_style=square, center_style=square, border_color=#000000, center_color=#000000, offset=(20, 20)", "module": "qr_preview", "function": "_draw_finder_marker", "line": 97}
{"timestamp": "2025-06-12T17:05:24.355408", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Creating finder pattern group with id='marker-46-0'", "module": "qr_preview", "function": "_draw_finder_marker", "line": 110}
{"timestamp": "2025-06-12T17:05:24.355439", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Drawing square border:", "module": "qr_preview", "function": "_draw_finder_marker", "line": 160}
{"timestamp": "2025-06-12T17:05:24.355470", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "  Outer: insert=(271.6981132075472, 20.0), size=(38.301886792452834, 38.301886792452834)", "module": "qr_preview", "function": "_draw_finder_marker", "line": 161}
{"timestamp": "2025-06-12T17:05:24.355503", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "  Inner: insert=(278.26415094339626, 26.566037735849058), size=(25.169811320754715, 25.169811320754715)", "module": "qr_preview", "function": "_draw_finder_marker", "line": 162}
{"timestamp": "2025-06-12T17:05:24.355563", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Drawing marker center: style=square, color=#000000, pos=(285.37735849056605, 33.67924528301887), size=10.943396226415095", "module": "qr_preview", "function": "_draw_marker_center", "line": 179}
{"timestamp": "2025-06-12T17:05:24.355611", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Successfully added finder pattern group to SVG", "module": "qr_preview", "function": "_draw_finder_marker", "line": 171}
{"timestamp": "2025-06-12T17:05:24.355644", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Successfully drew finder pattern 2/3", "module": "qr_preview", "function": "generate_preview_svg_modular", "line": 782}
{"timestamp": "2025-06-12T17:05:24.355673", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Drawing finder pattern 3/3 at position (0, 46)", "module": "qr_preview", "function": "generate_preview_svg_modular", "line": 780}
{"timestamp": "2025-06-12T17:05:24.355704", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "_draw_finder_marker called: mx=0, my=46, module_size=5.471698113207547, border_style=square, center_style=square, border_color=#000000, center_color=#000000, offset=(20, 20)", "module": "qr_preview", "function": "_draw_finder_marker", "line": 97}
{"timestamp": "2025-06-12T17:05:24.355740", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Creating finder pattern group with id='marker-0-46'", "module": "qr_preview", "function": "_draw_finder_marker", "line": 110}
{"timestamp": "2025-06-12T17:05:24.355771", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Drawing square border:", "module": "qr_preview", "function": "_draw_finder_marker", "line": 160}
{"timestamp": "2025-06-12T17:05:24.355803", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "  Outer: insert=(20.0, 271.6981132075472), size=(38.301886792452834, 38.301886792452834)", "module": "qr_preview", "function": "_draw_finder_marker", "line": 161}
{"timestamp": "2025-06-12T17:05:24.355834", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "  Inner: insert=(26.566037735849058, 278.26415094339626), size=(25.169811320754715, 25.169811320754715)", "module": "qr_preview", "function": "_draw_finder_marker", "line": 162}
{"timestamp": "2025-06-12T17:05:24.355890", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Drawing marker center: style=square, color=#000000, pos=(33.67924528301887, 285.37735849056605), size=10.943396226415095", "module": "qr_preview", "function": "_draw_marker_center", "line": 179}
{"timestamp": "2025-06-12T17:05:24.355951", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Successfully added finder pattern group to SVG", "module": "qr_preview", "function": "_draw_finder_marker", "line": 171}
{"timestamp": "2025-06-12T17:05:24.355980", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Successfully drew finder pattern 3/3", "module": "qr_preview", "function": "generate_preview_svg_modular", "line": 782}
{"timestamp": "2025-06-12T17:05:24.376664", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Generated SVG length: 201904", "module": "qr_preview", "function": "generate_preview_svg_modular", "line": 802}
{"timestamp": "2025-06-12T17:05:24.376789", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "SVG contains marker-0-0: True", "module": "qr_preview", "function": "generate_preview_svg_modular", "line": 803}
{"timestamp": "2025-06-12T17:05:24.376898", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "SVG contains marker-18-0: False", "module": "qr_preview", "function": "generate_preview_svg_modular", "line": 804}
{"timestamp": "2025-06-12T17:05:24.377002", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "SVG contains marker-0-18: False", "module": "qr_preview", "function": "generate_preview_svg_modular", "line": 805}
{"timestamp": "2025-06-12T17:05:24.377330", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "SVG marker counts - outer: 3, inner: 3, dot: 0", "module": "qr_preview", "function": "generate_preview_svg_modular", "line": 813}
{"timestamp": "2025-06-12T17:05:24.377362", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "SVG label counts - text: 0, background: 0", "module": "qr_preview", "function": "generate_preview_svg_modular", "line": 814}
{"timestamp": "2025-06-12T17:05:24.379240", "level": "DEBUG", "logger": "backend.main", "message": "[Auth] Enforced Bearer token authentication for request", "module": "main", "function": "bearer_token_auth_middleware", "line": 274}
{"timestamp": "2025-06-12T17:05:25.214273", "level": "DEBUG", "logger": "backend.core.security.jwt_keys", "message": "Token contains key ID: 8be4a455-8974-4e97-b307-6c8428b2c9ff", "module": "jwt_keys", "function": "decode_token", "line": 188}
{"timestamp": "2025-06-12T17:05:25.214354", "level": "DEBUG", "logger": "backend.core.security.jwt_keys", "message": "Using key ID 8be4a455-8974-4e97-b307-6c8428b2c9ff for verification", "module": "jwt_keys", "function": "decode_token", "line": 195}
{"timestamp": "2025-06-12T17:05:25.214596", "level": "DEBUG", "logger": "backend.services.auth", "message": "JWT decoded successfully with key manager", "module": "auth", "function": "authenticate_token", "line": 683}
{"timestamp": "2025-06-12T17:05:25.216854", "level": "DEBUG", "logger": "backend.services.db_utils", "message": "Query 'auth_lookup_e4ae2d8f-b32f-52d7-99cd-8a33b19652c7' completed in 0.002s", "module": "db_utils", "function": "execute_query_with_timeout", "line": 68}
{"timestamp": "2025-06-12T17:05:25.216930", "level": "DEBUG", "logger": "backend.services.db_utils", "message": "Auth query 'auth_lookup_e4ae2d8f-b32f-52d7-99cd-8a33b19652c7' successful, result format preserved", "module": "db_utils", "function": "execute_query_with_timeout", "line": 72}
{"timestamp": "2025-06-12T17:05:25.224048", "level": "DEBUG", "logger": "backend.services.auth", "message": "Authentication attempt with token prefix: eyJhbGciOi...", "module": "auth", "function": "get_current_user", "line": 727}
{"timestamp": "2025-06-12T17:05:25.224191", "level": "DEBUG", "logger": "backend.core.security.jwt_keys", "message": "Token contains key ID: 8be4a455-8974-4e97-b307-6c8428b2c9ff", "module": "jwt_keys", "function": "decode_token", "line": 188}
{"timestamp": "2025-06-12T17:05:25.224271", "level": "DEBUG", "logger": "backend.core.security.jwt_keys", "message": "Using key ID 8be4a455-8974-4e97-b307-6c8428b2c9ff for verification", "module": "jwt_keys", "function": "decode_token", "line": 195}
{"timestamp": "2025-06-12T17:05:25.224398", "level": "DEBUG", "logger": "backend.services.auth", "message": "JWT decoded successfully with key manager", "module": "auth", "function": "authenticate_token", "line": 683}
{"timestamp": "2025-06-12T17:05:25.227874", "level": "DEBUG", "logger": "backend.services.db_utils", "message": "Query 'auth_lookup_e4ae2d8f-b32f-52d7-99cd-8a33b19652c7' completed in 0.003s", "module": "db_utils", "function": "execute_query_with_timeout", "line": 68}
{"timestamp": "2025-06-12T17:05:25.227989", "level": "DEBUG", "logger": "backend.services.db_utils", "message": "Auth query 'auth_lookup_e4ae2d8f-b32f-52d7-99cd-8a33b19652c7' successful, result format preserved", "module": "db_utils", "function": "execute_query_with_timeout", "line": 72}
{"timestamp": "2025-06-12T17:05:25.228085", "level": "DEBUG", "logger": "backend.services.auth", "message": "Successfully authenticated user: <EMAIL>", "module": "auth", "function": "get_current_user", "line": 732}
{"timestamp": "2025-06-12T17:05:25.228134", "level": "DEBUG", "logger": "backend.services.auth", "message": "Successfully authenticated user: <EMAIL>", "module": "auth", "function": "get_current_user", "line": 738}
{"timestamp": "2025-06-12T17:05:25.230209", "level": "DEBUG", "logger": "backend.routes.qr_preview", "message": "Received QR preview SVG request: {\"qr_type\": \"scooter_unlock\", \"data\": {\"scooter_id\": \"SCOOT123\", \"unlock_code\": \"UNLOCK42\", \"title\": \"Scooter_unlock QR Code\", \"description\": \"Scan to view scooter_unlock content\", \"service_provider\": \"Lime\"}, \"designOptions\": {\"size\": 256, \"margin\": 10, \"pattern\": \"square\", \"marker_border_style\": \"square\", \"marker_border_color\": \"#000000\", \"marker_center_style\": \"square\", \"marker_center_color\": \"#000000\", \"logo_size\": 0.3, \"logo_position\": \"center\", \"logo_opacity\": 1, \"frame_style\": \"none\", \"fr", "module": "qr_preview", "function": "preview_qr_svg", "line": 242}
{"timestamp": "2025-06-12T17:05:25.230308", "level": "DEBUG", "logger": "backend.routes.qr_preview", "message": "Form data keys: ['request']", "module": "qr_preview", "function": "preview_qr_svg", "line": 297}
{"timestamp": "2025-06-12T17:05:25.230354", "level": "DEBUG", "logger": "backend.routes.qr_preview", "message": "No logo key found in form data", "module": "qr_preview", "function": "preview_qr_svg", "line": 309}
{"timestamp": "2025-06-12T17:05:25.230404", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Formatting content for QR type: qrtype.scooter_unlock, content keys: ['scooter_id', 'unlock_code', 'title', 'description', 'service_provider']", "module": "qr_preview", "function": "_format_qr_data", "line": 876}
{"timestamp": "2025-06-12T17:05:25.249426", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Logo remove background setting: False, has logo: False", "module": "qr_preview", "function": "generate_preview_svg_modular", "line": 727}
{"timestamp": "2025-06-12T17:05:25.265170", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Drawing 3 finder patterns at positions: [(0, 0), (46, 0), (0, 46)]", "module": "qr_preview", "function": "generate_preview_svg_modular", "line": 775}
{"timestamp": "2025-06-12T17:05:25.265246", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "QR size: 53, border_style: square, center_style: square", "module": "qr_preview", "function": "generate_preview_svg_modular", "line": 776}
{"timestamp": "2025-06-12T17:05:25.265290", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Drawing finder pattern 1/3 at position (0, 0)", "module": "qr_preview", "function": "generate_preview_svg_modular", "line": 780}
{"timestamp": "2025-06-12T17:05:25.265350", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "_draw_finder_marker called: mx=0, my=0, module_size=5.471698113207547, border_style=square, center_style=square, border_color=#000000, center_color=#000000, offset=(20, 20)", "module": "qr_preview", "function": "_draw_finder_marker", "line": 97}
{"timestamp": "2025-06-12T17:05:25.265440", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Creating finder pattern group with id='marker-0-0'", "module": "qr_preview", "function": "_draw_finder_marker", "line": 110}
{"timestamp": "2025-06-12T17:05:25.265483", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Drawing square border:", "module": "qr_preview", "function": "_draw_finder_marker", "line": 160}
{"timestamp": "2025-06-12T17:05:25.265522", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "  Outer: insert=(20.0, 20.0), size=(38.301886792452834, 38.301886792452834)", "module": "qr_preview", "function": "_draw_finder_marker", "line": 161}
{"timestamp": "2025-06-12T17:05:25.265562", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "  Inner: insert=(26.566037735849058, 26.566037735849058), size=(25.169811320754715, 25.169811320754715)", "module": "qr_preview", "function": "_draw_finder_marker", "line": 162}
{"timestamp": "2025-06-12T17:05:25.265636", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Drawing marker center: style=square, color=#000000, pos=(33.67924528301887, 33.67924528301887), size=10.943396226415095", "module": "qr_preview", "function": "_draw_marker_center", "line": 179}
{"timestamp": "2025-06-12T17:05:25.265690", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Successfully added finder pattern group to SVG", "module": "qr_preview", "function": "_draw_finder_marker", "line": 171}
{"timestamp": "2025-06-12T17:05:25.265727", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Successfully drew finder pattern 1/3", "module": "qr_preview", "function": "generate_preview_svg_modular", "line": 782}
{"timestamp": "2025-06-12T17:05:25.265762", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Drawing finder pattern 2/3 at position (46, 0)", "module": "qr_preview", "function": "generate_preview_svg_modular", "line": 780}
{"timestamp": "2025-06-12T17:05:25.265798", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "_draw_finder_marker called: mx=46, my=0, module_size=5.471698113207547, border_style=square, center_style=square, border_color=#000000, center_color=#000000, offset=(20, 20)", "module": "qr_preview", "function": "_draw_finder_marker", "line": 97}
{"timestamp": "2025-06-12T17:05:25.265840", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Creating finder pattern group with id='marker-46-0'", "module": "qr_preview", "function": "_draw_finder_marker", "line": 110}
{"timestamp": "2025-06-12T17:05:25.265874", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Drawing square border:", "module": "qr_preview", "function": "_draw_finder_marker", "line": 160}
{"timestamp": "2025-06-12T17:05:25.265913", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "  Outer: insert=(271.6981132075472, 20.0), size=(38.301886792452834, 38.301886792452834)", "module": "qr_preview", "function": "_draw_finder_marker", "line": 161}
{"timestamp": "2025-06-12T17:05:25.265951", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "  Inner: insert=(278.26415094339626, 26.566037735849058), size=(25.169811320754715, 25.169811320754715)", "module": "qr_preview", "function": "_draw_finder_marker", "line": 162}
{"timestamp": "2025-06-12T17:05:25.266017", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Drawing marker center: style=square, color=#000000, pos=(285.37735849056605, 33.67924528301887), size=10.943396226415095", "module": "qr_preview", "function": "_draw_marker_center", "line": 179}
{"timestamp": "2025-06-12T17:05:25.266067", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Successfully added finder pattern group to SVG", "module": "qr_preview", "function": "_draw_finder_marker", "line": 171}
{"timestamp": "2025-06-12T17:05:25.266121", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Successfully drew finder pattern 2/3", "module": "qr_preview", "function": "generate_preview_svg_modular", "line": 782}
{"timestamp": "2025-06-12T17:05:25.266156", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Drawing finder pattern 3/3 at position (0, 46)", "module": "qr_preview", "function": "generate_preview_svg_modular", "line": 780}
{"timestamp": "2025-06-12T17:05:25.266191", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "_draw_finder_marker called: mx=0, my=46, module_size=5.471698113207547, border_style=square, center_style=square, border_color=#000000, center_color=#000000, offset=(20, 20)", "module": "qr_preview", "function": "_draw_finder_marker", "line": 97}
{"timestamp": "2025-06-12T17:05:25.266238", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Creating finder pattern group with id='marker-0-46'", "module": "qr_preview", "function": "_draw_finder_marker", "line": 110}
{"timestamp": "2025-06-12T17:05:25.266274", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Drawing square border:", "module": "qr_preview", "function": "_draw_finder_marker", "line": 160}
{"timestamp": "2025-06-12T17:05:25.266312", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "  Outer: insert=(20.0, 271.6981132075472), size=(38.301886792452834, 38.301886792452834)", "module": "qr_preview", "function": "_draw_finder_marker", "line": 161}
{"timestamp": "2025-06-12T17:05:25.266353", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "  Inner: insert=(26.566037735849058, 278.26415094339626), size=(25.169811320754715, 25.169811320754715)", "module": "qr_preview", "function": "_draw_finder_marker", "line": 162}
{"timestamp": "2025-06-12T17:05:25.266414", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Drawing marker center: style=square, color=#000000, pos=(33.67924528301887, 285.37735849056605), size=10.943396226415095", "module": "qr_preview", "function": "_draw_marker_center", "line": 179}
{"timestamp": "2025-06-12T17:05:25.266463", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Successfully added finder pattern group to SVG", "module": "qr_preview", "function": "_draw_finder_marker", "line": 171}
{"timestamp": "2025-06-12T17:05:25.266501", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Successfully drew finder pattern 3/3", "module": "qr_preview", "function": "generate_preview_svg_modular", "line": 782}
{"timestamp": "2025-06-12T17:05:25.289304", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Generated SVG length: 201904", "module": "qr_preview", "function": "generate_preview_svg_modular", "line": 802}
{"timestamp": "2025-06-12T17:05:25.289485", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "SVG contains marker-0-0: True", "module": "qr_preview", "function": "generate_preview_svg_modular", "line": 803}
{"timestamp": "2025-06-12T17:05:25.289611", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "SVG contains marker-18-0: False", "module": "qr_preview", "function": "generate_preview_svg_modular", "line": 804}
{"timestamp": "2025-06-12T17:05:25.289722", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "SVG contains marker-0-18: False", "module": "qr_preview", "function": "generate_preview_svg_modular", "line": 805}
{"timestamp": "2025-06-12T17:05:25.290066", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "SVG marker counts - outer: 3, inner: 3, dot: 0", "module": "qr_preview", "function": "generate_preview_svg_modular", "line": 813}
{"timestamp": "2025-06-12T17:05:25.290105", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "SVG label counts - text: 0, background: 0", "module": "qr_preview", "function": "generate_preview_svg_modular", "line": 814}
{"timestamp": "2025-06-12T17:05:25.293921", "level": "DEBUG", "logger": "backend.main", "message": "[Auth] Enforced Bearer token authentication for request", "module": "main", "function": "bearer_token_auth_middleware", "line": 274}
{"timestamp": "2025-06-12T17:05:32.257637", "level": "DEBUG", "logger": "backend.core.security.jwt_keys", "message": "Token contains key ID: 8be4a455-8974-4e97-b307-6c8428b2c9ff", "module": "jwt_keys", "function": "decode_token", "line": 188}
{"timestamp": "2025-06-12T17:05:32.258233", "level": "DEBUG", "logger": "backend.core.security.jwt_keys", "message": "Using key ID 8be4a455-8974-4e97-b307-6c8428b2c9ff for verification", "module": "jwt_keys", "function": "decode_token", "line": 195}
{"timestamp": "2025-06-12T17:05:32.259726", "level": "DEBUG", "logger": "backend.services.auth", "message": "JWT decoded successfully with key manager", "module": "auth", "function": "authenticate_token", "line": 683}
{"timestamp": "2025-06-12T17:05:32.265267", "level": "DEBUG", "logger": "backend.services.db_utils", "message": "Query 'auth_lookup_e4ae2d8f-b32f-52d7-99cd-8a33b19652c7' completed in 0.005s", "module": "db_utils", "function": "execute_query_with_timeout", "line": 68}
{"timestamp": "2025-06-12T17:05:32.265343", "level": "DEBUG", "logger": "backend.services.db_utils", "message": "Auth query 'auth_lookup_e4ae2d8f-b32f-52d7-99cd-8a33b19652c7' successful, result format preserved", "module": "db_utils", "function": "execute_query_with_timeout", "line": 72}
{"timestamp": "2025-06-12T17:05:32.271785", "level": "DEBUG", "logger": "backend.services.auth", "message": "Authentication attempt with token prefix: eyJhbGciOi...", "module": "auth", "function": "get_current_user", "line": 727}
{"timestamp": "2025-06-12T17:05:32.271914", "level": "DEBUG", "logger": "backend.core.security.jwt_keys", "message": "Token contains key ID: 8be4a455-8974-4e97-b307-6c8428b2c9ff", "module": "jwt_keys", "function": "decode_token", "line": 188}
{"timestamp": "2025-06-12T17:05:32.271952", "level": "DEBUG", "logger": "backend.core.security.jwt_keys", "message": "Using key ID 8be4a455-8974-4e97-b307-6c8428b2c9ff for verification", "module": "jwt_keys", "function": "decode_token", "line": 195}
{"timestamp": "2025-06-12T17:05:32.272088", "level": "DEBUG", "logger": "backend.services.auth", "message": "JWT decoded successfully with key manager", "module": "auth", "function": "authenticate_token", "line": 683}
{"timestamp": "2025-06-12T17:05:32.273222", "level": "DEBUG", "logger": "backend.services.db_utils", "message": "Query 'auth_lookup_e4ae2d8f-b32f-52d7-99cd-8a33b19652c7' completed in 0.001s", "module": "db_utils", "function": "execute_query_with_timeout", "line": 68}
{"timestamp": "2025-06-12T17:05:32.273274", "level": "DEBUG", "logger": "backend.services.db_utils", "message": "Auth query 'auth_lookup_e4ae2d8f-b32f-52d7-99cd-8a33b19652c7' successful, result format preserved", "module": "db_utils", "function": "execute_query_with_timeout", "line": 72}
{"timestamp": "2025-06-12T17:05:32.273332", "level": "DEBUG", "logger": "backend.services.auth", "message": "Successfully authenticated user: <EMAIL>", "module": "auth", "function": "get_current_user", "line": 732}
{"timestamp": "2025-06-12T17:05:32.273366", "level": "DEBUG", "logger": "backend.services.auth", "message": "Successfully authenticated user: <EMAIL>", "module": "auth", "function": "get_current_user", "line": 738}
{"timestamp": "2025-06-12T17:05:32.274524", "level": "DEBUG", "logger": "backend.routes.qr_preview", "message": "Received QR preview SVG request: {\"qr_type\": \"scooter_unlock\", \"data\": {\"scooter_id\": \"SCOOT123\", \"unlock_code\": \"UNLOCK42\", \"title\": \"Scooter_unlock QR Code\", \"description\": \"Scan to view scooter_unlock content\", \"service_provider\": \"Lime\"}, \"designOptions\": {\"size\": 256, \"margin\": 10, \"pattern\": \"diagonal\", \"marker_border_style\": \"square\", \"marker_center_style\": \"square\", \"logo_size\": 0.3, \"logo_position\": \"center\", \"logo_opacity\": 1, \"frame_style\": \"none\", \"frame_font\": \"SF Pro\", \"frame_font_size\": 16, \"foreground_color\": \"#", "module": "qr_preview", "function": "preview_qr_svg", "line": 242}
{"timestamp": "2025-06-12T17:05:32.274665", "level": "DEBUG", "logger": "backend.routes.qr_preview", "message": "Form data keys: ['request']", "module": "qr_preview", "function": "preview_qr_svg", "line": 297}
{"timestamp": "2025-06-12T17:05:32.274704", "level": "DEBUG", "logger": "backend.routes.qr_preview", "message": "No logo key found in form data", "module": "qr_preview", "function": "preview_qr_svg", "line": 309}
{"timestamp": "2025-06-12T17:05:32.274749", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Formatting content for QR type: qrtype.scooter_unlock, content keys: ['scooter_id', 'unlock_code', 'title', 'description', 'service_provider']", "module": "qr_preview", "function": "_format_qr_data", "line": 876}
{"timestamp": "2025-06-12T17:05:32.298124", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Logo remove background setting: False, has logo: False", "module": "qr_preview", "function": "generate_preview_svg_modular", "line": 727}
{"timestamp": "2025-06-12T17:05:32.327239", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Drawing 3 finder patterns at positions: [(0, 0), (46, 0), (0, 46)]", "module": "qr_preview", "function": "generate_preview_svg_modular", "line": 775}
{"timestamp": "2025-06-12T17:05:32.327311", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "QR size: 53, border_style: square, center_style: square", "module": "qr_preview", "function": "generate_preview_svg_modular", "line": 776}
{"timestamp": "2025-06-12T17:05:32.327357", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Drawing finder pattern 1/3 at position (0, 0)", "module": "qr_preview", "function": "generate_preview_svg_modular", "line": 780}
{"timestamp": "2025-06-12T17:05:32.327408", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "_draw_finder_marker called: mx=0, my=0, module_size=5.471698113207547, border_style=square, center_style=square, border_color=#000000, center_color=#000000, offset=(20, 20)", "module": "qr_preview", "function": "_draw_finder_marker", "line": 97}
{"timestamp": "2025-06-12T17:05:32.327467", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Creating finder pattern group with id='marker-0-0'", "module": "qr_preview", "function": "_draw_finder_marker", "line": 110}
{"timestamp": "2025-06-12T17:05:32.327509", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Drawing square border:", "module": "qr_preview", "function": "_draw_finder_marker", "line": 160}
{"timestamp": "2025-06-12T17:05:32.327550", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "  Outer: insert=(20.0, 20.0), size=(38.301886792452834, 38.301886792452834)", "module": "qr_preview", "function": "_draw_finder_marker", "line": 161}
{"timestamp": "2025-06-12T17:05:32.327591", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "  Inner: insert=(26.566037735849058, 26.566037735849058), size=(25.169811320754715, 25.169811320754715)", "module": "qr_preview", "function": "_draw_finder_marker", "line": 162}
{"timestamp": "2025-06-12T17:05:32.327671", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Drawing marker center: style=square, color=#000000, pos=(33.67924528301887, 33.67924528301887), size=10.943396226415095", "module": "qr_preview", "function": "_draw_marker_center", "line": 179}
{"timestamp": "2025-06-12T17:05:32.327732", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Successfully added finder pattern group to SVG", "module": "qr_preview", "function": "_draw_finder_marker", "line": 171}
{"timestamp": "2025-06-12T17:05:32.327773", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Successfully drew finder pattern 1/3", "module": "qr_preview", "function": "generate_preview_svg_modular", "line": 782}
{"timestamp": "2025-06-12T17:05:32.327838", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Drawing finder pattern 2/3 at position (46, 0)", "module": "qr_preview", "function": "generate_preview_svg_modular", "line": 780}
{"timestamp": "2025-06-12T17:05:32.327880", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "_draw_finder_marker called: mx=46, my=0, module_size=5.471698113207547, border_style=square, center_style=square, border_color=#000000, center_color=#000000, offset=(20, 20)", "module": "qr_preview", "function": "_draw_finder_marker", "line": 97}
{"timestamp": "2025-06-12T17:05:32.327929", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Creating finder pattern group with id='marker-46-0'", "module": "qr_preview", "function": "_draw_finder_marker", "line": 110}
{"timestamp": "2025-06-12T17:05:32.327966", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Drawing square border:", "module": "qr_preview", "function": "_draw_finder_marker", "line": 160}
{"timestamp": "2025-06-12T17:05:32.328005", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "  Outer: insert=(271.6981132075472, 20.0), size=(38.301886792452834, 38.301886792452834)", "module": "qr_preview", "function": "_draw_finder_marker", "line": 161}
{"timestamp": "2025-06-12T17:05:32.328044", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "  Inner: insert=(278.26415094339626, 26.566037735849058), size=(25.169811320754715, 25.169811320754715)", "module": "qr_preview", "function": "_draw_finder_marker", "line": 162}
{"timestamp": "2025-06-12T17:05:32.328114", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Drawing marker center: style=square, color=#000000, pos=(285.37735849056605, 33.67924528301887), size=10.943396226415095", "module": "qr_preview", "function": "_draw_marker_center", "line": 179}
{"timestamp": "2025-06-12T17:05:32.328170", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Successfully added finder pattern group to SVG", "module": "qr_preview", "function": "_draw_finder_marker", "line": 171}
{"timestamp": "2025-06-12T17:05:32.328210", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Successfully drew finder pattern 2/3", "module": "qr_preview", "function": "generate_preview_svg_modular", "line": 782}
{"timestamp": "2025-06-12T17:05:32.328247", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Drawing finder pattern 3/3 at position (0, 46)", "module": "qr_preview", "function": "generate_preview_svg_modular", "line": 780}
{"timestamp": "2025-06-12T17:05:32.328286", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "_draw_finder_marker called: mx=0, my=46, module_size=5.471698113207547, border_style=square, center_style=square, border_color=#000000, center_color=#000000, offset=(20, 20)", "module": "qr_preview", "function": "_draw_finder_marker", "line": 97}
{"timestamp": "2025-06-12T17:05:32.328332", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Creating finder pattern group with id='marker-0-46'", "module": "qr_preview", "function": "_draw_finder_marker", "line": 110}
{"timestamp": "2025-06-12T17:05:32.328367", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Drawing square border:", "module": "qr_preview", "function": "_draw_finder_marker", "line": 160}
{"timestamp": "2025-06-12T17:05:32.328405", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "  Outer: insert=(20.0, 271.6981132075472), size=(38.301886792452834, 38.301886792452834)", "module": "qr_preview", "function": "_draw_finder_marker", "line": 161}
{"timestamp": "2025-06-12T17:05:32.328445", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "  Inner: insert=(26.566037735849058, 278.26415094339626), size=(25.169811320754715, 25.169811320754715)", "module": "qr_preview", "function": "_draw_finder_marker", "line": 162}
{"timestamp": "2025-06-12T17:05:32.328554", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Drawing marker center: style=square, color=#000000, pos=(33.67924528301887, 285.37735849056605), size=10.943396226415095", "module": "qr_preview", "function": "_draw_marker_center", "line": 179}
{"timestamp": "2025-06-12T17:05:32.328607", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Successfully added finder pattern group to SVG", "module": "qr_preview", "function": "_draw_finder_marker", "line": 171}
{"timestamp": "2025-06-12T17:05:32.328644", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Successfully drew finder pattern 3/3", "module": "qr_preview", "function": "generate_preview_svg_modular", "line": 782}
{"timestamp": "2025-06-12T17:05:32.355282", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Generated SVG length: 279518", "module": "qr_preview", "function": "generate_preview_svg_modular", "line": 802}
{"timestamp": "2025-06-12T17:05:32.355451", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "SVG contains marker-0-0: True", "module": "qr_preview", "function": "generate_preview_svg_modular", "line": 803}
{"timestamp": "2025-06-12T17:05:32.355593", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "SVG contains marker-18-0: False", "module": "qr_preview", "function": "generate_preview_svg_modular", "line": 804}
{"timestamp": "2025-06-12T17:05:32.355733", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "SVG contains marker-0-18: False", "module": "qr_preview", "function": "generate_preview_svg_modular", "line": 805}
{"timestamp": "2025-06-12T17:05:32.356188", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "SVG marker counts - outer: 3, inner: 3, dot: 0", "module": "qr_preview", "function": "generate_preview_svg_modular", "line": 813}
{"timestamp": "2025-06-12T17:05:32.356221", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "SVG label counts - text: 0, background: 0", "module": "qr_preview", "function": "generate_preview_svg_modular", "line": 814}
{"timestamp": "2025-06-12T17:05:32.358310", "level": "DEBUG", "logger": "backend.main", "message": "[Auth] Enforced Bearer token authentication for request", "module": "main", "function": "bearer_token_auth_middleware", "line": 274}
{"timestamp": "2025-06-12T17:05:33.212347", "level": "DEBUG", "logger": "backend.core.security.jwt_keys", "message": "Token contains key ID: 8be4a455-8974-4e97-b307-6c8428b2c9ff", "module": "jwt_keys", "function": "decode_token", "line": 188}
{"timestamp": "2025-06-12T17:05:33.212416", "level": "DEBUG", "logger": "backend.core.security.jwt_keys", "message": "Using key ID 8be4a455-8974-4e97-b307-6c8428b2c9ff for verification", "module": "jwt_keys", "function": "decode_token", "line": 195}
{"timestamp": "2025-06-12T17:05:33.212544", "level": "DEBUG", "logger": "backend.services.auth", "message": "JWT decoded successfully with key manager", "module": "auth", "function": "authenticate_token", "line": 683}
{"timestamp": "2025-06-12T17:05:33.214066", "level": "DEBUG", "logger": "backend.services.db_utils", "message": "Query 'auth_lookup_e4ae2d8f-b32f-52d7-99cd-8a33b19652c7' completed in 0.001s", "module": "db_utils", "function": "execute_query_with_timeout", "line": 68}
{"timestamp": "2025-06-12T17:05:33.214122", "level": "DEBUG", "logger": "backend.services.db_utils", "message": "Auth query 'auth_lookup_e4ae2d8f-b32f-52d7-99cd-8a33b19652c7' successful, result format preserved", "module": "db_utils", "function": "execute_query_with_timeout", "line": 72}
{"timestamp": "2025-06-12T17:05:33.220880", "level": "DEBUG", "logger": "backend.services.auth", "message": "Authentication attempt with token prefix: eyJhbGciOi...", "module": "auth", "function": "get_current_user", "line": 727}
{"timestamp": "2025-06-12T17:05:33.221013", "level": "DEBUG", "logger": "backend.core.security.jwt_keys", "message": "Token contains key ID: 8be4a455-8974-4e97-b307-6c8428b2c9ff", "module": "jwt_keys", "function": "decode_token", "line": 188}
{"timestamp": "2025-06-12T17:05:33.221128", "level": "DEBUG", "logger": "backend.core.security.jwt_keys", "message": "Using key ID 8be4a455-8974-4e97-b307-6c8428b2c9ff for verification", "module": "jwt_keys", "function": "decode_token", "line": 195}
{"timestamp": "2025-06-12T17:05:33.221253", "level": "DEBUG", "logger": "backend.services.auth", "message": "JWT decoded successfully with key manager", "module": "auth", "function": "authenticate_token", "line": 683}
{"timestamp": "2025-06-12T17:05:33.222628", "level": "DEBUG", "logger": "backend.services.db_utils", "message": "Query 'auth_lookup_e4ae2d8f-b32f-52d7-99cd-8a33b19652c7' completed in 0.001s", "module": "db_utils", "function": "execute_query_with_timeout", "line": 68}
{"timestamp": "2025-06-12T17:05:33.222677", "level": "DEBUG", "logger": "backend.services.db_utils", "message": "Auth query 'auth_lookup_e4ae2d8f-b32f-52d7-99cd-8a33b19652c7' successful, result format preserved", "module": "db_utils", "function": "execute_query_with_timeout", "line": 72}
{"timestamp": "2025-06-12T17:05:33.222733", "level": "DEBUG", "logger": "backend.services.auth", "message": "Successfully authenticated user: <EMAIL>", "module": "auth", "function": "get_current_user", "line": 732}
{"timestamp": "2025-06-12T17:05:33.222772", "level": "DEBUG", "logger": "backend.services.auth", "message": "Successfully authenticated user: <EMAIL>", "module": "auth", "function": "get_current_user", "line": 738}
{"timestamp": "2025-06-12T17:05:33.223930", "level": "DEBUG", "logger": "backend.routes.qr_preview", "message": "Received QR preview SVG request: {\"qr_type\": \"scooter_unlock\", \"data\": {\"scooter_id\": \"SCOOT123\", \"unlock_code\": \"UNLOCK42\", \"title\": \"Scooter_unlock QR Code\", \"description\": \"Scan to view scooter_unlock content\", \"service_provider\": \"Lime\"}, \"designOptions\": {\"size\": 256, \"margin\": 10, \"pattern\": \"diagonal\", \"marker_border_style\": \"square\", \"marker_border_color\": \"#000000\", \"marker_center_style\": \"square\", \"marker_center_color\": \"#000000\", \"logo_size\": 0.3, \"logo_position\": \"center\", \"logo_opacity\": 1, \"frame_style\": \"none\", \"", "module": "qr_preview", "function": "preview_qr_svg", "line": 242}
{"timestamp": "2025-06-12T17:05:33.224011", "level": "DEBUG", "logger": "backend.routes.qr_preview", "message": "Form data keys: ['request']", "module": "qr_preview", "function": "preview_qr_svg", "line": 297}
{"timestamp": "2025-06-12T17:05:33.224052", "level": "DEBUG", "logger": "backend.routes.qr_preview", "message": "No logo key found in form data", "module": "qr_preview", "function": "preview_qr_svg", "line": 309}
{"timestamp": "2025-06-12T17:05:33.224095", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Formatting content for QR type: qrtype.scooter_unlock, content keys: ['scooter_id', 'unlock_code', 'title', 'description', 'service_provider']", "module": "qr_preview", "function": "_format_qr_data", "line": 876}
{"timestamp": "2025-06-12T17:05:33.242511", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Logo remove background setting: False, has logo: False", "module": "qr_preview", "function": "generate_preview_svg_modular", "line": 727}
{"timestamp": "2025-06-12T17:05:33.262905", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Drawing 3 finder patterns at positions: [(0, 0), (46, 0), (0, 46)]", "module": "qr_preview", "function": "generate_preview_svg_modular", "line": 775}
{"timestamp": "2025-06-12T17:05:33.262963", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "QR size: 53, border_style: square, center_style: square", "module": "qr_preview", "function": "generate_preview_svg_modular", "line": 776}
{"timestamp": "2025-06-12T17:05:33.263000", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Drawing finder pattern 1/3 at position (0, 0)", "module": "qr_preview", "function": "generate_preview_svg_modular", "line": 780}
{"timestamp": "2025-06-12T17:05:33.263076", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "_draw_finder_marker called: mx=0, my=0, module_size=5.471698113207547, border_style=square, center_style=square, border_color=#000000, center_color=#000000, offset=(20, 20)", "module": "qr_preview", "function": "_draw_finder_marker", "line": 97}
{"timestamp": "2025-06-12T17:05:33.263120", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Creating finder pattern group with id='marker-0-0'", "module": "qr_preview", "function": "_draw_finder_marker", "line": 110}
{"timestamp": "2025-06-12T17:05:33.263155", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Drawing square border:", "module": "qr_preview", "function": "_draw_finder_marker", "line": 160}
{"timestamp": "2025-06-12T17:05:33.263188", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "  Outer: insert=(20.0, 20.0), size=(38.301886792452834, 38.301886792452834)", "module": "qr_preview", "function": "_draw_finder_marker", "line": 161}
{"timestamp": "2025-06-12T17:05:33.263218", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "  Inner: insert=(26.566037735849058, 26.566037735849058), size=(25.169811320754715, 25.169811320754715)", "module": "qr_preview", "function": "_draw_finder_marker", "line": 162}
{"timestamp": "2025-06-12T17:05:33.263286", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Drawing marker center: style=square, color=#000000, pos=(33.67924528301887, 33.67924528301887), size=10.943396226415095", "module": "qr_preview", "function": "_draw_marker_center", "line": 179}
{"timestamp": "2025-06-12T17:05:33.263332", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Successfully added finder pattern group to SVG", "module": "qr_preview", "function": "_draw_finder_marker", "line": 171}
{"timestamp": "2025-06-12T17:05:33.263360", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Successfully drew finder pattern 1/3", "module": "qr_preview", "function": "generate_preview_svg_modular", "line": 782}
{"timestamp": "2025-06-12T17:05:33.263388", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Drawing finder pattern 2/3 at position (46, 0)", "module": "qr_preview", "function": "generate_preview_svg_modular", "line": 780}
{"timestamp": "2025-06-12T17:05:33.263416", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "_draw_finder_marker called: mx=46, my=0, module_size=5.471698113207547, border_style=square, center_style=square, border_color=#000000, center_color=#000000, offset=(20, 20)", "module": "qr_preview", "function": "_draw_finder_marker", "line": 97}
{"timestamp": "2025-06-12T17:05:33.263452", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Creating finder pattern group with id='marker-46-0'", "module": "qr_preview", "function": "_draw_finder_marker", "line": 110}
{"timestamp": "2025-06-12T17:05:33.263479", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Drawing square border:", "module": "qr_preview", "function": "_draw_finder_marker", "line": 160}
{"timestamp": "2025-06-12T17:05:33.263508", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "  Outer: insert=(271.6981132075472, 20.0), size=(38.301886792452834, 38.301886792452834)", "module": "qr_preview", "function": "_draw_finder_marker", "line": 161}
{"timestamp": "2025-06-12T17:05:33.263538", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "  Inner: insert=(278.26415094339626, 26.566037735849058), size=(25.169811320754715, 25.169811320754715)", "module": "qr_preview", "function": "_draw_finder_marker", "line": 162}
{"timestamp": "2025-06-12T17:05:33.263592", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Drawing marker center: style=square, color=#000000, pos=(285.37735849056605, 33.67924528301887), size=10.943396226415095", "module": "qr_preview", "function": "_draw_marker_center", "line": 179}
{"timestamp": "2025-06-12T17:05:33.263646", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Successfully added finder pattern group to SVG", "module": "qr_preview", "function": "_draw_finder_marker", "line": 171}
{"timestamp": "2025-06-12T17:05:33.263672", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Successfully drew finder pattern 2/3", "module": "qr_preview", "function": "generate_preview_svg_modular", "line": 782}
{"timestamp": "2025-06-12T17:05:33.263702", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Drawing finder pattern 3/3 at position (0, 46)", "module": "qr_preview", "function": "generate_preview_svg_modular", "line": 780}
{"timestamp": "2025-06-12T17:05:33.263729", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "_draw_finder_marker called: mx=0, my=46, module_size=5.471698113207547, border_style=square, center_style=square, border_color=#000000, center_color=#000000, offset=(20, 20)", "module": "qr_preview", "function": "_draw_finder_marker", "line": 97}
{"timestamp": "2025-06-12T17:05:33.263763", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Creating finder pattern group with id='marker-0-46'", "module": "qr_preview", "function": "_draw_finder_marker", "line": 110}
{"timestamp": "2025-06-12T17:05:33.263789", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Drawing square border:", "module": "qr_preview", "function": "_draw_finder_marker", "line": 160}
{"timestamp": "2025-06-12T17:05:33.263817", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "  Outer: insert=(20.0, 271.6981132075472), size=(38.301886792452834, 38.301886792452834)", "module": "qr_preview", "function": "_draw_finder_marker", "line": 161}
{"timestamp": "2025-06-12T17:05:33.263844", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "  Inner: insert=(26.566037735849058, 278.26415094339626), size=(25.169811320754715, 25.169811320754715)", "module": "qr_preview", "function": "_draw_finder_marker", "line": 162}
{"timestamp": "2025-06-12T17:05:33.263900", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Drawing marker center: style=square, color=#000000, pos=(33.67924528301887, 285.37735849056605), size=10.943396226415095", "module": "qr_preview", "function": "_draw_marker_center", "line": 179}
{"timestamp": "2025-06-12T17:05:33.263940", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Successfully added finder pattern group to SVG", "module": "qr_preview", "function": "_draw_finder_marker", "line": 171}
{"timestamp": "2025-06-12T17:05:33.263967", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Successfully drew finder pattern 3/3", "module": "qr_preview", "function": "generate_preview_svg_modular", "line": 782}
{"timestamp": "2025-06-12T17:05:33.290067", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Generated SVG length: 279518", "module": "qr_preview", "function": "generate_preview_svg_modular", "line": 802}
{"timestamp": "2025-06-12T17:05:33.290246", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "SVG contains marker-0-0: True", "module": "qr_preview", "function": "generate_preview_svg_modular", "line": 803}
{"timestamp": "2025-06-12T17:05:33.290390", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "SVG contains marker-18-0: False", "module": "qr_preview", "function": "generate_preview_svg_modular", "line": 804}
{"timestamp": "2025-06-12T17:05:33.290525", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "SVG contains marker-0-18: False", "module": "qr_preview", "function": "generate_preview_svg_modular", "line": 805}
{"timestamp": "2025-06-12T17:05:33.290971", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "SVG marker counts - outer: 3, inner: 3, dot: 0", "module": "qr_preview", "function": "generate_preview_svg_modular", "line": 813}
{"timestamp": "2025-06-12T17:05:33.291000", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "SVG label counts - text: 0, background: 0", "module": "qr_preview", "function": "generate_preview_svg_modular", "line": 814}
{"timestamp": "2025-06-12T17:05:33.293408", "level": "DEBUG", "logger": "backend.main", "message": "[Auth] Enforced Bearer token authentication for request", "module": "main", "function": "bearer_token_auth_middleware", "line": 274}
{"timestamp": "2025-06-12T17:05:40.358668", "level": "DEBUG", "logger": "backend.core.security.jwt_keys", "message": "Token contains key ID: 8be4a455-8974-4e97-b307-6c8428b2c9ff", "module": "jwt_keys", "function": "decode_token", "line": 188}
{"timestamp": "2025-06-12T17:05:40.358935", "level": "DEBUG", "logger": "backend.core.security.jwt_keys", "message": "Using key ID 8be4a455-8974-4e97-b307-6c8428b2c9ff for verification", "module": "jwt_keys", "function": "decode_token", "line": 195}
{"timestamp": "2025-06-12T17:05:40.359391", "level": "DEBUG", "logger": "backend.services.auth", "message": "JWT decoded successfully with key manager", "module": "auth", "function": "authenticate_token", "line": 683}
{"timestamp": "2025-06-12T17:05:40.364688", "level": "DEBUG", "logger": "backend.services.db_utils", "message": "Query 'auth_lookup_e4ae2d8f-b32f-52d7-99cd-8a33b19652c7' completed in 0.004s", "module": "db_utils", "function": "execute_query_with_timeout", "line": 68}
{"timestamp": "2025-06-12T17:05:40.364750", "level": "DEBUG", "logger": "backend.services.db_utils", "message": "Auth query 'auth_lookup_e4ae2d8f-b32f-52d7-99cd-8a33b19652c7' successful, result format preserved", "module": "db_utils", "function": "execute_query_with_timeout", "line": 72}
{"timestamp": "2025-06-12T17:05:40.369960", "level": "DEBUG", "logger": "backend.services.auth", "message": "Authentication attempt with token prefix: eyJhbGciOi...", "module": "auth", "function": "get_current_user", "line": 727}
{"timestamp": "2025-06-12T17:05:40.370055", "level": "DEBUG", "logger": "backend.core.security.jwt_keys", "message": "Token contains key ID: 8be4a455-8974-4e97-b307-6c8428b2c9ff", "module": "jwt_keys", "function": "decode_token", "line": 188}
{"timestamp": "2025-06-12T17:05:40.370091", "level": "DEBUG", "logger": "backend.core.security.jwt_keys", "message": "Using key ID 8be4a455-8974-4e97-b307-6c8428b2c9ff for verification", "module": "jwt_keys", "function": "decode_token", "line": 195}
{"timestamp": "2025-06-12T17:05:40.370199", "level": "DEBUG", "logger": "backend.services.auth", "message": "JWT decoded successfully with key manager", "module": "auth", "function": "authenticate_token", "line": 683}
{"timestamp": "2025-06-12T17:05:40.371362", "level": "DEBUG", "logger": "backend.services.db_utils", "message": "Query 'auth_lookup_e4ae2d8f-b32f-52d7-99cd-8a33b19652c7' completed in 0.001s", "module": "db_utils", "function": "execute_query_with_timeout", "line": 68}
{"timestamp": "2025-06-12T17:05:40.371397", "level": "DEBUG", "logger": "backend.services.db_utils", "message": "Auth query 'auth_lookup_e4ae2d8f-b32f-52d7-99cd-8a33b19652c7' successful, result format preserved", "module": "db_utils", "function": "execute_query_with_timeout", "line": 72}
{"timestamp": "2025-06-12T17:05:40.371446", "level": "DEBUG", "logger": "backend.services.auth", "message": "Successfully authenticated user: <EMAIL>", "module": "auth", "function": "get_current_user", "line": 732}
{"timestamp": "2025-06-12T17:05:40.371482", "level": "DEBUG", "logger": "backend.services.auth", "message": "Successfully authenticated user: <EMAIL>", "module": "auth", "function": "get_current_user", "line": 738}
{"timestamp": "2025-06-12T17:05:40.375188", "level": "DEBUG", "logger": "backend.routes.qr_preview", "message": "Received QR preview SVG request: {\"qr_type\": \"scooter_unlock\", \"data\": {\"scooter_id\": \"SCOOT123\", \"unlock_code\": \"UNLOCK42\", \"title\": \"Scooter_unlock QR Code\", \"description\": \"Scan to view scooter_unlock content\", \"service_provider\": \"Lime\"}, \"designOptions\": {\"size\": 256, \"margin\": 10, \"pattern\": \"diagonal\", \"marker_border_style\": \"square\", \"marker_center_style\": \"square\", \"logo_size\": 0.3, \"logo_position\": \"center\", \"logo_opacity\": 1, \"frame_style\": \"none\", \"frame_font\": \"SF Pro\", \"frame_font_size\": 16, \"foreground_color\": \"#", "module": "qr_preview", "function": "preview_qr_svg", "line": 242}
{"timestamp": "2025-06-12T17:05:40.375418", "level": "DEBUG", "logger": "backend.routes.qr_preview", "message": "Form data keys: ['request', 'logo']", "module": "qr_preview", "function": "preview_qr_svg", "line": 297}
{"timestamp": "2025-06-12T17:05:40.375464", "level": "DEBUG", "logger": "backend.routes.qr_preview", "message": "Logo file found: type=<class 'starlette.datastructures.UploadFile'>, filename=LOGO - 1080 - BLACK.png", "module": "qr_preview", "function": "preview_qr_svg", "line": 300}
{"timestamp": "2025-06-12T17:05:40.375509", "level": "DEBUG", "logger": "backend.routes.qr_preview", "message": "Logo content read: 127619 bytes", "module": "qr_preview", "function": "preview_qr_svg", "line": 305}
{"timestamp": "2025-06-12T17:05:40.375598", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Formatting content for QR type: qrtype.scooter_unlock, content keys: ['scooter_id', 'unlock_code', 'title', 'description', 'service_provider']", "module": "qr_preview", "function": "_format_qr_data", "line": 876}
{"timestamp": "2025-06-12T17:05:40.394546", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Logo remove background setting: False, has logo: True", "module": "qr_preview", "function": "generate_preview_svg_modular", "line": 727}
{"timestamp": "2025-06-12T17:05:40.394587", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Logo present but background removal disabled", "module": "qr_preview", "function": "generate_preview_svg_modular", "line": 741}
{"timestamp": "2025-06-12T17:05:40.413765", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Drawing 3 finder patterns at positions: [(0, 0), (46, 0), (0, 46)]", "module": "qr_preview", "function": "generate_preview_svg_modular", "line": 775}
{"timestamp": "2025-06-12T17:05:40.413809", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "QR size: 53, border_style: square, center_style: square", "module": "qr_preview", "function": "generate_preview_svg_modular", "line": 776}
{"timestamp": "2025-06-12T17:05:40.413844", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Drawing finder pattern 1/3 at position (0, 0)", "module": "qr_preview", "function": "generate_preview_svg_modular", "line": 780}
{"timestamp": "2025-06-12T17:05:40.413896", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "_draw_finder_marker called: mx=0, my=0, module_size=5.471698113207547, border_style=square, center_style=square, border_color=#000000, center_color=#000000, offset=(20, 20)", "module": "qr_preview", "function": "_draw_finder_marker", "line": 97}
{"timestamp": "2025-06-12T17:05:40.413954", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Creating finder pattern group with id='marker-0-0'", "module": "qr_preview", "function": "_draw_finder_marker", "line": 110}
{"timestamp": "2025-06-12T17:05:40.413988", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Drawing square border:", "module": "qr_preview", "function": "_draw_finder_marker", "line": 160}
{"timestamp": "2025-06-12T17:05:40.414021", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "  Outer: insert=(20.0, 20.0), size=(38.301886792452834, 38.301886792452834)", "module": "qr_preview", "function": "_draw_finder_marker", "line": 161}
{"timestamp": "2025-06-12T17:05:40.414054", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "  Inner: insert=(26.566037735849058, 26.566037735849058), size=(25.169811320754715, 25.169811320754715)", "module": "qr_preview", "function": "_draw_finder_marker", "line": 162}
{"timestamp": "2025-06-12T17:05:40.414119", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Drawing marker center: style=square, color=#000000, pos=(33.67924528301887, 33.67924528301887), size=10.943396226415095", "module": "qr_preview", "function": "_draw_marker_center", "line": 179}
{"timestamp": "2025-06-12T17:05:40.414208", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Successfully added finder pattern group to SVG", "module": "qr_preview", "function": "_draw_finder_marker", "line": 171}
{"timestamp": "2025-06-12T17:05:40.414240", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Successfully drew finder pattern 1/3", "module": "qr_preview", "function": "generate_preview_svg_modular", "line": 782}
{"timestamp": "2025-06-12T17:05:40.414270", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Drawing finder pattern 2/3 at position (46, 0)", "module": "qr_preview", "function": "generate_preview_svg_modular", "line": 780}
{"timestamp": "2025-06-12T17:05:40.414302", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "_draw_finder_marker called: mx=46, my=0, module_size=5.471698113207547, border_style=square, center_style=square, border_color=#000000, center_color=#000000, offset=(20, 20)", "module": "qr_preview", "function": "_draw_finder_marker", "line": 97}
{"timestamp": "2025-06-12T17:05:40.414339", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Creating finder pattern group with id='marker-46-0'", "module": "qr_preview", "function": "_draw_finder_marker", "line": 110}
{"timestamp": "2025-06-12T17:05:40.414369", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Drawing square border:", "module": "qr_preview", "function": "_draw_finder_marker", "line": 160}
{"timestamp": "2025-06-12T17:05:40.414401", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "  Outer: insert=(271.6981132075472, 20.0), size=(38.301886792452834, 38.301886792452834)", "module": "qr_preview", "function": "_draw_finder_marker", "line": 161}
{"timestamp": "2025-06-12T17:05:40.414436", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "  Inner: insert=(278.26415094339626, 26.566037735849058), size=(25.169811320754715, 25.169811320754715)", "module": "qr_preview", "function": "_draw_finder_marker", "line": 162}
{"timestamp": "2025-06-12T17:05:40.414493", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Drawing marker center: style=square, color=#000000, pos=(285.37735849056605, 33.67924528301887), size=10.943396226415095", "module": "qr_preview", "function": "_draw_marker_center", "line": 179}
{"timestamp": "2025-06-12T17:05:40.414538", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Successfully added finder pattern group to SVG", "module": "qr_preview", "function": "_draw_finder_marker", "line": 171}
{"timestamp": "2025-06-12T17:05:40.414568", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Successfully drew finder pattern 2/3", "module": "qr_preview", "function": "generate_preview_svg_modular", "line": 782}
{"timestamp": "2025-06-12T17:05:40.414598", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Drawing finder pattern 3/3 at position (0, 46)", "module": "qr_preview", "function": "generate_preview_svg_modular", "line": 780}
{"timestamp": "2025-06-12T17:05:40.414628", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "_draw_finder_marker called: mx=0, my=46, module_size=5.471698113207547, border_style=square, center_style=square, border_color=#000000, center_color=#000000, offset=(20, 20)", "module": "qr_preview", "function": "_draw_finder_marker", "line": 97}
{"timestamp": "2025-06-12T17:05:40.414665", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Creating finder pattern group with id='marker-0-46'", "module": "qr_preview", "function": "_draw_finder_marker", "line": 110}
{"timestamp": "2025-06-12T17:05:40.414698", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Drawing square border:", "module": "qr_preview", "function": "_draw_finder_marker", "line": 160}
{"timestamp": "2025-06-12T17:05:40.414746", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "  Outer: insert=(20.0, 271.6981132075472), size=(38.301886792452834, 38.301886792452834)", "module": "qr_preview", "function": "_draw_finder_marker", "line": 161}
{"timestamp": "2025-06-12T17:05:40.414777", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "  Inner: insert=(26.566037735849058, 278.26415094339626), size=(25.169811320754715, 25.169811320754715)", "module": "qr_preview", "function": "_draw_finder_marker", "line": 162}
{"timestamp": "2025-06-12T17:05:40.414830", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Drawing marker center: style=square, color=#000000, pos=(33.67924528301887, 285.37735849056605), size=10.943396226415095", "module": "qr_preview", "function": "_draw_marker_center", "line": 179}
{"timestamp": "2025-06-12T17:05:40.414874", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Successfully added finder pattern group to SVG", "module": "qr_preview", "function": "_draw_finder_marker", "line": 171}
{"timestamp": "2025-06-12T17:05:40.414905", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Successfully drew finder pattern 3/3", "module": "qr_preview", "function": "generate_preview_svg_modular", "line": 782}
{"timestamp": "2025-06-12T17:05:40.414956", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "_draw_logo called with logo type: <class 'bytes'>, dimensions: 300", "module": "qr_preview", "function": "_draw_logo", "line": 434}
{"timestamp": "2025-06-12T17:05:40.782981", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Generated SVG length: 283664", "module": "qr_preview", "function": "generate_preview_svg_modular", "line": 802}
{"timestamp": "2025-06-12T17:05:40.783163", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "SVG contains marker-0-0: True", "module": "qr_preview", "function": "generate_preview_svg_modular", "line": 803}
{"timestamp": "2025-06-12T17:05:40.783311", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "SVG contains marker-18-0: False", "module": "qr_preview", "function": "generate_preview_svg_modular", "line": 804}
{"timestamp": "2025-06-12T17:05:40.783458", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "SVG contains marker-0-18: False", "module": "qr_preview", "function": "generate_preview_svg_modular", "line": 805}
{"timestamp": "2025-06-12T17:05:40.783918", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "SVG marker counts - outer: 3, inner: 3, dot: 0", "module": "qr_preview", "function": "generate_preview_svg_modular", "line": 813}
{"timestamp": "2025-06-12T17:05:40.783953", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "SVG label counts - text: 0, background: 0", "module": "qr_preview", "function": "generate_preview_svg_modular", "line": 814}
{"timestamp": "2025-06-12T17:05:40.786471", "level": "DEBUG", "logger": "backend.main", "message": "[Auth] Enforced Bearer token authentication for request", "module": "main", "function": "bearer_token_auth_middleware", "line": 274}
{"timestamp": "2025-06-12T17:05:41.621311", "level": "DEBUG", "logger": "backend.core.security.jwt_keys", "message": "Token contains key ID: 8be4a455-8974-4e97-b307-6c8428b2c9ff", "module": "jwt_keys", "function": "decode_token", "line": 188}
{"timestamp": "2025-06-12T17:05:41.621373", "level": "DEBUG", "logger": "backend.core.security.jwt_keys", "message": "Using key ID 8be4a455-8974-4e97-b307-6c8428b2c9ff for verification", "module": "jwt_keys", "function": "decode_token", "line": 195}
{"timestamp": "2025-06-12T17:05:41.621498", "level": "DEBUG", "logger": "backend.services.auth", "message": "JWT decoded successfully with key manager", "module": "auth", "function": "authenticate_token", "line": 683}
{"timestamp": "2025-06-12T17:05:41.623279", "level": "DEBUG", "logger": "backend.services.db_utils", "message": "Query 'auth_lookup_e4ae2d8f-b32f-52d7-99cd-8a33b19652c7' completed in 0.002s", "module": "db_utils", "function": "execute_query_with_timeout", "line": 68}
{"timestamp": "2025-06-12T17:05:41.623365", "level": "DEBUG", "logger": "backend.services.db_utils", "message": "Auth query 'auth_lookup_e4ae2d8f-b32f-52d7-99cd-8a33b19652c7' successful, result format preserved", "module": "db_utils", "function": "execute_query_with_timeout", "line": 72}
{"timestamp": "2025-06-12T17:05:41.637941", "level": "DEBUG", "logger": "backend.services.auth", "message": "Authentication attempt with token prefix: eyJhbGciOi...", "module": "auth", "function": "get_current_user", "line": 727}
{"timestamp": "2025-06-12T17:05:41.638074", "level": "DEBUG", "logger": "backend.core.security.jwt_keys", "message": "Token contains key ID: 8be4a455-8974-4e97-b307-6c8428b2c9ff", "module": "jwt_keys", "function": "decode_token", "line": 188}
{"timestamp": "2025-06-12T17:05:41.638115", "level": "DEBUG", "logger": "backend.core.security.jwt_keys", "message": "Using key ID 8be4a455-8974-4e97-b307-6c8428b2c9ff for verification", "module": "jwt_keys", "function": "decode_token", "line": 195}
{"timestamp": "2025-06-12T17:05:41.638391", "level": "DEBUG", "logger": "backend.services.auth", "message": "JWT decoded successfully with key manager", "module": "auth", "function": "authenticate_token", "line": 683}
{"timestamp": "2025-06-12T17:05:41.640594", "level": "DEBUG", "logger": "backend.services.db_utils", "message": "Query 'auth_lookup_e4ae2d8f-b32f-52d7-99cd-8a33b19652c7' completed in 0.002s", "module": "db_utils", "function": "execute_query_with_timeout", "line": 68}
{"timestamp": "2025-06-12T17:05:41.640680", "level": "DEBUG", "logger": "backend.services.db_utils", "message": "Auth query 'auth_lookup_e4ae2d8f-b32f-52d7-99cd-8a33b19652c7' successful, result format preserved", "module": "db_utils", "function": "execute_query_with_timeout", "line": 72}
{"timestamp": "2025-06-12T17:05:41.640752", "level": "DEBUG", "logger": "backend.services.auth", "message": "Successfully authenticated user: <EMAIL>", "module": "auth", "function": "get_current_user", "line": 732}
{"timestamp": "2025-06-12T17:05:41.640794", "level": "DEBUG", "logger": "backend.services.auth", "message": "Successfully authenticated user: <EMAIL>", "module": "auth", "function": "get_current_user", "line": 738}
{"timestamp": "2025-06-12T17:05:41.642712", "level": "DEBUG", "logger": "backend.routes.qr_preview", "message": "Received QR preview SVG request: {\"qr_type\": \"scooter_unlock\", \"data\": {\"scooter_id\": \"SCOOT123\", \"unlock_code\": \"UNLOCK42\", \"title\": \"Scooter_unlock QR Code\", \"description\": \"Scan to view scooter_unlock content\", \"service_provider\": \"Lime\"}, \"designOptions\": {\"size\": 256, \"margin\": 10, \"pattern\": \"diagonal\", \"marker_border_style\": \"square\", \"marker_border_color\": \"#000000\", \"marker_center_style\": \"square\", \"marker_center_color\": \"#000000\", \"logo_size\": 0.3, \"logo_position\": \"center\", \"logo_opacity\": 1, \"frame_style\": \"none\", \"", "module": "qr_preview", "function": "preview_qr_svg", "line": 242}
{"timestamp": "2025-06-12T17:05:41.642817", "level": "DEBUG", "logger": "backend.routes.qr_preview", "message": "Form data keys: ['request', 'logo']", "module": "qr_preview", "function": "preview_qr_svg", "line": 297}
{"timestamp": "2025-06-12T17:05:41.642864", "level": "DEBUG", "logger": "backend.routes.qr_preview", "message": "Logo file found: type=<class 'starlette.datastructures.UploadFile'>, filename=LOGO - 1080 - BLACK.png", "module": "qr_preview", "function": "preview_qr_svg", "line": 300}
{"timestamp": "2025-06-12T17:05:41.642912", "level": "DEBUG", "logger": "backend.routes.qr_preview", "message": "Logo content read: 127619 bytes", "module": "qr_preview", "function": "preview_qr_svg", "line": 305}
{"timestamp": "2025-06-12T17:05:41.642960", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Formatting content for QR type: qrtype.scooter_unlock, content keys: ['scooter_id', 'unlock_code', 'title', 'description', 'service_provider']", "module": "qr_preview", "function": "_format_qr_data", "line": 876}
{"timestamp": "2025-06-12T17:05:41.661845", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Logo remove background setting: False, has logo: True", "module": "qr_preview", "function": "generate_preview_svg_modular", "line": 727}
{"timestamp": "2025-06-12T17:05:41.661907", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Logo present but background removal disabled", "module": "qr_preview", "function": "generate_preview_svg_modular", "line": 741}
{"timestamp": "2025-06-12T17:05:41.681669", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Drawing 3 finder patterns at positions: [(0, 0), (46, 0), (0, 46)]", "module": "qr_preview", "function": "generate_preview_svg_modular", "line": 775}
{"timestamp": "2025-06-12T17:05:41.681718", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "QR size: 53, border_style: square, center_style: square", "module": "qr_preview", "function": "generate_preview_svg_modular", "line": 776}
{"timestamp": "2025-06-12T17:05:41.681753", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Drawing finder pattern 1/3 at position (0, 0)", "module": "qr_preview", "function": "generate_preview_svg_modular", "line": 780}
{"timestamp": "2025-06-12T17:05:41.681796", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "_draw_finder_marker called: mx=0, my=0, module_size=5.471698113207547, border_style=square, center_style=square, border_color=#000000, center_color=#000000, offset=(20, 20)", "module": "qr_preview", "function": "_draw_finder_marker", "line": 97}
{"timestamp": "2025-06-12T17:05:41.681843", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Creating finder pattern group with id='marker-0-0'", "module": "qr_preview", "function": "_draw_finder_marker", "line": 110}
{"timestamp": "2025-06-12T17:05:41.681879", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Drawing square border:", "module": "qr_preview", "function": "_draw_finder_marker", "line": 160}
{"timestamp": "2025-06-12T17:05:41.681915", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "  Outer: insert=(20.0, 20.0), size=(38.301886792452834, 38.301886792452834)", "module": "qr_preview", "function": "_draw_finder_marker", "line": 161}
{"timestamp": "2025-06-12T17:05:41.681949", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "  Inner: insert=(26.566037735849058, 26.566037735849058), size=(25.169811320754715, 25.169811320754715)", "module": "qr_preview", "function": "_draw_finder_marker", "line": 162}
{"timestamp": "2025-06-12T17:05:41.682020", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Drawing marker center: style=square, color=#000000, pos=(33.67924528301887, 33.67924528301887), size=10.943396226415095", "module": "qr_preview", "function": "_draw_marker_center", "line": 179}
{"timestamp": "2025-06-12T17:05:41.682067", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Successfully added finder pattern group to SVG", "module": "qr_preview", "function": "_draw_finder_marker", "line": 171}
{"timestamp": "2025-06-12T17:05:41.682100", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Successfully drew finder pattern 1/3", "module": "qr_preview", "function": "generate_preview_svg_modular", "line": 782}
{"timestamp": "2025-06-12T17:05:41.682131", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Drawing finder pattern 2/3 at position (46, 0)", "module": "qr_preview", "function": "generate_preview_svg_modular", "line": 780}
{"timestamp": "2025-06-12T17:05:41.682165", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "_draw_finder_marker called: mx=46, my=0, module_size=5.471698113207547, border_style=square, center_style=square, border_color=#000000, center_color=#000000, offset=(20, 20)", "module": "qr_preview", "function": "_draw_finder_marker", "line": 97}
{"timestamp": "2025-06-12T17:05:41.682233", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Creating finder pattern group with id='marker-46-0'", "module": "qr_preview", "function": "_draw_finder_marker", "line": 110}
{"timestamp": "2025-06-12T17:05:41.682266", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Drawing square border:", "module": "qr_preview", "function": "_draw_finder_marker", "line": 160}
{"timestamp": "2025-06-12T17:05:41.682299", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "  Outer: insert=(271.6981132075472, 20.0), size=(38.301886792452834, 38.301886792452834)", "module": "qr_preview", "function": "_draw_finder_marker", "line": 161}
{"timestamp": "2025-06-12T17:05:41.682331", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "  Inner: insert=(278.26415094339626, 26.566037735849058), size=(25.169811320754715, 25.169811320754715)", "module": "qr_preview", "function": "_draw_finder_marker", "line": 162}
{"timestamp": "2025-06-12T17:05:41.682386", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Drawing marker center: style=square, color=#000000, pos=(285.37735849056605, 33.67924528301887), size=10.943396226415095", "module": "qr_preview", "function": "_draw_marker_center", "line": 179}
{"timestamp": "2025-06-12T17:05:41.682461", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Successfully added finder pattern group to SVG", "module": "qr_preview", "function": "_draw_finder_marker", "line": 171}
{"timestamp": "2025-06-12T17:05:41.682492", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Successfully drew finder pattern 2/3", "module": "qr_preview", "function": "generate_preview_svg_modular", "line": 782}
{"timestamp": "2025-06-12T17:05:41.682524", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Drawing finder pattern 3/3 at position (0, 46)", "module": "qr_preview", "function": "generate_preview_svg_modular", "line": 780}
{"timestamp": "2025-06-12T17:05:41.682558", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "_draw_finder_marker called: mx=0, my=46, module_size=5.471698113207547, border_style=square, center_style=square, border_color=#000000, center_color=#000000, offset=(20, 20)", "module": "qr_preview", "function": "_draw_finder_marker", "line": 97}
{"timestamp": "2025-06-12T17:05:41.682597", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Creating finder pattern group with id='marker-0-46'", "module": "qr_preview", "function": "_draw_finder_marker", "line": 110}
{"timestamp": "2025-06-12T17:05:41.682627", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Drawing square border:", "module": "qr_preview", "function": "_draw_finder_marker", "line": 160}
{"timestamp": "2025-06-12T17:05:41.682660", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "  Outer: insert=(20.0, 271.6981132075472), size=(38.301886792452834, 38.301886792452834)", "module": "qr_preview", "function": "_draw_finder_marker", "line": 161}
{"timestamp": "2025-06-12T17:05:41.682694", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "  Inner: insert=(26.566037735849058, 278.26415094339626), size=(25.169811320754715, 25.169811320754715)", "module": "qr_preview", "function": "_draw_finder_marker", "line": 162}
{"timestamp": "2025-06-12T17:05:41.682750", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Drawing marker center: style=square, color=#000000, pos=(33.67924528301887, 285.37735849056605), size=10.943396226415095", "module": "qr_preview", "function": "_draw_marker_center", "line": 179}
{"timestamp": "2025-06-12T17:05:41.682795", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Successfully added finder pattern group to SVG", "module": "qr_preview", "function": "_draw_finder_marker", "line": 171}
{"timestamp": "2025-06-12T17:05:41.682827", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Successfully drew finder pattern 3/3", "module": "qr_preview", "function": "generate_preview_svg_modular", "line": 782}
{"timestamp": "2025-06-12T17:05:41.682878", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "_draw_logo called with logo type: <class 'bytes'>, dimensions: 300", "module": "qr_preview", "function": "_draw_logo", "line": 434}
{"timestamp": "2025-06-12T17:05:42.006683", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Generated SVG length: 283664", "module": "qr_preview", "function": "generate_preview_svg_modular", "line": 802}
{"timestamp": "2025-06-12T17:05:42.006877", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "SVG contains marker-0-0: True", "module": "qr_preview", "function": "generate_preview_svg_modular", "line": 803}
{"timestamp": "2025-06-12T17:05:42.007030", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "SVG contains marker-18-0: False", "module": "qr_preview", "function": "generate_preview_svg_modular", "line": 804}
{"timestamp": "2025-06-12T17:05:42.007220", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "SVG contains marker-0-18: False", "module": "qr_preview", "function": "generate_preview_svg_modular", "line": 805}
{"timestamp": "2025-06-12T17:05:42.007690", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "SVG marker counts - outer: 3, inner: 3, dot: 0", "module": "qr_preview", "function": "generate_preview_svg_modular", "line": 813}
{"timestamp": "2025-06-12T17:05:42.007731", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "SVG label counts - text: 0, background: 0", "module": "qr_preview", "function": "generate_preview_svg_modular", "line": 814}
{"timestamp": "2025-06-12T17:05:42.010088", "level": "DEBUG", "logger": "backend.main", "message": "[Auth] Enforced Bearer token authentication for request", "module": "main", "function": "bearer_token_auth_middleware", "line": 274}
{"timestamp": "2025-06-12T17:05:42.294240", "level": "DEBUG", "logger": "backend.main", "message": "[Auth] Enforced Bearer token authentication for request", "module": "main", "function": "bearer_token_auth_middleware", "line": 274}
{"timestamp": "2025-06-12T17:05:43.329696", "level": "DEBUG", "logger": "backend.main", "message": "[Auth] Enforced Bearer token authentication for request", "module": "main", "function": "bearer_token_auth_middleware", "line": 274}
{"timestamp": "2025-06-12T17:05:45.582548", "level": "DEBUG", "logger": "backend.core.security.jwt_keys", "message": "Token contains key ID: 8be4a455-8974-4e97-b307-6c8428b2c9ff", "module": "jwt_keys", "function": "decode_token", "line": 188}
{"timestamp": "2025-06-12T17:05:45.582613", "level": "DEBUG", "logger": "backend.core.security.jwt_keys", "message": "Using key ID 8be4a455-8974-4e97-b307-6c8428b2c9ff for verification", "module": "jwt_keys", "function": "decode_token", "line": 195}
{"timestamp": "2025-06-12T17:05:45.582861", "level": "DEBUG", "logger": "backend.services.auth", "message": "JWT decoded successfully with key manager", "module": "auth", "function": "authenticate_token", "line": 683}
{"timestamp": "2025-06-12T17:05:45.586646", "level": "DEBUG", "logger": "backend.services.db_utils", "message": "Query 'auth_lookup_e4ae2d8f-b32f-52d7-99cd-8a33b19652c7' completed in 0.003s", "module": "db_utils", "function": "execute_query_with_timeout", "line": 68}
{"timestamp": "2025-06-12T17:05:45.586702", "level": "DEBUG", "logger": "backend.services.db_utils", "message": "Auth query 'auth_lookup_e4ae2d8f-b32f-52d7-99cd-8a33b19652c7' successful, result format preserved", "module": "db_utils", "function": "execute_query_with_timeout", "line": 72}
{"timestamp": "2025-06-12T17:05:45.592578", "level": "DEBUG", "logger": "backend.services.auth", "message": "Authentication attempt with token prefix: eyJhbGciOi...", "module": "auth", "function": "get_current_user", "line": 727}
{"timestamp": "2025-06-12T17:05:45.592664", "level": "DEBUG", "logger": "backend.core.security.jwt_keys", "message": "Token contains key ID: 8be4a455-8974-4e97-b307-6c8428b2c9ff", "module": "jwt_keys", "function": "decode_token", "line": 188}
{"timestamp": "2025-06-12T17:05:45.592736", "level": "DEBUG", "logger": "backend.core.security.jwt_keys", "message": "Using key ID 8be4a455-8974-4e97-b307-6c8428b2c9ff for verification", "module": "jwt_keys", "function": "decode_token", "line": 195}
{"timestamp": "2025-06-12T17:05:45.592832", "level": "DEBUG", "logger": "backend.services.auth", "message": "JWT decoded successfully with key manager", "module": "auth", "function": "authenticate_token", "line": 683}
{"timestamp": "2025-06-12T17:05:45.593783", "level": "DEBUG", "logger": "backend.services.db_utils", "message": "Query 'auth_lookup_e4ae2d8f-b32f-52d7-99cd-8a33b19652c7' completed in 0.001s", "module": "db_utils", "function": "execute_query_with_timeout", "line": 68}
{"timestamp": "2025-06-12T17:05:45.593821", "level": "DEBUG", "logger": "backend.services.db_utils", "message": "Auth query 'auth_lookup_e4ae2d8f-b32f-52d7-99cd-8a33b19652c7' successful, result format preserved", "module": "db_utils", "function": "execute_query_with_timeout", "line": 72}
{"timestamp": "2025-06-12T17:05:45.593867", "level": "DEBUG", "logger": "backend.services.auth", "message": "Successfully authenticated user: <EMAIL>", "module": "auth", "function": "get_current_user", "line": 732}
{"timestamp": "2025-06-12T17:05:45.593900", "level": "DEBUG", "logger": "backend.services.auth", "message": "Successfully authenticated user: <EMAIL>", "module": "auth", "function": "get_current_user", "line": 738}
{"timestamp": "2025-06-12T17:05:45.595385", "level": "DEBUG", "logger": "backend.routes.qr_preview", "message": "Received QR preview SVG request: {\"qr_type\": \"scooter_unlock\", \"data\": {\"scooter_id\": \"SCOOT123\", \"unlock_code\": \"UNLOCK42\", \"title\": \"Scooter_unlock QR Code\", \"description\": \"Scan to view scooter_unlock content\", \"service_provider\": \"Lime\"}, \"designOptions\": {\"size\": 256, \"margin\": 10, \"pattern\": \"diagonal\", \"marker_border_style\": \"square\", \"marker_center_style\": \"square\", \"logo_size\": 0.3, \"logo_position\": \"center\", \"logo_opacity\": 1, \"frame_style\": \"none\", \"frame_font\": \"SF Pro\", \"frame_font_size\": 16, \"foreground_color\": \"#", "module": "qr_preview", "function": "preview_qr_svg", "line": 242}
{"timestamp": "2025-06-12T17:05:45.595498", "level": "DEBUG", "logger": "backend.routes.qr_preview", "message": "Form data keys: ['request', 'logo']", "module": "qr_preview", "function": "preview_qr_svg", "line": 297}
{"timestamp": "2025-06-12T17:05:45.595535", "level": "DEBUG", "logger": "backend.routes.qr_preview", "message": "Logo file found: type=<class 'starlette.datastructures.UploadFile'>, filename=LOGO - 1080 - BLACK.png", "module": "qr_preview", "function": "preview_qr_svg", "line": 300}
{"timestamp": "2025-06-12T17:05:45.595572", "level": "DEBUG", "logger": "backend.routes.qr_preview", "message": "Logo content read: 127619 bytes", "module": "qr_preview", "function": "preview_qr_svg", "line": 305}
{"timestamp": "2025-06-12T17:05:45.595628", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Formatting content for QR type: qrtype.scooter_unlock, content keys: ['scooter_id', 'unlock_code', 'title', 'description', 'service_provider']", "module": "qr_preview", "function": "_format_qr_data", "line": 876}
{"timestamp": "2025-06-12T17:05:45.614029", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Logo remove background setting: True, has logo: True", "module": "qr_preview", "function": "generate_preview_svg_modular", "line": 727}
{"timestamp": "2025-06-12T17:05:45.614078", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Logo area calculated: (19, 19, 34, 34), size_fraction: 0.3, size_modules: 15", "module": "qr_preview", "function": "generate_preview_svg_modular", "line": 739}
{"timestamp": "2025-06-12T17:05:45.631781", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Skipped 106 modules out of 1380 total modules for logo background removal", "module": "qr_preview", "function": "generate_preview_svg_modular", "line": 766}
{"timestamp": "2025-06-12T17:05:45.631875", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Drawing 3 finder patterns at positions: [(0, 0), (46, 0), (0, 46)]", "module": "qr_preview", "function": "generate_preview_svg_modular", "line": 775}
{"timestamp": "2025-06-12T17:05:45.631910", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "QR size: 53, border_style: square, center_style: square", "module": "qr_preview", "function": "generate_preview_svg_modular", "line": 776}
{"timestamp": "2025-06-12T17:05:45.631943", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Drawing finder pattern 1/3 at position (0, 0)", "module": "qr_preview", "function": "generate_preview_svg_modular", "line": 780}
{"timestamp": "2025-06-12T17:05:45.631982", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "_draw_finder_marker called: mx=0, my=0, module_size=5.471698113207547, border_style=square, center_style=square, border_color=#000000, center_color=#000000, offset=(20, 20)", "module": "qr_preview", "function": "_draw_finder_marker", "line": 97}
{"timestamp": "2025-06-12T17:05:45.632028", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Creating finder pattern group with id='marker-0-0'", "module": "qr_preview", "function": "_draw_finder_marker", "line": 110}
{"timestamp": "2025-06-12T17:05:45.632061", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Drawing square border:", "module": "qr_preview", "function": "_draw_finder_marker", "line": 160}
{"timestamp": "2025-06-12T17:05:45.632094", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "  Outer: insert=(20.0, 20.0), size=(38.301886792452834, 38.301886792452834)", "module": "qr_preview", "function": "_draw_finder_marker", "line": 161}
{"timestamp": "2025-06-12T17:05:45.632126", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "  Inner: insert=(26.566037735849058, 26.566037735849058), size=(25.169811320754715, 25.169811320754715)", "module": "qr_preview", "function": "_draw_finder_marker", "line": 162}
{"timestamp": "2025-06-12T17:05:45.632188", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Drawing marker center: style=square, color=#000000, pos=(33.67924528301887, 33.67924528301887), size=10.943396226415095", "module": "qr_preview", "function": "_draw_marker_center", "line": 179}
{"timestamp": "2025-06-12T17:05:45.632235", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Successfully added finder pattern group to SVG", "module": "qr_preview", "function": "_draw_finder_marker", "line": 171}
{"timestamp": "2025-06-12T17:05:45.632267", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Successfully drew finder pattern 1/3", "module": "qr_preview", "function": "generate_preview_svg_modular", "line": 782}
{"timestamp": "2025-06-12T17:05:45.632298", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Drawing finder pattern 2/3 at position (46, 0)", "module": "qr_preview", "function": "generate_preview_svg_modular", "line": 780}
{"timestamp": "2025-06-12T17:05:45.632330", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "_draw_finder_marker called: mx=46, my=0, module_size=5.471698113207547, border_style=square, center_style=square, border_color=#000000, center_color=#000000, offset=(20, 20)", "module": "qr_preview", "function": "_draw_finder_marker", "line": 97}
{"timestamp": "2025-06-12T17:05:45.632367", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Creating finder pattern group with id='marker-46-0'", "module": "qr_preview", "function": "_draw_finder_marker", "line": 110}
{"timestamp": "2025-06-12T17:05:45.632399", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Drawing square border:", "module": "qr_preview", "function": "_draw_finder_marker", "line": 160}
{"timestamp": "2025-06-12T17:05:45.632445", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "  Outer: insert=(271.6981132075472, 20.0), size=(38.301886792452834, 38.301886792452834)", "module": "qr_preview", "function": "_draw_finder_marker", "line": 161}
{"timestamp": "2025-06-12T17:05:45.632477", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "  Inner: insert=(278.26415094339626, 26.566037735849058), size=(25.169811320754715, 25.169811320754715)", "module": "qr_preview", "function": "_draw_finder_marker", "line": 162}
{"timestamp": "2025-06-12T17:05:45.632532", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Drawing marker center: style=square, color=#000000, pos=(285.37735849056605, 33.67924528301887), size=10.943396226415095", "module": "qr_preview", "function": "_draw_marker_center", "line": 179}
{"timestamp": "2025-06-12T17:05:45.632576", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Successfully added finder pattern group to SVG", "module": "qr_preview", "function": "_draw_finder_marker", "line": 171}
{"timestamp": "2025-06-12T17:05:45.632604", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Successfully drew finder pattern 2/3", "module": "qr_preview", "function": "generate_preview_svg_modular", "line": 782}
{"timestamp": "2025-06-12T17:05:45.632632", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Drawing finder pattern 3/3 at position (0, 46)", "module": "qr_preview", "function": "generate_preview_svg_modular", "line": 780}
{"timestamp": "2025-06-12T17:05:45.632663", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "_draw_finder_marker called: mx=0, my=46, module_size=5.471698113207547, border_style=square, center_style=square, border_color=#000000, center_color=#000000, offset=(20, 20)", "module": "qr_preview", "function": "_draw_finder_marker", "line": 97}
{"timestamp": "2025-06-12T17:05:45.632702", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Creating finder pattern group with id='marker-0-46'", "module": "qr_preview", "function": "_draw_finder_marker", "line": 110}
{"timestamp": "2025-06-12T17:05:45.632731", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Drawing square border:", "module": "qr_preview", "function": "_draw_finder_marker", "line": 160}
{"timestamp": "2025-06-12T17:05:45.632762", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "  Outer: insert=(20.0, 271.6981132075472), size=(38.301886792452834, 38.301886792452834)", "module": "qr_preview", "function": "_draw_finder_marker", "line": 161}
{"timestamp": "2025-06-12T17:05:45.632794", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "  Inner: insert=(26.566037735849058, 278.26415094339626), size=(25.169811320754715, 25.169811320754715)", "module": "qr_preview", "function": "_draw_finder_marker", "line": 162}
{"timestamp": "2025-06-12T17:05:45.632847", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Drawing marker center: style=square, color=#000000, pos=(33.67924528301887, 285.37735849056605), size=10.943396226415095", "module": "qr_preview", "function": "_draw_marker_center", "line": 179}
{"timestamp": "2025-06-12T17:05:45.632890", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Successfully added finder pattern group to SVG", "module": "qr_preview", "function": "_draw_finder_marker", "line": 171}
{"timestamp": "2025-06-12T17:05:45.632919", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Successfully drew finder pattern 3/3", "module": "qr_preview", "function": "generate_preview_svg_modular", "line": 782}
{"timestamp": "2025-06-12T17:05:45.632955", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "_draw_logo called with logo type: <class 'bytes'>, dimensions: 300", "module": "qr_preview", "function": "_draw_logo", "line": 434}
{"timestamp": "2025-06-12T17:05:45.962336", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Generated SVG length: 260564", "module": "qr_preview", "function": "generate_preview_svg_modular", "line": 802}
{"timestamp": "2025-06-12T17:05:45.962958", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "SVG contains marker-0-0: True", "module": "qr_preview", "function": "generate_preview_svg_modular", "line": 803}
{"timestamp": "2025-06-12T17:05:45.963107", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "SVG contains marker-18-0: False", "module": "qr_preview", "function": "generate_preview_svg_modular", "line": 804}
{"timestamp": "2025-06-12T17:05:45.963243", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "SVG contains marker-0-18: False", "module": "qr_preview", "function": "generate_preview_svg_modular", "line": 805}
{"timestamp": "2025-06-12T17:05:45.963698", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "SVG marker counts - outer: 3, inner: 3, dot: 0", "module": "qr_preview", "function": "generate_preview_svg_modular", "line": 813}
{"timestamp": "2025-06-12T17:05:45.963736", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "SVG label counts - text: 0, background: 0", "module": "qr_preview", "function": "generate_preview_svg_modular", "line": 814}
{"timestamp": "2025-06-12T17:05:45.966241", "level": "DEBUG", "logger": "backend.main", "message": "[Auth] Enforced Bearer token authentication for request", "module": "main", "function": "bearer_token_auth_middleware", "line": 274}
{"timestamp": "2025-06-12T17:05:46.800338", "level": "DEBUG", "logger": "backend.core.security.jwt_keys", "message": "Token contains key ID: 8be4a455-8974-4e97-b307-6c8428b2c9ff", "module": "jwt_keys", "function": "decode_token", "line": 188}
{"timestamp": "2025-06-12T17:05:46.800398", "level": "DEBUG", "logger": "backend.core.security.jwt_keys", "message": "Using key ID 8be4a455-8974-4e97-b307-6c8428b2c9ff for verification", "module": "jwt_keys", "function": "decode_token", "line": 195}
{"timestamp": "2025-06-12T17:05:46.800525", "level": "DEBUG", "logger": "backend.services.auth", "message": "JWT decoded successfully with key manager", "module": "auth", "function": "authenticate_token", "line": 683}
{"timestamp": "2025-06-12T17:05:46.812599", "level": "DEBUG", "logger": "backend.services.db_utils", "message": "Query 'auth_lookup_e4ae2d8f-b32f-52d7-99cd-8a33b19652c7' completed in 0.012s", "module": "db_utils", "function": "execute_query_with_timeout", "line": 68}
{"timestamp": "2025-06-12T17:05:46.812691", "level": "DEBUG", "logger": "backend.services.db_utils", "message": "Auth query 'auth_lookup_e4ae2d8f-b32f-52d7-99cd-8a33b19652c7' successful, result format preserved", "module": "db_utils", "function": "execute_query_with_timeout", "line": 72}
{"timestamp": "2025-06-12T17:05:46.816424", "level": "DEBUG", "logger": "backend.services.auth", "message": "Authentication attempt with token prefix: eyJhbGciOi...", "module": "auth", "function": "get_current_user", "line": 727}
{"timestamp": "2025-06-12T17:05:46.816538", "level": "DEBUG", "logger": "backend.core.security.jwt_keys", "message": "Token contains key ID: 8be4a455-8974-4e97-b307-6c8428b2c9ff", "module": "jwt_keys", "function": "decode_token", "line": 188}
{"timestamp": "2025-06-12T17:05:46.816587", "level": "DEBUG", "logger": "backend.core.security.jwt_keys", "message": "Using key ID 8be4a455-8974-4e97-b307-6c8428b2c9ff for verification", "module": "jwt_keys", "function": "decode_token", "line": 195}
{"timestamp": "2025-06-12T17:05:46.816711", "level": "DEBUG", "logger": "backend.services.auth", "message": "JWT decoded successfully with key manager", "module": "auth", "function": "authenticate_token", "line": 683}
{"timestamp": "2025-06-12T17:05:46.817713", "level": "DEBUG", "logger": "backend.services.db_utils", "message": "Query 'auth_lookup_e4ae2d8f-b32f-52d7-99cd-8a33b19652c7' completed in 0.001s", "module": "db_utils", "function": "execute_query_with_timeout", "line": 68}
{"timestamp": "2025-06-12T17:05:46.817792", "level": "DEBUG", "logger": "backend.services.db_utils", "message": "Auth query 'auth_lookup_e4ae2d8f-b32f-52d7-99cd-8a33b19652c7' successful, result format preserved", "module": "db_utils", "function": "execute_query_with_timeout", "line": 72}
{"timestamp": "2025-06-12T17:05:46.817844", "level": "DEBUG", "logger": "backend.services.auth", "message": "Successfully authenticated user: <EMAIL>", "module": "auth", "function": "get_current_user", "line": 732}
{"timestamp": "2025-06-12T17:05:46.817878", "level": "DEBUG", "logger": "backend.services.auth", "message": "Successfully authenticated user: <EMAIL>", "module": "auth", "function": "get_current_user", "line": 738}
{"timestamp": "2025-06-12T17:05:46.819558", "level": "DEBUG", "logger": "backend.routes.qr_preview", "message": "Received QR preview SVG request: {\"qr_type\": \"scooter_unlock\", \"data\": {\"scooter_id\": \"SCOOT123\", \"unlock_code\": \"UNLOCK42\", \"title\": \"Scooter_unlock QR Code\", \"description\": \"Scan to view scooter_unlock content\", \"service_provider\": \"Lime\"}, \"designOptions\": {\"size\": 256, \"margin\": 10, \"pattern\": \"diagonal\", \"marker_border_style\": \"square\", \"marker_border_color\": \"#000000\", \"marker_center_style\": \"square\", \"marker_center_color\": \"#000000\", \"logo_size\": 0.3, \"logo_position\": \"center\", \"logo_opacity\": 1, \"frame_style\": \"none\", \"", "module": "qr_preview", "function": "preview_qr_svg", "line": 242}
{"timestamp": "2025-06-12T17:05:46.819684", "level": "DEBUG", "logger": "backend.routes.qr_preview", "message": "Form data keys: ['request', 'logo']", "module": "qr_preview", "function": "preview_qr_svg", "line": 297}
{"timestamp": "2025-06-12T17:05:46.819741", "level": "DEBUG", "logger": "backend.routes.qr_preview", "message": "Logo file found: type=<class 'starlette.datastructures.UploadFile'>, filename=LOGO - 1080 - BLACK.png", "module": "qr_preview", "function": "preview_qr_svg", "line": 300}
{"timestamp": "2025-06-12T17:05:46.819797", "level": "DEBUG", "logger": "backend.routes.qr_preview", "message": "Logo content read: 127619 bytes", "module": "qr_preview", "function": "preview_qr_svg", "line": 305}
{"timestamp": "2025-06-12T17:05:46.819845", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Formatting content for QR type: qrtype.scooter_unlock, content keys: ['scooter_id', 'unlock_code', 'title', 'description', 'service_provider']", "module": "qr_preview", "function": "_format_qr_data", "line": 876}
{"timestamp": "2025-06-12T17:05:46.840040", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Logo remove background setting: False, has logo: True", "module": "qr_preview", "function": "generate_preview_svg_modular", "line": 727}
{"timestamp": "2025-06-12T17:05:46.840107", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Logo present but background removal disabled", "module": "qr_preview", "function": "generate_preview_svg_modular", "line": 741}
{"timestamp": "2025-06-12T17:05:46.860126", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Drawing 3 finder patterns at positions: [(0, 0), (46, 0), (0, 46)]", "module": "qr_preview", "function": "generate_preview_svg_modular", "line": 775}
{"timestamp": "2025-06-12T17:05:46.860192", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "QR size: 53, border_style: square, center_style: square", "module": "qr_preview", "function": "generate_preview_svg_modular", "line": 776}
{"timestamp": "2025-06-12T17:05:46.860236", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Drawing finder pattern 1/3 at position (0, 0)", "module": "qr_preview", "function": "generate_preview_svg_modular", "line": 780}
{"timestamp": "2025-06-12T17:05:46.860282", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "_draw_finder_marker called: mx=0, my=0, module_size=5.471698113207547, border_style=square, center_style=square, border_color=#000000, center_color=#000000, offset=(20, 20)", "module": "qr_preview", "function": "_draw_finder_marker", "line": 97}
{"timestamp": "2025-06-12T17:05:46.860387", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Creating finder pattern group with id='marker-0-0'", "module": "qr_preview", "function": "_draw_finder_marker", "line": 110}
{"timestamp": "2025-06-12T17:05:46.860432", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Drawing square border:", "module": "qr_preview", "function": "_draw_finder_marker", "line": 160}
{"timestamp": "2025-06-12T17:05:46.860475", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "  Outer: insert=(20.0, 20.0), size=(38.301886792452834, 38.301886792452834)", "module": "qr_preview", "function": "_draw_finder_marker", "line": 161}
{"timestamp": "2025-06-12T17:05:46.860517", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "  Inner: insert=(26.566037735849058, 26.566037735849058), size=(25.169811320754715, 25.169811320754715)", "module": "qr_preview", "function": "_draw_finder_marker", "line": 162}
{"timestamp": "2025-06-12T17:05:46.860587", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Drawing marker center: style=square, color=#000000, pos=(33.67924528301887, 33.67924528301887), size=10.943396226415095", "module": "qr_preview", "function": "_draw_marker_center", "line": 179}
{"timestamp": "2025-06-12T17:05:46.860642", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Successfully added finder pattern group to SVG", "module": "qr_preview", "function": "_draw_finder_marker", "line": 171}
{"timestamp": "2025-06-12T17:05:46.860681", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Successfully drew finder pattern 1/3", "module": "qr_preview", "function": "generate_preview_svg_modular", "line": 782}
{"timestamp": "2025-06-12T17:05:46.860718", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Drawing finder pattern 2/3 at position (46, 0)", "module": "qr_preview", "function": "generate_preview_svg_modular", "line": 780}
{"timestamp": "2025-06-12T17:05:46.860757", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "_draw_finder_marker called: mx=46, my=0, module_size=5.471698113207547, border_style=square, center_style=square, border_color=#000000, center_color=#000000, offset=(20, 20)", "module": "qr_preview", "function": "_draw_finder_marker", "line": 97}
{"timestamp": "2025-06-12T17:05:46.860802", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Creating finder pattern group with id='marker-46-0'", "module": "qr_preview", "function": "_draw_finder_marker", "line": 110}
{"timestamp": "2025-06-12T17:05:46.860844", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Drawing square border:", "module": "qr_preview", "function": "_draw_finder_marker", "line": 160}
{"timestamp": "2025-06-12T17:05:46.860885", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "  Outer: insert=(271.6981132075472, 20.0), size=(38.301886792452834, 38.301886792452834)", "module": "qr_preview", "function": "_draw_finder_marker", "line": 161}
{"timestamp": "2025-06-12T17:05:46.860922", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "  Inner: insert=(278.26415094339626, 26.566037735849058), size=(25.169811320754715, 25.169811320754715)", "module": "qr_preview", "function": "_draw_finder_marker", "line": 162}
{"timestamp": "2025-06-12T17:05:46.860984", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Drawing marker center: style=square, color=#000000, pos=(285.37735849056605, 33.67924528301887), size=10.943396226415095", "module": "qr_preview", "function": "_draw_marker_center", "line": 179}
{"timestamp": "2025-06-12T17:05:46.861033", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Successfully added finder pattern group to SVG", "module": "qr_preview", "function": "_draw_finder_marker", "line": 171}
{"timestamp": "2025-06-12T17:05:46.861072", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Successfully drew finder pattern 2/3", "module": "qr_preview", "function": "generate_preview_svg_modular", "line": 782}
{"timestamp": "2025-06-12T17:05:46.861125", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Drawing finder pattern 3/3 at position (0, 46)", "module": "qr_preview", "function": "generate_preview_svg_modular", "line": 780}
{"timestamp": "2025-06-12T17:05:46.861162", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "_draw_finder_marker called: mx=0, my=46, module_size=5.471698113207547, border_style=square, center_style=square, border_color=#000000, center_color=#000000, offset=(20, 20)", "module": "qr_preview", "function": "_draw_finder_marker", "line": 97}
{"timestamp": "2025-06-12T17:05:46.861206", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Creating finder pattern group with id='marker-0-46'", "module": "qr_preview", "function": "_draw_finder_marker", "line": 110}
{"timestamp": "2025-06-12T17:05:46.861242", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Drawing square border:", "module": "qr_preview", "function": "_draw_finder_marker", "line": 160}
{"timestamp": "2025-06-12T17:05:46.861274", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "  Outer: insert=(20.0, 271.6981132075472), size=(38.301886792452834, 38.301886792452834)", "module": "qr_preview", "function": "_draw_finder_marker", "line": 161}
{"timestamp": "2025-06-12T17:05:46.861312", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "  Inner: insert=(26.566037735849058, 278.26415094339626), size=(25.169811320754715, 25.169811320754715)", "module": "qr_preview", "function": "_draw_finder_marker", "line": 162}
{"timestamp": "2025-06-12T17:05:46.861370", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Drawing marker center: style=square, color=#000000, pos=(33.67924528301887, 285.37735849056605), size=10.943396226415095", "module": "qr_preview", "function": "_draw_marker_center", "line": 179}
{"timestamp": "2025-06-12T17:05:46.861425", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Successfully added finder pattern group to SVG", "module": "qr_preview", "function": "_draw_finder_marker", "line": 171}
{"timestamp": "2025-06-12T17:05:46.861538", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Successfully drew finder pattern 3/3", "module": "qr_preview", "function": "generate_preview_svg_modular", "line": 782}
{"timestamp": "2025-06-12T17:05:46.861622", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "_draw_logo called with logo type: <class 'bytes'>, dimensions: 300", "module": "qr_preview", "function": "_draw_logo", "line": 434}
{"timestamp": "2025-06-12T17:05:47.193284", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Generated SVG length: 283664", "module": "qr_preview", "function": "generate_preview_svg_modular", "line": 802}
{"timestamp": "2025-06-12T17:05:47.193473", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "SVG contains marker-0-0: True", "module": "qr_preview", "function": "generate_preview_svg_modular", "line": 803}
{"timestamp": "2025-06-12T17:05:47.193624", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "SVG contains marker-18-0: False", "module": "qr_preview", "function": "generate_preview_svg_modular", "line": 804}
{"timestamp": "2025-06-12T17:05:47.193771", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "SVG contains marker-0-18: False", "module": "qr_preview", "function": "generate_preview_svg_modular", "line": 805}
{"timestamp": "2025-06-12T17:05:47.194239", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "SVG marker counts - outer: 3, inner: 3, dot: 0", "module": "qr_preview", "function": "generate_preview_svg_modular", "line": 813}
{"timestamp": "2025-06-12T17:05:47.194275", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "SVG label counts - text: 0, background: 0", "module": "qr_preview", "function": "generate_preview_svg_modular", "line": 814}
{"timestamp": "2025-06-12T17:05:47.196723", "level": "DEBUG", "logger": "backend.main", "message": "[Auth] Enforced Bearer token authentication for request", "module": "main", "function": "bearer_token_auth_middleware", "line": 274}
{"timestamp": "2025-06-12T17:05:58.445856", "level": "DEBUG", "logger": "backend.core.security.jwt_keys", "message": "Token contains key ID: 8be4a455-8974-4e97-b307-6c8428b2c9ff", "module": "jwt_keys", "function": "decode_token", "line": 188}
{"timestamp": "2025-06-12T17:05:58.446304", "level": "DEBUG", "logger": "backend.core.security.jwt_keys", "message": "Using key ID 8be4a455-8974-4e97-b307-6c8428b2c9ff for verification", "module": "jwt_keys", "function": "decode_token", "line": 195}
{"timestamp": "2025-06-12T17:05:58.446842", "level": "DEBUG", "logger": "backend.services.auth", "message": "JWT decoded successfully with key manager", "module": "auth", "function": "authenticate_token", "line": 683}
{"timestamp": "2025-06-12T17:05:58.454197", "level": "DEBUG", "logger": "backend.services.db_utils", "message": "Query 'auth_lookup_e4ae2d8f-b32f-52d7-99cd-8a33b19652c7' completed in 0.006s", "module": "db_utils", "function": "execute_query_with_timeout", "line": 68}
{"timestamp": "2025-06-12T17:05:58.454261", "level": "DEBUG", "logger": "backend.services.db_utils", "message": "Auth query 'auth_lookup_e4ae2d8f-b32f-52d7-99cd-8a33b19652c7' successful, result format preserved", "module": "db_utils", "function": "execute_query_with_timeout", "line": 72}
{"timestamp": "2025-06-12T17:05:58.471406", "level": "DEBUG", "logger": "backend.services.auth", "message": "Authentication attempt with token prefix: eyJhbGciOi...", "module": "auth", "function": "get_current_user", "line": 727}
{"timestamp": "2025-06-12T17:05:58.471529", "level": "DEBUG", "logger": "backend.core.security.jwt_keys", "message": "Token contains key ID: 8be4a455-8974-4e97-b307-6c8428b2c9ff", "module": "jwt_keys", "function": "decode_token", "line": 188}
{"timestamp": "2025-06-12T17:05:58.471571", "level": "DEBUG", "logger": "backend.core.security.jwt_keys", "message": "Using key ID 8be4a455-8974-4e97-b307-6c8428b2c9ff for verification", "module": "jwt_keys", "function": "decode_token", "line": 195}
{"timestamp": "2025-06-12T17:05:58.471724", "level": "DEBUG", "logger": "backend.services.auth", "message": "JWT decoded successfully with key manager", "module": "auth", "function": "authenticate_token", "line": 683}
{"timestamp": "2025-06-12T17:05:58.474011", "level": "DEBUG", "logger": "backend.services.db_utils", "message": "Query 'auth_lookup_e4ae2d8f-b32f-52d7-99cd-8a33b19652c7' completed in 0.002s", "module": "db_utils", "function": "execute_query_with_timeout", "line": 68}
{"timestamp": "2025-06-12T17:05:58.474071", "level": "DEBUG", "logger": "backend.services.db_utils", "message": "Auth query 'auth_lookup_e4ae2d8f-b32f-52d7-99cd-8a33b19652c7' successful, result format preserved", "module": "db_utils", "function": "execute_query_with_timeout", "line": 72}
{"timestamp": "2025-06-12T17:05:58.474139", "level": "DEBUG", "logger": "backend.services.auth", "message": "Successfully authenticated user: <EMAIL>", "module": "auth", "function": "get_current_user", "line": 732}
{"timestamp": "2025-06-12T17:05:58.474202", "level": "DEBUG", "logger": "backend.services.auth", "message": "Successfully authenticated user: <EMAIL>", "module": "auth", "function": "get_current_user", "line": 738}
{"timestamp": "2025-06-12T17:05:58.478903", "level": "DEBUG", "logger": "backend.routes.qr_preview", "message": "Received QR preview SVG request: {\"qr_type\": \"scooter_unlock\", \"data\": {\"scooter_id\": \"SCOOT123\", \"unlock_code\": \"UNLOCK42\", \"title\": \"Scooter_unlock QR Code\", \"description\": \"Scan to view scooter_unlock content\", \"service_provider\": \"Lime\"}, \"designOptions\": {\"size\": 256, \"margin\": 10, \"pattern\": \"blocks\", \"marker_border_style\": \"square\", \"marker_center_style\": \"square\", \"logo_size\": 0.3, \"logo_position\": \"center\", \"logo_opacity\": 1, \"frame_style\": \"none\", \"frame_font\": \"SF Pro\", \"frame_font_size\": 16, \"foreground_color\": \"#00", "module": "qr_preview", "function": "preview_qr_svg", "line": 242}
{"timestamp": "2025-06-12T17:05:58.479205", "level": "DEBUG", "logger": "backend.routes.qr_preview", "message": "Form data keys: ['request', 'logo']", "module": "qr_preview", "function": "preview_qr_svg", "line": 297}
{"timestamp": "2025-06-12T17:05:58.479251", "level": "DEBUG", "logger": "backend.routes.qr_preview", "message": "Logo file found: type=<class 'starlette.datastructures.UploadFile'>, filename=LOGO - 1080 - BLACK.png", "module": "qr_preview", "function": "preview_qr_svg", "line": 300}
{"timestamp": "2025-06-12T17:05:58.479293", "level": "DEBUG", "logger": "backend.routes.qr_preview", "message": "Logo content read: 127619 bytes", "module": "qr_preview", "function": "preview_qr_svg", "line": 305}
{"timestamp": "2025-06-12T17:05:58.480623", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Formatting content for QR type: qrtype.scooter_unlock, content keys: ['scooter_id', 'unlock_code', 'title', 'description', 'service_provider']", "module": "qr_preview", "function": "_format_qr_data", "line": 876}
{"timestamp": "2025-06-12T17:05:58.500061", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Logo remove background setting: True, has logo: True", "module": "qr_preview", "function": "generate_preview_svg_modular", "line": 727}
{"timestamp": "2025-06-12T17:05:58.500116", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Logo area calculated: (19, 19, 34, 34), size_fraction: 0.3, size_modules: 15", "module": "qr_preview", "function": "generate_preview_svg_modular", "line": 739}
{"timestamp": "2025-06-12T17:05:58.514077", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Skipped 106 modules out of 1380 total modules for logo background removal", "module": "qr_preview", "function": "generate_preview_svg_modular", "line": 766}
{"timestamp": "2025-06-12T17:05:58.514150", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Drawing 3 finder patterns at positions: [(0, 0), (46, 0), (0, 46)]", "module": "qr_preview", "function": "generate_preview_svg_modular", "line": 775}
{"timestamp": "2025-06-12T17:05:58.514188", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "QR size: 53, border_style: square, center_style: square", "module": "qr_preview", "function": "generate_preview_svg_modular", "line": 776}
{"timestamp": "2025-06-12T17:05:58.514224", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Drawing finder pattern 1/3 at position (0, 0)", "module": "qr_preview", "function": "generate_preview_svg_modular", "line": 780}
{"timestamp": "2025-06-12T17:05:58.514279", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "_draw_finder_marker called: mx=0, my=0, module_size=5.471698113207547, border_style=square, center_style=square, border_color=#000000, center_color=#000000, offset=(20, 20)", "module": "qr_preview", "function": "_draw_finder_marker", "line": 97}
{"timestamp": "2025-06-12T17:05:58.514504", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Creating finder pattern group with id='marker-0-0'", "module": "qr_preview", "function": "_draw_finder_marker", "line": 110}
{"timestamp": "2025-06-12T17:05:58.514555", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Drawing square border:", "module": "qr_preview", "function": "_draw_finder_marker", "line": 160}
{"timestamp": "2025-06-12T17:05:58.514599", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "  Outer: insert=(20.0, 20.0), size=(38.301886792452834, 38.301886792452834)", "module": "qr_preview", "function": "_draw_finder_marker", "line": 161}
{"timestamp": "2025-06-12T17:05:58.514636", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "  Inner: insert=(26.566037735849058, 26.566037735849058), size=(25.169811320754715, 25.169811320754715)", "module": "qr_preview", "function": "_draw_finder_marker", "line": 162}
{"timestamp": "2025-06-12T17:05:58.515012", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Drawing marker center: style=square, color=#000000, pos=(33.67924528301887, 33.67924528301887), size=10.943396226415095", "module": "qr_preview", "function": "_draw_marker_center", "line": 179}
{"timestamp": "2025-06-12T17:05:58.515079", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Successfully added finder pattern group to SVG", "module": "qr_preview", "function": "_draw_finder_marker", "line": 171}
{"timestamp": "2025-06-12T17:05:58.515118", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Successfully drew finder pattern 1/3", "module": "qr_preview", "function": "generate_preview_svg_modular", "line": 782}
{"timestamp": "2025-06-12T17:05:58.515150", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Drawing finder pattern 2/3 at position (46, 0)", "module": "qr_preview", "function": "generate_preview_svg_modular", "line": 780}
{"timestamp": "2025-06-12T17:05:58.515236", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "_draw_finder_marker called: mx=46, my=0, module_size=5.471698113207547, border_style=square, center_style=square, border_color=#000000, center_color=#000000, offset=(20, 20)", "module": "qr_preview", "function": "_draw_finder_marker", "line": 97}
{"timestamp": "2025-06-12T17:05:58.515278", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Creating finder pattern group with id='marker-46-0'", "module": "qr_preview", "function": "_draw_finder_marker", "line": 110}
{"timestamp": "2025-06-12T17:05:58.515322", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Drawing square border:", "module": "qr_preview", "function": "_draw_finder_marker", "line": 160}
{"timestamp": "2025-06-12T17:05:58.515357", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "  Outer: insert=(271.6981132075472, 20.0), size=(38.301886792452834, 38.301886792452834)", "module": "qr_preview", "function": "_draw_finder_marker", "line": 161}
{"timestamp": "2025-06-12T17:05:58.515388", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "  Inner: insert=(278.26415094339626, 26.566037735849058), size=(25.169811320754715, 25.169811320754715)", "module": "qr_preview", "function": "_draw_finder_marker", "line": 162}
{"timestamp": "2025-06-12T17:05:58.515445", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Drawing marker center: style=square, color=#000000, pos=(285.37735849056605, 33.67924528301887), size=10.943396226415095", "module": "qr_preview", "function": "_draw_marker_center", "line": 179}
{"timestamp": "2025-06-12T17:05:58.515494", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Successfully added finder pattern group to SVG", "module": "qr_preview", "function": "_draw_finder_marker", "line": 171}
{"timestamp": "2025-06-12T17:05:58.515630", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Successfully drew finder pattern 2/3", "module": "qr_preview", "function": "generate_preview_svg_modular", "line": 782}
{"timestamp": "2025-06-12T17:05:58.515665", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Drawing finder pattern 3/3 at position (0, 46)", "module": "qr_preview", "function": "generate_preview_svg_modular", "line": 780}
{"timestamp": "2025-06-12T17:05:58.515698", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "_draw_finder_marker called: mx=0, my=46, module_size=5.471698113207547, border_style=square, center_style=square, border_color=#000000, center_color=#000000, offset=(20, 20)", "module": "qr_preview", "function": "_draw_finder_marker", "line": 97}
{"timestamp": "2025-06-12T17:05:58.515745", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Creating finder pattern group with id='marker-0-46'", "module": "qr_preview", "function": "_draw_finder_marker", "line": 110}
{"timestamp": "2025-06-12T17:05:58.515802", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Drawing square border:", "module": "qr_preview", "function": "_draw_finder_marker", "line": 160}
{"timestamp": "2025-06-12T17:05:58.515888", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "  Outer: insert=(20.0, 271.6981132075472), size=(38.301886792452834, 38.301886792452834)", "module": "qr_preview", "function": "_draw_finder_marker", "line": 161}
{"timestamp": "2025-06-12T17:05:58.515925", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "  Inner: insert=(26.566037735849058, 278.26415094339626), size=(25.169811320754715, 25.169811320754715)", "module": "qr_preview", "function": "_draw_finder_marker", "line": 162}
{"timestamp": "2025-06-12T17:05:58.515993", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Drawing marker center: style=square, color=#000000, pos=(33.67924528301887, 285.37735849056605), size=10.943396226415095", "module": "qr_preview", "function": "_draw_marker_center", "line": 179}
{"timestamp": "2025-06-12T17:05:58.516041", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Successfully added finder pattern group to SVG", "module": "qr_preview", "function": "_draw_finder_marker", "line": 171}
{"timestamp": "2025-06-12T17:05:58.516076", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Successfully drew finder pattern 3/3", "module": "qr_preview", "function": "generate_preview_svg_modular", "line": 782}
{"timestamp": "2025-06-12T17:05:58.516518", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "_draw_logo called with logo type: <class 'bytes'>, dimensions: 300", "module": "qr_preview", "function": "_draw_logo", "line": 434}
{"timestamp": "2025-06-12T17:05:58.862922", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Generated SVG length: 189231", "module": "qr_preview", "function": "generate_preview_svg_modular", "line": 802}
{"timestamp": "2025-06-12T17:05:58.863070", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "SVG contains marker-0-0: True", "module": "qr_preview", "function": "generate_preview_svg_modular", "line": 803}
{"timestamp": "2025-06-12T17:05:58.863192", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "SVG contains marker-18-0: False", "module": "qr_preview", "function": "generate_preview_svg_modular", "line": 804}
{"timestamp": "2025-06-12T17:05:58.863310", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "SVG contains marker-0-18: False", "module": "qr_preview", "function": "generate_preview_svg_modular", "line": 805}
{"timestamp": "2025-06-12T17:05:58.863627", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "SVG marker counts - outer: 3, inner: 3, dot: 0", "module": "qr_preview", "function": "generate_preview_svg_modular", "line": 813}
{"timestamp": "2025-06-12T17:05:58.863660", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "SVG label counts - text: 0, background: 0", "module": "qr_preview", "function": "generate_preview_svg_modular", "line": 814}
{"timestamp": "2025-06-12T17:05:58.865812", "level": "DEBUG", "logger": "backend.main", "message": "[Auth] Enforced Bearer token authentication for request", "module": "main", "function": "bearer_token_auth_middleware", "line": 274}
{"timestamp": "2025-06-12T17:05:59.715063", "level": "DEBUG", "logger": "backend.core.security.jwt_keys", "message": "Token contains key ID: 8be4a455-8974-4e97-b307-6c8428b2c9ff", "module": "jwt_keys", "function": "decode_token", "line": 188}
{"timestamp": "2025-06-12T17:05:59.715123", "level": "DEBUG", "logger": "backend.core.security.jwt_keys", "message": "Using key ID 8be4a455-8974-4e97-b307-6c8428b2c9ff for verification", "module": "jwt_keys", "function": "decode_token", "line": 195}
{"timestamp": "2025-06-12T17:05:59.715304", "level": "DEBUG", "logger": "backend.services.auth", "message": "JWT decoded successfully with key manager", "module": "auth", "function": "authenticate_token", "line": 683}
{"timestamp": "2025-06-12T17:05:59.716777", "level": "DEBUG", "logger": "backend.services.db_utils", "message": "Query 'auth_lookup_e4ae2d8f-b32f-52d7-99cd-8a33b19652c7' completed in 0.001s", "module": "db_utils", "function": "execute_query_with_timeout", "line": 68}
{"timestamp": "2025-06-12T17:05:59.716820", "level": "DEBUG", "logger": "backend.services.db_utils", "message": "Auth query 'auth_lookup_e4ae2d8f-b32f-52d7-99cd-8a33b19652c7' successful, result format preserved", "module": "db_utils", "function": "execute_query_with_timeout", "line": 72}
{"timestamp": "2025-06-12T17:05:59.720302", "level": "DEBUG", "logger": "backend.services.auth", "message": "Authentication attempt with token prefix: eyJhbGciOi...", "module": "auth", "function": "get_current_user", "line": 727}
{"timestamp": "2025-06-12T17:05:59.720416", "level": "DEBUG", "logger": "backend.core.security.jwt_keys", "message": "Token contains key ID: 8be4a455-8974-4e97-b307-6c8428b2c9ff", "module": "jwt_keys", "function": "decode_token", "line": 188}
{"timestamp": "2025-06-12T17:05:59.720455", "level": "DEBUG", "logger": "backend.core.security.jwt_keys", "message": "Using key ID 8be4a455-8974-4e97-b307-6c8428b2c9ff for verification", "module": "jwt_keys", "function": "decode_token", "line": 195}
{"timestamp": "2025-06-12T17:05:59.720583", "level": "DEBUG", "logger": "backend.services.auth", "message": "JWT decoded successfully with key manager", "module": "auth", "function": "authenticate_token", "line": 683}
{"timestamp": "2025-06-12T17:05:59.722864", "level": "DEBUG", "logger": "backend.services.db_utils", "message": "Query 'auth_lookup_e4ae2d8f-b32f-52d7-99cd-8a33b19652c7' completed in 0.002s", "module": "db_utils", "function": "execute_query_with_timeout", "line": 68}
{"timestamp": "2025-06-12T17:05:59.722917", "level": "DEBUG", "logger": "backend.services.db_utils", "message": "Auth query 'auth_lookup_e4ae2d8f-b32f-52d7-99cd-8a33b19652c7' successful, result format preserved", "module": "db_utils", "function": "execute_query_with_timeout", "line": 72}
{"timestamp": "2025-06-12T17:05:59.722979", "level": "DEBUG", "logger": "backend.services.auth", "message": "Successfully authenticated user: <EMAIL>", "module": "auth", "function": "get_current_user", "line": 732}
{"timestamp": "2025-06-12T17:05:59.723100", "level": "DEBUG", "logger": "backend.services.auth", "message": "Successfully authenticated user: <EMAIL>", "module": "auth", "function": "get_current_user", "line": 738}
{"timestamp": "2025-06-12T17:05:59.724910", "level": "DEBUG", "logger": "backend.routes.qr_preview", "message": "Received QR preview SVG request: {\"qr_type\": \"scooter_unlock\", \"data\": {\"scooter_id\": \"SCOOT123\", \"unlock_code\": \"UNLOCK42\", \"title\": \"Scooter_unlock QR Code\", \"description\": \"Scan to view scooter_unlock content\", \"service_provider\": \"Lime\"}, \"designOptions\": {\"size\": 256, \"margin\": 10, \"pattern\": \"blocks\", \"marker_border_style\": \"square\", \"marker_border_color\": \"#000000\", \"marker_center_style\": \"square\", \"marker_center_color\": \"#000000\", \"logo_size\": 0.3, \"logo_position\": \"center\", \"logo_opacity\": 1, \"frame_style\": \"none\", \"fr", "module": "qr_preview", "function": "preview_qr_svg", "line": 242}
{"timestamp": "2025-06-12T17:05:59.725006", "level": "DEBUG", "logger": "backend.routes.qr_preview", "message": "Form data keys: ['request', 'logo']", "module": "qr_preview", "function": "preview_qr_svg", "line": 297}
{"timestamp": "2025-06-12T17:05:59.725885", "level": "DEBUG", "logger": "backend.routes.qr_preview", "message": "Logo file found: type=<class 'starlette.datastructures.UploadFile'>, filename=LOGO - 1080 - BLACK.png", "module": "qr_preview", "function": "preview_qr_svg", "line": 300}
{"timestamp": "2025-06-12T17:05:59.725952", "level": "DEBUG", "logger": "backend.routes.qr_preview", "message": "Logo content read: 127619 bytes", "module": "qr_preview", "function": "preview_qr_svg", "line": 305}
{"timestamp": "2025-06-12T17:05:59.726205", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Formatting content for QR type: qrtype.scooter_unlock, content keys: ['scooter_id', 'unlock_code', 'title', 'description', 'service_provider']", "module": "qr_preview", "function": "_format_qr_data", "line": 876}
{"timestamp": "2025-06-12T17:05:59.745076", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Logo remove background setting: False, has logo: True", "module": "qr_preview", "function": "generate_preview_svg_modular", "line": 727}
{"timestamp": "2025-06-12T17:05:59.745123", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Logo present but background removal disabled", "module": "qr_preview", "function": "generate_preview_svg_modular", "line": 741}
{"timestamp": "2025-06-12T17:05:59.760324", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Drawing 3 finder patterns at positions: [(0, 0), (46, 0), (0, 46)]", "module": "qr_preview", "function": "generate_preview_svg_modular", "line": 775}
{"timestamp": "2025-06-12T17:05:59.760377", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "QR size: 53, border_style: square, center_style: square", "module": "qr_preview", "function": "generate_preview_svg_modular", "line": 776}
{"timestamp": "2025-06-12T17:05:59.760413", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Drawing finder pattern 1/3 at position (0, 0)", "module": "qr_preview", "function": "generate_preview_svg_modular", "line": 780}
{"timestamp": "2025-06-12T17:05:59.763953", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "_draw_finder_marker called: mx=0, my=0, module_size=5.471698113207547, border_style=square, center_style=square, border_color=#000000, center_color=#000000, offset=(20, 20)", "module": "qr_preview", "function": "_draw_finder_marker", "line": 97}
{"timestamp": "2025-06-12T17:05:59.764068", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Creating finder pattern group with id='marker-0-0'", "module": "qr_preview", "function": "_draw_finder_marker", "line": 110}
{"timestamp": "2025-06-12T17:05:59.764110", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Drawing square border:", "module": "qr_preview", "function": "_draw_finder_marker", "line": 160}
{"timestamp": "2025-06-12T17:05:59.764155", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "  Outer: insert=(20.0, 20.0), size=(38.301886792452834, 38.301886792452834)", "module": "qr_preview", "function": "_draw_finder_marker", "line": 161}
{"timestamp": "2025-06-12T17:05:59.764194", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "  Inner: insert=(26.566037735849058, 26.566037735849058), size=(25.169811320754715, 25.169811320754715)", "module": "qr_preview", "function": "_draw_finder_marker", "line": 162}
{"timestamp": "2025-06-12T17:05:59.764613", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Drawing marker center: style=square, color=#000000, pos=(33.67924528301887, 33.67924528301887), size=10.943396226415095", "module": "qr_preview", "function": "_draw_marker_center", "line": 179}
{"timestamp": "2025-06-12T17:05:59.764710", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Successfully added finder pattern group to SVG", "module": "qr_preview", "function": "_draw_finder_marker", "line": 171}
{"timestamp": "2025-06-12T17:05:59.764749", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Successfully drew finder pattern 1/3", "module": "qr_preview", "function": "generate_preview_svg_modular", "line": 782}
{"timestamp": "2025-06-12T17:05:59.764785", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Drawing finder pattern 2/3 at position (46, 0)", "module": "qr_preview", "function": "generate_preview_svg_modular", "line": 780}
{"timestamp": "2025-06-12T17:05:59.764850", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "_draw_finder_marker called: mx=46, my=0, module_size=5.471698113207547, border_style=square, center_style=square, border_color=#000000, center_color=#000000, offset=(20, 20)", "module": "qr_preview", "function": "_draw_finder_marker", "line": 97}
{"timestamp": "2025-06-12T17:05:59.764895", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Creating finder pattern group with id='marker-46-0'", "module": "qr_preview", "function": "_draw_finder_marker", "line": 110}
{"timestamp": "2025-06-12T17:05:59.764927", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Drawing square border:", "module": "qr_preview", "function": "_draw_finder_marker", "line": 160}
{"timestamp": "2025-06-12T17:05:59.764962", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "  Outer: insert=(271.6981132075472, 20.0), size=(38.301886792452834, 38.301886792452834)", "module": "qr_preview", "function": "_draw_finder_marker", "line": 161}
{"timestamp": "2025-06-12T17:05:59.764996", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "  Inner: insert=(278.26415094339626, 26.566037735849058), size=(25.169811320754715, 25.169811320754715)", "module": "qr_preview", "function": "_draw_finder_marker", "line": 162}
{"timestamp": "2025-06-12T17:05:59.765058", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Drawing marker center: style=square, color=#000000, pos=(285.37735849056605, 33.67924528301887), size=10.943396226415095", "module": "qr_preview", "function": "_draw_marker_center", "line": 179}
{"timestamp": "2025-06-12T17:05:59.765104", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Successfully added finder pattern group to SVG", "module": "qr_preview", "function": "_draw_finder_marker", "line": 171}
{"timestamp": "2025-06-12T17:05:59.765137", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Successfully drew finder pattern 2/3", "module": "qr_preview", "function": "generate_preview_svg_modular", "line": 782}
{"timestamp": "2025-06-12T17:05:59.765169", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Drawing finder pattern 3/3 at position (0, 46)", "module": "qr_preview", "function": "generate_preview_svg_modular", "line": 780}
{"timestamp": "2025-06-12T17:05:59.765201", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "_draw_finder_marker called: mx=0, my=46, module_size=5.471698113207547, border_style=square, center_style=square, border_color=#000000, center_color=#000000, offset=(20, 20)", "module": "qr_preview", "function": "_draw_finder_marker", "line": 97}
{"timestamp": "2025-06-12T17:05:59.765240", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Creating finder pattern group with id='marker-0-46'", "module": "qr_preview", "function": "_draw_finder_marker", "line": 110}
{"timestamp": "2025-06-12T17:05:59.765268", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Drawing square border:", "module": "qr_preview", "function": "_draw_finder_marker", "line": 160}
{"timestamp": "2025-06-12T17:05:59.765303", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "  Outer: insert=(20.0, 271.6981132075472), size=(38.301886792452834, 38.301886792452834)", "module": "qr_preview", "function": "_draw_finder_marker", "line": 161}
{"timestamp": "2025-06-12T17:05:59.765338", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "  Inner: insert=(26.566037735849058, 278.26415094339626), size=(25.169811320754715, 25.169811320754715)", "module": "qr_preview", "function": "_draw_finder_marker", "line": 162}
{"timestamp": "2025-06-12T17:05:59.765400", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Drawing marker center: style=square, color=#000000, pos=(33.67924528301887, 285.37735849056605), size=10.943396226415095", "module": "qr_preview", "function": "_draw_marker_center", "line": 179}
{"timestamp": "2025-06-12T17:05:59.765467", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Successfully added finder pattern group to SVG", "module": "qr_preview", "function": "_draw_finder_marker", "line": 171}
{"timestamp": "2025-06-12T17:05:59.765500", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Successfully drew finder pattern 3/3", "module": "qr_preview", "function": "generate_preview_svg_modular", "line": 782}
{"timestamp": "2025-06-12T17:05:59.765545", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "_draw_logo called with logo type: <class 'bytes'>, dimensions: 300", "module": "qr_preview", "function": "_draw_logo", "line": 434}
{"timestamp": "2025-06-12T17:06:00.088837", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Generated SVG length: 205914", "module": "qr_preview", "function": "generate_preview_svg_modular", "line": 802}
{"timestamp": "2025-06-12T17:06:00.088987", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "SVG contains marker-0-0: True", "module": "qr_preview", "function": "generate_preview_svg_modular", "line": 803}
{"timestamp": "2025-06-12T17:06:00.089111", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "SVG contains marker-18-0: False", "module": "qr_preview", "function": "generate_preview_svg_modular", "line": 804}
{"timestamp": "2025-06-12T17:06:00.089278", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "SVG contains marker-0-18: False", "module": "qr_preview", "function": "generate_preview_svg_modular", "line": 805}
{"timestamp": "2025-06-12T17:06:00.089615", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "SVG marker counts - outer: 3, inner: 3, dot: 0", "module": "qr_preview", "function": "generate_preview_svg_modular", "line": 813}
{"timestamp": "2025-06-12T17:06:00.089648", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "SVG label counts - text: 0, background: 0", "module": "qr_preview", "function": "generate_preview_svg_modular", "line": 814}
{"timestamp": "2025-06-12T17:06:00.091477", "level": "DEBUG", "logger": "backend.main", "message": "[Auth] Enforced Bearer token authentication for request", "module": "main", "function": "bearer_token_auth_middleware", "line": 274}
{"timestamp": "2025-06-12T17:06:13.422569", "level": "DEBUG", "logger": "backend.main", "message": "[Auth] Enforced Bearer token authentication for request", "module": "main", "function": "bearer_token_auth_middleware", "line": 274}
{"timestamp": "2025-06-12T17:06:13.869596", "level": "DEBUG", "logger": "backend.core.security.jwt_keys", "message": "Token contains key ID: 8be4a455-8974-4e97-b307-6c8428b2c9ff", "module": "jwt_keys", "function": "decode_token", "line": 188}
{"timestamp": "2025-06-12T17:06:13.869678", "level": "DEBUG", "logger": "backend.core.security.jwt_keys", "message": "Using key ID 8be4a455-8974-4e97-b307-6c8428b2c9ff for verification", "module": "jwt_keys", "function": "decode_token", "line": 195}
{"timestamp": "2025-06-12T17:06:13.870173", "level": "DEBUG", "logger": "backend.services.auth", "message": "JWT decoded successfully with key manager", "module": "auth", "function": "authenticate_token", "line": 683}
{"timestamp": "2025-06-12T17:06:13.909801", "level": "DEBUG", "logger": "backend.services.db_utils", "message": "Query 'auth_lookup_e4ae2d8f-b32f-52d7-99cd-8a33b19652c7' completed in 0.038s", "module": "db_utils", "function": "execute_query_with_timeout", "line": 68}
{"timestamp": "2025-06-12T17:06:13.910335", "level": "DEBUG", "logger": "backend.services.db_utils", "message": "Auth query 'auth_lookup_e4ae2d8f-b32f-52d7-99cd-8a33b19652c7' successful, result format preserved", "module": "db_utils", "function": "execute_query_with_timeout", "line": 72}
{"timestamp": "2025-06-12T17:06:13.925215", "level": "DEBUG", "logger": "backend.services.auth", "message": "Authentication attempt with token prefix: eyJhbGciOi...", "module": "auth", "function": "get_current_user", "line": 727}
{"timestamp": "2025-06-12T17:06:13.925403", "level": "DEBUG", "logger": "backend.core.security.jwt_keys", "message": "Token contains key ID: 8be4a455-8974-4e97-b307-6c8428b2c9ff", "module": "jwt_keys", "function": "decode_token", "line": 188}
{"timestamp": "2025-06-12T17:06:13.925454", "level": "DEBUG", "logger": "backend.core.security.jwt_keys", "message": "Using key ID 8be4a455-8974-4e97-b307-6c8428b2c9ff for verification", "module": "jwt_keys", "function": "decode_token", "line": 195}
{"timestamp": "2025-06-12T17:06:13.925605", "level": "DEBUG", "logger": "backend.services.auth", "message": "JWT decoded successfully with key manager", "module": "auth", "function": "authenticate_token", "line": 683}
{"timestamp": "2025-06-12T17:06:13.932928", "level": "DEBUG", "logger": "backend.services.db_utils", "message": "Query 'auth_lookup_e4ae2d8f-b32f-52d7-99cd-8a33b19652c7' completed in 0.004s", "module": "db_utils", "function": "execute_query_with_timeout", "line": 68}
{"timestamp": "2025-06-12T17:06:13.933032", "level": "DEBUG", "logger": "backend.services.db_utils", "message": "Auth query 'auth_lookup_e4ae2d8f-b32f-52d7-99cd-8a33b19652c7' successful, result format preserved", "module": "db_utils", "function": "execute_query_with_timeout", "line": 72}
{"timestamp": "2025-06-12T17:06:13.933135", "level": "DEBUG", "logger": "backend.services.auth", "message": "Successfully authenticated user: <EMAIL>", "module": "auth", "function": "get_current_user", "line": 732}
{"timestamp": "2025-06-12T17:06:13.933197", "level": "DEBUG", "logger": "backend.services.auth", "message": "Successfully authenticated user: <EMAIL>", "module": "auth", "function": "get_current_user", "line": 738}
{"timestamp": "2025-06-12T17:06:13.936285", "level": "DEBUG", "logger": "backend.routes.qr_preview", "message": "Received QR preview SVG request: {\"qr_type\": \"scooter_unlock\", \"data\": {\"scooter_id\": \"SCOOT123\", \"unlock_code\": \"UNLOCK42\", \"title\": \"Scooter_unlock QR Code\", \"description\": \"Scan to view scooter_unlock content\", \"service_provider\": \"Lime\"}, \"designOptions\": {\"size\": 256, \"margin\": 10, \"pattern\": \"rounded\", \"marker_border_style\": \"square\", \"marker_center_style\": \"square\", \"logo_size\": 0.3, \"logo_position\": \"center\", \"logo_opacity\": 1, \"frame_style\": \"none\", \"frame_font\": \"SF Pro\", \"frame_font_size\": 16, \"foreground_color\": \"#0", "module": "qr_preview", "function": "preview_qr_svg", "line": 242}
{"timestamp": "2025-06-12T17:06:13.936539", "level": "DEBUG", "logger": "backend.routes.qr_preview", "message": "Form data keys: ['request', 'logo']", "module": "qr_preview", "function": "preview_qr_svg", "line": 297}
{"timestamp": "2025-06-12T17:06:13.936593", "level": "DEBUG", "logger": "backend.routes.qr_preview", "message": "Logo file found: type=<class 'starlette.datastructures.UploadFile'>, filename=LOGO - 1080 - BLACK.png", "module": "qr_preview", "function": "preview_qr_svg", "line": 300}
{"timestamp": "2025-06-12T17:06:13.936637", "level": "DEBUG", "logger": "backend.routes.qr_preview", "message": "Logo content read: 127619 bytes", "module": "qr_preview", "function": "preview_qr_svg", "line": 305}
{"timestamp": "2025-06-12T17:06:13.937553", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Formatting content for QR type: qrtype.scooter_unlock, content keys: ['scooter_id', 'unlock_code', 'title', 'description', 'service_provider']", "module": "qr_preview", "function": "_format_qr_data", "line": 876}
{"timestamp": "2025-06-12T17:06:13.957434", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Logo remove background setting: True, has logo: True", "module": "qr_preview", "function": "generate_preview_svg_modular", "line": 727}
{"timestamp": "2025-06-12T17:06:13.957495", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Logo area calculated: (19, 19, 34, 34), size_fraction: 0.3, size_modules: 15", "module": "qr_preview", "function": "generate_preview_svg_modular", "line": 739}
{"timestamp": "2025-06-12T17:06:13.974132", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Skipped 106 modules out of 1380 total modules for logo background removal", "module": "qr_preview", "function": "generate_preview_svg_modular", "line": 766}
{"timestamp": "2025-06-12T17:06:13.974216", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Drawing 3 finder patterns at positions: [(0, 0), (46, 0), (0, 46)]", "module": "qr_preview", "function": "generate_preview_svg_modular", "line": 775}
{"timestamp": "2025-06-12T17:06:13.974255", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "QR size: 53, border_style: square, center_style: square", "module": "qr_preview", "function": "generate_preview_svg_modular", "line": 776}
{"timestamp": "2025-06-12T17:06:13.974290", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Drawing finder pattern 1/3 at position (0, 0)", "module": "qr_preview", "function": "generate_preview_svg_modular", "line": 780}
{"timestamp": "2025-06-12T17:06:13.974347", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "_draw_finder_marker called: mx=0, my=0, module_size=5.471698113207547, border_style=square, center_style=square, border_color=#000000, center_color=#000000, offset=(20, 20)", "module": "qr_preview", "function": "_draw_finder_marker", "line": 97}
{"timestamp": "2025-06-12T17:06:13.974415", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Creating finder pattern group with id='marker-0-0'", "module": "qr_preview", "function": "_draw_finder_marker", "line": 110}
{"timestamp": "2025-06-12T17:06:13.974450", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Drawing square border:", "module": "qr_preview", "function": "_draw_finder_marker", "line": 160}
{"timestamp": "2025-06-12T17:06:13.974487", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "  Outer: insert=(20.0, 20.0), size=(38.301886792452834, 38.301886792452834)", "module": "qr_preview", "function": "_draw_finder_marker", "line": 161}
{"timestamp": "2025-06-12T17:06:13.974523", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "  Inner: insert=(26.566037735849058, 26.566037735849058), size=(25.169811320754715, 25.169811320754715)", "module": "qr_preview", "function": "_draw_finder_marker", "line": 162}
{"timestamp": "2025-06-12T17:06:13.974599", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Drawing marker center: style=square, color=#000000, pos=(33.67924528301887, 33.67924528301887), size=10.943396226415095", "module": "qr_preview", "function": "_draw_marker_center", "line": 179}
{"timestamp": "2025-06-12T17:06:13.974662", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Successfully added finder pattern group to SVG", "module": "qr_preview", "function": "_draw_finder_marker", "line": 171}
{"timestamp": "2025-06-12T17:06:13.974697", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Successfully drew finder pattern 1/3", "module": "qr_preview", "function": "generate_preview_svg_modular", "line": 782}
{"timestamp": "2025-06-12T17:06:13.974730", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Drawing finder pattern 2/3 at position (46, 0)", "module": "qr_preview", "function": "generate_preview_svg_modular", "line": 780}
{"timestamp": "2025-06-12T17:06:13.981780", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "_draw_finder_marker called: mx=46, my=0, module_size=5.471698113207547, border_style=square, center_style=square, border_color=#000000, center_color=#000000, offset=(20, 20)", "module": "qr_preview", "function": "_draw_finder_marker", "line": 97}
{"timestamp": "2025-06-12T17:06:13.981890", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Creating finder pattern group with id='marker-46-0'", "module": "qr_preview", "function": "_draw_finder_marker", "line": 110}
{"timestamp": "2025-06-12T17:06:13.981932", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Drawing square border:", "module": "qr_preview", "function": "_draw_finder_marker", "line": 160}
{"timestamp": "2025-06-12T17:06:13.982000", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "  Outer: insert=(271.6981132075472, 20.0), size=(38.301886792452834, 38.301886792452834)", "module": "qr_preview", "function": "_draw_finder_marker", "line": 161}
{"timestamp": "2025-06-12T17:06:13.983128", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "  Inner: insert=(278.26415094339626, 26.566037735849058), size=(25.169811320754715, 25.169811320754715)", "module": "qr_preview", "function": "_draw_finder_marker", "line": 162}
{"timestamp": "2025-06-12T17:06:13.983236", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Drawing marker center: style=square, color=#000000, pos=(285.37735849056605, 33.67924528301887), size=10.943396226415095", "module": "qr_preview", "function": "_draw_marker_center", "line": 179}
{"timestamp": "2025-06-12T17:06:13.983289", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Successfully added finder pattern group to SVG", "module": "qr_preview", "function": "_draw_finder_marker", "line": 171}
{"timestamp": "2025-06-12T17:06:13.983324", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Successfully drew finder pattern 2/3", "module": "qr_preview", "function": "generate_preview_svg_modular", "line": 782}
{"timestamp": "2025-06-12T17:06:13.985541", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Drawing finder pattern 3/3 at position (0, 46)", "module": "qr_preview", "function": "generate_preview_svg_modular", "line": 780}
{"timestamp": "2025-06-12T17:06:13.985578", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "_draw_finder_marker called: mx=0, my=46, module_size=5.471698113207547, border_style=square, center_style=square, border_color=#000000, center_color=#000000, offset=(20, 20)", "module": "qr_preview", "function": "_draw_finder_marker", "line": 97}
{"timestamp": "2025-06-12T17:06:13.985624", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Creating finder pattern group with id='marker-0-46'", "module": "qr_preview", "function": "_draw_finder_marker", "line": 110}
{"timestamp": "2025-06-12T17:06:13.985654", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Drawing square border:", "module": "qr_preview", "function": "_draw_finder_marker", "line": 160}
{"timestamp": "2025-06-12T17:06:13.985687", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "  Outer: insert=(20.0, 271.6981132075472), size=(38.301886792452834, 38.301886792452834)", "module": "qr_preview", "function": "_draw_finder_marker", "line": 161}
{"timestamp": "2025-06-12T17:06:14.029543", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "  Inner: insert=(26.566037735849058, 278.26415094339626), size=(25.169811320754715, 25.169811320754715)", "module": "qr_preview", "function": "_draw_finder_marker", "line": 162}
{"timestamp": "2025-06-12T17:06:14.029692", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Drawing marker center: style=square, color=#000000, pos=(33.67924528301887, 285.37735849056605), size=10.943396226415095", "module": "qr_preview", "function": "_draw_marker_center", "line": 179}
{"timestamp": "2025-06-12T17:06:14.029743", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Successfully added finder pattern group to SVG", "module": "qr_preview", "function": "_draw_finder_marker", "line": 171}
{"timestamp": "2025-06-12T17:06:14.029775", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Successfully drew finder pattern 3/3", "module": "qr_preview", "function": "generate_preview_svg_modular", "line": 782}
{"timestamp": "2025-06-12T17:06:14.029886", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "_draw_logo called with logo type: <class 'bytes'>, dimensions: 300", "module": "qr_preview", "function": "_draw_logo", "line": 434}
{"timestamp": "2025-06-12T17:06:14.373514", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Generated SVG length: 245783", "module": "qr_preview", "function": "generate_preview_svg_modular", "line": 802}
{"timestamp": "2025-06-12T17:06:14.373693", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "SVG contains marker-0-0: True", "module": "qr_preview", "function": "generate_preview_svg_modular", "line": 803}
{"timestamp": "2025-06-12T17:06:14.373841", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "SVG contains marker-18-0: False", "module": "qr_preview", "function": "generate_preview_svg_modular", "line": 804}
{"timestamp": "2025-06-12T17:06:14.373974", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "SVG contains marker-0-18: False", "module": "qr_preview", "function": "generate_preview_svg_modular", "line": 805}
{"timestamp": "2025-06-12T17:06:14.377188", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "SVG marker counts - outer: 3, inner: 3, dot: 0", "module": "qr_preview", "function": "generate_preview_svg_modular", "line": 813}
{"timestamp": "2025-06-12T17:06:14.377243", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "SVG label counts - text: 0, background: 0", "module": "qr_preview", "function": "generate_preview_svg_modular", "line": 814}
{"timestamp": "2025-06-12T17:06:14.380012", "level": "DEBUG", "logger": "backend.main", "message": "[Auth] Enforced Bearer token authentication for request", "module": "main", "function": "bearer_token_auth_middleware", "line": 274}
{"timestamp": "2025-06-12T17:06:14.383729", "level": "DEBUG", "logger": "backend.core.security.jwt_keys", "message": "Token contains key ID: 8be4a455-8974-4e97-b307-6c8428b2c9ff", "module": "jwt_keys", "function": "decode_token", "line": 188}
{"timestamp": "2025-06-12T17:06:14.383788", "level": "DEBUG", "logger": "backend.core.security.jwt_keys", "message": "Using key ID 8be4a455-8974-4e97-b307-6c8428b2c9ff for verification", "module": "jwt_keys", "function": "decode_token", "line": 195}
{"timestamp": "2025-06-12T17:06:14.383923", "level": "DEBUG", "logger": "backend.services.auth", "message": "JWT decoded successfully with key manager", "module": "auth", "function": "authenticate_token", "line": 683}
{"timestamp": "2025-06-12T17:06:14.386080", "level": "DEBUG", "logger": "backend.services.db_utils", "message": "Query 'auth_lookup_e4ae2d8f-b32f-52d7-99cd-8a33b19652c7' completed in 0.002s", "module": "db_utils", "function": "execute_query_with_timeout", "line": 68}
{"timestamp": "2025-06-12T17:06:14.386147", "level": "DEBUG", "logger": "backend.services.db_utils", "message": "Auth query 'auth_lookup_e4ae2d8f-b32f-52d7-99cd-8a33b19652c7' successful, result format preserved", "module": "db_utils", "function": "execute_query_with_timeout", "line": 72}
{"timestamp": "2025-06-12T17:06:14.401384", "level": "DEBUG", "logger": "backend.services.auth", "message": "Authentication attempt with token prefix: eyJhbGciOi...", "module": "auth", "function": "get_current_user", "line": 727}
{"timestamp": "2025-06-12T17:06:14.401505", "level": "DEBUG", "logger": "backend.core.security.jwt_keys", "message": "Token contains key ID: 8be4a455-8974-4e97-b307-6c8428b2c9ff", "module": "jwt_keys", "function": "decode_token", "line": 188}
{"timestamp": "2025-06-12T17:06:14.401551", "level": "DEBUG", "logger": "backend.core.security.jwt_keys", "message": "Using key ID 8be4a455-8974-4e97-b307-6c8428b2c9ff for verification", "module": "jwt_keys", "function": "decode_token", "line": 195}
{"timestamp": "2025-06-12T17:06:14.401676", "level": "DEBUG", "logger": "backend.services.auth", "message": "JWT decoded successfully with key manager", "module": "auth", "function": "authenticate_token", "line": 683}
{"timestamp": "2025-06-12T17:06:14.407489", "level": "DEBUG", "logger": "backend.services.db_utils", "message": "Query 'auth_lookup_e4ae2d8f-b32f-52d7-99cd-8a33b19652c7' completed in 0.005s", "module": "db_utils", "function": "execute_query_with_timeout", "line": 68}
{"timestamp": "2025-06-12T17:06:14.407571", "level": "DEBUG", "logger": "backend.services.db_utils", "message": "Auth query 'auth_lookup_e4ae2d8f-b32f-52d7-99cd-8a33b19652c7' successful, result format preserved", "module": "db_utils", "function": "execute_query_with_timeout", "line": 72}
{"timestamp": "2025-06-12T17:06:14.407623", "level": "DEBUG", "logger": "backend.services.auth", "message": "Successfully authenticated user: <EMAIL>", "module": "auth", "function": "get_current_user", "line": 732}
{"timestamp": "2025-06-12T17:06:14.407657", "level": "DEBUG", "logger": "backend.services.auth", "message": "Successfully authenticated user: <EMAIL>", "module": "auth", "function": "get_current_user", "line": 738}
{"timestamp": "2025-06-12T17:06:14.413452", "level": "DEBUG", "logger": "backend.routes.qr_preview", "message": "Received QR preview SVG request: {\"qr_type\": \"scooter_unlock\", \"data\": {\"scooter_id\": \"SCOOT123\", \"unlock_code\": \"UNLOCK42\", \"title\": \"Scooter_unlock QR Code\", \"description\": \"Scan to view scooter_unlock content\", \"service_provider\": \"Lime\"}, \"designOptions\": {\"size\": 256, \"margin\": 10, \"pattern\": \"rounded\", \"marker_border_style\": \"square\", \"marker_border_color\": \"#000000\", \"marker_center_style\": \"square\", \"marker_center_color\": \"#000000\", \"logo_size\": 0.3, \"logo_position\": \"center\", \"logo_opacity\": 1, \"frame_style\": \"none\", \"f", "module": "qr_preview", "function": "preview_qr_svg", "line": 242}
{"timestamp": "2025-06-12T17:06:14.413580", "level": "DEBUG", "logger": "backend.routes.qr_preview", "message": "Form data keys: ['request', 'logo']", "module": "qr_preview", "function": "preview_qr_svg", "line": 297}
{"timestamp": "2025-06-12T17:06:14.413620", "level": "DEBUG", "logger": "backend.routes.qr_preview", "message": "Logo file found: type=<class 'starlette.datastructures.UploadFile'>, filename=LOGO - 1080 - BLACK.png", "module": "qr_preview", "function": "preview_qr_svg", "line": 300}
{"timestamp": "2025-06-12T17:06:14.413661", "level": "DEBUG", "logger": "backend.routes.qr_preview", "message": "Logo content read: 127619 bytes", "module": "qr_preview", "function": "preview_qr_svg", "line": 305}
{"timestamp": "2025-06-12T17:06:14.413705", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Formatting content for QR type: qrtype.scooter_unlock, content keys: ['scooter_id', 'unlock_code', 'title', 'description', 'service_provider']", "module": "qr_preview", "function": "_format_qr_data", "line": 876}
{"timestamp": "2025-06-12T17:06:14.432274", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Logo remove background setting: False, has logo: True", "module": "qr_preview", "function": "generate_preview_svg_modular", "line": 727}
{"timestamp": "2025-06-12T17:06:14.432325", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Logo present but background removal disabled", "module": "qr_preview", "function": "generate_preview_svg_modular", "line": 741}
{"timestamp": "2025-06-12T17:06:14.449660", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Drawing 3 finder patterns at positions: [(0, 0), (46, 0), (0, 46)]", "module": "qr_preview", "function": "generate_preview_svg_modular", "line": 775}
{"timestamp": "2025-06-12T17:06:14.449731", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "QR size: 53, border_style: square, center_style: square", "module": "qr_preview", "function": "generate_preview_svg_modular", "line": 776}
{"timestamp": "2025-06-12T17:06:14.449767", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Drawing finder pattern 1/3 at position (0, 0)", "module": "qr_preview", "function": "generate_preview_svg_modular", "line": 780}
{"timestamp": "2025-06-12T17:06:14.449810", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "_draw_finder_marker called: mx=0, my=0, module_size=5.471698113207547, border_style=square, center_style=square, border_color=#000000, center_color=#000000, offset=(20, 20)", "module": "qr_preview", "function": "_draw_finder_marker", "line": 97}
{"timestamp": "2025-06-12T17:06:14.449894", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Creating finder pattern group with id='marker-0-0'", "module": "qr_preview", "function": "_draw_finder_marker", "line": 110}
{"timestamp": "2025-06-12T17:06:14.449931", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Drawing square border:", "module": "qr_preview", "function": "_draw_finder_marker", "line": 160}
{"timestamp": "2025-06-12T17:06:14.449963", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "  Outer: insert=(20.0, 20.0), size=(38.301886792452834, 38.301886792452834)", "module": "qr_preview", "function": "_draw_finder_marker", "line": 161}
{"timestamp": "2025-06-12T17:06:14.449997", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "  Inner: insert=(26.566037735849058, 26.566037735849058), size=(25.169811320754715, 25.169811320754715)", "module": "qr_preview", "function": "_draw_finder_marker", "line": 162}
{"timestamp": "2025-06-12T17:06:14.450065", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Drawing marker center: style=square, color=#000000, pos=(33.67924528301887, 33.67924528301887), size=10.943396226415095", "module": "qr_preview", "function": "_draw_marker_center", "line": 179}
{"timestamp": "2025-06-12T17:06:14.450116", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Successfully added finder pattern group to SVG", "module": "qr_preview", "function": "_draw_finder_marker", "line": 171}
{"timestamp": "2025-06-12T17:06:14.450150", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Successfully drew finder pattern 1/3", "module": "qr_preview", "function": "generate_preview_svg_modular", "line": 782}
{"timestamp": "2025-06-12T17:06:14.450228", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Drawing finder pattern 2/3 at position (46, 0)", "module": "qr_preview", "function": "generate_preview_svg_modular", "line": 780}
{"timestamp": "2025-06-12T17:06:14.450485", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "_draw_finder_marker called: mx=46, my=0, module_size=5.471698113207547, border_style=square, center_style=square, border_color=#000000, center_color=#000000, offset=(20, 20)", "module": "qr_preview", "function": "_draw_finder_marker", "line": 97}
{"timestamp": "2025-06-12T17:06:14.450548", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Creating finder pattern group with id='marker-46-0'", "module": "qr_preview", "function": "_draw_finder_marker", "line": 110}
{"timestamp": "2025-06-12T17:06:14.450592", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Drawing square border:", "module": "qr_preview", "function": "_draw_finder_marker", "line": 160}
{"timestamp": "2025-06-12T17:06:14.450632", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "  Outer: insert=(271.6981132075472, 20.0), size=(38.301886792452834, 38.301886792452834)", "module": "qr_preview", "function": "_draw_finder_marker", "line": 161}
{"timestamp": "2025-06-12T17:06:14.450669", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "  Inner: insert=(278.26415094339626, 26.566037735849058), size=(25.169811320754715, 25.169811320754715)", "module": "qr_preview", "function": "_draw_finder_marker", "line": 162}
{"timestamp": "2025-06-12T17:06:14.450772", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Drawing marker center: style=square, color=#000000, pos=(285.37735849056605, 33.67924528301887), size=10.943396226415095", "module": "qr_preview", "function": "_draw_marker_center", "line": 179}
{"timestamp": "2025-06-12T17:06:14.450819", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Successfully added finder pattern group to SVG", "module": "qr_preview", "function": "_draw_finder_marker", "line": 171}
{"timestamp": "2025-06-12T17:06:14.450866", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Successfully drew finder pattern 2/3", "module": "qr_preview", "function": "generate_preview_svg_modular", "line": 782}
{"timestamp": "2025-06-12T17:06:14.451754", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Drawing finder pattern 3/3 at position (0, 46)", "module": "qr_preview", "function": "generate_preview_svg_modular", "line": 780}
{"timestamp": "2025-06-12T17:06:14.451817", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "_draw_finder_marker called: mx=0, my=46, module_size=5.471698113207547, border_style=square, center_style=square, border_color=#000000, center_color=#000000, offset=(20, 20)", "module": "qr_preview", "function": "_draw_finder_marker", "line": 97}
{"timestamp": "2025-06-12T17:06:14.452000", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Creating finder pattern group with id='marker-0-46'", "module": "qr_preview", "function": "_draw_finder_marker", "line": 110}
{"timestamp": "2025-06-12T17:06:14.452055", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Drawing square border:", "module": "qr_preview", "function": "_draw_finder_marker", "line": 160}
{"timestamp": "2025-06-12T17:06:14.452103", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "  Outer: insert=(20.0, 271.6981132075472), size=(38.301886792452834, 38.301886792452834)", "module": "qr_preview", "function": "_draw_finder_marker", "line": 161}
{"timestamp": "2025-06-12T17:06:14.452152", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "  Inner: insert=(26.566037735849058, 278.26415094339626), size=(25.169811320754715, 25.169811320754715)", "module": "qr_preview", "function": "_draw_finder_marker", "line": 162}
{"timestamp": "2025-06-12T17:06:14.452255", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Drawing marker center: style=square, color=#000000, pos=(33.67924528301887, 285.37735849056605), size=10.943396226415095", "module": "qr_preview", "function": "_draw_marker_center", "line": 179}
{"timestamp": "2025-06-12T17:06:14.452322", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Successfully added finder pattern group to SVG", "module": "qr_preview", "function": "_draw_finder_marker", "line": 171}
{"timestamp": "2025-06-12T17:06:14.452362", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Successfully drew finder pattern 3/3", "module": "qr_preview", "function": "generate_preview_svg_modular", "line": 782}
{"timestamp": "2025-06-12T17:06:14.452407", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "_draw_logo called with logo type: <class 'bytes'>, dimensions: 300", "module": "qr_preview", "function": "_draw_logo", "line": 434}
{"timestamp": "2025-06-12T17:06:14.776262", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Generated SVG length: 267538", "module": "qr_preview", "function": "generate_preview_svg_modular", "line": 802}
{"timestamp": "2025-06-12T17:06:14.776445", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "SVG contains marker-0-0: True", "module": "qr_preview", "function": "generate_preview_svg_modular", "line": 803}
{"timestamp": "2025-06-12T17:06:14.776593", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "SVG contains marker-18-0: False", "module": "qr_preview", "function": "generate_preview_svg_modular", "line": 804}
{"timestamp": "2025-06-12T17:06:14.776732", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "SVG contains marker-0-18: False", "module": "qr_preview", "function": "generate_preview_svg_modular", "line": 805}
{"timestamp": "2025-06-12T17:06:14.777156", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "SVG marker counts - outer: 3, inner: 3, dot: 0", "module": "qr_preview", "function": "generate_preview_svg_modular", "line": 813}
{"timestamp": "2025-06-12T17:06:14.777221", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "SVG label counts - text: 0, background: 0", "module": "qr_preview", "function": "generate_preview_svg_modular", "line": 814}
{"timestamp": "2025-06-12T17:06:14.779459", "level": "DEBUG", "logger": "backend.main", "message": "[Auth] Enforced Bearer token authentication for request", "module": "main", "function": "bearer_token_auth_middleware", "line": 274}
{"timestamp": "2025-06-12T17:06:15.206284", "level": "DEBUG", "logger": "backend.core.security.jwt_keys", "message": "Token contains key ID: 8be4a455-8974-4e97-b307-6c8428b2c9ff", "module": "jwt_keys", "function": "decode_token", "line": 188}
{"timestamp": "2025-06-12T17:06:15.206338", "level": "DEBUG", "logger": "backend.core.security.jwt_keys", "message": "Using key ID 8be4a455-8974-4e97-b307-6c8428b2c9ff for verification", "module": "jwt_keys", "function": "decode_token", "line": 195}
{"timestamp": "2025-06-12T17:06:15.207166", "level": "DEBUG", "logger": "backend.services.auth", "message": "JWT decoded successfully with key manager", "module": "auth", "function": "authenticate_token", "line": 683}
{"timestamp": "2025-06-12T17:06:15.212691", "level": "DEBUG", "logger": "backend.services.db_utils", "message": "Query 'auth_lookup_e4ae2d8f-b32f-52d7-99cd-8a33b19652c7' completed in 0.005s", "module": "db_utils", "function": "execute_query_with_timeout", "line": 68}
{"timestamp": "2025-06-12T17:06:15.212746", "level": "DEBUG", "logger": "backend.services.db_utils", "message": "Auth query 'auth_lookup_e4ae2d8f-b32f-52d7-99cd-8a33b19652c7' successful, result format preserved", "module": "db_utils", "function": "execute_query_with_timeout", "line": 72}
{"timestamp": "2025-06-12T17:06:15.226924", "level": "DEBUG", "logger": "backend.services.auth", "message": "Authentication attempt with token prefix: eyJhbGciOi...", "module": "auth", "function": "get_current_user", "line": 727}
{"timestamp": "2025-06-12T17:06:15.227035", "level": "DEBUG", "logger": "backend.core.security.jwt_keys", "message": "Token contains key ID: 8be4a455-8974-4e97-b307-6c8428b2c9ff", "module": "jwt_keys", "function": "decode_token", "line": 188}
{"timestamp": "2025-06-12T17:06:15.227069", "level": "DEBUG", "logger": "backend.core.security.jwt_keys", "message": "Using key ID 8be4a455-8974-4e97-b307-6c8428b2c9ff for verification", "module": "jwt_keys", "function": "decode_token", "line": 195}
{"timestamp": "2025-06-12T17:06:15.227181", "level": "DEBUG", "logger": "backend.services.auth", "message": "JWT decoded successfully with key manager", "module": "auth", "function": "authenticate_token", "line": 683}
{"timestamp": "2025-06-12T17:06:15.231610", "level": "DEBUG", "logger": "backend.services.db_utils", "message": "Query 'auth_lookup_e4ae2d8f-b32f-52d7-99cd-8a33b19652c7' completed in 0.002s", "module": "db_utils", "function": "execute_query_with_timeout", "line": 68}
{"timestamp": "2025-06-12T17:06:15.231672", "level": "DEBUG", "logger": "backend.services.db_utils", "message": "Auth query 'auth_lookup_e4ae2d8f-b32f-52d7-99cd-8a33b19652c7' successful, result format preserved", "module": "db_utils", "function": "execute_query_with_timeout", "line": 72}
{"timestamp": "2025-06-12T17:06:15.231727", "level": "DEBUG", "logger": "backend.services.auth", "message": "Successfully authenticated user: <EMAIL>", "module": "auth", "function": "get_current_user", "line": 732}
{"timestamp": "2025-06-12T17:06:15.231758", "level": "DEBUG", "logger": "backend.services.auth", "message": "Successfully authenticated user: <EMAIL>", "module": "auth", "function": "get_current_user", "line": 738}
{"timestamp": "2025-06-12T17:06:15.239519", "level": "DEBUG", "logger": "backend.routes.qr_preview", "message": "Received QR preview SVG request: {\"qr_type\": \"scooter_unlock\", \"data\": {\"scooter_id\": \"SCOOT123\", \"unlock_code\": \"UNLOCK42\", \"title\": \"Scooter_unlock QR Code\", \"description\": \"Scan to view scooter_unlock content\", \"service_provider\": \"Lime\"}, \"designOptions\": {\"size\": 256, \"margin\": 10, \"pattern\": \"rounded\", \"marker_border_style\": \"square\", \"marker_border_color\": \"#000000\", \"marker_center_style\": \"square\", \"marker_center_color\": \"#000000\", \"logo_size\": 0.3, \"logo_position\": \"center\", \"logo_opacity\": 1, \"frame_style\": \"none\", \"f", "module": "qr_preview", "function": "preview_qr_svg", "line": 242}
{"timestamp": "2025-06-12T17:06:15.241481", "level": "DEBUG", "logger": "backend.routes.qr_preview", "message": "Form data keys: ['request', 'logo']", "module": "qr_preview", "function": "preview_qr_svg", "line": 297}
{"timestamp": "2025-06-12T17:06:15.241619", "level": "DEBUG", "logger": "backend.routes.qr_preview", "message": "Logo file found: type=<class 'starlette.datastructures.UploadFile'>, filename=LOGO - 1080 - BLACK.png", "module": "qr_preview", "function": "preview_qr_svg", "line": 300}
{"timestamp": "2025-06-12T17:06:15.244354", "level": "DEBUG", "logger": "backend.routes.qr_preview", "message": "Logo content read: 127619 bytes", "module": "qr_preview", "function": "preview_qr_svg", "line": 305}
{"timestamp": "2025-06-12T17:06:15.244406", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Formatting content for QR type: qrtype.scooter_unlock, content keys: ['scooter_id', 'unlock_code', 'title', 'description', 'service_provider']", "module": "qr_preview", "function": "_format_qr_data", "line": 876}
{"timestamp": "2025-06-12T17:06:15.263575", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Logo remove background setting: False, has logo: True", "module": "qr_preview", "function": "generate_preview_svg_modular", "line": 727}
{"timestamp": "2025-06-12T17:06:15.263632", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Logo present but background removal disabled", "module": "qr_preview", "function": "generate_preview_svg_modular", "line": 741}
{"timestamp": "2025-06-12T17:06:15.280756", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Drawing 3 finder patterns at positions: [(0, 0), (46, 0), (0, 46)]", "module": "qr_preview", "function": "generate_preview_svg_modular", "line": 775}
{"timestamp": "2025-06-12T17:06:15.280824", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "QR size: 53, border_style: square, center_style: square", "module": "qr_preview", "function": "generate_preview_svg_modular", "line": 776}
{"timestamp": "2025-06-12T17:06:15.280860", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Drawing finder pattern 1/3 at position (0, 0)", "module": "qr_preview", "function": "generate_preview_svg_modular", "line": 780}
{"timestamp": "2025-06-12T17:06:15.280904", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "_draw_finder_marker called: mx=0, my=0, module_size=5.471698113207547, border_style=square, center_style=square, border_color=#000000, center_color=#000000, offset=(20, 20)", "module": "qr_preview", "function": "_draw_finder_marker", "line": 97}
{"timestamp": "2025-06-12T17:06:15.281056", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Creating finder pattern group with id='marker-0-0'", "module": "qr_preview", "function": "_draw_finder_marker", "line": 110}
{"timestamp": "2025-06-12T17:06:15.281097", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Drawing square border:", "module": "qr_preview", "function": "_draw_finder_marker", "line": 160}
{"timestamp": "2025-06-12T17:06:15.281128", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "  Outer: insert=(20.0, 20.0), size=(38.301886792452834, 38.301886792452834)", "module": "qr_preview", "function": "_draw_finder_marker", "line": 161}
{"timestamp": "2025-06-12T17:06:15.281159", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "  Inner: insert=(26.566037735849058, 26.566037735849058), size=(25.169811320754715, 25.169811320754715)", "module": "qr_preview", "function": "_draw_finder_marker", "line": 162}
{"timestamp": "2025-06-12T17:06:15.281235", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Drawing marker center: style=square, color=#000000, pos=(33.67924528301887, 33.67924528301887), size=10.943396226415095", "module": "qr_preview", "function": "_draw_marker_center", "line": 179}
{"timestamp": "2025-06-12T17:06:15.281312", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Successfully added finder pattern group to SVG", "module": "qr_preview", "function": "_draw_finder_marker", "line": 171}
{"timestamp": "2025-06-12T17:06:15.281346", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Successfully drew finder pattern 1/3", "module": "qr_preview", "function": "generate_preview_svg_modular", "line": 782}
{"timestamp": "2025-06-12T17:06:15.281372", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Drawing finder pattern 2/3 at position (46, 0)", "module": "qr_preview", "function": "generate_preview_svg_modular", "line": 780}
{"timestamp": "2025-06-12T17:06:15.281400", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "_draw_finder_marker called: mx=46, my=0, module_size=5.471698113207547, border_style=square, center_style=square, border_color=#000000, center_color=#000000, offset=(20, 20)", "module": "qr_preview", "function": "_draw_finder_marker", "line": 97}
{"timestamp": "2025-06-12T17:06:15.281472", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Creating finder pattern group with id='marker-46-0'", "module": "qr_preview", "function": "_draw_finder_marker", "line": 110}
{"timestamp": "2025-06-12T17:06:15.281504", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Drawing square border:", "module": "qr_preview", "function": "_draw_finder_marker", "line": 160}
{"timestamp": "2025-06-12T17:06:15.281535", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "  Outer: insert=(271.6981132075472, 20.0), size=(38.301886792452834, 38.301886792452834)", "module": "qr_preview", "function": "_draw_finder_marker", "line": 161}
{"timestamp": "2025-06-12T17:06:15.281570", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "  Inner: insert=(278.26415094339626, 26.566037735849058), size=(25.169811320754715, 25.169811320754715)", "module": "qr_preview", "function": "_draw_finder_marker", "line": 162}
{"timestamp": "2025-06-12T17:06:15.281628", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Drawing marker center: style=square, color=#000000, pos=(285.37735849056605, 33.67924528301887), size=10.943396226415095", "module": "qr_preview", "function": "_draw_marker_center", "line": 179}
{"timestamp": "2025-06-12T17:06:15.281670", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Successfully added finder pattern group to SVG", "module": "qr_preview", "function": "_draw_finder_marker", "line": 171}
{"timestamp": "2025-06-12T17:06:15.281700", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Successfully drew finder pattern 2/3", "module": "qr_preview", "function": "generate_preview_svg_modular", "line": 782}
{"timestamp": "2025-06-12T17:06:15.281731", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Drawing finder pattern 3/3 at position (0, 46)", "module": "qr_preview", "function": "generate_preview_svg_modular", "line": 780}
{"timestamp": "2025-06-12T17:06:15.281760", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "_draw_finder_marker called: mx=0, my=46, module_size=5.471698113207547, border_style=square, center_style=square, border_color=#000000, center_color=#000000, offset=(20, 20)", "module": "qr_preview", "function": "_draw_finder_marker", "line": 97}
{"timestamp": "2025-06-12T17:06:15.281799", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Creating finder pattern group with id='marker-0-46'", "module": "qr_preview", "function": "_draw_finder_marker", "line": 110}
{"timestamp": "2025-06-12T17:06:15.281827", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Drawing square border:", "module": "qr_preview", "function": "_draw_finder_marker", "line": 160}
{"timestamp": "2025-06-12T17:06:15.281869", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "  Outer: insert=(20.0, 271.6981132075472), size=(38.301886792452834, 38.301886792452834)", "module": "qr_preview", "function": "_draw_finder_marker", "line": 161}
{"timestamp": "2025-06-12T17:06:15.281899", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "  Inner: insert=(26.566037735849058, 278.26415094339626), size=(25.169811320754715, 25.169811320754715)", "module": "qr_preview", "function": "_draw_finder_marker", "line": 162}
{"timestamp": "2025-06-12T17:06:15.282141", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Drawing marker center: style=square, color=#000000, pos=(33.67924528301887, 285.37735849056605), size=10.943396226415095", "module": "qr_preview", "function": "_draw_marker_center", "line": 179}
{"timestamp": "2025-06-12T17:06:15.282185", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Successfully added finder pattern group to SVG", "module": "qr_preview", "function": "_draw_finder_marker", "line": 171}
{"timestamp": "2025-06-12T17:06:15.282212", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Successfully drew finder pattern 3/3", "module": "qr_preview", "function": "generate_preview_svg_modular", "line": 782}
{"timestamp": "2025-06-12T17:06:15.282247", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "_draw_logo called with logo type: <class 'bytes'>, dimensions: 300", "module": "qr_preview", "function": "_draw_logo", "line": 434}
{"timestamp": "2025-06-12T17:06:15.608758", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Generated SVG length: 267538", "module": "qr_preview", "function": "generate_preview_svg_modular", "line": 802}
{"timestamp": "2025-06-12T17:06:15.608924", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "SVG contains marker-0-0: True", "module": "qr_preview", "function": "generate_preview_svg_modular", "line": 803}
{"timestamp": "2025-06-12T17:06:15.609066", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "SVG contains marker-18-0: False", "module": "qr_preview", "function": "generate_preview_svg_modular", "line": 804}
{"timestamp": "2025-06-12T17:06:15.609198", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "SVG contains marker-0-18: False", "module": "qr_preview", "function": "generate_preview_svg_modular", "line": 805}
{"timestamp": "2025-06-12T17:06:15.609619", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "SVG marker counts - outer: 3, inner: 3, dot: 0", "module": "qr_preview", "function": "generate_preview_svg_modular", "line": 813}
{"timestamp": "2025-06-12T17:06:15.609649", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "SVG label counts - text: 0, background: 0", "module": "qr_preview", "function": "generate_preview_svg_modular", "line": 814}
{"timestamp": "2025-06-12T17:06:15.611665", "level": "DEBUG", "logger": "backend.main", "message": "[Auth] Enforced Bearer token authentication for request", "module": "main", "function": "bearer_token_auth_middleware", "line": 274}
{"timestamp": "2025-06-12T17:06:43.606035", "level": "DEBUG", "logger": "backend.main", "message": "[Auth] Enforced Bearer token authentication for request", "module": "main", "function": "bearer_token_auth_middleware", "line": 274}
{"timestamp": "2025-06-12T17:06:43.769834", "level": "DEBUG", "logger": "backend.main", "message": "[Auth] Enforced Bearer token authentication for request", "module": "main", "function": "bearer_token_auth_middleware", "line": 274}
{"timestamp": "2025-06-12T17:07:14.325258", "level": "DEBUG", "logger": "backend.main", "message": "[Auth] Enforced Bearer token authentication for request", "module": "main", "function": "bearer_token_auth_middleware", "line": 274}
{"timestamp": "2025-06-12T17:07:43.609445", "level": "DEBUG", "logger": "backend.main", "message": "[Auth] Enforced Bearer token authentication for request", "module": "main", "function": "bearer_token_auth_middleware", "line": 274}
{"timestamp": "2025-06-12T17:07:44.600921", "level": "DEBUG", "logger": "backend.main", "message": "[Auth] Enforced Bearer token authentication for request", "module": "main", "function": "bearer_token_auth_middleware", "line": 274}
{"timestamp": "2025-06-12T17:08:15.330759", "level": "DEBUG", "logger": "backend.main", "message": "[Auth] Enforced Bearer token authentication for request", "module": "main", "function": "bearer_token_auth_middleware", "line": 274}
