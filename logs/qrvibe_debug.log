{"timestamp": "2025-06-11T15:44:14.786602", "level": "DEBUG", "logger": "backend.main", "message": "[Auth] Preflight request handled directly in middleware", "module": "main", "function": "bearer_token_auth_middleware", "line": 218}
{"timestamp": "2025-06-11T15:44:14.791332", "level": "DEBUG", "logger": "backend.core.security.jwt_keys", "message": "Token contains key ID: 69b5ada0-20e9-42b1-85e8-1ec80762bb7c", "module": "jwt_keys", "function": "decode_token", "line": 188}
{"timestamp": "2025-06-11T15:44:14.791396", "level": "DEBUG", "logger": "backend.core.security.jwt_keys", "message": "Using key ID 69b5ada0-20e9-42b1-85e8-1ec80762bb7c for verification", "module": "jwt_keys", "function": "decode_token", "line": 195}
{"timestamp": "2025-06-11T15:44:14.792235", "level": "DEBUG", "logger": "backend.services.auth", "message": "JWT decoded successfully with key manager", "module": "auth", "function": "authenticate_token", "line": 683}
{"timestamp": "2025-06-11T15:44:14.802456", "level": "DEBUG", "logger": "backend.services.db_utils", "message": "Query 'auth_lookup_e4ae2d8f-b32f-52d7-99cd-8a33b19652c7' completed in 0.006s", "module": "db_utils", "function": "execute_query_with_timeout", "line": 68}
{"timestamp": "2025-06-11T15:44:14.802530", "level": "DEBUG", "logger": "backend.services.db_utils", "message": "Auth query 'auth_lookup_e4ae2d8f-b32f-52d7-99cd-8a33b19652c7' successful, result format preserved", "module": "db_utils", "function": "execute_query_with_timeout", "line": 72}
{"timestamp": "2025-06-11T15:44:14.810116", "level": "DEBUG", "logger": "backend.services.auth", "message": "Authentication attempt with token prefix: eyJhbGciOi...", "module": "auth", "function": "get_current_user", "line": 727}
{"timestamp": "2025-06-11T15:44:14.810238", "level": "DEBUG", "logger": "backend.core.security.jwt_keys", "message": "Token contains key ID: 69b5ada0-20e9-42b1-85e8-1ec80762bb7c", "module": "jwt_keys", "function": "decode_token", "line": 188}
{"timestamp": "2025-06-11T15:44:14.810281", "level": "DEBUG", "logger": "backend.core.security.jwt_keys", "message": "Using key ID 69b5ada0-20e9-42b1-85e8-1ec80762bb7c for verification", "module": "jwt_keys", "function": "decode_token", "line": 195}
{"timestamp": "2025-06-11T15:44:14.810415", "level": "DEBUG", "logger": "backend.services.auth", "message": "JWT decoded successfully with key manager", "module": "auth", "function": "authenticate_token", "line": 683}
{"timestamp": "2025-06-11T15:44:14.811780", "level": "DEBUG", "logger": "backend.services.db_utils", "message": "Query 'auth_lookup_e4ae2d8f-b32f-52d7-99cd-8a33b19652c7' completed in 0.001s", "module": "db_utils", "function": "execute_query_with_timeout", "line": 68}
{"timestamp": "2025-06-11T15:44:14.811828", "level": "DEBUG", "logger": "backend.services.db_utils", "message": "Auth query 'auth_lookup_e4ae2d8f-b32f-52d7-99cd-8a33b19652c7' successful, result format preserved", "module": "db_utils", "function": "execute_query_with_timeout", "line": 72}
{"timestamp": "2025-06-11T15:44:14.811884", "level": "DEBUG", "logger": "backend.services.auth", "message": "Successfully authenticated user: <EMAIL>", "module": "auth", "function": "get_current_user", "line": 732}
{"timestamp": "2025-06-11T15:44:14.811928", "level": "DEBUG", "logger": "backend.services.auth", "message": "Successfully authenticated user: <EMAIL>", "module": "auth", "function": "get_current_user", "line": 738}
{"timestamp": "2025-06-11T15:44:14.819463", "level": "DEBUG", "logger": "backend.routes.qr_preview", "message": "Received QR preview SVG request: {\"qr_type\": \"url\", \"data\": {\"url\": \"https://example.com\", \"utm_source\": \"\", \"utm_medium\": \"\", \"utm_campaign\": \"\", \"short_url\": false, \"title\": \"Url QR Code\", \"description\": \"Scan to view url content\"}, \"designOptions\": {\"size\": 256, \"margin\": 10, \"pattern\": \"square\", \"marker_border_style\": \"square\", \"marker_center_style\": \"square\", \"logo_size\": 48, \"logo_position\": \"center\", \"logo_opacity\": 1, \"frame_style\": \"none\", \"foreground_color\": \"#000000\", \"background_color\": \"#FFFFFF\", \"color_mode\": \"sol", "module": "qr_preview", "function": "preview_qr_svg", "line": 242}
{"timestamp": "2025-06-11T15:44:14.820018", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Formatting content for QR type: qrtype.url, content keys: ['url', 'utm_source', 'utm_medium', 'utm_campaign', 'short_url', 'title', 'description']", "module": "qr_preview", "function": "_format_qr_data", "line": 554}
{"timestamp": "2025-06-11T15:44:14.829181", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Drawing 3 finder patterns at positions: [(0, 0), (18, 0), (0, 18)]", "module": "qr_preview", "function": "generate_preview_svg_modular", "line": 479}
{"timestamp": "2025-06-11T15:44:14.829237", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "QR size: 25, marker_shape: square, marker_color: #000000", "module": "qr_preview", "function": "generate_preview_svg_modular", "line": 480}
{"timestamp": "2025-06-11T15:44:14.829271", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Drawing finder pattern 1/3 at position (0, 0)", "module": "qr_preview", "function": "generate_preview_svg_modular", "line": 484}
{"timestamp": "2025-06-11T15:44:14.829318", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "_draw_finder_marker called: mx=0, my=0, module_size=11.6, shape=square, color=#000000, offset=(5, 5)", "module": "qr_preview", "function": "_draw_finder_marker", "line": 56}
{"timestamp": "2025-06-11T15:44:14.829405", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Creating finder pattern group with id='marker-0-0'", "module": "qr_preview", "function": "_draw_finder_marker", "line": 64}
{"timestamp": "2025-06-11T15:44:14.829438", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Drawing square finder pattern:", "module": "qr_preview", "function": "_draw_finder_marker", "line": 79}
{"timestamp": "2025-06-11T15:44:14.829466", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "  Outer: insert=(5.0, 5.0), size=(81.2, 81.2)", "module": "qr_preview", "function": "_draw_finder_marker", "line": 80}
{"timestamp": "2025-06-11T15:44:14.829497", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "  Inner: insert=(18.92, 18.92), size=(53.35999999999999, 53.35999999999999)", "module": "qr_preview", "function": "_draw_finder_marker", "line": 81}
{"timestamp": "2025-06-11T15:44:14.829526", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "  Dot: insert=(34.0, 34.0), size=(23.2, 23.2)", "module": "qr_preview", "function": "_draw_finder_marker", "line": 82}
{"timestamp": "2025-06-11T15:44:14.829601", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Successfully added finder pattern group to SVG", "module": "qr_preview", "function": "_draw_finder_marker", "line": 89}
{"timestamp": "2025-06-11T15:44:14.829631", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Successfully drew finder pattern 1/3", "module": "qr_preview", "function": "generate_preview_svg_modular", "line": 486}
{"timestamp": "2025-06-11T15:44:14.829661", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Drawing finder pattern 2/3 at position (18, 0)", "module": "qr_preview", "function": "generate_preview_svg_modular", "line": 484}
{"timestamp": "2025-06-11T15:44:14.829691", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "_draw_finder_marker called: mx=18, my=0, module_size=11.6, shape=square, color=#000000, offset=(5, 5)", "module": "qr_preview", "function": "_draw_finder_marker", "line": 56}
{"timestamp": "2025-06-11T15:44:14.829728", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Creating finder pattern group with id='marker-18-0'", "module": "qr_preview", "function": "_draw_finder_marker", "line": 64}
{"timestamp": "2025-06-11T15:44:14.829757", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Drawing square finder pattern:", "module": "qr_preview", "function": "_draw_finder_marker", "line": 79}
{"timestamp": "2025-06-11T15:44:14.829811", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "  Outer: insert=(213.79999999999998, 5.0), size=(81.2, 81.2)", "module": "qr_preview", "function": "_draw_finder_marker", "line": 80}
{"timestamp": "2025-06-11T15:44:14.829843", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "  Inner: insert=(227.71999999999997, 18.92), size=(53.35999999999999, 53.35999999999999)", "module": "qr_preview", "function": "_draw_finder_marker", "line": 81}
{"timestamp": "2025-06-11T15:44:14.829872", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "  Dot: insert=(242.79999999999998, 34.0), size=(23.2, 23.2)", "module": "qr_preview", "function": "_draw_finder_marker", "line": 82}
{"timestamp": "2025-06-11T15:44:14.829936", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Successfully added finder pattern group to SVG", "module": "qr_preview", "function": "_draw_finder_marker", "line": 89}
{"timestamp": "2025-06-11T15:44:14.829964", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Successfully drew finder pattern 2/3", "module": "qr_preview", "function": "generate_preview_svg_modular", "line": 486}
{"timestamp": "2025-06-11T15:44:14.829991", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Drawing finder pattern 3/3 at position (0, 18)", "module": "qr_preview", "function": "generate_preview_svg_modular", "line": 484}
{"timestamp": "2025-06-11T15:44:14.830020", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "_draw_finder_marker called: mx=0, my=18, module_size=11.6, shape=square, color=#000000, offset=(5, 5)", "module": "qr_preview", "function": "_draw_finder_marker", "line": 56}
{"timestamp": "2025-06-11T15:44:14.830056", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Creating finder pattern group with id='marker-0-18'", "module": "qr_preview", "function": "_draw_finder_marker", "line": 64}
{"timestamp": "2025-06-11T15:44:14.830084", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Drawing square finder pattern:", "module": "qr_preview", "function": "_draw_finder_marker", "line": 79}
{"timestamp": "2025-06-11T15:44:14.830111", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "  Outer: insert=(5.0, 213.79999999999998), size=(81.2, 81.2)", "module": "qr_preview", "function": "_draw_finder_marker", "line": 80}
{"timestamp": "2025-06-11T15:44:14.830140", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "  Inner: insert=(18.92, 227.71999999999997), size=(53.35999999999999, 53.35999999999999)", "module": "qr_preview", "function": "_draw_finder_marker", "line": 81}
{"timestamp": "2025-06-11T15:44:14.830168", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "  Dot: insert=(34.0, 242.79999999999998), size=(23.2, 23.2)", "module": "qr_preview", "function": "_draw_finder_marker", "line": 82}
{"timestamp": "2025-06-11T15:44:14.830228", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Successfully added finder pattern group to SVG", "module": "qr_preview", "function": "_draw_finder_marker", "line": 89}
{"timestamp": "2025-06-11T15:44:14.830255", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Successfully drew finder pattern 3/3", "module": "qr_preview", "function": "generate_preview_svg_modular", "line": 486}
{"timestamp": "2025-06-11T15:44:14.843979", "level": "DEBUG", "logger": "backend.main", "message": "[Auth] Enforced Bearer token authentication for request", "module": "main", "function": "bearer_token_auth_middleware", "line": 274}
{"timestamp": "2025-06-11T15:44:19.693635", "level": "DEBUG", "logger": "backend.main", "message": "[Auth] Preflight request handled directly in middleware", "module": "main", "function": "bearer_token_auth_middleware", "line": 218}
{"timestamp": "2025-06-11T15:44:19.700998", "level": "DEBUG", "logger": "backend.core.security.jwt_keys", "message": "Token contains key ID: 69b5ada0-20e9-42b1-85e8-1ec80762bb7c", "module": "jwt_keys", "function": "decode_token", "line": 188}
{"timestamp": "2025-06-11T15:44:19.704612", "level": "DEBUG", "logger": "backend.core.security.jwt_keys", "message": "Using key ID 69b5ada0-20e9-42b1-85e8-1ec80762bb7c for verification", "module": "jwt_keys", "function": "decode_token", "line": 195}
{"timestamp": "2025-06-11T15:44:19.705159", "level": "DEBUG", "logger": "backend.services.auth", "message": "JWT decoded successfully with key manager", "module": "auth", "function": "authenticate_token", "line": 683}
{"timestamp": "2025-06-11T15:44:19.714480", "level": "DEBUG", "logger": "backend.services.db_utils", "message": "Query 'auth_lookup_e4ae2d8f-b32f-52d7-99cd-8a33b19652c7' completed in 0.009s", "module": "db_utils", "function": "execute_query_with_timeout", "line": 68}
{"timestamp": "2025-06-11T15:44:19.714538", "level": "DEBUG", "logger": "backend.services.db_utils", "message": "Auth query 'auth_lookup_e4ae2d8f-b32f-52d7-99cd-8a33b19652c7' successful, result format preserved", "module": "db_utils", "function": "execute_query_with_timeout", "line": 72}
{"timestamp": "2025-06-11T15:44:19.738850", "level": "DEBUG", "logger": "backend.services.auth", "message": "Authentication attempt with token prefix: eyJhbGciOi...", "module": "auth", "function": "get_current_user", "line": 727}
{"timestamp": "2025-06-11T15:44:19.739089", "level": "DEBUG", "logger": "backend.core.security.jwt_keys", "message": "Token contains key ID: 69b5ada0-20e9-42b1-85e8-1ec80762bb7c", "module": "jwt_keys", "function": "decode_token", "line": 188}
{"timestamp": "2025-06-11T15:44:19.739183", "level": "DEBUG", "logger": "backend.core.security.jwt_keys", "message": "Using key ID 69b5ada0-20e9-42b1-85e8-1ec80762bb7c for verification", "module": "jwt_keys", "function": "decode_token", "line": 195}
{"timestamp": "2025-06-11T15:44:19.739473", "level": "DEBUG", "logger": "backend.services.auth", "message": "JWT decoded successfully with key manager", "module": "auth", "function": "authenticate_token", "line": 683}
{"timestamp": "2025-06-11T15:44:19.742015", "level": "DEBUG", "logger": "backend.services.db_utils", "message": "Query 'auth_lookup_e4ae2d8f-b32f-52d7-99cd-8a33b19652c7' completed in 0.002s", "module": "db_utils", "function": "execute_query_with_timeout", "line": 68}
{"timestamp": "2025-06-11T15:44:19.742078", "level": "DEBUG", "logger": "backend.services.db_utils", "message": "Auth query 'auth_lookup_e4ae2d8f-b32f-52d7-99cd-8a33b19652c7' successful, result format preserved", "module": "db_utils", "function": "execute_query_with_timeout", "line": 72}
{"timestamp": "2025-06-11T15:44:19.742137", "level": "DEBUG", "logger": "backend.services.auth", "message": "Successfully authenticated user: <EMAIL>", "module": "auth", "function": "get_current_user", "line": 732}
{"timestamp": "2025-06-11T15:44:19.742169", "level": "DEBUG", "logger": "backend.services.auth", "message": "Successfully authenticated user: <EMAIL>", "module": "auth", "function": "get_current_user", "line": 738}
{"timestamp": "2025-06-11T15:44:19.744093", "level": "DEBUG", "logger": "backend.routes.qr_preview", "message": "Received QR preview SVG request: {\"qr_type\": \"scooter_unlock\", \"data\": {\"scooter_id\": \"SCOOT123\", \"unlock_code\": \"UNLOCK42\", \"title\": \"Scooter_unlock QR Code\", \"description\": \"Scan to view scooter_unlock content\"}, \"designOptions\": {\"size\": 256, \"margin\": 10, \"pattern\": \"square\", \"marker_border_style\": \"square\", \"marker_center_style\": \"square\", \"logo_size\": 48, \"logo_position\": \"center\", \"logo_opacity\": 1, \"frame_style\": \"none\", \"foreground_color\": \"#000000\", \"background_color\": \"#FFFFFF\", \"color_mode\": \"solid\", \"corner_radius\"", "module": "qr_preview", "function": "preview_qr_svg", "line": 242}
{"timestamp": "2025-06-11T15:44:19.744213", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Formatting content for QR type: qrtype.scooter_unlock, content keys: ['scooter_id', 'unlock_code', 'title', 'description']", "module": "qr_preview", "function": "_format_qr_data", "line": 554}
{"timestamp": "2025-06-11T15:44:19.774635", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Drawing 3 finder patterns at positions: [(0, 0), (42, 0), (0, 42)]", "module": "qr_preview", "function": "generate_preview_svg_modular", "line": 479}
{"timestamp": "2025-06-11T15:44:19.774706", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "QR size: 49, marker_shape: square, marker_color: #000000", "module": "qr_preview", "function": "generate_preview_svg_modular", "line": 480}
{"timestamp": "2025-06-11T15:44:19.774742", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Drawing finder pattern 1/3 at position (0, 0)", "module": "qr_preview", "function": "generate_preview_svg_modular", "line": 484}
{"timestamp": "2025-06-11T15:44:19.774784", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "_draw_finder_marker called: mx=0, my=0, module_size=5.918367346938775, shape=square, color=#000000, offset=(5, 5)", "module": "qr_preview", "function": "_draw_finder_marker", "line": 56}
{"timestamp": "2025-06-11T15:44:19.774837", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Creating finder pattern group with id='marker-0-0'", "module": "qr_preview", "function": "_draw_finder_marker", "line": 64}
{"timestamp": "2025-06-11T15:44:19.774866", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Drawing square finder pattern:", "module": "qr_preview", "function": "_draw_finder_marker", "line": 79}
{"timestamp": "2025-06-11T15:44:19.774896", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "  Outer: insert=(5.0, 5.0), size=(41.42857142857143, 41.42857142857143)", "module": "qr_preview", "function": "_draw_finder_marker", "line": 80}
{"timestamp": "2025-06-11T15:44:19.774925", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "  Inner: insert=(12.10204081632653, 12.10204081632653), size=(27.224489795918366, 27.224489795918366)", "module": "qr_preview", "function": "_draw_finder_marker", "line": 81}
{"timestamp": "2025-06-11T15:44:19.774953", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "  Dot: insert=(19.79591836734694, 19.79591836734694), size=(11.83673469387755, 11.83673469387755)", "module": "qr_preview", "function": "_draw_finder_marker", "line": 82}
{"timestamp": "2025-06-11T15:44:19.775050", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Successfully added finder pattern group to SVG", "module": "qr_preview", "function": "_draw_finder_marker", "line": 89}
{"timestamp": "2025-06-11T15:44:19.775118", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Successfully drew finder pattern 1/3", "module": "qr_preview", "function": "generate_preview_svg_modular", "line": 486}
{"timestamp": "2025-06-11T15:44:19.775175", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Drawing finder pattern 2/3 at position (42, 0)", "module": "qr_preview", "function": "generate_preview_svg_modular", "line": 484}
{"timestamp": "2025-06-11T15:44:19.775215", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "_draw_finder_marker called: mx=42, my=0, module_size=5.918367346938775, shape=square, color=#000000, offset=(5, 5)", "module": "qr_preview", "function": "_draw_finder_marker", "line": 56}
{"timestamp": "2025-06-11T15:44:19.775264", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Creating finder pattern group with id='marker-42-0'", "module": "qr_preview", "function": "_draw_finder_marker", "line": 64}
{"timestamp": "2025-06-11T15:44:19.775296", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Drawing square finder pattern:", "module": "qr_preview", "function": "_draw_finder_marker", "line": 79}
{"timestamp": "2025-06-11T15:44:19.775327", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "  Outer: insert=(253.57142857142856, 5.0), size=(41.42857142857143, 41.42857142857143)", "module": "qr_preview", "function": "_draw_finder_marker", "line": 80}
{"timestamp": "2025-06-11T15:44:19.775384", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "  Inner: insert=(260.67346938775506, 12.10204081632653), size=(27.224489795918366, 27.224489795918366)", "module": "qr_preview", "function": "_draw_finder_marker", "line": 81}
{"timestamp": "2025-06-11T15:44:19.775416", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "  Dot: insert=(268.3673469387755, 19.79591836734694), size=(11.83673469387755, 11.83673469387755)", "module": "qr_preview", "function": "_draw_finder_marker", "line": 82}
{"timestamp": "2025-06-11T15:44:19.775509", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Successfully added finder pattern group to SVG", "module": "qr_preview", "function": "_draw_finder_marker", "line": 89}
{"timestamp": "2025-06-11T15:44:19.775545", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Successfully drew finder pattern 2/3", "module": "qr_preview", "function": "generate_preview_svg_modular", "line": 486}
{"timestamp": "2025-06-11T15:44:19.775577", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Drawing finder pattern 3/3 at position (0, 42)", "module": "qr_preview", "function": "generate_preview_svg_modular", "line": 484}
{"timestamp": "2025-06-11T15:44:19.775611", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "_draw_finder_marker called: mx=0, my=42, module_size=5.918367346938775, shape=square, color=#000000, offset=(5, 5)", "module": "qr_preview", "function": "_draw_finder_marker", "line": 56}
{"timestamp": "2025-06-11T15:44:19.775648", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Creating finder pattern group with id='marker-0-42'", "module": "qr_preview", "function": "_draw_finder_marker", "line": 64}
{"timestamp": "2025-06-11T15:44:19.775675", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Drawing square finder pattern:", "module": "qr_preview", "function": "_draw_finder_marker", "line": 79}
{"timestamp": "2025-06-11T15:44:19.775707", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "  Outer: insert=(5.0, 253.57142857142856), size=(41.42857142857143, 41.42857142857143)", "module": "qr_preview", "function": "_draw_finder_marker", "line": 80}
{"timestamp": "2025-06-11T15:44:19.775870", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "  Inner: insert=(12.10204081632653, 260.67346938775506), size=(27.224489795918366, 27.224489795918366)", "module": "qr_preview", "function": "_draw_finder_marker", "line": 81}
{"timestamp": "2025-06-11T15:44:19.775906", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "  Dot: insert=(19.79591836734694, 268.3673469387755), size=(11.83673469387755, 11.83673469387755)", "module": "qr_preview", "function": "_draw_finder_marker", "line": 82}
{"timestamp": "2025-06-11T15:44:19.775974", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Successfully added finder pattern group to SVG", "module": "qr_preview", "function": "_draw_finder_marker", "line": 89}
{"timestamp": "2025-06-11T15:44:19.776002", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Successfully drew finder pattern 3/3", "module": "qr_preview", "function": "generate_preview_svg_modular", "line": 486}
{"timestamp": "2025-06-11T15:44:19.803340", "level": "DEBUG", "logger": "backend.main", "message": "[Auth] Enforced Bearer token authentication for request", "module": "main", "function": "bearer_token_auth_middleware", "line": 274}
{"timestamp": "2025-06-11T15:44:24.710651", "level": "DEBUG", "logger": "backend.main", "message": "[Auth] Preflight request handled directly in middleware", "module": "main", "function": "bearer_token_auth_middleware", "line": 218}
{"timestamp": "2025-06-11T15:44:24.720162", "level": "DEBUG", "logger": "backend.main", "message": "[Auth] Preflight request handled directly in middleware", "module": "main", "function": "bearer_token_auth_middleware", "line": 218}
{"timestamp": "2025-06-11T15:44:24.721763", "level": "DEBUG", "logger": "backend.core.security.jwt_keys", "message": "Token contains key ID: 69b5ada0-20e9-42b1-85e8-1ec80762bb7c", "module": "jwt_keys", "function": "decode_token", "line": 188}
{"timestamp": "2025-06-11T15:44:24.721835", "level": "DEBUG", "logger": "backend.core.security.jwt_keys", "message": "Using key ID 69b5ada0-20e9-42b1-85e8-1ec80762bb7c for verification", "module": "jwt_keys", "function": "decode_token", "line": 195}
{"timestamp": "2025-06-11T15:44:24.722643", "level": "DEBUG", "logger": "backend.services.auth", "message": "JWT decoded successfully with key manager", "module": "auth", "function": "authenticate_token", "line": 683}
{"timestamp": "2025-06-11T15:44:24.732434", "level": "DEBUG", "logger": "backend.core.security.jwt_keys", "message": "Token contains key ID: 69b5ada0-20e9-42b1-85e8-1ec80762bb7c", "module": "jwt_keys", "function": "decode_token", "line": 188}
{"timestamp": "2025-06-11T15:44:24.732489", "level": "DEBUG", "logger": "backend.core.security.jwt_keys", "message": "Using key ID 69b5ada0-20e9-42b1-85e8-1ec80762bb7c for verification", "module": "jwt_keys", "function": "decode_token", "line": 195}
{"timestamp": "2025-06-11T15:44:24.732825", "level": "DEBUG", "logger": "backend.services.auth", "message": "JWT decoded successfully with key manager", "module": "auth", "function": "authenticate_token", "line": 683}
{"timestamp": "2025-06-11T15:44:24.738329", "level": "DEBUG", "logger": "backend.services.db_utils", "message": "Query 'auth_lookup_e4ae2d8f-b32f-52d7-99cd-8a33b19652c7' completed in 0.014s", "module": "db_utils", "function": "execute_query_with_timeout", "line": 68}
{"timestamp": "2025-06-11T15:44:24.738392", "level": "DEBUG", "logger": "backend.services.db_utils", "message": "Auth query 'auth_lookup_e4ae2d8f-b32f-52d7-99cd-8a33b19652c7' successful, result format preserved", "module": "db_utils", "function": "execute_query_with_timeout", "line": 72}
{"timestamp": "2025-06-11T15:44:24.746429", "level": "DEBUG", "logger": "backend.services.auth", "message": "Authentication attempt with token prefix: eyJhbGciOi...", "module": "auth", "function": "get_current_user", "line": 727}
{"timestamp": "2025-06-11T15:44:24.746533", "level": "DEBUG", "logger": "backend.core.security.jwt_keys", "message": "Token contains key ID: 69b5ada0-20e9-42b1-85e8-1ec80762bb7c", "module": "jwt_keys", "function": "decode_token", "line": 188}
{"timestamp": "2025-06-11T15:44:24.746569", "level": "DEBUG", "logger": "backend.core.security.jwt_keys", "message": "Using key ID 69b5ada0-20e9-42b1-85e8-1ec80762bb7c for verification", "module": "jwt_keys", "function": "decode_token", "line": 195}
{"timestamp": "2025-06-11T15:44:24.746684", "level": "DEBUG", "logger": "backend.services.auth", "message": "JWT decoded successfully with key manager", "module": "auth", "function": "authenticate_token", "line": 683}
{"timestamp": "2025-06-11T15:44:24.748190", "level": "DEBUG", "logger": "backend.services.db_utils", "message": "Query 'auth_lookup_e4ae2d8f-b32f-52d7-99cd-8a33b19652c7' completed in 0.001s", "module": "db_utils", "function": "execute_query_with_timeout", "line": 68}
{"timestamp": "2025-06-11T15:44:24.748240", "level": "DEBUG", "logger": "backend.services.db_utils", "message": "Auth query 'auth_lookup_e4ae2d8f-b32f-52d7-99cd-8a33b19652c7' successful, result format preserved", "module": "db_utils", "function": "execute_query_with_timeout", "line": 72}
{"timestamp": "2025-06-11T15:44:24.748293", "level": "DEBUG", "logger": "backend.services.auth", "message": "Successfully authenticated user: <EMAIL>", "module": "auth", "function": "get_current_user", "line": 732}
{"timestamp": "2025-06-11T15:44:24.748331", "level": "DEBUG", "logger": "backend.services.auth", "message": "Successfully authenticated user: <EMAIL>", "module": "auth", "function": "get_current_user", "line": 738}
{"timestamp": "2025-06-11T15:44:24.752705", "level": "DEBUG", "logger": "backend.routes.qr_preview", "message": "Received QR preview SVG request: {\"qr_type\": \"scooter_unlock\", \"data\": {\"scooter_id\": \"SCOOT123\", \"unlock_code\": \"UNLOCK42\", \"title\": \"Scooter_unlock QR Code\", \"description\": \"Scan to view scooter_unlock content\"}, \"designOptions\": {\"size\": 256, \"margin\": 10, \"pattern\": \"square\", \"error_correction\": \"M\", \"svg_render_dpi\": 300, \"svg_optimize\": true}, \"dimensions\": 300}", "module": "qr_preview", "function": "preview_qr_svg", "line": 242}
{"timestamp": "2025-06-11T15:44:24.756306", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Formatting content for QR type: qrtype.scooter_unlock, content keys: ['scooter_id', 'unlock_code', 'title', 'description']", "module": "qr_preview", "function": "_format_qr_data", "line": 554}
{"timestamp": "2025-06-11T15:44:24.791334", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Drawing 3 finder patterns at positions: [(0, 0), (42, 0), (0, 42)]", "module": "qr_preview", "function": "generate_preview_svg_modular", "line": 479}
{"timestamp": "2025-06-11T15:44:24.791393", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "QR size: 49, marker_shape: square, marker_color: #000000", "module": "qr_preview", "function": "generate_preview_svg_modular", "line": 480}
{"timestamp": "2025-06-11T15:44:24.791427", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Drawing finder pattern 1/3 at position (0, 0)", "module": "qr_preview", "function": "generate_preview_svg_modular", "line": 484}
{"timestamp": "2025-06-11T15:44:24.791475", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "_draw_finder_marker called: mx=0, my=0, module_size=5.918367346938775, shape=square, color=#000000, offset=(5, 5)", "module": "qr_preview", "function": "_draw_finder_marker", "line": 56}
{"timestamp": "2025-06-11T15:44:24.791555", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Creating finder pattern group with id='marker-0-0'", "module": "qr_preview", "function": "_draw_finder_marker", "line": 64}
{"timestamp": "2025-06-11T15:44:24.791584", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Drawing square finder pattern:", "module": "qr_preview", "function": "_draw_finder_marker", "line": 79}
{"timestamp": "2025-06-11T15:44:24.791615", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "  Outer: insert=(5.0, 5.0), size=(41.42857142857143, 41.42857142857143)", "module": "qr_preview", "function": "_draw_finder_marker", "line": 80}
{"timestamp": "2025-06-11T15:44:24.791646", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "  Inner: insert=(12.10204081632653, 12.10204081632653), size=(27.224489795918366, 27.224489795918366)", "module": "qr_preview", "function": "_draw_finder_marker", "line": 81}
{"timestamp": "2025-06-11T15:44:24.791676", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "  Dot: insert=(19.79591836734694, 19.79591836734694), size=(11.83673469387755, 11.83673469387755)", "module": "qr_preview", "function": "_draw_finder_marker", "line": 82}
{"timestamp": "2025-06-11T15:44:24.791754", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Successfully added finder pattern group to SVG", "module": "qr_preview", "function": "_draw_finder_marker", "line": 89}
{"timestamp": "2025-06-11T15:44:24.791785", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Successfully drew finder pattern 1/3", "module": "qr_preview", "function": "generate_preview_svg_modular", "line": 486}
{"timestamp": "2025-06-11T15:44:24.791813", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Drawing finder pattern 2/3 at position (42, 0)", "module": "qr_preview", "function": "generate_preview_svg_modular", "line": 484}
{"timestamp": "2025-06-11T15:44:24.791846", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "_draw_finder_marker called: mx=42, my=0, module_size=5.918367346938775, shape=square, color=#000000, offset=(5, 5)", "module": "qr_preview", "function": "_draw_finder_marker", "line": 56}
{"timestamp": "2025-06-11T15:44:24.791900", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Creating finder pattern group with id='marker-42-0'", "module": "qr_preview", "function": "_draw_finder_marker", "line": 64}
{"timestamp": "2025-06-11T15:44:24.791928", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Drawing square finder pattern:", "module": "qr_preview", "function": "_draw_finder_marker", "line": 79}
{"timestamp": "2025-06-11T15:44:24.791958", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "  Outer: insert=(253.57142857142856, 5.0), size=(41.42857142857143, 41.42857142857143)", "module": "qr_preview", "function": "_draw_finder_marker", "line": 80}
{"timestamp": "2025-06-11T15:44:24.791986", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "  Inner: insert=(260.67346938775506, 12.10204081632653), size=(27.224489795918366, 27.224489795918366)", "module": "qr_preview", "function": "_draw_finder_marker", "line": 81}
{"timestamp": "2025-06-11T15:44:24.792014", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "  Dot: insert=(268.3673469387755, 19.79591836734694), size=(11.83673469387755, 11.83673469387755)", "module": "qr_preview", "function": "_draw_finder_marker", "line": 82}
{"timestamp": "2025-06-11T15:44:24.792075", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Successfully added finder pattern group to SVG", "module": "qr_preview", "function": "_draw_finder_marker", "line": 89}
{"timestamp": "2025-06-11T15:44:24.792102", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Successfully drew finder pattern 2/3", "module": "qr_preview", "function": "generate_preview_svg_modular", "line": 486}
{"timestamp": "2025-06-11T15:44:24.792129", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Drawing finder pattern 3/3 at position (0, 42)", "module": "qr_preview", "function": "generate_preview_svg_modular", "line": 484}
{"timestamp": "2025-06-11T15:44:24.792159", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "_draw_finder_marker called: mx=0, my=42, module_size=5.918367346938775, shape=square, color=#000000, offset=(5, 5)", "module": "qr_preview", "function": "_draw_finder_marker", "line": 56}
{"timestamp": "2025-06-11T15:44:24.792191", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Creating finder pattern group with id='marker-0-42'", "module": "qr_preview", "function": "_draw_finder_marker", "line": 64}
{"timestamp": "2025-06-11T15:44:24.792216", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Drawing square finder pattern:", "module": "qr_preview", "function": "_draw_finder_marker", "line": 79}
{"timestamp": "2025-06-11T15:44:24.792242", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "  Outer: insert=(5.0, 253.57142857142856), size=(41.42857142857143, 41.42857142857143)", "module": "qr_preview", "function": "_draw_finder_marker", "line": 80}
{"timestamp": "2025-06-11T15:44:24.792270", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "  Inner: insert=(12.10204081632653, 260.67346938775506), size=(27.224489795918366, 27.224489795918366)", "module": "qr_preview", "function": "_draw_finder_marker", "line": 81}
{"timestamp": "2025-06-11T15:44:24.792296", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "  Dot: insert=(19.79591836734694, 268.3673469387755), size=(11.83673469387755, 11.83673469387755)", "module": "qr_preview", "function": "_draw_finder_marker", "line": 82}
{"timestamp": "2025-06-11T15:44:24.792355", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Successfully added finder pattern group to SVG", "module": "qr_preview", "function": "_draw_finder_marker", "line": 89}
{"timestamp": "2025-06-11T15:44:24.792380", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Successfully drew finder pattern 3/3", "module": "qr_preview", "function": "generate_preview_svg_modular", "line": 486}
{"timestamp": "2025-06-11T15:44:24.813607", "level": "DEBUG", "logger": "backend.services.db_utils", "message": "Query 'auth_lookup_e4ae2d8f-b32f-52d7-99cd-8a33b19652c7' completed in 0.081s", "module": "db_utils", "function": "execute_query_with_timeout", "line": 68}
{"timestamp": "2025-06-11T15:44:24.813666", "level": "DEBUG", "logger": "backend.services.db_utils", "message": "Auth query 'auth_lookup_e4ae2d8f-b32f-52d7-99cd-8a33b19652c7' successful, result format preserved", "module": "db_utils", "function": "execute_query_with_timeout", "line": 72}
{"timestamp": "2025-06-11T15:44:24.815111", "level": "DEBUG", "logger": "backend.main", "message": "[Auth] Enforced Bearer token authentication for request", "module": "main", "function": "bearer_token_auth_middleware", "line": 274}
{"timestamp": "2025-06-11T15:44:24.820138", "level": "DEBUG", "logger": "backend.services.auth", "message": "Authentication attempt with token prefix: eyJhbGciOi...", "module": "auth", "function": "get_current_user", "line": 727}
{"timestamp": "2025-06-11T15:44:24.820275", "level": "DEBUG", "logger": "backend.core.security.jwt_keys", "message": "Token contains key ID: 69b5ada0-20e9-42b1-85e8-1ec80762bb7c", "module": "jwt_keys", "function": "decode_token", "line": 188}
{"timestamp": "2025-06-11T15:44:24.820326", "level": "DEBUG", "logger": "backend.core.security.jwt_keys", "message": "Using key ID 69b5ada0-20e9-42b1-85e8-1ec80762bb7c for verification", "module": "jwt_keys", "function": "decode_token", "line": 195}
{"timestamp": "2025-06-11T15:44:24.820469", "level": "DEBUG", "logger": "backend.services.auth", "message": "JWT decoded successfully with key manager", "module": "auth", "function": "authenticate_token", "line": 683}
{"timestamp": "2025-06-11T15:44:24.821896", "level": "DEBUG", "logger": "backend.services.db_utils", "message": "Query 'auth_lookup_e4ae2d8f-b32f-52d7-99cd-8a33b19652c7' completed in 0.001s", "module": "db_utils", "function": "execute_query_with_timeout", "line": 68}
{"timestamp": "2025-06-11T15:44:24.821954", "level": "DEBUG", "logger": "backend.services.db_utils", "message": "Auth query 'auth_lookup_e4ae2d8f-b32f-52d7-99cd-8a33b19652c7' successful, result format preserved", "module": "db_utils", "function": "execute_query_with_timeout", "line": 72}
{"timestamp": "2025-06-11T15:44:24.822014", "level": "DEBUG", "logger": "backend.services.auth", "message": "Successfully authenticated user: <EMAIL>", "module": "auth", "function": "get_current_user", "line": 732}
{"timestamp": "2025-06-11T15:44:24.822045", "level": "DEBUG", "logger": "backend.services.auth", "message": "Successfully authenticated user: <EMAIL>", "module": "auth", "function": "get_current_user", "line": 738}
{"timestamp": "2025-06-11T15:44:24.823598", "level": "DEBUG", "logger": "backend.routes.qr_preview", "message": "Received QR preview SVG request: {\"qr_type\": \"scooter_unlock\", \"data\": {\"scooter_id\": \"SCOOT123\", \"unlock_code\": \"UNLOCK42\", \"title\": \"Scooter_unlock QR Code\", \"description\": \"Scan to view scooter_unlock content\"}, \"designOptions\": {\"size\": 256, \"margin\": 10, \"pattern\": \"square\", \"marker_border_style\": \"square\", \"marker_center_style\": \"square\", \"logo_size\": 48, \"logo_position\": \"center\", \"logo_opacity\": 1, \"frame_style\": \"none\", \"foreground_color\": \"#000000\", \"background_color\": \"#FFFFFF\", \"color_mode\": \"solid\", \"corner_radius\"", "module": "qr_preview", "function": "preview_qr_svg", "line": 242}
{"timestamp": "2025-06-11T15:44:24.823693", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Formatting content for QR type: qrtype.scooter_unlock, content keys: ['scooter_id', 'unlock_code', 'title', 'description']", "module": "qr_preview", "function": "_format_qr_data", "line": 554}
{"timestamp": "2025-06-11T15:44:24.854700", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Drawing 3 finder patterns at positions: [(0, 0), (42, 0), (0, 42)]", "module": "qr_preview", "function": "generate_preview_svg_modular", "line": 479}
{"timestamp": "2025-06-11T15:44:24.854775", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "QR size: 49, marker_shape: square, marker_color: #000000", "module": "qr_preview", "function": "generate_preview_svg_modular", "line": 480}
{"timestamp": "2025-06-11T15:44:24.854814", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Drawing finder pattern 1/3 at position (0, 0)", "module": "qr_preview", "function": "generate_preview_svg_modular", "line": 484}
{"timestamp": "2025-06-11T15:44:24.854884", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "_draw_finder_marker called: mx=0, my=0, module_size=5.918367346938775, shape=square, color=#000000, offset=(5, 5)", "module": "qr_preview", "function": "_draw_finder_marker", "line": 56}
{"timestamp": "2025-06-11T15:44:24.854938", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Creating finder pattern group with id='marker-0-0'", "module": "qr_preview", "function": "_draw_finder_marker", "line": 64}
{"timestamp": "2025-06-11T15:44:24.854973", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Drawing square finder pattern:", "module": "qr_preview", "function": "_draw_finder_marker", "line": 79}
{"timestamp": "2025-06-11T15:44:24.855055", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "  Outer: insert=(5.0, 5.0), size=(41.42857142857143, 41.42857142857143)", "module": "qr_preview", "function": "_draw_finder_marker", "line": 80}
{"timestamp": "2025-06-11T15:44:24.855105", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "  Inner: insert=(12.10204081632653, 12.10204081632653), size=(27.224489795918366, 27.224489795918366)", "module": "qr_preview", "function": "_draw_finder_marker", "line": 81}
{"timestamp": "2025-06-11T15:44:24.855145", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "  Dot: insert=(19.79591836734694, 19.79591836734694), size=(11.83673469387755, 11.83673469387755)", "module": "qr_preview", "function": "_draw_finder_marker", "line": 82}
{"timestamp": "2025-06-11T15:44:24.855250", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Successfully added finder pattern group to SVG", "module": "qr_preview", "function": "_draw_finder_marker", "line": 89}
{"timestamp": "2025-06-11T15:44:24.855288", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Successfully drew finder pattern 1/3", "module": "qr_preview", "function": "generate_preview_svg_modular", "line": 486}
{"timestamp": "2025-06-11T15:44:24.855322", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Drawing finder pattern 2/3 at position (42, 0)", "module": "qr_preview", "function": "generate_preview_svg_modular", "line": 484}
{"timestamp": "2025-06-11T15:44:24.855391", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "_draw_finder_marker called: mx=42, my=0, module_size=5.918367346938775, shape=square, color=#000000, offset=(5, 5)", "module": "qr_preview", "function": "_draw_finder_marker", "line": 56}
{"timestamp": "2025-06-11T15:44:24.855456", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Creating finder pattern group with id='marker-42-0'", "module": "qr_preview", "function": "_draw_finder_marker", "line": 64}
{"timestamp": "2025-06-11T15:44:24.855497", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Drawing square finder pattern:", "module": "qr_preview", "function": "_draw_finder_marker", "line": 79}
{"timestamp": "2025-06-11T15:44:24.855536", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "  Outer: insert=(253.57142857142856, 5.0), size=(41.42857142857143, 41.42857142857143)", "module": "qr_preview", "function": "_draw_finder_marker", "line": 80}
{"timestamp": "2025-06-11T15:44:24.855578", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "  Inner: insert=(260.67346938775506, 12.10204081632653), size=(27.224489795918366, 27.224489795918366)", "module": "qr_preview", "function": "_draw_finder_marker", "line": 81}
{"timestamp": "2025-06-11T15:44:24.855621", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "  Dot: insert=(268.3673469387755, 19.79591836734694), size=(11.83673469387755, 11.83673469387755)", "module": "qr_preview", "function": "_draw_finder_marker", "line": 82}
{"timestamp": "2025-06-11T15:44:24.855704", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Successfully added finder pattern group to SVG", "module": "qr_preview", "function": "_draw_finder_marker", "line": 89}
{"timestamp": "2025-06-11T15:44:24.859013", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Successfully drew finder pattern 2/3", "module": "qr_preview", "function": "generate_preview_svg_modular", "line": 486}
{"timestamp": "2025-06-11T15:44:24.859082", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Drawing finder pattern 3/3 at position (0, 42)", "module": "qr_preview", "function": "generate_preview_svg_modular", "line": 484}
{"timestamp": "2025-06-11T15:44:24.859124", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "_draw_finder_marker called: mx=0, my=42, module_size=5.918367346938775, shape=square, color=#000000, offset=(5, 5)", "module": "qr_preview", "function": "_draw_finder_marker", "line": 56}
{"timestamp": "2025-06-11T15:44:24.859182", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Creating finder pattern group with id='marker-0-42'", "module": "qr_preview", "function": "_draw_finder_marker", "line": 64}
{"timestamp": "2025-06-11T15:44:24.859217", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Drawing square finder pattern:", "module": "qr_preview", "function": "_draw_finder_marker", "line": 79}
{"timestamp": "2025-06-11T15:44:24.859253", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "  Outer: insert=(5.0, 253.57142857142856), size=(41.42857142857143, 41.42857142857143)", "module": "qr_preview", "function": "_draw_finder_marker", "line": 80}
{"timestamp": "2025-06-11T15:44:24.859287", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "  Inner: insert=(12.10204081632653, 260.67346938775506), size=(27.224489795918366, 27.224489795918366)", "module": "qr_preview", "function": "_draw_finder_marker", "line": 81}
{"timestamp": "2025-06-11T15:44:24.859318", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "  Dot: insert=(19.79591836734694, 268.3673469387755), size=(11.83673469387755, 11.83673469387755)", "module": "qr_preview", "function": "_draw_finder_marker", "line": 82}
{"timestamp": "2025-06-11T15:44:24.859403", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Successfully added finder pattern group to SVG", "module": "qr_preview", "function": "_draw_finder_marker", "line": 89}
{"timestamp": "2025-06-11T15:44:24.859436", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Successfully drew finder pattern 3/3", "module": "qr_preview", "function": "generate_preview_svg_modular", "line": 486}
{"timestamp": "2025-06-11T15:44:24.881068", "level": "DEBUG", "logger": "backend.main", "message": "[Auth] Enforced Bearer token authentication for request", "module": "main", "function": "bearer_token_auth_middleware", "line": 274}
{"timestamp": "2025-06-11T15:44:25.023619", "level": "DEBUG", "logger": "backend.main", "message": "[Auth] Preflight request handled directly in middleware", "module": "main", "function": "bearer_token_auth_middleware", "line": 218}
{"timestamp": "2025-06-11T15:44:25.024063", "level": "DEBUG", "logger": "backend.main", "message": "[Auth] Preflight request handled directly in middleware", "module": "main", "function": "bearer_token_auth_middleware", "line": 218}
{"timestamp": "2025-06-11T15:44:25.026066", "level": "DEBUG", "logger": "backend.core.security.jwt_keys", "message": "Token contains key ID: 69b5ada0-20e9-42b1-85e8-1ec80762bb7c", "module": "jwt_keys", "function": "decode_token", "line": 188}
{"timestamp": "2025-06-11T15:44:25.026149", "level": "DEBUG", "logger": "backend.core.security.jwt_keys", "message": "Using key ID 69b5ada0-20e9-42b1-85e8-1ec80762bb7c for verification", "module": "jwt_keys", "function": "decode_token", "line": 195}
{"timestamp": "2025-06-11T15:44:25.026273", "level": "DEBUG", "logger": "backend.services.auth", "message": "JWT decoded successfully with key manager", "module": "auth", "function": "authenticate_token", "line": 683}
{"timestamp": "2025-06-11T15:44:25.026968", "level": "DEBUG", "logger": "backend.core.security.jwt_keys", "message": "Token contains key ID: 69b5ada0-20e9-42b1-85e8-1ec80762bb7c", "module": "jwt_keys", "function": "decode_token", "line": 188}
{"timestamp": "2025-06-11T15:44:25.027003", "level": "DEBUG", "logger": "backend.core.security.jwt_keys", "message": "Using key ID 69b5ada0-20e9-42b1-85e8-1ec80762bb7c for verification", "module": "jwt_keys", "function": "decode_token", "line": 195}
{"timestamp": "2025-06-11T15:44:25.027087", "level": "DEBUG", "logger": "backend.services.auth", "message": "JWT decoded successfully with key manager", "module": "auth", "function": "authenticate_token", "line": 683}
{"timestamp": "2025-06-11T15:44:25.030034", "level": "DEBUG", "logger": "backend.services.db_utils", "message": "Query 'auth_lookup_e4ae2d8f-b32f-52d7-99cd-8a33b19652c7' completed in 0.003s", "module": "db_utils", "function": "execute_query_with_timeout", "line": 68}
{"timestamp": "2025-06-11T15:44:25.030087", "level": "DEBUG", "logger": "backend.services.db_utils", "message": "Auth query 'auth_lookup_e4ae2d8f-b32f-52d7-99cd-8a33b19652c7' successful, result format preserved", "module": "db_utils", "function": "execute_query_with_timeout", "line": 72}
{"timestamp": "2025-06-11T15:44:25.030714", "level": "DEBUG", "logger": "backend.services.db_utils", "message": "Query 'auth_lookup_e4ae2d8f-b32f-52d7-99cd-8a33b19652c7' completed in 0.004s", "module": "db_utils", "function": "execute_query_with_timeout", "line": 68}
{"timestamp": "2025-06-11T15:44:25.030771", "level": "DEBUG", "logger": "backend.services.db_utils", "message": "Auth query 'auth_lookup_e4ae2d8f-b32f-52d7-99cd-8a33b19652c7' successful, result format preserved", "module": "db_utils", "function": "execute_query_with_timeout", "line": 72}
{"timestamp": "2025-06-11T15:44:25.039142", "level": "DEBUG", "logger": "backend.services.auth", "message": "Authentication attempt with token prefix: eyJhbGciOi...", "module": "auth", "function": "get_current_user", "line": 727}
{"timestamp": "2025-06-11T15:44:25.039249", "level": "DEBUG", "logger": "backend.core.security.jwt_keys", "message": "Token contains key ID: 69b5ada0-20e9-42b1-85e8-1ec80762bb7c", "module": "jwt_keys", "function": "decode_token", "line": 188}
{"timestamp": "2025-06-11T15:44:25.039283", "level": "DEBUG", "logger": "backend.core.security.jwt_keys", "message": "Using key ID 69b5ada0-20e9-42b1-85e8-1ec80762bb7c for verification", "module": "jwt_keys", "function": "decode_token", "line": 195}
{"timestamp": "2025-06-11T15:44:25.039404", "level": "DEBUG", "logger": "backend.services.auth", "message": "JWT decoded successfully with key manager", "module": "auth", "function": "authenticate_token", "line": 683}
{"timestamp": "2025-06-11T15:44:25.040067", "level": "DEBUG", "logger": "backend.services.auth", "message": "Authentication attempt with token prefix: eyJhbGciOi...", "module": "auth", "function": "get_current_user", "line": 727}
{"timestamp": "2025-06-11T15:44:25.040146", "level": "DEBUG", "logger": "backend.core.security.jwt_keys", "message": "Token contains key ID: 69b5ada0-20e9-42b1-85e8-1ec80762bb7c", "module": "jwt_keys", "function": "decode_token", "line": 188}
{"timestamp": "2025-06-11T15:44:25.040184", "level": "DEBUG", "logger": "backend.core.security.jwt_keys", "message": "Using key ID 69b5ada0-20e9-42b1-85e8-1ec80762bb7c for verification", "module": "jwt_keys", "function": "decode_token", "line": 195}
{"timestamp": "2025-06-11T15:44:25.040312", "level": "DEBUG", "logger": "backend.services.auth", "message": "JWT decoded successfully with key manager", "module": "auth", "function": "authenticate_token", "line": 683}
{"timestamp": "2025-06-11T15:44:25.043742", "level": "DEBUG", "logger": "backend.services.db_utils", "message": "Query 'auth_lookup_e4ae2d8f-b32f-52d7-99cd-8a33b19652c7' completed in 0.003s", "module": "db_utils", "function": "execute_query_with_timeout", "line": 68}
{"timestamp": "2025-06-11T15:44:25.043789", "level": "DEBUG", "logger": "backend.services.db_utils", "message": "Auth query 'auth_lookup_e4ae2d8f-b32f-52d7-99cd-8a33b19652c7' successful, result format preserved", "module": "db_utils", "function": "execute_query_with_timeout", "line": 72}
{"timestamp": "2025-06-11T15:44:25.043844", "level": "DEBUG", "logger": "backend.services.auth", "message": "Successfully authenticated user: <EMAIL>", "module": "auth", "function": "get_current_user", "line": 732}
{"timestamp": "2025-06-11T15:44:25.043880", "level": "DEBUG", "logger": "backend.services.auth", "message": "Successfully authenticated user: <EMAIL>", "module": "auth", "function": "get_current_user", "line": 738}
{"timestamp": "2025-06-11T15:44:25.043954", "level": "DEBUG", "logger": "backend.services.db_utils", "message": "Query 'auth_lookup_e4ae2d8f-b32f-52d7-99cd-8a33b19652c7' completed in 0.004s", "module": "db_utils", "function": "execute_query_with_timeout", "line": 68}
{"timestamp": "2025-06-11T15:44:25.044006", "level": "DEBUG", "logger": "backend.services.db_utils", "message": "Auth query 'auth_lookup_e4ae2d8f-b32f-52d7-99cd-8a33b19652c7' successful, result format preserved", "module": "db_utils", "function": "execute_query_with_timeout", "line": 72}
{"timestamp": "2025-06-11T15:44:25.044047", "level": "DEBUG", "logger": "backend.services.auth", "message": "Successfully authenticated user: <EMAIL>", "module": "auth", "function": "get_current_user", "line": 732}
{"timestamp": "2025-06-11T15:44:25.044082", "level": "DEBUG", "logger": "backend.services.auth", "message": "Successfully authenticated user: <EMAIL>", "module": "auth", "function": "get_current_user", "line": 738}
{"timestamp": "2025-06-11T15:44:25.045852", "level": "DEBUG", "logger": "backend.routes.qr_preview", "message": "Received QR preview SVG request: {\"qr_type\": \"scooter_unlock\", \"data\": {\"scooter_id\": \"SCOOT123\", \"unlock_code\": \"UNLOCK42\", \"title\": \"Scooter_unlock QR Code\", \"description\": \"Scan to view scooter_unlock content\"}, \"designOptions\": {\"size\": 256, \"margin\": 10, \"pattern\": \"square\", \"marker_border_style\": \"square\", \"marker_center_style\": \"square\", \"logo_size\": 48, \"logo_position\": \"center\", \"logo_opacity\": 1, \"frame_style\": \"none\", \"foreground_color\": \"#000000\", \"background_color\": \"#FFFFFF\", \"color_mode\": \"solid\", \"corner_radius\"", "module": "qr_preview", "function": "preview_qr_svg", "line": 242}
{"timestamp": "2025-06-11T15:44:25.045962", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Formatting content for QR type: qrtype.scooter_unlock, content keys: ['scooter_id', 'unlock_code', 'title', 'description']", "module": "qr_preview", "function": "_format_qr_data", "line": 554}
{"timestamp": "2025-06-11T15:44:25.077017", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Drawing 3 finder patterns at positions: [(0, 0), (42, 0), (0, 42)]", "module": "qr_preview", "function": "generate_preview_svg_modular", "line": 479}
{"timestamp": "2025-06-11T15:44:25.077076", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "QR size: 49, marker_shape: square, marker_color: #000000", "module": "qr_preview", "function": "generate_preview_svg_modular", "line": 480}
{"timestamp": "2025-06-11T15:44:25.077120", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Drawing finder pattern 1/3 at position (0, 0)", "module": "qr_preview", "function": "generate_preview_svg_modular", "line": 484}
{"timestamp": "2025-06-11T15:44:25.077495", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "_draw_finder_marker called: mx=0, my=0, module_size=5.918367346938775, shape=square, color=#000000, offset=(5, 5)", "module": "qr_preview", "function": "_draw_finder_marker", "line": 56}
{"timestamp": "2025-06-11T15:44:25.077549", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Creating finder pattern group with id='marker-0-0'", "module": "qr_preview", "function": "_draw_finder_marker", "line": 64}
{"timestamp": "2025-06-11T15:44:25.077584", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Drawing square finder pattern:", "module": "qr_preview", "function": "_draw_finder_marker", "line": 79}
{"timestamp": "2025-06-11T15:44:25.077622", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "  Outer: insert=(5.0, 5.0), size=(41.42857142857143, 41.42857142857143)", "module": "qr_preview", "function": "_draw_finder_marker", "line": 80}
{"timestamp": "2025-06-11T15:44:25.077654", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "  Inner: insert=(12.10204081632653, 12.10204081632653), size=(27.224489795918366, 27.224489795918366)", "module": "qr_preview", "function": "_draw_finder_marker", "line": 81}
{"timestamp": "2025-06-11T15:44:25.077686", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "  Dot: insert=(19.79591836734694, 19.79591836734694), size=(11.83673469387755, 11.83673469387755)", "module": "qr_preview", "function": "_draw_finder_marker", "line": 82}
{"timestamp": "2025-06-11T15:44:25.077764", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Successfully added finder pattern group to SVG", "module": "qr_preview", "function": "_draw_finder_marker", "line": 89}
{"timestamp": "2025-06-11T15:44:25.077795", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Successfully drew finder pattern 1/3", "module": "qr_preview", "function": "generate_preview_svg_modular", "line": 486}
{"timestamp": "2025-06-11T15:44:25.077826", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Drawing finder pattern 2/3 at position (42, 0)", "module": "qr_preview", "function": "generate_preview_svg_modular", "line": 484}
{"timestamp": "2025-06-11T15:44:25.077858", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "_draw_finder_marker called: mx=42, my=0, module_size=5.918367346938775, shape=square, color=#000000, offset=(5, 5)", "module": "qr_preview", "function": "_draw_finder_marker", "line": 56}
{"timestamp": "2025-06-11T15:44:25.077896", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Creating finder pattern group with id='marker-42-0'", "module": "qr_preview", "function": "_draw_finder_marker", "line": 64}
{"timestamp": "2025-06-11T15:44:25.077925", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Drawing square finder pattern:", "module": "qr_preview", "function": "_draw_finder_marker", "line": 79}
{"timestamp": "2025-06-11T15:44:25.077956", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "  Outer: insert=(253.57142857142856, 5.0), size=(41.42857142857143, 41.42857142857143)", "module": "qr_preview", "function": "_draw_finder_marker", "line": 80}
{"timestamp": "2025-06-11T15:44:25.077987", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "  Inner: insert=(260.67346938775506, 12.10204081632653), size=(27.224489795918366, 27.224489795918366)", "module": "qr_preview", "function": "_draw_finder_marker", "line": 81}
{"timestamp": "2025-06-11T15:44:25.078021", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "  Dot: insert=(268.3673469387755, 19.79591836734694), size=(11.83673469387755, 11.83673469387755)", "module": "qr_preview", "function": "_draw_finder_marker", "line": 82}
{"timestamp": "2025-06-11T15:44:25.078130", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Successfully added finder pattern group to SVG", "module": "qr_preview", "function": "_draw_finder_marker", "line": 89}
{"timestamp": "2025-06-11T15:44:25.078192", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Successfully drew finder pattern 2/3", "module": "qr_preview", "function": "generate_preview_svg_modular", "line": 486}
{"timestamp": "2025-06-11T15:44:25.078223", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Drawing finder pattern 3/3 at position (0, 42)", "module": "qr_preview", "function": "generate_preview_svg_modular", "line": 484}
{"timestamp": "2025-06-11T15:44:25.078255", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "_draw_finder_marker called: mx=0, my=42, module_size=5.918367346938775, shape=square, color=#000000, offset=(5, 5)", "module": "qr_preview", "function": "_draw_finder_marker", "line": 56}
{"timestamp": "2025-06-11T15:44:25.078465", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Creating finder pattern group with id='marker-0-42'", "module": "qr_preview", "function": "_draw_finder_marker", "line": 64}
{"timestamp": "2025-06-11T15:44:25.078515", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Drawing square finder pattern:", "module": "qr_preview", "function": "_draw_finder_marker", "line": 79}
{"timestamp": "2025-06-11T15:44:25.078560", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "  Outer: insert=(5.0, 253.57142857142856), size=(41.42857142857143, 41.42857142857143)", "module": "qr_preview", "function": "_draw_finder_marker", "line": 80}
{"timestamp": "2025-06-11T15:44:25.078601", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "  Inner: insert=(12.10204081632653, 260.67346938775506), size=(27.224489795918366, 27.224489795918366)", "module": "qr_preview", "function": "_draw_finder_marker", "line": 81}
{"timestamp": "2025-06-11T15:44:25.078639", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "  Dot: insert=(19.79591836734694, 268.3673469387755), size=(11.83673469387755, 11.83673469387755)", "module": "qr_preview", "function": "_draw_finder_marker", "line": 82}
{"timestamp": "2025-06-11T15:44:25.078738", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Successfully added finder pattern group to SVG", "module": "qr_preview", "function": "_draw_finder_marker", "line": 89}
{"timestamp": "2025-06-11T15:44:25.078780", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Successfully drew finder pattern 3/3", "module": "qr_preview", "function": "generate_preview_svg_modular", "line": 486}
{"timestamp": "2025-06-11T15:44:25.101358", "level": "DEBUG", "logger": "backend.main", "message": "[Auth] Enforced Bearer token authentication for request", "module": "main", "function": "bearer_token_auth_middleware", "line": 274}
{"timestamp": "2025-06-11T15:44:25.103012", "level": "DEBUG", "logger": "backend.routes.qr_preview", "message": "Received QR preview SVG request: {\"qr_type\": \"scooter_unlock\", \"data\": {\"scooter_id\": \"SCOOT123\", \"unlock_code\": \"UNLOCK42\", \"title\": \"Scooter_unlock QR Code\", \"description\": \"Scan to view scooter_unlock content\"}, \"designOptions\": {\"size\": 256, \"margin\": 10, \"pattern\": \"square\", \"error_correction\": \"M\", \"svg_render_dpi\": 300, \"svg_optimize\": true}, \"dimensions\": 300}", "module": "qr_preview", "function": "preview_qr_svg", "line": 242}
{"timestamp": "2025-06-11T15:44:25.103114", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Formatting content for QR type: qrtype.scooter_unlock, content keys: ['scooter_id', 'unlock_code', 'title', 'description']", "module": "qr_preview", "function": "_format_qr_data", "line": 554}
{"timestamp": "2025-06-11T15:44:25.132591", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Drawing 3 finder patterns at positions: [(0, 0), (42, 0), (0, 42)]", "module": "qr_preview", "function": "generate_preview_svg_modular", "line": 479}
{"timestamp": "2025-06-11T15:44:25.132649", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "QR size: 49, marker_shape: square, marker_color: #000000", "module": "qr_preview", "function": "generate_preview_svg_modular", "line": 480}
{"timestamp": "2025-06-11T15:44:25.132701", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Drawing finder pattern 1/3 at position (0, 0)", "module": "qr_preview", "function": "generate_preview_svg_modular", "line": 484}
{"timestamp": "2025-06-11T15:44:25.132743", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "_draw_finder_marker called: mx=0, my=0, module_size=5.918367346938775, shape=square, color=#000000, offset=(5, 5)", "module": "qr_preview", "function": "_draw_finder_marker", "line": 56}
{"timestamp": "2025-06-11T15:44:25.132790", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Creating finder pattern group with id='marker-0-0'", "module": "qr_preview", "function": "_draw_finder_marker", "line": 64}
{"timestamp": "2025-06-11T15:44:25.132902", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Drawing square finder pattern:", "module": "qr_preview", "function": "_draw_finder_marker", "line": 79}
{"timestamp": "2025-06-11T15:44:25.132991", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "  Outer: insert=(5.0, 5.0), size=(41.42857142857143, 41.42857142857143)", "module": "qr_preview", "function": "_draw_finder_marker", "line": 80}
{"timestamp": "2025-06-11T15:44:25.133039", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "  Inner: insert=(12.10204081632653, 12.10204081632653), size=(27.224489795918366, 27.224489795918366)", "module": "qr_preview", "function": "_draw_finder_marker", "line": 81}
{"timestamp": "2025-06-11T15:44:25.133081", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "  Dot: insert=(19.79591836734694, 19.79591836734694), size=(11.83673469387755, 11.83673469387755)", "module": "qr_preview", "function": "_draw_finder_marker", "line": 82}
{"timestamp": "2025-06-11T15:44:25.133200", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Successfully added finder pattern group to SVG", "module": "qr_preview", "function": "_draw_finder_marker", "line": 89}
{"timestamp": "2025-06-11T15:44:25.133237", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Successfully drew finder pattern 1/3", "module": "qr_preview", "function": "generate_preview_svg_modular", "line": 486}
{"timestamp": "2025-06-11T15:44:25.133273", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Drawing finder pattern 2/3 at position (42, 0)", "module": "qr_preview", "function": "generate_preview_svg_modular", "line": 484}
{"timestamp": "2025-06-11T15:44:25.133312", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "_draw_finder_marker called: mx=42, my=0, module_size=5.918367346938775, shape=square, color=#000000, offset=(5, 5)", "module": "qr_preview", "function": "_draw_finder_marker", "line": 56}
{"timestamp": "2025-06-11T15:44:25.133360", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Creating finder pattern group with id='marker-42-0'", "module": "qr_preview", "function": "_draw_finder_marker", "line": 64}
{"timestamp": "2025-06-11T15:44:25.133396", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Drawing square finder pattern:", "module": "qr_preview", "function": "_draw_finder_marker", "line": 79}
{"timestamp": "2025-06-11T15:44:25.133432", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "  Outer: insert=(253.57142857142856, 5.0), size=(41.42857142857143, 41.42857142857143)", "module": "qr_preview", "function": "_draw_finder_marker", "line": 80}
{"timestamp": "2025-06-11T15:44:25.133468", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "  Inner: insert=(260.67346938775506, 12.10204081632653), size=(27.224489795918366, 27.224489795918366)", "module": "qr_preview", "function": "_draw_finder_marker", "line": 81}
{"timestamp": "2025-06-11T15:44:25.133522", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "  Dot: insert=(268.3673469387755, 19.79591836734694), size=(11.83673469387755, 11.83673469387755)", "module": "qr_preview", "function": "_draw_finder_marker", "line": 82}
{"timestamp": "2025-06-11T15:44:25.133596", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Successfully added finder pattern group to SVG", "module": "qr_preview", "function": "_draw_finder_marker", "line": 89}
{"timestamp": "2025-06-11T15:44:25.133630", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Successfully drew finder pattern 2/3", "module": "qr_preview", "function": "generate_preview_svg_modular", "line": 486}
{"timestamp": "2025-06-11T15:44:25.133663", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Drawing finder pattern 3/3 at position (0, 42)", "module": "qr_preview", "function": "generate_preview_svg_modular", "line": 484}
{"timestamp": "2025-06-11T15:44:25.133698", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "_draw_finder_marker called: mx=0, my=42, module_size=5.918367346938775, shape=square, color=#000000, offset=(5, 5)", "module": "qr_preview", "function": "_draw_finder_marker", "line": 56}
{"timestamp": "2025-06-11T15:44:25.133737", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Creating finder pattern group with id='marker-0-42'", "module": "qr_preview", "function": "_draw_finder_marker", "line": 64}
{"timestamp": "2025-06-11T15:44:25.133771", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Drawing square finder pattern:", "module": "qr_preview", "function": "_draw_finder_marker", "line": 79}
{"timestamp": "2025-06-11T15:44:25.133803", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "  Outer: insert=(5.0, 253.57142857142856), size=(41.42857142857143, 41.42857142857143)", "module": "qr_preview", "function": "_draw_finder_marker", "line": 80}
{"timestamp": "2025-06-11T15:44:25.133837", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "  Inner: insert=(12.10204081632653, 260.67346938775506), size=(27.224489795918366, 27.224489795918366)", "module": "qr_preview", "function": "_draw_finder_marker", "line": 81}
{"timestamp": "2025-06-11T15:44:25.133872", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "  Dot: insert=(19.79591836734694, 268.3673469387755), size=(11.83673469387755, 11.83673469387755)", "module": "qr_preview", "function": "_draw_finder_marker", "line": 82}
{"timestamp": "2025-06-11T15:44:25.133937", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Successfully added finder pattern group to SVG", "module": "qr_preview", "function": "_draw_finder_marker", "line": 89}
{"timestamp": "2025-06-11T15:44:25.133969", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Successfully drew finder pattern 3/3", "module": "qr_preview", "function": "generate_preview_svg_modular", "line": 486}
{"timestamp": "2025-06-11T15:44:25.155973", "level": "DEBUG", "logger": "backend.main", "message": "[Auth] Enforced Bearer token authentication for request", "module": "main", "function": "bearer_token_auth_middleware", "line": 274}
{"timestamp": "2025-06-11T15:44:25.453455", "level": "DEBUG", "logger": "backend.main", "message": "[Auth] Enforced Bearer token authentication for request", "module": "main", "function": "bearer_token_auth_middleware", "line": 274}
{"timestamp": "2025-06-11T15:44:42.426458", "level": "DEBUG", "logger": "backend.core.security.jwt_keys", "message": "Token contains key ID: 69b5ada0-20e9-42b1-85e8-1ec80762bb7c", "module": "jwt_keys", "function": "decode_token", "line": 188}
{"timestamp": "2025-06-11T15:44:42.426542", "level": "DEBUG", "logger": "backend.core.security.jwt_keys", "message": "Using key ID 69b5ada0-20e9-42b1-85e8-1ec80762bb7c for verification", "module": "jwt_keys", "function": "decode_token", "line": 195}
{"timestamp": "2025-06-11T15:44:42.426993", "level": "DEBUG", "logger": "backend.services.auth", "message": "JWT decoded successfully with key manager", "module": "auth", "function": "authenticate_token", "line": 683}
{"timestamp": "2025-06-11T15:44:42.434068", "level": "DEBUG", "logger": "backend.core.security.jwt_keys", "message": "Token contains key ID: 69b5ada0-20e9-42b1-85e8-1ec80762bb7c", "module": "jwt_keys", "function": "decode_token", "line": 188}
{"timestamp": "2025-06-11T15:44:42.434122", "level": "DEBUG", "logger": "backend.core.security.jwt_keys", "message": "Using key ID 69b5ada0-20e9-42b1-85e8-1ec80762bb7c for verification", "module": "jwt_keys", "function": "decode_token", "line": 195}
{"timestamp": "2025-06-11T15:44:42.434268", "level": "DEBUG", "logger": "backend.services.auth", "message": "JWT decoded successfully with key manager", "module": "auth", "function": "authenticate_token", "line": 683}
{"timestamp": "2025-06-11T15:44:42.442220", "level": "DEBUG", "logger": "backend.services.db_utils", "message": "Query 'auth_lookup_e4ae2d8f-b32f-52d7-99cd-8a33b19652c7' completed in 0.010s", "module": "db_utils", "function": "execute_query_with_timeout", "line": 68}
{"timestamp": "2025-06-11T15:44:42.442285", "level": "DEBUG", "logger": "backend.services.db_utils", "message": "Auth query 'auth_lookup_e4ae2d8f-b32f-52d7-99cd-8a33b19652c7' successful, result format preserved", "module": "db_utils", "function": "execute_query_with_timeout", "line": 72}
{"timestamp": "2025-06-11T15:44:42.442644", "level": "DEBUG", "logger": "backend.services.db_utils", "message": "Query 'auth_lookup_e4ae2d8f-b32f-52d7-99cd-8a33b19652c7' completed in 0.008s", "module": "db_utils", "function": "execute_query_with_timeout", "line": 68}
{"timestamp": "2025-06-11T15:44:42.442682", "level": "DEBUG", "logger": "backend.services.db_utils", "message": "Auth query 'auth_lookup_e4ae2d8f-b32f-52d7-99cd-8a33b19652c7' successful, result format preserved", "module": "db_utils", "function": "execute_query_with_timeout", "line": 72}
{"timestamp": "2025-06-11T15:44:42.460856", "level": "DEBUG", "logger": "backend.services.auth", "message": "Authentication attempt with token prefix: eyJhbGciOi...", "module": "auth", "function": "get_current_user", "line": 727}
{"timestamp": "2025-06-11T15:44:42.460961", "level": "DEBUG", "logger": "backend.core.security.jwt_keys", "message": "Token contains key ID: 69b5ada0-20e9-42b1-85e8-1ec80762bb7c", "module": "jwt_keys", "function": "decode_token", "line": 188}
{"timestamp": "2025-06-11T15:44:42.460996", "level": "DEBUG", "logger": "backend.core.security.jwt_keys", "message": "Using key ID 69b5ada0-20e9-42b1-85e8-1ec80762bb7c for verification", "module": "jwt_keys", "function": "decode_token", "line": 195}
{"timestamp": "2025-06-11T15:44:42.461124", "level": "DEBUG", "logger": "backend.services.auth", "message": "JWT decoded successfully with key manager", "module": "auth", "function": "authenticate_token", "line": 683}
{"timestamp": "2025-06-11T15:44:42.462409", "level": "DEBUG", "logger": "backend.services.auth", "message": "Authentication attempt with token prefix: eyJhbGciOi...", "module": "auth", "function": "get_current_user", "line": 727}
{"timestamp": "2025-06-11T15:44:42.462491", "level": "DEBUG", "logger": "backend.core.security.jwt_keys", "message": "Token contains key ID: 69b5ada0-20e9-42b1-85e8-1ec80762bb7c", "module": "jwt_keys", "function": "decode_token", "line": 188}
{"timestamp": "2025-06-11T15:44:42.462976", "level": "DEBUG", "logger": "backend.core.security.jwt_keys", "message": "Using key ID 69b5ada0-20e9-42b1-85e8-1ec80762bb7c for verification", "module": "jwt_keys", "function": "decode_token", "line": 195}
{"timestamp": "2025-06-11T15:44:42.463121", "level": "DEBUG", "logger": "backend.services.auth", "message": "JWT decoded successfully with key manager", "module": "auth", "function": "authenticate_token", "line": 683}
{"timestamp": "2025-06-11T15:44:42.464567", "level": "DEBUG", "logger": "backend.services.db_utils", "message": "Query 'auth_lookup_e4ae2d8f-b32f-52d7-99cd-8a33b19652c7' completed in 0.003s", "module": "db_utils", "function": "execute_query_with_timeout", "line": 68}
{"timestamp": "2025-06-11T15:44:42.464623", "level": "DEBUG", "logger": "backend.services.db_utils", "message": "Auth query 'auth_lookup_e4ae2d8f-b32f-52d7-99cd-8a33b19652c7' successful, result format preserved", "module": "db_utils", "function": "execute_query_with_timeout", "line": 72}
{"timestamp": "2025-06-11T15:44:42.464720", "level": "DEBUG", "logger": "backend.services.auth", "message": "Successfully authenticated user: <EMAIL>", "module": "auth", "function": "get_current_user", "line": 732}
{"timestamp": "2025-06-11T15:44:42.464769", "level": "DEBUG", "logger": "backend.services.auth", "message": "Successfully authenticated user: <EMAIL>", "module": "auth", "function": "get_current_user", "line": 738}
{"timestamp": "2025-06-11T15:44:42.465787", "level": "DEBUG", "logger": "backend.services.db_utils", "message": "Query 'auth_lookup_e4ae2d8f-b32f-52d7-99cd-8a33b19652c7' completed in 0.003s", "module": "db_utils", "function": "execute_query_with_timeout", "line": 68}
{"timestamp": "2025-06-11T15:44:42.465829", "level": "DEBUG", "logger": "backend.services.db_utils", "message": "Auth query 'auth_lookup_e4ae2d8f-b32f-52d7-99cd-8a33b19652c7' successful, result format preserved", "module": "db_utils", "function": "execute_query_with_timeout", "line": 72}
{"timestamp": "2025-06-11T15:44:42.465878", "level": "DEBUG", "logger": "backend.services.auth", "message": "Successfully authenticated user: <EMAIL>", "module": "auth", "function": "get_current_user", "line": 732}
{"timestamp": "2025-06-11T15:44:42.465914", "level": "DEBUG", "logger": "backend.services.auth", "message": "Successfully authenticated user: <EMAIL>", "module": "auth", "function": "get_current_user", "line": 738}
{"timestamp": "2025-06-11T15:44:42.467826", "level": "DEBUG", "logger": "backend.routes.qr_preview", "message": "Received QR preview SVG request: {\"qr_type\": \"scooter_unlock\", \"data\": {\"scooter_id\": \"SCOOT123\", \"unlock_code\": \"UNLOCK42\", \"title\": \"Scooter_unlock QR Code\", \"description\": \"Scan to view scooter_unlock content\"}, \"designOptions\": {\"size\": 256, \"margin\": 10, \"pattern\": \"square\", \"error_correction\": \"M\", \"svg_render_dpi\": 300, \"svg_optimize\": true}, \"dimensions\": 300}", "module": "qr_preview", "function": "preview_qr_svg", "line": 242}
{"timestamp": "2025-06-11T15:44:42.468105", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Formatting content for QR type: qrtype.scooter_unlock, content keys: ['scooter_id', 'unlock_code', 'title', 'description']", "module": "qr_preview", "function": "_format_qr_data", "line": 554}
{"timestamp": "2025-06-11T15:44:42.498423", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Drawing 3 finder patterns at positions: [(0, 0), (42, 0), (0, 42)]", "module": "qr_preview", "function": "generate_preview_svg_modular", "line": 479}
{"timestamp": "2025-06-11T15:44:42.498478", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "QR size: 49, marker_shape: square, marker_color: #000000", "module": "qr_preview", "function": "generate_preview_svg_modular", "line": 480}
{"timestamp": "2025-06-11T15:44:42.498516", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Drawing finder pattern 1/3 at position (0, 0)", "module": "qr_preview", "function": "generate_preview_svg_modular", "line": 484}
{"timestamp": "2025-06-11T15:44:42.498567", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "_draw_finder_marker called: mx=0, my=0, module_size=5.918367346938775, shape=square, color=#000000, offset=(5, 5)", "module": "qr_preview", "function": "_draw_finder_marker", "line": 56}
{"timestamp": "2025-06-11T15:44:42.498629", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Creating finder pattern group with id='marker-0-0'", "module": "qr_preview", "function": "_draw_finder_marker", "line": 64}
{"timestamp": "2025-06-11T15:44:42.498660", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Drawing square finder pattern:", "module": "qr_preview", "function": "_draw_finder_marker", "line": 79}
{"timestamp": "2025-06-11T15:44:42.498694", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "  Outer: insert=(5.0, 5.0), size=(41.42857142857143, 41.42857142857143)", "module": "qr_preview", "function": "_draw_finder_marker", "line": 80}
{"timestamp": "2025-06-11T15:44:42.498726", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "  Inner: insert=(12.10204081632653, 12.10204081632653), size=(27.224489795918366, 27.224489795918366)", "module": "qr_preview", "function": "_draw_finder_marker", "line": 81}
{"timestamp": "2025-06-11T15:44:42.498757", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "  Dot: insert=(19.79591836734694, 19.79591836734694), size=(11.83673469387755, 11.83673469387755)", "module": "qr_preview", "function": "_draw_finder_marker", "line": 82}
{"timestamp": "2025-06-11T15:44:42.498833", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Successfully added finder pattern group to SVG", "module": "qr_preview", "function": "_draw_finder_marker", "line": 89}
{"timestamp": "2025-06-11T15:44:42.498863", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Successfully drew finder pattern 1/3", "module": "qr_preview", "function": "generate_preview_svg_modular", "line": 486}
{"timestamp": "2025-06-11T15:44:42.498892", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Drawing finder pattern 2/3 at position (42, 0)", "module": "qr_preview", "function": "generate_preview_svg_modular", "line": 484}
{"timestamp": "2025-06-11T15:44:42.498923", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "_draw_finder_marker called: mx=42, my=0, module_size=5.918367346938775, shape=square, color=#000000, offset=(5, 5)", "module": "qr_preview", "function": "_draw_finder_marker", "line": 56}
{"timestamp": "2025-06-11T15:44:42.498960", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Creating finder pattern group with id='marker-42-0'", "module": "qr_preview", "function": "_draw_finder_marker", "line": 64}
{"timestamp": "2025-06-11T15:44:42.498988", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Drawing square finder pattern:", "module": "qr_preview", "function": "_draw_finder_marker", "line": 79}
{"timestamp": "2025-06-11T15:44:42.499029", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "  Outer: insert=(253.57142857142856, 5.0), size=(41.42857142857143, 41.42857142857143)", "module": "qr_preview", "function": "_draw_finder_marker", "line": 80}
{"timestamp": "2025-06-11T15:44:42.499059", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "  Inner: insert=(260.67346938775506, 12.10204081632653), size=(27.224489795918366, 27.224489795918366)", "module": "qr_preview", "function": "_draw_finder_marker", "line": 81}
{"timestamp": "2025-06-11T15:44:42.499087", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "  Dot: insert=(268.3673469387755, 19.79591836734694), size=(11.83673469387755, 11.83673469387755)", "module": "qr_preview", "function": "_draw_finder_marker", "line": 82}
{"timestamp": "2025-06-11T15:44:42.499150", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Successfully added finder pattern group to SVG", "module": "qr_preview", "function": "_draw_finder_marker", "line": 89}
{"timestamp": "2025-06-11T15:44:42.499177", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Successfully drew finder pattern 2/3", "module": "qr_preview", "function": "generate_preview_svg_modular", "line": 486}
{"timestamp": "2025-06-11T15:44:42.499205", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Drawing finder pattern 3/3 at position (0, 42)", "module": "qr_preview", "function": "generate_preview_svg_modular", "line": 484}
{"timestamp": "2025-06-11T15:44:42.499236", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "_draw_finder_marker called: mx=0, my=42, module_size=5.918367346938775, shape=square, color=#000000, offset=(5, 5)", "module": "qr_preview", "function": "_draw_finder_marker", "line": 56}
{"timestamp": "2025-06-11T15:44:42.499274", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Creating finder pattern group with id='marker-0-42'", "module": "qr_preview", "function": "_draw_finder_marker", "line": 64}
{"timestamp": "2025-06-11T15:44:42.499301", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Drawing square finder pattern:", "module": "qr_preview", "function": "_draw_finder_marker", "line": 79}
{"timestamp": "2025-06-11T15:44:42.499331", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "  Outer: insert=(5.0, 253.57142857142856), size=(41.42857142857143, 41.42857142857143)", "module": "qr_preview", "function": "_draw_finder_marker", "line": 80}
{"timestamp": "2025-06-11T15:44:42.499361", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "  Inner: insert=(12.10204081632653, 260.67346938775506), size=(27.224489795918366, 27.224489795918366)", "module": "qr_preview", "function": "_draw_finder_marker", "line": 81}
{"timestamp": "2025-06-11T15:44:42.499398", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "  Dot: insert=(19.79591836734694, 268.3673469387755), size=(11.83673469387755, 11.83673469387755)", "module": "qr_preview", "function": "_draw_finder_marker", "line": 82}
{"timestamp": "2025-06-11T15:44:42.499459", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Successfully added finder pattern group to SVG", "module": "qr_preview", "function": "_draw_finder_marker", "line": 89}
{"timestamp": "2025-06-11T15:44:42.499488", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Successfully drew finder pattern 3/3", "module": "qr_preview", "function": "generate_preview_svg_modular", "line": 486}
{"timestamp": "2025-06-11T15:44:42.519746", "level": "DEBUG", "logger": "backend.routes.qr_preview", "message": "Received QR preview SVG request: {\"qr_type\": \"scooter_unlock\", \"data\": {\"scooter_id\": \"SCOOT123\", \"unlock_code\": \"UNLOCK42\", \"title\": \"Scooter_unlock QR Code\", \"description\": \"Scan to view scooter_unlock content\"}, \"designOptions\": {\"size\": 256, \"margin\": 10, \"pattern\": \"horizontal\", \"marker_border_style\": \"square\", \"marker_center_style\": \"square\", \"logo_size\": 48, \"logo_position\": \"center\", \"logo_opacity\": 1, \"frame_style\": \"none\", \"foreground_color\": \"#000000\", \"background_color\": \"#FFFFFF\", \"color_mode\": \"solid\", \"corner_rad", "module": "qr_preview", "function": "preview_qr_svg", "line": 242}
{"timestamp": "2025-06-11T15:44:42.519831", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Formatting content for QR type: qrtype.scooter_unlock, content keys: ['scooter_id', 'unlock_code', 'title', 'description']", "module": "qr_preview", "function": "_format_qr_data", "line": 554}
{"timestamp": "2025-06-11T15:44:42.549168", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Drawing 3 finder patterns at positions: [(0, 0), (42, 0), (0, 42)]", "module": "qr_preview", "function": "generate_preview_svg_modular", "line": 479}
{"timestamp": "2025-06-11T15:44:42.549232", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "QR size: 49, marker_shape: square, marker_color: #000000", "module": "qr_preview", "function": "generate_preview_svg_modular", "line": 480}
{"timestamp": "2025-06-11T15:44:42.549275", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Drawing finder pattern 1/3 at position (0, 0)", "module": "qr_preview", "function": "generate_preview_svg_modular", "line": 484}
{"timestamp": "2025-06-11T15:44:42.549321", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "_draw_finder_marker called: mx=0, my=0, module_size=5.918367346938775, shape=square, color=#000000, offset=(5, 5)", "module": "qr_preview", "function": "_draw_finder_marker", "line": 56}
{"timestamp": "2025-06-11T15:44:42.549372", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Creating finder pattern group with id='marker-0-0'", "module": "qr_preview", "function": "_draw_finder_marker", "line": 64}
{"timestamp": "2025-06-11T15:44:42.549409", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Drawing square finder pattern:", "module": "qr_preview", "function": "_draw_finder_marker", "line": 79}
{"timestamp": "2025-06-11T15:44:42.549447", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "  Outer: insert=(5.0, 5.0), size=(41.42857142857143, 41.42857142857143)", "module": "qr_preview", "function": "_draw_finder_marker", "line": 80}
{"timestamp": "2025-06-11T15:44:42.549484", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "  Inner: insert=(12.10204081632653, 12.10204081632653), size=(27.224489795918366, 27.224489795918366)", "module": "qr_preview", "function": "_draw_finder_marker", "line": 81}
{"timestamp": "2025-06-11T15:44:42.549520", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "  Dot: insert=(19.79591836734694, 19.79591836734694), size=(11.83673469387755, 11.83673469387755)", "module": "qr_preview", "function": "_draw_finder_marker", "line": 82}
{"timestamp": "2025-06-11T15:44:42.549594", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Successfully added finder pattern group to SVG", "module": "qr_preview", "function": "_draw_finder_marker", "line": 89}
{"timestamp": "2025-06-11T15:44:42.549630", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Successfully drew finder pattern 1/3", "module": "qr_preview", "function": "generate_preview_svg_modular", "line": 486}
{"timestamp": "2025-06-11T15:44:42.549666", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Drawing finder pattern 2/3 at position (42, 0)", "module": "qr_preview", "function": "generate_preview_svg_modular", "line": 484}
{"timestamp": "2025-06-11T15:44:42.549701", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "_draw_finder_marker called: mx=42, my=0, module_size=5.918367346938775, shape=square, color=#000000, offset=(5, 5)", "module": "qr_preview", "function": "_draw_finder_marker", "line": 56}
{"timestamp": "2025-06-11T15:44:42.549740", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Creating finder pattern group with id='marker-42-0'", "module": "qr_preview", "function": "_draw_finder_marker", "line": 64}
{"timestamp": "2025-06-11T15:44:42.549773", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Drawing square finder pattern:", "module": "qr_preview", "function": "_draw_finder_marker", "line": 79}
{"timestamp": "2025-06-11T15:44:42.549808", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "  Outer: insert=(253.57142857142856, 5.0), size=(41.42857142857143, 41.42857142857143)", "module": "qr_preview", "function": "_draw_finder_marker", "line": 80}
{"timestamp": "2025-06-11T15:44:42.549845", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "  Inner: insert=(260.67346938775506, 12.10204081632653), size=(27.224489795918366, 27.224489795918366)", "module": "qr_preview", "function": "_draw_finder_marker", "line": 81}
{"timestamp": "2025-06-11T15:44:42.549880", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "  Dot: insert=(268.3673469387755, 19.79591836734694), size=(11.83673469387755, 11.83673469387755)", "module": "qr_preview", "function": "_draw_finder_marker", "line": 82}
{"timestamp": "2025-06-11T15:44:42.549947", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Successfully added finder pattern group to SVG", "module": "qr_preview", "function": "_draw_finder_marker", "line": 89}
{"timestamp": "2025-06-11T15:44:42.549982", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Successfully drew finder pattern 2/3", "module": "qr_preview", "function": "generate_preview_svg_modular", "line": 486}
{"timestamp": "2025-06-11T15:44:42.550016", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Drawing finder pattern 3/3 at position (0, 42)", "module": "qr_preview", "function": "generate_preview_svg_modular", "line": 484}
{"timestamp": "2025-06-11T15:44:42.550052", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "_draw_finder_marker called: mx=0, my=42, module_size=5.918367346938775, shape=square, color=#000000, offset=(5, 5)", "module": "qr_preview", "function": "_draw_finder_marker", "line": 56}
{"timestamp": "2025-06-11T15:44:42.550094", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Creating finder pattern group with id='marker-0-42'", "module": "qr_preview", "function": "_draw_finder_marker", "line": 64}
{"timestamp": "2025-06-11T15:44:42.550127", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Drawing square finder pattern:", "module": "qr_preview", "function": "_draw_finder_marker", "line": 79}
{"timestamp": "2025-06-11T15:44:42.550162", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "  Outer: insert=(5.0, 253.57142857142856), size=(41.42857142857143, 41.42857142857143)", "module": "qr_preview", "function": "_draw_finder_marker", "line": 80}
{"timestamp": "2025-06-11T15:44:42.550198", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "  Inner: insert=(12.10204081632653, 260.67346938775506), size=(27.224489795918366, 27.224489795918366)", "module": "qr_preview", "function": "_draw_finder_marker", "line": 81}
{"timestamp": "2025-06-11T15:44:42.550233", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "  Dot: insert=(19.79591836734694, 268.3673469387755), size=(11.83673469387755, 11.83673469387755)", "module": "qr_preview", "function": "_draw_finder_marker", "line": 82}
{"timestamp": "2025-06-11T15:44:42.550299", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Successfully added finder pattern group to SVG", "module": "qr_preview", "function": "_draw_finder_marker", "line": 89}
{"timestamp": "2025-06-11T15:44:42.550332", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Successfully drew finder pattern 3/3", "module": "qr_preview", "function": "generate_preview_svg_modular", "line": 486}
{"timestamp": "2025-06-11T15:44:42.575279", "level": "DEBUG", "logger": "backend.main", "message": "[Auth] Enforced Bearer token authentication for request", "module": "main", "function": "bearer_token_auth_middleware", "line": 274}
{"timestamp": "2025-06-11T15:44:42.582996", "level": "DEBUG", "logger": "backend.main", "message": "[Auth] Enforced Bearer token authentication for request", "module": "main", "function": "bearer_token_auth_middleware", "line": 274}
{"timestamp": "2025-06-11T15:44:47.454503", "level": "DEBUG", "logger": "backend.core.security.jwt_keys", "message": "Token contains key ID: 69b5ada0-20e9-42b1-85e8-1ec80762bb7c", "module": "jwt_keys", "function": "decode_token", "line": 188}
{"timestamp": "2025-06-11T15:44:47.454565", "level": "DEBUG", "logger": "backend.core.security.jwt_keys", "message": "Using key ID 69b5ada0-20e9-42b1-85e8-1ec80762bb7c for verification", "module": "jwt_keys", "function": "decode_token", "line": 195}
{"timestamp": "2025-06-11T15:44:47.454696", "level": "DEBUG", "logger": "backend.services.auth", "message": "JWT decoded successfully with key manager", "module": "auth", "function": "authenticate_token", "line": 683}
{"timestamp": "2025-06-11T15:44:47.454909", "level": "DEBUG", "logger": "backend.core.security.jwt_keys", "message": "Token contains key ID: 69b5ada0-20e9-42b1-85e8-1ec80762bb7c", "module": "jwt_keys", "function": "decode_token", "line": 188}
{"timestamp": "2025-06-11T15:44:47.454942", "level": "DEBUG", "logger": "backend.core.security.jwt_keys", "message": "Using key ID 69b5ada0-20e9-42b1-85e8-1ec80762bb7c for verification", "module": "jwt_keys", "function": "decode_token", "line": 195}
{"timestamp": "2025-06-11T15:44:47.455299", "level": "DEBUG", "logger": "backend.services.auth", "message": "JWT decoded successfully with key manager", "module": "auth", "function": "authenticate_token", "line": 683}
{"timestamp": "2025-06-11T15:44:47.456983", "level": "DEBUG", "logger": "backend.services.db_utils", "message": "Query 'auth_lookup_e4ae2d8f-b32f-52d7-99cd-8a33b19652c7' completed in 0.002s", "module": "db_utils", "function": "execute_query_with_timeout", "line": 68}
{"timestamp": "2025-06-11T15:44:47.457050", "level": "DEBUG", "logger": "backend.services.db_utils", "message": "Auth query 'auth_lookup_e4ae2d8f-b32f-52d7-99cd-8a33b19652c7' successful, result format preserved", "module": "db_utils", "function": "execute_query_with_timeout", "line": 72}
{"timestamp": "2025-06-11T15:44:47.457268", "level": "DEBUG", "logger": "backend.services.db_utils", "message": "Query 'auth_lookup_e4ae2d8f-b32f-52d7-99cd-8a33b19652c7' completed in 0.002s", "module": "db_utils", "function": "execute_query_with_timeout", "line": 68}
{"timestamp": "2025-06-11T15:44:47.457303", "level": "DEBUG", "logger": "backend.services.db_utils", "message": "Auth query 'auth_lookup_e4ae2d8f-b32f-52d7-99cd-8a33b19652c7' successful, result format preserved", "module": "db_utils", "function": "execute_query_with_timeout", "line": 72}
{"timestamp": "2025-06-11T15:44:47.460908", "level": "DEBUG", "logger": "backend.services.auth", "message": "Authentication attempt with token prefix: eyJhbGciOi...", "module": "auth", "function": "get_current_user", "line": 727}
{"timestamp": "2025-06-11T15:44:47.461015", "level": "DEBUG", "logger": "backend.core.security.jwt_keys", "message": "Token contains key ID: 69b5ada0-20e9-42b1-85e8-1ec80762bb7c", "module": "jwt_keys", "function": "decode_token", "line": 188}
{"timestamp": "2025-06-11T15:44:47.461052", "level": "DEBUG", "logger": "backend.core.security.jwt_keys", "message": "Using key ID 69b5ada0-20e9-42b1-85e8-1ec80762bb7c for verification", "module": "jwt_keys", "function": "decode_token", "line": 195}
{"timestamp": "2025-06-11T15:44:47.461163", "level": "DEBUG", "logger": "backend.services.auth", "message": "JWT decoded successfully with key manager", "module": "auth", "function": "authenticate_token", "line": 683}
{"timestamp": "2025-06-11T15:44:47.461918", "level": "DEBUG", "logger": "backend.services.auth", "message": "Authentication attempt with token prefix: eyJhbGciOi...", "module": "auth", "function": "get_current_user", "line": 727}
{"timestamp": "2025-06-11T15:44:47.462019", "level": "DEBUG", "logger": "backend.core.security.jwt_keys", "message": "Token contains key ID: 69b5ada0-20e9-42b1-85e8-1ec80762bb7c", "module": "jwt_keys", "function": "decode_token", "line": 188}
{"timestamp": "2025-06-11T15:44:47.462054", "level": "DEBUG", "logger": "backend.core.security.jwt_keys", "message": "Using key ID 69b5ada0-20e9-42b1-85e8-1ec80762bb7c for verification", "module": "jwt_keys", "function": "decode_token", "line": 195}
{"timestamp": "2025-06-11T15:44:47.462135", "level": "DEBUG", "logger": "backend.services.auth", "message": "JWT decoded successfully with key manager", "module": "auth", "function": "authenticate_token", "line": 683}
{"timestamp": "2025-06-11T15:44:47.463402", "level": "DEBUG", "logger": "backend.services.db_utils", "message": "Query 'auth_lookup_e4ae2d8f-b32f-52d7-99cd-8a33b19652c7' completed in 0.002s", "module": "db_utils", "function": "execute_query_with_timeout", "line": 68}
{"timestamp": "2025-06-11T15:44:47.463464", "level": "DEBUG", "logger": "backend.services.db_utils", "message": "Auth query 'auth_lookup_e4ae2d8f-b32f-52d7-99cd-8a33b19652c7' successful, result format preserved", "module": "db_utils", "function": "execute_query_with_timeout", "line": 72}
{"timestamp": "2025-06-11T15:44:47.463527", "level": "DEBUG", "logger": "backend.services.auth", "message": "Successfully authenticated user: <EMAIL>", "module": "auth", "function": "get_current_user", "line": 732}
{"timestamp": "2025-06-11T15:44:47.463566", "level": "DEBUG", "logger": "backend.services.auth", "message": "Successfully authenticated user: <EMAIL>", "module": "auth", "function": "get_current_user", "line": 738}
{"timestamp": "2025-06-11T15:44:47.464323", "level": "DEBUG", "logger": "backend.services.db_utils", "message": "Query 'auth_lookup_e4ae2d8f-b32f-52d7-99cd-8a33b19652c7' completed in 0.002s", "module": "db_utils", "function": "execute_query_with_timeout", "line": 68}
{"timestamp": "2025-06-11T15:44:47.464368", "level": "DEBUG", "logger": "backend.services.db_utils", "message": "Auth query 'auth_lookup_e4ae2d8f-b32f-52d7-99cd-8a33b19652c7' successful, result format preserved", "module": "db_utils", "function": "execute_query_with_timeout", "line": 72}
{"timestamp": "2025-06-11T15:44:47.464420", "level": "DEBUG", "logger": "backend.services.auth", "message": "Successfully authenticated user: <EMAIL>", "module": "auth", "function": "get_current_user", "line": 732}
{"timestamp": "2025-06-11T15:44:47.464456", "level": "DEBUG", "logger": "backend.services.auth", "message": "Successfully authenticated user: <EMAIL>", "module": "auth", "function": "get_current_user", "line": 738}
{"timestamp": "2025-06-11T15:44:47.465618", "level": "DEBUG", "logger": "backend.routes.qr_preview", "message": "Received QR preview SVG request: {\"qr_type\": \"scooter_unlock\", \"data\": {\"scooter_id\": \"SCOOT123\", \"unlock_code\": \"UNLOCK42\", \"title\": \"Scooter_unlock QR Code\", \"description\": \"Scan to view scooter_unlock content\"}, \"designOptions\": {\"size\": 256, \"margin\": 10, \"pattern\": \"square\", \"error_correction\": \"M\", \"svg_render_dpi\": 300, \"svg_optimize\": true}, \"dimensions\": 300}", "module": "qr_preview", "function": "preview_qr_svg", "line": 242}
{"timestamp": "2025-06-11T15:44:47.465735", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Formatting content for QR type: qrtype.scooter_unlock, content keys: ['scooter_id', 'unlock_code', 'title', 'description']", "module": "qr_preview", "function": "_format_qr_data", "line": 554}
{"timestamp": "2025-06-11T15:44:47.495488", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Drawing 3 finder patterns at positions: [(0, 0), (42, 0), (0, 42)]", "module": "qr_preview", "function": "generate_preview_svg_modular", "line": 479}
{"timestamp": "2025-06-11T15:44:47.495559", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "QR size: 49, marker_shape: square, marker_color: #000000", "module": "qr_preview", "function": "generate_preview_svg_modular", "line": 480}
{"timestamp": "2025-06-11T15:44:47.495605", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Drawing finder pattern 1/3 at position (0, 0)", "module": "qr_preview", "function": "generate_preview_svg_modular", "line": 484}
{"timestamp": "2025-06-11T15:44:47.495721", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "_draw_finder_marker called: mx=0, my=0, module_size=5.918367346938775, shape=square, color=#000000, offset=(5, 5)", "module": "qr_preview", "function": "_draw_finder_marker", "line": 56}
{"timestamp": "2025-06-11T15:44:47.495799", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Creating finder pattern group with id='marker-0-0'", "module": "qr_preview", "function": "_draw_finder_marker", "line": 64}
{"timestamp": "2025-06-11T15:44:47.495851", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Drawing square finder pattern:", "module": "qr_preview", "function": "_draw_finder_marker", "line": 79}
{"timestamp": "2025-06-11T15:44:47.495896", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "  Outer: insert=(5.0, 5.0), size=(41.42857142857143, 41.42857142857143)", "module": "qr_preview", "function": "_draw_finder_marker", "line": 80}
{"timestamp": "2025-06-11T15:44:47.495938", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "  Inner: insert=(12.10204081632653, 12.10204081632653), size=(27.224489795918366, 27.224489795918366)", "module": "qr_preview", "function": "_draw_finder_marker", "line": 81}
{"timestamp": "2025-06-11T15:44:47.496208", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "  Dot: insert=(19.79591836734694, 19.79591836734694), size=(11.83673469387755, 11.83673469387755)", "module": "qr_preview", "function": "_draw_finder_marker", "line": 82}
{"timestamp": "2025-06-11T15:44:47.496294", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Successfully added finder pattern group to SVG", "module": "qr_preview", "function": "_draw_finder_marker", "line": 89}
{"timestamp": "2025-06-11T15:44:47.496328", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Successfully drew finder pattern 1/3", "module": "qr_preview", "function": "generate_preview_svg_modular", "line": 486}
{"timestamp": "2025-06-11T15:44:47.496361", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Drawing finder pattern 2/3 at position (42, 0)", "module": "qr_preview", "function": "generate_preview_svg_modular", "line": 484}
{"timestamp": "2025-06-11T15:44:47.496395", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "_draw_finder_marker called: mx=42, my=0, module_size=5.918367346938775, shape=square, color=#000000, offset=(5, 5)", "module": "qr_preview", "function": "_draw_finder_marker", "line": 56}
{"timestamp": "2025-06-11T15:44:47.496439", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Creating finder pattern group with id='marker-42-0'", "module": "qr_preview", "function": "_draw_finder_marker", "line": 64}
{"timestamp": "2025-06-11T15:44:47.496470", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Drawing square finder pattern:", "module": "qr_preview", "function": "_draw_finder_marker", "line": 79}
{"timestamp": "2025-06-11T15:44:47.496498", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "  Outer: insert=(253.57142857142856, 5.0), size=(41.42857142857143, 41.42857142857143)", "module": "qr_preview", "function": "_draw_finder_marker", "line": 80}
{"timestamp": "2025-06-11T15:44:47.496555", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "  Inner: insert=(260.67346938775506, 12.10204081632653), size=(27.224489795918366, 27.224489795918366)", "module": "qr_preview", "function": "_draw_finder_marker", "line": 81}
{"timestamp": "2025-06-11T15:44:47.498582", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "  Dot: insert=(268.3673469387755, 19.79591836734694), size=(11.83673469387755, 11.83673469387755)", "module": "qr_preview", "function": "_draw_finder_marker", "line": 82}
{"timestamp": "2025-06-11T15:44:47.498710", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Successfully added finder pattern group to SVG", "module": "qr_preview", "function": "_draw_finder_marker", "line": 89}
{"timestamp": "2025-06-11T15:44:47.498757", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Successfully drew finder pattern 2/3", "module": "qr_preview", "function": "generate_preview_svg_modular", "line": 486}
{"timestamp": "2025-06-11T15:44:47.498798", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Drawing finder pattern 3/3 at position (0, 42)", "module": "qr_preview", "function": "generate_preview_svg_modular", "line": 484}
{"timestamp": "2025-06-11T15:44:47.498841", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "_draw_finder_marker called: mx=0, my=42, module_size=5.918367346938775, shape=square, color=#000000, offset=(5, 5)", "module": "qr_preview", "function": "_draw_finder_marker", "line": 56}
{"timestamp": "2025-06-11T15:44:47.498890", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Creating finder pattern group with id='marker-0-42'", "module": "qr_preview", "function": "_draw_finder_marker", "line": 64}
{"timestamp": "2025-06-11T15:44:47.498925", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Drawing square finder pattern:", "module": "qr_preview", "function": "_draw_finder_marker", "line": 79}
{"timestamp": "2025-06-11T15:44:47.498967", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "  Outer: insert=(5.0, 253.57142857142856), size=(41.42857142857143, 41.42857142857143)", "module": "qr_preview", "function": "_draw_finder_marker", "line": 80}
{"timestamp": "2025-06-11T15:44:47.499005", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "  Inner: insert=(12.10204081632653, 260.67346938775506), size=(27.224489795918366, 27.224489795918366)", "module": "qr_preview", "function": "_draw_finder_marker", "line": 81}
{"timestamp": "2025-06-11T15:44:47.499043", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "  Dot: insert=(19.79591836734694, 268.3673469387755), size=(11.83673469387755, 11.83673469387755)", "module": "qr_preview", "function": "_draw_finder_marker", "line": 82}
{"timestamp": "2025-06-11T15:44:47.499116", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Successfully added finder pattern group to SVG", "module": "qr_preview", "function": "_draw_finder_marker", "line": 89}
{"timestamp": "2025-06-11T15:44:47.499144", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Successfully drew finder pattern 3/3", "module": "qr_preview", "function": "generate_preview_svg_modular", "line": 486}
{"timestamp": "2025-06-11T15:44:47.519897", "level": "DEBUG", "logger": "backend.routes.qr_preview", "message": "Received QR preview SVG request: {\"qr_type\": \"scooter_unlock\", \"data\": {\"scooter_id\": \"SCOOT123\", \"unlock_code\": \"UNLOCK42\", \"title\": \"Scooter_unlock QR Code\", \"description\": \"Scan to view scooter_unlock content\"}, \"designOptions\": {\"size\": 256, \"margin\": 10, \"pattern\": \"vertical\", \"marker_border_style\": \"square\", \"marker_center_style\": \"square\", \"logo_size\": 48, \"logo_position\": \"center\", \"logo_opacity\": 1, \"frame_style\": \"none\", \"foreground_color\": \"#000000\", \"background_color\": \"#FFFFFF\", \"color_mode\": \"solid\", \"corner_radiu", "module": "qr_preview", "function": "preview_qr_svg", "line": 242}
{"timestamp": "2025-06-11T15:44:47.519987", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Formatting content for QR type: qrtype.scooter_unlock, content keys: ['scooter_id', 'unlock_code', 'title', 'description']", "module": "qr_preview", "function": "_format_qr_data", "line": 554}
{"timestamp": "2025-06-11T15:44:47.548764", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Drawing 3 finder patterns at positions: [(0, 0), (42, 0), (0, 42)]", "module": "qr_preview", "function": "generate_preview_svg_modular", "line": 479}
{"timestamp": "2025-06-11T15:44:47.548822", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "QR size: 49, marker_shape: square, marker_color: #000000", "module": "qr_preview", "function": "generate_preview_svg_modular", "line": 480}
{"timestamp": "2025-06-11T15:44:47.548857", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Drawing finder pattern 1/3 at position (0, 0)", "module": "qr_preview", "function": "generate_preview_svg_modular", "line": 484}
{"timestamp": "2025-06-11T15:44:47.549193", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "_draw_finder_marker called: mx=0, my=0, module_size=5.918367346938775, shape=square, color=#000000, offset=(5, 5)", "module": "qr_preview", "function": "_draw_finder_marker", "line": 56}
{"timestamp": "2025-06-11T15:44:47.549262", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Creating finder pattern group with id='marker-0-0'", "module": "qr_preview", "function": "_draw_finder_marker", "line": 64}
{"timestamp": "2025-06-11T15:44:47.549299", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Drawing square finder pattern:", "module": "qr_preview", "function": "_draw_finder_marker", "line": 79}
{"timestamp": "2025-06-11T15:44:47.549330", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "  Outer: insert=(5.0, 5.0), size=(41.42857142857143, 41.42857142857143)", "module": "qr_preview", "function": "_draw_finder_marker", "line": 80}
{"timestamp": "2025-06-11T15:44:47.549364", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "  Inner: insert=(12.10204081632653, 12.10204081632653), size=(27.224489795918366, 27.224489795918366)", "module": "qr_preview", "function": "_draw_finder_marker", "line": 81}
{"timestamp": "2025-06-11T15:44:47.550181", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "  Dot: insert=(19.79591836734694, 19.79591836734694), size=(11.83673469387755, 11.83673469387755)", "module": "qr_preview", "function": "_draw_finder_marker", "line": 82}
{"timestamp": "2025-06-11T15:44:47.550261", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Successfully added finder pattern group to SVG", "module": "qr_preview", "function": "_draw_finder_marker", "line": 89}
{"timestamp": "2025-06-11T15:44:47.550290", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Successfully drew finder pattern 1/3", "module": "qr_preview", "function": "generate_preview_svg_modular", "line": 486}
{"timestamp": "2025-06-11T15:44:47.550318", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Drawing finder pattern 2/3 at position (42, 0)", "module": "qr_preview", "function": "generate_preview_svg_modular", "line": 484}
{"timestamp": "2025-06-11T15:44:47.550348", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "_draw_finder_marker called: mx=42, my=0, module_size=5.918367346938775, shape=square, color=#000000, offset=(5, 5)", "module": "qr_preview", "function": "_draw_finder_marker", "line": 56}
{"timestamp": "2025-06-11T15:44:47.551588", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Creating finder pattern group with id='marker-42-0'", "module": "qr_preview", "function": "_draw_finder_marker", "line": 64}
{"timestamp": "2025-06-11T15:44:47.551628", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Drawing square finder pattern:", "module": "qr_preview", "function": "_draw_finder_marker", "line": 79}
{"timestamp": "2025-06-11T15:44:47.551659", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "  Outer: insert=(253.57142857142856, 5.0), size=(41.42857142857143, 41.42857142857143)", "module": "qr_preview", "function": "_draw_finder_marker", "line": 80}
{"timestamp": "2025-06-11T15:44:47.551688", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "  Inner: insert=(260.67346938775506, 12.10204081632653), size=(27.224489795918366, 27.224489795918366)", "module": "qr_preview", "function": "_draw_finder_marker", "line": 81}
{"timestamp": "2025-06-11T15:44:47.551822", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "  Dot: insert=(268.3673469387755, 19.79591836734694), size=(11.83673469387755, 11.83673469387755)", "module": "qr_preview", "function": "_draw_finder_marker", "line": 82}
{"timestamp": "2025-06-11T15:44:47.551901", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Successfully added finder pattern group to SVG", "module": "qr_preview", "function": "_draw_finder_marker", "line": 89}
{"timestamp": "2025-06-11T15:44:47.551935", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Successfully drew finder pattern 2/3", "module": "qr_preview", "function": "generate_preview_svg_modular", "line": 486}
{"timestamp": "2025-06-11T15:44:47.551965", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Drawing finder pattern 3/3 at position (0, 42)", "module": "qr_preview", "function": "generate_preview_svg_modular", "line": 484}
{"timestamp": "2025-06-11T15:44:47.551998", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "_draw_finder_marker called: mx=0, my=42, module_size=5.918367346938775, shape=square, color=#000000, offset=(5, 5)", "module": "qr_preview", "function": "_draw_finder_marker", "line": 56}
{"timestamp": "2025-06-11T15:44:47.552033", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Creating finder pattern group with id='marker-0-42'", "module": "qr_preview", "function": "_draw_finder_marker", "line": 64}
{"timestamp": "2025-06-11T15:44:47.552064", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Drawing square finder pattern:", "module": "qr_preview", "function": "_draw_finder_marker", "line": 79}
{"timestamp": "2025-06-11T15:44:47.552142", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "  Outer: insert=(5.0, 253.57142857142856), size=(41.42857142857143, 41.42857142857143)", "module": "qr_preview", "function": "_draw_finder_marker", "line": 80}
{"timestamp": "2025-06-11T15:44:47.552174", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "  Inner: insert=(12.10204081632653, 260.67346938775506), size=(27.224489795918366, 27.224489795918366)", "module": "qr_preview", "function": "_draw_finder_marker", "line": 81}
{"timestamp": "2025-06-11T15:44:47.552204", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "  Dot: insert=(19.79591836734694, 268.3673469387755), size=(11.83673469387755, 11.83673469387755)", "module": "qr_preview", "function": "_draw_finder_marker", "line": 82}
{"timestamp": "2025-06-11T15:44:47.552327", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Successfully added finder pattern group to SVG", "module": "qr_preview", "function": "_draw_finder_marker", "line": 89}
{"timestamp": "2025-06-11T15:44:47.552359", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Successfully drew finder pattern 3/3", "module": "qr_preview", "function": "generate_preview_svg_modular", "line": 486}
{"timestamp": "2025-06-11T15:44:47.575307", "level": "DEBUG", "logger": "backend.main", "message": "[Auth] Enforced Bearer token authentication for request", "module": "main", "function": "bearer_token_auth_middleware", "line": 274}
{"timestamp": "2025-06-11T15:44:47.577391", "level": "DEBUG", "logger": "backend.main", "message": "[Auth] Enforced Bearer token authentication for request", "module": "main", "function": "bearer_token_auth_middleware", "line": 274}
{"timestamp": "2025-06-11T15:44:49.077184", "level": "DEBUG", "logger": "backend.core.security.jwt_keys", "message": "Token contains key ID: 69b5ada0-20e9-42b1-85e8-1ec80762bb7c", "module": "jwt_keys", "function": "decode_token", "line": 188}
{"timestamp": "2025-06-11T15:44:49.077505", "level": "DEBUG", "logger": "backend.core.security.jwt_keys", "message": "Using key ID 69b5ada0-20e9-42b1-85e8-1ec80762bb7c for verification", "module": "jwt_keys", "function": "decode_token", "line": 195}
{"timestamp": "2025-06-11T15:44:49.077625", "level": "DEBUG", "logger": "backend.services.auth", "message": "JWT decoded successfully with key manager", "module": "auth", "function": "authenticate_token", "line": 683}
{"timestamp": "2025-06-11T15:44:49.078322", "level": "DEBUG", "logger": "backend.core.security.jwt_keys", "message": "Token contains key ID: 69b5ada0-20e9-42b1-85e8-1ec80762bb7c", "module": "jwt_keys", "function": "decode_token", "line": 188}
{"timestamp": "2025-06-11T15:44:49.078360", "level": "DEBUG", "logger": "backend.core.security.jwt_keys", "message": "Using key ID 69b5ada0-20e9-42b1-85e8-1ec80762bb7c for verification", "module": "jwt_keys", "function": "decode_token", "line": 195}
{"timestamp": "2025-06-11T15:44:49.080880", "level": "DEBUG", "logger": "backend.services.auth", "message": "JWT decoded successfully with key manager", "module": "auth", "function": "authenticate_token", "line": 683}
{"timestamp": "2025-06-11T15:44:49.082830", "level": "DEBUG", "logger": "backend.services.db_utils", "message": "Query 'auth_lookup_e4ae2d8f-b32f-52d7-99cd-8a33b19652c7' completed in 0.005s", "module": "db_utils", "function": "execute_query_with_timeout", "line": 68}
{"timestamp": "2025-06-11T15:44:49.082873", "level": "DEBUG", "logger": "backend.services.db_utils", "message": "Auth query 'auth_lookup_e4ae2d8f-b32f-52d7-99cd-8a33b19652c7' successful, result format preserved", "module": "db_utils", "function": "execute_query_with_timeout", "line": 72}
{"timestamp": "2025-06-11T15:44:49.082992", "level": "DEBUG", "logger": "backend.services.db_utils", "message": "Query 'auth_lookup_e4ae2d8f-b32f-52d7-99cd-8a33b19652c7' completed in 0.002s", "module": "db_utils", "function": "execute_query_with_timeout", "line": 68}
{"timestamp": "2025-06-11T15:44:49.083025", "level": "DEBUG", "logger": "backend.services.db_utils", "message": "Auth query 'auth_lookup_e4ae2d8f-b32f-52d7-99cd-8a33b19652c7' successful, result format preserved", "module": "db_utils", "function": "execute_query_with_timeout", "line": 72}
{"timestamp": "2025-06-11T15:44:49.086231", "level": "DEBUG", "logger": "backend.services.auth", "message": "Authentication attempt with token prefix: eyJhbGciOi...", "module": "auth", "function": "get_current_user", "line": 727}
{"timestamp": "2025-06-11T15:44:49.086307", "level": "DEBUG", "logger": "backend.core.security.jwt_keys", "message": "Token contains key ID: 69b5ada0-20e9-42b1-85e8-1ec80762bb7c", "module": "jwt_keys", "function": "decode_token", "line": 188}
{"timestamp": "2025-06-11T15:44:49.086339", "level": "DEBUG", "logger": "backend.core.security.jwt_keys", "message": "Using key ID 69b5ada0-20e9-42b1-85e8-1ec80762bb7c for verification", "module": "jwt_keys", "function": "decode_token", "line": 195}
{"timestamp": "2025-06-11T15:44:49.086442", "level": "DEBUG", "logger": "backend.services.auth", "message": "JWT decoded successfully with key manager", "module": "auth", "function": "authenticate_token", "line": 683}
{"timestamp": "2025-06-11T15:44:49.086750", "level": "DEBUG", "logger": "backend.services.auth", "message": "Authentication attempt with token prefix: eyJhbGciOi...", "module": "auth", "function": "get_current_user", "line": 727}
{"timestamp": "2025-06-11T15:44:49.086803", "level": "DEBUG", "logger": "backend.core.security.jwt_keys", "message": "Token contains key ID: 69b5ada0-20e9-42b1-85e8-1ec80762bb7c", "module": "jwt_keys", "function": "decode_token", "line": 188}
{"timestamp": "2025-06-11T15:44:49.086832", "level": "DEBUG", "logger": "backend.core.security.jwt_keys", "message": "Using key ID 69b5ada0-20e9-42b1-85e8-1ec80762bb7c for verification", "module": "jwt_keys", "function": "decode_token", "line": 195}
{"timestamp": "2025-06-11T15:44:49.092353", "level": "DEBUG", "logger": "backend.services.auth", "message": "JWT decoded successfully with key manager", "module": "auth", "function": "authenticate_token", "line": 683}
{"timestamp": "2025-06-11T15:44:49.093757", "level": "DEBUG", "logger": "backend.services.db_utils", "message": "Query 'auth_lookup_e4ae2d8f-b32f-52d7-99cd-8a33b19652c7' completed in 0.007s", "module": "db_utils", "function": "execute_query_with_timeout", "line": 68}
{"timestamp": "2025-06-11T15:44:49.093801", "level": "DEBUG", "logger": "backend.services.db_utils", "message": "Auth query 'auth_lookup_e4ae2d8f-b32f-52d7-99cd-8a33b19652c7' successful, result format preserved", "module": "db_utils", "function": "execute_query_with_timeout", "line": 72}
{"timestamp": "2025-06-11T15:44:49.093853", "level": "DEBUG", "logger": "backend.services.auth", "message": "Successfully authenticated user: <EMAIL>", "module": "auth", "function": "get_current_user", "line": 732}
{"timestamp": "2025-06-11T15:44:49.093889", "level": "DEBUG", "logger": "backend.services.auth", "message": "Successfully authenticated user: <EMAIL>", "module": "auth", "function": "get_current_user", "line": 738}
{"timestamp": "2025-06-11T15:44:49.098720", "level": "DEBUG", "logger": "backend.services.db_utils", "message": "Query 'auth_lookup_e4ae2d8f-b32f-52d7-99cd-8a33b19652c7' completed in 0.003s", "module": "db_utils", "function": "execute_query_with_timeout", "line": 68}
{"timestamp": "2025-06-11T15:44:49.098786", "level": "DEBUG", "logger": "backend.services.db_utils", "message": "Auth query 'auth_lookup_e4ae2d8f-b32f-52d7-99cd-8a33b19652c7' successful, result format preserved", "module": "db_utils", "function": "execute_query_with_timeout", "line": 72}
{"timestamp": "2025-06-11T15:44:49.098843", "level": "DEBUG", "logger": "backend.services.auth", "message": "Successfully authenticated user: <EMAIL>", "module": "auth", "function": "get_current_user", "line": 732}
{"timestamp": "2025-06-11T15:44:49.098878", "level": "DEBUG", "logger": "backend.services.auth", "message": "Successfully authenticated user: <EMAIL>", "module": "auth", "function": "get_current_user", "line": 738}
{"timestamp": "2025-06-11T15:44:49.100243", "level": "DEBUG", "logger": "backend.routes.qr_preview", "message": "Received QR preview SVG request: {\"qr_type\": \"scooter_unlock\", \"data\": {\"scooter_id\": \"SCOOT123\", \"unlock_code\": \"UNLOCK42\", \"title\": \"Scooter_unlock QR Code\", \"description\": \"Scan to view scooter_unlock content\"}, \"designOptions\": {\"size\": 256, \"margin\": 10, \"pattern\": \"square\", \"error_correction\": \"M\", \"svg_render_dpi\": 300, \"svg_optimize\": true}, \"dimensions\": 300}", "module": "qr_preview", "function": "preview_qr_svg", "line": 242}
{"timestamp": "2025-06-11T15:44:49.100322", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Formatting content for QR type: qrtype.scooter_unlock, content keys: ['scooter_id', 'unlock_code', 'title', 'description']", "module": "qr_preview", "function": "_format_qr_data", "line": 554}
{"timestamp": "2025-06-11T15:44:49.129988", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Drawing 3 finder patterns at positions: [(0, 0), (42, 0), (0, 42)]", "module": "qr_preview", "function": "generate_preview_svg_modular", "line": 479}
{"timestamp": "2025-06-11T15:44:49.130052", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "QR size: 49, marker_shape: square, marker_color: #000000", "module": "qr_preview", "function": "generate_preview_svg_modular", "line": 480}
{"timestamp": "2025-06-11T15:44:49.130091", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Drawing finder pattern 1/3 at position (0, 0)", "module": "qr_preview", "function": "generate_preview_svg_modular", "line": 484}
{"timestamp": "2025-06-11T15:44:49.130135", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "_draw_finder_marker called: mx=0, my=0, module_size=5.918367346938775, shape=square, color=#000000, offset=(5, 5)", "module": "qr_preview", "function": "_draw_finder_marker", "line": 56}
{"timestamp": "2025-06-11T15:44:49.130178", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Creating finder pattern group with id='marker-0-0'", "module": "qr_preview", "function": "_draw_finder_marker", "line": 64}
{"timestamp": "2025-06-11T15:44:49.130210", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Drawing square finder pattern:", "module": "qr_preview", "function": "_draw_finder_marker", "line": 79}
{"timestamp": "2025-06-11T15:44:49.130240", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "  Outer: insert=(5.0, 5.0), size=(41.42857142857143, 41.42857142857143)", "module": "qr_preview", "function": "_draw_finder_marker", "line": 80}
{"timestamp": "2025-06-11T15:44:49.130270", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "  Inner: insert=(12.10204081632653, 12.10204081632653), size=(27.224489795918366, 27.224489795918366)", "module": "qr_preview", "function": "_draw_finder_marker", "line": 81}
{"timestamp": "2025-06-11T15:44:49.131165", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "  Dot: insert=(19.79591836734694, 19.79591836734694), size=(11.83673469387755, 11.83673469387755)", "module": "qr_preview", "function": "_draw_finder_marker", "line": 82}
{"timestamp": "2025-06-11T15:44:49.131241", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Successfully added finder pattern group to SVG", "module": "qr_preview", "function": "_draw_finder_marker", "line": 89}
{"timestamp": "2025-06-11T15:44:49.131273", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Successfully drew finder pattern 1/3", "module": "qr_preview", "function": "generate_preview_svg_modular", "line": 486}
{"timestamp": "2025-06-11T15:44:49.131313", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Drawing finder pattern 2/3 at position (42, 0)", "module": "qr_preview", "function": "generate_preview_svg_modular", "line": 484}
{"timestamp": "2025-06-11T15:44:49.131349", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "_draw_finder_marker called: mx=42, my=0, module_size=5.918367346938775, shape=square, color=#000000, offset=(5, 5)", "module": "qr_preview", "function": "_draw_finder_marker", "line": 56}
{"timestamp": "2025-06-11T15:44:49.131390", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Creating finder pattern group with id='marker-42-0'", "module": "qr_preview", "function": "_draw_finder_marker", "line": 64}
{"timestamp": "2025-06-11T15:44:49.131424", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Drawing square finder pattern:", "module": "qr_preview", "function": "_draw_finder_marker", "line": 79}
{"timestamp": "2025-06-11T15:44:49.131457", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "  Outer: insert=(253.57142857142856, 5.0), size=(41.42857142857143, 41.42857142857143)", "module": "qr_preview", "function": "_draw_finder_marker", "line": 80}
{"timestamp": "2025-06-11T15:44:49.131494", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "  Inner: insert=(260.67346938775506, 12.10204081632653), size=(27.224489795918366, 27.224489795918366)", "module": "qr_preview", "function": "_draw_finder_marker", "line": 81}
{"timestamp": "2025-06-11T15:44:49.131526", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "  Dot: insert=(268.3673469387755, 19.79591836734694), size=(11.83673469387755, 11.83673469387755)", "module": "qr_preview", "function": "_draw_finder_marker", "line": 82}
{"timestamp": "2025-06-11T15:44:49.131594", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Successfully added finder pattern group to SVG", "module": "qr_preview", "function": "_draw_finder_marker", "line": 89}
{"timestamp": "2025-06-11T15:44:49.131626", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Successfully drew finder pattern 2/3", "module": "qr_preview", "function": "generate_preview_svg_modular", "line": 486}
{"timestamp": "2025-06-11T15:44:49.131655", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Drawing finder pattern 3/3 at position (0, 42)", "module": "qr_preview", "function": "generate_preview_svg_modular", "line": 484}
{"timestamp": "2025-06-11T15:44:49.131692", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "_draw_finder_marker called: mx=0, my=42, module_size=5.918367346938775, shape=square, color=#000000, offset=(5, 5)", "module": "qr_preview", "function": "_draw_finder_marker", "line": 56}
{"timestamp": "2025-06-11T15:44:49.131730", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Creating finder pattern group with id='marker-0-42'", "module": "qr_preview", "function": "_draw_finder_marker", "line": 64}
{"timestamp": "2025-06-11T15:44:49.131761", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Drawing square finder pattern:", "module": "qr_preview", "function": "_draw_finder_marker", "line": 79}
{"timestamp": "2025-06-11T15:44:49.131793", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "  Outer: insert=(5.0, 253.57142857142856), size=(41.42857142857143, 41.42857142857143)", "module": "qr_preview", "function": "_draw_finder_marker", "line": 80}
{"timestamp": "2025-06-11T15:44:49.131825", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "  Inner: insert=(12.10204081632653, 260.67346938775506), size=(27.224489795918366, 27.224489795918366)", "module": "qr_preview", "function": "_draw_finder_marker", "line": 81}
{"timestamp": "2025-06-11T15:44:49.131854", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "  Dot: insert=(19.79591836734694, 268.3673469387755), size=(11.83673469387755, 11.83673469387755)", "module": "qr_preview", "function": "_draw_finder_marker", "line": 82}
{"timestamp": "2025-06-11T15:44:49.131941", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Successfully added finder pattern group to SVG", "module": "qr_preview", "function": "_draw_finder_marker", "line": 89}
{"timestamp": "2025-06-11T15:44:49.131969", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Successfully drew finder pattern 3/3", "module": "qr_preview", "function": "generate_preview_svg_modular", "line": 486}
{"timestamp": "2025-06-11T15:44:49.152736", "level": "DEBUG", "logger": "backend.routes.qr_preview", "message": "Received QR preview SVG request: {\"qr_type\": \"scooter_unlock\", \"data\": {\"scooter_id\": \"SCOOT123\", \"unlock_code\": \"UNLOCK42\", \"title\": \"Scooter_unlock QR Code\", \"description\": \"Scan to view scooter_unlock content\"}, \"designOptions\": {\"size\": 256, \"margin\": 10, \"pattern\": \"diagonal\", \"marker_border_style\": \"square\", \"marker_center_style\": \"square\", \"logo_size\": 48, \"logo_position\": \"center\", \"logo_opacity\": 1, \"frame_style\": \"none\", \"foreground_color\": \"#000000\", \"background_color\": \"#FFFFFF\", \"color_mode\": \"solid\", \"corner_radiu", "module": "qr_preview", "function": "preview_qr_svg", "line": 242}
{"timestamp": "2025-06-11T15:44:49.154926", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Formatting content for QR type: qrtype.scooter_unlock, content keys: ['scooter_id', 'unlock_code', 'title', 'description']", "module": "qr_preview", "function": "_format_qr_data", "line": 554}
{"timestamp": "2025-06-11T15:44:49.183610", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Drawing 3 finder patterns at positions: [(0, 0), (42, 0), (0, 42)]", "module": "qr_preview", "function": "generate_preview_svg_modular", "line": 479}
{"timestamp": "2025-06-11T15:44:49.183672", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "QR size: 49, marker_shape: square, marker_color: #000000", "module": "qr_preview", "function": "generate_preview_svg_modular", "line": 480}
{"timestamp": "2025-06-11T15:44:49.183721", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Drawing finder pattern 1/3 at position (0, 0)", "module": "qr_preview", "function": "generate_preview_svg_modular", "line": 484}
{"timestamp": "2025-06-11T15:44:49.183764", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "_draw_finder_marker called: mx=0, my=0, module_size=5.918367346938775, shape=square, color=#000000, offset=(5, 5)", "module": "qr_preview", "function": "_draw_finder_marker", "line": 56}
{"timestamp": "2025-06-11T15:44:49.183815", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Creating finder pattern group with id='marker-0-0'", "module": "qr_preview", "function": "_draw_finder_marker", "line": 64}
{"timestamp": "2025-06-11T15:44:49.183850", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Drawing square finder pattern:", "module": "qr_preview", "function": "_draw_finder_marker", "line": 79}
{"timestamp": "2025-06-11T15:44:49.183886", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "  Outer: insert=(5.0, 5.0), size=(41.42857142857143, 41.42857142857143)", "module": "qr_preview", "function": "_draw_finder_marker", "line": 80}
{"timestamp": "2025-06-11T15:44:49.183921", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "  Inner: insert=(12.10204081632653, 12.10204081632653), size=(27.224489795918366, 27.224489795918366)", "module": "qr_preview", "function": "_draw_finder_marker", "line": 81}
{"timestamp": "2025-06-11T15:44:49.183959", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "  Dot: insert=(19.79591836734694, 19.79591836734694), size=(11.83673469387755, 11.83673469387755)", "module": "qr_preview", "function": "_draw_finder_marker", "line": 82}
{"timestamp": "2025-06-11T15:44:49.184042", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Successfully added finder pattern group to SVG", "module": "qr_preview", "function": "_draw_finder_marker", "line": 89}
{"timestamp": "2025-06-11T15:44:49.184076", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Successfully drew finder pattern 1/3", "module": "qr_preview", "function": "generate_preview_svg_modular", "line": 486}
{"timestamp": "2025-06-11T15:44:49.184112", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Drawing finder pattern 2/3 at position (42, 0)", "module": "qr_preview", "function": "generate_preview_svg_modular", "line": 484}
{"timestamp": "2025-06-11T15:44:49.184148", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "_draw_finder_marker called: mx=42, my=0, module_size=5.918367346938775, shape=square, color=#000000, offset=(5, 5)", "module": "qr_preview", "function": "_draw_finder_marker", "line": 56}
{"timestamp": "2025-06-11T15:44:49.184186", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Creating finder pattern group with id='marker-42-0'", "module": "qr_preview", "function": "_draw_finder_marker", "line": 64}
{"timestamp": "2025-06-11T15:44:49.184217", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Drawing square finder pattern:", "module": "qr_preview", "function": "_draw_finder_marker", "line": 79}
{"timestamp": "2025-06-11T15:44:49.186695", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "  Outer: insert=(253.57142857142856, 5.0), size=(41.42857142857143, 41.42857142857143)", "module": "qr_preview", "function": "_draw_finder_marker", "line": 80}
{"timestamp": "2025-06-11T15:44:49.186781", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "  Inner: insert=(260.67346938775506, 12.10204081632653), size=(27.224489795918366, 27.224489795918366)", "module": "qr_preview", "function": "_draw_finder_marker", "line": 81}
{"timestamp": "2025-06-11T15:44:49.186817", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "  Dot: insert=(268.3673469387755, 19.79591836734694), size=(11.83673469387755, 11.83673469387755)", "module": "qr_preview", "function": "_draw_finder_marker", "line": 82}
{"timestamp": "2025-06-11T15:44:49.186926", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Successfully added finder pattern group to SVG", "module": "qr_preview", "function": "_draw_finder_marker", "line": 89}
{"timestamp": "2025-06-11T15:44:49.192035", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Successfully drew finder pattern 2/3", "module": "qr_preview", "function": "generate_preview_svg_modular", "line": 486}
{"timestamp": "2025-06-11T15:44:49.192077", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Drawing finder pattern 3/3 at position (0, 42)", "module": "qr_preview", "function": "generate_preview_svg_modular", "line": 484}
{"timestamp": "2025-06-11T15:44:49.192123", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "_draw_finder_marker called: mx=0, my=42, module_size=5.918367346938775, shape=square, color=#000000, offset=(5, 5)", "module": "qr_preview", "function": "_draw_finder_marker", "line": 56}
{"timestamp": "2025-06-11T15:44:49.192170", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Creating finder pattern group with id='marker-0-42'", "module": "qr_preview", "function": "_draw_finder_marker", "line": 64}
{"timestamp": "2025-06-11T15:44:49.192207", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Drawing square finder pattern:", "module": "qr_preview", "function": "_draw_finder_marker", "line": 79}
{"timestamp": "2025-06-11T15:44:49.192239", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "  Outer: insert=(5.0, 253.57142857142856), size=(41.42857142857143, 41.42857142857143)", "module": "qr_preview", "function": "_draw_finder_marker", "line": 80}
{"timestamp": "2025-06-11T15:44:49.192278", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "  Inner: insert=(12.10204081632653, 260.67346938775506), size=(27.224489795918366, 27.224489795918366)", "module": "qr_preview", "function": "_draw_finder_marker", "line": 81}
{"timestamp": "2025-06-11T15:44:49.192308", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "  Dot: insert=(19.79591836734694, 268.3673469387755), size=(11.83673469387755, 11.83673469387755)", "module": "qr_preview", "function": "_draw_finder_marker", "line": 82}
{"timestamp": "2025-06-11T15:44:49.192458", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Successfully added finder pattern group to SVG", "module": "qr_preview", "function": "_draw_finder_marker", "line": 89}
{"timestamp": "2025-06-11T15:44:49.192489", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Successfully drew finder pattern 3/3", "module": "qr_preview", "function": "generate_preview_svg_modular", "line": 486}
{"timestamp": "2025-06-11T15:44:49.212000", "level": "DEBUG", "logger": "backend.main", "message": "[Auth] Enforced Bearer token authentication for request", "module": "main", "function": "bearer_token_auth_middleware", "line": 274}
{"timestamp": "2025-06-11T15:44:49.214434", "level": "DEBUG", "logger": "backend.main", "message": "[Auth] Enforced Bearer token authentication for request", "module": "main", "function": "bearer_token_auth_middleware", "line": 274}
{"timestamp": "2025-06-11T15:44:54.949546", "level": "DEBUG", "logger": "backend.core.security.jwt_keys", "message": "Token contains key ID: 69b5ada0-20e9-42b1-85e8-1ec80762bb7c", "module": "jwt_keys", "function": "decode_token", "line": 188}
{"timestamp": "2025-06-11T15:44:54.949652", "level": "DEBUG", "logger": "backend.core.security.jwt_keys", "message": "Using key ID 69b5ada0-20e9-42b1-85e8-1ec80762bb7c for verification", "module": "jwt_keys", "function": "decode_token", "line": 195}
{"timestamp": "2025-06-11T15:44:54.950284", "level": "DEBUG", "logger": "backend.services.auth", "message": "JWT decoded successfully with key manager", "module": "auth", "function": "authenticate_token", "line": 683}
{"timestamp": "2025-06-11T15:44:54.952537", "level": "DEBUG", "logger": "backend.core.security.jwt_keys", "message": "Token contains key ID: 69b5ada0-20e9-42b1-85e8-1ec80762bb7c", "module": "jwt_keys", "function": "decode_token", "line": 188}
{"timestamp": "2025-06-11T15:44:54.952600", "level": "DEBUG", "logger": "backend.core.security.jwt_keys", "message": "Using key ID 69b5ada0-20e9-42b1-85e8-1ec80762bb7c for verification", "module": "jwt_keys", "function": "decode_token", "line": 195}
{"timestamp": "2025-06-11T15:44:54.952746", "level": "DEBUG", "logger": "backend.services.auth", "message": "JWT decoded successfully with key manager", "module": "auth", "function": "authenticate_token", "line": 683}
{"timestamp": "2025-06-11T15:44:54.958348", "level": "DEBUG", "logger": "backend.services.db_utils", "message": "Query 'auth_lookup_e4ae2d8f-b32f-52d7-99cd-8a33b19652c7' completed in 0.005s", "module": "db_utils", "function": "execute_query_with_timeout", "line": 68}
{"timestamp": "2025-06-11T15:44:54.958417", "level": "DEBUG", "logger": "backend.services.db_utils", "message": "Auth query 'auth_lookup_e4ae2d8f-b32f-52d7-99cd-8a33b19652c7' successful, result format preserved", "module": "db_utils", "function": "execute_query_with_timeout", "line": 72}
{"timestamp": "2025-06-11T15:44:54.958530", "level": "DEBUG", "logger": "backend.services.db_utils", "message": "Query 'auth_lookup_e4ae2d8f-b32f-52d7-99cd-8a33b19652c7' completed in 0.006s", "module": "db_utils", "function": "execute_query_with_timeout", "line": 68}
{"timestamp": "2025-06-11T15:44:54.958571", "level": "DEBUG", "logger": "backend.services.db_utils", "message": "Auth query 'auth_lookup_e4ae2d8f-b32f-52d7-99cd-8a33b19652c7' successful, result format preserved", "module": "db_utils", "function": "execute_query_with_timeout", "line": 72}
{"timestamp": "2025-06-11T15:44:54.966539", "level": "DEBUG", "logger": "backend.services.auth", "message": "Authentication attempt with token prefix: eyJhbGciOi...", "module": "auth", "function": "get_current_user", "line": 727}
{"timestamp": "2025-06-11T15:44:54.966655", "level": "DEBUG", "logger": "backend.core.security.jwt_keys", "message": "Token contains key ID: 69b5ada0-20e9-42b1-85e8-1ec80762bb7c", "module": "jwt_keys", "function": "decode_token", "line": 188}
{"timestamp": "2025-06-11T15:44:54.966691", "level": "DEBUG", "logger": "backend.core.security.jwt_keys", "message": "Using key ID 69b5ada0-20e9-42b1-85e8-1ec80762bb7c for verification", "module": "jwt_keys", "function": "decode_token", "line": 195}
{"timestamp": "2025-06-11T15:44:54.966812", "level": "DEBUG", "logger": "backend.services.auth", "message": "JWT decoded successfully with key manager", "module": "auth", "function": "authenticate_token", "line": 683}
{"timestamp": "2025-06-11T15:44:54.967270", "level": "DEBUG", "logger": "backend.services.auth", "message": "Authentication attempt with token prefix: eyJhbGciOi...", "module": "auth", "function": "get_current_user", "line": 727}
{"timestamp": "2025-06-11T15:44:54.967331", "level": "DEBUG", "logger": "backend.core.security.jwt_keys", "message": "Token contains key ID: 69b5ada0-20e9-42b1-85e8-1ec80762bb7c", "module": "jwt_keys", "function": "decode_token", "line": 188}
{"timestamp": "2025-06-11T15:44:54.967363", "level": "DEBUG", "logger": "backend.core.security.jwt_keys", "message": "Using key ID 69b5ada0-20e9-42b1-85e8-1ec80762bb7c for verification", "module": "jwt_keys", "function": "decode_token", "line": 195}
{"timestamp": "2025-06-11T15:44:54.967441", "level": "DEBUG", "logger": "backend.services.auth", "message": "JWT decoded successfully with key manager", "module": "auth", "function": "authenticate_token", "line": 683}
{"timestamp": "2025-06-11T15:44:54.968959", "level": "DEBUG", "logger": "backend.services.db_utils", "message": "Query 'auth_lookup_e4ae2d8f-b32f-52d7-99cd-8a33b19652c7' completed in 0.002s", "module": "db_utils", "function": "execute_query_with_timeout", "line": 68}
{"timestamp": "2025-06-11T15:44:54.968999", "level": "DEBUG", "logger": "backend.services.db_utils", "message": "Auth query 'auth_lookup_e4ae2d8f-b32f-52d7-99cd-8a33b19652c7' successful, result format preserved", "module": "db_utils", "function": "execute_query_with_timeout", "line": 72}
{"timestamp": "2025-06-11T15:44:54.969054", "level": "DEBUG", "logger": "backend.services.auth", "message": "Successfully authenticated user: <EMAIL>", "module": "auth", "function": "get_current_user", "line": 732}
{"timestamp": "2025-06-11T15:44:54.969091", "level": "DEBUG", "logger": "backend.services.auth", "message": "Successfully authenticated user: <EMAIL>", "module": "auth", "function": "get_current_user", "line": 738}
{"timestamp": "2025-06-11T15:44:54.969420", "level": "DEBUG", "logger": "backend.services.db_utils", "message": "Query 'auth_lookup_e4ae2d8f-b32f-52d7-99cd-8a33b19652c7' completed in 0.002s", "module": "db_utils", "function": "execute_query_with_timeout", "line": 68}
{"timestamp": "2025-06-11T15:44:54.969453", "level": "DEBUG", "logger": "backend.services.db_utils", "message": "Auth query 'auth_lookup_e4ae2d8f-b32f-52d7-99cd-8a33b19652c7' successful, result format preserved", "module": "db_utils", "function": "execute_query_with_timeout", "line": 72}
{"timestamp": "2025-06-11T15:44:54.969962", "level": "DEBUG", "logger": "backend.services.auth", "message": "Successfully authenticated user: <EMAIL>", "module": "auth", "function": "get_current_user", "line": 732}
{"timestamp": "2025-06-11T15:44:54.969996", "level": "DEBUG", "logger": "backend.services.auth", "message": "Successfully authenticated user: <EMAIL>", "module": "auth", "function": "get_current_user", "line": 738}
{"timestamp": "2025-06-11T15:44:54.971957", "level": "DEBUG", "logger": "backend.routes.qr_preview", "message": "Received QR preview SVG request: {\"qr_type\": \"scooter_unlock\", \"data\": {\"scooter_id\": \"SCOOT123\", \"unlock_code\": \"UNLOCK42\", \"title\": \"Scooter_unlock QR Code\", \"description\": \"Scan to view scooter_unlock content\"}, \"designOptions\": {\"size\": 256, \"margin\": 10, \"pattern\": \"square\", \"error_correction\": \"M\", \"svg_render_dpi\": 300, \"svg_optimize\": true}, \"dimensions\": 300}", "module": "qr_preview", "function": "preview_qr_svg", "line": 242}
{"timestamp": "2025-06-11T15:44:54.973734", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Formatting content for QR type: qrtype.scooter_unlock, content keys: ['scooter_id', 'unlock_code', 'title', 'description']", "module": "qr_preview", "function": "_format_qr_data", "line": 554}
{"timestamp": "2025-06-11T15:44:55.003901", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Drawing 3 finder patterns at positions: [(0, 0), (42, 0), (0, 42)]", "module": "qr_preview", "function": "generate_preview_svg_modular", "line": 479}
{"timestamp": "2025-06-11T15:44:55.003961", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "QR size: 49, marker_shape: square, marker_color: #000000", "module": "qr_preview", "function": "generate_preview_svg_modular", "line": 480}
{"timestamp": "2025-06-11T15:44:55.003994", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Drawing finder pattern 1/3 at position (0, 0)", "module": "qr_preview", "function": "generate_preview_svg_modular", "line": 484}
{"timestamp": "2025-06-11T15:44:55.004043", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "_draw_finder_marker called: mx=0, my=0, module_size=5.918367346938775, shape=square, color=#000000, offset=(5, 5)", "module": "qr_preview", "function": "_draw_finder_marker", "line": 56}
{"timestamp": "2025-06-11T15:44:55.004113", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Creating finder pattern group with id='marker-0-0'", "module": "qr_preview", "function": "_draw_finder_marker", "line": 64}
{"timestamp": "2025-06-11T15:44:55.004143", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Drawing square finder pattern:", "module": "qr_preview", "function": "_draw_finder_marker", "line": 79}
{"timestamp": "2025-06-11T15:44:55.004174", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "  Outer: insert=(5.0, 5.0), size=(41.42857142857143, 41.42857142857143)", "module": "qr_preview", "function": "_draw_finder_marker", "line": 80}
{"timestamp": "2025-06-11T15:44:55.004205", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "  Inner: insert=(12.10204081632653, 12.10204081632653), size=(27.224489795918366, 27.224489795918366)", "module": "qr_preview", "function": "_draw_finder_marker", "line": 81}
{"timestamp": "2025-06-11T15:44:55.005291", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "  Dot: insert=(19.79591836734694, 19.79591836734694), size=(11.83673469387755, 11.83673469387755)", "module": "qr_preview", "function": "_draw_finder_marker", "line": 82}
{"timestamp": "2025-06-11T15:44:55.005416", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Successfully added finder pattern group to SVG", "module": "qr_preview", "function": "_draw_finder_marker", "line": 89}
{"timestamp": "2025-06-11T15:44:55.005457", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Successfully drew finder pattern 1/3", "module": "qr_preview", "function": "generate_preview_svg_modular", "line": 486}
{"timestamp": "2025-06-11T15:44:55.005493", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Drawing finder pattern 2/3 at position (42, 0)", "module": "qr_preview", "function": "generate_preview_svg_modular", "line": 484}
{"timestamp": "2025-06-11T15:44:55.005530", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "_draw_finder_marker called: mx=42, my=0, module_size=5.918367346938775, shape=square, color=#000000, offset=(5, 5)", "module": "qr_preview", "function": "_draw_finder_marker", "line": 56}
{"timestamp": "2025-06-11T15:44:55.005574", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Creating finder pattern group with id='marker-42-0'", "module": "qr_preview", "function": "_draw_finder_marker", "line": 64}
{"timestamp": "2025-06-11T15:44:55.005605", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Drawing square finder pattern:", "module": "qr_preview", "function": "_draw_finder_marker", "line": 79}
{"timestamp": "2025-06-11T15:44:55.005636", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "  Outer: insert=(253.57142857142856, 5.0), size=(41.42857142857143, 41.42857142857143)", "module": "qr_preview", "function": "_draw_finder_marker", "line": 80}
{"timestamp": "2025-06-11T15:44:55.005673", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "  Inner: insert=(260.67346938775506, 12.10204081632653), size=(27.224489795918366, 27.224489795918366)", "module": "qr_preview", "function": "_draw_finder_marker", "line": 81}
{"timestamp": "2025-06-11T15:44:55.005703", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "  Dot: insert=(268.3673469387755, 19.79591836734694), size=(11.83673469387755, 11.83673469387755)", "module": "qr_preview", "function": "_draw_finder_marker", "line": 82}
{"timestamp": "2025-06-11T15:44:55.005771", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Successfully added finder pattern group to SVG", "module": "qr_preview", "function": "_draw_finder_marker", "line": 89}
{"timestamp": "2025-06-11T15:44:55.005802", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Successfully drew finder pattern 2/3", "module": "qr_preview", "function": "generate_preview_svg_modular", "line": 486}
{"timestamp": "2025-06-11T15:44:55.005833", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Drawing finder pattern 3/3 at position (0, 42)", "module": "qr_preview", "function": "generate_preview_svg_modular", "line": 484}
{"timestamp": "2025-06-11T15:44:55.005863", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "_draw_finder_marker called: mx=0, my=42, module_size=5.918367346938775, shape=square, color=#000000, offset=(5, 5)", "module": "qr_preview", "function": "_draw_finder_marker", "line": 56}
{"timestamp": "2025-06-11T15:44:55.005898", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Creating finder pattern group with id='marker-0-42'", "module": "qr_preview", "function": "_draw_finder_marker", "line": 64}
{"timestamp": "2025-06-11T15:44:55.005926", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Drawing square finder pattern:", "module": "qr_preview", "function": "_draw_finder_marker", "line": 79}
{"timestamp": "2025-06-11T15:44:55.006049", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "  Outer: insert=(5.0, 253.57142857142856), size=(41.42857142857143, 41.42857142857143)", "module": "qr_preview", "function": "_draw_finder_marker", "line": 80}
{"timestamp": "2025-06-11T15:44:55.006091", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "  Inner: insert=(12.10204081632653, 260.67346938775506), size=(27.224489795918366, 27.224489795918366)", "module": "qr_preview", "function": "_draw_finder_marker", "line": 81}
{"timestamp": "2025-06-11T15:44:55.006124", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "  Dot: insert=(19.79591836734694, 268.3673469387755), size=(11.83673469387755, 11.83673469387755)", "module": "qr_preview", "function": "_draw_finder_marker", "line": 82}
{"timestamp": "2025-06-11T15:44:55.006205", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Successfully added finder pattern group to SVG", "module": "qr_preview", "function": "_draw_finder_marker", "line": 89}
{"timestamp": "2025-06-11T15:44:55.006239", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Successfully drew finder pattern 3/3", "module": "qr_preview", "function": "generate_preview_svg_modular", "line": 486}
{"timestamp": "2025-06-11T15:44:55.026648", "level": "DEBUG", "logger": "backend.routes.qr_preview", "message": "Received QR preview SVG request: {\"qr_type\": \"scooter_unlock\", \"data\": {\"scooter_id\": \"SCOOT123\", \"unlock_code\": \"UNLOCK42\", \"title\": \"Scooter_unlock QR Code\", \"description\": \"Scan to view scooter_unlock content\"}, \"designOptions\": {\"size\": 256, \"margin\": 10, \"pattern\": \"vertical\", \"marker_border_style\": \"square\", \"marker_center_style\": \"square\", \"logo_size\": 48, \"logo_position\": \"center\", \"logo_opacity\": 1, \"frame_style\": \"none\", \"foreground_color\": \"#000000\", \"background_color\": \"#FFFFFF\", \"color_mode\": \"solid\", \"corner_radiu", "module": "qr_preview", "function": "preview_qr_svg", "line": 242}
{"timestamp": "2025-06-11T15:44:55.026841", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Formatting content for QR type: qrtype.scooter_unlock, content keys: ['scooter_id', 'unlock_code', 'title', 'description']", "module": "qr_preview", "function": "_format_qr_data", "line": 554}
{"timestamp": "2025-06-11T15:44:55.056258", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Drawing 3 finder patterns at positions: [(0, 0), (42, 0), (0, 42)]", "module": "qr_preview", "function": "generate_preview_svg_modular", "line": 479}
{"timestamp": "2025-06-11T15:44:55.056813", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "QR size: 49, marker_shape: square, marker_color: #000000", "module": "qr_preview", "function": "generate_preview_svg_modular", "line": 480}
{"timestamp": "2025-06-11T15:44:55.056869", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Drawing finder pattern 1/3 at position (0, 0)", "module": "qr_preview", "function": "generate_preview_svg_modular", "line": 484}
{"timestamp": "2025-06-11T15:44:55.063791", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "_draw_finder_marker called: mx=0, my=0, module_size=5.918367346938775, shape=square, color=#000000, offset=(5, 5)", "module": "qr_preview", "function": "_draw_finder_marker", "line": 56}
{"timestamp": "2025-06-11T15:44:55.063963", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Creating finder pattern group with id='marker-0-0'", "module": "qr_preview", "function": "_draw_finder_marker", "line": 64}
{"timestamp": "2025-06-11T15:44:55.064048", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Drawing square finder pattern:", "module": "qr_preview", "function": "_draw_finder_marker", "line": 79}
{"timestamp": "2025-06-11T15:44:55.064111", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "  Outer: insert=(5.0, 5.0), size=(41.42857142857143, 41.42857142857143)", "module": "qr_preview", "function": "_draw_finder_marker", "line": 80}
{"timestamp": "2025-06-11T15:44:55.064162", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "  Inner: insert=(12.10204081632653, 12.10204081632653), size=(27.224489795918366, 27.224489795918366)", "module": "qr_preview", "function": "_draw_finder_marker", "line": 81}
{"timestamp": "2025-06-11T15:44:55.064288", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "  Dot: insert=(19.79591836734694, 19.79591836734694), size=(11.83673469387755, 11.83673469387755)", "module": "qr_preview", "function": "_draw_finder_marker", "line": 82}
{"timestamp": "2025-06-11T15:44:55.064673", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Successfully added finder pattern group to SVG", "module": "qr_preview", "function": "_draw_finder_marker", "line": 89}
{"timestamp": "2025-06-11T15:44:55.064740", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Successfully drew finder pattern 1/3", "module": "qr_preview", "function": "generate_preview_svg_modular", "line": 486}
{"timestamp": "2025-06-11T15:44:55.064784", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Drawing finder pattern 2/3 at position (42, 0)", "module": "qr_preview", "function": "generate_preview_svg_modular", "line": 484}
{"timestamp": "2025-06-11T15:44:55.064856", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "_draw_finder_marker called: mx=42, my=0, module_size=5.918367346938775, shape=square, color=#000000, offset=(5, 5)", "module": "qr_preview", "function": "_draw_finder_marker", "line": 56}
{"timestamp": "2025-06-11T15:44:55.064913", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Creating finder pattern group with id='marker-42-0'", "module": "qr_preview", "function": "_draw_finder_marker", "line": 64}
{"timestamp": "2025-06-11T15:44:55.064954", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Drawing square finder pattern:", "module": "qr_preview", "function": "_draw_finder_marker", "line": 79}
{"timestamp": "2025-06-11T15:44:55.064994", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "  Outer: insert=(253.57142857142856, 5.0), size=(41.42857142857143, 41.42857142857143)", "module": "qr_preview", "function": "_draw_finder_marker", "line": 80}
{"timestamp": "2025-06-11T15:44:55.065032", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "  Inner: insert=(260.67346938775506, 12.10204081632653), size=(27.224489795918366, 27.224489795918366)", "module": "qr_preview", "function": "_draw_finder_marker", "line": 81}
{"timestamp": "2025-06-11T15:44:55.065072", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "  Dot: insert=(268.3673469387755, 19.79591836734694), size=(11.83673469387755, 11.83673469387755)", "module": "qr_preview", "function": "_draw_finder_marker", "line": 82}
{"timestamp": "2025-06-11T15:44:55.065149", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Successfully added finder pattern group to SVG", "module": "qr_preview", "function": "_draw_finder_marker", "line": 89}
{"timestamp": "2025-06-11T15:44:55.065185", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Successfully drew finder pattern 2/3", "module": "qr_preview", "function": "generate_preview_svg_modular", "line": 486}
{"timestamp": "2025-06-11T15:44:55.065215", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Drawing finder pattern 3/3 at position (0, 42)", "module": "qr_preview", "function": "generate_preview_svg_modular", "line": 484}
{"timestamp": "2025-06-11T15:44:55.065252", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "_draw_finder_marker called: mx=0, my=42, module_size=5.918367346938775, shape=square, color=#000000, offset=(5, 5)", "module": "qr_preview", "function": "_draw_finder_marker", "line": 56}
{"timestamp": "2025-06-11T15:44:55.065290", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Creating finder pattern group with id='marker-0-42'", "module": "qr_preview", "function": "_draw_finder_marker", "line": 64}
{"timestamp": "2025-06-11T15:44:55.065319", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Drawing square finder pattern:", "module": "qr_preview", "function": "_draw_finder_marker", "line": 79}
{"timestamp": "2025-06-11T15:44:55.065349", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "  Outer: insert=(5.0, 253.57142857142856), size=(41.42857142857143, 41.42857142857143)", "module": "qr_preview", "function": "_draw_finder_marker", "line": 80}
{"timestamp": "2025-06-11T15:44:55.065389", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "  Inner: insert=(12.10204081632653, 260.67346938775506), size=(27.224489795918366, 27.224489795918366)", "module": "qr_preview", "function": "_draw_finder_marker", "line": 81}
{"timestamp": "2025-06-11T15:44:55.065425", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "  Dot: insert=(19.79591836734694, 268.3673469387755), size=(11.83673469387755, 11.83673469387755)", "module": "qr_preview", "function": "_draw_finder_marker", "line": 82}
{"timestamp": "2025-06-11T15:44:55.065580", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Successfully added finder pattern group to SVG", "module": "qr_preview", "function": "_draw_finder_marker", "line": 89}
{"timestamp": "2025-06-11T15:44:55.065623", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Successfully drew finder pattern 3/3", "module": "qr_preview", "function": "generate_preview_svg_modular", "line": 486}
{"timestamp": "2025-06-11T15:44:55.087505", "level": "DEBUG", "logger": "backend.main", "message": "[Auth] Enforced Bearer token authentication for request", "module": "main", "function": "bearer_token_auth_middleware", "line": 274}
{"timestamp": "2025-06-11T15:44:55.088530", "level": "DEBUG", "logger": "backend.main", "message": "[Auth] Enforced Bearer token authentication for request", "module": "main", "function": "bearer_token_auth_middleware", "line": 274}
{"timestamp": "2025-06-11T15:44:55.456842", "level": "DEBUG", "logger": "backend.main", "message": "[Auth] Enforced Bearer token authentication for request", "module": "main", "function": "bearer_token_auth_middleware", "line": 274}
{"timestamp": "2025-06-11T15:44:55.457218", "level": "DEBUG", "logger": "backend.main", "message": "[Auth] Enforced Bearer token authentication for request", "module": "main", "function": "bearer_token_auth_middleware", "line": 274}
{"timestamp": "2025-06-11T15:45:01.023084", "level": "DEBUG", "logger": "backend.core.security.jwt_keys", "message": "Token contains key ID: 69b5ada0-20e9-42b1-85e8-1ec80762bb7c", "module": "jwt_keys", "function": "decode_token", "line": 188}
{"timestamp": "2025-06-11T15:45:01.023219", "level": "DEBUG", "logger": "backend.core.security.jwt_keys", "message": "Using key ID 69b5ada0-20e9-42b1-85e8-1ec80762bb7c for verification", "module": "jwt_keys", "function": "decode_token", "line": 195}
{"timestamp": "2025-06-11T15:45:01.023497", "level": "DEBUG", "logger": "backend.services.auth", "message": "JWT decoded successfully with key manager", "module": "auth", "function": "authenticate_token", "line": 683}
{"timestamp": "2025-06-11T15:45:01.024763", "level": "DEBUG", "logger": "backend.core.security.jwt_keys", "message": "Token contains key ID: 69b5ada0-20e9-42b1-85e8-1ec80762bb7c", "module": "jwt_keys", "function": "decode_token", "line": 188}
{"timestamp": "2025-06-11T15:45:01.024831", "level": "DEBUG", "logger": "backend.core.security.jwt_keys", "message": "Using key ID 69b5ada0-20e9-42b1-85e8-1ec80762bb7c for verification", "module": "jwt_keys", "function": "decode_token", "line": 195}
{"timestamp": "2025-06-11T15:45:01.025005", "level": "DEBUG", "logger": "backend.services.auth", "message": "JWT decoded successfully with key manager", "module": "auth", "function": "authenticate_token", "line": 683}
{"timestamp": "2025-06-11T15:45:01.028021", "level": "DEBUG", "logger": "backend.services.db_utils", "message": "Query 'auth_lookup_e4ae2d8f-b32f-52d7-99cd-8a33b19652c7' completed in 0.003s", "module": "db_utils", "function": "execute_query_with_timeout", "line": 68}
{"timestamp": "2025-06-11T15:45:01.028086", "level": "DEBUG", "logger": "backend.services.db_utils", "message": "Auth query 'auth_lookup_e4ae2d8f-b32f-52d7-99cd-8a33b19652c7' successful, result format preserved", "module": "db_utils", "function": "execute_query_with_timeout", "line": 72}
{"timestamp": "2025-06-11T15:45:01.028394", "level": "DEBUG", "logger": "backend.services.db_utils", "message": "Query 'auth_lookup_e4ae2d8f-b32f-52d7-99cd-8a33b19652c7' completed in 0.005s", "module": "db_utils", "function": "execute_query_with_timeout", "line": 68}
{"timestamp": "2025-06-11T15:45:01.031010", "level": "DEBUG", "logger": "backend.services.db_utils", "message": "Auth query 'auth_lookup_e4ae2d8f-b32f-52d7-99cd-8a33b19652c7' successful, result format preserved", "module": "db_utils", "function": "execute_query_with_timeout", "line": 72}
{"timestamp": "2025-06-11T15:45:01.038118", "level": "DEBUG", "logger": "backend.services.auth", "message": "Authentication attempt with token prefix: eyJhbGciOi...", "module": "auth", "function": "get_current_user", "line": 727}
{"timestamp": "2025-06-11T15:45:01.038292", "level": "DEBUG", "logger": "backend.core.security.jwt_keys", "message": "Token contains key ID: 69b5ada0-20e9-42b1-85e8-1ec80762bb7c", "module": "jwt_keys", "function": "decode_token", "line": 188}
{"timestamp": "2025-06-11T15:45:01.038360", "level": "DEBUG", "logger": "backend.core.security.jwt_keys", "message": "Using key ID 69b5ada0-20e9-42b1-85e8-1ec80762bb7c for verification", "module": "jwt_keys", "function": "decode_token", "line": 195}
{"timestamp": "2025-06-11T15:45:01.038565", "level": "DEBUG", "logger": "backend.services.auth", "message": "JWT decoded successfully with key manager", "module": "auth", "function": "authenticate_token", "line": 683}
{"timestamp": "2025-06-11T15:45:01.039419", "level": "DEBUG", "logger": "backend.services.auth", "message": "Authentication attempt with token prefix: eyJhbGciOi...", "module": "auth", "function": "get_current_user", "line": 727}
{"timestamp": "2025-06-11T15:45:01.039530", "level": "DEBUG", "logger": "backend.core.security.jwt_keys", "message": "Token contains key ID: 69b5ada0-20e9-42b1-85e8-1ec80762bb7c", "module": "jwt_keys", "function": "decode_token", "line": 188}
{"timestamp": "2025-06-11T15:45:01.039584", "level": "DEBUG", "logger": "backend.core.security.jwt_keys", "message": "Using key ID 69b5ada0-20e9-42b1-85e8-1ec80762bb7c for verification", "module": "jwt_keys", "function": "decode_token", "line": 195}
{"timestamp": "2025-06-11T15:45:01.039708", "level": "DEBUG", "logger": "backend.services.auth", "message": "JWT decoded successfully with key manager", "module": "auth", "function": "authenticate_token", "line": 683}
{"timestamp": "2025-06-11T15:45:01.041245", "level": "DEBUG", "logger": "backend.services.db_utils", "message": "Query 'auth_lookup_e4ae2d8f-b32f-52d7-99cd-8a33b19652c7' completed in 0.002s", "module": "db_utils", "function": "execute_query_with_timeout", "line": 68}
{"timestamp": "2025-06-11T15:45:01.041334", "level": "DEBUG", "logger": "backend.services.db_utils", "message": "Auth query 'auth_lookup_e4ae2d8f-b32f-52d7-99cd-8a33b19652c7' successful, result format preserved", "module": "db_utils", "function": "execute_query_with_timeout", "line": 72}
{"timestamp": "2025-06-11T15:45:01.041402", "level": "DEBUG", "logger": "backend.services.auth", "message": "Successfully authenticated user: <EMAIL>", "module": "auth", "function": "get_current_user", "line": 732}
{"timestamp": "2025-06-11T15:45:01.041437", "level": "DEBUG", "logger": "backend.services.auth", "message": "Successfully authenticated user: <EMAIL>", "module": "auth", "function": "get_current_user", "line": 738}
{"timestamp": "2025-06-11T15:45:01.042024", "level": "DEBUG", "logger": "backend.services.db_utils", "message": "Query 'auth_lookup_e4ae2d8f-b32f-52d7-99cd-8a33b19652c7' completed in 0.002s", "module": "db_utils", "function": "execute_query_with_timeout", "line": 68}
{"timestamp": "2025-06-11T15:45:01.042065", "level": "DEBUG", "logger": "backend.services.db_utils", "message": "Auth query 'auth_lookup_e4ae2d8f-b32f-52d7-99cd-8a33b19652c7' successful, result format preserved", "module": "db_utils", "function": "execute_query_with_timeout", "line": 72}
{"timestamp": "2025-06-11T15:45:01.042113", "level": "DEBUG", "logger": "backend.services.auth", "message": "Successfully authenticated user: <EMAIL>", "module": "auth", "function": "get_current_user", "line": 732}
{"timestamp": "2025-06-11T15:45:01.042148", "level": "DEBUG", "logger": "backend.services.auth", "message": "Successfully authenticated user: <EMAIL>", "module": "auth", "function": "get_current_user", "line": 738}
{"timestamp": "2025-06-11T15:45:01.043116", "level": "DEBUG", "logger": "backend.routes.qr_preview", "message": "Received QR preview SVG request: {\"qr_type\": \"scooter_unlock\", \"data\": {\"scooter_id\": \"SCOOT123\", \"unlock_code\": \"UNLOCK42\", \"title\": \"Scooter_unlock QR Code\", \"description\": \"Scan to view scooter_unlock content\"}, \"designOptions\": {\"size\": 256, \"margin\": 10, \"pattern\": \"diagonal\", \"marker_border_style\": \"square\", \"marker_center_style\": \"square\", \"logo_size\": 48, \"logo_position\": \"center\", \"logo_opacity\": 1, \"frame_style\": \"none\", \"foreground_color\": \"#000000\", \"background_color\": \"#FFFFFF\", \"color_mode\": \"solid\", \"corner_radiu", "module": "qr_preview", "function": "preview_qr_svg", "line": 242}
{"timestamp": "2025-06-11T15:45:01.043192", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Formatting content for QR type: qrtype.scooter_unlock, content keys: ['scooter_id', 'unlock_code', 'title', 'description']", "module": "qr_preview", "function": "_format_qr_data", "line": 554}
{"timestamp": "2025-06-11T15:45:01.081346", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Drawing 3 finder patterns at positions: [(0, 0), (42, 0), (0, 42)]", "module": "qr_preview", "function": "generate_preview_svg_modular", "line": 479}
{"timestamp": "2025-06-11T15:45:01.081424", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "QR size: 49, marker_shape: square, marker_color: #000000", "module": "qr_preview", "function": "generate_preview_svg_modular", "line": 480}
{"timestamp": "2025-06-11T15:45:01.081470", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Drawing finder pattern 1/3 at position (0, 0)", "module": "qr_preview", "function": "generate_preview_svg_modular", "line": 484}
{"timestamp": "2025-06-11T15:45:01.081525", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "_draw_finder_marker called: mx=0, my=0, module_size=5.918367346938775, shape=square, color=#000000, offset=(5, 5)", "module": "qr_preview", "function": "_draw_finder_marker", "line": 56}
{"timestamp": "2025-06-11T15:45:01.081580", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Creating finder pattern group with id='marker-0-0'", "module": "qr_preview", "function": "_draw_finder_marker", "line": 64}
{"timestamp": "2025-06-11T15:45:01.081621", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Drawing square finder pattern:", "module": "qr_preview", "function": "_draw_finder_marker", "line": 79}
{"timestamp": "2025-06-11T15:45:01.081660", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "  Outer: insert=(5.0, 5.0), size=(41.42857142857143, 41.42857142857143)", "module": "qr_preview", "function": "_draw_finder_marker", "line": 80}
{"timestamp": "2025-06-11T15:45:01.081698", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "  Inner: insert=(12.10204081632653, 12.10204081632653), size=(27.224489795918366, 27.224489795918366)", "module": "qr_preview", "function": "_draw_finder_marker", "line": 81}
{"timestamp": "2025-06-11T15:45:01.081737", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "  Dot: insert=(19.79591836734694, 19.79591836734694), size=(11.83673469387755, 11.83673469387755)", "module": "qr_preview", "function": "_draw_finder_marker", "line": 82}
{"timestamp": "2025-06-11T15:45:01.081824", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Successfully added finder pattern group to SVG", "module": "qr_preview", "function": "_draw_finder_marker", "line": 89}
{"timestamp": "2025-06-11T15:45:01.081861", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Successfully drew finder pattern 1/3", "module": "qr_preview", "function": "generate_preview_svg_modular", "line": 486}
{"timestamp": "2025-06-11T15:45:01.081896", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Drawing finder pattern 2/3 at position (42, 0)", "module": "qr_preview", "function": "generate_preview_svg_modular", "line": 484}
{"timestamp": "2025-06-11T15:45:01.081935", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "_draw_finder_marker called: mx=42, my=0, module_size=5.918367346938775, shape=square, color=#000000, offset=(5, 5)", "module": "qr_preview", "function": "_draw_finder_marker", "line": 56}
{"timestamp": "2025-06-11T15:45:01.081981", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Creating finder pattern group with id='marker-42-0'", "module": "qr_preview", "function": "_draw_finder_marker", "line": 64}
{"timestamp": "2025-06-11T15:45:01.082021", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Drawing square finder pattern:", "module": "qr_preview", "function": "_draw_finder_marker", "line": 79}
{"timestamp": "2025-06-11T15:45:01.082059", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "  Outer: insert=(253.57142857142856, 5.0), size=(41.42857142857143, 41.42857142857143)", "module": "qr_preview", "function": "_draw_finder_marker", "line": 80}
{"timestamp": "2025-06-11T15:45:01.082096", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "  Inner: insert=(260.67346938775506, 12.10204081632653), size=(27.224489795918366, 27.224489795918366)", "module": "qr_preview", "function": "_draw_finder_marker", "line": 81}
{"timestamp": "2025-06-11T15:45:01.082132", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "  Dot: insert=(268.3673469387755, 19.79591836734694), size=(11.83673469387755, 11.83673469387755)", "module": "qr_preview", "function": "_draw_finder_marker", "line": 82}
{"timestamp": "2025-06-11T15:45:01.082211", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Successfully added finder pattern group to SVG", "module": "qr_preview", "function": "_draw_finder_marker", "line": 89}
{"timestamp": "2025-06-11T15:45:01.082247", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Successfully drew finder pattern 2/3", "module": "qr_preview", "function": "generate_preview_svg_modular", "line": 486}
{"timestamp": "2025-06-11T15:45:01.082279", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Drawing finder pattern 3/3 at position (0, 42)", "module": "qr_preview", "function": "generate_preview_svg_modular", "line": 484}
{"timestamp": "2025-06-11T15:45:01.082314", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "_draw_finder_marker called: mx=0, my=42, module_size=5.918367346938775, shape=square, color=#000000, offset=(5, 5)", "module": "qr_preview", "function": "_draw_finder_marker", "line": 56}
{"timestamp": "2025-06-11T15:45:01.082357", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Creating finder pattern group with id='marker-0-42'", "module": "qr_preview", "function": "_draw_finder_marker", "line": 64}
{"timestamp": "2025-06-11T15:45:01.082396", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Drawing square finder pattern:", "module": "qr_preview", "function": "_draw_finder_marker", "line": 79}
{"timestamp": "2025-06-11T15:45:01.082432", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "  Outer: insert=(5.0, 253.57142857142856), size=(41.42857142857143, 41.42857142857143)", "module": "qr_preview", "function": "_draw_finder_marker", "line": 80}
{"timestamp": "2025-06-11T15:45:01.082468", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "  Inner: insert=(12.10204081632653, 260.67346938775506), size=(27.224489795918366, 27.224489795918366)", "module": "qr_preview", "function": "_draw_finder_marker", "line": 81}
{"timestamp": "2025-06-11T15:45:01.082504", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "  Dot: insert=(19.79591836734694, 268.3673469387755), size=(11.83673469387755, 11.83673469387755)", "module": "qr_preview", "function": "_draw_finder_marker", "line": 82}
{"timestamp": "2025-06-11T15:45:01.082578", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Successfully added finder pattern group to SVG", "module": "qr_preview", "function": "_draw_finder_marker", "line": 89}
{"timestamp": "2025-06-11T15:45:01.082612", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Successfully drew finder pattern 3/3", "module": "qr_preview", "function": "generate_preview_svg_modular", "line": 486}
{"timestamp": "2025-06-11T15:45:01.102710", "level": "DEBUG", "logger": "backend.routes.qr_preview", "message": "Received QR preview SVG request: {\"qr_type\": \"scooter_unlock\", \"data\": {\"scooter_id\": \"SCOOT123\", \"unlock_code\": \"UNLOCK42\", \"title\": \"Scooter_unlock QR Code\", \"description\": \"Scan to view scooter_unlock content\"}, \"designOptions\": {\"size\": 256, \"margin\": 10, \"pattern\": \"square\", \"error_correction\": \"M\", \"svg_render_dpi\": 300, \"svg_optimize\": true}, \"dimensions\": 300}", "module": "qr_preview", "function": "preview_qr_svg", "line": 242}
{"timestamp": "2025-06-11T15:45:01.102791", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Formatting content for QR type: qrtype.scooter_unlock, content keys: ['scooter_id', 'unlock_code', 'title', 'description']", "module": "qr_preview", "function": "_format_qr_data", "line": 554}
{"timestamp": "2025-06-11T15:45:01.131628", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Drawing 3 finder patterns at positions: [(0, 0), (42, 0), (0, 42)]", "module": "qr_preview", "function": "generate_preview_svg_modular", "line": 479}
{"timestamp": "2025-06-11T15:45:01.131695", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "QR size: 49, marker_shape: square, marker_color: #000000", "module": "qr_preview", "function": "generate_preview_svg_modular", "line": 480}
{"timestamp": "2025-06-11T15:45:01.131736", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Drawing finder pattern 1/3 at position (0, 0)", "module": "qr_preview", "function": "generate_preview_svg_modular", "line": 484}
{"timestamp": "2025-06-11T15:45:01.132104", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "_draw_finder_marker called: mx=0, my=0, module_size=5.918367346938775, shape=square, color=#000000, offset=(5, 5)", "module": "qr_preview", "function": "_draw_finder_marker", "line": 56}
{"timestamp": "2025-06-11T15:45:01.132184", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Creating finder pattern group with id='marker-0-0'", "module": "qr_preview", "function": "_draw_finder_marker", "line": 64}
{"timestamp": "2025-06-11T15:45:01.132225", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Drawing square finder pattern:", "module": "qr_preview", "function": "_draw_finder_marker", "line": 79}
{"timestamp": "2025-06-11T15:45:01.132265", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "  Outer: insert=(5.0, 5.0), size=(41.42857142857143, 41.42857142857143)", "module": "qr_preview", "function": "_draw_finder_marker", "line": 80}
{"timestamp": "2025-06-11T15:45:01.132305", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "  Inner: insert=(12.10204081632653, 12.10204081632653), size=(27.224489795918366, 27.224489795918366)", "module": "qr_preview", "function": "_draw_finder_marker", "line": 81}
{"timestamp": "2025-06-11T15:45:01.132342", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "  Dot: insert=(19.79591836734694, 19.79591836734694), size=(11.83673469387755, 11.83673469387755)", "module": "qr_preview", "function": "_draw_finder_marker", "line": 82}
{"timestamp": "2025-06-11T15:45:01.132425", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Successfully added finder pattern group to SVG", "module": "qr_preview", "function": "_draw_finder_marker", "line": 89}
{"timestamp": "2025-06-11T15:45:01.132462", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Successfully drew finder pattern 1/3", "module": "qr_preview", "function": "generate_preview_svg_modular", "line": 486}
{"timestamp": "2025-06-11T15:45:01.132495", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Drawing finder pattern 2/3 at position (42, 0)", "module": "qr_preview", "function": "generate_preview_svg_modular", "line": 484}
{"timestamp": "2025-06-11T15:45:01.132531", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "_draw_finder_marker called: mx=42, my=0, module_size=5.918367346938775, shape=square, color=#000000, offset=(5, 5)", "module": "qr_preview", "function": "_draw_finder_marker", "line": 56}
{"timestamp": "2025-06-11T15:45:01.132572", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Creating finder pattern group with id='marker-42-0'", "module": "qr_preview", "function": "_draw_finder_marker", "line": 64}
{"timestamp": "2025-06-11T15:45:01.132609", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Drawing square finder pattern:", "module": "qr_preview", "function": "_draw_finder_marker", "line": 79}
{"timestamp": "2025-06-11T15:45:01.132642", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "  Outer: insert=(253.57142857142856, 5.0), size=(41.42857142857143, 41.42857142857143)", "module": "qr_preview", "function": "_draw_finder_marker", "line": 80}
{"timestamp": "2025-06-11T15:45:01.132781", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "  Inner: insert=(260.67346938775506, 12.10204081632653), size=(27.224489795918366, 27.224489795918366)", "module": "qr_preview", "function": "_draw_finder_marker", "line": 81}
{"timestamp": "2025-06-11T15:45:01.132815", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "  Dot: insert=(268.3673469387755, 19.79591836734694), size=(11.83673469387755, 11.83673469387755)", "module": "qr_preview", "function": "_draw_finder_marker", "line": 82}
{"timestamp": "2025-06-11T15:45:01.132882", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Successfully added finder pattern group to SVG", "module": "qr_preview", "function": "_draw_finder_marker", "line": 89}
{"timestamp": "2025-06-11T15:45:01.132913", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Successfully drew finder pattern 2/3", "module": "qr_preview", "function": "generate_preview_svg_modular", "line": 486}
{"timestamp": "2025-06-11T15:45:01.132943", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Drawing finder pattern 3/3 at position (0, 42)", "module": "qr_preview", "function": "generate_preview_svg_modular", "line": 484}
{"timestamp": "2025-06-11T15:45:01.132975", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "_draw_finder_marker called: mx=0, my=42, module_size=5.918367346938775, shape=square, color=#000000, offset=(5, 5)", "module": "qr_preview", "function": "_draw_finder_marker", "line": 56}
{"timestamp": "2025-06-11T15:45:01.133014", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Creating finder pattern group with id='marker-0-42'", "module": "qr_preview", "function": "_draw_finder_marker", "line": 64}
{"timestamp": "2025-06-11T15:45:01.133041", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Drawing square finder pattern:", "module": "qr_preview", "function": "_draw_finder_marker", "line": 79}
{"timestamp": "2025-06-11T15:45:01.133072", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "  Outer: insert=(5.0, 253.57142857142856), size=(41.42857142857143, 41.42857142857143)", "module": "qr_preview", "function": "_draw_finder_marker", "line": 80}
{"timestamp": "2025-06-11T15:45:01.133106", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "  Inner: insert=(12.10204081632653, 260.67346938775506), size=(27.224489795918366, 27.224489795918366)", "module": "qr_preview", "function": "_draw_finder_marker", "line": 81}
{"timestamp": "2025-06-11T15:45:01.133143", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "  Dot: insert=(19.79591836734694, 268.3673469387755), size=(11.83673469387755, 11.83673469387755)", "module": "qr_preview", "function": "_draw_finder_marker", "line": 82}
{"timestamp": "2025-06-11T15:45:01.133206", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Successfully added finder pattern group to SVG", "module": "qr_preview", "function": "_draw_finder_marker", "line": 89}
{"timestamp": "2025-06-11T15:45:01.133233", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Successfully drew finder pattern 3/3", "module": "qr_preview", "function": "generate_preview_svg_modular", "line": 486}
{"timestamp": "2025-06-11T15:45:01.154134", "level": "DEBUG", "logger": "backend.main", "message": "[Auth] Enforced Bearer token authentication for request", "module": "main", "function": "bearer_token_auth_middleware", "line": 274}
{"timestamp": "2025-06-11T15:45:01.155604", "level": "DEBUG", "logger": "backend.main", "message": "[Auth] Enforced Bearer token authentication for request", "module": "main", "function": "bearer_token_auth_middleware", "line": 274}
{"timestamp": "2025-06-11T15:45:03.289775", "level": "DEBUG", "logger": "backend.core.security.jwt_keys", "message": "Token contains key ID: 69b5ada0-20e9-42b1-85e8-1ec80762bb7c", "module": "jwt_keys", "function": "decode_token", "line": 188}
{"timestamp": "2025-06-11T15:45:03.289857", "level": "DEBUG", "logger": "backend.core.security.jwt_keys", "message": "Using key ID 69b5ada0-20e9-42b1-85e8-1ec80762bb7c for verification", "module": "jwt_keys", "function": "decode_token", "line": 195}
{"timestamp": "2025-06-11T15:45:03.289996", "level": "DEBUG", "logger": "backend.services.auth", "message": "JWT decoded successfully with key manager", "module": "auth", "function": "authenticate_token", "line": 683}
{"timestamp": "2025-06-11T15:45:03.291876", "level": "DEBUG", "logger": "backend.services.db_utils", "message": "Query 'auth_lookup_e4ae2d8f-b32f-52d7-99cd-8a33b19652c7' completed in 0.002s", "module": "db_utils", "function": "execute_query_with_timeout", "line": 68}
{"timestamp": "2025-06-11T15:45:03.291943", "level": "DEBUG", "logger": "backend.services.db_utils", "message": "Auth query 'auth_lookup_e4ae2d8f-b32f-52d7-99cd-8a33b19652c7' successful, result format preserved", "module": "db_utils", "function": "execute_query_with_timeout", "line": 72}
{"timestamp": "2025-06-11T15:45:03.293259", "level": "DEBUG", "logger": "backend.core.security.jwt_keys", "message": "Token contains key ID: 69b5ada0-20e9-42b1-85e8-1ec80762bb7c", "module": "jwt_keys", "function": "decode_token", "line": 188}
{"timestamp": "2025-06-11T15:45:03.293897", "level": "DEBUG", "logger": "backend.core.security.jwt_keys", "message": "Using key ID 69b5ada0-20e9-42b1-85e8-1ec80762bb7c for verification", "module": "jwt_keys", "function": "decode_token", "line": 195}
{"timestamp": "2025-06-11T15:45:03.294054", "level": "DEBUG", "logger": "backend.services.auth", "message": "JWT decoded successfully with key manager", "module": "auth", "function": "authenticate_token", "line": 683}
{"timestamp": "2025-06-11T15:45:03.295445", "level": "DEBUG", "logger": "backend.services.db_utils", "message": "Query 'auth_lookup_e4ae2d8f-b32f-52d7-99cd-8a33b19652c7' completed in 0.001s", "module": "db_utils", "function": "execute_query_with_timeout", "line": 68}
{"timestamp": "2025-06-11T15:45:03.295493", "level": "DEBUG", "logger": "backend.services.db_utils", "message": "Auth query 'auth_lookup_e4ae2d8f-b32f-52d7-99cd-8a33b19652c7' successful, result format preserved", "module": "db_utils", "function": "execute_query_with_timeout", "line": 72}
{"timestamp": "2025-06-11T15:45:03.298388", "level": "DEBUG", "logger": "backend.services.auth", "message": "Authentication attempt with token prefix: eyJhbGciOi...", "module": "auth", "function": "get_current_user", "line": 727}
{"timestamp": "2025-06-11T15:45:03.298497", "level": "DEBUG", "logger": "backend.core.security.jwt_keys", "message": "Token contains key ID: 69b5ada0-20e9-42b1-85e8-1ec80762bb7c", "module": "jwt_keys", "function": "decode_token", "line": 188}
{"timestamp": "2025-06-11T15:45:03.298533", "level": "DEBUG", "logger": "backend.core.security.jwt_keys", "message": "Using key ID 69b5ada0-20e9-42b1-85e8-1ec80762bb7c for verification", "module": "jwt_keys", "function": "decode_token", "line": 195}
{"timestamp": "2025-06-11T15:45:03.298659", "level": "DEBUG", "logger": "backend.services.auth", "message": "JWT decoded successfully with key manager", "module": "auth", "function": "authenticate_token", "line": 683}
{"timestamp": "2025-06-11T15:45:03.300153", "level": "DEBUG", "logger": "backend.services.db_utils", "message": "Query 'auth_lookup_e4ae2d8f-b32f-52d7-99cd-8a33b19652c7' completed in 0.001s", "module": "db_utils", "function": "execute_query_with_timeout", "line": 68}
{"timestamp": "2025-06-11T15:45:03.300196", "level": "DEBUG", "logger": "backend.services.db_utils", "message": "Auth query 'auth_lookup_e4ae2d8f-b32f-52d7-99cd-8a33b19652c7' successful, result format preserved", "module": "db_utils", "function": "execute_query_with_timeout", "line": 72}
{"timestamp": "2025-06-11T15:45:03.300245", "level": "DEBUG", "logger": "backend.services.auth", "message": "Successfully authenticated user: <EMAIL>", "module": "auth", "function": "get_current_user", "line": 732}
{"timestamp": "2025-06-11T15:45:03.300274", "level": "DEBUG", "logger": "backend.services.auth", "message": "Successfully authenticated user: <EMAIL>", "module": "auth", "function": "get_current_user", "line": 738}
{"timestamp": "2025-06-11T15:45:03.300607", "level": "DEBUG", "logger": "backend.services.auth", "message": "Authentication attempt with token prefix: eyJhbGciOi...", "module": "auth", "function": "get_current_user", "line": 727}
{"timestamp": "2025-06-11T15:45:03.300668", "level": "DEBUG", "logger": "backend.core.security.jwt_keys", "message": "Token contains key ID: 69b5ada0-20e9-42b1-85e8-1ec80762bb7c", "module": "jwt_keys", "function": "decode_token", "line": 188}
{"timestamp": "2025-06-11T15:45:03.300695", "level": "DEBUG", "logger": "backend.core.security.jwt_keys", "message": "Using key ID 69b5ada0-20e9-42b1-85e8-1ec80762bb7c for verification", "module": "jwt_keys", "function": "decode_token", "line": 195}
{"timestamp": "2025-06-11T15:45:03.300773", "level": "DEBUG", "logger": "backend.services.auth", "message": "JWT decoded successfully with key manager", "module": "auth", "function": "authenticate_token", "line": 683}
{"timestamp": "2025-06-11T15:45:03.301956", "level": "DEBUG", "logger": "backend.routes.qr_preview", "message": "Received QR preview SVG request: {\"qr_type\": \"scooter_unlock\", \"data\": {\"scooter_id\": \"SCOOT123\", \"unlock_code\": \"UNLOCK42\", \"title\": \"Scooter_unlock QR Code\", \"description\": \"Scan to view scooter_unlock content\"}, \"designOptions\": {\"size\": 256, \"margin\": 10, \"pattern\": \"square\", \"error_correction\": \"M\", \"svg_render_dpi\": 300, \"svg_optimize\": true}, \"dimensions\": 300}", "module": "qr_preview", "function": "preview_qr_svg", "line": 242}
{"timestamp": "2025-06-11T15:45:03.302027", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Formatting content for QR type: qrtype.scooter_unlock, content keys: ['scooter_id', 'unlock_code', 'title', 'description']", "module": "qr_preview", "function": "_format_qr_data", "line": 554}
{"timestamp": "2025-06-11T15:45:03.337577", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Drawing 3 finder patterns at positions: [(0, 0), (42, 0), (0, 42)]", "module": "qr_preview", "function": "generate_preview_svg_modular", "line": 479}
{"timestamp": "2025-06-11T15:45:03.337632", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "QR size: 49, marker_shape: square, marker_color: #000000", "module": "qr_preview", "function": "generate_preview_svg_modular", "line": 480}
{"timestamp": "2025-06-11T15:45:03.337665", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Drawing finder pattern 1/3 at position (0, 0)", "module": "qr_preview", "function": "generate_preview_svg_modular", "line": 484}
{"timestamp": "2025-06-11T15:45:03.337706", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "_draw_finder_marker called: mx=0, my=0, module_size=5.918367346938775, shape=square, color=#000000, offset=(5, 5)", "module": "qr_preview", "function": "_draw_finder_marker", "line": 56}
{"timestamp": "2025-06-11T15:45:03.337751", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Creating finder pattern group with id='marker-0-0'", "module": "qr_preview", "function": "_draw_finder_marker", "line": 64}
{"timestamp": "2025-06-11T15:45:03.337790", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Drawing square finder pattern:", "module": "qr_preview", "function": "_draw_finder_marker", "line": 79}
{"timestamp": "2025-06-11T15:45:03.337818", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "  Outer: insert=(5.0, 5.0), size=(41.42857142857143, 41.42857142857143)", "module": "qr_preview", "function": "_draw_finder_marker", "line": 80}
{"timestamp": "2025-06-11T15:45:03.341469", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "  Inner: insert=(12.10204081632653, 12.10204081632653), size=(27.224489795918366, 27.224489795918366)", "module": "qr_preview", "function": "_draw_finder_marker", "line": 81}
{"timestamp": "2025-06-11T15:45:03.341543", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "  Dot: insert=(19.79591836734694, 19.79591836734694), size=(11.83673469387755, 11.83673469387755)", "module": "qr_preview", "function": "_draw_finder_marker", "line": 82}
{"timestamp": "2025-06-11T15:45:03.341658", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Successfully added finder pattern group to SVG", "module": "qr_preview", "function": "_draw_finder_marker", "line": 89}
{"timestamp": "2025-06-11T15:45:03.341691", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Successfully drew finder pattern 1/3", "module": "qr_preview", "function": "generate_preview_svg_modular", "line": 486}
{"timestamp": "2025-06-11T15:45:03.341723", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Drawing finder pattern 2/3 at position (42, 0)", "module": "qr_preview", "function": "generate_preview_svg_modular", "line": 484}
{"timestamp": "2025-06-11T15:45:03.341758", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "_draw_finder_marker called: mx=42, my=0, module_size=5.918367346938775, shape=square, color=#000000, offset=(5, 5)", "module": "qr_preview", "function": "_draw_finder_marker", "line": 56}
{"timestamp": "2025-06-11T15:45:03.341800", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Creating finder pattern group with id='marker-42-0'", "module": "qr_preview", "function": "_draw_finder_marker", "line": 64}
{"timestamp": "2025-06-11T15:45:03.341831", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Drawing square finder pattern:", "module": "qr_preview", "function": "_draw_finder_marker", "line": 79}
{"timestamp": "2025-06-11T15:45:03.341858", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "  Outer: insert=(253.57142857142856, 5.0), size=(41.42857142857143, 41.42857142857143)", "module": "qr_preview", "function": "_draw_finder_marker", "line": 80}
{"timestamp": "2025-06-11T15:45:03.341889", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "  Inner: insert=(260.67346938775506, 12.10204081632653), size=(27.224489795918366, 27.224489795918366)", "module": "qr_preview", "function": "_draw_finder_marker", "line": 81}
{"timestamp": "2025-06-11T15:45:03.359393", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "  Dot: insert=(268.3673469387755, 19.79591836734694), size=(11.83673469387755, 11.83673469387755)", "module": "qr_preview", "function": "_draw_finder_marker", "line": 82}
{"timestamp": "2025-06-11T15:45:03.359568", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Successfully added finder pattern group to SVG", "module": "qr_preview", "function": "_draw_finder_marker", "line": 89}
{"timestamp": "2025-06-11T15:45:03.359610", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Successfully drew finder pattern 2/3", "module": "qr_preview", "function": "generate_preview_svg_modular", "line": 486}
{"timestamp": "2025-06-11T15:45:03.359649", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Drawing finder pattern 3/3 at position (0, 42)", "module": "qr_preview", "function": "generate_preview_svg_modular", "line": 484}
{"timestamp": "2025-06-11T15:45:03.359689", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "_draw_finder_marker called: mx=0, my=42, module_size=5.918367346938775, shape=square, color=#000000, offset=(5, 5)", "module": "qr_preview", "function": "_draw_finder_marker", "line": 56}
{"timestamp": "2025-06-11T15:45:03.359935", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Creating finder pattern group with id='marker-0-42'", "module": "qr_preview", "function": "_draw_finder_marker", "line": 64}
{"timestamp": "2025-06-11T15:45:03.359970", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Drawing square finder pattern:", "module": "qr_preview", "function": "_draw_finder_marker", "line": 79}
{"timestamp": "2025-06-11T15:45:03.360006", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "  Outer: insert=(5.0, 253.57142857142856), size=(41.42857142857143, 41.42857142857143)", "module": "qr_preview", "function": "_draw_finder_marker", "line": 80}
{"timestamp": "2025-06-11T15:45:03.360041", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "  Inner: insert=(12.10204081632653, 260.67346938775506), size=(27.224489795918366, 27.224489795918366)", "module": "qr_preview", "function": "_draw_finder_marker", "line": 81}
{"timestamp": "2025-06-11T15:45:03.360074", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "  Dot: insert=(19.79591836734694, 268.3673469387755), size=(11.83673469387755, 11.83673469387755)", "module": "qr_preview", "function": "_draw_finder_marker", "line": 82}
{"timestamp": "2025-06-11T15:45:03.360157", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Successfully added finder pattern group to SVG", "module": "qr_preview", "function": "_draw_finder_marker", "line": 89}
{"timestamp": "2025-06-11T15:45:03.360189", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Successfully drew finder pattern 3/3", "module": "qr_preview", "function": "generate_preview_svg_modular", "line": 486}
{"timestamp": "2025-06-11T15:45:03.381281", "level": "DEBUG", "logger": "backend.services.db_utils", "message": "Query 'auth_lookup_e4ae2d8f-b32f-52d7-99cd-8a33b19652c7' completed in 0.080s", "module": "db_utils", "function": "execute_query_with_timeout", "line": 68}
{"timestamp": "2025-06-11T15:45:03.381331", "level": "DEBUG", "logger": "backend.services.db_utils", "message": "Auth query 'auth_lookup_e4ae2d8f-b32f-52d7-99cd-8a33b19652c7' successful, result format preserved", "module": "db_utils", "function": "execute_query_with_timeout", "line": 72}
{"timestamp": "2025-06-11T15:45:03.381386", "level": "DEBUG", "logger": "backend.services.auth", "message": "Successfully authenticated user: <EMAIL>", "module": "auth", "function": "get_current_user", "line": 732}
{"timestamp": "2025-06-11T15:45:03.381414", "level": "DEBUG", "logger": "backend.services.auth", "message": "Successfully authenticated user: <EMAIL>", "module": "auth", "function": "get_current_user", "line": 738}
{"timestamp": "2025-06-11T15:45:03.382653", "level": "DEBUG", "logger": "backend.main", "message": "[Auth] Enforced Bearer token authentication for request", "module": "main", "function": "bearer_token_auth_middleware", "line": 274}
{"timestamp": "2025-06-11T15:45:03.384170", "level": "DEBUG", "logger": "backend.routes.qr_preview", "message": "Received QR preview SVG request: {\"qr_type\": \"scooter_unlock\", \"data\": {\"scooter_id\": \"SCOOT123\", \"unlock_code\": \"UNLOCK42\", \"title\": \"Scooter_unlock QR Code\", \"description\": \"Scan to view scooter_unlock content\"}, \"designOptions\": {\"size\": 256, \"margin\": 10, \"pattern\": \"tiny-dots\", \"marker_border_style\": \"square\", \"marker_center_style\": \"square\", \"logo_size\": 48, \"logo_position\": \"center\", \"logo_opacity\": 1, \"frame_style\": \"none\", \"foreground_color\": \"#000000\", \"background_color\": \"#FFFFFF\", \"color_mode\": \"solid\", \"corner_radi", "module": "qr_preview", "function": "preview_qr_svg", "line": 242}
{"timestamp": "2025-06-11T15:45:03.384251", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Formatting content for QR type: qrtype.scooter_unlock, content keys: ['scooter_id', 'unlock_code', 'title', 'description']", "module": "qr_preview", "function": "_format_qr_data", "line": 554}
{"timestamp": "2025-06-11T15:45:03.414269", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Drawing 3 finder patterns at positions: [(0, 0), (42, 0), (0, 42)]", "module": "qr_preview", "function": "generate_preview_svg_modular", "line": 479}
{"timestamp": "2025-06-11T15:45:03.414318", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "QR size: 49, marker_shape: square, marker_color: #000000", "module": "qr_preview", "function": "generate_preview_svg_modular", "line": 480}
{"timestamp": "2025-06-11T15:45:03.414348", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Drawing finder pattern 1/3 at position (0, 0)", "module": "qr_preview", "function": "generate_preview_svg_modular", "line": 484}
{"timestamp": "2025-06-11T15:45:03.415695", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "_draw_finder_marker called: mx=0, my=0, module_size=5.918367346938775, shape=square, color=#000000, offset=(5, 5)", "module": "qr_preview", "function": "_draw_finder_marker", "line": 56}
{"timestamp": "2025-06-11T15:45:03.415801", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Creating finder pattern group with id='marker-0-0'", "module": "qr_preview", "function": "_draw_finder_marker", "line": 64}
{"timestamp": "2025-06-11T15:45:03.415840", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Drawing square finder pattern:", "module": "qr_preview", "function": "_draw_finder_marker", "line": 79}
{"timestamp": "2025-06-11T15:45:03.415876", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "  Outer: insert=(5.0, 5.0), size=(41.42857142857143, 41.42857142857143)", "module": "qr_preview", "function": "_draw_finder_marker", "line": 80}
{"timestamp": "2025-06-11T15:45:03.415912", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "  Inner: insert=(12.10204081632653, 12.10204081632653), size=(27.224489795918366, 27.224489795918366)", "module": "qr_preview", "function": "_draw_finder_marker", "line": 81}
{"timestamp": "2025-06-11T15:45:03.415947", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "  Dot: insert=(19.79591836734694, 19.79591836734694), size=(11.83673469387755, 11.83673469387755)", "module": "qr_preview", "function": "_draw_finder_marker", "line": 82}
{"timestamp": "2025-06-11T15:45:03.416035", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Successfully added finder pattern group to SVG", "module": "qr_preview", "function": "_draw_finder_marker", "line": 89}
{"timestamp": "2025-06-11T15:45:03.416063", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Successfully drew finder pattern 1/3", "module": "qr_preview", "function": "generate_preview_svg_modular", "line": 486}
{"timestamp": "2025-06-11T15:45:03.416093", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Drawing finder pattern 2/3 at position (42, 0)", "module": "qr_preview", "function": "generate_preview_svg_modular", "line": 484}
{"timestamp": "2025-06-11T15:45:03.416126", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "_draw_finder_marker called: mx=42, my=0, module_size=5.918367346938775, shape=square, color=#000000, offset=(5, 5)", "module": "qr_preview", "function": "_draw_finder_marker", "line": 56}
{"timestamp": "2025-06-11T15:45:03.416162", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Creating finder pattern group with id='marker-42-0'", "module": "qr_preview", "function": "_draw_finder_marker", "line": 64}
{"timestamp": "2025-06-11T15:45:03.416190", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Drawing square finder pattern:", "module": "qr_preview", "function": "_draw_finder_marker", "line": 79}
{"timestamp": "2025-06-11T15:45:03.416226", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "  Outer: insert=(253.57142857142856, 5.0), size=(41.42857142857143, 41.42857142857143)", "module": "qr_preview", "function": "_draw_finder_marker", "line": 80}
{"timestamp": "2025-06-11T15:45:03.416257", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "  Inner: insert=(260.67346938775506, 12.10204081632653), size=(27.224489795918366, 27.224489795918366)", "module": "qr_preview", "function": "_draw_finder_marker", "line": 81}
{"timestamp": "2025-06-11T15:45:03.416286", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "  Dot: insert=(268.3673469387755, 19.79591836734694), size=(11.83673469387755, 11.83673469387755)", "module": "qr_preview", "function": "_draw_finder_marker", "line": 82}
{"timestamp": "2025-06-11T15:45:03.416351", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Successfully added finder pattern group to SVG", "module": "qr_preview", "function": "_draw_finder_marker", "line": 89}
{"timestamp": "2025-06-11T15:45:03.416379", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Successfully drew finder pattern 2/3", "module": "qr_preview", "function": "generate_preview_svg_modular", "line": 486}
{"timestamp": "2025-06-11T15:45:03.416406", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Drawing finder pattern 3/3 at position (0, 42)", "module": "qr_preview", "function": "generate_preview_svg_modular", "line": 484}
{"timestamp": "2025-06-11T15:45:03.416434", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "_draw_finder_marker called: mx=0, my=42, module_size=5.918367346938775, shape=square, color=#000000, offset=(5, 5)", "module": "qr_preview", "function": "_draw_finder_marker", "line": 56}
{"timestamp": "2025-06-11T15:45:03.416471", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Creating finder pattern group with id='marker-0-42'", "module": "qr_preview", "function": "_draw_finder_marker", "line": 64}
{"timestamp": "2025-06-11T15:45:03.416497", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Drawing square finder pattern:", "module": "qr_preview", "function": "_draw_finder_marker", "line": 79}
{"timestamp": "2025-06-11T15:45:03.416523", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "  Outer: insert=(5.0, 253.57142857142856), size=(41.42857142857143, 41.42857142857143)", "module": "qr_preview", "function": "_draw_finder_marker", "line": 80}
{"timestamp": "2025-06-11T15:45:03.416549", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "  Inner: insert=(12.10204081632653, 260.67346938775506), size=(27.224489795918366, 27.224489795918366)", "module": "qr_preview", "function": "_draw_finder_marker", "line": 81}
{"timestamp": "2025-06-11T15:45:03.416578", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "  Dot: insert=(19.79591836734694, 268.3673469387755), size=(11.83673469387755, 11.83673469387755)", "module": "qr_preview", "function": "_draw_finder_marker", "line": 82}
{"timestamp": "2025-06-11T15:45:03.416644", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Successfully added finder pattern group to SVG", "module": "qr_preview", "function": "_draw_finder_marker", "line": 89}
{"timestamp": "2025-06-11T15:45:03.416670", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Successfully drew finder pattern 3/3", "module": "qr_preview", "function": "generate_preview_svg_modular", "line": 486}
{"timestamp": "2025-06-11T15:45:03.437958", "level": "DEBUG", "logger": "backend.main", "message": "[Auth] Enforced Bearer token authentication for request", "module": "main", "function": "bearer_token_auth_middleware", "line": 274}
{"timestamp": "2025-06-11T15:45:05.205480", "level": "DEBUG", "logger": "backend.core.security.jwt_keys", "message": "Token contains key ID: 69b5ada0-20e9-42b1-85e8-1ec80762bb7c", "module": "jwt_keys", "function": "decode_token", "line": 188}
{"timestamp": "2025-06-11T15:45:05.205537", "level": "DEBUG", "logger": "backend.core.security.jwt_keys", "message": "Using key ID 69b5ada0-20e9-42b1-85e8-1ec80762bb7c for verification", "module": "jwt_keys", "function": "decode_token", "line": 195}
{"timestamp": "2025-06-11T15:45:05.205655", "level": "DEBUG", "logger": "backend.services.auth", "message": "JWT decoded successfully with key manager", "module": "auth", "function": "authenticate_token", "line": 683}
{"timestamp": "2025-06-11T15:45:05.206351", "level": "DEBUG", "logger": "backend.core.security.jwt_keys", "message": "Token contains key ID: 69b5ada0-20e9-42b1-85e8-1ec80762bb7c", "module": "jwt_keys", "function": "decode_token", "line": 188}
{"timestamp": "2025-06-11T15:45:05.206387", "level": "DEBUG", "logger": "backend.core.security.jwt_keys", "message": "Using key ID 69b5ada0-20e9-42b1-85e8-1ec80762bb7c for verification", "module": "jwt_keys", "function": "decode_token", "line": 195}
{"timestamp": "2025-06-11T15:45:05.212725", "level": "DEBUG", "logger": "backend.services.auth", "message": "JWT decoded successfully with key manager", "module": "auth", "function": "authenticate_token", "line": 683}
{"timestamp": "2025-06-11T15:45:05.214313", "level": "DEBUG", "logger": "backend.services.db_utils", "message": "Query 'auth_lookup_e4ae2d8f-b32f-52d7-99cd-8a33b19652c7' completed in 0.001s", "module": "db_utils", "function": "execute_query_with_timeout", "line": 68}
{"timestamp": "2025-06-11T15:45:05.214356", "level": "DEBUG", "logger": "backend.services.db_utils", "message": "Auth query 'auth_lookup_e4ae2d8f-b32f-52d7-99cd-8a33b19652c7' successful, result format preserved", "module": "db_utils", "function": "execute_query_with_timeout", "line": 72}
{"timestamp": "2025-06-11T15:45:05.214534", "level": "DEBUG", "logger": "backend.services.db_utils", "message": "Query 'auth_lookup_e4ae2d8f-b32f-52d7-99cd-8a33b19652c7' completed in 0.009s", "module": "db_utils", "function": "execute_query_with_timeout", "line": 68}
{"timestamp": "2025-06-11T15:45:05.218760", "level": "DEBUG", "logger": "backend.services.db_utils", "message": "Auth query 'auth_lookup_e4ae2d8f-b32f-52d7-99cd-8a33b19652c7' successful, result format preserved", "module": "db_utils", "function": "execute_query_with_timeout", "line": 72}
{"timestamp": "2025-06-11T15:45:05.224536", "level": "DEBUG", "logger": "backend.services.auth", "message": "Authentication attempt with token prefix: eyJhbGciOi...", "module": "auth", "function": "get_current_user", "line": 727}
{"timestamp": "2025-06-11T15:45:05.224636", "level": "DEBUG", "logger": "backend.core.security.jwt_keys", "message": "Token contains key ID: 69b5ada0-20e9-42b1-85e8-1ec80762bb7c", "module": "jwt_keys", "function": "decode_token", "line": 188}
{"timestamp": "2025-06-11T15:45:05.224671", "level": "DEBUG", "logger": "backend.core.security.jwt_keys", "message": "Using key ID 69b5ada0-20e9-42b1-85e8-1ec80762bb7c for verification", "module": "jwt_keys", "function": "decode_token", "line": 195}
{"timestamp": "2025-06-11T15:45:05.224779", "level": "DEBUG", "logger": "backend.services.auth", "message": "JWT decoded successfully with key manager", "module": "auth", "function": "authenticate_token", "line": 683}
{"timestamp": "2025-06-11T15:45:05.227886", "level": "DEBUG", "logger": "backend.services.auth", "message": "Authentication attempt with token prefix: eyJhbGciOi...", "module": "auth", "function": "get_current_user", "line": 727}
{"timestamp": "2025-06-11T15:45:05.227971", "level": "DEBUG", "logger": "backend.core.security.jwt_keys", "message": "Token contains key ID: 69b5ada0-20e9-42b1-85e8-1ec80762bb7c", "module": "jwt_keys", "function": "decode_token", "line": 188}
{"timestamp": "2025-06-11T15:45:05.228005", "level": "DEBUG", "logger": "backend.core.security.jwt_keys", "message": "Using key ID 69b5ada0-20e9-42b1-85e8-1ec80762bb7c for verification", "module": "jwt_keys", "function": "decode_token", "line": 195}
{"timestamp": "2025-06-11T15:45:05.228105", "level": "DEBUG", "logger": "backend.services.auth", "message": "JWT decoded successfully with key manager", "module": "auth", "function": "authenticate_token", "line": 683}
{"timestamp": "2025-06-11T15:45:05.230621", "level": "DEBUG", "logger": "backend.services.db_utils", "message": "Query 'auth_lookup_e4ae2d8f-b32f-52d7-99cd-8a33b19652c7' completed in 0.006s", "module": "db_utils", "function": "execute_query_with_timeout", "line": 68}
{"timestamp": "2025-06-11T15:45:05.237855", "level": "DEBUG", "logger": "backend.services.db_utils", "message": "Auth query 'auth_lookup_e4ae2d8f-b32f-52d7-99cd-8a33b19652c7' successful, result format preserved", "module": "db_utils", "function": "execute_query_with_timeout", "line": 72}
{"timestamp": "2025-06-11T15:45:05.237964", "level": "DEBUG", "logger": "backend.services.auth", "message": "Successfully authenticated user: <EMAIL>", "module": "auth", "function": "get_current_user", "line": 732}
{"timestamp": "2025-06-11T15:45:05.237999", "level": "DEBUG", "logger": "backend.services.auth", "message": "Successfully authenticated user: <EMAIL>", "module": "auth", "function": "get_current_user", "line": 738}
{"timestamp": "2025-06-11T15:45:05.238120", "level": "DEBUG", "logger": "backend.services.db_utils", "message": "Query 'auth_lookup_e4ae2d8f-b32f-52d7-99cd-8a33b19652c7' completed in 0.010s", "module": "db_utils", "function": "execute_query_with_timeout", "line": 68}
{"timestamp": "2025-06-11T15:45:05.238152", "level": "DEBUG", "logger": "backend.services.db_utils", "message": "Auth query 'auth_lookup_e4ae2d8f-b32f-52d7-99cd-8a33b19652c7' successful, result format preserved", "module": "db_utils", "function": "execute_query_with_timeout", "line": 72}
{"timestamp": "2025-06-11T15:45:05.238199", "level": "DEBUG", "logger": "backend.services.auth", "message": "Successfully authenticated user: <EMAIL>", "module": "auth", "function": "get_current_user", "line": 732}
{"timestamp": "2025-06-11T15:45:05.238228", "level": "DEBUG", "logger": "backend.services.auth", "message": "Successfully authenticated user: <EMAIL>", "module": "auth", "function": "get_current_user", "line": 738}
{"timestamp": "2025-06-11T15:45:05.239733", "level": "DEBUG", "logger": "backend.routes.qr_preview", "message": "Received QR preview SVG request: {\"qr_type\": \"scooter_unlock\", \"data\": {\"scooter_id\": \"SCOOT123\", \"unlock_code\": \"UNLOCK42\", \"title\": \"Scooter_unlock QR Code\", \"description\": \"Scan to view scooter_unlock content\"}, \"designOptions\": {\"size\": 256, \"margin\": 10, \"pattern\": \"classy-rounded\", \"marker_border_style\": \"square\", \"marker_center_style\": \"square\", \"logo_size\": 48, \"logo_position\": \"center\", \"logo_opacity\": 1, \"frame_style\": \"none\", \"foreground_color\": \"#000000\", \"background_color\": \"#FFFFFF\", \"color_mode\": \"solid\", \"corner", "module": "qr_preview", "function": "preview_qr_svg", "line": 242}
{"timestamp": "2025-06-11T15:45:05.247378", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Formatting content for QR type: qrtype.scooter_unlock, content keys: ['scooter_id', 'unlock_code', 'title', 'description']", "module": "qr_preview", "function": "_format_qr_data", "line": 554}
{"timestamp": "2025-06-11T15:45:05.277259", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Drawing 3 finder patterns at positions: [(0, 0), (42, 0), (0, 42)]", "module": "qr_preview", "function": "generate_preview_svg_modular", "line": 479}
{"timestamp": "2025-06-11T15:45:05.277312", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "QR size: 49, marker_shape: square, marker_color: #000000", "module": "qr_preview", "function": "generate_preview_svg_modular", "line": 480}
{"timestamp": "2025-06-11T15:45:05.277347", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Drawing finder pattern 1/3 at position (0, 0)", "module": "qr_preview", "function": "generate_preview_svg_modular", "line": 484}
{"timestamp": "2025-06-11T15:45:05.279177", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "_draw_finder_marker called: mx=0, my=0, module_size=5.918367346938775, shape=square, color=#000000, offset=(5, 5)", "module": "qr_preview", "function": "_draw_finder_marker", "line": 56}
{"timestamp": "2025-06-11T15:45:05.279258", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Creating finder pattern group with id='marker-0-0'", "module": "qr_preview", "function": "_draw_finder_marker", "line": 64}
{"timestamp": "2025-06-11T15:45:05.279295", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Drawing square finder pattern:", "module": "qr_preview", "function": "_draw_finder_marker", "line": 79}
{"timestamp": "2025-06-11T15:45:05.279332", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "  Outer: insert=(5.0, 5.0), size=(41.42857142857143, 41.42857142857143)", "module": "qr_preview", "function": "_draw_finder_marker", "line": 80}
{"timestamp": "2025-06-11T15:45:05.279365", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "  Inner: insert=(12.10204081632653, 12.10204081632653), size=(27.224489795918366, 27.224489795918366)", "module": "qr_preview", "function": "_draw_finder_marker", "line": 81}
{"timestamp": "2025-06-11T15:45:05.285961", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "  Dot: insert=(19.79591836734694, 19.79591836734694), size=(11.83673469387755, 11.83673469387755)", "module": "qr_preview", "function": "_draw_finder_marker", "line": 82}
{"timestamp": "2025-06-11T15:45:05.286112", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Successfully added finder pattern group to SVG", "module": "qr_preview", "function": "_draw_finder_marker", "line": 89}
{"timestamp": "2025-06-11T15:45:05.286150", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Successfully drew finder pattern 1/3", "module": "qr_preview", "function": "generate_preview_svg_modular", "line": 486}
{"timestamp": "2025-06-11T15:45:05.286184", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Drawing finder pattern 2/3 at position (42, 0)", "module": "qr_preview", "function": "generate_preview_svg_modular", "line": 484}
{"timestamp": "2025-06-11T15:45:05.286222", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "_draw_finder_marker called: mx=42, my=0, module_size=5.918367346938775, shape=square, color=#000000, offset=(5, 5)", "module": "qr_preview", "function": "_draw_finder_marker", "line": 56}
{"timestamp": "2025-06-11T15:45:05.287627", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Creating finder pattern group with id='marker-42-0'", "module": "qr_preview", "function": "_draw_finder_marker", "line": 64}
{"timestamp": "2025-06-11T15:45:05.287666", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Drawing square finder pattern:", "module": "qr_preview", "function": "_draw_finder_marker", "line": 79}
{"timestamp": "2025-06-11T15:45:05.287704", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "  Outer: insert=(253.57142857142856, 5.0), size=(41.42857142857143, 41.42857142857143)", "module": "qr_preview", "function": "_draw_finder_marker", "line": 80}
{"timestamp": "2025-06-11T15:45:05.291078", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "  Inner: insert=(260.67346938775506, 12.10204081632653), size=(27.224489795918366, 27.224489795918366)", "module": "qr_preview", "function": "_draw_finder_marker", "line": 81}
{"timestamp": "2025-06-11T15:45:05.291148", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "  Dot: insert=(268.3673469387755, 19.79591836734694), size=(11.83673469387755, 11.83673469387755)", "module": "qr_preview", "function": "_draw_finder_marker", "line": 82}
{"timestamp": "2025-06-11T15:45:05.291256", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Successfully added finder pattern group to SVG", "module": "qr_preview", "function": "_draw_finder_marker", "line": 89}
{"timestamp": "2025-06-11T15:45:05.291299", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Successfully drew finder pattern 2/3", "module": "qr_preview", "function": "generate_preview_svg_modular", "line": 486}
{"timestamp": "2025-06-11T15:45:05.291332", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Drawing finder pattern 3/3 at position (0, 42)", "module": "qr_preview", "function": "generate_preview_svg_modular", "line": 484}
{"timestamp": "2025-06-11T15:45:05.291369", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "_draw_finder_marker called: mx=0, my=42, module_size=5.918367346938775, shape=square, color=#000000, offset=(5, 5)", "module": "qr_preview", "function": "_draw_finder_marker", "line": 56}
{"timestamp": "2025-06-11T15:45:05.292733", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Creating finder pattern group with id='marker-0-42'", "module": "qr_preview", "function": "_draw_finder_marker", "line": 64}
{"timestamp": "2025-06-11T15:45:05.292796", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Drawing square finder pattern:", "module": "qr_preview", "function": "_draw_finder_marker", "line": 79}
{"timestamp": "2025-06-11T15:45:05.292835", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "  Outer: insert=(5.0, 253.57142857142856), size=(41.42857142857143, 41.42857142857143)", "module": "qr_preview", "function": "_draw_finder_marker", "line": 80}
{"timestamp": "2025-06-11T15:45:05.292871", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "  Inner: insert=(12.10204081632653, 260.67346938775506), size=(27.224489795918366, 27.224489795918366)", "module": "qr_preview", "function": "_draw_finder_marker", "line": 81}
{"timestamp": "2025-06-11T15:45:05.294312", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "  Dot: insert=(19.79591836734694, 268.3673469387755), size=(11.83673469387755, 11.83673469387755)", "module": "qr_preview", "function": "_draw_finder_marker", "line": 82}
{"timestamp": "2025-06-11T15:45:05.294436", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Successfully added finder pattern group to SVG", "module": "qr_preview", "function": "_draw_finder_marker", "line": 89}
{"timestamp": "2025-06-11T15:45:05.294470", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Successfully drew finder pattern 3/3", "module": "qr_preview", "function": "generate_preview_svg_modular", "line": 486}
{"timestamp": "2025-06-11T15:45:05.314128", "level": "DEBUG", "logger": "backend.routes.qr_preview", "message": "Received QR preview SVG request: {\"qr_type\": \"scooter_unlock\", \"data\": {\"scooter_id\": \"SCOOT123\", \"unlock_code\": \"UNLOCK42\", \"title\": \"Scooter_unlock QR Code\", \"description\": \"Scan to view scooter_unlock content\"}, \"designOptions\": {\"size\": 256, \"margin\": 10, \"pattern\": \"square\", \"error_correction\": \"M\", \"svg_render_dpi\": 300, \"svg_optimize\": true}, \"dimensions\": 300}", "module": "qr_preview", "function": "preview_qr_svg", "line": 242}
{"timestamp": "2025-06-11T15:45:05.314202", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Formatting content for QR type: qrtype.scooter_unlock, content keys: ['scooter_id', 'unlock_code', 'title', 'description']", "module": "qr_preview", "function": "_format_qr_data", "line": 554}
{"timestamp": "2025-06-11T15:45:05.343696", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Drawing 3 finder patterns at positions: [(0, 0), (42, 0), (0, 42)]", "module": "qr_preview", "function": "generate_preview_svg_modular", "line": 479}
{"timestamp": "2025-06-11T15:45:05.343747", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "QR size: 49, marker_shape: square, marker_color: #000000", "module": "qr_preview", "function": "generate_preview_svg_modular", "line": 480}
{"timestamp": "2025-06-11T15:45:05.343784", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Drawing finder pattern 1/3 at position (0, 0)", "module": "qr_preview", "function": "generate_preview_svg_modular", "line": 484}
{"timestamp": "2025-06-11T15:45:05.355452", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "_draw_finder_marker called: mx=0, my=0, module_size=5.918367346938775, shape=square, color=#000000, offset=(5, 5)", "module": "qr_preview", "function": "_draw_finder_marker", "line": 56}
{"timestamp": "2025-06-11T15:45:05.355558", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Creating finder pattern group with id='marker-0-0'", "module": "qr_preview", "function": "_draw_finder_marker", "line": 64}
{"timestamp": "2025-06-11T15:45:05.355598", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Drawing square finder pattern:", "module": "qr_preview", "function": "_draw_finder_marker", "line": 79}
{"timestamp": "2025-06-11T15:45:05.355632", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "  Outer: insert=(5.0, 5.0), size=(41.42857142857143, 41.42857142857143)", "module": "qr_preview", "function": "_draw_finder_marker", "line": 80}
{"timestamp": "2025-06-11T15:45:05.355665", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "  Inner: insert=(12.10204081632653, 12.10204081632653), size=(27.224489795918366, 27.224489795918366)", "module": "qr_preview", "function": "_draw_finder_marker", "line": 81}
{"timestamp": "2025-06-11T15:45:05.378493", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "  Dot: insert=(19.79591836734694, 19.79591836734694), size=(11.83673469387755, 11.83673469387755)", "module": "qr_preview", "function": "_draw_finder_marker", "line": 82}
{"timestamp": "2025-06-11T15:45:05.378656", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Successfully added finder pattern group to SVG", "module": "qr_preview", "function": "_draw_finder_marker", "line": 89}
{"timestamp": "2025-06-11T15:45:05.378695", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Successfully drew finder pattern 1/3", "module": "qr_preview", "function": "generate_preview_svg_modular", "line": 486}
{"timestamp": "2025-06-11T15:45:05.378728", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Drawing finder pattern 2/3 at position (42, 0)", "module": "qr_preview", "function": "generate_preview_svg_modular", "line": 484}
{"timestamp": "2025-06-11T15:45:05.378766", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "_draw_finder_marker called: mx=42, my=0, module_size=5.918367346938775, shape=square, color=#000000, offset=(5, 5)", "module": "qr_preview", "function": "_draw_finder_marker", "line": 56}
{"timestamp": "2025-06-11T15:45:05.378869", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Creating finder pattern group with id='marker-42-0'", "module": "qr_preview", "function": "_draw_finder_marker", "line": 64}
{"timestamp": "2025-06-11T15:45:05.378901", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Drawing square finder pattern:", "module": "qr_preview", "function": "_draw_finder_marker", "line": 79}
{"timestamp": "2025-06-11T15:45:05.378934", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "  Outer: insert=(253.57142857142856, 5.0), size=(41.42857142857143, 41.42857142857143)", "module": "qr_preview", "function": "_draw_finder_marker", "line": 80}
{"timestamp": "2025-06-11T15:45:05.380440", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "  Inner: insert=(260.67346938775506, 12.10204081632653), size=(27.224489795918366, 27.224489795918366)", "module": "qr_preview", "function": "_draw_finder_marker", "line": 81}
{"timestamp": "2025-06-11T15:45:05.380498", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "  Dot: insert=(268.3673469387755, 19.79591836734694), size=(11.83673469387755, 11.83673469387755)", "module": "qr_preview", "function": "_draw_finder_marker", "line": 82}
{"timestamp": "2025-06-11T15:45:05.380595", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Successfully added finder pattern group to SVG", "module": "qr_preview", "function": "_draw_finder_marker", "line": 89}
{"timestamp": "2025-06-11T15:45:05.380628", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Successfully drew finder pattern 2/3", "module": "qr_preview", "function": "generate_preview_svg_modular", "line": 486}
{"timestamp": "2025-06-11T15:45:05.380658", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Drawing finder pattern 3/3 at position (0, 42)", "module": "qr_preview", "function": "generate_preview_svg_modular", "line": 484}
{"timestamp": "2025-06-11T15:45:05.382441", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "_draw_finder_marker called: mx=0, my=42, module_size=5.918367346938775, shape=square, color=#000000, offset=(5, 5)", "module": "qr_preview", "function": "_draw_finder_marker", "line": 56}
{"timestamp": "2025-06-11T15:45:05.382493", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Creating finder pattern group with id='marker-0-42'", "module": "qr_preview", "function": "_draw_finder_marker", "line": 64}
{"timestamp": "2025-06-11T15:45:05.382524", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Drawing square finder pattern:", "module": "qr_preview", "function": "_draw_finder_marker", "line": 79}
{"timestamp": "2025-06-11T15:45:05.382556", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "  Outer: insert=(5.0, 253.57142857142856), size=(41.42857142857143, 41.42857142857143)", "module": "qr_preview", "function": "_draw_finder_marker", "line": 80}
{"timestamp": "2025-06-11T15:45:05.382589", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "  Inner: insert=(12.10204081632653, 260.67346938775506), size=(27.224489795918366, 27.224489795918366)", "module": "qr_preview", "function": "_draw_finder_marker", "line": 81}
{"timestamp": "2025-06-11T15:45:05.382621", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "  Dot: insert=(19.79591836734694, 268.3673469387755), size=(11.83673469387755, 11.83673469387755)", "module": "qr_preview", "function": "_draw_finder_marker", "line": 82}
{"timestamp": "2025-06-11T15:45:05.382697", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Successfully added finder pattern group to SVG", "module": "qr_preview", "function": "_draw_finder_marker", "line": 89}
{"timestamp": "2025-06-11T15:45:05.382727", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Successfully drew finder pattern 3/3", "module": "qr_preview", "function": "generate_preview_svg_modular", "line": 486}
{"timestamp": "2025-06-11T15:45:05.403782", "level": "DEBUG", "logger": "backend.main", "message": "[Auth] Enforced Bearer token authentication for request", "module": "main", "function": "bearer_token_auth_middleware", "line": 274}
{"timestamp": "2025-06-11T15:45:05.404079", "level": "DEBUG", "logger": "backend.main", "message": "[Auth] Enforced Bearer token authentication for request", "module": "main", "function": "bearer_token_auth_middleware", "line": 274}
{"timestamp": "2025-06-11T15:45:06.946103", "level": "DEBUG", "logger": "backend.core.security.jwt_keys", "message": "Token contains key ID: 69b5ada0-20e9-42b1-85e8-1ec80762bb7c", "module": "jwt_keys", "function": "decode_token", "line": 188}
{"timestamp": "2025-06-11T15:45:06.946156", "level": "DEBUG", "logger": "backend.core.security.jwt_keys", "message": "Using key ID 69b5ada0-20e9-42b1-85e8-1ec80762bb7c for verification", "module": "jwt_keys", "function": "decode_token", "line": 195}
{"timestamp": "2025-06-11T15:45:06.946272", "level": "DEBUG", "logger": "backend.services.auth", "message": "JWT decoded successfully with key manager", "module": "auth", "function": "authenticate_token", "line": 683}
{"timestamp": "2025-06-11T15:45:06.946814", "level": "DEBUG", "logger": "backend.core.security.jwt_keys", "message": "Token contains key ID: 69b5ada0-20e9-42b1-85e8-1ec80762bb7c", "module": "jwt_keys", "function": "decode_token", "line": 188}
{"timestamp": "2025-06-11T15:45:06.970807", "level": "DEBUG", "logger": "backend.core.security.jwt_keys", "message": "Using key ID 69b5ada0-20e9-42b1-85e8-1ec80762bb7c for verification", "module": "jwt_keys", "function": "decode_token", "line": 195}
{"timestamp": "2025-06-11T15:45:06.971014", "level": "DEBUG", "logger": "backend.services.auth", "message": "JWT decoded successfully with key manager", "module": "auth", "function": "authenticate_token", "line": 683}
{"timestamp": "2025-06-11T15:45:06.980099", "level": "DEBUG", "logger": "backend.services.db_utils", "message": "Query 'auth_lookup_e4ae2d8f-b32f-52d7-99cd-8a33b19652c7' completed in 0.034s", "module": "db_utils", "function": "execute_query_with_timeout", "line": 68}
{"timestamp": "2025-06-11T15:45:06.980157", "level": "DEBUG", "logger": "backend.services.db_utils", "message": "Auth query 'auth_lookup_e4ae2d8f-b32f-52d7-99cd-8a33b19652c7' successful, result format preserved", "module": "db_utils", "function": "execute_query_with_timeout", "line": 72}
{"timestamp": "2025-06-11T15:45:06.997364", "level": "DEBUG", "logger": "backend.services.db_utils", "message": "Query 'auth_lookup_e4ae2d8f-b32f-52d7-99cd-8a33b19652c7' completed in 0.009s", "module": "db_utils", "function": "execute_query_with_timeout", "line": 68}
{"timestamp": "2025-06-11T15:45:06.997445", "level": "DEBUG", "logger": "backend.services.db_utils", "message": "Auth query 'auth_lookup_e4ae2d8f-b32f-52d7-99cd-8a33b19652c7' successful, result format preserved", "module": "db_utils", "function": "execute_query_with_timeout", "line": 72}
{"timestamp": "2025-06-11T15:45:07.003620", "level": "DEBUG", "logger": "backend.services.auth", "message": "Authentication attempt with token prefix: eyJhbGciOi...", "module": "auth", "function": "get_current_user", "line": 727}
{"timestamp": "2025-06-11T15:45:07.003730", "level": "DEBUG", "logger": "backend.core.security.jwt_keys", "message": "Token contains key ID: 69b5ada0-20e9-42b1-85e8-1ec80762bb7c", "module": "jwt_keys", "function": "decode_token", "line": 188}
{"timestamp": "2025-06-11T15:45:07.003761", "level": "DEBUG", "logger": "backend.core.security.jwt_keys", "message": "Using key ID 69b5ada0-20e9-42b1-85e8-1ec80762bb7c for verification", "module": "jwt_keys", "function": "decode_token", "line": 195}
{"timestamp": "2025-06-11T15:45:07.003866", "level": "DEBUG", "logger": "backend.services.auth", "message": "JWT decoded successfully with key manager", "module": "auth", "function": "authenticate_token", "line": 683}
{"timestamp": "2025-06-11T15:45:07.004245", "level": "DEBUG", "logger": "backend.services.auth", "message": "Authentication attempt with token prefix: eyJhbGciOi...", "module": "auth", "function": "get_current_user", "line": 727}
{"timestamp": "2025-06-11T15:45:07.004299", "level": "DEBUG", "logger": "backend.core.security.jwt_keys", "message": "Token contains key ID: 69b5ada0-20e9-42b1-85e8-1ec80762bb7c", "module": "jwt_keys", "function": "decode_token", "line": 188}
{"timestamp": "2025-06-11T15:45:07.004329", "level": "DEBUG", "logger": "backend.core.security.jwt_keys", "message": "Using key ID 69b5ada0-20e9-42b1-85e8-1ec80762bb7c for verification", "module": "jwt_keys", "function": "decode_token", "line": 195}
{"timestamp": "2025-06-11T15:45:07.004394", "level": "DEBUG", "logger": "backend.services.auth", "message": "JWT decoded successfully with key manager", "module": "auth", "function": "authenticate_token", "line": 683}
{"timestamp": "2025-06-11T15:45:07.005227", "level": "DEBUG", "logger": "backend.services.db_utils", "message": "Query 'auth_lookup_e4ae2d8f-b32f-52d7-99cd-8a33b19652c7' completed in 0.001s", "module": "db_utils", "function": "execute_query_with_timeout", "line": 68}
{"timestamp": "2025-06-11T15:45:07.005265", "level": "DEBUG", "logger": "backend.services.db_utils", "message": "Auth query 'auth_lookup_e4ae2d8f-b32f-52d7-99cd-8a33b19652c7' successful, result format preserved", "module": "db_utils", "function": "execute_query_with_timeout", "line": 72}
{"timestamp": "2025-06-11T15:45:07.005316", "level": "DEBUG", "logger": "backend.services.auth", "message": "Successfully authenticated user: <EMAIL>", "module": "auth", "function": "get_current_user", "line": 732}
{"timestamp": "2025-06-11T15:45:07.005346", "level": "DEBUG", "logger": "backend.services.auth", "message": "Successfully authenticated user: <EMAIL>", "module": "auth", "function": "get_current_user", "line": 738}
{"timestamp": "2025-06-11T15:45:07.005518", "level": "DEBUG", "logger": "backend.services.db_utils", "message": "Query 'auth_lookup_e4ae2d8f-b32f-52d7-99cd-8a33b19652c7' completed in 0.001s", "module": "db_utils", "function": "execute_query_with_timeout", "line": 68}
{"timestamp": "2025-06-11T15:45:07.005548", "level": "DEBUG", "logger": "backend.services.db_utils", "message": "Auth query 'auth_lookup_e4ae2d8f-b32f-52d7-99cd-8a33b19652c7' successful, result format preserved", "module": "db_utils", "function": "execute_query_with_timeout", "line": 72}
{"timestamp": "2025-06-11T15:45:07.005584", "level": "DEBUG", "logger": "backend.services.auth", "message": "Successfully authenticated user: <EMAIL>", "module": "auth", "function": "get_current_user", "line": 732}
{"timestamp": "2025-06-11T15:45:07.005612", "level": "DEBUG", "logger": "backend.services.auth", "message": "Successfully authenticated user: <EMAIL>", "module": "auth", "function": "get_current_user", "line": 738}
{"timestamp": "2025-06-11T15:45:07.006646", "level": "DEBUG", "logger": "backend.routes.qr_preview", "message": "Received QR preview SVG request: {\"qr_type\": \"scooter_unlock\", \"data\": {\"scooter_id\": \"SCOOT123\", \"unlock_code\": \"UNLOCK42\", \"title\": \"Scooter_unlock QR Code\", \"description\": \"Scan to view scooter_unlock content\"}, \"designOptions\": {\"size\": 256, \"margin\": 10, \"pattern\": \"square\", \"error_correction\": \"M\", \"svg_render_dpi\": 300, \"svg_optimize\": true}, \"dimensions\": 300}", "module": "qr_preview", "function": "preview_qr_svg", "line": 242}
{"timestamp": "2025-06-11T15:45:07.006707", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Formatting content for QR type: qrtype.scooter_unlock, content keys: ['scooter_id', 'unlock_code', 'title', 'description']", "module": "qr_preview", "function": "_format_qr_data", "line": 554}
{"timestamp": "2025-06-11T15:45:07.036685", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Drawing 3 finder patterns at positions: [(0, 0), (42, 0), (0, 42)]", "module": "qr_preview", "function": "generate_preview_svg_modular", "line": 479}
{"timestamp": "2025-06-11T15:45:07.036739", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "QR size: 49, marker_shape: square, marker_color: #000000", "module": "qr_preview", "function": "generate_preview_svg_modular", "line": 480}
{"timestamp": "2025-06-11T15:45:07.036771", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Drawing finder pattern 1/3 at position (0, 0)", "module": "qr_preview", "function": "generate_preview_svg_modular", "line": 484}
{"timestamp": "2025-06-11T15:45:07.036808", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "_draw_finder_marker called: mx=0, my=0, module_size=5.918367346938775, shape=square, color=#000000, offset=(5, 5)", "module": "qr_preview", "function": "_draw_finder_marker", "line": 56}
{"timestamp": "2025-06-11T15:45:07.036849", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Creating finder pattern group with id='marker-0-0'", "module": "qr_preview", "function": "_draw_finder_marker", "line": 64}
{"timestamp": "2025-06-11T15:45:07.036878", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Drawing square finder pattern:", "module": "qr_preview", "function": "_draw_finder_marker", "line": 79}
{"timestamp": "2025-06-11T15:45:07.036905", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "  Outer: insert=(5.0, 5.0), size=(41.42857142857143, 41.42857142857143)", "module": "qr_preview", "function": "_draw_finder_marker", "line": 80}
{"timestamp": "2025-06-11T15:45:07.036936", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "  Inner: insert=(12.10204081632653, 12.10204081632653), size=(27.224489795918366, 27.224489795918366)", "module": "qr_preview", "function": "_draw_finder_marker", "line": 81}
{"timestamp": "2025-06-11T15:45:07.036965", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "  Dot: insert=(19.79591836734694, 19.79591836734694), size=(11.83673469387755, 11.83673469387755)", "module": "qr_preview", "function": "_draw_finder_marker", "line": 82}
{"timestamp": "2025-06-11T15:45:07.037032", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Successfully added finder pattern group to SVG", "module": "qr_preview", "function": "_draw_finder_marker", "line": 89}
{"timestamp": "2025-06-11T15:45:07.037061", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Successfully drew finder pattern 1/3", "module": "qr_preview", "function": "generate_preview_svg_modular", "line": 486}
{"timestamp": "2025-06-11T15:45:07.037086", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Drawing finder pattern 2/3 at position (42, 0)", "module": "qr_preview", "function": "generate_preview_svg_modular", "line": 484}
{"timestamp": "2025-06-11T15:45:07.037115", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "_draw_finder_marker called: mx=42, my=0, module_size=5.918367346938775, shape=square, color=#000000, offset=(5, 5)", "module": "qr_preview", "function": "_draw_finder_marker", "line": 56}
{"timestamp": "2025-06-11T15:45:07.037150", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Creating finder pattern group with id='marker-42-0'", "module": "qr_preview", "function": "_draw_finder_marker", "line": 64}
{"timestamp": "2025-06-11T15:45:07.037175", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Drawing square finder pattern:", "module": "qr_preview", "function": "_draw_finder_marker", "line": 79}
{"timestamp": "2025-06-11T15:45:07.037203", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "  Outer: insert=(253.57142857142856, 5.0), size=(41.42857142857143, 41.42857142857143)", "module": "qr_preview", "function": "_draw_finder_marker", "line": 80}
{"timestamp": "2025-06-11T15:45:07.037230", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "  Inner: insert=(260.67346938775506, 12.10204081632653), size=(27.224489795918366, 27.224489795918366)", "module": "qr_preview", "function": "_draw_finder_marker", "line": 81}
{"timestamp": "2025-06-11T15:45:07.037257", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "  Dot: insert=(268.3673469387755, 19.79591836734694), size=(11.83673469387755, 11.83673469387755)", "module": "qr_preview", "function": "_draw_finder_marker", "line": 82}
{"timestamp": "2025-06-11T15:45:07.037316", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Successfully added finder pattern group to SVG", "module": "qr_preview", "function": "_draw_finder_marker", "line": 89}
{"timestamp": "2025-06-11T15:45:07.037341", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Successfully drew finder pattern 2/3", "module": "qr_preview", "function": "generate_preview_svg_modular", "line": 486}
{"timestamp": "2025-06-11T15:45:07.037367", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Drawing finder pattern 3/3 at position (0, 42)", "module": "qr_preview", "function": "generate_preview_svg_modular", "line": 484}
{"timestamp": "2025-06-11T15:45:07.037396", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "_draw_finder_marker called: mx=0, my=42, module_size=5.918367346938775, shape=square, color=#000000, offset=(5, 5)", "module": "qr_preview", "function": "_draw_finder_marker", "line": 56}
{"timestamp": "2025-06-11T15:45:07.037428", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Creating finder pattern group with id='marker-0-42'", "module": "qr_preview", "function": "_draw_finder_marker", "line": 64}
{"timestamp": "2025-06-11T15:45:07.037454", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Drawing square finder pattern:", "module": "qr_preview", "function": "_draw_finder_marker", "line": 79}
{"timestamp": "2025-06-11T15:45:07.037483", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "  Outer: insert=(5.0, 253.57142857142856), size=(41.42857142857143, 41.42857142857143)", "module": "qr_preview", "function": "_draw_finder_marker", "line": 80}
{"timestamp": "2025-06-11T15:45:07.037509", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "  Inner: insert=(12.10204081632653, 260.67346938775506), size=(27.224489795918366, 27.224489795918366)", "module": "qr_preview", "function": "_draw_finder_marker", "line": 81}
{"timestamp": "2025-06-11T15:45:07.037537", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "  Dot: insert=(19.79591836734694, 268.3673469387755), size=(11.83673469387755, 11.83673469387755)", "module": "qr_preview", "function": "_draw_finder_marker", "line": 82}
{"timestamp": "2025-06-11T15:45:07.037596", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Successfully added finder pattern group to SVG", "module": "qr_preview", "function": "_draw_finder_marker", "line": 89}
{"timestamp": "2025-06-11T15:45:07.037621", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Successfully drew finder pattern 3/3", "module": "qr_preview", "function": "generate_preview_svg_modular", "line": 486}
{"timestamp": "2025-06-11T15:45:07.057195", "level": "DEBUG", "logger": "backend.routes.qr_preview", "message": "Received QR preview SVG request: {\"qr_type\": \"scooter_unlock\", \"data\": {\"scooter_id\": \"SCOOT123\", \"unlock_code\": \"UNLOCK42\", \"title\": \"Scooter_unlock QR Code\", \"description\": \"Scan to view scooter_unlock content\"}, \"designOptions\": {\"size\": 256, \"margin\": 10, \"pattern\": \"square\", \"marker_border_style\": \"square\", \"marker_center_style\": \"square\", \"logo_size\": 48, \"logo_position\": \"center\", \"logo_opacity\": 1, \"frame_style\": \"none\", \"foreground_color\": \"#000000\", \"background_color\": \"#FFFFFF\", \"color_mode\": \"solid\", \"corner_radius\"", "module": "qr_preview", "function": "preview_qr_svg", "line": 242}
{"timestamp": "2025-06-11T15:45:07.057272", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Formatting content for QR type: qrtype.scooter_unlock, content keys: ['scooter_id', 'unlock_code', 'title', 'description']", "module": "qr_preview", "function": "_format_qr_data", "line": 554}
{"timestamp": "2025-06-11T15:45:07.085948", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Drawing 3 finder patterns at positions: [(0, 0), (42, 0), (0, 42)]", "module": "qr_preview", "function": "generate_preview_svg_modular", "line": 479}
{"timestamp": "2025-06-11T15:45:07.086005", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "QR size: 49, marker_shape: square, marker_color: #000000", "module": "qr_preview", "function": "generate_preview_svg_modular", "line": 480}
{"timestamp": "2025-06-11T15:45:07.086039", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Drawing finder pattern 1/3 at position (0, 0)", "module": "qr_preview", "function": "generate_preview_svg_modular", "line": 484}
{"timestamp": "2025-06-11T15:45:07.086168", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "_draw_finder_marker called: mx=0, my=0, module_size=5.918367346938775, shape=square, color=#000000, offset=(5, 5)", "module": "qr_preview", "function": "_draw_finder_marker", "line": 56}
{"timestamp": "2025-06-11T15:45:07.086231", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Creating finder pattern group with id='marker-0-0'", "module": "qr_preview", "function": "_draw_finder_marker", "line": 64}
{"timestamp": "2025-06-11T15:45:07.086264", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Drawing square finder pattern:", "module": "qr_preview", "function": "_draw_finder_marker", "line": 79}
{"timestamp": "2025-06-11T15:45:07.086292", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "  Outer: insert=(5.0, 5.0), size=(41.42857142857143, 41.42857142857143)", "module": "qr_preview", "function": "_draw_finder_marker", "line": 80}
{"timestamp": "2025-06-11T15:45:07.086323", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "  Inner: insert=(12.10204081632653, 12.10204081632653), size=(27.224489795918366, 27.224489795918366)", "module": "qr_preview", "function": "_draw_finder_marker", "line": 81}
{"timestamp": "2025-06-11T15:45:07.086355", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "  Dot: insert=(19.79591836734694, 19.79591836734694), size=(11.83673469387755, 11.83673469387755)", "module": "qr_preview", "function": "_draw_finder_marker", "line": 82}
{"timestamp": "2025-06-11T15:45:07.086427", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Successfully added finder pattern group to SVG", "module": "qr_preview", "function": "_draw_finder_marker", "line": 89}
{"timestamp": "2025-06-11T15:45:07.086458", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Successfully drew finder pattern 1/3", "module": "qr_preview", "function": "generate_preview_svg_modular", "line": 486}
{"timestamp": "2025-06-11T15:45:07.086486", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Drawing finder pattern 2/3 at position (42, 0)", "module": "qr_preview", "function": "generate_preview_svg_modular", "line": 484}
{"timestamp": "2025-06-11T15:45:07.086516", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "_draw_finder_marker called: mx=42, my=0, module_size=5.918367346938775, shape=square, color=#000000, offset=(5, 5)", "module": "qr_preview", "function": "_draw_finder_marker", "line": 56}
{"timestamp": "2025-06-11T15:45:07.086550", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Creating finder pattern group with id='marker-42-0'", "module": "qr_preview", "function": "_draw_finder_marker", "line": 64}
{"timestamp": "2025-06-11T15:45:07.086578", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Drawing square finder pattern:", "module": "qr_preview", "function": "_draw_finder_marker", "line": 79}
{"timestamp": "2025-06-11T15:45:07.086607", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "  Outer: insert=(253.57142857142856, 5.0), size=(41.42857142857143, 41.42857142857143)", "module": "qr_preview", "function": "_draw_finder_marker", "line": 80}
{"timestamp": "2025-06-11T15:45:07.086632", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "  Inner: insert=(260.67346938775506, 12.10204081632653), size=(27.224489795918366, 27.224489795918366)", "module": "qr_preview", "function": "_draw_finder_marker", "line": 81}
{"timestamp": "2025-06-11T15:45:07.086656", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "  Dot: insert=(268.3673469387755, 19.79591836734694), size=(11.83673469387755, 11.83673469387755)", "module": "qr_preview", "function": "_draw_finder_marker", "line": 82}
{"timestamp": "2025-06-11T15:45:07.086916", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Successfully added finder pattern group to SVG", "module": "qr_preview", "function": "_draw_finder_marker", "line": 89}
{"timestamp": "2025-06-11T15:45:07.086955", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Successfully drew finder pattern 2/3", "module": "qr_preview", "function": "generate_preview_svg_modular", "line": 486}
{"timestamp": "2025-06-11T15:45:07.086985", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Drawing finder pattern 3/3 at position (0, 42)", "module": "qr_preview", "function": "generate_preview_svg_modular", "line": 484}
{"timestamp": "2025-06-11T15:45:07.087020", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "_draw_finder_marker called: mx=0, my=42, module_size=5.918367346938775, shape=square, color=#000000, offset=(5, 5)", "module": "qr_preview", "function": "_draw_finder_marker", "line": 56}
{"timestamp": "2025-06-11T15:45:07.087057", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Creating finder pattern group with id='marker-0-42'", "module": "qr_preview", "function": "_draw_finder_marker", "line": 64}
{"timestamp": "2025-06-11T15:45:07.087083", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Drawing square finder pattern:", "module": "qr_preview", "function": "_draw_finder_marker", "line": 79}
{"timestamp": "2025-06-11T15:45:07.087113", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "  Outer: insert=(5.0, 253.57142857142856), size=(41.42857142857143, 41.42857142857143)", "module": "qr_preview", "function": "_draw_finder_marker", "line": 80}
{"timestamp": "2025-06-11T15:45:07.087143", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "  Inner: insert=(12.10204081632653, 260.67346938775506), size=(27.224489795918366, 27.224489795918366)", "module": "qr_preview", "function": "_draw_finder_marker", "line": 81}
{"timestamp": "2025-06-11T15:45:07.087176", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "  Dot: insert=(19.79591836734694, 268.3673469387755), size=(11.83673469387755, 11.83673469387755)", "module": "qr_preview", "function": "_draw_finder_marker", "line": 82}
{"timestamp": "2025-06-11T15:45:07.087240", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Successfully added finder pattern group to SVG", "module": "qr_preview", "function": "_draw_finder_marker", "line": 89}
{"timestamp": "2025-06-11T15:45:07.087264", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Successfully drew finder pattern 3/3", "module": "qr_preview", "function": "generate_preview_svg_modular", "line": 486}
{"timestamp": "2025-06-11T15:45:07.107857", "level": "DEBUG", "logger": "backend.main", "message": "[Auth] Enforced Bearer token authentication for request", "module": "main", "function": "bearer_token_auth_middleware", "line": 274}
{"timestamp": "2025-06-11T15:45:07.108288", "level": "DEBUG", "logger": "backend.main", "message": "[Auth] Enforced Bearer token authentication for request", "module": "main", "function": "bearer_token_auth_middleware", "line": 274}
{"timestamp": "2025-06-11T15:45:08.628974", "level": "DEBUG", "logger": "backend.core.security.jwt_keys", "message": "Token contains key ID: 69b5ada0-20e9-42b1-85e8-1ec80762bb7c", "module": "jwt_keys", "function": "decode_token", "line": 188}
{"timestamp": "2025-06-11T15:45:08.629035", "level": "DEBUG", "logger": "backend.core.security.jwt_keys", "message": "Using key ID 69b5ada0-20e9-42b1-85e8-1ec80762bb7c for verification", "module": "jwt_keys", "function": "decode_token", "line": 195}
{"timestamp": "2025-06-11T15:45:08.629178", "level": "DEBUG", "logger": "backend.services.auth", "message": "JWT decoded successfully with key manager", "module": "auth", "function": "authenticate_token", "line": 683}
{"timestamp": "2025-06-11T15:45:08.629790", "level": "DEBUG", "logger": "backend.core.security.jwt_keys", "message": "Token contains key ID: 69b5ada0-20e9-42b1-85e8-1ec80762bb7c", "module": "jwt_keys", "function": "decode_token", "line": 188}
{"timestamp": "2025-06-11T15:45:08.633880", "level": "DEBUG", "logger": "backend.core.security.jwt_keys", "message": "Using key ID 69b5ada0-20e9-42b1-85e8-1ec80762bb7c for verification", "module": "jwt_keys", "function": "decode_token", "line": 195}
{"timestamp": "2025-06-11T15:45:08.634067", "level": "DEBUG", "logger": "backend.services.auth", "message": "JWT decoded successfully with key manager", "module": "auth", "function": "authenticate_token", "line": 683}
{"timestamp": "2025-06-11T15:45:08.635838", "level": "DEBUG", "logger": "backend.services.db_utils", "message": "Query 'auth_lookup_e4ae2d8f-b32f-52d7-99cd-8a33b19652c7' completed in 0.006s", "module": "db_utils", "function": "execute_query_with_timeout", "line": 68}
{"timestamp": "2025-06-11T15:45:08.635897", "level": "DEBUG", "logger": "backend.services.db_utils", "message": "Auth query 'auth_lookup_e4ae2d8f-b32f-52d7-99cd-8a33b19652c7' successful, result format preserved", "module": "db_utils", "function": "execute_query_with_timeout", "line": 72}
{"timestamp": "2025-06-11T15:45:08.636024", "level": "DEBUG", "logger": "backend.services.db_utils", "message": "Query 'auth_lookup_e4ae2d8f-b32f-52d7-99cd-8a33b19652c7' completed in 0.002s", "module": "db_utils", "function": "execute_query_with_timeout", "line": 68}
{"timestamp": "2025-06-11T15:45:08.636430", "level": "DEBUG", "logger": "backend.services.db_utils", "message": "Auth query 'auth_lookup_e4ae2d8f-b32f-52d7-99cd-8a33b19652c7' successful, result format preserved", "module": "db_utils", "function": "execute_query_with_timeout", "line": 72}
{"timestamp": "2025-06-11T15:45:08.639806", "level": "DEBUG", "logger": "backend.services.auth", "message": "Authentication attempt with token prefix: eyJhbGciOi...", "module": "auth", "function": "get_current_user", "line": 727}
{"timestamp": "2025-06-11T15:45:08.639896", "level": "DEBUG", "logger": "backend.core.security.jwt_keys", "message": "Token contains key ID: 69b5ada0-20e9-42b1-85e8-1ec80762bb7c", "module": "jwt_keys", "function": "decode_token", "line": 188}
{"timestamp": "2025-06-11T15:45:08.639933", "level": "DEBUG", "logger": "backend.core.security.jwt_keys", "message": "Using key ID 69b5ada0-20e9-42b1-85e8-1ec80762bb7c for verification", "module": "jwt_keys", "function": "decode_token", "line": 195}
{"timestamp": "2025-06-11T15:45:08.645591", "level": "DEBUG", "logger": "backend.services.auth", "message": "JWT decoded successfully with key manager", "module": "auth", "function": "authenticate_token", "line": 683}
{"timestamp": "2025-06-11T15:45:08.646190", "level": "DEBUG", "logger": "backend.services.auth", "message": "Authentication attempt with token prefix: eyJhbGciOi...", "module": "auth", "function": "get_current_user", "line": 727}
{"timestamp": "2025-06-11T15:45:08.646267", "level": "DEBUG", "logger": "backend.core.security.jwt_keys", "message": "Token contains key ID: 69b5ada0-20e9-42b1-85e8-1ec80762bb7c", "module": "jwt_keys", "function": "decode_token", "line": 188}
{"timestamp": "2025-06-11T15:45:08.646299", "level": "DEBUG", "logger": "backend.core.security.jwt_keys", "message": "Using key ID 69b5ada0-20e9-42b1-85e8-1ec80762bb7c for verification", "module": "jwt_keys", "function": "decode_token", "line": 195}
{"timestamp": "2025-06-11T15:45:08.646398", "level": "DEBUG", "logger": "backend.services.auth", "message": "JWT decoded successfully with key manager", "module": "auth", "function": "authenticate_token", "line": 683}
{"timestamp": "2025-06-11T15:45:08.647614", "level": "DEBUG", "logger": "backend.services.db_utils", "message": "Query 'auth_lookup_e4ae2d8f-b32f-52d7-99cd-8a33b19652c7' completed in 0.002s", "module": "db_utils", "function": "execute_query_with_timeout", "line": 68}
{"timestamp": "2025-06-11T15:45:08.647646", "level": "DEBUG", "logger": "backend.services.db_utils", "message": "Auth query 'auth_lookup_e4ae2d8f-b32f-52d7-99cd-8a33b19652c7' successful, result format preserved", "module": "db_utils", "function": "execute_query_with_timeout", "line": 72}
{"timestamp": "2025-06-11T15:45:08.647686", "level": "DEBUG", "logger": "backend.services.auth", "message": "Successfully authenticated user: <EMAIL>", "module": "auth", "function": "get_current_user", "line": 732}
{"timestamp": "2025-06-11T15:45:08.647711", "level": "DEBUG", "logger": "backend.services.auth", "message": "Successfully authenticated user: <EMAIL>", "module": "auth", "function": "get_current_user", "line": 738}
{"timestamp": "2025-06-11T15:45:08.648939", "level": "DEBUG", "logger": "backend.services.db_utils", "message": "Query 'auth_lookup_e4ae2d8f-b32f-52d7-99cd-8a33b19652c7' completed in 0.001s", "module": "db_utils", "function": "execute_query_with_timeout", "line": 68}
{"timestamp": "2025-06-11T15:45:08.648968", "level": "DEBUG", "logger": "backend.services.db_utils", "message": "Auth query 'auth_lookup_e4ae2d8f-b32f-52d7-99cd-8a33b19652c7' successful, result format preserved", "module": "db_utils", "function": "execute_query_with_timeout", "line": 72}
{"timestamp": "2025-06-11T15:45:08.649003", "level": "DEBUG", "logger": "backend.services.auth", "message": "Successfully authenticated user: <EMAIL>", "module": "auth", "function": "get_current_user", "line": 732}
{"timestamp": "2025-06-11T15:45:08.649028", "level": "DEBUG", "logger": "backend.services.auth", "message": "Successfully authenticated user: <EMAIL>", "module": "auth", "function": "get_current_user", "line": 738}
{"timestamp": "2025-06-11T15:45:08.650261", "level": "DEBUG", "logger": "backend.routes.qr_preview", "message": "Received QR preview SVG request: {\"qr_type\": \"scooter_unlock\", \"data\": {\"scooter_id\": \"SCOOT123\", \"unlock_code\": \"UNLOCK42\", \"title\": \"Scooter_unlock QR Code\", \"description\": \"Scan to view scooter_unlock content\"}, \"designOptions\": {\"size\": 256, \"margin\": 10, \"pattern\": \"square\", \"error_correction\": \"M\", \"svg_render_dpi\": 300, \"svg_optimize\": true}, \"dimensions\": 300}", "module": "qr_preview", "function": "preview_qr_svg", "line": 242}
{"timestamp": "2025-06-11T15:45:08.650329", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Formatting content for QR type: qrtype.scooter_unlock, content keys: ['scooter_id', 'unlock_code', 'title', 'description']", "module": "qr_preview", "function": "_format_qr_data", "line": 554}
{"timestamp": "2025-06-11T15:45:08.679555", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Drawing 3 finder patterns at positions: [(0, 0), (42, 0), (0, 42)]", "module": "qr_preview", "function": "generate_preview_svg_modular", "line": 479}
{"timestamp": "2025-06-11T15:45:08.679616", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "QR size: 49, marker_shape: square, marker_color: #000000", "module": "qr_preview", "function": "generate_preview_svg_modular", "line": 480}
{"timestamp": "2025-06-11T15:45:08.679654", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Drawing finder pattern 1/3 at position (0, 0)", "module": "qr_preview", "function": "generate_preview_svg_modular", "line": 484}
{"timestamp": "2025-06-11T15:45:08.679694", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "_draw_finder_marker called: mx=0, my=0, module_size=5.918367346938775, shape=square, color=#000000, offset=(5, 5)", "module": "qr_preview", "function": "_draw_finder_marker", "line": 56}
{"timestamp": "2025-06-11T15:45:08.679743", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Creating finder pattern group with id='marker-0-0'", "module": "qr_preview", "function": "_draw_finder_marker", "line": 64}
{"timestamp": "2025-06-11T15:45:08.679777", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Drawing square finder pattern:", "module": "qr_preview", "function": "_draw_finder_marker", "line": 79}
{"timestamp": "2025-06-11T15:45:08.679810", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "  Outer: insert=(5.0, 5.0), size=(41.42857142857143, 41.42857142857143)", "module": "qr_preview", "function": "_draw_finder_marker", "line": 80}
{"timestamp": "2025-06-11T15:45:08.679844", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "  Inner: insert=(12.10204081632653, 12.10204081632653), size=(27.224489795918366, 27.224489795918366)", "module": "qr_preview", "function": "_draw_finder_marker", "line": 81}
{"timestamp": "2025-06-11T15:45:08.679876", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "  Dot: insert=(19.79591836734694, 19.79591836734694), size=(11.83673469387755, 11.83673469387755)", "module": "qr_preview", "function": "_draw_finder_marker", "line": 82}
{"timestamp": "2025-06-11T15:45:08.679948", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Successfully added finder pattern group to SVG", "module": "qr_preview", "function": "_draw_finder_marker", "line": 89}
{"timestamp": "2025-06-11T15:45:08.679980", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Successfully drew finder pattern 1/3", "module": "qr_preview", "function": "generate_preview_svg_modular", "line": 486}
{"timestamp": "2025-06-11T15:45:08.680011", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Drawing finder pattern 2/3 at position (42, 0)", "module": "qr_preview", "function": "generate_preview_svg_modular", "line": 484}
{"timestamp": "2025-06-11T15:45:08.680044", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "_draw_finder_marker called: mx=42, my=0, module_size=5.918367346938775, shape=square, color=#000000, offset=(5, 5)", "module": "qr_preview", "function": "_draw_finder_marker", "line": 56}
{"timestamp": "2025-06-11T15:45:08.680081", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Creating finder pattern group with id='marker-42-0'", "module": "qr_preview", "function": "_draw_finder_marker", "line": 64}
{"timestamp": "2025-06-11T15:45:08.680112", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Drawing square finder pattern:", "module": "qr_preview", "function": "_draw_finder_marker", "line": 79}
{"timestamp": "2025-06-11T15:45:08.680147", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "  Outer: insert=(253.57142857142856, 5.0), size=(41.42857142857143, 41.42857142857143)", "module": "qr_preview", "function": "_draw_finder_marker", "line": 80}
{"timestamp": "2025-06-11T15:45:08.680178", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "  Inner: insert=(260.67346938775506, 12.10204081632653), size=(27.224489795918366, 27.224489795918366)", "module": "qr_preview", "function": "_draw_finder_marker", "line": 81}
{"timestamp": "2025-06-11T15:45:08.680212", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "  Dot: insert=(268.3673469387755, 19.79591836734694), size=(11.83673469387755, 11.83673469387755)", "module": "qr_preview", "function": "_draw_finder_marker", "line": 82}
{"timestamp": "2025-06-11T15:45:08.680276", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Successfully added finder pattern group to SVG", "module": "qr_preview", "function": "_draw_finder_marker", "line": 89}
{"timestamp": "2025-06-11T15:45:08.680306", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Successfully drew finder pattern 2/3", "module": "qr_preview", "function": "generate_preview_svg_modular", "line": 486}
{"timestamp": "2025-06-11T15:45:08.680335", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Drawing finder pattern 3/3 at position (0, 42)", "module": "qr_preview", "function": "generate_preview_svg_modular", "line": 484}
{"timestamp": "2025-06-11T15:45:08.680367", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "_draw_finder_marker called: mx=0, my=42, module_size=5.918367346938775, shape=square, color=#000000, offset=(5, 5)", "module": "qr_preview", "function": "_draw_finder_marker", "line": 56}
{"timestamp": "2025-06-11T15:45:08.680403", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Creating finder pattern group with id='marker-0-42'", "module": "qr_preview", "function": "_draw_finder_marker", "line": 64}
{"timestamp": "2025-06-11T15:45:08.680434", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Drawing square finder pattern:", "module": "qr_preview", "function": "_draw_finder_marker", "line": 79}
{"timestamp": "2025-06-11T15:45:08.680465", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "  Outer: insert=(5.0, 253.57142857142856), size=(41.42857142857143, 41.42857142857143)", "module": "qr_preview", "function": "_draw_finder_marker", "line": 80}
{"timestamp": "2025-06-11T15:45:08.680496", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "  Inner: insert=(12.10204081632653, 260.67346938775506), size=(27.224489795918366, 27.224489795918366)", "module": "qr_preview", "function": "_draw_finder_marker", "line": 81}
{"timestamp": "2025-06-11T15:45:08.680533", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "  Dot: insert=(19.79591836734694, 268.3673469387755), size=(11.83673469387755, 11.83673469387755)", "module": "qr_preview", "function": "_draw_finder_marker", "line": 82}
{"timestamp": "2025-06-11T15:45:08.680592", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Successfully added finder pattern group to SVG", "module": "qr_preview", "function": "_draw_finder_marker", "line": 89}
{"timestamp": "2025-06-11T15:45:08.680618", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Successfully drew finder pattern 3/3", "module": "qr_preview", "function": "generate_preview_svg_modular", "line": 486}
{"timestamp": "2025-06-11T15:45:08.700217", "level": "DEBUG", "logger": "backend.routes.qr_preview", "message": "Received QR preview SVG request: {\"qr_type\": \"scooter_unlock\", \"data\": {\"scooter_id\": \"SCOOT123\", \"unlock_code\": \"UNLOCK42\", \"title\": \"Scooter_unlock QR Code\", \"description\": \"Scan to view scooter_unlock content\"}, \"designOptions\": {\"size\": 256, \"margin\": 10, \"pattern\": \"rounded\", \"marker_border_style\": \"square\", \"marker_center_style\": \"square\", \"logo_size\": 48, \"logo_position\": \"center\", \"logo_opacity\": 1, \"frame_style\": \"none\", \"foreground_color\": \"#000000\", \"background_color\": \"#FFFFFF\", \"color_mode\": \"solid\", \"corner_radius", "module": "qr_preview", "function": "preview_qr_svg", "line": 242}
{"timestamp": "2025-06-11T15:45:08.700308", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Formatting content for QR type: qrtype.scooter_unlock, content keys: ['scooter_id', 'unlock_code', 'title', 'description']", "module": "qr_preview", "function": "_format_qr_data", "line": 554}
{"timestamp": "2025-06-11T15:45:08.729196", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Drawing 3 finder patterns at positions: [(0, 0), (42, 0), (0, 42)]", "module": "qr_preview", "function": "generate_preview_svg_modular", "line": 479}
{"timestamp": "2025-06-11T15:45:08.729252", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "QR size: 49, marker_shape: square, marker_color: #000000", "module": "qr_preview", "function": "generate_preview_svg_modular", "line": 480}
{"timestamp": "2025-06-11T15:45:08.729292", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Drawing finder pattern 1/3 at position (0, 0)", "module": "qr_preview", "function": "generate_preview_svg_modular", "line": 484}
{"timestamp": "2025-06-11T15:45:08.729424", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "_draw_finder_marker called: mx=0, my=0, module_size=5.918367346938775, shape=square, color=#000000, offset=(5, 5)", "module": "qr_preview", "function": "_draw_finder_marker", "line": 56}
{"timestamp": "2025-06-11T15:45:08.729522", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Creating finder pattern group with id='marker-0-0'", "module": "qr_preview", "function": "_draw_finder_marker", "line": 64}
{"timestamp": "2025-06-11T15:45:08.729563", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Drawing square finder pattern:", "module": "qr_preview", "function": "_draw_finder_marker", "line": 79}
{"timestamp": "2025-06-11T15:45:08.729600", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "  Outer: insert=(5.0, 5.0), size=(41.42857142857143, 41.42857142857143)", "module": "qr_preview", "function": "_draw_finder_marker", "line": 80}
{"timestamp": "2025-06-11T15:45:08.729636", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "  Inner: insert=(12.10204081632653, 12.10204081632653), size=(27.224489795918366, 27.224489795918366)", "module": "qr_preview", "function": "_draw_finder_marker", "line": 81}
{"timestamp": "2025-06-11T15:45:08.729668", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "  Dot: insert=(19.79591836734694, 19.79591836734694), size=(11.83673469387755, 11.83673469387755)", "module": "qr_preview", "function": "_draw_finder_marker", "line": 82}
{"timestamp": "2025-06-11T15:45:08.729749", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Successfully added finder pattern group to SVG", "module": "qr_preview", "function": "_draw_finder_marker", "line": 89}
{"timestamp": "2025-06-11T15:45:08.729781", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Successfully drew finder pattern 1/3", "module": "qr_preview", "function": "generate_preview_svg_modular", "line": 486}
{"timestamp": "2025-06-11T15:45:08.729818", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Drawing finder pattern 2/3 at position (42, 0)", "module": "qr_preview", "function": "generate_preview_svg_modular", "line": 484}
{"timestamp": "2025-06-11T15:45:08.729853", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "_draw_finder_marker called: mx=42, my=0, module_size=5.918367346938775, shape=square, color=#000000, offset=(5, 5)", "module": "qr_preview", "function": "_draw_finder_marker", "line": 56}
{"timestamp": "2025-06-11T15:45:08.729893", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Creating finder pattern group with id='marker-42-0'", "module": "qr_preview", "function": "_draw_finder_marker", "line": 64}
{"timestamp": "2025-06-11T15:45:08.729922", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Drawing square finder pattern:", "module": "qr_preview", "function": "_draw_finder_marker", "line": 79}
{"timestamp": "2025-06-11T15:45:08.729957", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "  Outer: insert=(253.57142857142856, 5.0), size=(41.42857142857143, 41.42857142857143)", "module": "qr_preview", "function": "_draw_finder_marker", "line": 80}
{"timestamp": "2025-06-11T15:45:08.729988", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "  Inner: insert=(260.67346938775506, 12.10204081632653), size=(27.224489795918366, 27.224489795918366)", "module": "qr_preview", "function": "_draw_finder_marker", "line": 81}
{"timestamp": "2025-06-11T15:45:08.730020", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "  Dot: insert=(268.3673469387755, 19.79591836734694), size=(11.83673469387755, 11.83673469387755)", "module": "qr_preview", "function": "_draw_finder_marker", "line": 82}
{"timestamp": "2025-06-11T15:45:08.730085", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Successfully added finder pattern group to SVG", "module": "qr_preview", "function": "_draw_finder_marker", "line": 89}
{"timestamp": "2025-06-11T15:45:08.730114", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Successfully drew finder pattern 2/3", "module": "qr_preview", "function": "generate_preview_svg_modular", "line": 486}
{"timestamp": "2025-06-11T15:45:08.730148", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Drawing finder pattern 3/3 at position (0, 42)", "module": "qr_preview", "function": "generate_preview_svg_modular", "line": 484}
{"timestamp": "2025-06-11T15:45:08.730180", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "_draw_finder_marker called: mx=0, my=42, module_size=5.918367346938775, shape=square, color=#000000, offset=(5, 5)", "module": "qr_preview", "function": "_draw_finder_marker", "line": 56}
{"timestamp": "2025-06-11T15:45:08.730217", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Creating finder pattern group with id='marker-0-42'", "module": "qr_preview", "function": "_draw_finder_marker", "line": 64}
{"timestamp": "2025-06-11T15:45:08.730246", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Drawing square finder pattern:", "module": "qr_preview", "function": "_draw_finder_marker", "line": 79}
{"timestamp": "2025-06-11T15:45:08.730276", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "  Outer: insert=(5.0, 253.57142857142856), size=(41.42857142857143, 41.42857142857143)", "module": "qr_preview", "function": "_draw_finder_marker", "line": 80}
{"timestamp": "2025-06-11T15:45:08.730306", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "  Inner: insert=(12.10204081632653, 260.67346938775506), size=(27.224489795918366, 27.224489795918366)", "module": "qr_preview", "function": "_draw_finder_marker", "line": 81}
{"timestamp": "2025-06-11T15:45:08.730336", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "  Dot: insert=(19.79591836734694, 268.3673469387755), size=(11.83673469387755, 11.83673469387755)", "module": "qr_preview", "function": "_draw_finder_marker", "line": 82}
{"timestamp": "2025-06-11T15:45:08.730398", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Successfully added finder pattern group to SVG", "module": "qr_preview", "function": "_draw_finder_marker", "line": 89}
{"timestamp": "2025-06-11T15:45:08.730424", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Successfully drew finder pattern 3/3", "module": "qr_preview", "function": "generate_preview_svg_modular", "line": 486}
{"timestamp": "2025-06-11T15:45:08.750839", "level": "DEBUG", "logger": "backend.main", "message": "[Auth] Enforced Bearer token authentication for request", "module": "main", "function": "bearer_token_auth_middleware", "line": 274}
{"timestamp": "2025-06-11T15:45:08.751341", "level": "DEBUG", "logger": "backend.main", "message": "[Auth] Enforced Bearer token authentication for request", "module": "main", "function": "bearer_token_auth_middleware", "line": 274}
{"timestamp": "2025-06-11T15:45:25.477300", "level": "DEBUG", "logger": "backend.main", "message": "[Auth] Enforced Bearer token authentication for request", "module": "main", "function": "bearer_token_auth_middleware", "line": 274}
