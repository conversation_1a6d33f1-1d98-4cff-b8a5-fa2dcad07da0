{"timestamp": "2025-06-11T14:24:58.060443", "level": "DEBUG", "logger": "backend.main", "message": "[Auth] Enforced Bearer token authentication for request", "module": "main", "function": "bearer_token_auth_middleware", "line": 274}
{"timestamp": "2025-06-11T14:25:07.756419", "level": "DEBUG", "logger": "backend.core.security.jwt_keys", "message": "Token contains key ID: acda791c-cc77-4888-8c9b-83dd6e9d53cc", "module": "jwt_keys", "function": "decode_token", "line": 188}
{"timestamp": "2025-06-11T14:25:07.756659", "level": "DEBUG", "logger": "backend.core.security.jwt_keys", "message": "Using key ID acda791c-cc77-4888-8c9b-83dd6e9d53cc for verification", "module": "jwt_keys", "function": "decode_token", "line": 195}
{"timestamp": "2025-06-11T14:25:07.758499", "level": "DEBUG", "logger": "backend.services.auth", "message": "JWT decoded successfully with key manager", "module": "auth", "function": "authenticate_token", "line": 683}
{"timestamp": "2025-06-11T14:25:07.790632", "level": "DEBUG", "logger": "backend.services.db_utils", "message": "Query 'auth_lookup_e4ae2d8f-b32f-52d7-99cd-8a33b19652c7' completed in 0.021s", "module": "db_utils", "function": "execute_query_with_timeout", "line": 68}
{"timestamp": "2025-06-11T14:25:07.790723", "level": "DEBUG", "logger": "backend.services.db_utils", "message": "Auth query 'auth_lookup_e4ae2d8f-b32f-52d7-99cd-8a33b19652c7' successful, result format preserved", "module": "db_utils", "function": "execute_query_with_timeout", "line": 72}
{"timestamp": "2025-06-11T14:25:07.817663", "level": "DEBUG", "logger": "backend.services.auth", "message": "Authentication attempt with token prefix: eyJhbGciOi...", "module": "auth", "function": "get_current_user", "line": 727}
{"timestamp": "2025-06-11T14:25:07.817816", "level": "DEBUG", "logger": "backend.core.security.jwt_keys", "message": "Token contains key ID: acda791c-cc77-4888-8c9b-83dd6e9d53cc", "module": "jwt_keys", "function": "decode_token", "line": 188}
{"timestamp": "2025-06-11T14:25:07.817871", "level": "DEBUG", "logger": "backend.core.security.jwt_keys", "message": "Using key ID acda791c-cc77-4888-8c9b-83dd6e9d53cc for verification", "module": "jwt_keys", "function": "decode_token", "line": 195}
{"timestamp": "2025-06-11T14:25:07.818028", "level": "DEBUG", "logger": "backend.services.auth", "message": "JWT decoded successfully with key manager", "module": "auth", "function": "authenticate_token", "line": 683}
{"timestamp": "2025-06-11T14:25:07.823161", "level": "DEBUG", "logger": "backend.services.db_utils", "message": "Query 'auth_lookup_e4ae2d8f-b32f-52d7-99cd-8a33b19652c7' completed in 0.004s", "module": "db_utils", "function": "execute_query_with_timeout", "line": 68}
{"timestamp": "2025-06-11T14:25:07.823259", "level": "DEBUG", "logger": "backend.services.db_utils", "message": "Auth query 'auth_lookup_e4ae2d8f-b32f-52d7-99cd-8a33b19652c7' successful, result format preserved", "module": "db_utils", "function": "execute_query_with_timeout", "line": 72}
{"timestamp": "2025-06-11T14:25:07.823346", "level": "DEBUG", "logger": "backend.services.auth", "message": "Successfully authenticated user: <EMAIL>", "module": "auth", "function": "get_current_user", "line": 732}
{"timestamp": "2025-06-11T14:25:07.823770", "level": "DEBUG", "logger": "backend.services.auth", "message": "Successfully authenticated user: <EMAIL>", "module": "auth", "function": "get_current_user", "line": 738}
{"timestamp": "2025-06-11T14:25:07.877702", "level": "DEBUG", "logger": "backend.routes.qr_preview", "message": "Received QR preview SVG request: {\"qr_type\": \"url\", \"data\": {\"url\": \"https://example.com\", \"utm_source\": \"\", \"utm_medium\": \"\", \"utm_campaign\": \"\", \"short_url\": false, \"title\": \"Url QR Code\", \"description\": \"Scan to view url content\"}, \"designOptions\": {\"size\": 256, \"margin\": 10, \"pattern\": \"classic\", \"marker_border_style\": \"square\", \"marker_border_color\": \"#000000\", \"marker_center_style\": \"circle\", \"marker_center_color\": \"#000000\", \"logo_size\": 48, \"logo_position\": \"center\", \"logo_opacity\": 1, \"frame_style\": \"classic\", \"frame_c", "module": "qr_preview", "function": "preview_qr_svg", "line": 242}
{"timestamp": "2025-06-11T14:25:07.880351", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Formatting content for QR type: qrtype.url, content keys: ['url', 'utm_source', 'utm_medium', 'utm_campaign', 'short_url', 'title', 'description']", "module": "qr_preview", "function": "_format_qr_data", "line": 599}
{"timestamp": "2025-06-11T14:25:07.889992", "level": "ERROR", "logger": "backend.services.qr_preview", "message": "Error formatting QR data for type qrtype.url: TRANSLATION_SERVICE", "module": "qr_preview", "function": "_format_qr_data", "line": 1639, "exception": "Traceback (most recent call last):\n  File \"/Users/<USER>/aihub/qrvibe/backend/services/qr_preview.py\", line 1602, in _format_qr_data\n    elif qr_type_str == QRType.TRANSLATION_SERVICE.lower():\n                        ^^^^^^^^^^^^^^^^^^^^^^^^^^\n  File \"/opt/homebrew/Cellar/python@3.11/3.11.11/Frameworks/Python.framework/Versions/3.11/lib/python3.11/enum.py\", line 786, in __getattr__\n    raise AttributeError(name) from None\nAttributeError: TRANSLATION_SERVICE"}
{"timestamp": "2025-06-11T14:25:07.969256", "level": "DEBUG", "logger": "backend.main", "message": "[Auth] Enforced Bearer token authentication for request", "module": "main", "function": "bearer_token_auth_middleware", "line": 274}
{"timestamp": "2025-06-11T14:25:14.023006", "level": "DEBUG", "logger": "backend.core.security.jwt_keys", "message": "Token contains key ID: acda791c-cc77-4888-8c9b-83dd6e9d53cc", "module": "jwt_keys", "function": "decode_token", "line": 188}
{"timestamp": "2025-06-11T14:25:14.023286", "level": "DEBUG", "logger": "backend.core.security.jwt_keys", "message": "Using key ID acda791c-cc77-4888-8c9b-83dd6e9d53cc for verification", "module": "jwt_keys", "function": "decode_token", "line": 195}
{"timestamp": "2025-06-11T14:25:14.023555", "level": "DEBUG", "logger": "backend.services.auth", "message": "JWT decoded successfully with key manager", "module": "auth", "function": "authenticate_token", "line": 683}
{"timestamp": "2025-06-11T14:25:14.026677", "level": "DEBUG", "logger": "backend.services.db_utils", "message": "Query 'auth_lookup_e4ae2d8f-b32f-52d7-99cd-8a33b19652c7' completed in 0.003s", "module": "db_utils", "function": "execute_query_with_timeout", "line": 68}
{"timestamp": "2025-06-11T14:25:14.026749", "level": "DEBUG", "logger": "backend.services.db_utils", "message": "Auth query 'auth_lookup_e4ae2d8f-b32f-52d7-99cd-8a33b19652c7' successful, result format preserved", "module": "db_utils", "function": "execute_query_with_timeout", "line": 72}
{"timestamp": "2025-06-11T14:25:14.041332", "level": "DEBUG", "logger": "backend.services.auth", "message": "Authentication attempt with token prefix: eyJhbGciOi...", "module": "auth", "function": "get_current_user", "line": 727}
{"timestamp": "2025-06-11T14:25:14.041478", "level": "DEBUG", "logger": "backend.core.security.jwt_keys", "message": "Token contains key ID: acda791c-cc77-4888-8c9b-83dd6e9d53cc", "module": "jwt_keys", "function": "decode_token", "line": 188}
{"timestamp": "2025-06-11T14:25:14.041532", "level": "DEBUG", "logger": "backend.core.security.jwt_keys", "message": "Using key ID acda791c-cc77-4888-8c9b-83dd6e9d53cc for verification", "module": "jwt_keys", "function": "decode_token", "line": 195}
{"timestamp": "2025-06-11T14:25:14.041690", "level": "DEBUG", "logger": "backend.services.auth", "message": "JWT decoded successfully with key manager", "module": "auth", "function": "authenticate_token", "line": 683}
{"timestamp": "2025-06-11T14:25:14.043148", "level": "DEBUG", "logger": "backend.services.db_utils", "message": "Query 'auth_lookup_e4ae2d8f-b32f-52d7-99cd-8a33b19652c7' completed in 0.001s", "module": "db_utils", "function": "execute_query_with_timeout", "line": 68}
{"timestamp": "2025-06-11T14:25:14.043218", "level": "DEBUG", "logger": "backend.services.db_utils", "message": "Auth query 'auth_lookup_e4ae2d8f-b32f-52d7-99cd-8a33b19652c7' successful, result format preserved", "module": "db_utils", "function": "execute_query_with_timeout", "line": 72}
{"timestamp": "2025-06-11T14:25:14.043335", "level": "DEBUG", "logger": "backend.services.auth", "message": "Successfully authenticated user: <EMAIL>", "module": "auth", "function": "get_current_user", "line": 732}
{"timestamp": "2025-06-11T14:25:14.043398", "level": "DEBUG", "logger": "backend.services.auth", "message": "Successfully authenticated user: <EMAIL>", "module": "auth", "function": "get_current_user", "line": 738}
{"timestamp": "2025-06-11T14:25:14.047966", "level": "DEBUG", "logger": "backend.routes.qr_preview", "message": "Received QR preview SVG request: {\"qr_type\": \"scooter_unlock\", \"data\": {\"scooter_id\": \"SCOOT123\", \"unlock_code\": \"UNLOCK42\", \"title\": \"Scooter_unlock QR Code\", \"description\": \"Scan to view scooter_unlock content\"}, \"designOptions\": {\"size\": 256, \"margin\": 10, \"pattern\": \"classic\", \"marker_border_style\": \"square\", \"marker_border_color\": \"#000000\", \"marker_center_style\": \"circle\", \"marker_center_color\": \"#000000\", \"logo_size\": 48, \"logo_position\": \"center\", \"logo_opacity\": 1, \"frame_style\": \"classic\", \"frame_color\": \"#000000\", \"f", "module": "qr_preview", "function": "preview_qr_svg", "line": 242}
{"timestamp": "2025-06-11T14:25:14.048361", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Formatting content for QR type: qrtype.scooter_unlock, content keys: ['scooter_id', 'unlock_code', 'title', 'description']", "module": "qr_preview", "function": "_format_qr_data", "line": 599}
{"timestamp": "2025-06-11T14:25:14.055222", "level": "ERROR", "logger": "backend.services.qr_preview", "message": "Error formatting QR data for type qrtype.scooter_unlock: TRANSLATION_SERVICE", "module": "qr_preview", "function": "_format_qr_data", "line": 1639, "exception": "Traceback (most recent call last):\n  File \"/Users/<USER>/aihub/qrvibe/backend/services/qr_preview.py\", line 1602, in _format_qr_data\n    elif qr_type_str == QRType.TRANSLATION_SERVICE.lower():\n                        ^^^^^^^^^^^^^^^^^^^^^^^^^^\n  File \"/opt/homebrew/Cellar/python@3.11/3.11.11/Frameworks/Python.framework/Versions/3.11/lib/python3.11/enum.py\", line 786, in __getattr__\n    raise AttributeError(name) from None\nAttributeError: TRANSLATION_SERVICE"}
{"timestamp": "2025-06-11T14:25:14.912368", "level": "DEBUG", "logger": "backend.main", "message": "[Auth] Enforced Bearer token authentication for request", "module": "main", "function": "bearer_token_auth_middleware", "line": 274}
{"timestamp": "2025-06-11T14:25:17.153512", "level": "DEBUG", "logger": "backend.core.security.jwt_keys", "message": "Token contains key ID: acda791c-cc77-4888-8c9b-83dd6e9d53cc", "module": "jwt_keys", "function": "decode_token", "line": 188}
{"timestamp": "2025-06-11T14:25:17.153594", "level": "DEBUG", "logger": "backend.core.security.jwt_keys", "message": "Using key ID acda791c-cc77-4888-8c9b-83dd6e9d53cc for verification", "module": "jwt_keys", "function": "decode_token", "line": 195}
{"timestamp": "2025-06-11T14:25:17.153793", "level": "DEBUG", "logger": "backend.services.auth", "message": "JWT decoded successfully with key manager", "module": "auth", "function": "authenticate_token", "line": 683}
{"timestamp": "2025-06-11T14:25:17.155329", "level": "DEBUG", "logger": "backend.core.security.jwt_keys", "message": "Token contains key ID: acda791c-cc77-4888-8c9b-83dd6e9d53cc", "module": "jwt_keys", "function": "decode_token", "line": 188}
{"timestamp": "2025-06-11T14:25:17.155377", "level": "DEBUG", "logger": "backend.core.security.jwt_keys", "message": "Using key ID acda791c-cc77-4888-8c9b-83dd6e9d53cc for verification", "module": "jwt_keys", "function": "decode_token", "line": 195}
{"timestamp": "2025-06-11T14:25:17.155472", "level": "DEBUG", "logger": "backend.services.auth", "message": "JWT decoded successfully with key manager", "module": "auth", "function": "authenticate_token", "line": 683}
{"timestamp": "2025-06-11T14:25:17.156828", "level": "DEBUG", "logger": "backend.services.db_utils", "message": "Query 'auth_lookup_e4ae2d8f-b32f-52d7-99cd-8a33b19652c7' completed in 0.003s", "module": "db_utils", "function": "execute_query_with_timeout", "line": 68}
{"timestamp": "2025-06-11T14:25:17.156935", "level": "DEBUG", "logger": "backend.services.db_utils", "message": "Auth query 'auth_lookup_e4ae2d8f-b32f-52d7-99cd-8a33b19652c7' successful, result format preserved", "module": "db_utils", "function": "execute_query_with_timeout", "line": 72}
{"timestamp": "2025-06-11T14:25:17.160587", "level": "DEBUG", "logger": "backend.services.db_utils", "message": "Query 'auth_lookup_e4ae2d8f-b32f-52d7-99cd-8a33b19652c7' completed in 0.005s", "module": "db_utils", "function": "execute_query_with_timeout", "line": 68}
{"timestamp": "2025-06-11T14:25:17.160650", "level": "DEBUG", "logger": "backend.services.db_utils", "message": "Auth query 'auth_lookup_e4ae2d8f-b32f-52d7-99cd-8a33b19652c7' successful, result format preserved", "module": "db_utils", "function": "execute_query_with_timeout", "line": 72}
{"timestamp": "2025-06-11T14:25:17.164135", "level": "DEBUG", "logger": "backend.services.auth", "message": "Authentication attempt with token prefix: eyJhbGciOi...", "module": "auth", "function": "get_current_user", "line": 727}
{"timestamp": "2025-06-11T14:25:17.164250", "level": "DEBUG", "logger": "backend.core.security.jwt_keys", "message": "Token contains key ID: acda791c-cc77-4888-8c9b-83dd6e9d53cc", "module": "jwt_keys", "function": "decode_token", "line": 188}
{"timestamp": "2025-06-11T14:25:17.164290", "level": "DEBUG", "logger": "backend.core.security.jwt_keys", "message": "Using key ID acda791c-cc77-4888-8c9b-83dd6e9d53cc for verification", "module": "jwt_keys", "function": "decode_token", "line": 195}
{"timestamp": "2025-06-11T14:25:17.164418", "level": "DEBUG", "logger": "backend.services.auth", "message": "JWT decoded successfully with key manager", "module": "auth", "function": "authenticate_token", "line": 683}
{"timestamp": "2025-06-11T14:25:17.165659", "level": "DEBUG", "logger": "backend.services.db_utils", "message": "Query 'auth_lookup_e4ae2d8f-b32f-52d7-99cd-8a33b19652c7' completed in 0.001s", "module": "db_utils", "function": "execute_query_with_timeout", "line": 68}
{"timestamp": "2025-06-11T14:25:17.165705", "level": "DEBUG", "logger": "backend.services.db_utils", "message": "Auth query 'auth_lookup_e4ae2d8f-b32f-52d7-99cd-8a33b19652c7' successful, result format preserved", "module": "db_utils", "function": "execute_query_with_timeout", "line": 72}
{"timestamp": "2025-06-11T14:25:17.165756", "level": "DEBUG", "logger": "backend.services.auth", "message": "Successfully authenticated user: <EMAIL>", "module": "auth", "function": "get_current_user", "line": 732}
{"timestamp": "2025-06-11T14:25:17.165791", "level": "DEBUG", "logger": "backend.services.auth", "message": "Successfully authenticated user: <EMAIL>", "module": "auth", "function": "get_current_user", "line": 738}
{"timestamp": "2025-06-11T14:25:17.167646", "level": "DEBUG", "logger": "backend.routes.qr_preview", "message": "Received QR preview SVG request: {\"qr_type\": \"scooter_unlock\", \"data\": {\"scooter_id\": \"SCOOT123\", \"unlock_code\": \"UNLOCK42\", \"title\": \"Scooter_unlock QR Code\", \"description\": \"Scan to view scooter_unlock content\"}, \"designOptions\": {\"size\": 256, \"margin\": 10, \"pattern\": \"square\", \"error_correction\": \"M\", \"svg_render_dpi\": 300, \"svg_optimize\": true}, \"dimensions\": 300}", "module": "qr_preview", "function": "preview_qr_svg", "line": 242}
{"timestamp": "2025-06-11T14:25:17.167743", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Formatting content for QR type: qrtype.scooter_unlock, content keys: ['scooter_id', 'unlock_code', 'title', 'description']", "module": "qr_preview", "function": "_format_qr_data", "line": 599}
{"timestamp": "2025-06-11T14:25:17.173823", "level": "ERROR", "logger": "backend.services.qr_preview", "message": "Error formatting QR data for type qrtype.scooter_unlock: TRANSLATION_SERVICE", "module": "qr_preview", "function": "_format_qr_data", "line": 1639, "exception": "Traceback (most recent call last):\n  File \"/Users/<USER>/aihub/qrvibe/backend/services/qr_preview.py\", line 1602, in _format_qr_data\n    elif qr_type_str == QRType.TRANSLATION_SERVICE.lower():\n                        ^^^^^^^^^^^^^^^^^^^^^^^^^^\n  File \"/opt/homebrew/Cellar/python@3.11/3.11.11/Frameworks/Python.framework/Versions/3.11/lib/python3.11/enum.py\", line 786, in __getattr__\n    raise AttributeError(name) from None\nAttributeError: TRANSLATION_SERVICE"}
{"timestamp": "2025-06-11T14:25:17.224925", "level": "DEBUG", "logger": "backend.services.auth", "message": "Authentication attempt with token prefix: eyJhbGciOi...", "module": "auth", "function": "get_current_user", "line": 727}
{"timestamp": "2025-06-11T14:25:17.225057", "level": "DEBUG", "logger": "backend.core.security.jwt_keys", "message": "Token contains key ID: acda791c-cc77-4888-8c9b-83dd6e9d53cc", "module": "jwt_keys", "function": "decode_token", "line": 188}
{"timestamp": "2025-06-11T14:25:17.225095", "level": "DEBUG", "logger": "backend.core.security.jwt_keys", "message": "Using key ID acda791c-cc77-4888-8c9b-83dd6e9d53cc for verification", "module": "jwt_keys", "function": "decode_token", "line": 195}
{"timestamp": "2025-06-11T14:25:17.225229", "level": "DEBUG", "logger": "backend.services.auth", "message": "JWT decoded successfully with key manager", "module": "auth", "function": "authenticate_token", "line": 683}
{"timestamp": "2025-06-11T14:25:17.226892", "level": "DEBUG", "logger": "backend.main", "message": "[Auth] Enforced Bearer token authentication for request", "module": "main", "function": "bearer_token_auth_middleware", "line": 274}
{"timestamp": "2025-06-11T14:25:17.229629", "level": "DEBUG", "logger": "backend.services.db_utils", "message": "Query 'auth_lookup_e4ae2d8f-b32f-52d7-99cd-8a33b19652c7' completed in 0.004s", "module": "db_utils", "function": "execute_query_with_timeout", "line": 68}
{"timestamp": "2025-06-11T14:25:17.229678", "level": "DEBUG", "logger": "backend.services.db_utils", "message": "Auth query 'auth_lookup_e4ae2d8f-b32f-52d7-99cd-8a33b19652c7' successful, result format preserved", "module": "db_utils", "function": "execute_query_with_timeout", "line": 72}
{"timestamp": "2025-06-11T14:25:17.229739", "level": "DEBUG", "logger": "backend.services.auth", "message": "Successfully authenticated user: <EMAIL>", "module": "auth", "function": "get_current_user", "line": 732}
{"timestamp": "2025-06-11T14:25:17.229773", "level": "DEBUG", "logger": "backend.services.auth", "message": "Successfully authenticated user: <EMAIL>", "module": "auth", "function": "get_current_user", "line": 738}
{"timestamp": "2025-06-11T14:25:17.231740", "level": "DEBUG", "logger": "backend.routes.qr_preview", "message": "Received QR preview SVG request: {\"qr_type\": \"scooter_unlock\", \"data\": {\"scooter_id\": \"SCOOT123\", \"unlock_code\": \"UNLOCK42\", \"title\": \"Scooter_unlock QR Code\", \"description\": \"Scan to view scooter_unlock content\"}, \"designOptions\": {\"size\": 256, \"margin\": 10, \"pattern\": \"classic\", \"marker_border_style\": \"square\", \"marker_border_color\": \"#000000\", \"marker_center_style\": \"circle\", \"marker_center_color\": \"#000000\", \"logo_size\": 48, \"logo_position\": \"center\", \"logo_opacity\": 1, \"frame_style\": \"classic\", \"frame_color\": \"#000000\", \"f", "module": "qr_preview", "function": "preview_qr_svg", "line": 242}
{"timestamp": "2025-06-11T14:25:17.231831", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Formatting content for QR type: qrtype.scooter_unlock, content keys: ['scooter_id', 'unlock_code', 'title', 'description']", "module": "qr_preview", "function": "_format_qr_data", "line": 599}
{"timestamp": "2025-06-11T14:25:17.237889", "level": "ERROR", "logger": "backend.services.qr_preview", "message": "Error formatting QR data for type qrtype.scooter_unlock: TRANSLATION_SERVICE", "module": "qr_preview", "function": "_format_qr_data", "line": 1639, "exception": "Traceback (most recent call last):\n  File \"/Users/<USER>/aihub/qrvibe/backend/services/qr_preview.py\", line 1602, in _format_qr_data\n    elif qr_type_str == QRType.TRANSLATION_SERVICE.lower():\n                        ^^^^^^^^^^^^^^^^^^^^^^^^^^\n  File \"/opt/homebrew/Cellar/python@3.11/3.11.11/Frameworks/Python.framework/Versions/3.11/lib/python3.11/enum.py\", line 786, in __getattr__\n    raise AttributeError(name) from None\nAttributeError: TRANSLATION_SERVICE"}
{"timestamp": "2025-06-11T14:25:17.289850", "level": "DEBUG", "logger": "backend.main", "message": "[Auth] Enforced Bearer token authentication for request", "module": "main", "function": "bearer_token_auth_middleware", "line": 274}
{"timestamp": "2025-06-11T14:25:17.779683", "level": "DEBUG", "logger": "backend.core.security.jwt_keys", "message": "Token contains key ID: acda791c-cc77-4888-8c9b-83dd6e9d53cc", "module": "jwt_keys", "function": "decode_token", "line": 188}
{"timestamp": "2025-06-11T14:25:17.779750", "level": "DEBUG", "logger": "backend.core.security.jwt_keys", "message": "Using key ID acda791c-cc77-4888-8c9b-83dd6e9d53cc for verification", "module": "jwt_keys", "function": "decode_token", "line": 195}
{"timestamp": "2025-06-11T14:25:17.779888", "level": "DEBUG", "logger": "backend.services.auth", "message": "JWT decoded successfully with key manager", "module": "auth", "function": "authenticate_token", "line": 683}
{"timestamp": "2025-06-11T14:25:17.780403", "level": "DEBUG", "logger": "backend.core.security.jwt_keys", "message": "Token contains key ID: acda791c-cc77-4888-8c9b-83dd6e9d53cc", "module": "jwt_keys", "function": "decode_token", "line": 188}
{"timestamp": "2025-06-11T14:25:17.780456", "level": "DEBUG", "logger": "backend.core.security.jwt_keys", "message": "Using key ID acda791c-cc77-4888-8c9b-83dd6e9d53cc for verification", "module": "jwt_keys", "function": "decode_token", "line": 195}
{"timestamp": "2025-06-11T14:25:17.780567", "level": "DEBUG", "logger": "backend.services.auth", "message": "JWT decoded successfully with key manager", "module": "auth", "function": "authenticate_token", "line": 683}
{"timestamp": "2025-06-11T14:25:17.781799", "level": "DEBUG", "logger": "backend.services.db_utils", "message": "Query 'auth_lookup_e4ae2d8f-b32f-52d7-99cd-8a33b19652c7' completed in 0.002s", "module": "db_utils", "function": "execute_query_with_timeout", "line": 68}
{"timestamp": "2025-06-11T14:25:17.781856", "level": "DEBUG", "logger": "backend.services.db_utils", "message": "Auth query 'auth_lookup_e4ae2d8f-b32f-52d7-99cd-8a33b19652c7' successful, result format preserved", "module": "db_utils", "function": "execute_query_with_timeout", "line": 72}
{"timestamp": "2025-06-11T14:25:17.782071", "level": "DEBUG", "logger": "backend.services.db_utils", "message": "Query 'auth_lookup_e4ae2d8f-b32f-52d7-99cd-8a33b19652c7' completed in 0.001s", "module": "db_utils", "function": "execute_query_with_timeout", "line": 68}
{"timestamp": "2025-06-11T14:25:17.782111", "level": "DEBUG", "logger": "backend.services.db_utils", "message": "Auth query 'auth_lookup_e4ae2d8f-b32f-52d7-99cd-8a33b19652c7' successful, result format preserved", "module": "db_utils", "function": "execute_query_with_timeout", "line": 72}
{"timestamp": "2025-06-11T14:25:17.787016", "level": "DEBUG", "logger": "backend.services.auth", "message": "Authentication attempt with token prefix: eyJhbGciOi...", "module": "auth", "function": "get_current_user", "line": 727}
{"timestamp": "2025-06-11T14:25:17.787132", "level": "DEBUG", "logger": "backend.core.security.jwt_keys", "message": "Token contains key ID: acda791c-cc77-4888-8c9b-83dd6e9d53cc", "module": "jwt_keys", "function": "decode_token", "line": 188}
{"timestamp": "2025-06-11T14:25:17.787176", "level": "DEBUG", "logger": "backend.core.security.jwt_keys", "message": "Using key ID acda791c-cc77-4888-8c9b-83dd6e9d53cc for verification", "module": "jwt_keys", "function": "decode_token", "line": 195}
{"timestamp": "2025-06-11T14:25:17.787297", "level": "DEBUG", "logger": "backend.services.auth", "message": "JWT decoded successfully with key manager", "module": "auth", "function": "authenticate_token", "line": 683}
{"timestamp": "2025-06-11T14:25:17.787909", "level": "DEBUG", "logger": "backend.services.auth", "message": "Authentication attempt with token prefix: eyJhbGciOi...", "module": "auth", "function": "get_current_user", "line": 727}
{"timestamp": "2025-06-11T14:25:17.787984", "level": "DEBUG", "logger": "backend.core.security.jwt_keys", "message": "Token contains key ID: acda791c-cc77-4888-8c9b-83dd6e9d53cc", "module": "jwt_keys", "function": "decode_token", "line": 188}
{"timestamp": "2025-06-11T14:25:17.788025", "level": "DEBUG", "logger": "backend.core.security.jwt_keys", "message": "Using key ID acda791c-cc77-4888-8c9b-83dd6e9d53cc for verification", "module": "jwt_keys", "function": "decode_token", "line": 195}
{"timestamp": "2025-06-11T14:25:17.788117", "level": "DEBUG", "logger": "backend.services.auth", "message": "JWT decoded successfully with key manager", "module": "auth", "function": "authenticate_token", "line": 683}
{"timestamp": "2025-06-11T14:25:17.788962", "level": "DEBUG", "logger": "backend.services.db_utils", "message": "Query 'auth_lookup_e4ae2d8f-b32f-52d7-99cd-8a33b19652c7' completed in 0.002s", "module": "db_utils", "function": "execute_query_with_timeout", "line": 68}
{"timestamp": "2025-06-11T14:25:17.789006", "level": "DEBUG", "logger": "backend.services.db_utils", "message": "Auth query 'auth_lookup_e4ae2d8f-b32f-52d7-99cd-8a33b19652c7' successful, result format preserved", "module": "db_utils", "function": "execute_query_with_timeout", "line": 72}
{"timestamp": "2025-06-11T14:25:17.789055", "level": "DEBUG", "logger": "backend.services.auth", "message": "Successfully authenticated user: <EMAIL>", "module": "auth", "function": "get_current_user", "line": 732}
{"timestamp": "2025-06-11T14:25:17.789095", "level": "DEBUG", "logger": "backend.services.auth", "message": "Successfully authenticated user: <EMAIL>", "module": "auth", "function": "get_current_user", "line": 738}
{"timestamp": "2025-06-11T14:25:17.789359", "level": "DEBUG", "logger": "backend.services.db_utils", "message": "Query 'auth_lookup_e4ae2d8f-b32f-52d7-99cd-8a33b19652c7' completed in 0.001s", "module": "db_utils", "function": "execute_query_with_timeout", "line": 68}
{"timestamp": "2025-06-11T14:25:17.789397", "level": "DEBUG", "logger": "backend.services.db_utils", "message": "Auth query 'auth_lookup_e4ae2d8f-b32f-52d7-99cd-8a33b19652c7' successful, result format preserved", "module": "db_utils", "function": "execute_query_with_timeout", "line": 72}
{"timestamp": "2025-06-11T14:25:17.789445", "level": "DEBUG", "logger": "backend.services.auth", "message": "Successfully authenticated user: <EMAIL>", "module": "auth", "function": "get_current_user", "line": 732}
{"timestamp": "2025-06-11T14:25:17.789480", "level": "DEBUG", "logger": "backend.services.auth", "message": "Successfully authenticated user: <EMAIL>", "module": "auth", "function": "get_current_user", "line": 738}
{"timestamp": "2025-06-11T14:25:17.790600", "level": "DEBUG", "logger": "backend.routes.qr_preview", "message": "Received QR preview SVG request: {\"qr_type\": \"scooter_unlock\", \"data\": {\"scooter_id\": \"SCOOT123\", \"unlock_code\": \"UNLOCK42\", \"title\": \"Scooter_unlock QR Code\", \"description\": \"Scan to view scooter_unlock content\"}, \"designOptions\": {\"size\": 256, \"margin\": 10, \"pattern\": \"square\", \"error_correction\": \"M\", \"svg_render_dpi\": 300, \"svg_optimize\": true}, \"dimensions\": 300}", "module": "qr_preview", "function": "preview_qr_svg", "line": 242}
{"timestamp": "2025-06-11T14:25:17.790673", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Formatting content for QR type: qrtype.scooter_unlock, content keys: ['scooter_id', 'unlock_code', 'title', 'description']", "module": "qr_preview", "function": "_format_qr_data", "line": 599}
{"timestamp": "2025-06-11T14:25:17.796752", "level": "ERROR", "logger": "backend.services.qr_preview", "message": "Error formatting QR data for type qrtype.scooter_unlock: TRANSLATION_SERVICE", "module": "qr_preview", "function": "_format_qr_data", "line": 1639, "exception": "Traceback (most recent call last):\n  File \"/Users/<USER>/aihub/qrvibe/backend/services/qr_preview.py\", line 1602, in _format_qr_data\n    elif qr_type_str == QRType.TRANSLATION_SERVICE.lower():\n                        ^^^^^^^^^^^^^^^^^^^^^^^^^^\n  File \"/opt/homebrew/Cellar/python@3.11/3.11.11/Frameworks/Python.framework/Versions/3.11/lib/python3.11/enum.py\", line 786, in __getattr__\n    raise AttributeError(name) from None\nAttributeError: TRANSLATION_SERVICE"}
{"timestamp": "2025-06-11T14:25:17.848031", "level": "DEBUG", "logger": "backend.routes.qr_preview", "message": "Received QR preview SVG request: {\"qr_type\": \"scooter_unlock\", \"data\": {\"scooter_id\": \"SCOOT123\", \"unlock_code\": \"UNLOCK42\", \"title\": \"Scooter_unlock QR Code\", \"description\": \"Scan to view scooter_unlock content\"}, \"designOptions\": {\"size\": 256, \"margin\": 10, \"pattern\": \"classic\", \"marker_border_style\": \"square\", \"marker_border_color\": \"#000000\", \"marker_center_style\": \"circle\", \"marker_center_color\": \"#000000\", \"logo_size\": 48, \"logo_position\": \"center\", \"logo_opacity\": 1, \"frame_style\": \"classic\", \"frame_color\": \"#000000\", \"f", "module": "qr_preview", "function": "preview_qr_svg", "line": 242}
{"timestamp": "2025-06-11T14:25:17.848125", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Formatting content for QR type: qrtype.scooter_unlock, content keys: ['scooter_id', 'unlock_code', 'title', 'description']", "module": "qr_preview", "function": "_format_qr_data", "line": 599}
{"timestamp": "2025-06-11T14:25:17.854265", "level": "ERROR", "logger": "backend.services.qr_preview", "message": "Error formatting QR data for type qrtype.scooter_unlock: TRANSLATION_SERVICE", "module": "qr_preview", "function": "_format_qr_data", "line": 1639, "exception": "Traceback (most recent call last):\n  File \"/Users/<USER>/aihub/qrvibe/backend/services/qr_preview.py\", line 1602, in _format_qr_data\n    elif qr_type_str == QRType.TRANSLATION_SERVICE.lower():\n                        ^^^^^^^^^^^^^^^^^^^^^^^^^^\n  File \"/opt/homebrew/Cellar/python@3.11/3.11.11/Frameworks/Python.framework/Versions/3.11/lib/python3.11/enum.py\", line 786, in __getattr__\n    raise AttributeError(name) from None\nAttributeError: TRANSLATION_SERVICE"}
{"timestamp": "2025-06-11T14:25:17.905299", "level": "DEBUG", "logger": "backend.main", "message": "[Auth] Enforced Bearer token authentication for request", "module": "main", "function": "bearer_token_auth_middleware", "line": 274}
{"timestamp": "2025-06-11T14:25:17.906238", "level": "DEBUG", "logger": "backend.main", "message": "[Auth] Enforced Bearer token authentication for request", "module": "main", "function": "bearer_token_auth_middleware", "line": 274}
{"timestamp": "2025-06-11T14:25:28.054858", "level": "DEBUG", "logger": "backend.main", "message": "[Auth] Enforced Bearer token authentication for request", "module": "main", "function": "bearer_token_auth_middleware", "line": 274}
{"timestamp": "2025-06-11T14:25:28.062738", "level": "DEBUG", "logger": "backend.main", "message": "[Auth] Enforced Bearer token authentication for request", "module": "main", "function": "bearer_token_auth_middleware", "line": 274}
{"timestamp": "2025-06-11T14:25:58.246609", "level": "DEBUG", "logger": "backend.main", "message": "[Auth] Enforced Bearer token authentication for request", "module": "main", "function": "bearer_token_auth_middleware", "line": 274}
{"timestamp": "2025-06-11T14:26:28.054176", "level": "DEBUG", "logger": "backend.main", "message": "[Auth] Enforced Bearer token authentication for request", "module": "main", "function": "bearer_token_auth_middleware", "line": 274}
{"timestamp": "2025-06-11T14:26:28.195758", "level": "DEBUG", "logger": "backend.main", "message": "[Auth] Enforced Bearer token authentication for request", "module": "main", "function": "bearer_token_auth_middleware", "line": 274}
{"timestamp": "2025-06-11T14:26:58.297470", "level": "DEBUG", "logger": "backend.main", "message": "[Auth] Enforced Bearer token authentication for request", "module": "main", "function": "bearer_token_auth_middleware", "line": 274}
{"timestamp": "2025-06-11T14:27:28.137035", "level": "DEBUG", "logger": "backend.main", "message": "[Auth] Enforced Bearer token authentication for request", "module": "main", "function": "bearer_token_auth_middleware", "line": 274}
{"timestamp": "2025-06-11T14:27:28.205593", "level": "DEBUG", "logger": "backend.main", "message": "[Auth] Enforced Bearer token authentication for request", "module": "main", "function": "bearer_token_auth_middleware", "line": 274}
{"timestamp": "2025-06-11T14:27:58.258594", "level": "DEBUG", "logger": "backend.main", "message": "[Auth] Enforced Bearer token authentication for request", "module": "main", "function": "bearer_token_auth_middleware", "line": 274}
{"timestamp": "2025-06-11T14:28:07.682365", "level": "INFO", "logger": "backend.services.qr_preview_cache", "message": "QR preview cache cleanup task stopped", "module": "qr_preview_cache", "function": "stop", "line": 257}
{"timestamp": "2025-06-11T14:28:07.682587", "level": "INFO", "logger": "backend.main", "message": "QR preview cache stopped", "module": "main", "function": "shutdown_qr_preview_cache", "line": 352}
{"timestamp": "2025-06-11T14:28:12.816452", "level": "INFO", "logger": "backend.main", "message": "=== QRVibe API Startup: Logging to file is enabled ===", "module": "main", "function": "<module>", "line": 37}
{"timestamp": "2025-06-11T14:28:12.816548", "level": "INFO", "logger": "backend.main", "message": "Applying critical patches before initialization", "module": "main", "function": "<module>", "line": 43}
{"timestamp": "2025-06-11T14:28:12.816873", "level": "INFO", "logger": "backend.patches", "message": "Applying patches (none required)", "module": "__init__", "function": "apply_patches", "line": 11}
{"timestamp": "2025-06-11T14:28:12.817443", "level": "INFO", "logger": "backend.core.middleware.cors", "message": "Setting up CORS middleware with origins: ['https://127.0.0.1:3000', 'http://127.0.0.1:3000', 'http://localhost:3000', 'https://localhost:3000']", "module": "cors", "function": "setup_cors", "line": 86}
{"timestamp": "2025-06-11T14:28:12.817531", "level": "INFO", "logger": "backend.core.middleware.cors", "message": "CORS middleware configured - preflight requests should be handled automatically", "module": "cors", "function": "setup_cors", "line": 98}
{"timestamp": "2025-06-11T14:28:12.819316", "level": "INFO", "logger": "backend.middleware.security_middleware", "message": "Added Enhanced Security Middleware with tiered rate limiting", "module": "security_middleware", "function": "add_security_middleware", "line": 176}
{"timestamp": "2025-06-11T14:28:12.819456", "level": "INFO", "logger": "backend.main", "message": "Mounting storage directory: /Users/<USER>/aihub/qrvibe/storage", "module": "main", "function": "<module>", "line": 107}
{"timestamp": "2025-06-11T14:28:12.820414", "level": "INFO", "logger": "backend.main", "message": "Storage directory absolute path: /Users/<USER>/aihub/qrvibe/storage", "module": "main", "function": "<module>", "line": 119}
{"timestamp": "2025-06-11T14:28:13.400521", "level": "INFO", "logger": "root", "message": "Storage initialized: environment=test, storage_dir=/Users/<USER>/aihub/qrvibe/storage, s3_enabled=True", "module": "storage", "function": "__init__", "line": 41}
{"timestamp": "2025-06-11T14:28:13.403824", "level": "INFO", "logger": "root", "message": "S3 storage enabled with bucket: qrverse and CDN: https://dev-cdn.qrvibe.com", "module": "storage", "function": "__init__", "line": 59}
{"timestamp": "2025-06-11T14:28:13.464815", "level": "INFO", "logger": "root", "message": "Storage initialized: environment=test, storage_dir=/Users/<USER>/aihub/qrvibe/storage, s3_enabled=True", "module": "storage", "function": "__init__", "line": 41}
{"timestamp": "2025-06-11T14:28:13.468781", "level": "INFO", "logger": "root", "message": "S3 storage enabled with bucket: qrverse and CDN: https://dev-cdn.qrvibe.com", "module": "storage", "function": "__init__", "line": 59}
{"timestamp": "2025-06-11T14:28:13.664926", "level": "INFO", "logger": "backend.middleware.security_middleware", "message": "Enhanced security middleware initialized with tiered rate limiting", "module": "security_middleware", "function": "__init__", "line": 87}
{"timestamp": "2025-06-11T14:28:13.665049", "level": "INFO", "logger": "backend.core.middleware.csrf", "message": "CSRF Protection Middleware initialized with exclude paths: ['/auth/jwt/login', '/auth/logout', '/auth/register', '/auth/2fa', '/auth/me', '/auth/verify-email', '/auth/reset-password', '/admin/login', '/admin/logout']", "module": "csrf", "function": "__init__", "line": 44}
{"timestamp": "2025-06-11T14:28:13.671780", "level": "INFO", "logger": "backend.services.token_blacklist", "message": "Token blacklist service initialized with Redis", "module": "token_blacklist", "function": "initialize", "line": 59}
{"timestamp": "2025-06-11T14:28:13.671873", "level": "INFO", "logger": "backend.main", "message": "Token blacklist service initialized", "module": "main", "function": "initialize_token_blacklist", "line": 98}
{"timestamp": "2025-06-11T14:28:13.674983", "level": "INFO", "logger": "backend.services.qr_preview_cache", "message": "QR preview cache initialized at /Users/<USER>/aihub/qrvibe/storage/cache/qr_preview_cache.db", "module": "qr_preview_cache", "function": "_init_db", "line": 56}
{"timestamp": "2025-06-11T14:28:13.675072", "level": "INFO", "logger": "backend.services.qr_preview_cache", "message": "Started QR preview cache cleanup task", "module": "qr_preview_cache", "function": "start_cleanup_task", "line": 62}
{"timestamp": "2025-06-11T14:28:13.675156", "level": "INFO", "logger": "backend.main", "message": "QR preview cache initialized and cleanup task started", "module": "main", "function": "init_qr_preview_cache", "line": 345}
{"timestamp": "2025-06-11T14:28:28.064973", "level": "DEBUG", "logger": "backend.main", "message": "[Auth] Enforced Bearer token authentication for request", "module": "main", "function": "bearer_token_auth_middleware", "line": 274}
{"timestamp": "2025-06-11T14:28:28.215350", "level": "DEBUG", "logger": "backend.main", "message": "[Auth] Enforced Bearer token authentication for request", "module": "main", "function": "bearer_token_auth_middleware", "line": 274}
{"timestamp": "2025-06-11T14:28:57.064134", "level": "INFO", "logger": "backend.services.qr_preview_cache", "message": "QR preview cache cleanup task stopped", "module": "qr_preview_cache", "function": "stop", "line": 257}
{"timestamp": "2025-06-11T14:28:57.064307", "level": "INFO", "logger": "backend.main", "message": "QR preview cache stopped", "module": "main", "function": "shutdown_qr_preview_cache", "line": 352}
