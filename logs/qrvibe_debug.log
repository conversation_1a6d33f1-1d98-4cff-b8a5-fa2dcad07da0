{"timestamp": "2025-06-12T17:33:49.032937", "level": "DEBUG", "logger": "backend.core.security.jwt_keys", "message": "Token contains key ID: 0bf271b1-2fbf-4d96-987f-e0b06b2fd890", "module": "jwt_keys", "function": "decode_token", "line": 188}
{"timestamp": "2025-06-12T17:33:49.033191", "level": "DEBUG", "logger": "backend.core.security.jwt_keys", "message": "Using key ID 0bf271b1-2fbf-4d96-987f-e0b06b2fd890 for verification", "module": "jwt_keys", "function": "decode_token", "line": 195}
{"timestamp": "2025-06-12T17:33:49.033846", "level": "DEBUG", "logger": "backend.services.auth", "message": "JWT decoded successfully with key manager", "module": "auth", "function": "authenticate_token", "line": 683}
{"timestamp": "2025-06-12T17:33:49.042473", "level": "DEBUG", "logger": "backend.services.db_utils", "message": "Query 'auth_lookup_e4ae2d8f-b32f-52d7-99cd-8a33b19652c7' completed in 0.006s", "module": "db_utils", "function": "execute_query_with_timeout", "line": 68}
{"timestamp": "2025-06-12T17:33:49.042532", "level": "DEBUG", "logger": "backend.services.db_utils", "message": "Auth query 'auth_lookup_e4ae2d8f-b32f-52d7-99cd-8a33b19652c7' successful, result format preserved", "module": "db_utils", "function": "execute_query_with_timeout", "line": 72}
{"timestamp": "2025-06-12T17:33:49.052353", "level": "DEBUG", "logger": "backend.services.auth", "message": "Authentication attempt with token prefix: eyJhbGciOi...", "module": "auth", "function": "get_current_user", "line": 727}
{"timestamp": "2025-06-12T17:33:49.052474", "level": "DEBUG", "logger": "backend.core.security.jwt_keys", "message": "Token contains key ID: 0bf271b1-2fbf-4d96-987f-e0b06b2fd890", "module": "jwt_keys", "function": "decode_token", "line": 188}
{"timestamp": "2025-06-12T17:33:49.052516", "level": "DEBUG", "logger": "backend.core.security.jwt_keys", "message": "Using key ID 0bf271b1-2fbf-4d96-987f-e0b06b2fd890 for verification", "module": "jwt_keys", "function": "decode_token", "line": 195}
{"timestamp": "2025-06-12T17:33:49.052641", "level": "DEBUG", "logger": "backend.services.auth", "message": "JWT decoded successfully with key manager", "module": "auth", "function": "authenticate_token", "line": 683}
{"timestamp": "2025-06-12T17:33:49.054785", "level": "DEBUG", "logger": "backend.services.db_utils", "message": "Query 'auth_lookup_e4ae2d8f-b32f-52d7-99cd-8a33b19652c7' completed in 0.002s", "module": "db_utils", "function": "execute_query_with_timeout", "line": 68}
{"timestamp": "2025-06-12T17:33:49.054831", "level": "DEBUG", "logger": "backend.services.db_utils", "message": "Auth query 'auth_lookup_e4ae2d8f-b32f-52d7-99cd-8a33b19652c7' successful, result format preserved", "module": "db_utils", "function": "execute_query_with_timeout", "line": 72}
{"timestamp": "2025-06-12T17:33:49.054897", "level": "DEBUG", "logger": "backend.services.auth", "message": "Successfully authenticated user: <EMAIL>", "module": "auth", "function": "get_current_user", "line": 732}
{"timestamp": "2025-06-12T17:33:49.054942", "level": "DEBUG", "logger": "backend.services.auth", "message": "Successfully authenticated user: <EMAIL>", "module": "auth", "function": "get_current_user", "line": 738}
{"timestamp": "2025-06-12T17:33:49.057174", "level": "DEBUG", "logger": "backend.routes.qr_preview", "message": "Received QR preview SVG request: {\"qr_type\": \"scooter_unlock\", \"data\": {\"scooter_id\": \"SCOOT123\", \"unlock_code\": \"UNLOCK42\", \"title\": \"Scooter_unlock QR Code\", \"description\": \"Scan to view scooter_unlock content\"}, \"designOptions\": {\"size\": 256, \"margin\": 10, \"pattern\": \"square\", \"marker_border_style\": \"square\", \"marker_center_style\": \"square\", \"logo_size\": 0.3, \"logo_position\": \"center\", \"logo_opacity\": 1, \"logo_remove_background\": false, \"frame_style\": \"none\", \"frame_font\": \"SF Pro\", \"frame_font_size\": 16, \"foreground_color\":", "module": "qr_preview", "function": "preview_qr_svg", "line": 242}
{"timestamp": "2025-06-12T17:33:49.057409", "level": "DEBUG", "logger": "backend.routes.qr_preview", "message": "Form data keys: ['request']", "module": "qr_preview", "function": "preview_qr_svg", "line": 297}
{"timestamp": "2025-06-12T17:33:49.057496", "level": "DEBUG", "logger": "backend.routes.qr_preview", "message": "No logo key found in form data", "module": "qr_preview", "function": "preview_qr_svg", "line": 309}
{"timestamp": "2025-06-12T17:33:49.058387", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Formatting content for QR type: qrtype.scooter_unlock, content keys: ['scooter_id', 'unlock_code', 'title', 'description']", "module": "qr_preview", "function": "_format_qr_data", "line": 876}
{"timestamp": "2025-06-12T17:33:49.075526", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Logo remove background setting: False, has logo: False", "module": "qr_preview", "function": "generate_preview_svg_modular", "line": 727}
{"timestamp": "2025-06-12T17:33:49.089807", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Drawing 3 finder patterns at positions: [(0, 0), (42, 0), (0, 42)]", "module": "qr_preview", "function": "generate_preview_svg_modular", "line": 775}
{"timestamp": "2025-06-12T17:33:49.089872", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "QR size: 49, border_style: square, center_style: square", "module": "qr_preview", "function": "generate_preview_svg_modular", "line": 776}
{"timestamp": "2025-06-12T17:33:49.089913", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Drawing finder pattern 1/3 at position (0, 0)", "module": "qr_preview", "function": "generate_preview_svg_modular", "line": 780}
{"timestamp": "2025-06-12T17:33:49.089982", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "_draw_finder_marker called: mx=0, my=0, module_size=5.918367346938775, border_style=square, center_style=square, border_color=#000000, center_color=#000000, offset=(20, 20)", "module": "qr_preview", "function": "_draw_finder_marker", "line": 97}
{"timestamp": "2025-06-12T17:33:49.090051", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Creating finder pattern group with id='marker-0-0'", "module": "qr_preview", "function": "_draw_finder_marker", "line": 110}
{"timestamp": "2025-06-12T17:33:49.090091", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Drawing square border:", "module": "qr_preview", "function": "_draw_finder_marker", "line": 160}
{"timestamp": "2025-06-12T17:33:49.090154", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "  Outer: insert=(20.0, 20.0), size=(41.42857142857143, 41.42857142857143)", "module": "qr_preview", "function": "_draw_finder_marker", "line": 161}
{"timestamp": "2025-06-12T17:33:49.090219", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "  Inner: insert=(27.10204081632653, 27.10204081632653), size=(27.224489795918366, 27.224489795918366)", "module": "qr_preview", "function": "_draw_finder_marker", "line": 162}
{"timestamp": "2025-06-12T17:33:49.090332", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Drawing marker center: style=square, color=#000000, pos=(34.79591836734694, 34.79591836734694), size=11.83673469387755", "module": "qr_preview", "function": "_draw_marker_center", "line": 179}
{"timestamp": "2025-06-12T17:33:49.090411", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Successfully added finder pattern group to SVG", "module": "qr_preview", "function": "_draw_finder_marker", "line": 171}
{"timestamp": "2025-06-12T17:33:49.090457", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Successfully drew finder pattern 1/3", "module": "qr_preview", "function": "generate_preview_svg_modular", "line": 782}
{"timestamp": "2025-06-12T17:33:49.090504", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Drawing finder pattern 2/3 at position (42, 0)", "module": "qr_preview", "function": "generate_preview_svg_modular", "line": 780}
{"timestamp": "2025-06-12T17:33:49.090556", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "_draw_finder_marker called: mx=42, my=0, module_size=5.918367346938775, border_style=square, center_style=square, border_color=#000000, center_color=#000000, offset=(20, 20)", "module": "qr_preview", "function": "_draw_finder_marker", "line": 97}
{"timestamp": "2025-06-12T17:33:49.090646", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Creating finder pattern group with id='marker-42-0'", "module": "qr_preview", "function": "_draw_finder_marker", "line": 110}
{"timestamp": "2025-06-12T17:33:49.090683", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Drawing square border:", "module": "qr_preview", "function": "_draw_finder_marker", "line": 160}
{"timestamp": "2025-06-12T17:33:49.090725", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "  Outer: insert=(268.57142857142856, 20.0), size=(41.42857142857143, 41.42857142857143)", "module": "qr_preview", "function": "_draw_finder_marker", "line": 161}
{"timestamp": "2025-06-12T17:33:49.090769", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "  Inner: insert=(275.67346938775506, 27.10204081632653), size=(27.224489795918366, 27.224489795918366)", "module": "qr_preview", "function": "_draw_finder_marker", "line": 162}
{"timestamp": "2025-06-12T17:33:49.090842", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Drawing marker center: style=square, color=#000000, pos=(283.3673469387755, 34.79591836734694), size=11.83673469387755", "module": "qr_preview", "function": "_draw_marker_center", "line": 179}
{"timestamp": "2025-06-12T17:33:49.090900", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Successfully added finder pattern group to SVG", "module": "qr_preview", "function": "_draw_finder_marker", "line": 171}
{"timestamp": "2025-06-12T17:33:49.090939", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Successfully drew finder pattern 2/3", "module": "qr_preview", "function": "generate_preview_svg_modular", "line": 782}
{"timestamp": "2025-06-12T17:33:49.090979", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Drawing finder pattern 3/3 at position (0, 42)", "module": "qr_preview", "function": "generate_preview_svg_modular", "line": 780}
{"timestamp": "2025-06-12T17:33:49.091025", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "_draw_finder_marker called: mx=0, my=42, module_size=5.918367346938775, border_style=square, center_style=square, border_color=#000000, center_color=#000000, offset=(20, 20)", "module": "qr_preview", "function": "_draw_finder_marker", "line": 97}
{"timestamp": "2025-06-12T17:33:49.091073", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Creating finder pattern group with id='marker-0-42'", "module": "qr_preview", "function": "_draw_finder_marker", "line": 110}
{"timestamp": "2025-06-12T17:33:49.091110", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Drawing square border:", "module": "qr_preview", "function": "_draw_finder_marker", "line": 160}
{"timestamp": "2025-06-12T17:33:49.091152", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "  Outer: insert=(20.0, 268.57142857142856), size=(41.42857142857143, 41.42857142857143)", "module": "qr_preview", "function": "_draw_finder_marker", "line": 161}
{"timestamp": "2025-06-12T17:33:49.091191", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "  Inner: insert=(27.10204081632653, 275.67346938775506), size=(27.224489795918366, 27.224489795918366)", "module": "qr_preview", "function": "_draw_finder_marker", "line": 162}
{"timestamp": "2025-06-12T17:33:49.091260", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Drawing marker center: style=square, color=#000000, pos=(34.79591836734694, 283.3673469387755), size=11.83673469387755", "module": "qr_preview", "function": "_draw_marker_center", "line": 179}
{"timestamp": "2025-06-12T17:33:49.091313", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Successfully added finder pattern group to SVG", "module": "qr_preview", "function": "_draw_finder_marker", "line": 171}
{"timestamp": "2025-06-12T17:33:49.091368", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Successfully drew finder pattern 3/3", "module": "qr_preview", "function": "generate_preview_svg_modular", "line": 782}
{"timestamp": "2025-06-12T17:33:49.120060", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Generated SVG length: 176251", "module": "qr_preview", "function": "generate_preview_svg_modular", "line": 802}
{"timestamp": "2025-06-12T17:33:49.120200", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "SVG contains marker-0-0: True", "module": "qr_preview", "function": "generate_preview_svg_modular", "line": 803}
{"timestamp": "2025-06-12T17:33:49.120309", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "SVG contains marker-18-0: False", "module": "qr_preview", "function": "generate_preview_svg_modular", "line": 804}
{"timestamp": "2025-06-12T17:33:49.120418", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "SVG contains marker-0-18: False", "module": "qr_preview", "function": "generate_preview_svg_modular", "line": 805}
{"timestamp": "2025-06-12T17:33:49.120713", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "SVG marker counts - outer: 3, inner: 3, dot: 0", "module": "qr_preview", "function": "generate_preview_svg_modular", "line": 813}
{"timestamp": "2025-06-12T17:33:49.120750", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "SVG label counts - text: 0, background: 0", "module": "qr_preview", "function": "generate_preview_svg_modular", "line": 814}
{"timestamp": "2025-06-12T17:33:49.126633", "level": "DEBUG", "logger": "backend.main", "message": "[Auth] Enforced Bearer token authentication for request", "module": "main", "function": "bearer_token_auth_middleware", "line": 274}
{"timestamp": "2025-06-12T17:33:51.956791", "level": "DEBUG", "logger": "backend.core.security.jwt_keys", "message": "Token contains key ID: 0bf271b1-2fbf-4d96-987f-e0b06b2fd890", "module": "jwt_keys", "function": "decode_token", "line": 188}
{"timestamp": "2025-06-12T17:33:51.956862", "level": "DEBUG", "logger": "backend.core.security.jwt_keys", "message": "Using key ID 0bf271b1-2fbf-4d96-987f-e0b06b2fd890 for verification", "module": "jwt_keys", "function": "decode_token", "line": 195}
{"timestamp": "2025-06-12T17:33:51.957021", "level": "DEBUG", "logger": "backend.services.auth", "message": "JWT decoded successfully with key manager", "module": "auth", "function": "authenticate_token", "line": 683}
{"timestamp": "2025-06-12T17:33:51.958912", "level": "DEBUG", "logger": "backend.services.db_utils", "message": "Query 'auth_lookup_e4ae2d8f-b32f-52d7-99cd-8a33b19652c7' completed in 0.002s", "module": "db_utils", "function": "execute_query_with_timeout", "line": 68}
{"timestamp": "2025-06-12T17:33:51.959085", "level": "DEBUG", "logger": "backend.services.db_utils", "message": "Auth query 'auth_lookup_e4ae2d8f-b32f-52d7-99cd-8a33b19652c7' successful, result format preserved", "module": "db_utils", "function": "execute_query_with_timeout", "line": 72}
{"timestamp": "2025-06-12T17:33:51.965029", "level": "DEBUG", "logger": "backend.services.auth", "message": "Authentication attempt with token prefix: eyJhbGciOi...", "module": "auth", "function": "get_current_user", "line": 727}
{"timestamp": "2025-06-12T17:33:51.965197", "level": "DEBUG", "logger": "backend.core.security.jwt_keys", "message": "Token contains key ID: 0bf271b1-2fbf-4d96-987f-e0b06b2fd890", "module": "jwt_keys", "function": "decode_token", "line": 188}
{"timestamp": "2025-06-12T17:33:51.965258", "level": "DEBUG", "logger": "backend.core.security.jwt_keys", "message": "Using key ID 0bf271b1-2fbf-4d96-987f-e0b06b2fd890 for verification", "module": "jwt_keys", "function": "decode_token", "line": 195}
{"timestamp": "2025-06-12T17:33:51.965405", "level": "DEBUG", "logger": "backend.services.auth", "message": "JWT decoded successfully with key manager", "module": "auth", "function": "authenticate_token", "line": 683}
{"timestamp": "2025-06-12T17:33:51.967276", "level": "DEBUG", "logger": "backend.services.db_utils", "message": "Query 'auth_lookup_e4ae2d8f-b32f-52d7-99cd-8a33b19652c7' completed in 0.002s", "module": "db_utils", "function": "execute_query_with_timeout", "line": 68}
{"timestamp": "2025-06-12T17:33:51.967330", "level": "DEBUG", "logger": "backend.services.db_utils", "message": "Auth query 'auth_lookup_e4ae2d8f-b32f-52d7-99cd-8a33b19652c7' successful, result format preserved", "module": "db_utils", "function": "execute_query_with_timeout", "line": 72}
{"timestamp": "2025-06-12T17:33:51.967426", "level": "DEBUG", "logger": "backend.services.auth", "message": "Successfully authenticated user: <EMAIL>", "module": "auth", "function": "get_current_user", "line": 732}
{"timestamp": "2025-06-12T17:33:51.967472", "level": "DEBUG", "logger": "backend.services.auth", "message": "Successfully authenticated user: <EMAIL>", "module": "auth", "function": "get_current_user", "line": 738}
{"timestamp": "2025-06-12T17:33:51.968783", "level": "DEBUG", "logger": "backend.routes.qr_preview", "message": "Received QR preview SVG request: {\"qr_type\": \"scooter_unlock\", \"data\": {\"scooter_id\": \"SCOOT123\", \"unlock_code\": \"UNLOCK42\", \"title\": \"Scooter_unlock QR Code\", \"description\": \"Scan to view scooter_unlock content\"}, \"designOptions\": {\"size\": 256, \"margin\": 10, \"pattern\": \"square\", \"marker_border_style\": \"square\", \"marker_center_style\": \"square\", \"logo_size\": 0.3, \"logo_position\": \"center\", \"logo_opacity\": 1, \"logo_remove_background\": false, \"frame_style\": \"none\", \"frame_font\": \"SF Pro\", \"frame_font_size\": 16, \"foreground_color\":", "module": "qr_preview", "function": "preview_qr_svg", "line": 242}
{"timestamp": "2025-06-12T17:33:51.968861", "level": "DEBUG", "logger": "backend.routes.qr_preview", "message": "Form data keys: ['request']", "module": "qr_preview", "function": "preview_qr_svg", "line": 297}
{"timestamp": "2025-06-12T17:33:51.968897", "level": "DEBUG", "logger": "backend.routes.qr_preview", "message": "No logo key found in form data", "module": "qr_preview", "function": "preview_qr_svg", "line": 309}
{"timestamp": "2025-06-12T17:33:51.968935", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Formatting content for QR type: qrtype.scooter_unlock, content keys: ['scooter_id', 'unlock_code', 'title', 'description']", "module": "qr_preview", "function": "_format_qr_data", "line": 876}
{"timestamp": "2025-06-12T17:33:51.985477", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Logo remove background setting: False, has logo: False", "module": "qr_preview", "function": "generate_preview_svg_modular", "line": 727}
{"timestamp": "2025-06-12T17:33:51.998888", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Drawing 3 finder patterns at positions: [(0, 0), (42, 0), (0, 42)]", "module": "qr_preview", "function": "generate_preview_svg_modular", "line": 775}
{"timestamp": "2025-06-12T17:33:51.998973", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "QR size: 49, border_style: square, center_style: square", "module": "qr_preview", "function": "generate_preview_svg_modular", "line": 776}
{"timestamp": "2025-06-12T17:33:51.999021", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Drawing finder pattern 1/3 at position (0, 0)", "module": "qr_preview", "function": "generate_preview_svg_modular", "line": 780}
{"timestamp": "2025-06-12T17:33:51.999083", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "_draw_finder_marker called: mx=0, my=0, module_size=5.918367346938775, border_style=square, center_style=square, border_color=#000000, center_color=#000000, offset=(20, 20)", "module": "qr_preview", "function": "_draw_finder_marker", "line": 97}
{"timestamp": "2025-06-12T17:33:51.999144", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Creating finder pattern group with id='marker-0-0'", "module": "qr_preview", "function": "_draw_finder_marker", "line": 110}
{"timestamp": "2025-06-12T17:33:51.999215", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Drawing square border:", "module": "qr_preview", "function": "_draw_finder_marker", "line": 160}
{"timestamp": "2025-06-12T17:33:51.999259", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "  Outer: insert=(20.0, 20.0), size=(41.42857142857143, 41.42857142857143)", "module": "qr_preview", "function": "_draw_finder_marker", "line": 161}
{"timestamp": "2025-06-12T17:33:51.999298", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "  Inner: insert=(27.10204081632653, 27.10204081632653), size=(27.224489795918366, 27.224489795918366)", "module": "qr_preview", "function": "_draw_finder_marker", "line": 162}
{"timestamp": "2025-06-12T17:33:51.999373", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Drawing marker center: style=square, color=#000000, pos=(34.79591836734694, 34.79591836734694), size=11.83673469387755", "module": "qr_preview", "function": "_draw_marker_center", "line": 179}
{"timestamp": "2025-06-12T17:33:51.999431", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Successfully added finder pattern group to SVG", "module": "qr_preview", "function": "_draw_finder_marker", "line": 171}
{"timestamp": "2025-06-12T17:33:51.999469", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Successfully drew finder pattern 1/3", "module": "qr_preview", "function": "generate_preview_svg_modular", "line": 782}
{"timestamp": "2025-06-12T17:33:51.999506", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Drawing finder pattern 2/3 at position (42, 0)", "module": "qr_preview", "function": "generate_preview_svg_modular", "line": 780}
{"timestamp": "2025-06-12T17:33:51.999549", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "_draw_finder_marker called: mx=42, my=0, module_size=5.918367346938775, border_style=square, center_style=square, border_color=#000000, center_color=#000000, offset=(20, 20)", "module": "qr_preview", "function": "_draw_finder_marker", "line": 97}
{"timestamp": "2025-06-12T17:33:51.999594", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Creating finder pattern group with id='marker-42-0'", "module": "qr_preview", "function": "_draw_finder_marker", "line": 110}
{"timestamp": "2025-06-12T17:33:51.999630", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Drawing square border:", "module": "qr_preview", "function": "_draw_finder_marker", "line": 160}
{"timestamp": "2025-06-12T17:33:51.999669", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "  Outer: insert=(268.57142857142856, 20.0), size=(41.42857142857143, 41.42857142857143)", "module": "qr_preview", "function": "_draw_finder_marker", "line": 161}
{"timestamp": "2025-06-12T17:33:51.999709", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "  Inner: insert=(275.67346938775506, 27.10204081632653), size=(27.224489795918366, 27.224489795918366)", "module": "qr_preview", "function": "_draw_finder_marker", "line": 162}
{"timestamp": "2025-06-12T17:33:51.999775", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Drawing marker center: style=square, color=#000000, pos=(283.3673469387755, 34.79591836734694), size=11.83673469387755", "module": "qr_preview", "function": "_draw_marker_center", "line": 179}
{"timestamp": "2025-06-12T17:33:51.999827", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Successfully added finder pattern group to SVG", "module": "qr_preview", "function": "_draw_finder_marker", "line": 171}
{"timestamp": "2025-06-12T17:33:51.999864", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Successfully drew finder pattern 2/3", "module": "qr_preview", "function": "generate_preview_svg_modular", "line": 782}
{"timestamp": "2025-06-12T17:33:51.999916", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Drawing finder pattern 3/3 at position (0, 42)", "module": "qr_preview", "function": "generate_preview_svg_modular", "line": 780}
{"timestamp": "2025-06-12T17:33:51.999955", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "_draw_finder_marker called: mx=0, my=42, module_size=5.918367346938775, border_style=square, center_style=square, border_color=#000000, center_color=#000000, offset=(20, 20)", "module": "qr_preview", "function": "_draw_finder_marker", "line": 97}
{"timestamp": "2025-06-12T17:33:51.999997", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Creating finder pattern group with id='marker-0-42'", "module": "qr_preview", "function": "_draw_finder_marker", "line": 110}
{"timestamp": "2025-06-12T17:33:52.000033", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Drawing square border:", "module": "qr_preview", "function": "_draw_finder_marker", "line": 160}
{"timestamp": "2025-06-12T17:33:52.000071", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "  Outer: insert=(20.0, 268.57142857142856), size=(41.42857142857143, 41.42857142857143)", "module": "qr_preview", "function": "_draw_finder_marker", "line": 161}
{"timestamp": "2025-06-12T17:33:52.000108", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "  Inner: insert=(27.10204081632653, 275.67346938775506), size=(27.224489795918366, 27.224489795918366)", "module": "qr_preview", "function": "_draw_finder_marker", "line": 162}
{"timestamp": "2025-06-12T17:33:52.000171", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Drawing marker center: style=square, color=#000000, pos=(34.79591836734694, 283.3673469387755), size=11.83673469387755", "module": "qr_preview", "function": "_draw_marker_center", "line": 179}
{"timestamp": "2025-06-12T17:33:52.000221", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Successfully added finder pattern group to SVG", "module": "qr_preview", "function": "_draw_finder_marker", "line": 171}
{"timestamp": "2025-06-12T17:33:52.000257", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Successfully drew finder pattern 3/3", "module": "qr_preview", "function": "generate_preview_svg_modular", "line": 782}
{"timestamp": "2025-06-12T17:33:52.019801", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Generated SVG length: 176251", "module": "qr_preview", "function": "generate_preview_svg_modular", "line": 802}
{"timestamp": "2025-06-12T17:33:52.019946", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "SVG contains marker-0-0: True", "module": "qr_preview", "function": "generate_preview_svg_modular", "line": 803}
{"timestamp": "2025-06-12T17:33:52.020055", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "SVG contains marker-18-0: False", "module": "qr_preview", "function": "generate_preview_svg_modular", "line": 804}
{"timestamp": "2025-06-12T17:33:52.020163", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "SVG contains marker-0-18: False", "module": "qr_preview", "function": "generate_preview_svg_modular", "line": 805}
{"timestamp": "2025-06-12T17:33:52.020452", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "SVG marker counts - outer: 3, inner: 3, dot: 0", "module": "qr_preview", "function": "generate_preview_svg_modular", "line": 813}
{"timestamp": "2025-06-12T17:33:52.020484", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "SVG label counts - text: 0, background: 0", "module": "qr_preview", "function": "generate_preview_svg_modular", "line": 814}
{"timestamp": "2025-06-12T17:33:52.023033", "level": "DEBUG", "logger": "backend.main", "message": "[Auth] Enforced Bearer token authentication for request", "module": "main", "function": "bearer_token_auth_middleware", "line": 274}
{"timestamp": "2025-06-12T17:33:52.277661", "level": "DEBUG", "logger": "backend.core.security.jwt_keys", "message": "Token contains key ID: 0bf271b1-2fbf-4d96-987f-e0b06b2fd890", "module": "jwt_keys", "function": "decode_token", "line": 188}
{"timestamp": "2025-06-12T17:33:52.277724", "level": "DEBUG", "logger": "backend.core.security.jwt_keys", "message": "Using key ID 0bf271b1-2fbf-4d96-987f-e0b06b2fd890 for verification", "module": "jwt_keys", "function": "decode_token", "line": 195}
{"timestamp": "2025-06-12T17:33:52.277873", "level": "DEBUG", "logger": "backend.services.auth", "message": "JWT decoded successfully with key manager", "module": "auth", "function": "authenticate_token", "line": 683}
{"timestamp": "2025-06-12T17:33:52.279476", "level": "DEBUG", "logger": "backend.services.db_utils", "message": "Query 'auth_lookup_e4ae2d8f-b32f-52d7-99cd-8a33b19652c7' completed in 0.001s", "module": "db_utils", "function": "execute_query_with_timeout", "line": 68}
{"timestamp": "2025-06-12T17:33:52.279520", "level": "DEBUG", "logger": "backend.services.db_utils", "message": "Auth query 'auth_lookup_e4ae2d8f-b32f-52d7-99cd-8a33b19652c7' successful, result format preserved", "module": "db_utils", "function": "execute_query_with_timeout", "line": 72}
{"timestamp": "2025-06-12T17:33:52.284564", "level": "DEBUG", "logger": "backend.services.auth", "message": "Authentication attempt with token prefix: eyJhbGciOi...", "module": "auth", "function": "get_current_user", "line": 727}
{"timestamp": "2025-06-12T17:33:52.284654", "level": "DEBUG", "logger": "backend.core.security.jwt_keys", "message": "Token contains key ID: 0bf271b1-2fbf-4d96-987f-e0b06b2fd890", "module": "jwt_keys", "function": "decode_token", "line": 188}
{"timestamp": "2025-06-12T17:33:52.284690", "level": "DEBUG", "logger": "backend.core.security.jwt_keys", "message": "Using key ID 0bf271b1-2fbf-4d96-987f-e0b06b2fd890 for verification", "module": "jwt_keys", "function": "decode_token", "line": 195}
{"timestamp": "2025-06-12T17:33:52.284777", "level": "DEBUG", "logger": "backend.services.auth", "message": "JWT decoded successfully with key manager", "module": "auth", "function": "authenticate_token", "line": 683}
{"timestamp": "2025-06-12T17:33:52.286511", "level": "DEBUG", "logger": "backend.services.db_utils", "message": "Query 'auth_lookup_e4ae2d8f-b32f-52d7-99cd-8a33b19652c7' completed in 0.002s", "module": "db_utils", "function": "execute_query_with_timeout", "line": 68}
{"timestamp": "2025-06-12T17:33:52.286555", "level": "DEBUG", "logger": "backend.services.db_utils", "message": "Auth query 'auth_lookup_e4ae2d8f-b32f-52d7-99cd-8a33b19652c7' successful, result format preserved", "module": "db_utils", "function": "execute_query_with_timeout", "line": 72}
{"timestamp": "2025-06-12T17:33:52.286604", "level": "DEBUG", "logger": "backend.services.auth", "message": "Successfully authenticated user: <EMAIL>", "module": "auth", "function": "get_current_user", "line": 732}
{"timestamp": "2025-06-12T17:33:52.286637", "level": "DEBUG", "logger": "backend.services.auth", "message": "Successfully authenticated user: <EMAIL>", "module": "auth", "function": "get_current_user", "line": 738}
{"timestamp": "2025-06-12T17:33:52.287792", "level": "DEBUG", "logger": "backend.routes.qr_preview", "message": "Received QR preview SVG request: {\"qr_type\": \"scooter_unlock\", \"data\": {\"scooter_id\": \"SCOOT123\", \"unlock_code\": \"UNLOCK42\", \"title\": \"Scooter_unlock QR Code\", \"description\": \"Scan to view scooter_unlock content\"}, \"designOptions\": {\"size\": 256, \"margin\": 10, \"pattern\": \"square\", \"marker_border_style\": \"square\", \"marker_center_style\": \"square\", \"logo_size\": 0.3, \"logo_position\": \"center\", \"logo_opacity\": 1, \"logo_remove_background\": false, \"frame_style\": \"none\", \"frame_font\": \"SF Pro\", \"frame_font_size\": 16, \"foreground_color\":", "module": "qr_preview", "function": "preview_qr_svg", "line": 242}
{"timestamp": "2025-06-12T17:33:52.287866", "level": "DEBUG", "logger": "backend.routes.qr_preview", "message": "Form data keys: ['request']", "module": "qr_preview", "function": "preview_qr_svg", "line": 297}
{"timestamp": "2025-06-12T17:33:52.287935", "level": "DEBUG", "logger": "backend.routes.qr_preview", "message": "No logo key found in form data", "module": "qr_preview", "function": "preview_qr_svg", "line": 309}
{"timestamp": "2025-06-12T17:33:52.287975", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Formatting content for QR type: qrtype.scooter_unlock, content keys: ['scooter_id', 'unlock_code', 'title', 'description']", "module": "qr_preview", "function": "_format_qr_data", "line": 876}
{"timestamp": "2025-06-12T17:33:52.304196", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Logo remove background setting: False, has logo: False", "module": "qr_preview", "function": "generate_preview_svg_modular", "line": 727}
{"timestamp": "2025-06-12T17:33:52.317266", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Drawing 3 finder patterns at positions: [(0, 0), (42, 0), (0, 42)]", "module": "qr_preview", "function": "generate_preview_svg_modular", "line": 775}
{"timestamp": "2025-06-12T17:33:52.317311", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "QR size: 49, border_style: square, center_style: square", "module": "qr_preview", "function": "generate_preview_svg_modular", "line": 776}
{"timestamp": "2025-06-12T17:33:52.317348", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Drawing finder pattern 1/3 at position (0, 0)", "module": "qr_preview", "function": "generate_preview_svg_modular", "line": 780}
{"timestamp": "2025-06-12T17:33:52.317388", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "_draw_finder_marker called: mx=0, my=0, module_size=5.918367346938775, border_style=square, center_style=square, border_color=#000000, center_color=#000000, offset=(20, 20)", "module": "qr_preview", "function": "_draw_finder_marker", "line": 97}
{"timestamp": "2025-06-12T17:33:52.317436", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Creating finder pattern group with id='marker-0-0'", "module": "qr_preview", "function": "_draw_finder_marker", "line": 110}
{"timestamp": "2025-06-12T17:33:52.317471", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Drawing square border:", "module": "qr_preview", "function": "_draw_finder_marker", "line": 160}
{"timestamp": "2025-06-12T17:33:52.317506", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "  Outer: insert=(20.0, 20.0), size=(41.42857142857143, 41.42857142857143)", "module": "qr_preview", "function": "_draw_finder_marker", "line": 161}
{"timestamp": "2025-06-12T17:33:52.317541", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "  Inner: insert=(27.10204081632653, 27.10204081632653), size=(27.224489795918366, 27.224489795918366)", "module": "qr_preview", "function": "_draw_finder_marker", "line": 162}
{"timestamp": "2025-06-12T17:33:52.317605", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Drawing marker center: style=square, color=#000000, pos=(34.79591836734694, 34.79591836734694), size=11.83673469387755", "module": "qr_preview", "function": "_draw_marker_center", "line": 179}
{"timestamp": "2025-06-12T17:33:52.317669", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Successfully added finder pattern group to SVG", "module": "qr_preview", "function": "_draw_finder_marker", "line": 171}
{"timestamp": "2025-06-12T17:33:52.317704", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Successfully drew finder pattern 1/3", "module": "qr_preview", "function": "generate_preview_svg_modular", "line": 782}
{"timestamp": "2025-06-12T17:33:52.317733", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Drawing finder pattern 2/3 at position (42, 0)", "module": "qr_preview", "function": "generate_preview_svg_modular", "line": 780}
{"timestamp": "2025-06-12T17:33:52.317765", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "_draw_finder_marker called: mx=42, my=0, module_size=5.918367346938775, border_style=square, center_style=square, border_color=#000000, center_color=#000000, offset=(20, 20)", "module": "qr_preview", "function": "_draw_finder_marker", "line": 97}
{"timestamp": "2025-06-12T17:33:52.317828", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Creating finder pattern group with id='marker-42-0'", "module": "qr_preview", "function": "_draw_finder_marker", "line": 110}
{"timestamp": "2025-06-12T17:33:52.317862", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Drawing square border:", "module": "qr_preview", "function": "_draw_finder_marker", "line": 160}
{"timestamp": "2025-06-12T17:33:52.317899", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "  Outer: insert=(268.57142857142856, 20.0), size=(41.42857142857143, 41.42857142857143)", "module": "qr_preview", "function": "_draw_finder_marker", "line": 161}
{"timestamp": "2025-06-12T17:33:52.317931", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "  Inner: insert=(275.67346938775506, 27.10204081632653), size=(27.224489795918366, 27.224489795918366)", "module": "qr_preview", "function": "_draw_finder_marker", "line": 162}
{"timestamp": "2025-06-12T17:33:52.317986", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Drawing marker center: style=square, color=#000000, pos=(283.3673469387755, 34.79591836734694), size=11.83673469387755", "module": "qr_preview", "function": "_draw_marker_center", "line": 179}
{"timestamp": "2025-06-12T17:33:52.318030", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Successfully added finder pattern group to SVG", "module": "qr_preview", "function": "_draw_finder_marker", "line": 171}
{"timestamp": "2025-06-12T17:33:52.318060", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Successfully drew finder pattern 2/3", "module": "qr_preview", "function": "generate_preview_svg_modular", "line": 782}
{"timestamp": "2025-06-12T17:33:52.318092", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Drawing finder pattern 3/3 at position (0, 42)", "module": "qr_preview", "function": "generate_preview_svg_modular", "line": 780}
{"timestamp": "2025-06-12T17:33:52.318125", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "_draw_finder_marker called: mx=0, my=42, module_size=5.918367346938775, border_style=square, center_style=square, border_color=#000000, center_color=#000000, offset=(20, 20)", "module": "qr_preview", "function": "_draw_finder_marker", "line": 97}
{"timestamp": "2025-06-12T17:33:52.318161", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Creating finder pattern group with id='marker-0-42'", "module": "qr_preview", "function": "_draw_finder_marker", "line": 110}
{"timestamp": "2025-06-12T17:33:52.318193", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Drawing square border:", "module": "qr_preview", "function": "_draw_finder_marker", "line": 160}
{"timestamp": "2025-06-12T17:33:52.318224", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "  Outer: insert=(20.0, 268.57142857142856), size=(41.42857142857143, 41.42857142857143)", "module": "qr_preview", "function": "_draw_finder_marker", "line": 161}
{"timestamp": "2025-06-12T17:33:52.318255", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "  Inner: insert=(27.10204081632653, 275.67346938775506), size=(27.224489795918366, 27.224489795918366)", "module": "qr_preview", "function": "_draw_finder_marker", "line": 162}
{"timestamp": "2025-06-12T17:33:52.318316", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Drawing marker center: style=square, color=#000000, pos=(34.79591836734694, 283.3673469387755), size=11.83673469387755", "module": "qr_preview", "function": "_draw_marker_center", "line": 179}
{"timestamp": "2025-06-12T17:33:52.318360", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Successfully added finder pattern group to SVG", "module": "qr_preview", "function": "_draw_finder_marker", "line": 171}
{"timestamp": "2025-06-12T17:33:52.318403", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Successfully drew finder pattern 3/3", "module": "qr_preview", "function": "generate_preview_svg_modular", "line": 782}
{"timestamp": "2025-06-12T17:33:52.337455", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Generated SVG length: 176251", "module": "qr_preview", "function": "generate_preview_svg_modular", "line": 802}
{"timestamp": "2025-06-12T17:33:52.337582", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "SVG contains marker-0-0: True", "module": "qr_preview", "function": "generate_preview_svg_modular", "line": 803}
{"timestamp": "2025-06-12T17:33:52.337695", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "SVG contains marker-18-0: False", "module": "qr_preview", "function": "generate_preview_svg_modular", "line": 804}
{"timestamp": "2025-06-12T17:33:52.337805", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "SVG contains marker-0-18: False", "module": "qr_preview", "function": "generate_preview_svg_modular", "line": 805}
{"timestamp": "2025-06-12T17:33:52.338092", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "SVG marker counts - outer: 3, inner: 3, dot: 0", "module": "qr_preview", "function": "generate_preview_svg_modular", "line": 813}
{"timestamp": "2025-06-12T17:33:52.338126", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "SVG label counts - text: 0, background: 0", "module": "qr_preview", "function": "generate_preview_svg_modular", "line": 814}
{"timestamp": "2025-06-12T17:33:52.339912", "level": "DEBUG", "logger": "backend.main", "message": "[Auth] Enforced Bearer token authentication for request", "module": "main", "function": "bearer_token_auth_middleware", "line": 274}
{"timestamp": "2025-06-12T17:33:52.779980", "level": "DEBUG", "logger": "backend.core.security.jwt_keys", "message": "Token contains key ID: 0bf271b1-2fbf-4d96-987f-e0b06b2fd890", "module": "jwt_keys", "function": "decode_token", "line": 188}
{"timestamp": "2025-06-12T17:33:52.780037", "level": "DEBUG", "logger": "backend.core.security.jwt_keys", "message": "Using key ID 0bf271b1-2fbf-4d96-987f-e0b06b2fd890 for verification", "module": "jwt_keys", "function": "decode_token", "line": 195}
{"timestamp": "2025-06-12T17:33:52.780159", "level": "DEBUG", "logger": "backend.services.auth", "message": "JWT decoded successfully with key manager", "module": "auth", "function": "authenticate_token", "line": 683}
{"timestamp": "2025-06-12T17:33:52.782146", "level": "DEBUG", "logger": "backend.services.db_utils", "message": "Query 'auth_lookup_e4ae2d8f-b32f-52d7-99cd-8a33b19652c7' completed in 0.002s", "module": "db_utils", "function": "execute_query_with_timeout", "line": 68}
{"timestamp": "2025-06-12T17:33:52.782208", "level": "DEBUG", "logger": "backend.services.db_utils", "message": "Auth query 'auth_lookup_e4ae2d8f-b32f-52d7-99cd-8a33b19652c7' successful, result format preserved", "module": "db_utils", "function": "execute_query_with_timeout", "line": 72}
{"timestamp": "2025-06-12T17:33:52.787040", "level": "DEBUG", "logger": "backend.services.auth", "message": "Authentication attempt with token prefix: eyJhbGciOi...", "module": "auth", "function": "get_current_user", "line": 727}
{"timestamp": "2025-06-12T17:33:52.787174", "level": "DEBUG", "logger": "backend.core.security.jwt_keys", "message": "Token contains key ID: 0bf271b1-2fbf-4d96-987f-e0b06b2fd890", "module": "jwt_keys", "function": "decode_token", "line": 188}
{"timestamp": "2025-06-12T17:33:52.787224", "level": "DEBUG", "logger": "backend.core.security.jwt_keys", "message": "Using key ID 0bf271b1-2fbf-4d96-987f-e0b06b2fd890 for verification", "module": "jwt_keys", "function": "decode_token", "line": 195}
{"timestamp": "2025-06-12T17:33:52.787347", "level": "DEBUG", "logger": "backend.services.auth", "message": "JWT decoded successfully with key manager", "module": "auth", "function": "authenticate_token", "line": 683}
{"timestamp": "2025-06-12T17:33:52.788720", "level": "DEBUG", "logger": "backend.services.db_utils", "message": "Query 'auth_lookup_e4ae2d8f-b32f-52d7-99cd-8a33b19652c7' completed in 0.001s", "module": "db_utils", "function": "execute_query_with_timeout", "line": 68}
{"timestamp": "2025-06-12T17:33:52.788773", "level": "DEBUG", "logger": "backend.services.db_utils", "message": "Auth query 'auth_lookup_e4ae2d8f-b32f-52d7-99cd-8a33b19652c7' successful, result format preserved", "module": "db_utils", "function": "execute_query_with_timeout", "line": 72}
{"timestamp": "2025-06-12T17:33:52.788840", "level": "DEBUG", "logger": "backend.services.auth", "message": "Successfully authenticated user: <EMAIL>", "module": "auth", "function": "get_current_user", "line": 732}
{"timestamp": "2025-06-12T17:33:52.788882", "level": "DEBUG", "logger": "backend.services.auth", "message": "Successfully authenticated user: <EMAIL>", "module": "auth", "function": "get_current_user", "line": 738}
{"timestamp": "2025-06-12T17:33:52.790363", "level": "DEBUG", "logger": "backend.routes.qr_preview", "message": "Received QR preview SVG request: {\"qr_type\": \"scooter_unlock\", \"data\": {\"scooter_id\": \"SCOOT123\", \"unlock_code\": \"UNLOCK42\", \"title\": \"Scooter_unlock QR Code\", \"description\": \"Scan to view scooter_unlock content\"}, \"designOptions\": {\"size\": 256, \"margin\": 10, \"pattern\": \"square\", \"marker_border_style\": \"square\", \"marker_border_color\": \"#000000\", \"marker_center_style\": \"square\", \"marker_center_color\": \"#000000\", \"logo_size\": 0.3, \"logo_position\": \"center\", \"logo_opacity\": 1, \"logo_remove_background\": false, \"frame_style\": \"none\"", "module": "qr_preview", "function": "preview_qr_svg", "line": 242}
{"timestamp": "2025-06-12T17:33:52.790462", "level": "DEBUG", "logger": "backend.routes.qr_preview", "message": "Form data keys: ['request']", "module": "qr_preview", "function": "preview_qr_svg", "line": 297}
{"timestamp": "2025-06-12T17:33:52.790517", "level": "DEBUG", "logger": "backend.routes.qr_preview", "message": "No logo key found in form data", "module": "qr_preview", "function": "preview_qr_svg", "line": 309}
{"timestamp": "2025-06-12T17:33:52.790558", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Formatting content for QR type: qrtype.scooter_unlock, content keys: ['scooter_id', 'unlock_code', 'title', 'description']", "module": "qr_preview", "function": "_format_qr_data", "line": 876}
{"timestamp": "2025-06-12T17:33:52.806778", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Logo remove background setting: False, has logo: False", "module": "qr_preview", "function": "generate_preview_svg_modular", "line": 727}
{"timestamp": "2025-06-12T17:33:52.820414", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Drawing 3 finder patterns at positions: [(0, 0), (42, 0), (0, 42)]", "module": "qr_preview", "function": "generate_preview_svg_modular", "line": 775}
{"timestamp": "2025-06-12T17:33:52.820486", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "QR size: 49, border_style: square, center_style: square", "module": "qr_preview", "function": "generate_preview_svg_modular", "line": 776}
{"timestamp": "2025-06-12T17:33:52.820537", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Drawing finder pattern 1/3 at position (0, 0)", "module": "qr_preview", "function": "generate_preview_svg_modular", "line": 780}
{"timestamp": "2025-06-12T17:33:52.820589", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "_draw_finder_marker called: mx=0, my=0, module_size=5.918367346938775, border_style=square, center_style=square, border_color=#000000, center_color=#000000, offset=(20, 20)", "module": "qr_preview", "function": "_draw_finder_marker", "line": 97}
{"timestamp": "2025-06-12T17:33:52.820646", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Creating finder pattern group with id='marker-0-0'", "module": "qr_preview", "function": "_draw_finder_marker", "line": 110}
{"timestamp": "2025-06-12T17:33:52.821052", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Drawing square border:", "module": "qr_preview", "function": "_draw_finder_marker", "line": 160}
{"timestamp": "2025-06-12T17:33:52.821112", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "  Outer: insert=(20.0, 20.0), size=(41.42857142857143, 41.42857142857143)", "module": "qr_preview", "function": "_draw_finder_marker", "line": 161}
{"timestamp": "2025-06-12T17:33:52.821156", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "  Inner: insert=(27.10204081632653, 27.10204081632653), size=(27.224489795918366, 27.224489795918366)", "module": "qr_preview", "function": "_draw_finder_marker", "line": 162}
{"timestamp": "2025-06-12T17:33:52.821263", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Drawing marker center: style=square, color=#000000, pos=(34.79591836734694, 34.79591836734694), size=11.83673469387755", "module": "qr_preview", "function": "_draw_marker_center", "line": 179}
{"timestamp": "2025-06-12T17:33:52.821320", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Successfully added finder pattern group to SVG", "module": "qr_preview", "function": "_draw_finder_marker", "line": 171}
{"timestamp": "2025-06-12T17:33:52.821358", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Successfully drew finder pattern 1/3", "module": "qr_preview", "function": "generate_preview_svg_modular", "line": 782}
{"timestamp": "2025-06-12T17:33:52.821395", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Drawing finder pattern 2/3 at position (42, 0)", "module": "qr_preview", "function": "generate_preview_svg_modular", "line": 780}
{"timestamp": "2025-06-12T17:33:52.821431", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "_draw_finder_marker called: mx=42, my=0, module_size=5.918367346938775, border_style=square, center_style=square, border_color=#000000, center_color=#000000, offset=(20, 20)", "module": "qr_preview", "function": "_draw_finder_marker", "line": 97}
{"timestamp": "2025-06-12T17:33:52.821472", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Creating finder pattern group with id='marker-42-0'", "module": "qr_preview", "function": "_draw_finder_marker", "line": 110}
{"timestamp": "2025-06-12T17:33:52.821506", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Drawing square border:", "module": "qr_preview", "function": "_draw_finder_marker", "line": 160}
{"timestamp": "2025-06-12T17:33:52.821536", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "  Outer: insert=(268.57142857142856, 20.0), size=(41.42857142857143, 41.42857142857143)", "module": "qr_preview", "function": "_draw_finder_marker", "line": 161}
{"timestamp": "2025-06-12T17:33:52.821566", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "  Inner: insert=(275.67346938775506, 27.10204081632653), size=(27.224489795918366, 27.224489795918366)", "module": "qr_preview", "function": "_draw_finder_marker", "line": 162}
{"timestamp": "2025-06-12T17:33:52.821636", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Drawing marker center: style=square, color=#000000, pos=(283.3673469387755, 34.79591836734694), size=11.83673469387755", "module": "qr_preview", "function": "_draw_marker_center", "line": 179}
{"timestamp": "2025-06-12T17:33:52.821726", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Successfully added finder pattern group to SVG", "module": "qr_preview", "function": "_draw_finder_marker", "line": 171}
{"timestamp": "2025-06-12T17:33:52.821766", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Successfully drew finder pattern 2/3", "module": "qr_preview", "function": "generate_preview_svg_modular", "line": 782}
{"timestamp": "2025-06-12T17:33:52.821825", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Drawing finder pattern 3/3 at position (0, 42)", "module": "qr_preview", "function": "generate_preview_svg_modular", "line": 780}
{"timestamp": "2025-06-12T17:33:52.821861", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "_draw_finder_marker called: mx=0, my=42, module_size=5.918367346938775, border_style=square, center_style=square, border_color=#000000, center_color=#000000, offset=(20, 20)", "module": "qr_preview", "function": "_draw_finder_marker", "line": 97}
{"timestamp": "2025-06-12T17:33:52.821903", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Creating finder pattern group with id='marker-0-42'", "module": "qr_preview", "function": "_draw_finder_marker", "line": 110}
{"timestamp": "2025-06-12T17:33:52.821938", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Drawing square border:", "module": "qr_preview", "function": "_draw_finder_marker", "line": 160}
{"timestamp": "2025-06-12T17:33:52.821970", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "  Outer: insert=(20.0, 268.57142857142856), size=(41.42857142857143, 41.42857142857143)", "module": "qr_preview", "function": "_draw_finder_marker", "line": 161}
{"timestamp": "2025-06-12T17:33:52.822001", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "  Inner: insert=(27.10204081632653, 275.67346938775506), size=(27.224489795918366, 27.224489795918366)", "module": "qr_preview", "function": "_draw_finder_marker", "line": 162}
{"timestamp": "2025-06-12T17:33:52.822066", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Drawing marker center: style=square, color=#000000, pos=(34.79591836734694, 283.3673469387755), size=11.83673469387755", "module": "qr_preview", "function": "_draw_marker_center", "line": 179}
{"timestamp": "2025-06-12T17:33:52.822114", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Successfully added finder pattern group to SVG", "module": "qr_preview", "function": "_draw_finder_marker", "line": 171}
{"timestamp": "2025-06-12T17:33:52.822144", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Successfully drew finder pattern 3/3", "module": "qr_preview", "function": "generate_preview_svg_modular", "line": 782}
{"timestamp": "2025-06-12T17:33:52.842173", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Generated SVG length: 176251", "module": "qr_preview", "function": "generate_preview_svg_modular", "line": 802}
{"timestamp": "2025-06-12T17:33:52.842309", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "SVG contains marker-0-0: True", "module": "qr_preview", "function": "generate_preview_svg_modular", "line": 803}
{"timestamp": "2025-06-12T17:33:52.842422", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "SVG contains marker-18-0: False", "module": "qr_preview", "function": "generate_preview_svg_modular", "line": 804}
{"timestamp": "2025-06-12T17:33:52.842534", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "SVG contains marker-0-18: False", "module": "qr_preview", "function": "generate_preview_svg_modular", "line": 805}
{"timestamp": "2025-06-12T17:33:52.842832", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "SVG marker counts - outer: 3, inner: 3, dot: 0", "module": "qr_preview", "function": "generate_preview_svg_modular", "line": 813}
{"timestamp": "2025-06-12T17:33:52.842864", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "SVG label counts - text: 0, background: 0", "module": "qr_preview", "function": "generate_preview_svg_modular", "line": 814}
{"timestamp": "2025-06-12T17:33:52.844690", "level": "DEBUG", "logger": "backend.main", "message": "[Auth] Enforced Bearer token authentication for request", "module": "main", "function": "bearer_token_auth_middleware", "line": 274}
{"timestamp": "2025-06-12T17:33:55.339787", "level": "DEBUG", "logger": "backend.core.security.jwt_keys", "message": "Token contains key ID: 0bf271b1-2fbf-4d96-987f-e0b06b2fd890", "module": "jwt_keys", "function": "decode_token", "line": 188}
{"timestamp": "2025-06-12T17:33:55.340082", "level": "DEBUG", "logger": "backend.core.security.jwt_keys", "message": "Using key ID 0bf271b1-2fbf-4d96-987f-e0b06b2fd890 for verification", "module": "jwt_keys", "function": "decode_token", "line": 195}
{"timestamp": "2025-06-12T17:33:55.340223", "level": "DEBUG", "logger": "backend.services.auth", "message": "JWT decoded successfully with key manager", "module": "auth", "function": "authenticate_token", "line": 683}
{"timestamp": "2025-06-12T17:33:55.344155", "level": "DEBUG", "logger": "backend.services.db_utils", "message": "Query 'auth_lookup_e4ae2d8f-b32f-52d7-99cd-8a33b19652c7' completed in 0.004s", "module": "db_utils", "function": "execute_query_with_timeout", "line": 68}
{"timestamp": "2025-06-12T17:33:55.344210", "level": "DEBUG", "logger": "backend.services.db_utils", "message": "Auth query 'auth_lookup_e4ae2d8f-b32f-52d7-99cd-8a33b19652c7' successful, result format preserved", "module": "db_utils", "function": "execute_query_with_timeout", "line": 72}
{"timestamp": "2025-06-12T17:33:55.348295", "level": "DEBUG", "logger": "backend.services.auth", "message": "Authentication attempt with token prefix: eyJhbGciOi...", "module": "auth", "function": "get_current_user", "line": 727}
{"timestamp": "2025-06-12T17:33:55.348385", "level": "DEBUG", "logger": "backend.core.security.jwt_keys", "message": "Token contains key ID: 0bf271b1-2fbf-4d96-987f-e0b06b2fd890", "module": "jwt_keys", "function": "decode_token", "line": 188}
{"timestamp": "2025-06-12T17:33:55.348421", "level": "DEBUG", "logger": "backend.core.security.jwt_keys", "message": "Using key ID 0bf271b1-2fbf-4d96-987f-e0b06b2fd890 for verification", "module": "jwt_keys", "function": "decode_token", "line": 195}
{"timestamp": "2025-06-12T17:33:55.348520", "level": "DEBUG", "logger": "backend.services.auth", "message": "JWT decoded successfully with key manager", "module": "auth", "function": "authenticate_token", "line": 683}
{"timestamp": "2025-06-12T17:33:55.351710", "level": "DEBUG", "logger": "backend.services.db_utils", "message": "Query 'auth_lookup_e4ae2d8f-b32f-52d7-99cd-8a33b19652c7' completed in 0.003s", "module": "db_utils", "function": "execute_query_with_timeout", "line": 68}
{"timestamp": "2025-06-12T17:33:55.351750", "level": "DEBUG", "logger": "backend.services.db_utils", "message": "Auth query 'auth_lookup_e4ae2d8f-b32f-52d7-99cd-8a33b19652c7' successful, result format preserved", "module": "db_utils", "function": "execute_query_with_timeout", "line": 72}
{"timestamp": "2025-06-12T17:33:55.351797", "level": "DEBUG", "logger": "backend.services.auth", "message": "Successfully authenticated user: <EMAIL>", "module": "auth", "function": "get_current_user", "line": 732}
{"timestamp": "2025-06-12T17:33:55.351829", "level": "DEBUG", "logger": "backend.services.auth", "message": "Successfully authenticated user: <EMAIL>", "module": "auth", "function": "get_current_user", "line": 738}
{"timestamp": "2025-06-12T17:33:55.352958", "level": "DEBUG", "logger": "backend.routes.qr_preview", "message": "Received QR preview SVG request: {\"qr_type\": \"scooter_unlock\", \"data\": {\"scooter_id\": \"SCOOT123\", \"unlock_code\": \"UNLOCK42\", \"title\": \"Scooter_unlock QR Code\", \"description\": \"Scan to view scooter_unlock content\", \"service_provider\": \"Lime\"}, \"designOptions\": {\"size\": 256, \"margin\": 10, \"pattern\": \"square\", \"marker_border_style\": \"square\", \"marker_center_style\": \"square\", \"logo_size\": 0.3, \"logo_position\": \"center\", \"logo_opacity\": 1, \"logo_remove_background\": false, \"frame_style\": \"none\", \"frame_font\": \"SF Pro\", \"frame_font_si", "module": "qr_preview", "function": "preview_qr_svg", "line": 242}
{"timestamp": "2025-06-12T17:33:55.353052", "level": "DEBUG", "logger": "backend.routes.qr_preview", "message": "Form data keys: ['request']", "module": "qr_preview", "function": "preview_qr_svg", "line": 297}
{"timestamp": "2025-06-12T17:33:55.353115", "level": "DEBUG", "logger": "backend.routes.qr_preview", "message": "No logo key found in form data", "module": "qr_preview", "function": "preview_qr_svg", "line": 309}
{"timestamp": "2025-06-12T17:33:55.353158", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Formatting content for QR type: qrtype.scooter_unlock, content keys: ['scooter_id', 'unlock_code', 'title', 'description', 'service_provider']", "module": "qr_preview", "function": "_format_qr_data", "line": 876}
{"timestamp": "2025-06-12T17:33:55.371907", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Logo remove background setting: False, has logo: False", "module": "qr_preview", "function": "generate_preview_svg_modular", "line": 727}
{"timestamp": "2025-06-12T17:33:55.386737", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Drawing 3 finder patterns at positions: [(0, 0), (46, 0), (0, 46)]", "module": "qr_preview", "function": "generate_preview_svg_modular", "line": 775}
{"timestamp": "2025-06-12T17:33:55.386799", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "QR size: 53, border_style: square, center_style: square", "module": "qr_preview", "function": "generate_preview_svg_modular", "line": 776}
{"timestamp": "2025-06-12T17:33:55.386839", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Drawing finder pattern 1/3 at position (0, 0)", "module": "qr_preview", "function": "generate_preview_svg_modular", "line": 780}
{"timestamp": "2025-06-12T17:33:55.386890", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "_draw_finder_marker called: mx=0, my=0, module_size=5.471698113207547, border_style=square, center_style=square, border_color=#000000, center_color=#000000, offset=(20, 20)", "module": "qr_preview", "function": "_draw_finder_marker", "line": 97}
{"timestamp": "2025-06-12T17:33:55.386942", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Creating finder pattern group with id='marker-0-0'", "module": "qr_preview", "function": "_draw_finder_marker", "line": 110}
{"timestamp": "2025-06-12T17:33:55.386976", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Drawing square border:", "module": "qr_preview", "function": "_draw_finder_marker", "line": 160}
{"timestamp": "2025-06-12T17:33:55.387011", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "  Outer: insert=(20.0, 20.0), size=(38.301886792452834, 38.301886792452834)", "module": "qr_preview", "function": "_draw_finder_marker", "line": 161}
{"timestamp": "2025-06-12T17:33:55.387042", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "  Inner: insert=(26.566037735849058, 26.566037735849058), size=(25.169811320754715, 25.169811320754715)", "module": "qr_preview", "function": "_draw_finder_marker", "line": 162}
{"timestamp": "2025-06-12T17:33:55.387110", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Drawing marker center: style=square, color=#000000, pos=(33.67924528301887, 33.67924528301887), size=10.943396226415095", "module": "qr_preview", "function": "_draw_marker_center", "line": 179}
{"timestamp": "2025-06-12T17:33:55.387158", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Successfully added finder pattern group to SVG", "module": "qr_preview", "function": "_draw_finder_marker", "line": 171}
{"timestamp": "2025-06-12T17:33:55.387191", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Successfully drew finder pattern 1/3", "module": "qr_preview", "function": "generate_preview_svg_modular", "line": 782}
{"timestamp": "2025-06-12T17:33:55.387221", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Drawing finder pattern 2/3 at position (46, 0)", "module": "qr_preview", "function": "generate_preview_svg_modular", "line": 780}
{"timestamp": "2025-06-12T17:33:55.387295", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "_draw_finder_marker called: mx=46, my=0, module_size=5.471698113207547, border_style=square, center_style=square, border_color=#000000, center_color=#000000, offset=(20, 20)", "module": "qr_preview", "function": "_draw_finder_marker", "line": 97}
{"timestamp": "2025-06-12T17:33:55.387334", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Creating finder pattern group with id='marker-46-0'", "module": "qr_preview", "function": "_draw_finder_marker", "line": 110}
{"timestamp": "2025-06-12T17:33:55.387370", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Drawing square border:", "module": "qr_preview", "function": "_draw_finder_marker", "line": 160}
{"timestamp": "2025-06-12T17:33:55.387402", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "  Outer: insert=(271.6981132075472, 20.0), size=(38.301886792452834, 38.301886792452834)", "module": "qr_preview", "function": "_draw_finder_marker", "line": 161}
{"timestamp": "2025-06-12T17:33:55.387434", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "  Inner: insert=(278.26415094339626, 26.566037735849058), size=(25.169811320754715, 25.169811320754715)", "module": "qr_preview", "function": "_draw_finder_marker", "line": 162}
{"timestamp": "2025-06-12T17:33:55.387492", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Drawing marker center: style=square, color=#000000, pos=(285.37735849056605, 33.67924528301887), size=10.943396226415095", "module": "qr_preview", "function": "_draw_marker_center", "line": 179}
{"timestamp": "2025-06-12T17:33:55.387537", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Successfully added finder pattern group to SVG", "module": "qr_preview", "function": "_draw_finder_marker", "line": 171}
{"timestamp": "2025-06-12T17:33:55.387567", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Successfully drew finder pattern 2/3", "module": "qr_preview", "function": "generate_preview_svg_modular", "line": 782}
{"timestamp": "2025-06-12T17:33:55.387596", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Drawing finder pattern 3/3 at position (0, 46)", "module": "qr_preview", "function": "generate_preview_svg_modular", "line": 780}
{"timestamp": "2025-06-12T17:33:55.387626", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "_draw_finder_marker called: mx=0, my=46, module_size=5.471698113207547, border_style=square, center_style=square, border_color=#000000, center_color=#000000, offset=(20, 20)", "module": "qr_preview", "function": "_draw_finder_marker", "line": 97}
{"timestamp": "2025-06-12T17:33:55.387662", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Creating finder pattern group with id='marker-0-46'", "module": "qr_preview", "function": "_draw_finder_marker", "line": 110}
{"timestamp": "2025-06-12T17:33:55.387692", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Drawing square border:", "module": "qr_preview", "function": "_draw_finder_marker", "line": 160}
{"timestamp": "2025-06-12T17:33:55.387724", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "  Outer: insert=(20.0, 271.6981132075472), size=(38.301886792452834, 38.301886792452834)", "module": "qr_preview", "function": "_draw_finder_marker", "line": 161}
{"timestamp": "2025-06-12T17:33:55.387756", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "  Inner: insert=(26.566037735849058, 278.26415094339626), size=(25.169811320754715, 25.169811320754715)", "module": "qr_preview", "function": "_draw_finder_marker", "line": 162}
{"timestamp": "2025-06-12T17:33:55.387811", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Drawing marker center: style=square, color=#000000, pos=(33.67924528301887, 285.37735849056605), size=10.943396226415095", "module": "qr_preview", "function": "_draw_marker_center", "line": 179}
{"timestamp": "2025-06-12T17:33:55.387872", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Successfully added finder pattern group to SVG", "module": "qr_preview", "function": "_draw_finder_marker", "line": 171}
{"timestamp": "2025-06-12T17:33:55.387902", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Successfully drew finder pattern 3/3", "module": "qr_preview", "function": "generate_preview_svg_modular", "line": 782}
{"timestamp": "2025-06-12T17:33:55.408838", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Generated SVG length: 201904", "module": "qr_preview", "function": "generate_preview_svg_modular", "line": 802}
{"timestamp": "2025-06-12T17:33:55.408990", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "SVG contains marker-0-0: True", "module": "qr_preview", "function": "generate_preview_svg_modular", "line": 803}
{"timestamp": "2025-06-12T17:33:55.409104", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "SVG contains marker-18-0: False", "module": "qr_preview", "function": "generate_preview_svg_modular", "line": 804}
{"timestamp": "2025-06-12T17:33:55.409213", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "SVG contains marker-0-18: False", "module": "qr_preview", "function": "generate_preview_svg_modular", "line": 805}
{"timestamp": "2025-06-12T17:33:55.409543", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "SVG marker counts - outer: 3, inner: 3, dot: 0", "module": "qr_preview", "function": "generate_preview_svg_modular", "line": 813}
{"timestamp": "2025-06-12T17:33:55.409578", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "SVG label counts - text: 0, background: 0", "module": "qr_preview", "function": "generate_preview_svg_modular", "line": 814}
{"timestamp": "2025-06-12T17:33:55.411358", "level": "DEBUG", "logger": "backend.main", "message": "[Auth] Enforced Bearer token authentication for request", "module": "main", "function": "bearer_token_auth_middleware", "line": 274}
{"timestamp": "2025-06-12T17:33:56.246366", "level": "DEBUG", "logger": "backend.core.security.jwt_keys", "message": "Token contains key ID: 0bf271b1-2fbf-4d96-987f-e0b06b2fd890", "module": "jwt_keys", "function": "decode_token", "line": 188}
{"timestamp": "2025-06-12T17:33:56.246470", "level": "DEBUG", "logger": "backend.core.security.jwt_keys", "message": "Using key ID 0bf271b1-2fbf-4d96-987f-e0b06b2fd890 for verification", "module": "jwt_keys", "function": "decode_token", "line": 195}
{"timestamp": "2025-06-12T17:33:56.246676", "level": "DEBUG", "logger": "backend.services.auth", "message": "JWT decoded successfully with key manager", "module": "auth", "function": "authenticate_token", "line": 683}
{"timestamp": "2025-06-12T17:33:56.248908", "level": "DEBUG", "logger": "backend.services.db_utils", "message": "Query 'auth_lookup_e4ae2d8f-b32f-52d7-99cd-8a33b19652c7' completed in 0.002s", "module": "db_utils", "function": "execute_query_with_timeout", "line": 68}
{"timestamp": "2025-06-12T17:33:56.248992", "level": "DEBUG", "logger": "backend.services.db_utils", "message": "Auth query 'auth_lookup_e4ae2d8f-b32f-52d7-99cd-8a33b19652c7' successful, result format preserved", "module": "db_utils", "function": "execute_query_with_timeout", "line": 72}
{"timestamp": "2025-06-12T17:33:56.253997", "level": "DEBUG", "logger": "backend.services.auth", "message": "Authentication attempt with token prefix: eyJhbGciOi...", "module": "auth", "function": "get_current_user", "line": 727}
{"timestamp": "2025-06-12T17:33:56.254150", "level": "DEBUG", "logger": "backend.core.security.jwt_keys", "message": "Token contains key ID: 0bf271b1-2fbf-4d96-987f-e0b06b2fd890", "module": "jwt_keys", "function": "decode_token", "line": 188}
{"timestamp": "2025-06-12T17:33:56.254206", "level": "DEBUG", "logger": "backend.core.security.jwt_keys", "message": "Using key ID 0bf271b1-2fbf-4d96-987f-e0b06b2fd890 for verification", "module": "jwt_keys", "function": "decode_token", "line": 195}
{"timestamp": "2025-06-12T17:33:56.254420", "level": "DEBUG", "logger": "backend.services.auth", "message": "JWT decoded successfully with key manager", "module": "auth", "function": "authenticate_token", "line": 683}
{"timestamp": "2025-06-12T17:33:56.256194", "level": "DEBUG", "logger": "backend.services.db_utils", "message": "Query 'auth_lookup_e4ae2d8f-b32f-52d7-99cd-8a33b19652c7' completed in 0.002s", "module": "db_utils", "function": "execute_query_with_timeout", "line": 68}
{"timestamp": "2025-06-12T17:33:56.256277", "level": "DEBUG", "logger": "backend.services.db_utils", "message": "Auth query 'auth_lookup_e4ae2d8f-b32f-52d7-99cd-8a33b19652c7' successful, result format preserved", "module": "db_utils", "function": "execute_query_with_timeout", "line": 72}
{"timestamp": "2025-06-12T17:33:56.256357", "level": "DEBUG", "logger": "backend.services.auth", "message": "Successfully authenticated user: <EMAIL>", "module": "auth", "function": "get_current_user", "line": 732}
{"timestamp": "2025-06-12T17:33:56.256412", "level": "DEBUG", "logger": "backend.services.auth", "message": "Successfully authenticated user: <EMAIL>", "module": "auth", "function": "get_current_user", "line": 738}
{"timestamp": "2025-06-12T17:33:56.257983", "level": "DEBUG", "logger": "backend.routes.qr_preview", "message": "Received QR preview SVG request: {\"qr_type\": \"scooter_unlock\", \"data\": {\"scooter_id\": \"SCOOT123\", \"unlock_code\": \"UNLOCK42\", \"title\": \"Scooter_unlock QR Code\", \"description\": \"Scan to view scooter_unlock content\", \"service_provider\": \"Lime\"}, \"designOptions\": {\"size\": 256, \"margin\": 10, \"pattern\": \"square\", \"marker_border_style\": \"square\", \"marker_border_color\": \"#000000\", \"marker_center_style\": \"square\", \"marker_center_color\": \"#000000\", \"logo_size\": 0.3, \"logo_position\": \"center\", \"logo_opacity\": 1, \"logo_remove_background\": ", "module": "qr_preview", "function": "preview_qr_svg", "line": 242}
{"timestamp": "2025-06-12T17:33:56.258095", "level": "DEBUG", "logger": "backend.routes.qr_preview", "message": "Form data keys: ['request']", "module": "qr_preview", "function": "preview_qr_svg", "line": 297}
{"timestamp": "2025-06-12T17:33:56.258149", "level": "DEBUG", "logger": "backend.routes.qr_preview", "message": "No logo key found in form data", "module": "qr_preview", "function": "preview_qr_svg", "line": 309}
{"timestamp": "2025-06-12T17:33:56.258209", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Formatting content for QR type: qrtype.scooter_unlock, content keys: ['scooter_id', 'unlock_code', 'title', 'description', 'service_provider']", "module": "qr_preview", "function": "_format_qr_data", "line": 876}
{"timestamp": "2025-06-12T17:33:56.286580", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Logo remove background setting: False, has logo: False", "module": "qr_preview", "function": "generate_preview_svg_modular", "line": 727}
{"timestamp": "2025-06-12T17:33:56.305271", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Drawing 3 finder patterns at positions: [(0, 0), (46, 0), (0, 46)]", "module": "qr_preview", "function": "generate_preview_svg_modular", "line": 775}
{"timestamp": "2025-06-12T17:33:56.305327", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "QR size: 53, border_style: square, center_style: square", "module": "qr_preview", "function": "generate_preview_svg_modular", "line": 776}
{"timestamp": "2025-06-12T17:33:56.305366", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Drawing finder pattern 1/3 at position (0, 0)", "module": "qr_preview", "function": "generate_preview_svg_modular", "line": 780}
{"timestamp": "2025-06-12T17:33:56.305410", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "_draw_finder_marker called: mx=0, my=0, module_size=5.471698113207547, border_style=square, center_style=square, border_color=#000000, center_color=#000000, offset=(20, 20)", "module": "qr_preview", "function": "_draw_finder_marker", "line": 97}
{"timestamp": "2025-06-12T17:33:56.305490", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Creating finder pattern group with id='marker-0-0'", "module": "qr_preview", "function": "_draw_finder_marker", "line": 110}
{"timestamp": "2025-06-12T17:33:56.305525", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Drawing square border:", "module": "qr_preview", "function": "_draw_finder_marker", "line": 160}
{"timestamp": "2025-06-12T17:33:56.305560", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "  Outer: insert=(20.0, 20.0), size=(38.301886792452834, 38.301886792452834)", "module": "qr_preview", "function": "_draw_finder_marker", "line": 161}
{"timestamp": "2025-06-12T17:33:56.305595", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "  Inner: insert=(26.566037735849058, 26.566037735849058), size=(25.169811320754715, 25.169811320754715)", "module": "qr_preview", "function": "_draw_finder_marker", "line": 162}
{"timestamp": "2025-06-12T17:33:56.305665", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Drawing marker center: style=square, color=#000000, pos=(33.67924528301887, 33.67924528301887), size=10.943396226415095", "module": "qr_preview", "function": "_draw_marker_center", "line": 179}
{"timestamp": "2025-06-12T17:33:56.305713", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Successfully added finder pattern group to SVG", "module": "qr_preview", "function": "_draw_finder_marker", "line": 171}
{"timestamp": "2025-06-12T17:33:56.305745", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Successfully drew finder pattern 1/3", "module": "qr_preview", "function": "generate_preview_svg_modular", "line": 782}
{"timestamp": "2025-06-12T17:33:56.305776", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Drawing finder pattern 2/3 at position (46, 0)", "module": "qr_preview", "function": "generate_preview_svg_modular", "line": 780}
{"timestamp": "2025-06-12T17:33:56.305808", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "_draw_finder_marker called: mx=46, my=0, module_size=5.471698113207547, border_style=square, center_style=square, border_color=#000000, center_color=#000000, offset=(20, 20)", "module": "qr_preview", "function": "_draw_finder_marker", "line": 97}
{"timestamp": "2025-06-12T17:33:56.305847", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Creating finder pattern group with id='marker-46-0'", "module": "qr_preview", "function": "_draw_finder_marker", "line": 110}
{"timestamp": "2025-06-12T17:33:56.305877", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Drawing square border:", "module": "qr_preview", "function": "_draw_finder_marker", "line": 160}
{"timestamp": "2025-06-12T17:33:56.305909", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "  Outer: insert=(271.6981132075472, 20.0), size=(38.301886792452834, 38.301886792452834)", "module": "qr_preview", "function": "_draw_finder_marker", "line": 161}
{"timestamp": "2025-06-12T17:33:56.305940", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "  Inner: insert=(278.26415094339626, 26.566037735849058), size=(25.169811320754715, 25.169811320754715)", "module": "qr_preview", "function": "_draw_finder_marker", "line": 162}
{"timestamp": "2025-06-12T17:33:56.305998", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Drawing marker center: style=square, color=#000000, pos=(285.37735849056605, 33.67924528301887), size=10.943396226415095", "module": "qr_preview", "function": "_draw_marker_center", "line": 179}
{"timestamp": "2025-06-12T17:33:56.306043", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Successfully added finder pattern group to SVG", "module": "qr_preview", "function": "_draw_finder_marker", "line": 171}
{"timestamp": "2025-06-12T17:33:56.306071", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Successfully drew finder pattern 2/3", "module": "qr_preview", "function": "generate_preview_svg_modular", "line": 782}
{"timestamp": "2025-06-12T17:33:56.306118", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Drawing finder pattern 3/3 at position (0, 46)", "module": "qr_preview", "function": "generate_preview_svg_modular", "line": 780}
{"timestamp": "2025-06-12T17:33:56.306149", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "_draw_finder_marker called: mx=0, my=46, module_size=5.471698113207547, border_style=square, center_style=square, border_color=#000000, center_color=#000000, offset=(20, 20)", "module": "qr_preview", "function": "_draw_finder_marker", "line": 97}
{"timestamp": "2025-06-12T17:33:56.306187", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Creating finder pattern group with id='marker-0-46'", "module": "qr_preview", "function": "_draw_finder_marker", "line": 110}
{"timestamp": "2025-06-12T17:33:56.306217", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Drawing square border:", "module": "qr_preview", "function": "_draw_finder_marker", "line": 160}
{"timestamp": "2025-06-12T17:33:56.306249", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "  Outer: insert=(20.0, 271.6981132075472), size=(38.301886792452834, 38.301886792452834)", "module": "qr_preview", "function": "_draw_finder_marker", "line": 161}
{"timestamp": "2025-06-12T17:33:56.306280", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "  Inner: insert=(26.566037735849058, 278.26415094339626), size=(25.169811320754715, 25.169811320754715)", "module": "qr_preview", "function": "_draw_finder_marker", "line": 162}
{"timestamp": "2025-06-12T17:33:56.306336", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Drawing marker center: style=square, color=#000000, pos=(33.67924528301887, 285.37735849056605), size=10.943396226415095", "module": "qr_preview", "function": "_draw_marker_center", "line": 179}
{"timestamp": "2025-06-12T17:33:56.306380", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Successfully added finder pattern group to SVG", "module": "qr_preview", "function": "_draw_finder_marker", "line": 171}
{"timestamp": "2025-06-12T17:33:56.306409", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Successfully drew finder pattern 3/3", "module": "qr_preview", "function": "generate_preview_svg_modular", "line": 782}
{"timestamp": "2025-06-12T17:33:56.328243", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Generated SVG length: 201904", "module": "qr_preview", "function": "generate_preview_svg_modular", "line": 802}
{"timestamp": "2025-06-12T17:33:56.328390", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "SVG contains marker-0-0: True", "module": "qr_preview", "function": "generate_preview_svg_modular", "line": 803}
{"timestamp": "2025-06-12T17:33:56.328502", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "SVG contains marker-18-0: False", "module": "qr_preview", "function": "generate_preview_svg_modular", "line": 804}
{"timestamp": "2025-06-12T17:33:56.328608", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "SVG contains marker-0-18: False", "module": "qr_preview", "function": "generate_preview_svg_modular", "line": 805}
{"timestamp": "2025-06-12T17:33:56.328992", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "SVG marker counts - outer: 3, inner: 3, dot: 0", "module": "qr_preview", "function": "generate_preview_svg_modular", "line": 813}
{"timestamp": "2025-06-12T17:33:56.329038", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "SVG label counts - text: 0, background: 0", "module": "qr_preview", "function": "generate_preview_svg_modular", "line": 814}
{"timestamp": "2025-06-12T17:33:56.330984", "level": "DEBUG", "logger": "backend.main", "message": "[Auth] Enforced Bearer token authentication for request", "module": "main", "function": "bearer_token_auth_middleware", "line": 274}
{"timestamp": "2025-06-12T17:33:58.269195", "level": "DEBUG", "logger": "backend.core.security.jwt_keys", "message": "Token contains key ID: 0bf271b1-2fbf-4d96-987f-e0b06b2fd890", "module": "jwt_keys", "function": "decode_token", "line": 188}
{"timestamp": "2025-06-12T17:33:58.269279", "level": "DEBUG", "logger": "backend.core.security.jwt_keys", "message": "Using key ID 0bf271b1-2fbf-4d96-987f-e0b06b2fd890 for verification", "module": "jwt_keys", "function": "decode_token", "line": 195}
{"timestamp": "2025-06-12T17:33:58.269532", "level": "DEBUG", "logger": "backend.services.auth", "message": "JWT decoded successfully with key manager", "module": "auth", "function": "authenticate_token", "line": 683}
{"timestamp": "2025-06-12T17:33:58.272184", "level": "DEBUG", "logger": "backend.services.db_utils", "message": "Query 'auth_lookup_e4ae2d8f-b32f-52d7-99cd-8a33b19652c7' completed in 0.002s", "module": "db_utils", "function": "execute_query_with_timeout", "line": 68}
{"timestamp": "2025-06-12T17:33:58.272235", "level": "DEBUG", "logger": "backend.services.db_utils", "message": "Auth query 'auth_lookup_e4ae2d8f-b32f-52d7-99cd-8a33b19652c7' successful, result format preserved", "module": "db_utils", "function": "execute_query_with_timeout", "line": 72}
{"timestamp": "2025-06-12T17:33:58.277257", "level": "DEBUG", "logger": "backend.services.auth", "message": "Authentication attempt with token prefix: eyJhbGciOi...", "module": "auth", "function": "get_current_user", "line": 727}
{"timestamp": "2025-06-12T17:33:58.277343", "level": "DEBUG", "logger": "backend.core.security.jwt_keys", "message": "Token contains key ID: 0bf271b1-2fbf-4d96-987f-e0b06b2fd890", "module": "jwt_keys", "function": "decode_token", "line": 188}
{"timestamp": "2025-06-12T17:33:58.277379", "level": "DEBUG", "logger": "backend.core.security.jwt_keys", "message": "Using key ID 0bf271b1-2fbf-4d96-987f-e0b06b2fd890 for verification", "module": "jwt_keys", "function": "decode_token", "line": 195}
{"timestamp": "2025-06-12T17:33:58.277473", "level": "DEBUG", "logger": "backend.services.auth", "message": "JWT decoded successfully with key manager", "module": "auth", "function": "authenticate_token", "line": 683}
{"timestamp": "2025-06-12T17:33:58.278924", "level": "DEBUG", "logger": "backend.services.db_utils", "message": "Query 'auth_lookup_e4ae2d8f-b32f-52d7-99cd-8a33b19652c7' completed in 0.001s", "module": "db_utils", "function": "execute_query_with_timeout", "line": 68}
{"timestamp": "2025-06-12T17:33:58.278962", "level": "DEBUG", "logger": "backend.services.db_utils", "message": "Auth query 'auth_lookup_e4ae2d8f-b32f-52d7-99cd-8a33b19652c7' successful, result format preserved", "module": "db_utils", "function": "execute_query_with_timeout", "line": 72}
{"timestamp": "2025-06-12T17:33:58.279009", "level": "DEBUG", "logger": "backend.services.auth", "message": "Successfully authenticated user: <EMAIL>", "module": "auth", "function": "get_current_user", "line": 732}
{"timestamp": "2025-06-12T17:33:58.279042", "level": "DEBUG", "logger": "backend.services.auth", "message": "Successfully authenticated user: <EMAIL>", "module": "auth", "function": "get_current_user", "line": 738}
{"timestamp": "2025-06-12T17:33:58.280406", "level": "DEBUG", "logger": "backend.routes.qr_preview", "message": "Received QR preview SVG request: {\"qr_type\": \"scooter_unlock\", \"data\": {\"scooter_id\": \"SCOOT123\", \"unlock_code\": \"UNLOCK42\", \"title\": \"Scooter_unlock QR Code\", \"description\": \"Scan to view scooter_unlock content\", \"service_provider\": \"Lime\", \"rate_info\": \"@\"}, \"designOptions\": {\"size\": 256, \"margin\": 10, \"pattern\": \"square\", \"marker_border_style\": \"square\", \"marker_center_style\": \"square\", \"logo_size\": 0.3, \"logo_position\": \"center\", \"logo_opacity\": 1, \"logo_remove_background\": false, \"frame_style\": \"none\", \"frame_font\": \"SF Pr", "module": "qr_preview", "function": "preview_qr_svg", "line": 242}
{"timestamp": "2025-06-12T17:33:58.280544", "level": "DEBUG", "logger": "backend.routes.qr_preview", "message": "Form data keys: ['request']", "module": "qr_preview", "function": "preview_qr_svg", "line": 297}
{"timestamp": "2025-06-12T17:33:58.280579", "level": "DEBUG", "logger": "backend.routes.qr_preview", "message": "No logo key found in form data", "module": "qr_preview", "function": "preview_qr_svg", "line": 309}
{"timestamp": "2025-06-12T17:33:58.280633", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Formatting content for QR type: qrtype.scooter_unlock, content keys: ['scooter_id', 'unlock_code', 'title', 'description', 'service_provider', 'rate_info']", "module": "qr_preview", "function": "_format_qr_data", "line": 876}
{"timestamp": "2025-06-12T17:33:58.302924", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Logo remove background setting: False, has logo: False", "module": "qr_preview", "function": "generate_preview_svg_modular", "line": 727}
{"timestamp": "2025-06-12T17:33:58.320409", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Drawing 3 finder patterns at positions: [(0, 0), (50, 0), (0, 50)]", "module": "qr_preview", "function": "generate_preview_svg_modular", "line": 775}
{"timestamp": "2025-06-12T17:33:58.320454", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "QR size: 57, border_style: square, center_style: square", "module": "qr_preview", "function": "generate_preview_svg_modular", "line": 776}
{"timestamp": "2025-06-12T17:33:58.320488", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Drawing finder pattern 1/3 at position (0, 0)", "module": "qr_preview", "function": "generate_preview_svg_modular", "line": 780}
{"timestamp": "2025-06-12T17:33:58.320534", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "_draw_finder_marker called: mx=0, my=0, module_size=5.087719298245614, border_style=square, center_style=square, border_color=#000000, center_color=#000000, offset=(20, 20)", "module": "qr_preview", "function": "_draw_finder_marker", "line": 97}
{"timestamp": "2025-06-12T17:33:58.320586", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Creating finder pattern group with id='marker-0-0'", "module": "qr_preview", "function": "_draw_finder_marker", "line": 110}
{"timestamp": "2025-06-12T17:33:58.320622", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Drawing square border:", "module": "qr_preview", "function": "_draw_finder_marker", "line": 160}
{"timestamp": "2025-06-12T17:33:58.320655", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "  Outer: insert=(20.0, 20.0), size=(35.614035087719294, 35.614035087719294)", "module": "qr_preview", "function": "_draw_finder_marker", "line": 161}
{"timestamp": "2025-06-12T17:33:58.320687", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "  Inner: insert=(26.105263157894736, 26.105263157894736), size=(23.403508771929822, 23.403508771929822)", "module": "qr_preview", "function": "_draw_finder_marker", "line": 162}
{"timestamp": "2025-06-12T17:33:58.320751", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Drawing marker center: style=square, color=#000000, pos=(32.719298245614034, 32.719298245614034), size=10.175438596491228", "module": "qr_preview", "function": "_draw_marker_center", "line": 179}
{"timestamp": "2025-06-12T17:33:58.320802", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Successfully added finder pattern group to SVG", "module": "qr_preview", "function": "_draw_finder_marker", "line": 171}
{"timestamp": "2025-06-12T17:33:58.320833", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Successfully drew finder pattern 1/3", "module": "qr_preview", "function": "generate_preview_svg_modular", "line": 782}
{"timestamp": "2025-06-12T17:33:58.320863", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Drawing finder pattern 2/3 at position (50, 0)", "module": "qr_preview", "function": "generate_preview_svg_modular", "line": 780}
{"timestamp": "2025-06-12T17:33:58.320920", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "_draw_finder_marker called: mx=50, my=0, module_size=5.087719298245614, border_style=square, center_style=square, border_color=#000000, center_color=#000000, offset=(20, 20)", "module": "qr_preview", "function": "_draw_finder_marker", "line": 97}
{"timestamp": "2025-06-12T17:33:58.320957", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Creating finder pattern group with id='marker-50-0'", "module": "qr_preview", "function": "_draw_finder_marker", "line": 110}
{"timestamp": "2025-06-12T17:33:58.320987", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Drawing square border:", "module": "qr_preview", "function": "_draw_finder_marker", "line": 160}
{"timestamp": "2025-06-12T17:33:58.321018", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "  Outer: insert=(274.38596491228066, 20.0), size=(35.614035087719294, 35.614035087719294)", "module": "qr_preview", "function": "_draw_finder_marker", "line": 161}
{"timestamp": "2025-06-12T17:33:58.321049", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "  Inner: insert=(280.4912280701754, 26.105263157894736), size=(23.403508771929822, 23.403508771929822)", "module": "qr_preview", "function": "_draw_finder_marker", "line": 162}
{"timestamp": "2025-06-12T17:33:58.321104", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Drawing marker center: style=square, color=#000000, pos=(287.10526315789474, 32.719298245614034), size=10.175438596491228", "module": "qr_preview", "function": "_draw_marker_center", "line": 179}
{"timestamp": "2025-06-12T17:33:58.321150", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Successfully added finder pattern group to SVG", "module": "qr_preview", "function": "_draw_finder_marker", "line": 171}
{"timestamp": "2025-06-12T17:33:58.321180", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Successfully drew finder pattern 2/3", "module": "qr_preview", "function": "generate_preview_svg_modular", "line": 782}
{"timestamp": "2025-06-12T17:33:58.321210", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Drawing finder pattern 3/3 at position (0, 50)", "module": "qr_preview", "function": "generate_preview_svg_modular", "line": 780}
{"timestamp": "2025-06-12T17:33:58.321243", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "_draw_finder_marker called: mx=0, my=50, module_size=5.087719298245614, border_style=square, center_style=square, border_color=#000000, center_color=#000000, offset=(20, 20)", "module": "qr_preview", "function": "_draw_finder_marker", "line": 97}
{"timestamp": "2025-06-12T17:33:58.321283", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Creating finder pattern group with id='marker-0-50'", "module": "qr_preview", "function": "_draw_finder_marker", "line": 110}
{"timestamp": "2025-06-12T17:33:58.321312", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Drawing square border:", "module": "qr_preview", "function": "_draw_finder_marker", "line": 160}
{"timestamp": "2025-06-12T17:33:58.321342", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "  Outer: insert=(20.0, 274.38596491228066), size=(35.614035087719294, 35.614035087719294)", "module": "qr_preview", "function": "_draw_finder_marker", "line": 161}
{"timestamp": "2025-06-12T17:33:58.321375", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "  Inner: insert=(26.105263157894736, 280.4912280701754), size=(23.403508771929822, 23.403508771929822)", "module": "qr_preview", "function": "_draw_finder_marker", "line": 162}
{"timestamp": "2025-06-12T17:33:58.321429", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Drawing marker center: style=square, color=#000000, pos=(32.719298245614034, 287.10526315789474), size=10.175438596491228", "module": "qr_preview", "function": "_draw_marker_center", "line": 179}
{"timestamp": "2025-06-12T17:33:58.321491", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Successfully added finder pattern group to SVG", "module": "qr_preview", "function": "_draw_finder_marker", "line": 171}
{"timestamp": "2025-06-12T17:33:58.321525", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Successfully drew finder pattern 3/3", "module": "qr_preview", "function": "generate_preview_svg_modular", "line": 782}
{"timestamp": "2025-06-12T17:33:58.346931", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Generated SVG length: 243784", "module": "qr_preview", "function": "generate_preview_svg_modular", "line": 802}
{"timestamp": "2025-06-12T17:33:58.347100", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "SVG contains marker-0-0: True", "module": "qr_preview", "function": "generate_preview_svg_modular", "line": 803}
{"timestamp": "2025-06-12T17:33:58.347234", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "SVG contains marker-18-0: False", "module": "qr_preview", "function": "generate_preview_svg_modular", "line": 804}
{"timestamp": "2025-06-12T17:33:58.347370", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "SVG contains marker-0-18: False", "module": "qr_preview", "function": "generate_preview_svg_modular", "line": 805}
{"timestamp": "2025-06-12T17:33:58.347756", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "SVG marker counts - outer: 3, inner: 3, dot: 0", "module": "qr_preview", "function": "generate_preview_svg_modular", "line": 813}
{"timestamp": "2025-06-12T17:33:58.347789", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "SVG label counts - text: 0, background: 0", "module": "qr_preview", "function": "generate_preview_svg_modular", "line": 814}
{"timestamp": "2025-06-12T17:33:58.349889", "level": "DEBUG", "logger": "backend.main", "message": "[Auth] Enforced Bearer token authentication for request", "module": "main", "function": "bearer_token_auth_middleware", "line": 274}
{"timestamp": "2025-06-12T17:33:59.107505", "level": "DEBUG", "logger": "backend.main", "message": "[Auth] Enforced Bearer token authentication for request", "module": "main", "function": "bearer_token_auth_middleware", "line": 274}
{"timestamp": "2025-06-12T17:33:59.191984", "level": "DEBUG", "logger": "backend.core.security.jwt_keys", "message": "Token contains key ID: 0bf271b1-2fbf-4d96-987f-e0b06b2fd890", "module": "jwt_keys", "function": "decode_token", "line": 188}
{"timestamp": "2025-06-12T17:33:59.192054", "level": "DEBUG", "logger": "backend.core.security.jwt_keys", "message": "Using key ID 0bf271b1-2fbf-4d96-987f-e0b06b2fd890 for verification", "module": "jwt_keys", "function": "decode_token", "line": 195}
{"timestamp": "2025-06-12T17:33:59.192183", "level": "DEBUG", "logger": "backend.services.auth", "message": "JWT decoded successfully with key manager", "module": "auth", "function": "authenticate_token", "line": 683}
{"timestamp": "2025-06-12T17:33:59.193984", "level": "DEBUG", "logger": "backend.services.db_utils", "message": "Query 'auth_lookup_e4ae2d8f-b32f-52d7-99cd-8a33b19652c7' completed in 0.002s", "module": "db_utils", "function": "execute_query_with_timeout", "line": 68}
{"timestamp": "2025-06-12T17:33:59.194041", "level": "DEBUG", "logger": "backend.services.db_utils", "message": "Auth query 'auth_lookup_e4ae2d8f-b32f-52d7-99cd-8a33b19652c7' successful, result format preserved", "module": "db_utils", "function": "execute_query_with_timeout", "line": 72}
{"timestamp": "2025-06-12T17:33:59.198853", "level": "DEBUG", "logger": "backend.services.auth", "message": "Authentication attempt with token prefix: eyJhbGciOi...", "module": "auth", "function": "get_current_user", "line": 727}
{"timestamp": "2025-06-12T17:33:59.198967", "level": "DEBUG", "logger": "backend.core.security.jwt_keys", "message": "Token contains key ID: 0bf271b1-2fbf-4d96-987f-e0b06b2fd890", "module": "jwt_keys", "function": "decode_token", "line": 188}
{"timestamp": "2025-06-12T17:33:59.199061", "level": "DEBUG", "logger": "backend.core.security.jwt_keys", "message": "Using key ID 0bf271b1-2fbf-4d96-987f-e0b06b2fd890 for verification", "module": "jwt_keys", "function": "decode_token", "line": 195}
{"timestamp": "2025-06-12T17:33:59.199175", "level": "DEBUG", "logger": "backend.services.auth", "message": "JWT decoded successfully with key manager", "module": "auth", "function": "authenticate_token", "line": 683}
{"timestamp": "2025-06-12T17:33:59.200852", "level": "DEBUG", "logger": "backend.services.db_utils", "message": "Query 'auth_lookup_e4ae2d8f-b32f-52d7-99cd-8a33b19652c7' completed in 0.001s", "module": "db_utils", "function": "execute_query_with_timeout", "line": 68}
{"timestamp": "2025-06-12T17:33:59.200915", "level": "DEBUG", "logger": "backend.services.db_utils", "message": "Auth query 'auth_lookup_e4ae2d8f-b32f-52d7-99cd-8a33b19652c7' successful, result format preserved", "module": "db_utils", "function": "execute_query_with_timeout", "line": 72}
{"timestamp": "2025-06-12T17:33:59.200966", "level": "DEBUG", "logger": "backend.services.auth", "message": "Successfully authenticated user: <EMAIL>", "module": "auth", "function": "get_current_user", "line": 732}
{"timestamp": "2025-06-12T17:33:59.201004", "level": "DEBUG", "logger": "backend.services.auth", "message": "Successfully authenticated user: <EMAIL>", "module": "auth", "function": "get_current_user", "line": 738}
{"timestamp": "2025-06-12T17:33:59.202033", "level": "DEBUG", "logger": "backend.routes.qr_preview", "message": "Received QR preview SVG request: {\"qr_type\": \"scooter_unlock\", \"data\": {\"scooter_id\": \"SCOOT123\", \"unlock_code\": \"UNLOCK42\", \"title\": \"Scooter_unlock QR Code\", \"description\": \"Scan to view scooter_unlock content\", \"service_provider\": \"Lime\", \"rate_info\": \"@\"}, \"designOptions\": {\"size\": 256, \"margin\": 10, \"pattern\": \"square\", \"marker_border_style\": \"square\", \"marker_border_color\": \"#000000\", \"marker_center_style\": \"square\", \"marker_center_color\": \"#000000\", \"logo_size\": 0.3, \"logo_position\": \"center\", \"logo_opacity\": 1, \"logo_re", "module": "qr_preview", "function": "preview_qr_svg", "line": 242}
{"timestamp": "2025-06-12T17:33:59.202114", "level": "DEBUG", "logger": "backend.routes.qr_preview", "message": "Form data keys: ['request']", "module": "qr_preview", "function": "preview_qr_svg", "line": 297}
{"timestamp": "2025-06-12T17:33:59.202156", "level": "DEBUG", "logger": "backend.routes.qr_preview", "message": "No logo key found in form data", "module": "qr_preview", "function": "preview_qr_svg", "line": 309}
{"timestamp": "2025-06-12T17:33:59.202202", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Formatting content for QR type: qrtype.scooter_unlock, content keys: ['scooter_id', 'unlock_code', 'title', 'description', 'service_provider', 'rate_info']", "module": "qr_preview", "function": "_format_qr_data", "line": 876}
{"timestamp": "2025-06-12T17:33:59.224529", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Logo remove background setting: False, has logo: False", "module": "qr_preview", "function": "generate_preview_svg_modular", "line": 727}
{"timestamp": "2025-06-12T17:33:59.242512", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Drawing 3 finder patterns at positions: [(0, 0), (50, 0), (0, 50)]", "module": "qr_preview", "function": "generate_preview_svg_modular", "line": 775}
{"timestamp": "2025-06-12T17:33:59.242574", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "QR size: 57, border_style: square, center_style: square", "module": "qr_preview", "function": "generate_preview_svg_modular", "line": 776}
{"timestamp": "2025-06-12T17:33:59.242620", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Drawing finder pattern 1/3 at position (0, 0)", "module": "qr_preview", "function": "generate_preview_svg_modular", "line": 780}
{"timestamp": "2025-06-12T17:33:59.242694", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "_draw_finder_marker called: mx=0, my=0, module_size=5.087719298245614, border_style=square, center_style=square, border_color=#000000, center_color=#000000, offset=(20, 20)", "module": "qr_preview", "function": "_draw_finder_marker", "line": 97}
{"timestamp": "2025-06-12T17:33:59.242745", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Creating finder pattern group with id='marker-0-0'", "module": "qr_preview", "function": "_draw_finder_marker", "line": 110}
{"timestamp": "2025-06-12T17:33:59.242784", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Drawing square border:", "module": "qr_preview", "function": "_draw_finder_marker", "line": 160}
{"timestamp": "2025-06-12T17:33:59.242825", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "  Outer: insert=(20.0, 20.0), size=(35.614035087719294, 35.614035087719294)", "module": "qr_preview", "function": "_draw_finder_marker", "line": 161}
{"timestamp": "2025-06-12T17:33:59.242869", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "  Inner: insert=(26.105263157894736, 26.105263157894736), size=(23.403508771929822, 23.403508771929822)", "module": "qr_preview", "function": "_draw_finder_marker", "line": 162}
{"timestamp": "2025-06-12T17:33:59.242946", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Drawing marker center: style=square, color=#000000, pos=(32.719298245614034, 32.719298245614034), size=10.175438596491228", "module": "qr_preview", "function": "_draw_marker_center", "line": 179}
{"timestamp": "2025-06-12T17:33:59.243001", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Successfully added finder pattern group to SVG", "module": "qr_preview", "function": "_draw_finder_marker", "line": 171}
{"timestamp": "2025-06-12T17:33:59.243038", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Successfully drew finder pattern 1/3", "module": "qr_preview", "function": "generate_preview_svg_modular", "line": 782}
{"timestamp": "2025-06-12T17:33:59.243075", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Drawing finder pattern 2/3 at position (50, 0)", "module": "qr_preview", "function": "generate_preview_svg_modular", "line": 780}
{"timestamp": "2025-06-12T17:33:59.243120", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "_draw_finder_marker called: mx=50, my=0, module_size=5.087719298245614, border_style=square, center_style=square, border_color=#000000, center_color=#000000, offset=(20, 20)", "module": "qr_preview", "function": "_draw_finder_marker", "line": 97}
{"timestamp": "2025-06-12T17:33:59.243163", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Creating finder pattern group with id='marker-50-0'", "module": "qr_preview", "function": "_draw_finder_marker", "line": 110}
{"timestamp": "2025-06-12T17:33:59.243195", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Drawing square border:", "module": "qr_preview", "function": "_draw_finder_marker", "line": 160}
{"timestamp": "2025-06-12T17:33:59.243235", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "  Outer: insert=(274.38596491228066, 20.0), size=(35.614035087719294, 35.614035087719294)", "module": "qr_preview", "function": "_draw_finder_marker", "line": 161}
{"timestamp": "2025-06-12T17:33:59.243272", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "  Inner: insert=(280.4912280701754, 26.105263157894736), size=(23.403508771929822, 23.403508771929822)", "module": "qr_preview", "function": "_draw_finder_marker", "line": 162}
{"timestamp": "2025-06-12T17:33:59.243334", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Drawing marker center: style=square, color=#000000, pos=(287.10526315789474, 32.719298245614034), size=10.175438596491228", "module": "qr_preview", "function": "_draw_marker_center", "line": 179}
{"timestamp": "2025-06-12T17:33:59.243402", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Successfully added finder pattern group to SVG", "module": "qr_preview", "function": "_draw_finder_marker", "line": 171}
{"timestamp": "2025-06-12T17:33:59.243439", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Successfully drew finder pattern 2/3", "module": "qr_preview", "function": "generate_preview_svg_modular", "line": 782}
{"timestamp": "2025-06-12T17:33:59.243473", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Drawing finder pattern 3/3 at position (0, 50)", "module": "qr_preview", "function": "generate_preview_svg_modular", "line": 780}
{"timestamp": "2025-06-12T17:33:59.243510", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "_draw_finder_marker called: mx=0, my=50, module_size=5.087719298245614, border_style=square, center_style=square, border_color=#000000, center_color=#000000, offset=(20, 20)", "module": "qr_preview", "function": "_draw_finder_marker", "line": 97}
{"timestamp": "2025-06-12T17:33:59.243551", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Creating finder pattern group with id='marker-0-50'", "module": "qr_preview", "function": "_draw_finder_marker", "line": 110}
{"timestamp": "2025-06-12T17:33:59.243585", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Drawing square border:", "module": "qr_preview", "function": "_draw_finder_marker", "line": 160}
{"timestamp": "2025-06-12T17:33:59.243624", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "  Outer: insert=(20.0, 274.38596491228066), size=(35.614035087719294, 35.614035087719294)", "module": "qr_preview", "function": "_draw_finder_marker", "line": 161}
{"timestamp": "2025-06-12T17:33:59.243664", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "  Inner: insert=(26.105263157894736, 280.4912280701754), size=(23.403508771929822, 23.403508771929822)", "module": "qr_preview", "function": "_draw_finder_marker", "line": 162}
{"timestamp": "2025-06-12T17:33:59.243722", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Drawing marker center: style=square, color=#000000, pos=(32.719298245614034, 287.10526315789474), size=10.175438596491228", "module": "qr_preview", "function": "_draw_marker_center", "line": 179}
{"timestamp": "2025-06-12T17:33:59.243771", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Successfully added finder pattern group to SVG", "module": "qr_preview", "function": "_draw_finder_marker", "line": 171}
{"timestamp": "2025-06-12T17:33:59.243815", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Successfully drew finder pattern 3/3", "module": "qr_preview", "function": "generate_preview_svg_modular", "line": 782}
{"timestamp": "2025-06-12T17:33:59.270123", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Generated SVG length: 243784", "module": "qr_preview", "function": "generate_preview_svg_modular", "line": 802}
{"timestamp": "2025-06-12T17:33:59.270286", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "SVG contains marker-0-0: True", "module": "qr_preview", "function": "generate_preview_svg_modular", "line": 803}
{"timestamp": "2025-06-12T17:33:59.270441", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "SVG contains marker-18-0: False", "module": "qr_preview", "function": "generate_preview_svg_modular", "line": 804}
{"timestamp": "2025-06-12T17:33:59.270567", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "SVG contains marker-0-18: False", "module": "qr_preview", "function": "generate_preview_svg_modular", "line": 805}
{"timestamp": "2025-06-12T17:33:59.270953", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "SVG marker counts - outer: 3, inner: 3, dot: 0", "module": "qr_preview", "function": "generate_preview_svg_modular", "line": 813}
{"timestamp": "2025-06-12T17:33:59.270991", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "SVG label counts - text: 0, background: 0", "module": "qr_preview", "function": "generate_preview_svg_modular", "line": 814}
{"timestamp": "2025-06-12T17:33:59.273000", "level": "DEBUG", "logger": "backend.main", "message": "[Auth] Enforced Bearer token authentication for request", "module": "main", "function": "bearer_token_auth_middleware", "line": 274}
{"timestamp": "2025-06-12T17:34:01.184692", "level": "DEBUG", "logger": "backend.core.security.jwt_keys", "message": "Token contains key ID: 0bf271b1-2fbf-4d96-987f-e0b06b2fd890", "module": "jwt_keys", "function": "decode_token", "line": 188}
{"timestamp": "2025-06-12T17:34:01.185132", "level": "DEBUG", "logger": "backend.core.security.jwt_keys", "message": "Using key ID 0bf271b1-2fbf-4d96-987f-e0b06b2fd890 for verification", "module": "jwt_keys", "function": "decode_token", "line": 195}
{"timestamp": "2025-06-12T17:34:01.185733", "level": "DEBUG", "logger": "backend.services.auth", "message": "JWT decoded successfully with key manager", "module": "auth", "function": "authenticate_token", "line": 683}
{"timestamp": "2025-06-12T17:34:01.189961", "level": "DEBUG", "logger": "backend.services.db_utils", "message": "Query 'auth_lookup_e4ae2d8f-b32f-52d7-99cd-8a33b19652c7' completed in 0.004s", "module": "db_utils", "function": "execute_query_with_timeout", "line": 68}
{"timestamp": "2025-06-12T17:34:01.190179", "level": "DEBUG", "logger": "backend.services.db_utils", "message": "Auth query 'auth_lookup_e4ae2d8f-b32f-52d7-99cd-8a33b19652c7' successful, result format preserved", "module": "db_utils", "function": "execute_query_with_timeout", "line": 72}
{"timestamp": "2025-06-12T17:34:01.204315", "level": "DEBUG", "logger": "backend.services.auth", "message": "Authentication attempt with token prefix: eyJhbGciOi...", "module": "auth", "function": "get_current_user", "line": 727}
{"timestamp": "2025-06-12T17:34:01.204604", "level": "DEBUG", "logger": "backend.core.security.jwt_keys", "message": "Token contains key ID: 0bf271b1-2fbf-4d96-987f-e0b06b2fd890", "module": "jwt_keys", "function": "decode_token", "line": 188}
{"timestamp": "2025-06-12T17:34:01.204721", "level": "DEBUG", "logger": "backend.core.security.jwt_keys", "message": "Using key ID 0bf271b1-2fbf-4d96-987f-e0b06b2fd890 for verification", "module": "jwt_keys", "function": "decode_token", "line": 195}
{"timestamp": "2025-06-12T17:34:01.205026", "level": "DEBUG", "logger": "backend.services.auth", "message": "JWT decoded successfully with key manager", "module": "auth", "function": "authenticate_token", "line": 683}
{"timestamp": "2025-06-12T17:34:01.207810", "level": "DEBUG", "logger": "backend.services.db_utils", "message": "Query 'auth_lookup_e4ae2d8f-b32f-52d7-99cd-8a33b19652c7' completed in 0.002s", "module": "db_utils", "function": "execute_query_with_timeout", "line": 68}
{"timestamp": "2025-06-12T17:34:01.207964", "level": "DEBUG", "logger": "backend.services.db_utils", "message": "Auth query 'auth_lookup_e4ae2d8f-b32f-52d7-99cd-8a33b19652c7' successful, result format preserved", "module": "db_utils", "function": "execute_query_with_timeout", "line": 72}
{"timestamp": "2025-06-12T17:34:01.208098", "level": "DEBUG", "logger": "backend.services.auth", "message": "Successfully authenticated user: <EMAIL>", "module": "auth", "function": "get_current_user", "line": 732}
{"timestamp": "2025-06-12T17:34:01.208198", "level": "DEBUG", "logger": "backend.services.auth", "message": "Successfully authenticated user: <EMAIL>", "module": "auth", "function": "get_current_user", "line": 738}
{"timestamp": "2025-06-12T17:34:01.210894", "level": "DEBUG", "logger": "backend.routes.qr_preview", "message": "Received QR preview SVG request: {\"qr_type\": \"scooter_unlock\", \"data\": {\"scooter_id\": \"SCOOT123\", \"unlock_code\": \"UNLOCK42\", \"title\": \"Scooter_unlock QR Code\", \"description\": \"Scan to view scooter_unlock content\", \"service_provider\": \"Lime\", \"rate_info\": \"\"}, \"designOptions\": {\"size\": 256, \"margin\": 10, \"pattern\": \"square\", \"marker_border_style\": \"square\", \"marker_center_style\": \"square\", \"logo_size\": 0.3, \"logo_position\": \"center\", \"logo_opacity\": 1, \"logo_remove_background\": false, \"frame_style\": \"none\", \"frame_font\": \"SF Pro", "module": "qr_preview", "function": "preview_qr_svg", "line": 242}
{"timestamp": "2025-06-12T17:34:01.211169", "level": "DEBUG", "logger": "backend.routes.qr_preview", "message": "Form data keys: ['request']", "module": "qr_preview", "function": "preview_qr_svg", "line": 297}
{"timestamp": "2025-06-12T17:34:01.211264", "level": "DEBUG", "logger": "backend.routes.qr_preview", "message": "No logo key found in form data", "module": "qr_preview", "function": "preview_qr_svg", "line": 309}
{"timestamp": "2025-06-12T17:34:01.211370", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Formatting content for QR type: qrtype.scooter_unlock, content keys: ['scooter_id', 'unlock_code', 'title', 'description', 'service_provider', 'rate_info']", "module": "qr_preview", "function": "_format_qr_data", "line": 876}
{"timestamp": "2025-06-12T17:34:01.237613", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Logo remove background setting: False, has logo: False", "module": "qr_preview", "function": "generate_preview_svg_modular", "line": 727}
{"timestamp": "2025-06-12T17:34:01.255346", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Drawing 3 finder patterns at positions: [(0, 0), (50, 0), (0, 50)]", "module": "qr_preview", "function": "generate_preview_svg_modular", "line": 775}
{"timestamp": "2025-06-12T17:34:01.255401", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "QR size: 57, border_style: square, center_style: square", "module": "qr_preview", "function": "generate_preview_svg_modular", "line": 776}
{"timestamp": "2025-06-12T17:34:01.255442", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Drawing finder pattern 1/3 at position (0, 0)", "module": "qr_preview", "function": "generate_preview_svg_modular", "line": 780}
{"timestamp": "2025-06-12T17:34:01.255490", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "_draw_finder_marker called: mx=0, my=0, module_size=5.087719298245614, border_style=square, center_style=square, border_color=#000000, center_color=#000000, offset=(20, 20)", "module": "qr_preview", "function": "_draw_finder_marker", "line": 97}
{"timestamp": "2025-06-12T17:34:01.255545", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Creating finder pattern group with id='marker-0-0'", "module": "qr_preview", "function": "_draw_finder_marker", "line": 110}
{"timestamp": "2025-06-12T17:34:01.255587", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Drawing square border:", "module": "qr_preview", "function": "_draw_finder_marker", "line": 160}
{"timestamp": "2025-06-12T17:34:01.255623", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "  Outer: insert=(20.0, 20.0), size=(35.614035087719294, 35.614035087719294)", "module": "qr_preview", "function": "_draw_finder_marker", "line": 161}
{"timestamp": "2025-06-12T17:34:01.255657", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "  Inner: insert=(26.105263157894736, 26.105263157894736), size=(23.403508771929822, 23.403508771929822)", "module": "qr_preview", "function": "_draw_finder_marker", "line": 162}
{"timestamp": "2025-06-12T17:34:01.255720", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Drawing marker center: style=square, color=#000000, pos=(32.719298245614034, 32.719298245614034), size=10.175438596491228", "module": "qr_preview", "function": "_draw_marker_center", "line": 179}
{"timestamp": "2025-06-12T17:34:01.255766", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Successfully added finder pattern group to SVG", "module": "qr_preview", "function": "_draw_finder_marker", "line": 171}
{"timestamp": "2025-06-12T17:34:01.255802", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Successfully drew finder pattern 1/3", "module": "qr_preview", "function": "generate_preview_svg_modular", "line": 782}
{"timestamp": "2025-06-12T17:34:01.255864", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Drawing finder pattern 2/3 at position (50, 0)", "module": "qr_preview", "function": "generate_preview_svg_modular", "line": 780}
{"timestamp": "2025-06-12T17:34:01.255897", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "_draw_finder_marker called: mx=50, my=0, module_size=5.087719298245614, border_style=square, center_style=square, border_color=#000000, center_color=#000000, offset=(20, 20)", "module": "qr_preview", "function": "_draw_finder_marker", "line": 97}
{"timestamp": "2025-06-12T17:34:01.255935", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Creating finder pattern group with id='marker-50-0'", "module": "qr_preview", "function": "_draw_finder_marker", "line": 110}
{"timestamp": "2025-06-12T17:34:01.255966", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Drawing square border:", "module": "qr_preview", "function": "_draw_finder_marker", "line": 160}
{"timestamp": "2025-06-12T17:34:01.255997", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "  Outer: insert=(274.38596491228066, 20.0), size=(35.614035087719294, 35.614035087719294)", "module": "qr_preview", "function": "_draw_finder_marker", "line": 161}
{"timestamp": "2025-06-12T17:34:01.256029", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "  Inner: insert=(280.4912280701754, 26.105263157894736), size=(23.403508771929822, 23.403508771929822)", "module": "qr_preview", "function": "_draw_finder_marker", "line": 162}
{"timestamp": "2025-06-12T17:34:01.256086", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Drawing marker center: style=square, color=#000000, pos=(287.10526315789474, 32.719298245614034), size=10.175438596491228", "module": "qr_preview", "function": "_draw_marker_center", "line": 179}
{"timestamp": "2025-06-12T17:34:01.256133", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Successfully added finder pattern group to SVG", "module": "qr_preview", "function": "_draw_finder_marker", "line": 171}
{"timestamp": "2025-06-12T17:34:01.256163", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Successfully drew finder pattern 2/3", "module": "qr_preview", "function": "generate_preview_svg_modular", "line": 782}
{"timestamp": "2025-06-12T17:34:01.256193", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Drawing finder pattern 3/3 at position (0, 50)", "module": "qr_preview", "function": "generate_preview_svg_modular", "line": 780}
{"timestamp": "2025-06-12T17:34:01.256223", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "_draw_finder_marker called: mx=0, my=50, module_size=5.087719298245614, border_style=square, center_style=square, border_color=#000000, center_color=#000000, offset=(20, 20)", "module": "qr_preview", "function": "_draw_finder_marker", "line": 97}
{"timestamp": "2025-06-12T17:34:01.256259", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Creating finder pattern group with id='marker-0-50'", "module": "qr_preview", "function": "_draw_finder_marker", "line": 110}
{"timestamp": "2025-06-12T17:34:01.256289", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Drawing square border:", "module": "qr_preview", "function": "_draw_finder_marker", "line": 160}
{"timestamp": "2025-06-12T17:34:01.256320", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "  Outer: insert=(20.0, 274.38596491228066), size=(35.614035087719294, 35.614035087719294)", "module": "qr_preview", "function": "_draw_finder_marker", "line": 161}
{"timestamp": "2025-06-12T17:34:01.256352", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "  Inner: insert=(26.105263157894736, 280.4912280701754), size=(23.403508771929822, 23.403508771929822)", "module": "qr_preview", "function": "_draw_finder_marker", "line": 162}
{"timestamp": "2025-06-12T17:34:01.256421", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Drawing marker center: style=square, color=#000000, pos=(32.719298245614034, 287.10526315789474), size=10.175438596491228", "module": "qr_preview", "function": "_draw_marker_center", "line": 179}
{"timestamp": "2025-06-12T17:34:01.256462", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Successfully added finder pattern group to SVG", "module": "qr_preview", "function": "_draw_finder_marker", "line": 171}
{"timestamp": "2025-06-12T17:34:01.256490", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Successfully drew finder pattern 3/3", "module": "qr_preview", "function": "generate_preview_svg_modular", "line": 782}
{"timestamp": "2025-06-12T17:34:01.286421", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Generated SVG length: 244708", "module": "qr_preview", "function": "generate_preview_svg_modular", "line": 802}
{"timestamp": "2025-06-12T17:34:01.286594", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "SVG contains marker-0-0: True", "module": "qr_preview", "function": "generate_preview_svg_modular", "line": 803}
{"timestamp": "2025-06-12T17:34:01.286741", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "SVG contains marker-18-0: False", "module": "qr_preview", "function": "generate_preview_svg_modular", "line": 804}
{"timestamp": "2025-06-12T17:34:01.286878", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "SVG contains marker-0-18: False", "module": "qr_preview", "function": "generate_preview_svg_modular", "line": 805}
{"timestamp": "2025-06-12T17:34:01.287273", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "SVG marker counts - outer: 3, inner: 3, dot: 0", "module": "qr_preview", "function": "generate_preview_svg_modular", "line": 813}
{"timestamp": "2025-06-12T17:34:01.287313", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "SVG label counts - text: 0, background: 0", "module": "qr_preview", "function": "generate_preview_svg_modular", "line": 814}
{"timestamp": "2025-06-12T17:34:01.290265", "level": "DEBUG", "logger": "backend.main", "message": "[Auth] Enforced Bearer token authentication for request", "module": "main", "function": "bearer_token_auth_middleware", "line": 274}
{"timestamp": "2025-06-12T17:34:02.153494", "level": "DEBUG", "logger": "backend.core.security.jwt_keys", "message": "Token contains key ID: 0bf271b1-2fbf-4d96-987f-e0b06b2fd890", "module": "jwt_keys", "function": "decode_token", "line": 188}
{"timestamp": "2025-06-12T17:34:02.153554", "level": "DEBUG", "logger": "backend.core.security.jwt_keys", "message": "Using key ID 0bf271b1-2fbf-4d96-987f-e0b06b2fd890 for verification", "module": "jwt_keys", "function": "decode_token", "line": 195}
{"timestamp": "2025-06-12T17:34:02.153679", "level": "DEBUG", "logger": "backend.services.auth", "message": "JWT decoded successfully with key manager", "module": "auth", "function": "authenticate_token", "line": 683}
{"timestamp": "2025-06-12T17:34:02.155877", "level": "DEBUG", "logger": "backend.services.db_utils", "message": "Query 'auth_lookup_e4ae2d8f-b32f-52d7-99cd-8a33b19652c7' completed in 0.002s", "module": "db_utils", "function": "execute_query_with_timeout", "line": 68}
{"timestamp": "2025-06-12T17:34:02.155927", "level": "DEBUG", "logger": "backend.services.db_utils", "message": "Auth query 'auth_lookup_e4ae2d8f-b32f-52d7-99cd-8a33b19652c7' successful, result format preserved", "module": "db_utils", "function": "execute_query_with_timeout", "line": 72}
{"timestamp": "2025-06-12T17:34:02.161386", "level": "DEBUG", "logger": "backend.services.auth", "message": "Authentication attempt with token prefix: eyJhbGciOi...", "module": "auth", "function": "get_current_user", "line": 727}
{"timestamp": "2025-06-12T17:34:02.161490", "level": "DEBUG", "logger": "backend.core.security.jwt_keys", "message": "Token contains key ID: 0bf271b1-2fbf-4d96-987f-e0b06b2fd890", "module": "jwt_keys", "function": "decode_token", "line": 188}
{"timestamp": "2025-06-12T17:34:02.161559", "level": "DEBUG", "logger": "backend.core.security.jwt_keys", "message": "Using key ID 0bf271b1-2fbf-4d96-987f-e0b06b2fd890 for verification", "module": "jwt_keys", "function": "decode_token", "line": 195}
{"timestamp": "2025-06-12T17:34:02.161687", "level": "DEBUG", "logger": "backend.services.auth", "message": "JWT decoded successfully with key manager", "module": "auth", "function": "authenticate_token", "line": 683}
{"timestamp": "2025-06-12T17:34:02.165541", "level": "DEBUG", "logger": "backend.services.db_utils", "message": "Query 'auth_lookup_e4ae2d8f-b32f-52d7-99cd-8a33b19652c7' completed in 0.004s", "module": "db_utils", "function": "execute_query_with_timeout", "line": 68}
{"timestamp": "2025-06-12T17:34:02.165605", "level": "DEBUG", "logger": "backend.services.db_utils", "message": "Auth query 'auth_lookup_e4ae2d8f-b32f-52d7-99cd-8a33b19652c7' successful, result format preserved", "module": "db_utils", "function": "execute_query_with_timeout", "line": 72}
{"timestamp": "2025-06-12T17:34:02.165661", "level": "DEBUG", "logger": "backend.services.auth", "message": "Successfully authenticated user: <EMAIL>", "module": "auth", "function": "get_current_user", "line": 732}
{"timestamp": "2025-06-12T17:34:02.165696", "level": "DEBUG", "logger": "backend.services.auth", "message": "Successfully authenticated user: <EMAIL>", "module": "auth", "function": "get_current_user", "line": 738}
{"timestamp": "2025-06-12T17:34:02.167260", "level": "DEBUG", "logger": "backend.routes.qr_preview", "message": "Received QR preview SVG request: {\"qr_type\": \"scooter_unlock\", \"data\": {\"scooter_id\": \"SCOOT123\", \"unlock_code\": \"UNLOCK42\", \"title\": \"Scooter_unlock QR Code\", \"description\": \"Scan to view scooter_unlock content\", \"service_provider\": \"Lime\", \"rate_info\": \"\"}, \"designOptions\": {\"size\": 256, \"margin\": 10, \"pattern\": \"square\", \"marker_border_style\": \"square\", \"marker_border_color\": \"#000000\", \"marker_center_style\": \"square\", \"marker_center_color\": \"#000000\", \"logo_size\": 0.3, \"logo_position\": \"center\", \"logo_opacity\": 1, \"logo_rem", "module": "qr_preview", "function": "preview_qr_svg", "line": 242}
{"timestamp": "2025-06-12T17:34:02.167347", "level": "DEBUG", "logger": "backend.routes.qr_preview", "message": "Form data keys: ['request']", "module": "qr_preview", "function": "preview_qr_svg", "line": 297}
{"timestamp": "2025-06-12T17:34:02.167378", "level": "DEBUG", "logger": "backend.routes.qr_preview", "message": "No logo key found in form data", "module": "qr_preview", "function": "preview_qr_svg", "line": 309}
{"timestamp": "2025-06-12T17:34:02.167421", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Formatting content for QR type: qrtype.scooter_unlock, content keys: ['scooter_id', 'unlock_code', 'title', 'description', 'service_provider', 'rate_info']", "module": "qr_preview", "function": "_format_qr_data", "line": 876}
{"timestamp": "2025-06-12T17:34:02.190391", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Logo remove background setting: False, has logo: False", "module": "qr_preview", "function": "generate_preview_svg_modular", "line": 727}
{"timestamp": "2025-06-12T17:34:02.209214", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Drawing 3 finder patterns at positions: [(0, 0), (50, 0), (0, 50)]", "module": "qr_preview", "function": "generate_preview_svg_modular", "line": 775}
{"timestamp": "2025-06-12T17:34:02.209273", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "QR size: 57, border_style: square, center_style: square", "module": "qr_preview", "function": "generate_preview_svg_modular", "line": 776}
{"timestamp": "2025-06-12T17:34:02.209308", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Drawing finder pattern 1/3 at position (0, 0)", "module": "qr_preview", "function": "generate_preview_svg_modular", "line": 780}
{"timestamp": "2025-06-12T17:34:02.209379", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "_draw_finder_marker called: mx=0, my=0, module_size=5.087719298245614, border_style=square, center_style=square, border_color=#000000, center_color=#000000, offset=(20, 20)", "module": "qr_preview", "function": "_draw_finder_marker", "line": 97}
{"timestamp": "2025-06-12T17:34:02.209425", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Creating finder pattern group with id='marker-0-0'", "module": "qr_preview", "function": "_draw_finder_marker", "line": 110}
{"timestamp": "2025-06-12T17:34:02.209459", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Drawing square border:", "module": "qr_preview", "function": "_draw_finder_marker", "line": 160}
{"timestamp": "2025-06-12T17:34:02.209491", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "  Outer: insert=(20.0, 20.0), size=(35.614035087719294, 35.614035087719294)", "module": "qr_preview", "function": "_draw_finder_marker", "line": 161}
{"timestamp": "2025-06-12T17:34:02.209521", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "  Inner: insert=(26.105263157894736, 26.105263157894736), size=(23.403508771929822, 23.403508771929822)", "module": "qr_preview", "function": "_draw_finder_marker", "line": 162}
{"timestamp": "2025-06-12T17:34:02.209607", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Drawing marker center: style=square, color=#000000, pos=(32.719298245614034, 32.719298245614034), size=10.175438596491228", "module": "qr_preview", "function": "_draw_marker_center", "line": 179}
{"timestamp": "2025-06-12T17:34:02.209652", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Successfully added finder pattern group to SVG", "module": "qr_preview", "function": "_draw_finder_marker", "line": 171}
{"timestamp": "2025-06-12T17:34:02.209685", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Successfully drew finder pattern 1/3", "module": "qr_preview", "function": "generate_preview_svg_modular", "line": 782}
{"timestamp": "2025-06-12T17:34:02.209713", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Drawing finder pattern 2/3 at position (50, 0)", "module": "qr_preview", "function": "generate_preview_svg_modular", "line": 780}
{"timestamp": "2025-06-12T17:34:02.209743", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "_draw_finder_marker called: mx=50, my=0, module_size=5.087719298245614, border_style=square, center_style=square, border_color=#000000, center_color=#000000, offset=(20, 20)", "module": "qr_preview", "function": "_draw_finder_marker", "line": 97}
{"timestamp": "2025-06-12T17:34:02.209779", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Creating finder pattern group with id='marker-50-0'", "module": "qr_preview", "function": "_draw_finder_marker", "line": 110}
{"timestamp": "2025-06-12T17:34:02.209806", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Drawing square border:", "module": "qr_preview", "function": "_draw_finder_marker", "line": 160}
{"timestamp": "2025-06-12T17:34:02.209835", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "  Outer: insert=(274.38596491228066, 20.0), size=(35.614035087719294, 35.614035087719294)", "module": "qr_preview", "function": "_draw_finder_marker", "line": 161}
{"timestamp": "2025-06-12T17:34:02.209864", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "  Inner: insert=(280.4912280701754, 26.105263157894736), size=(23.403508771929822, 23.403508771929822)", "module": "qr_preview", "function": "_draw_finder_marker", "line": 162}
{"timestamp": "2025-06-12T17:34:02.209920", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Drawing marker center: style=square, color=#000000, pos=(287.10526315789474, 32.719298245614034), size=10.175438596491228", "module": "qr_preview", "function": "_draw_marker_center", "line": 179}
{"timestamp": "2025-06-12T17:34:02.209977", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Successfully added finder pattern group to SVG", "module": "qr_preview", "function": "_draw_finder_marker", "line": 171}
{"timestamp": "2025-06-12T17:34:02.210004", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Successfully drew finder pattern 2/3", "module": "qr_preview", "function": "generate_preview_svg_modular", "line": 782}
{"timestamp": "2025-06-12T17:34:02.210031", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Drawing finder pattern 3/3 at position (0, 50)", "module": "qr_preview", "function": "generate_preview_svg_modular", "line": 780}
{"timestamp": "2025-06-12T17:34:02.210059", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "_draw_finder_marker called: mx=0, my=50, module_size=5.087719298245614, border_style=square, center_style=square, border_color=#000000, center_color=#000000, offset=(20, 20)", "module": "qr_preview", "function": "_draw_finder_marker", "line": 97}
{"timestamp": "2025-06-12T17:34:02.210092", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Creating finder pattern group with id='marker-0-50'", "module": "qr_preview", "function": "_draw_finder_marker", "line": 110}
{"timestamp": "2025-06-12T17:34:02.210119", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Drawing square border:", "module": "qr_preview", "function": "_draw_finder_marker", "line": 160}
{"timestamp": "2025-06-12T17:34:02.210147", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "  Outer: insert=(20.0, 274.38596491228066), size=(35.614035087719294, 35.614035087719294)", "module": "qr_preview", "function": "_draw_finder_marker", "line": 161}
{"timestamp": "2025-06-12T17:34:02.210176", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "  Inner: insert=(26.105263157894736, 280.4912280701754), size=(23.403508771929822, 23.403508771929822)", "module": "qr_preview", "function": "_draw_finder_marker", "line": 162}
{"timestamp": "2025-06-12T17:34:02.210231", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Drawing marker center: style=square, color=#000000, pos=(32.719298245614034, 287.10526315789474), size=10.175438596491228", "module": "qr_preview", "function": "_draw_marker_center", "line": 179}
{"timestamp": "2025-06-12T17:34:02.210271", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Successfully added finder pattern group to SVG", "module": "qr_preview", "function": "_draw_finder_marker", "line": 171}
{"timestamp": "2025-06-12T17:34:02.210298", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Successfully drew finder pattern 3/3", "module": "qr_preview", "function": "generate_preview_svg_modular", "line": 782}
{"timestamp": "2025-06-12T17:34:02.237205", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Generated SVG length: 244708", "module": "qr_preview", "function": "generate_preview_svg_modular", "line": 802}
{"timestamp": "2025-06-12T17:34:02.237375", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "SVG contains marker-0-0: True", "module": "qr_preview", "function": "generate_preview_svg_modular", "line": 803}
{"timestamp": "2025-06-12T17:34:02.237505", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "SVG contains marker-18-0: False", "module": "qr_preview", "function": "generate_preview_svg_modular", "line": 804}
{"timestamp": "2025-06-12T17:34:02.237633", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "SVG contains marker-0-18: False", "module": "qr_preview", "function": "generate_preview_svg_modular", "line": 805}
{"timestamp": "2025-06-12T17:34:02.238016", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "SVG marker counts - outer: 3, inner: 3, dot: 0", "module": "qr_preview", "function": "generate_preview_svg_modular", "line": 813}
{"timestamp": "2025-06-12T17:34:02.238086", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "SVG label counts - text: 0, background: 0", "module": "qr_preview", "function": "generate_preview_svg_modular", "line": 814}
{"timestamp": "2025-06-12T17:34:02.240278", "level": "DEBUG", "logger": "backend.main", "message": "[Auth] Enforced Bearer token authentication for request", "module": "main", "function": "bearer_token_auth_middleware", "line": 274}
{"timestamp": "2025-06-12T17:34:02.460388", "level": "DEBUG", "logger": "backend.core.security.jwt_keys", "message": "Token contains key ID: 0bf271b1-2fbf-4d96-987f-e0b06b2fd890", "module": "jwt_keys", "function": "decode_token", "line": 188}
{"timestamp": "2025-06-12T17:34:02.460456", "level": "DEBUG", "logger": "backend.core.security.jwt_keys", "message": "Using key ID 0bf271b1-2fbf-4d96-987f-e0b06b2fd890 for verification", "module": "jwt_keys", "function": "decode_token", "line": 195}
{"timestamp": "2025-06-12T17:34:02.460589", "level": "DEBUG", "logger": "backend.services.auth", "message": "JWT decoded successfully with key manager", "module": "auth", "function": "authenticate_token", "line": 683}
{"timestamp": "2025-06-12T17:34:02.466831", "level": "DEBUG", "logger": "backend.services.db_utils", "message": "Query 'auth_lookup_e4ae2d8f-b32f-52d7-99cd-8a33b19652c7' completed in 0.006s", "module": "db_utils", "function": "execute_query_with_timeout", "line": 68}
{"timestamp": "2025-06-12T17:34:02.466909", "level": "DEBUG", "logger": "backend.services.db_utils", "message": "Auth query 'auth_lookup_e4ae2d8f-b32f-52d7-99cd-8a33b19652c7' successful, result format preserved", "module": "db_utils", "function": "execute_query_with_timeout", "line": 72}
{"timestamp": "2025-06-12T17:34:02.470267", "level": "DEBUG", "logger": "backend.services.auth", "message": "Authentication attempt with token prefix: eyJhbGciOi...", "module": "auth", "function": "get_current_user", "line": 727}
{"timestamp": "2025-06-12T17:34:02.470381", "level": "DEBUG", "logger": "backend.core.security.jwt_keys", "message": "Token contains key ID: 0bf271b1-2fbf-4d96-987f-e0b06b2fd890", "module": "jwt_keys", "function": "decode_token", "line": 188}
{"timestamp": "2025-06-12T17:34:02.470427", "level": "DEBUG", "logger": "backend.core.security.jwt_keys", "message": "Using key ID 0bf271b1-2fbf-4d96-987f-e0b06b2fd890 for verification", "module": "jwt_keys", "function": "decode_token", "line": 195}
{"timestamp": "2025-06-12T17:34:02.470545", "level": "DEBUG", "logger": "backend.services.auth", "message": "JWT decoded successfully with key manager", "module": "auth", "function": "authenticate_token", "line": 683}
{"timestamp": "2025-06-12T17:34:02.472618", "level": "DEBUG", "logger": "backend.services.db_utils", "message": "Query 'auth_lookup_e4ae2d8f-b32f-52d7-99cd-8a33b19652c7' completed in 0.002s", "module": "db_utils", "function": "execute_query_with_timeout", "line": 68}
{"timestamp": "2025-06-12T17:34:02.472664", "level": "DEBUG", "logger": "backend.services.db_utils", "message": "Auth query 'auth_lookup_e4ae2d8f-b32f-52d7-99cd-8a33b19652c7' successful, result format preserved", "module": "db_utils", "function": "execute_query_with_timeout", "line": 72}
{"timestamp": "2025-06-12T17:34:02.472715", "level": "DEBUG", "logger": "backend.services.auth", "message": "Successfully authenticated user: <EMAIL>", "module": "auth", "function": "get_current_user", "line": 732}
{"timestamp": "2025-06-12T17:34:02.472750", "level": "DEBUG", "logger": "backend.services.auth", "message": "Successfully authenticated user: <EMAIL>", "module": "auth", "function": "get_current_user", "line": 738}
{"timestamp": "2025-06-12T17:34:02.473783", "level": "DEBUG", "logger": "backend.routes.qr_preview", "message": "Received QR preview SVG request: {\"qr_type\": \"scooter_unlock\", \"data\": {\"scooter_id\": \"SCOOT123\", \"unlock_code\": \"UNLOCK42\", \"title\": \"Scooter_unlock QR Code\", \"description\": \"Scan to view scooter_unlock content\", \"service_provider\": \"Lime\", \"rate_info\": \"$\"}, \"designOptions\": {\"size\": 256, \"margin\": 10, \"pattern\": \"square\", \"marker_border_style\": \"square\", \"marker_center_style\": \"square\", \"logo_size\": 0.3, \"logo_position\": \"center\", \"logo_opacity\": 1, \"logo_remove_background\": false, \"frame_style\": \"none\", \"frame_font\": \"SF Pr", "module": "qr_preview", "function": "preview_qr_svg", "line": 242}
{"timestamp": "2025-06-12T17:34:02.473903", "level": "DEBUG", "logger": "backend.routes.qr_preview", "message": "Form data keys: ['request']", "module": "qr_preview", "function": "preview_qr_svg", "line": 297}
{"timestamp": "2025-06-12T17:34:02.473942", "level": "DEBUG", "logger": "backend.routes.qr_preview", "message": "No logo key found in form data", "module": "qr_preview", "function": "preview_qr_svg", "line": 309}
{"timestamp": "2025-06-12T17:34:02.473986", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Formatting content for QR type: qrtype.scooter_unlock, content keys: ['scooter_id', 'unlock_code', 'title', 'description', 'service_provider', 'rate_info']", "module": "qr_preview", "function": "_format_qr_data", "line": 876}
{"timestamp": "2025-06-12T17:34:02.496138", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Logo remove background setting: False, has logo: False", "module": "qr_preview", "function": "generate_preview_svg_modular", "line": 727}
{"timestamp": "2025-06-12T17:34:02.513495", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Drawing 3 finder patterns at positions: [(0, 0), (50, 0), (0, 50)]", "module": "qr_preview", "function": "generate_preview_svg_modular", "line": 775}
{"timestamp": "2025-06-12T17:34:02.513536", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "QR size: 57, border_style: square, center_style: square", "module": "qr_preview", "function": "generate_preview_svg_modular", "line": 776}
{"timestamp": "2025-06-12T17:34:02.513570", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Drawing finder pattern 1/3 at position (0, 0)", "module": "qr_preview", "function": "generate_preview_svg_modular", "line": 780}
{"timestamp": "2025-06-12T17:34:02.513610", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "_draw_finder_marker called: mx=0, my=0, module_size=5.087719298245614, border_style=square, center_style=square, border_color=#000000, center_color=#000000, offset=(20, 20)", "module": "qr_preview", "function": "_draw_finder_marker", "line": 97}
{"timestamp": "2025-06-12T17:34:02.513656", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Creating finder pattern group with id='marker-0-0'", "module": "qr_preview", "function": "_draw_finder_marker", "line": 110}
{"timestamp": "2025-06-12T17:34:02.513690", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Drawing square border:", "module": "qr_preview", "function": "_draw_finder_marker", "line": 160}
{"timestamp": "2025-06-12T17:34:02.513722", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "  Outer: insert=(20.0, 20.0), size=(35.614035087719294, 35.614035087719294)", "module": "qr_preview", "function": "_draw_finder_marker", "line": 161}
{"timestamp": "2025-06-12T17:34:02.513756", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "  Inner: insert=(26.105263157894736, 26.105263157894736), size=(23.403508771929822, 23.403508771929822)", "module": "qr_preview", "function": "_draw_finder_marker", "line": 162}
{"timestamp": "2025-06-12T17:34:02.513819", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Drawing marker center: style=square, color=#000000, pos=(32.719298245614034, 32.719298245614034), size=10.175438596491228", "module": "qr_preview", "function": "_draw_marker_center", "line": 179}
{"timestamp": "2025-06-12T17:34:02.513867", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Successfully added finder pattern group to SVG", "module": "qr_preview", "function": "_draw_finder_marker", "line": 171}
{"timestamp": "2025-06-12T17:34:02.513926", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Successfully drew finder pattern 1/3", "module": "qr_preview", "function": "generate_preview_svg_modular", "line": 782}
{"timestamp": "2025-06-12T17:34:02.513956", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Drawing finder pattern 2/3 at position (50, 0)", "module": "qr_preview", "function": "generate_preview_svg_modular", "line": 780}
{"timestamp": "2025-06-12T17:34:02.513988", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "_draw_finder_marker called: mx=50, my=0, module_size=5.087719298245614, border_style=square, center_style=square, border_color=#000000, center_color=#000000, offset=(20, 20)", "module": "qr_preview", "function": "_draw_finder_marker", "line": 97}
{"timestamp": "2025-06-12T17:34:02.514026", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Creating finder pattern group with id='marker-50-0'", "module": "qr_preview", "function": "_draw_finder_marker", "line": 110}
{"timestamp": "2025-06-12T17:34:02.514056", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Drawing square border:", "module": "qr_preview", "function": "_draw_finder_marker", "line": 160}
{"timestamp": "2025-06-12T17:34:02.514088", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "  Outer: insert=(274.38596491228066, 20.0), size=(35.614035087719294, 35.614035087719294)", "module": "qr_preview", "function": "_draw_finder_marker", "line": 161}
{"timestamp": "2025-06-12T17:34:02.514120", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "  Inner: insert=(280.4912280701754, 26.105263157894736), size=(23.403508771929822, 23.403508771929822)", "module": "qr_preview", "function": "_draw_finder_marker", "line": 162}
{"timestamp": "2025-06-12T17:34:02.514176", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Drawing marker center: style=square, color=#000000, pos=(287.10526315789474, 32.719298245614034), size=10.175438596491228", "module": "qr_preview", "function": "_draw_marker_center", "line": 179}
{"timestamp": "2025-06-12T17:34:02.514222", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Successfully added finder pattern group to SVG", "module": "qr_preview", "function": "_draw_finder_marker", "line": 171}
{"timestamp": "2025-06-12T17:34:02.514253", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Successfully drew finder pattern 2/3", "module": "qr_preview", "function": "generate_preview_svg_modular", "line": 782}
{"timestamp": "2025-06-12T17:34:02.514284", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Drawing finder pattern 3/3 at position (0, 50)", "module": "qr_preview", "function": "generate_preview_svg_modular", "line": 780}
{"timestamp": "2025-06-12T17:34:02.514315", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "_draw_finder_marker called: mx=0, my=50, module_size=5.087719298245614, border_style=square, center_style=square, border_color=#000000, center_color=#000000, offset=(20, 20)", "module": "qr_preview", "function": "_draw_finder_marker", "line": 97}
{"timestamp": "2025-06-12T17:34:02.514351", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Creating finder pattern group with id='marker-0-50'", "module": "qr_preview", "function": "_draw_finder_marker", "line": 110}
{"timestamp": "2025-06-12T17:34:02.514381", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Drawing square border:", "module": "qr_preview", "function": "_draw_finder_marker", "line": 160}
{"timestamp": "2025-06-12T17:34:02.514413", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "  Outer: insert=(20.0, 274.38596491228066), size=(35.614035087719294, 35.614035087719294)", "module": "qr_preview", "function": "_draw_finder_marker", "line": 161}
{"timestamp": "2025-06-12T17:34:02.514444", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "  Inner: insert=(26.105263157894736, 280.4912280701754), size=(23.403508771929822, 23.403508771929822)", "module": "qr_preview", "function": "_draw_finder_marker", "line": 162}
{"timestamp": "2025-06-12T17:34:02.514513", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Drawing marker center: style=square, color=#000000, pos=(32.719298245614034, 287.10526315789474), size=10.175438596491228", "module": "qr_preview", "function": "_draw_marker_center", "line": 179}
{"timestamp": "2025-06-12T17:34:02.514556", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Successfully added finder pattern group to SVG", "module": "qr_preview", "function": "_draw_finder_marker", "line": 171}
{"timestamp": "2025-06-12T17:34:02.514586", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Successfully drew finder pattern 3/3", "module": "qr_preview", "function": "generate_preview_svg_modular", "line": 782}
{"timestamp": "2025-06-12T17:34:02.539489", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Generated SVG length: 243761", "module": "qr_preview", "function": "generate_preview_svg_modular", "line": 802}
{"timestamp": "2025-06-12T17:34:02.539638", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "SVG contains marker-0-0: True", "module": "qr_preview", "function": "generate_preview_svg_modular", "line": 803}
{"timestamp": "2025-06-12T17:34:02.539765", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "SVG contains marker-18-0: False", "module": "qr_preview", "function": "generate_preview_svg_modular", "line": 804}
{"timestamp": "2025-06-12T17:34:02.539893", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "SVG contains marker-0-18: False", "module": "qr_preview", "function": "generate_preview_svg_modular", "line": 805}
{"timestamp": "2025-06-12T17:34:02.540275", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "SVG marker counts - outer: 3, inner: 3, dot: 0", "module": "qr_preview", "function": "generate_preview_svg_modular", "line": 813}
{"timestamp": "2025-06-12T17:34:02.540306", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "SVG label counts - text: 0, background: 0", "module": "qr_preview", "function": "generate_preview_svg_modular", "line": 814}
{"timestamp": "2025-06-12T17:34:02.542291", "level": "DEBUG", "logger": "backend.main", "message": "[Auth] Enforced Bearer token authentication for request", "module": "main", "function": "bearer_token_auth_middleware", "line": 274}
{"timestamp": "2025-06-12T17:34:02.917148", "level": "DEBUG", "logger": "backend.core.security.jwt_keys", "message": "Token contains key ID: 0bf271b1-2fbf-4d96-987f-e0b06b2fd890", "module": "jwt_keys", "function": "decode_token", "line": 188}
{"timestamp": "2025-06-12T17:34:02.917216", "level": "DEBUG", "logger": "backend.core.security.jwt_keys", "message": "Using key ID 0bf271b1-2fbf-4d96-987f-e0b06b2fd890 for verification", "module": "jwt_keys", "function": "decode_token", "line": 195}
{"timestamp": "2025-06-12T17:34:02.917354", "level": "DEBUG", "logger": "backend.services.auth", "message": "JWT decoded successfully with key manager", "module": "auth", "function": "authenticate_token", "line": 683}
{"timestamp": "2025-06-12T17:34:02.920927", "level": "DEBUG", "logger": "backend.services.db_utils", "message": "Query 'auth_lookup_e4ae2d8f-b32f-52d7-99cd-8a33b19652c7' completed in 0.003s", "module": "db_utils", "function": "execute_query_with_timeout", "line": 68}
{"timestamp": "2025-06-12T17:34:02.920995", "level": "DEBUG", "logger": "backend.services.db_utils", "message": "Auth query 'auth_lookup_e4ae2d8f-b32f-52d7-99cd-8a33b19652c7' successful, result format preserved", "module": "db_utils", "function": "execute_query_with_timeout", "line": 72}
{"timestamp": "2025-06-12T17:34:02.924898", "level": "DEBUG", "logger": "backend.services.auth", "message": "Authentication attempt with token prefix: eyJhbGciOi...", "module": "auth", "function": "get_current_user", "line": 727}
{"timestamp": "2025-06-12T17:34:02.925017", "level": "DEBUG", "logger": "backend.core.security.jwt_keys", "message": "Token contains key ID: 0bf271b1-2fbf-4d96-987f-e0b06b2fd890", "module": "jwt_keys", "function": "decode_token", "line": 188}
{"timestamp": "2025-06-12T17:34:02.925054", "level": "DEBUG", "logger": "backend.core.security.jwt_keys", "message": "Using key ID 0bf271b1-2fbf-4d96-987f-e0b06b2fd890 for verification", "module": "jwt_keys", "function": "decode_token", "line": 195}
{"timestamp": "2025-06-12T17:34:02.925153", "level": "DEBUG", "logger": "backend.services.auth", "message": "JWT decoded successfully with key manager", "module": "auth", "function": "authenticate_token", "line": 683}
{"timestamp": "2025-06-12T17:34:02.926779", "level": "DEBUG", "logger": "backend.services.db_utils", "message": "Query 'auth_lookup_e4ae2d8f-b32f-52d7-99cd-8a33b19652c7' completed in 0.001s", "module": "db_utils", "function": "execute_query_with_timeout", "line": 68}
{"timestamp": "2025-06-12T17:34:02.926833", "level": "DEBUG", "logger": "backend.services.db_utils", "message": "Auth query 'auth_lookup_e4ae2d8f-b32f-52d7-99cd-8a33b19652c7' successful, result format preserved", "module": "db_utils", "function": "execute_query_with_timeout", "line": 72}
{"timestamp": "2025-06-12T17:34:02.926894", "level": "DEBUG", "logger": "backend.services.auth", "message": "Successfully authenticated user: <EMAIL>", "module": "auth", "function": "get_current_user", "line": 732}
{"timestamp": "2025-06-12T17:34:02.926937", "level": "DEBUG", "logger": "backend.services.auth", "message": "Successfully authenticated user: <EMAIL>", "module": "auth", "function": "get_current_user", "line": 738}
{"timestamp": "2025-06-12T17:34:02.927948", "level": "DEBUG", "logger": "backend.routes.qr_preview", "message": "Received QR preview SVG request: {\"qr_type\": \"scooter_unlock\", \"data\": {\"scooter_id\": \"SCOOT123\", \"unlock_code\": \"UNLOCK42\", \"title\": \"Scooter_unlock QR Code\", \"description\": \"Scan to view scooter_unlock content\", \"service_provider\": \"Lime\", \"rate_info\": \"$2\"}, \"designOptions\": {\"size\": 256, \"margin\": 10, \"pattern\": \"square\", \"marker_border_style\": \"square\", \"marker_center_style\": \"square\", \"logo_size\": 0.3, \"logo_position\": \"center\", \"logo_opacity\": 1, \"logo_remove_background\": false, \"frame_style\": \"none\", \"frame_font\": \"SF P", "module": "qr_preview", "function": "preview_qr_svg", "line": 242}
{"timestamp": "2025-06-12T17:34:02.928021", "level": "DEBUG", "logger": "backend.routes.qr_preview", "message": "Form data keys: ['request']", "module": "qr_preview", "function": "preview_qr_svg", "line": 297}
{"timestamp": "2025-06-12T17:34:02.928063", "level": "DEBUG", "logger": "backend.routes.qr_preview", "message": "No logo key found in form data", "module": "qr_preview", "function": "preview_qr_svg", "line": 309}
{"timestamp": "2025-06-12T17:34:02.928109", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Formatting content for QR type: qrtype.scooter_unlock, content keys: ['scooter_id', 'unlock_code', 'title', 'description', 'service_provider', 'rate_info']", "module": "qr_preview", "function": "_format_qr_data", "line": 876}
{"timestamp": "2025-06-12T17:34:02.951066", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Logo remove background setting: False, has logo: False", "module": "qr_preview", "function": "generate_preview_svg_modular", "line": 727}
{"timestamp": "2025-06-12T17:34:02.972391", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Drawing 3 finder patterns at positions: [(0, 0), (50, 0), (0, 50)]", "module": "qr_preview", "function": "generate_preview_svg_modular", "line": 775}
{"timestamp": "2025-06-12T17:34:02.972450", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "QR size: 57, border_style: square, center_style: square", "module": "qr_preview", "function": "generate_preview_svg_modular", "line": 776}
{"timestamp": "2025-06-12T17:34:02.972978", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Drawing finder pattern 1/3 at position (0, 0)", "module": "qr_preview", "function": "generate_preview_svg_modular", "line": 780}
{"timestamp": "2025-06-12T17:34:02.973031", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "_draw_finder_marker called: mx=0, my=0, module_size=5.087719298245614, border_style=square, center_style=square, border_color=#000000, center_color=#000000, offset=(20, 20)", "module": "qr_preview", "function": "_draw_finder_marker", "line": 97}
{"timestamp": "2025-06-12T17:34:02.973089", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Creating finder pattern group with id='marker-0-0'", "module": "qr_preview", "function": "_draw_finder_marker", "line": 110}
{"timestamp": "2025-06-12T17:34:02.973125", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Drawing square border:", "module": "qr_preview", "function": "_draw_finder_marker", "line": 160}
{"timestamp": "2025-06-12T17:34:02.973161", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "  Outer: insert=(20.0, 20.0), size=(35.614035087719294, 35.614035087719294)", "module": "qr_preview", "function": "_draw_finder_marker", "line": 161}
{"timestamp": "2025-06-12T17:34:02.973194", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "  Inner: insert=(26.105263157894736, 26.105263157894736), size=(23.403508771929822, 23.403508771929822)", "module": "qr_preview", "function": "_draw_finder_marker", "line": 162}
{"timestamp": "2025-06-12T17:34:02.973268", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Drawing marker center: style=square, color=#000000, pos=(32.719298245614034, 32.719298245614034), size=10.175438596491228", "module": "qr_preview", "function": "_draw_marker_center", "line": 179}
{"timestamp": "2025-06-12T17:34:02.973319", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Successfully added finder pattern group to SVG", "module": "qr_preview", "function": "_draw_finder_marker", "line": 171}
{"timestamp": "2025-06-12T17:34:02.973353", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Successfully drew finder pattern 1/3", "module": "qr_preview", "function": "generate_preview_svg_modular", "line": 782}
{"timestamp": "2025-06-12T17:34:02.973386", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Drawing finder pattern 2/3 at position (50, 0)", "module": "qr_preview", "function": "generate_preview_svg_modular", "line": 780}
{"timestamp": "2025-06-12T17:34:02.973416", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "_draw_finder_marker called: mx=50, my=0, module_size=5.087719298245614, border_style=square, center_style=square, border_color=#000000, center_color=#000000, offset=(20, 20)", "module": "qr_preview", "function": "_draw_finder_marker", "line": 97}
{"timestamp": "2025-06-12T17:34:02.973535", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Creating finder pattern group with id='marker-50-0'", "module": "qr_preview", "function": "_draw_finder_marker", "line": 110}
{"timestamp": "2025-06-12T17:34:02.973592", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Drawing square border:", "module": "qr_preview", "function": "_draw_finder_marker", "line": 160}
{"timestamp": "2025-06-12T17:34:02.973634", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "  Outer: insert=(274.38596491228066, 20.0), size=(35.614035087719294, 35.614035087719294)", "module": "qr_preview", "function": "_draw_finder_marker", "line": 161}
{"timestamp": "2025-06-12T17:34:02.973678", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "  Inner: insert=(280.4912280701754, 26.105263157894736), size=(23.403508771929822, 23.403508771929822)", "module": "qr_preview", "function": "_draw_finder_marker", "line": 162}
{"timestamp": "2025-06-12T17:34:02.973759", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Drawing marker center: style=square, color=#000000, pos=(287.10526315789474, 32.719298245614034), size=10.175438596491228", "module": "qr_preview", "function": "_draw_marker_center", "line": 179}
{"timestamp": "2025-06-12T17:34:02.973832", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Successfully added finder pattern group to SVG", "module": "qr_preview", "function": "_draw_finder_marker", "line": 171}
{"timestamp": "2025-06-12T17:34:02.973869", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Successfully drew finder pattern 2/3", "module": "qr_preview", "function": "generate_preview_svg_modular", "line": 782}
{"timestamp": "2025-06-12T17:34:02.973903", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Drawing finder pattern 3/3 at position (0, 50)", "module": "qr_preview", "function": "generate_preview_svg_modular", "line": 780}
{"timestamp": "2025-06-12T17:34:02.974041", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "_draw_finder_marker called: mx=0, my=50, module_size=5.087719298245614, border_style=square, center_style=square, border_color=#000000, center_color=#000000, offset=(20, 20)", "module": "qr_preview", "function": "_draw_finder_marker", "line": 97}
{"timestamp": "2025-06-12T17:34:02.974096", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Creating finder pattern group with id='marker-0-50'", "module": "qr_preview", "function": "_draw_finder_marker", "line": 110}
{"timestamp": "2025-06-12T17:34:02.974134", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Drawing square border:", "module": "qr_preview", "function": "_draw_finder_marker", "line": 160}
{"timestamp": "2025-06-12T17:34:02.974170", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "  Outer: insert=(20.0, 274.38596491228066), size=(35.614035087719294, 35.614035087719294)", "module": "qr_preview", "function": "_draw_finder_marker", "line": 161}
{"timestamp": "2025-06-12T17:34:02.974205", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "  Inner: insert=(26.105263157894736, 280.4912280701754), size=(23.403508771929822, 23.403508771929822)", "module": "qr_preview", "function": "_draw_finder_marker", "line": 162}
{"timestamp": "2025-06-12T17:34:02.974269", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Drawing marker center: style=square, color=#000000, pos=(32.719298245614034, 287.10526315789474), size=10.175438596491228", "module": "qr_preview", "function": "_draw_marker_center", "line": 179}
{"timestamp": "2025-06-12T17:34:02.974320", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Successfully added finder pattern group to SVG", "module": "qr_preview", "function": "_draw_finder_marker", "line": 171}
{"timestamp": "2025-06-12T17:34:02.974351", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Successfully drew finder pattern 3/3", "module": "qr_preview", "function": "generate_preview_svg_modular", "line": 782}
{"timestamp": "2025-06-12T17:34:03.001333", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Generated SVG length: 249802", "module": "qr_preview", "function": "generate_preview_svg_modular", "line": 802}
{"timestamp": "2025-06-12T17:34:03.001491", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "SVG contains marker-0-0: True", "module": "qr_preview", "function": "generate_preview_svg_modular", "line": 803}
{"timestamp": "2025-06-12T17:34:03.001623", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "SVG contains marker-18-0: False", "module": "qr_preview", "function": "generate_preview_svg_modular", "line": 804}
{"timestamp": "2025-06-12T17:34:03.001756", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "SVG contains marker-0-18: False", "module": "qr_preview", "function": "generate_preview_svg_modular", "line": 805}
{"timestamp": "2025-06-12T17:34:03.002149", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "SVG marker counts - outer: 3, inner: 3, dot: 0", "module": "qr_preview", "function": "generate_preview_svg_modular", "line": 813}
{"timestamp": "2025-06-12T17:34:03.002223", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "SVG label counts - text: 0, background: 0", "module": "qr_preview", "function": "generate_preview_svg_modular", "line": 814}
{"timestamp": "2025-06-12T17:34:03.004323", "level": "DEBUG", "logger": "backend.main", "message": "[Auth] Enforced Bearer token authentication for request", "module": "main", "function": "bearer_token_auth_middleware", "line": 274}
{"timestamp": "2025-06-12T17:34:03.225428", "level": "DEBUG", "logger": "backend.core.security.jwt_keys", "message": "Token contains key ID: 0bf271b1-2fbf-4d96-987f-e0b06b2fd890", "module": "jwt_keys", "function": "decode_token", "line": 188}
{"timestamp": "2025-06-12T17:34:03.225492", "level": "DEBUG", "logger": "backend.core.security.jwt_keys", "message": "Using key ID 0bf271b1-2fbf-4d96-987f-e0b06b2fd890 for verification", "module": "jwt_keys", "function": "decode_token", "line": 195}
{"timestamp": "2025-06-12T17:34:03.225620", "level": "DEBUG", "logger": "backend.services.auth", "message": "JWT decoded successfully with key manager", "module": "auth", "function": "authenticate_token", "line": 683}
{"timestamp": "2025-06-12T17:34:03.227261", "level": "DEBUG", "logger": "backend.services.db_utils", "message": "Query 'auth_lookup_e4ae2d8f-b32f-52d7-99cd-8a33b19652c7' completed in 0.001s", "module": "db_utils", "function": "execute_query_with_timeout", "line": 68}
{"timestamp": "2025-06-12T17:34:03.227309", "level": "DEBUG", "logger": "backend.services.db_utils", "message": "Auth query 'auth_lookup_e4ae2d8f-b32f-52d7-99cd-8a33b19652c7' successful, result format preserved", "module": "db_utils", "function": "execute_query_with_timeout", "line": 72}
{"timestamp": "2025-06-12T17:34:03.234994", "level": "DEBUG", "logger": "backend.services.auth", "message": "Authentication attempt with token prefix: eyJhbGciOi...", "module": "auth", "function": "get_current_user", "line": 727}
{"timestamp": "2025-06-12T17:34:03.235096", "level": "DEBUG", "logger": "backend.core.security.jwt_keys", "message": "Token contains key ID: 0bf271b1-2fbf-4d96-987f-e0b06b2fd890", "module": "jwt_keys", "function": "decode_token", "line": 188}
{"timestamp": "2025-06-12T17:34:03.235135", "level": "DEBUG", "logger": "backend.core.security.jwt_keys", "message": "Using key ID 0bf271b1-2fbf-4d96-987f-e0b06b2fd890 for verification", "module": "jwt_keys", "function": "decode_token", "line": 195}
{"timestamp": "2025-06-12T17:34:03.235232", "level": "DEBUG", "logger": "backend.services.auth", "message": "JWT decoded successfully with key manager", "module": "auth", "function": "authenticate_token", "line": 683}
{"timestamp": "2025-06-12T17:34:03.236426", "level": "DEBUG", "logger": "backend.services.db_utils", "message": "Query 'auth_lookup_e4ae2d8f-b32f-52d7-99cd-8a33b19652c7' completed in 0.001s", "module": "db_utils", "function": "execute_query_with_timeout", "line": 68}
{"timestamp": "2025-06-12T17:34:03.236474", "level": "DEBUG", "logger": "backend.services.db_utils", "message": "Auth query 'auth_lookup_e4ae2d8f-b32f-52d7-99cd-8a33b19652c7' successful, result format preserved", "module": "db_utils", "function": "execute_query_with_timeout", "line": 72}
{"timestamp": "2025-06-12T17:34:03.236529", "level": "DEBUG", "logger": "backend.services.auth", "message": "Successfully authenticated user: <EMAIL>", "module": "auth", "function": "get_current_user", "line": 732}
{"timestamp": "2025-06-12T17:34:03.236566", "level": "DEBUG", "logger": "backend.services.auth", "message": "Successfully authenticated user: <EMAIL>", "module": "auth", "function": "get_current_user", "line": 738}
{"timestamp": "2025-06-12T17:34:03.237601", "level": "DEBUG", "logger": "backend.routes.qr_preview", "message": "Received QR preview SVG request: {\"qr_type\": \"scooter_unlock\", \"data\": {\"scooter_id\": \"SCOOT123\", \"unlock_code\": \"UNLOCK42\", \"title\": \"Scooter_unlock QR Code\", \"description\": \"Scan to view scooter_unlock content\", \"service_provider\": \"Lime\", \"rate_info\": \"$20\"}, \"designOptions\": {\"size\": 256, \"margin\": 10, \"pattern\": \"square\", \"marker_border_style\": \"square\", \"marker_center_style\": \"square\", \"logo_size\": 0.3, \"logo_position\": \"center\", \"logo_opacity\": 1, \"logo_remove_background\": false, \"frame_style\": \"none\", \"frame_font\": \"SF ", "module": "qr_preview", "function": "preview_qr_svg", "line": 242}
{"timestamp": "2025-06-12T17:34:03.237699", "level": "DEBUG", "logger": "backend.routes.qr_preview", "message": "Form data keys: ['request']", "module": "qr_preview", "function": "preview_qr_svg", "line": 297}
{"timestamp": "2025-06-12T17:34:03.237733", "level": "DEBUG", "logger": "backend.routes.qr_preview", "message": "No logo key found in form data", "module": "qr_preview", "function": "preview_qr_svg", "line": 309}
{"timestamp": "2025-06-12T17:34:03.237770", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Formatting content for QR type: qrtype.scooter_unlock, content keys: ['scooter_id', 'unlock_code', 'title', 'description', 'service_provider', 'rate_info']", "module": "qr_preview", "function": "_format_qr_data", "line": 876}
{"timestamp": "2025-06-12T17:34:03.260902", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Logo remove background setting: False, has logo: False", "module": "qr_preview", "function": "generate_preview_svg_modular", "line": 727}
{"timestamp": "2025-06-12T17:34:03.279588", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Drawing 3 finder patterns at positions: [(0, 0), (50, 0), (0, 50)]", "module": "qr_preview", "function": "generate_preview_svg_modular", "line": 775}
{"timestamp": "2025-06-12T17:34:03.279656", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "QR size: 57, border_style: square, center_style: square", "module": "qr_preview", "function": "generate_preview_svg_modular", "line": 776}
{"timestamp": "2025-06-12T17:34:03.279736", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Drawing finder pattern 1/3 at position (0, 0)", "module": "qr_preview", "function": "generate_preview_svg_modular", "line": 780}
{"timestamp": "2025-06-12T17:34:03.279797", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "_draw_finder_marker called: mx=0, my=0, module_size=5.087719298245614, border_style=square, center_style=square, border_color=#000000, center_color=#000000, offset=(20, 20)", "module": "qr_preview", "function": "_draw_finder_marker", "line": 97}
{"timestamp": "2025-06-12T17:34:03.279859", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Creating finder pattern group with id='marker-0-0'", "module": "qr_preview", "function": "_draw_finder_marker", "line": 110}
{"timestamp": "2025-06-12T17:34:03.279901", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Drawing square border:", "module": "qr_preview", "function": "_draw_finder_marker", "line": 160}
{"timestamp": "2025-06-12T17:34:03.279945", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "  Outer: insert=(20.0, 20.0), size=(35.614035087719294, 35.614035087719294)", "module": "qr_preview", "function": "_draw_finder_marker", "line": 161}
{"timestamp": "2025-06-12T17:34:03.279995", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "  Inner: insert=(26.105263157894736, 26.105263157894736), size=(23.403508771929822, 23.403508771929822)", "module": "qr_preview", "function": "_draw_finder_marker", "line": 162}
{"timestamp": "2025-06-12T17:34:03.280077", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Drawing marker center: style=square, color=#000000, pos=(32.719298245614034, 32.719298245614034), size=10.175438596491228", "module": "qr_preview", "function": "_draw_marker_center", "line": 179}
{"timestamp": "2025-06-12T17:34:03.280133", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Successfully added finder pattern group to SVG", "module": "qr_preview", "function": "_draw_finder_marker", "line": 171}
{"timestamp": "2025-06-12T17:34:03.280210", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Successfully drew finder pattern 1/3", "module": "qr_preview", "function": "generate_preview_svg_modular", "line": 782}
{"timestamp": "2025-06-12T17:34:03.280248", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Drawing finder pattern 2/3 at position (50, 0)", "module": "qr_preview", "function": "generate_preview_svg_modular", "line": 780}
{"timestamp": "2025-06-12T17:34:03.280286", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "_draw_finder_marker called: mx=50, my=0, module_size=5.087719298245614, border_style=square, center_style=square, border_color=#000000, center_color=#000000, offset=(20, 20)", "module": "qr_preview", "function": "_draw_finder_marker", "line": 97}
{"timestamp": "2025-06-12T17:34:03.280330", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Creating finder pattern group with id='marker-50-0'", "module": "qr_preview", "function": "_draw_finder_marker", "line": 110}
{"timestamp": "2025-06-12T17:34:03.280366", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Drawing square border:", "module": "qr_preview", "function": "_draw_finder_marker", "line": 160}
{"timestamp": "2025-06-12T17:34:03.280403", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "  Outer: insert=(274.38596491228066, 20.0), size=(35.614035087719294, 35.614035087719294)", "module": "qr_preview", "function": "_draw_finder_marker", "line": 161}
{"timestamp": "2025-06-12T17:34:03.280439", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "  Inner: insert=(280.4912280701754, 26.105263157894736), size=(23.403508771929822, 23.403508771929822)", "module": "qr_preview", "function": "_draw_finder_marker", "line": 162}
{"timestamp": "2025-06-12T17:34:03.280502", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Drawing marker center: style=square, color=#000000, pos=(287.10526315789474, 32.719298245614034), size=10.175438596491228", "module": "qr_preview", "function": "_draw_marker_center", "line": 179}
{"timestamp": "2025-06-12T17:34:03.280552", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Successfully added finder pattern group to SVG", "module": "qr_preview", "function": "_draw_finder_marker", "line": 171}
{"timestamp": "2025-06-12T17:34:03.280589", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Successfully drew finder pattern 2/3", "module": "qr_preview", "function": "generate_preview_svg_modular", "line": 782}
{"timestamp": "2025-06-12T17:34:03.280634", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Drawing finder pattern 3/3 at position (0, 50)", "module": "qr_preview", "function": "generate_preview_svg_modular", "line": 780}
{"timestamp": "2025-06-12T17:34:03.280669", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "_draw_finder_marker called: mx=0, my=50, module_size=5.087719298245614, border_style=square, center_style=square, border_color=#000000, center_color=#000000, offset=(20, 20)", "module": "qr_preview", "function": "_draw_finder_marker", "line": 97}
{"timestamp": "2025-06-12T17:34:03.280738", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Creating finder pattern group with id='marker-0-50'", "module": "qr_preview", "function": "_draw_finder_marker", "line": 110}
{"timestamp": "2025-06-12T17:34:03.280802", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Drawing square border:", "module": "qr_preview", "function": "_draw_finder_marker", "line": 160}
{"timestamp": "2025-06-12T17:34:03.280849", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "  Outer: insert=(20.0, 274.38596491228066), size=(35.614035087719294, 35.614035087719294)", "module": "qr_preview", "function": "_draw_finder_marker", "line": 161}
{"timestamp": "2025-06-12T17:34:03.280911", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "  Inner: insert=(26.105263157894736, 280.4912280701754), size=(23.403508771929822, 23.403508771929822)", "module": "qr_preview", "function": "_draw_finder_marker", "line": 162}
{"timestamp": "2025-06-12T17:34:03.280987", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Drawing marker center: style=square, color=#000000, pos=(32.719298245614034, 287.10526315789474), size=10.175438596491228", "module": "qr_preview", "function": "_draw_marker_center", "line": 179}
{"timestamp": "2025-06-12T17:34:03.281043", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Successfully added finder pattern group to SVG", "module": "qr_preview", "function": "_draw_finder_marker", "line": 171}
{"timestamp": "2025-06-12T17:34:03.281086", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Successfully drew finder pattern 3/3", "module": "qr_preview", "function": "generate_preview_svg_modular", "line": 782}
{"timestamp": "2025-06-12T17:34:03.307668", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Generated SVG length: 247431", "module": "qr_preview", "function": "generate_preview_svg_modular", "line": 802}
{"timestamp": "2025-06-12T17:34:03.307827", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "SVG contains marker-0-0: True", "module": "qr_preview", "function": "generate_preview_svg_modular", "line": 803}
{"timestamp": "2025-06-12T17:34:03.307959", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "SVG contains marker-18-0: False", "module": "qr_preview", "function": "generate_preview_svg_modular", "line": 804}
{"timestamp": "2025-06-12T17:34:03.308092", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "SVG contains marker-0-18: False", "module": "qr_preview", "function": "generate_preview_svg_modular", "line": 805}
{"timestamp": "2025-06-12T17:34:03.308490", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "SVG marker counts - outer: 3, inner: 3, dot: 0", "module": "qr_preview", "function": "generate_preview_svg_modular", "line": 813}
{"timestamp": "2025-06-12T17:34:03.308527", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "SVG label counts - text: 0, background: 0", "module": "qr_preview", "function": "generate_preview_svg_modular", "line": 814}
{"timestamp": "2025-06-12T17:34:03.310511", "level": "DEBUG", "logger": "backend.main", "message": "[Auth] Enforced Bearer token authentication for request", "module": "main", "function": "bearer_token_auth_middleware", "line": 274}
{"timestamp": "2025-06-12T17:34:04.156912", "level": "DEBUG", "logger": "backend.core.security.jwt_keys", "message": "Token contains key ID: 0bf271b1-2fbf-4d96-987f-e0b06b2fd890", "module": "jwt_keys", "function": "decode_token", "line": 188}
{"timestamp": "2025-06-12T17:34:04.156985", "level": "DEBUG", "logger": "backend.core.security.jwt_keys", "message": "Using key ID 0bf271b1-2fbf-4d96-987f-e0b06b2fd890 for verification", "module": "jwt_keys", "function": "decode_token", "line": 195}
{"timestamp": "2025-06-12T17:34:04.157127", "level": "DEBUG", "logger": "backend.services.auth", "message": "JWT decoded successfully with key manager", "module": "auth", "function": "authenticate_token", "line": 683}
{"timestamp": "2025-06-12T17:34:04.159596", "level": "DEBUG", "logger": "backend.services.db_utils", "message": "Query 'auth_lookup_e4ae2d8f-b32f-52d7-99cd-8a33b19652c7' completed in 0.002s", "module": "db_utils", "function": "execute_query_with_timeout", "line": 68}
{"timestamp": "2025-06-12T17:34:04.159663", "level": "DEBUG", "logger": "backend.services.db_utils", "message": "Auth query 'auth_lookup_e4ae2d8f-b32f-52d7-99cd-8a33b19652c7' successful, result format preserved", "module": "db_utils", "function": "execute_query_with_timeout", "line": 72}
{"timestamp": "2025-06-12T17:34:04.170302", "level": "DEBUG", "logger": "backend.services.auth", "message": "Authentication attempt with token prefix: eyJhbGciOi...", "module": "auth", "function": "get_current_user", "line": 727}
{"timestamp": "2025-06-12T17:34:04.170461", "level": "DEBUG", "logger": "backend.core.security.jwt_keys", "message": "Token contains key ID: 0bf271b1-2fbf-4d96-987f-e0b06b2fd890", "module": "jwt_keys", "function": "decode_token", "line": 188}
{"timestamp": "2025-06-12T17:34:04.170498", "level": "DEBUG", "logger": "backend.core.security.jwt_keys", "message": "Using key ID 0bf271b1-2fbf-4d96-987f-e0b06b2fd890 for verification", "module": "jwt_keys", "function": "decode_token", "line": 195}
{"timestamp": "2025-06-12T17:34:04.170620", "level": "DEBUG", "logger": "backend.services.auth", "message": "JWT decoded successfully with key manager", "module": "auth", "function": "authenticate_token", "line": 683}
{"timestamp": "2025-06-12T17:34:04.173522", "level": "DEBUG", "logger": "backend.services.db_utils", "message": "Query 'auth_lookup_e4ae2d8f-b32f-52d7-99cd-8a33b19652c7' completed in 0.003s", "module": "db_utils", "function": "execute_query_with_timeout", "line": 68}
{"timestamp": "2025-06-12T17:34:04.173570", "level": "DEBUG", "logger": "backend.services.db_utils", "message": "Auth query 'auth_lookup_e4ae2d8f-b32f-52d7-99cd-8a33b19652c7' successful, result format preserved", "module": "db_utils", "function": "execute_query_with_timeout", "line": 72}
{"timestamp": "2025-06-12T17:34:04.173622", "level": "DEBUG", "logger": "backend.services.auth", "message": "Successfully authenticated user: <EMAIL>", "module": "auth", "function": "get_current_user", "line": 732}
{"timestamp": "2025-06-12T17:34:04.173657", "level": "DEBUG", "logger": "backend.services.auth", "message": "Successfully authenticated user: <EMAIL>", "module": "auth", "function": "get_current_user", "line": 738}
{"timestamp": "2025-06-12T17:34:04.174942", "level": "DEBUG", "logger": "backend.routes.qr_preview", "message": "Received QR preview SVG request: {\"qr_type\": \"scooter_unlock\", \"data\": {\"scooter_id\": \"SCOOT123\", \"unlock_code\": \"UNLOCK42\", \"title\": \"Scooter_unlock QR Code\", \"description\": \"Scan to view scooter_unlock content\", \"service_provider\": \"Lime\", \"rate_info\": \"$20\"}, \"designOptions\": {\"size\": 256, \"margin\": 10, \"pattern\": \"square\", \"marker_border_style\": \"square\", \"marker_border_color\": \"#000000\", \"marker_center_style\": \"square\", \"marker_center_color\": \"#000000\", \"logo_size\": 0.3, \"logo_position\": \"center\", \"logo_opacity\": 1, \"logo_", "module": "qr_preview", "function": "preview_qr_svg", "line": 242}
{"timestamp": "2025-06-12T17:34:04.175011", "level": "DEBUG", "logger": "backend.routes.qr_preview", "message": "Form data keys: ['request']", "module": "qr_preview", "function": "preview_qr_svg", "line": 297}
{"timestamp": "2025-06-12T17:34:04.175049", "level": "DEBUG", "logger": "backend.routes.qr_preview", "message": "No logo key found in form data", "module": "qr_preview", "function": "preview_qr_svg", "line": 309}
{"timestamp": "2025-06-12T17:34:04.175090", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Formatting content for QR type: qrtype.scooter_unlock, content keys: ['scooter_id', 'unlock_code', 'title', 'description', 'service_provider', 'rate_info']", "module": "qr_preview", "function": "_format_qr_data", "line": 876}
{"timestamp": "2025-06-12T17:34:04.198925", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Logo remove background setting: False, has logo: False", "module": "qr_preview", "function": "generate_preview_svg_modular", "line": 727}
{"timestamp": "2025-06-12T17:34:04.217561", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Drawing 3 finder patterns at positions: [(0, 0), (50, 0), (0, 50)]", "module": "qr_preview", "function": "generate_preview_svg_modular", "line": 775}
{"timestamp": "2025-06-12T17:34:04.217636", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "QR size: 57, border_style: square, center_style: square", "module": "qr_preview", "function": "generate_preview_svg_modular", "line": 776}
{"timestamp": "2025-06-12T17:34:04.217705", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Drawing finder pattern 1/3 at position (0, 0)", "module": "qr_preview", "function": "generate_preview_svg_modular", "line": 780}
{"timestamp": "2025-06-12T17:34:04.217756", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "_draw_finder_marker called: mx=0, my=0, module_size=5.087719298245614, border_style=square, center_style=square, border_color=#000000, center_color=#000000, offset=(20, 20)", "module": "qr_preview", "function": "_draw_finder_marker", "line": 97}
{"timestamp": "2025-06-12T17:34:04.217806", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Creating finder pattern group with id='marker-0-0'", "module": "qr_preview", "function": "_draw_finder_marker", "line": 110}
{"timestamp": "2025-06-12T17:34:04.217841", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Drawing square border:", "module": "qr_preview", "function": "_draw_finder_marker", "line": 160}
{"timestamp": "2025-06-12T17:34:04.217876", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "  Outer: insert=(20.0, 20.0), size=(35.614035087719294, 35.614035087719294)", "module": "qr_preview", "function": "_draw_finder_marker", "line": 161}
{"timestamp": "2025-06-12T17:34:04.217911", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "  Inner: insert=(26.105263157894736, 26.105263157894736), size=(23.403508771929822, 23.403508771929822)", "module": "qr_preview", "function": "_draw_finder_marker", "line": 162}
{"timestamp": "2025-06-12T17:34:04.217978", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Drawing marker center: style=square, color=#000000, pos=(32.719298245614034, 32.719298245614034), size=10.175438596491228", "module": "qr_preview", "function": "_draw_marker_center", "line": 179}
{"timestamp": "2025-06-12T17:34:04.218030", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Successfully added finder pattern group to SVG", "module": "qr_preview", "function": "_draw_finder_marker", "line": 171}
{"timestamp": "2025-06-12T17:34:04.218063", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Successfully drew finder pattern 1/3", "module": "qr_preview", "function": "generate_preview_svg_modular", "line": 782}
{"timestamp": "2025-06-12T17:34:04.218094", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Drawing finder pattern 2/3 at position (50, 0)", "module": "qr_preview", "function": "generate_preview_svg_modular", "line": 780}
{"timestamp": "2025-06-12T17:34:04.218126", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "_draw_finder_marker called: mx=50, my=0, module_size=5.087719298245614, border_style=square, center_style=square, border_color=#000000, center_color=#000000, offset=(20, 20)", "module": "qr_preview", "function": "_draw_finder_marker", "line": 97}
{"timestamp": "2025-06-12T17:34:04.218164", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Creating finder pattern group with id='marker-50-0'", "module": "qr_preview", "function": "_draw_finder_marker", "line": 110}
{"timestamp": "2025-06-12T17:34:04.218196", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Drawing square border:", "module": "qr_preview", "function": "_draw_finder_marker", "line": 160}
{"timestamp": "2025-06-12T17:34:04.218228", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "  Outer: insert=(274.38596491228066, 20.0), size=(35.614035087719294, 35.614035087719294)", "module": "qr_preview", "function": "_draw_finder_marker", "line": 161}
{"timestamp": "2025-06-12T17:34:04.218260", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "  Inner: insert=(280.4912280701754, 26.105263157894736), size=(23.403508771929822, 23.403508771929822)", "module": "qr_preview", "function": "_draw_finder_marker", "line": 162}
{"timestamp": "2025-06-12T17:34:04.218335", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Drawing marker center: style=square, color=#000000, pos=(287.10526315789474, 32.719298245614034), size=10.175438596491228", "module": "qr_preview", "function": "_draw_marker_center", "line": 179}
{"timestamp": "2025-06-12T17:34:04.218380", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Successfully added finder pattern group to SVG", "module": "qr_preview", "function": "_draw_finder_marker", "line": 171}
{"timestamp": "2025-06-12T17:34:04.218412", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Successfully drew finder pattern 2/3", "module": "qr_preview", "function": "generate_preview_svg_modular", "line": 782}
{"timestamp": "2025-06-12T17:34:04.218442", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Drawing finder pattern 3/3 at position (0, 50)", "module": "qr_preview", "function": "generate_preview_svg_modular", "line": 780}
{"timestamp": "2025-06-12T17:34:04.218473", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "_draw_finder_marker called: mx=0, my=50, module_size=5.087719298245614, border_style=square, center_style=square, border_color=#000000, center_color=#000000, offset=(20, 20)", "module": "qr_preview", "function": "_draw_finder_marker", "line": 97}
{"timestamp": "2025-06-12T17:34:04.218510", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Creating finder pattern group with id='marker-0-50'", "module": "qr_preview", "function": "_draw_finder_marker", "line": 110}
{"timestamp": "2025-06-12T17:34:04.218540", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Drawing square border:", "module": "qr_preview", "function": "_draw_finder_marker", "line": 160}
{"timestamp": "2025-06-12T17:34:04.218570", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "  Outer: insert=(20.0, 274.38596491228066), size=(35.614035087719294, 35.614035087719294)", "module": "qr_preview", "function": "_draw_finder_marker", "line": 161}
{"timestamp": "2025-06-12T17:34:04.218603", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "  Inner: insert=(26.105263157894736, 280.4912280701754), size=(23.403508771929822, 23.403508771929822)", "module": "qr_preview", "function": "_draw_finder_marker", "line": 162}
{"timestamp": "2025-06-12T17:34:04.218658", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Drawing marker center: style=square, color=#000000, pos=(32.719298245614034, 287.10526315789474), size=10.175438596491228", "module": "qr_preview", "function": "_draw_marker_center", "line": 179}
{"timestamp": "2025-06-12T17:34:04.218703", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Successfully added finder pattern group to SVG", "module": "qr_preview", "function": "_draw_finder_marker", "line": 171}
{"timestamp": "2025-06-12T17:34:04.218732", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Successfully drew finder pattern 3/3", "module": "qr_preview", "function": "generate_preview_svg_modular", "line": 782}
{"timestamp": "2025-06-12T17:34:04.246428", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Generated SVG length: 247431", "module": "qr_preview", "function": "generate_preview_svg_modular", "line": 802}
{"timestamp": "2025-06-12T17:34:04.246593", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "SVG contains marker-0-0: True", "module": "qr_preview", "function": "generate_preview_svg_modular", "line": 803}
{"timestamp": "2025-06-12T17:34:04.246726", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "SVG contains marker-18-0: False", "module": "qr_preview", "function": "generate_preview_svg_modular", "line": 804}
{"timestamp": "2025-06-12T17:34:04.246860", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "SVG contains marker-0-18: False", "module": "qr_preview", "function": "generate_preview_svg_modular", "line": 805}
{"timestamp": "2025-06-12T17:34:04.247273", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "SVG marker counts - outer: 3, inner: 3, dot: 0", "module": "qr_preview", "function": "generate_preview_svg_modular", "line": 813}
{"timestamp": "2025-06-12T17:34:04.247308", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "SVG label counts - text: 0, background: 0", "module": "qr_preview", "function": "generate_preview_svg_modular", "line": 814}
{"timestamp": "2025-06-12T17:34:04.249580", "level": "DEBUG", "logger": "backend.main", "message": "[Auth] Enforced Bearer token authentication for request", "module": "main", "function": "bearer_token_auth_middleware", "line": 274}
{"timestamp": "2025-06-12T17:34:15.725131", "level": "DEBUG", "logger": "backend.core.security.jwt_keys", "message": "Token contains key ID: 0bf271b1-2fbf-4d96-987f-e0b06b2fd890", "module": "jwt_keys", "function": "decode_token", "line": 188}
{"timestamp": "2025-06-12T17:34:15.725358", "level": "DEBUG", "logger": "backend.core.security.jwt_keys", "message": "Using key ID 0bf271b1-2fbf-4d96-987f-e0b06b2fd890 for verification", "module": "jwt_keys", "function": "decode_token", "line": 195}
{"timestamp": "2025-06-12T17:34:15.725852", "level": "DEBUG", "logger": "backend.services.auth", "message": "JWT decoded successfully with key manager", "module": "auth", "function": "authenticate_token", "line": 683}
{"timestamp": "2025-06-12T17:34:15.731698", "level": "DEBUG", "logger": "backend.services.db_utils", "message": "Query 'auth_lookup_e4ae2d8f-b32f-52d7-99cd-8a33b19652c7' completed in 0.004s", "module": "db_utils", "function": "execute_query_with_timeout", "line": 68}
{"timestamp": "2025-06-12T17:34:15.731746", "level": "DEBUG", "logger": "backend.services.db_utils", "message": "Auth query 'auth_lookup_e4ae2d8f-b32f-52d7-99cd-8a33b19652c7' successful, result format preserved", "module": "db_utils", "function": "execute_query_with_timeout", "line": 72}
{"timestamp": "2025-06-12T17:34:15.742328", "level": "DEBUG", "logger": "backend.services.auth", "message": "Authentication attempt with token prefix: eyJhbGciOi...", "module": "auth", "function": "get_current_user", "line": 727}
{"timestamp": "2025-06-12T17:34:15.742461", "level": "DEBUG", "logger": "backend.core.security.jwt_keys", "message": "Token contains key ID: 0bf271b1-2fbf-4d96-987f-e0b06b2fd890", "module": "jwt_keys", "function": "decode_token", "line": 188}
{"timestamp": "2025-06-12T17:34:15.742504", "level": "DEBUG", "logger": "backend.core.security.jwt_keys", "message": "Using key ID 0bf271b1-2fbf-4d96-987f-e0b06b2fd890 for verification", "module": "jwt_keys", "function": "decode_token", "line": 195}
{"timestamp": "2025-06-12T17:34:15.742626", "level": "DEBUG", "logger": "backend.services.auth", "message": "JWT decoded successfully with key manager", "module": "auth", "function": "authenticate_token", "line": 683}
{"timestamp": "2025-06-12T17:34:15.744709", "level": "DEBUG", "logger": "backend.services.db_utils", "message": "Query 'auth_lookup_e4ae2d8f-b32f-52d7-99cd-8a33b19652c7' completed in 0.002s", "module": "db_utils", "function": "execute_query_with_timeout", "line": 68}
{"timestamp": "2025-06-12T17:34:15.744768", "level": "DEBUG", "logger": "backend.services.db_utils", "message": "Auth query 'auth_lookup_e4ae2d8f-b32f-52d7-99cd-8a33b19652c7' successful, result format preserved", "module": "db_utils", "function": "execute_query_with_timeout", "line": 72}
{"timestamp": "2025-06-12T17:34:15.744836", "level": "DEBUG", "logger": "backend.services.auth", "message": "Successfully authenticated user: <EMAIL>", "module": "auth", "function": "get_current_user", "line": 732}
{"timestamp": "2025-06-12T17:34:15.744882", "level": "DEBUG", "logger": "backend.services.auth", "message": "Successfully authenticated user: <EMAIL>", "module": "auth", "function": "get_current_user", "line": 738}
{"timestamp": "2025-06-12T17:34:15.749658", "level": "DEBUG", "logger": "backend.routes.qr_preview", "message": "Received QR preview SVG request: {\"qr_type\": \"scooter_unlock\", \"data\": {\"scooter_id\": \"SCOOT123\", \"unlock_code\": \"UNLOCK42\", \"title\": \"Scooter_unlock QR Code\", \"description\": \"Scan to view scooter_unlock content\", \"service_provider\": \"Lime\", \"rate_info\": \"$20\"}, \"designOptions\": {\"size\": 256, \"margin\": 10, \"pattern\": \"square\", \"marker_border_style\": \"square\", \"marker_center_style\": \"square\", \"logo_size\": 0.3, \"logo_position\": \"center\", \"logo_opacity\": 1, \"logo_remove_background\": false, \"frame_style\": \"none\", \"frame_font\": \"SF ", "module": "qr_preview", "function": "preview_qr_svg", "line": 242}
{"timestamp": "2025-06-12T17:34:15.749972", "level": "DEBUG", "logger": "backend.routes.qr_preview", "message": "Form data keys: ['request', 'logo']", "module": "qr_preview", "function": "preview_qr_svg", "line": 297}
{"timestamp": "2025-06-12T17:34:15.750024", "level": "DEBUG", "logger": "backend.routes.qr_preview", "message": "Logo file found: type=<class 'starlette.datastructures.UploadFile'>, filename=LOGO - 1080 - BLACK-01.png", "module": "qr_preview", "function": "preview_qr_svg", "line": 300}
{"timestamp": "2025-06-12T17:34:15.750088", "level": "DEBUG", "logger": "backend.routes.qr_preview", "message": "Logo content read: 142252 bytes", "module": "qr_preview", "function": "preview_qr_svg", "line": 305}
{"timestamp": "2025-06-12T17:34:15.750190", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Formatting content for QR type: qrtype.scooter_unlock, content keys: ['scooter_id', 'unlock_code', 'title', 'description', 'service_provider', 'rate_info']", "module": "qr_preview", "function": "_format_qr_data", "line": 876}
{"timestamp": "2025-06-12T17:34:15.773952", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Logo remove background setting: False, has logo: True", "module": "qr_preview", "function": "generate_preview_svg_modular", "line": 727}
{"timestamp": "2025-06-12T17:34:15.774016", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Logo present but background removal disabled", "module": "qr_preview", "function": "generate_preview_svg_modular", "line": 741}
{"timestamp": "2025-06-12T17:34:15.792828", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Drawing 3 finder patterns at positions: [(0, 0), (50, 0), (0, 50)]", "module": "qr_preview", "function": "generate_preview_svg_modular", "line": 775}
{"timestamp": "2025-06-12T17:34:15.792888", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "QR size: 57, border_style: square, center_style: square", "module": "qr_preview", "function": "generate_preview_svg_modular", "line": 776}
{"timestamp": "2025-06-12T17:34:15.792926", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Drawing finder pattern 1/3 at position (0, 0)", "module": "qr_preview", "function": "generate_preview_svg_modular", "line": 780}
{"timestamp": "2025-06-12T17:34:15.792980", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "_draw_finder_marker called: mx=0, my=0, module_size=5.087719298245614, border_style=square, center_style=square, border_color=#000000, center_color=#000000, offset=(20, 20)", "module": "qr_preview", "function": "_draw_finder_marker", "line": 97}
{"timestamp": "2025-06-12T17:34:15.793037", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Creating finder pattern group with id='marker-0-0'", "module": "qr_preview", "function": "_draw_finder_marker", "line": 110}
{"timestamp": "2025-06-12T17:34:15.793071", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Drawing square border:", "module": "qr_preview", "function": "_draw_finder_marker", "line": 160}
{"timestamp": "2025-06-12T17:34:15.793107", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "  Outer: insert=(20.0, 20.0), size=(35.614035087719294, 35.614035087719294)", "module": "qr_preview", "function": "_draw_finder_marker", "line": 161}
{"timestamp": "2025-06-12T17:34:15.793182", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "  Inner: insert=(26.105263157894736, 26.105263157894736), size=(23.403508771929822, 23.403508771929822)", "module": "qr_preview", "function": "_draw_finder_marker", "line": 162}
{"timestamp": "2025-06-12T17:34:15.793273", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Drawing marker center: style=square, color=#000000, pos=(32.719298245614034, 32.719298245614034), size=10.175438596491228", "module": "qr_preview", "function": "_draw_marker_center", "line": 179}
{"timestamp": "2025-06-12T17:34:15.793332", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Successfully added finder pattern group to SVG", "module": "qr_preview", "function": "_draw_finder_marker", "line": 171}
{"timestamp": "2025-06-12T17:34:15.793365", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Successfully drew finder pattern 1/3", "module": "qr_preview", "function": "generate_preview_svg_modular", "line": 782}
{"timestamp": "2025-06-12T17:34:15.793400", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Drawing finder pattern 2/3 at position (50, 0)", "module": "qr_preview", "function": "generate_preview_svg_modular", "line": 780}
{"timestamp": "2025-06-12T17:34:15.793438", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "_draw_finder_marker called: mx=50, my=0, module_size=5.087719298245614, border_style=square, center_style=square, border_color=#000000, center_color=#000000, offset=(20, 20)", "module": "qr_preview", "function": "_draw_finder_marker", "line": 97}
{"timestamp": "2025-06-12T17:34:15.793478", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Creating finder pattern group with id='marker-50-0'", "module": "qr_preview", "function": "_draw_finder_marker", "line": 110}
{"timestamp": "2025-06-12T17:34:15.793510", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Drawing square border:", "module": "qr_preview", "function": "_draw_finder_marker", "line": 160}
{"timestamp": "2025-06-12T17:34:15.793548", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "  Outer: insert=(274.38596491228066, 20.0), size=(35.614035087719294, 35.614035087719294)", "module": "qr_preview", "function": "_draw_finder_marker", "line": 161}
{"timestamp": "2025-06-12T17:34:15.793581", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "  Inner: insert=(280.4912280701754, 26.105263157894736), size=(23.403508771929822, 23.403508771929822)", "module": "qr_preview", "function": "_draw_finder_marker", "line": 162}
{"timestamp": "2025-06-12T17:34:15.793646", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Drawing marker center: style=square, color=#000000, pos=(287.10526315789474, 32.719298245614034), size=10.175438596491228", "module": "qr_preview", "function": "_draw_marker_center", "line": 179}
{"timestamp": "2025-06-12T17:34:15.793689", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Successfully added finder pattern group to SVG", "module": "qr_preview", "function": "_draw_finder_marker", "line": 171}
{"timestamp": "2025-06-12T17:34:15.793719", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Successfully drew finder pattern 2/3", "module": "qr_preview", "function": "generate_preview_svg_modular", "line": 782}
{"timestamp": "2025-06-12T17:34:15.793749", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Drawing finder pattern 3/3 at position (0, 50)", "module": "qr_preview", "function": "generate_preview_svg_modular", "line": 780}
{"timestamp": "2025-06-12T17:34:15.793780", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "_draw_finder_marker called: mx=0, my=50, module_size=5.087719298245614, border_style=square, center_style=square, border_color=#000000, center_color=#000000, offset=(20, 20)", "module": "qr_preview", "function": "_draw_finder_marker", "line": 97}
{"timestamp": "2025-06-12T17:34:15.793834", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Creating finder pattern group with id='marker-0-50'", "module": "qr_preview", "function": "_draw_finder_marker", "line": 110}
{"timestamp": "2025-06-12T17:34:15.793863", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Drawing square border:", "module": "qr_preview", "function": "_draw_finder_marker", "line": 160}
{"timestamp": "2025-06-12T17:34:15.793895", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "  Outer: insert=(20.0, 274.38596491228066), size=(35.614035087719294, 35.614035087719294)", "module": "qr_preview", "function": "_draw_finder_marker", "line": 161}
{"timestamp": "2025-06-12T17:34:15.793926", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "  Inner: insert=(26.105263157894736, 280.4912280701754), size=(23.403508771929822, 23.403508771929822)", "module": "qr_preview", "function": "_draw_finder_marker", "line": 162}
{"timestamp": "2025-06-12T17:34:15.793981", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Drawing marker center: style=square, color=#000000, pos=(32.719298245614034, 287.10526315789474), size=10.175438596491228", "module": "qr_preview", "function": "_draw_marker_center", "line": 179}
{"timestamp": "2025-06-12T17:34:15.794025", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Successfully added finder pattern group to SVG", "module": "qr_preview", "function": "_draw_finder_marker", "line": 171}
{"timestamp": "2025-06-12T17:34:15.794055", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Successfully drew finder pattern 3/3", "module": "qr_preview", "function": "generate_preview_svg_modular", "line": 782}
{"timestamp": "2025-06-12T17:34:15.794111", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "_draw_logo called with logo type: <class 'bytes'>, dimensions: 300", "module": "qr_preview", "function": "_draw_logo", "line": 434}
{"timestamp": "2025-06-12T17:34:16.167165", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Generated SVG length: 252045", "module": "qr_preview", "function": "generate_preview_svg_modular", "line": 802}
{"timestamp": "2025-06-12T17:34:16.167329", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "SVG contains marker-0-0: True", "module": "qr_preview", "function": "generate_preview_svg_modular", "line": 803}
{"timestamp": "2025-06-12T17:34:16.167469", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "SVG contains marker-18-0: False", "module": "qr_preview", "function": "generate_preview_svg_modular", "line": 804}
{"timestamp": "2025-06-12T17:34:16.167606", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "SVG contains marker-0-18: False", "module": "qr_preview", "function": "generate_preview_svg_modular", "line": 805}
{"timestamp": "2025-06-12T17:34:16.168015", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "SVG marker counts - outer: 3, inner: 3, dot: 0", "module": "qr_preview", "function": "generate_preview_svg_modular", "line": 813}
{"timestamp": "2025-06-12T17:34:16.168056", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "SVG label counts - text: 0, background: 0", "module": "qr_preview", "function": "generate_preview_svg_modular", "line": 814}
{"timestamp": "2025-06-12T17:34:16.170491", "level": "DEBUG", "logger": "backend.main", "message": "[Auth] Enforced Bearer token authentication for request", "module": "main", "function": "bearer_token_auth_middleware", "line": 274}
{"timestamp": "2025-06-12T17:34:17.007913", "level": "DEBUG", "logger": "backend.core.security.jwt_keys", "message": "Token contains key ID: 0bf271b1-2fbf-4d96-987f-e0b06b2fd890", "module": "jwt_keys", "function": "decode_token", "line": 188}
{"timestamp": "2025-06-12T17:34:17.007975", "level": "DEBUG", "logger": "backend.core.security.jwt_keys", "message": "Using key ID 0bf271b1-2fbf-4d96-987f-e0b06b2fd890 for verification", "module": "jwt_keys", "function": "decode_token", "line": 195}
{"timestamp": "2025-06-12T17:34:17.008149", "level": "DEBUG", "logger": "backend.services.auth", "message": "JWT decoded successfully with key manager", "module": "auth", "function": "authenticate_token", "line": 683}
{"timestamp": "2025-06-12T17:34:17.010170", "level": "DEBUG", "logger": "backend.services.db_utils", "message": "Query 'auth_lookup_e4ae2d8f-b32f-52d7-99cd-8a33b19652c7' completed in 0.002s", "module": "db_utils", "function": "execute_query_with_timeout", "line": 68}
{"timestamp": "2025-06-12T17:34:17.010220", "level": "DEBUG", "logger": "backend.services.db_utils", "message": "Auth query 'auth_lookup_e4ae2d8f-b32f-52d7-99cd-8a33b19652c7' successful, result format preserved", "module": "db_utils", "function": "execute_query_with_timeout", "line": 72}
{"timestamp": "2025-06-12T17:34:17.020166", "level": "DEBUG", "logger": "backend.services.auth", "message": "Authentication attempt with token prefix: eyJhbGciOi...", "module": "auth", "function": "get_current_user", "line": 727}
{"timestamp": "2025-06-12T17:34:17.020294", "level": "DEBUG", "logger": "backend.core.security.jwt_keys", "message": "Token contains key ID: 0bf271b1-2fbf-4d96-987f-e0b06b2fd890", "module": "jwt_keys", "function": "decode_token", "line": 188}
{"timestamp": "2025-06-12T17:34:17.020337", "level": "DEBUG", "logger": "backend.core.security.jwt_keys", "message": "Using key ID 0bf271b1-2fbf-4d96-987f-e0b06b2fd890 for verification", "module": "jwt_keys", "function": "decode_token", "line": 195}
{"timestamp": "2025-06-12T17:34:17.020461", "level": "DEBUG", "logger": "backend.services.auth", "message": "JWT decoded successfully with key manager", "module": "auth", "function": "authenticate_token", "line": 683}
{"timestamp": "2025-06-12T17:34:17.025211", "level": "DEBUG", "logger": "backend.services.db_utils", "message": "Query 'auth_lookup_e4ae2d8f-b32f-52d7-99cd-8a33b19652c7' completed in 0.005s", "module": "db_utils", "function": "execute_query_with_timeout", "line": 68}
{"timestamp": "2025-06-12T17:34:17.025275", "level": "DEBUG", "logger": "backend.services.db_utils", "message": "Auth query 'auth_lookup_e4ae2d8f-b32f-52d7-99cd-8a33b19652c7' successful, result format preserved", "module": "db_utils", "function": "execute_query_with_timeout", "line": 72}
{"timestamp": "2025-06-12T17:34:17.025468", "level": "DEBUG", "logger": "backend.services.auth", "message": "Successfully authenticated user: <EMAIL>", "module": "auth", "function": "get_current_user", "line": 732}
{"timestamp": "2025-06-12T17:34:17.025532", "level": "DEBUG", "logger": "backend.services.auth", "message": "Successfully authenticated user: <EMAIL>", "module": "auth", "function": "get_current_user", "line": 738}
{"timestamp": "2025-06-12T17:34:17.027879", "level": "DEBUG", "logger": "backend.routes.qr_preview", "message": "Received QR preview SVG request: {\"qr_type\": \"scooter_unlock\", \"data\": {\"scooter_id\": \"SCOOT123\", \"unlock_code\": \"UNLOCK42\", \"title\": \"Scooter_unlock QR Code\", \"description\": \"Scan to view scooter_unlock content\", \"service_provider\": \"Lime\", \"rate_info\": \"$20\"}, \"designOptions\": {\"size\": 256, \"margin\": 10, \"pattern\": \"square\", \"marker_border_style\": \"square\", \"marker_border_color\": \"#000000\", \"marker_center_style\": \"square\", \"marker_center_color\": \"#000000\", \"logo_size\": 0.3, \"logo_position\": \"center\", \"logo_opacity\": 1, \"logo_", "module": "qr_preview", "function": "preview_qr_svg", "line": 242}
{"timestamp": "2025-06-12T17:34:17.028092", "level": "DEBUG", "logger": "backend.routes.qr_preview", "message": "Form data keys: ['request', 'logo']", "module": "qr_preview", "function": "preview_qr_svg", "line": 297}
{"timestamp": "2025-06-12T17:34:17.028153", "level": "DEBUG", "logger": "backend.routes.qr_preview", "message": "Logo file found: type=<class 'starlette.datastructures.UploadFile'>, filename=LOGO - 1080 - BLACK-01.png", "module": "qr_preview", "function": "preview_qr_svg", "line": 300}
{"timestamp": "2025-06-12T17:34:17.028263", "level": "DEBUG", "logger": "backend.routes.qr_preview", "message": "Logo content read: 142252 bytes", "module": "qr_preview", "function": "preview_qr_svg", "line": 305}
{"timestamp": "2025-06-12T17:34:17.028339", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Formatting content for QR type: qrtype.scooter_unlock, content keys: ['scooter_id', 'unlock_code', 'title', 'description', 'service_provider', 'rate_info']", "module": "qr_preview", "function": "_format_qr_data", "line": 876}
{"timestamp": "2025-06-12T17:34:17.051778", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Logo remove background setting: False, has logo: True", "module": "qr_preview", "function": "generate_preview_svg_modular", "line": 727}
{"timestamp": "2025-06-12T17:34:17.051845", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Logo present but background removal disabled", "module": "qr_preview", "function": "generate_preview_svg_modular", "line": 741}
{"timestamp": "2025-06-12T17:34:17.070742", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Drawing 3 finder patterns at positions: [(0, 0), (50, 0), (0, 50)]", "module": "qr_preview", "function": "generate_preview_svg_modular", "line": 775}
{"timestamp": "2025-06-12T17:34:17.070810", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "QR size: 57, border_style: square, center_style: square", "module": "qr_preview", "function": "generate_preview_svg_modular", "line": 776}
{"timestamp": "2025-06-12T17:34:17.070851", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Drawing finder pattern 1/3 at position (0, 0)", "module": "qr_preview", "function": "generate_preview_svg_modular", "line": 780}
{"timestamp": "2025-06-12T17:34:17.070904", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "_draw_finder_marker called: mx=0, my=0, module_size=5.087719298245614, border_style=square, center_style=square, border_color=#000000, center_color=#000000, offset=(20, 20)", "module": "qr_preview", "function": "_draw_finder_marker", "line": 97}
{"timestamp": "2025-06-12T17:34:17.070956", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Creating finder pattern group with id='marker-0-0'", "module": "qr_preview", "function": "_draw_finder_marker", "line": 110}
{"timestamp": "2025-06-12T17:34:17.070993", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Drawing square border:", "module": "qr_preview", "function": "_draw_finder_marker", "line": 160}
{"timestamp": "2025-06-12T17:34:17.071028", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "  Outer: insert=(20.0, 20.0), size=(35.614035087719294, 35.614035087719294)", "module": "qr_preview", "function": "_draw_finder_marker", "line": 161}
{"timestamp": "2025-06-12T17:34:17.071063", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "  Inner: insert=(26.105263157894736, 26.105263157894736), size=(23.403508771929822, 23.403508771929822)", "module": "qr_preview", "function": "_draw_finder_marker", "line": 162}
{"timestamp": "2025-06-12T17:34:17.071146", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Drawing marker center: style=square, color=#000000, pos=(32.719298245614034, 32.719298245614034), size=10.175438596491228", "module": "qr_preview", "function": "_draw_marker_center", "line": 179}
{"timestamp": "2025-06-12T17:34:17.071195", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Successfully added finder pattern group to SVG", "module": "qr_preview", "function": "_draw_finder_marker", "line": 171}
{"timestamp": "2025-06-12T17:34:17.071228", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Successfully drew finder pattern 1/3", "module": "qr_preview", "function": "generate_preview_svg_modular", "line": 782}
{"timestamp": "2025-06-12T17:34:17.071258", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Drawing finder pattern 2/3 at position (50, 0)", "module": "qr_preview", "function": "generate_preview_svg_modular", "line": 780}
{"timestamp": "2025-06-12T17:34:17.071321", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "_draw_finder_marker called: mx=50, my=0, module_size=5.087719298245614, border_style=square, center_style=square, border_color=#000000, center_color=#000000, offset=(20, 20)", "module": "qr_preview", "function": "_draw_finder_marker", "line": 97}
{"timestamp": "2025-06-12T17:34:17.071359", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Creating finder pattern group with id='marker-50-0'", "module": "qr_preview", "function": "_draw_finder_marker", "line": 110}
{"timestamp": "2025-06-12T17:34:17.071393", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Drawing square border:", "module": "qr_preview", "function": "_draw_finder_marker", "line": 160}
{"timestamp": "2025-06-12T17:34:17.071427", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "  Outer: insert=(274.38596491228066, 20.0), size=(35.614035087719294, 35.614035087719294)", "module": "qr_preview", "function": "_draw_finder_marker", "line": 161}
{"timestamp": "2025-06-12T17:34:17.071459", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "  Inner: insert=(280.4912280701754, 26.105263157894736), size=(23.403508771929822, 23.403508771929822)", "module": "qr_preview", "function": "_draw_finder_marker", "line": 162}
{"timestamp": "2025-06-12T17:34:17.071520", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Drawing marker center: style=square, color=#000000, pos=(287.10526315789474, 32.719298245614034), size=10.175438596491228", "module": "qr_preview", "function": "_draw_marker_center", "line": 179}
{"timestamp": "2025-06-12T17:34:17.071570", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Successfully added finder pattern group to SVG", "module": "qr_preview", "function": "_draw_finder_marker", "line": 171}
{"timestamp": "2025-06-12T17:34:17.071607", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Successfully drew finder pattern 2/3", "module": "qr_preview", "function": "generate_preview_svg_modular", "line": 782}
{"timestamp": "2025-06-12T17:34:17.071645", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Drawing finder pattern 3/3 at position (0, 50)", "module": "qr_preview", "function": "generate_preview_svg_modular", "line": 780}
{"timestamp": "2025-06-12T17:34:17.071687", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "_draw_finder_marker called: mx=0, my=50, module_size=5.087719298245614, border_style=square, center_style=square, border_color=#000000, center_color=#000000, offset=(20, 20)", "module": "qr_preview", "function": "_draw_finder_marker", "line": 97}
{"timestamp": "2025-06-12T17:34:17.071727", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Creating finder pattern group with id='marker-0-50'", "module": "qr_preview", "function": "_draw_finder_marker", "line": 110}
{"timestamp": "2025-06-12T17:34:17.071759", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Drawing square border:", "module": "qr_preview", "function": "_draw_finder_marker", "line": 160}
{"timestamp": "2025-06-12T17:34:17.071788", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "  Outer: insert=(20.0, 274.38596491228066), size=(35.614035087719294, 35.614035087719294)", "module": "qr_preview", "function": "_draw_finder_marker", "line": 161}
{"timestamp": "2025-06-12T17:34:17.071822", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "  Inner: insert=(26.105263157894736, 280.4912280701754), size=(23.403508771929822, 23.403508771929822)", "module": "qr_preview", "function": "_draw_finder_marker", "line": 162}
{"timestamp": "2025-06-12T17:34:17.071879", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Drawing marker center: style=square, color=#000000, pos=(32.719298245614034, 287.10526315789474), size=10.175438596491228", "module": "qr_preview", "function": "_draw_marker_center", "line": 179}
{"timestamp": "2025-06-12T17:34:17.071953", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Successfully added finder pattern group to SVG", "module": "qr_preview", "function": "_draw_finder_marker", "line": 171}
{"timestamp": "2025-06-12T17:34:17.071981", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Successfully drew finder pattern 3/3", "module": "qr_preview", "function": "generate_preview_svg_modular", "line": 782}
{"timestamp": "2025-06-12T17:34:17.072017", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "_draw_logo called with logo type: <class 'bytes'>, dimensions: 300", "module": "qr_preview", "function": "_draw_logo", "line": 434}
{"timestamp": "2025-06-12T17:34:17.403325", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Generated SVG length: 252045", "module": "qr_preview", "function": "generate_preview_svg_modular", "line": 802}
{"timestamp": "2025-06-12T17:34:17.403499", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "SVG contains marker-0-0: True", "module": "qr_preview", "function": "generate_preview_svg_modular", "line": 803}
{"timestamp": "2025-06-12T17:34:17.403644", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "SVG contains marker-18-0: False", "module": "qr_preview", "function": "generate_preview_svg_modular", "line": 804}
{"timestamp": "2025-06-12T17:34:17.403784", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "SVG contains marker-0-18: False", "module": "qr_preview", "function": "generate_preview_svg_modular", "line": 805}
{"timestamp": "2025-06-12T17:34:17.404187", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "SVG marker counts - outer: 3, inner: 3, dot: 0", "module": "qr_preview", "function": "generate_preview_svg_modular", "line": 813}
{"timestamp": "2025-06-12T17:34:17.404225", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "SVG label counts - text: 0, background: 0", "module": "qr_preview", "function": "generate_preview_svg_modular", "line": 814}
{"timestamp": "2025-06-12T17:34:17.406293", "level": "DEBUG", "logger": "backend.main", "message": "[Auth] Enforced Bearer token authentication for request", "module": "main", "function": "bearer_token_auth_middleware", "line": 274}
{"timestamp": "2025-06-12T17:34:22.239166", "level": "DEBUG", "logger": "backend.core.security.jwt_keys", "message": "Token contains key ID: 0bf271b1-2fbf-4d96-987f-e0b06b2fd890", "module": "jwt_keys", "function": "decode_token", "line": 188}
{"timestamp": "2025-06-12T17:34:22.239382", "level": "DEBUG", "logger": "backend.core.security.jwt_keys", "message": "Using key ID 0bf271b1-2fbf-4d96-987f-e0b06b2fd890 for verification", "module": "jwt_keys", "function": "decode_token", "line": 195}
{"timestamp": "2025-06-12T17:34:22.239886", "level": "DEBUG", "logger": "backend.services.auth", "message": "JWT decoded successfully with key manager", "module": "auth", "function": "authenticate_token", "line": 683}
{"timestamp": "2025-06-12T17:34:22.247681", "level": "DEBUG", "logger": "backend.services.db_utils", "message": "Query 'auth_lookup_e4ae2d8f-b32f-52d7-99cd-8a33b19652c7' completed in 0.006s", "module": "db_utils", "function": "execute_query_with_timeout", "line": 68}
{"timestamp": "2025-06-12T17:34:22.247755", "level": "DEBUG", "logger": "backend.services.db_utils", "message": "Auth query 'auth_lookup_e4ae2d8f-b32f-52d7-99cd-8a33b19652c7' successful, result format preserved", "module": "db_utils", "function": "execute_query_with_timeout", "line": 72}
{"timestamp": "2025-06-12T17:34:22.257589", "level": "DEBUG", "logger": "backend.services.auth", "message": "Authentication attempt with token prefix: eyJhbGciOi...", "module": "auth", "function": "get_current_user", "line": 727}
{"timestamp": "2025-06-12T17:34:22.257705", "level": "DEBUG", "logger": "backend.core.security.jwt_keys", "message": "Token contains key ID: 0bf271b1-2fbf-4d96-987f-e0b06b2fd890", "module": "jwt_keys", "function": "decode_token", "line": 188}
{"timestamp": "2025-06-12T17:34:22.257776", "level": "DEBUG", "logger": "backend.core.security.jwt_keys", "message": "Using key ID 0bf271b1-2fbf-4d96-987f-e0b06b2fd890 for verification", "module": "jwt_keys", "function": "decode_token", "line": 195}
{"timestamp": "2025-06-12T17:34:22.257908", "level": "DEBUG", "logger": "backend.services.auth", "message": "JWT decoded successfully with key manager", "module": "auth", "function": "authenticate_token", "line": 683}
{"timestamp": "2025-06-12T17:34:22.259549", "level": "DEBUG", "logger": "backend.services.db_utils", "message": "Query 'auth_lookup_e4ae2d8f-b32f-52d7-99cd-8a33b19652c7' completed in 0.001s", "module": "db_utils", "function": "execute_query_with_timeout", "line": 68}
{"timestamp": "2025-06-12T17:34:22.259604", "level": "DEBUG", "logger": "backend.services.db_utils", "message": "Auth query 'auth_lookup_e4ae2d8f-b32f-52d7-99cd-8a33b19652c7' successful, result format preserved", "module": "db_utils", "function": "execute_query_with_timeout", "line": 72}
{"timestamp": "2025-06-12T17:34:22.259663", "level": "DEBUG", "logger": "backend.services.auth", "message": "Successfully authenticated user: <EMAIL>", "module": "auth", "function": "get_current_user", "line": 732}
{"timestamp": "2025-06-12T17:34:22.259701", "level": "DEBUG", "logger": "backend.services.auth", "message": "Successfully authenticated user: <EMAIL>", "module": "auth", "function": "get_current_user", "line": 738}
{"timestamp": "2025-06-12T17:34:22.262048", "level": "DEBUG", "logger": "backend.routes.qr_preview", "message": "Received QR preview SVG request: {\"qr_type\": \"scooter_unlock\", \"data\": {\"scooter_id\": \"SCOOT123\", \"unlock_code\": \"UNLOCK42\", \"title\": \"Scooter_unlock QR Code\", \"description\": \"Scan to view scooter_unlock content\", \"service_provider\": \"Lime\", \"rate_info\": \"$20\"}, \"designOptions\": {\"size\": 256, \"margin\": 10, \"pattern\": \"square\", \"marker_border_style\": \"square\", \"marker_center_style\": \"square\", \"logo_size\": 0.3, \"logo_position\": \"center\", \"logo_opacity\": 1, \"logo_remove_background\": true, \"frame_style\": \"none\", \"frame_font\": \"SF P", "module": "qr_preview", "function": "preview_qr_svg", "line": 242}
{"timestamp": "2025-06-12T17:34:22.262277", "level": "DEBUG", "logger": "backend.routes.qr_preview", "message": "Form data keys: ['request', 'logo']", "module": "qr_preview", "function": "preview_qr_svg", "line": 297}
{"timestamp": "2025-06-12T17:34:22.262322", "level": "DEBUG", "logger": "backend.routes.qr_preview", "message": "Logo file found: type=<class 'starlette.datastructures.UploadFile'>, filename=LOGO - 1080 - BLACK-01.png", "module": "qr_preview", "function": "preview_qr_svg", "line": 300}
{"timestamp": "2025-06-12T17:34:22.262360", "level": "DEBUG", "logger": "backend.routes.qr_preview", "message": "Logo content read: 142252 bytes", "module": "qr_preview", "function": "preview_qr_svg", "line": 305}
{"timestamp": "2025-06-12T17:34:22.262448", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Formatting content for QR type: qrtype.scooter_unlock, content keys: ['scooter_id', 'unlock_code', 'title', 'description', 'service_provider', 'rate_info']", "module": "qr_preview", "function": "_format_qr_data", "line": 876}
{"timestamp": "2025-06-12T17:34:22.289213", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Logo remove background setting: True, has logo: True", "module": "qr_preview", "function": "generate_preview_svg_modular", "line": 727}
{"timestamp": "2025-06-12T17:34:22.289289", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Logo area calculated: (20, 20, 37, 37), size_fraction: 0.3, size_modules: 17", "module": "qr_preview", "function": "generate_preview_svg_modular", "line": 739}
{"timestamp": "2025-06-12T17:34:22.306688", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Skipped 151 modules out of 1668 total modules for logo background removal", "module": "qr_preview", "function": "generate_preview_svg_modular", "line": 766}
{"timestamp": "2025-06-12T17:34:22.306784", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Drawing 3 finder patterns at positions: [(0, 0), (50, 0), (0, 50)]", "module": "qr_preview", "function": "generate_preview_svg_modular", "line": 775}
{"timestamp": "2025-06-12T17:34:22.306824", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "QR size: 57, border_style: square, center_style: square", "module": "qr_preview", "function": "generate_preview_svg_modular", "line": 776}
{"timestamp": "2025-06-12T17:34:22.306860", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Drawing finder pattern 1/3 at position (0, 0)", "module": "qr_preview", "function": "generate_preview_svg_modular", "line": 780}
{"timestamp": "2025-06-12T17:34:22.306926", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "_draw_finder_marker called: mx=0, my=0, module_size=5.087719298245614, border_style=square, center_style=square, border_color=#000000, center_color=#000000, offset=(20, 20)", "module": "qr_preview", "function": "_draw_finder_marker", "line": 97}
{"timestamp": "2025-06-12T17:34:22.306989", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Creating finder pattern group with id='marker-0-0'", "module": "qr_preview", "function": "_draw_finder_marker", "line": 110}
{"timestamp": "2025-06-12T17:34:22.307025", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Drawing square border:", "module": "qr_preview", "function": "_draw_finder_marker", "line": 160}
{"timestamp": "2025-06-12T17:34:22.307059", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "  Outer: insert=(20.0, 20.0), size=(35.614035087719294, 35.614035087719294)", "module": "qr_preview", "function": "_draw_finder_marker", "line": 161}
{"timestamp": "2025-06-12T17:34:22.307093", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "  Inner: insert=(26.105263157894736, 26.105263157894736), size=(23.403508771929822, 23.403508771929822)", "module": "qr_preview", "function": "_draw_finder_marker", "line": 162}
{"timestamp": "2025-06-12T17:34:22.307173", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Drawing marker center: style=square, color=#000000, pos=(32.719298245614034, 32.719298245614034), size=10.175438596491228", "module": "qr_preview", "function": "_draw_marker_center", "line": 179}
{"timestamp": "2025-06-12T17:34:22.307234", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Successfully added finder pattern group to SVG", "module": "qr_preview", "function": "_draw_finder_marker", "line": 171}
{"timestamp": "2025-06-12T17:34:22.307267", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Successfully drew finder pattern 1/3", "module": "qr_preview", "function": "generate_preview_svg_modular", "line": 782}
{"timestamp": "2025-06-12T17:34:22.307300", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Drawing finder pattern 2/3 at position (50, 0)", "module": "qr_preview", "function": "generate_preview_svg_modular", "line": 780}
{"timestamp": "2025-06-12T17:34:22.307332", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "_draw_finder_marker called: mx=50, my=0, module_size=5.087719298245614, border_style=square, center_style=square, border_color=#000000, center_color=#000000, offset=(20, 20)", "module": "qr_preview", "function": "_draw_finder_marker", "line": 97}
{"timestamp": "2025-06-12T17:34:22.307370", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Creating finder pattern group with id='marker-50-0'", "module": "qr_preview", "function": "_draw_finder_marker", "line": 110}
{"timestamp": "2025-06-12T17:34:22.307400", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Drawing square border:", "module": "qr_preview", "function": "_draw_finder_marker", "line": 160}
{"timestamp": "2025-06-12T17:34:22.307451", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "  Outer: insert=(274.38596491228066, 20.0), size=(35.614035087719294, 35.614035087719294)", "module": "qr_preview", "function": "_draw_finder_marker", "line": 161}
{"timestamp": "2025-06-12T17:34:22.307486", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "  Inner: insert=(280.4912280701754, 26.105263157894736), size=(23.403508771929822, 23.403508771929822)", "module": "qr_preview", "function": "_draw_finder_marker", "line": 162}
{"timestamp": "2025-06-12T17:34:22.307543", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Drawing marker center: style=square, color=#000000, pos=(287.10526315789474, 32.719298245614034), size=10.175438596491228", "module": "qr_preview", "function": "_draw_marker_center", "line": 179}
{"timestamp": "2025-06-12T17:34:22.307591", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Successfully added finder pattern group to SVG", "module": "qr_preview", "function": "_draw_finder_marker", "line": 171}
{"timestamp": "2025-06-12T17:34:22.307621", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Successfully drew finder pattern 2/3", "module": "qr_preview", "function": "generate_preview_svg_modular", "line": 782}
{"timestamp": "2025-06-12T17:34:22.307655", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Drawing finder pattern 3/3 at position (0, 50)", "module": "qr_preview", "function": "generate_preview_svg_modular", "line": 780}
{"timestamp": "2025-06-12T17:34:22.307687", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "_draw_finder_marker called: mx=0, my=50, module_size=5.087719298245614, border_style=square, center_style=square, border_color=#000000, center_color=#000000, offset=(20, 20)", "module": "qr_preview", "function": "_draw_finder_marker", "line": 97}
{"timestamp": "2025-06-12T17:34:22.307724", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Creating finder pattern group with id='marker-0-50'", "module": "qr_preview", "function": "_draw_finder_marker", "line": 110}
{"timestamp": "2025-06-12T17:34:22.307753", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Drawing square border:", "module": "qr_preview", "function": "_draw_finder_marker", "line": 160}
{"timestamp": "2025-06-12T17:34:22.307785", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "  Outer: insert=(20.0, 274.38596491228066), size=(35.614035087719294, 35.614035087719294)", "module": "qr_preview", "function": "_draw_finder_marker", "line": 161}
{"timestamp": "2025-06-12T17:34:22.307820", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "  Inner: insert=(26.105263157894736, 280.4912280701754), size=(23.403508771929822, 23.403508771929822)", "module": "qr_preview", "function": "_draw_finder_marker", "line": 162}
{"timestamp": "2025-06-12T17:34:22.307875", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Drawing marker center: style=square, color=#000000, pos=(32.719298245614034, 287.10526315789474), size=10.175438596491228", "module": "qr_preview", "function": "_draw_marker_center", "line": 179}
{"timestamp": "2025-06-12T17:34:22.307919", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Successfully added finder pattern group to SVG", "module": "qr_preview", "function": "_draw_finder_marker", "line": 171}
{"timestamp": "2025-06-12T17:34:22.307949", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Successfully drew finder pattern 3/3", "module": "qr_preview", "function": "generate_preview_svg_modular", "line": 782}
{"timestamp": "2025-06-12T17:34:22.308011", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "_draw_logo called with logo type: <class 'bytes'>, dimensions: 300", "module": "qr_preview", "function": "_draw_logo", "line": 434}
{"timestamp": "2025-06-12T17:34:22.663367", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Generated SVG length: 228281", "module": "qr_preview", "function": "generate_preview_svg_modular", "line": 802}
{"timestamp": "2025-06-12T17:34:22.663710", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "SVG contains marker-0-0: True", "module": "qr_preview", "function": "generate_preview_svg_modular", "line": 803}
{"timestamp": "2025-06-12T17:34:22.663834", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "SVG contains marker-18-0: False", "module": "qr_preview", "function": "generate_preview_svg_modular", "line": 804}
{"timestamp": "2025-06-12T17:34:22.663956", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "SVG contains marker-0-18: False", "module": "qr_preview", "function": "generate_preview_svg_modular", "line": 805}
{"timestamp": "2025-06-12T17:34:22.664317", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "SVG marker counts - outer: 3, inner: 3, dot: 0", "module": "qr_preview", "function": "generate_preview_svg_modular", "line": 813}
{"timestamp": "2025-06-12T17:34:22.664347", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "SVG label counts - text: 0, background: 0", "module": "qr_preview", "function": "generate_preview_svg_modular", "line": 814}
{"timestamp": "2025-06-12T17:34:22.666969", "level": "DEBUG", "logger": "backend.main", "message": "[Auth] Enforced Bearer token authentication for request", "module": "main", "function": "bearer_token_auth_middleware", "line": 274}
{"timestamp": "2025-06-12T17:34:23.509790", "level": "DEBUG", "logger": "backend.core.security.jwt_keys", "message": "Token contains key ID: 0bf271b1-2fbf-4d96-987f-e0b06b2fd890", "module": "jwt_keys", "function": "decode_token", "line": 188}
{"timestamp": "2025-06-12T17:34:23.509858", "level": "DEBUG", "logger": "backend.core.security.jwt_keys", "message": "Using key ID 0bf271b1-2fbf-4d96-987f-e0b06b2fd890 for verification", "module": "jwt_keys", "function": "decode_token", "line": 195}
{"timestamp": "2025-06-12T17:34:23.509987", "level": "DEBUG", "logger": "backend.services.auth", "message": "JWT decoded successfully with key manager", "module": "auth", "function": "authenticate_token", "line": 683}
{"timestamp": "2025-06-12T17:34:23.512905", "level": "DEBUG", "logger": "backend.services.db_utils", "message": "Query 'auth_lookup_e4ae2d8f-b32f-52d7-99cd-8a33b19652c7' completed in 0.003s", "module": "db_utils", "function": "execute_query_with_timeout", "line": 68}
{"timestamp": "2025-06-12T17:34:23.512980", "level": "DEBUG", "logger": "backend.services.db_utils", "message": "Auth query 'auth_lookup_e4ae2d8f-b32f-52d7-99cd-8a33b19652c7' successful, result format preserved", "module": "db_utils", "function": "execute_query_with_timeout", "line": 72}
{"timestamp": "2025-06-12T17:34:23.518662", "level": "DEBUG", "logger": "backend.services.auth", "message": "Authentication attempt with token prefix: eyJhbGciOi...", "module": "auth", "function": "get_current_user", "line": 727}
{"timestamp": "2025-06-12T17:34:23.518793", "level": "DEBUG", "logger": "backend.core.security.jwt_keys", "message": "Token contains key ID: 0bf271b1-2fbf-4d96-987f-e0b06b2fd890", "module": "jwt_keys", "function": "decode_token", "line": 188}
{"timestamp": "2025-06-12T17:34:23.518842", "level": "DEBUG", "logger": "backend.core.security.jwt_keys", "message": "Using key ID 0bf271b1-2fbf-4d96-987f-e0b06b2fd890 for verification", "module": "jwt_keys", "function": "decode_token", "line": 195}
{"timestamp": "2025-06-12T17:34:23.518988", "level": "DEBUG", "logger": "backend.services.auth", "message": "JWT decoded successfully with key manager", "module": "auth", "function": "authenticate_token", "line": 683}
{"timestamp": "2025-06-12T17:34:23.520257", "level": "DEBUG", "logger": "backend.services.db_utils", "message": "Query 'auth_lookup_e4ae2d8f-b32f-52d7-99cd-8a33b19652c7' completed in 0.001s", "module": "db_utils", "function": "execute_query_with_timeout", "line": 68}
{"timestamp": "2025-06-12T17:34:23.520324", "level": "DEBUG", "logger": "backend.services.db_utils", "message": "Auth query 'auth_lookup_e4ae2d8f-b32f-52d7-99cd-8a33b19652c7' successful, result format preserved", "module": "db_utils", "function": "execute_query_with_timeout", "line": 72}
{"timestamp": "2025-06-12T17:34:23.520381", "level": "DEBUG", "logger": "backend.services.auth", "message": "Successfully authenticated user: <EMAIL>", "module": "auth", "function": "get_current_user", "line": 732}
{"timestamp": "2025-06-12T17:34:23.520416", "level": "DEBUG", "logger": "backend.services.auth", "message": "Successfully authenticated user: <EMAIL>", "module": "auth", "function": "get_current_user", "line": 738}
{"timestamp": "2025-06-12T17:34:23.523180", "level": "DEBUG", "logger": "backend.routes.qr_preview", "message": "Received QR preview SVG request: {\"qr_type\": \"scooter_unlock\", \"data\": {\"scooter_id\": \"SCOOT123\", \"unlock_code\": \"UNLOCK42\", \"title\": \"Scooter_unlock QR Code\", \"description\": \"Scan to view scooter_unlock content\", \"service_provider\": \"Lime\", \"rate_info\": \"$20\"}, \"designOptions\": {\"size\": 256, \"margin\": 10, \"pattern\": \"square\", \"marker_border_style\": \"square\", \"marker_border_color\": \"#000000\", \"marker_center_style\": \"square\", \"marker_center_color\": \"#000000\", \"logo_size\": 0.3, \"logo_position\": \"center\", \"logo_opacity\": 1, \"logo_", "module": "qr_preview", "function": "preview_qr_svg", "line": 242}
{"timestamp": "2025-06-12T17:34:23.523336", "level": "DEBUG", "logger": "backend.routes.qr_preview", "message": "Form data keys: ['request', 'logo']", "module": "qr_preview", "function": "preview_qr_svg", "line": 297}
{"timestamp": "2025-06-12T17:34:23.523388", "level": "DEBUG", "logger": "backend.routes.qr_preview", "message": "Logo file found: type=<class 'starlette.datastructures.UploadFile'>, filename=LOGO - 1080 - BLACK-01.png", "module": "qr_preview", "function": "preview_qr_svg", "line": 300}
{"timestamp": "2025-06-12T17:34:23.523438", "level": "DEBUG", "logger": "backend.routes.qr_preview", "message": "Logo content read: 142252 bytes", "module": "qr_preview", "function": "preview_qr_svg", "line": 305}
{"timestamp": "2025-06-12T17:34:23.523492", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Formatting content for QR type: qrtype.scooter_unlock, content keys: ['scooter_id', 'unlock_code', 'title', 'description', 'service_provider', 'rate_info']", "module": "qr_preview", "function": "_format_qr_data", "line": 876}
{"timestamp": "2025-06-12T17:34:23.546957", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Logo remove background setting: True, has logo: True", "module": "qr_preview", "function": "generate_preview_svg_modular", "line": 727}
{"timestamp": "2025-06-12T17:34:23.547030", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Logo area calculated: (20, 20, 37, 37), size_fraction: 0.3, size_modules: 17", "module": "qr_preview", "function": "generate_preview_svg_modular", "line": 739}
{"timestamp": "2025-06-12T17:34:23.563789", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Skipped 151 modules out of 1668 total modules for logo background removal", "module": "qr_preview", "function": "generate_preview_svg_modular", "line": 766}
{"timestamp": "2025-06-12T17:34:23.563862", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Drawing 3 finder patterns at positions: [(0, 0), (50, 0), (0, 50)]", "module": "qr_preview", "function": "generate_preview_svg_modular", "line": 775}
{"timestamp": "2025-06-12T17:34:23.563911", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "QR size: 57, border_style: square, center_style: square", "module": "qr_preview", "function": "generate_preview_svg_modular", "line": 776}
{"timestamp": "2025-06-12T17:34:23.564023", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Drawing finder pattern 1/3 at position (0, 0)", "module": "qr_preview", "function": "generate_preview_svg_modular", "line": 780}
{"timestamp": "2025-06-12T17:34:23.564220", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "_draw_finder_marker called: mx=0, my=0, module_size=5.087719298245614, border_style=square, center_style=square, border_color=#000000, center_color=#000000, offset=(20, 20)", "module": "qr_preview", "function": "_draw_finder_marker", "line": 97}
{"timestamp": "2025-06-12T17:34:23.564313", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Creating finder pattern group with id='marker-0-0'", "module": "qr_preview", "function": "_draw_finder_marker", "line": 110}
{"timestamp": "2025-06-12T17:34:23.564366", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Drawing square border:", "module": "qr_preview", "function": "_draw_finder_marker", "line": 160}
{"timestamp": "2025-06-12T17:34:23.564407", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "  Outer: insert=(20.0, 20.0), size=(35.614035087719294, 35.614035087719294)", "module": "qr_preview", "function": "_draw_finder_marker", "line": 161}
{"timestamp": "2025-06-12T17:34:23.564454", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "  Inner: insert=(26.105263157894736, 26.105263157894736), size=(23.403508771929822, 23.403508771929822)", "module": "qr_preview", "function": "_draw_finder_marker", "line": 162}
{"timestamp": "2025-06-12T17:34:23.564651", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Drawing marker center: style=square, color=#000000, pos=(32.719298245614034, 32.719298245614034), size=10.175438596491228", "module": "qr_preview", "function": "_draw_marker_center", "line": 179}
{"timestamp": "2025-06-12T17:34:23.564747", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Successfully added finder pattern group to SVG", "module": "qr_preview", "function": "_draw_finder_marker", "line": 171}
{"timestamp": "2025-06-12T17:34:23.564790", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Successfully drew finder pattern 1/3", "module": "qr_preview", "function": "generate_preview_svg_modular", "line": 782}
{"timestamp": "2025-06-12T17:34:23.564829", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Drawing finder pattern 2/3 at position (50, 0)", "module": "qr_preview", "function": "generate_preview_svg_modular", "line": 780}
{"timestamp": "2025-06-12T17:34:23.564881", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "_draw_finder_marker called: mx=50, my=0, module_size=5.087719298245614, border_style=square, center_style=square, border_color=#000000, center_color=#000000, offset=(20, 20)", "module": "qr_preview", "function": "_draw_finder_marker", "line": 97}
{"timestamp": "2025-06-12T17:34:23.564929", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Creating finder pattern group with id='marker-50-0'", "module": "qr_preview", "function": "_draw_finder_marker", "line": 110}
{"timestamp": "2025-06-12T17:34:23.564965", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Drawing square border:", "module": "qr_preview", "function": "_draw_finder_marker", "line": 160}
{"timestamp": "2025-06-12T17:34:23.565002", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "  Outer: insert=(274.38596491228066, 20.0), size=(35.614035087719294, 35.614035087719294)", "module": "qr_preview", "function": "_draw_finder_marker", "line": 161}
{"timestamp": "2025-06-12T17:34:23.565040", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "  Inner: insert=(280.4912280701754, 26.105263157894736), size=(23.403508771929822, 23.403508771929822)", "module": "qr_preview", "function": "_draw_finder_marker", "line": 162}
{"timestamp": "2025-06-12T17:34:23.565110", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Drawing marker center: style=square, color=#000000, pos=(287.10526315789474, 32.719298245614034), size=10.175438596491228", "module": "qr_preview", "function": "_draw_marker_center", "line": 179}
{"timestamp": "2025-06-12T17:34:23.565192", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Successfully added finder pattern group to SVG", "module": "qr_preview", "function": "_draw_finder_marker", "line": 171}
{"timestamp": "2025-06-12T17:34:23.565231", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Successfully drew finder pattern 2/3", "module": "qr_preview", "function": "generate_preview_svg_modular", "line": 782}
{"timestamp": "2025-06-12T17:34:23.565268", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Drawing finder pattern 3/3 at position (0, 50)", "module": "qr_preview", "function": "generate_preview_svg_modular", "line": 780}
{"timestamp": "2025-06-12T17:34:23.565307", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "_draw_finder_marker called: mx=0, my=50, module_size=5.087719298245614, border_style=square, center_style=square, border_color=#000000, center_color=#000000, offset=(20, 20)", "module": "qr_preview", "function": "_draw_finder_marker", "line": 97}
{"timestamp": "2025-06-12T17:34:23.565352", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Creating finder pattern group with id='marker-0-50'", "module": "qr_preview", "function": "_draw_finder_marker", "line": 110}
{"timestamp": "2025-06-12T17:34:23.565394", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Drawing square border:", "module": "qr_preview", "function": "_draw_finder_marker", "line": 160}
{"timestamp": "2025-06-12T17:34:23.565441", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "  Outer: insert=(20.0, 274.38596491228066), size=(35.614035087719294, 35.614035087719294)", "module": "qr_preview", "function": "_draw_finder_marker", "line": 161}
{"timestamp": "2025-06-12T17:34:23.565482", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "  Inner: insert=(26.105263157894736, 280.4912280701754), size=(23.403508771929822, 23.403508771929822)", "module": "qr_preview", "function": "_draw_finder_marker", "line": 162}
{"timestamp": "2025-06-12T17:34:23.565547", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Drawing marker center: style=square, color=#000000, pos=(32.719298245614034, 287.10526315789474), size=10.175438596491228", "module": "qr_preview", "function": "_draw_marker_center", "line": 179}
{"timestamp": "2025-06-12T17:34:23.565599", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Successfully added finder pattern group to SVG", "module": "qr_preview", "function": "_draw_finder_marker", "line": 171}
{"timestamp": "2025-06-12T17:34:23.565633", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Successfully drew finder pattern 3/3", "module": "qr_preview", "function": "generate_preview_svg_modular", "line": 782}
{"timestamp": "2025-06-12T17:34:23.565675", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "_draw_logo called with logo type: <class 'bytes'>, dimensions: 300", "module": "qr_preview", "function": "_draw_logo", "line": 434}
{"timestamp": "2025-06-12T17:34:23.902554", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Generated SVG length: 228281", "module": "qr_preview", "function": "generate_preview_svg_modular", "line": 802}
{"timestamp": "2025-06-12T17:34:23.902702", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "SVG contains marker-0-0: True", "module": "qr_preview", "function": "generate_preview_svg_modular", "line": 803}
{"timestamp": "2025-06-12T17:34:23.902821", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "SVG contains marker-18-0: False", "module": "qr_preview", "function": "generate_preview_svg_modular", "line": 804}
{"timestamp": "2025-06-12T17:34:23.902948", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "SVG contains marker-0-18: False", "module": "qr_preview", "function": "generate_preview_svg_modular", "line": 805}
{"timestamp": "2025-06-12T17:34:23.903339", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "SVG marker counts - outer: 3, inner: 3, dot: 0", "module": "qr_preview", "function": "generate_preview_svg_modular", "line": 813}
{"timestamp": "2025-06-12T17:34:23.903369", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "SVG label counts - text: 0, background: 0", "module": "qr_preview", "function": "generate_preview_svg_modular", "line": 814}
{"timestamp": "2025-06-12T17:34:23.905761", "level": "DEBUG", "logger": "backend.main", "message": "[Auth] Enforced Bearer token authentication for request", "module": "main", "function": "bearer_token_auth_middleware", "line": 274}
{"timestamp": "2025-06-12T17:34:29.078435", "level": "DEBUG", "logger": "backend.main", "message": "[Auth] Enforced Bearer token authentication for request", "module": "main", "function": "bearer_token_auth_middleware", "line": 274}
{"timestamp": "2025-06-12T17:34:29.101958", "level": "DEBUG", "logger": "backend.main", "message": "[Auth] Enforced Bearer token authentication for request", "module": "main", "function": "bearer_token_auth_middleware", "line": 274}
{"timestamp": "2025-06-12T17:34:30.592080", "level": "DEBUG", "logger": "backend.core.security.jwt_keys", "message": "Token contains key ID: 0bf271b1-2fbf-4d96-987f-e0b06b2fd890", "module": "jwt_keys", "function": "decode_token", "line": 188}
{"timestamp": "2025-06-12T17:34:30.592156", "level": "DEBUG", "logger": "backend.core.security.jwt_keys", "message": "Using key ID 0bf271b1-2fbf-4d96-987f-e0b06b2fd890 for verification", "module": "jwt_keys", "function": "decode_token", "line": 195}
{"timestamp": "2025-06-12T17:34:30.592617", "level": "DEBUG", "logger": "backend.services.auth", "message": "JWT decoded successfully with key manager", "module": "auth", "function": "authenticate_token", "line": 683}
{"timestamp": "2025-06-12T17:34:30.598618", "level": "DEBUG", "logger": "backend.services.db_utils", "message": "Query 'auth_lookup_e4ae2d8f-b32f-52d7-99cd-8a33b19652c7' completed in 0.005s", "module": "db_utils", "function": "execute_query_with_timeout", "line": 68}
{"timestamp": "2025-06-12T17:34:30.598678", "level": "DEBUG", "logger": "backend.services.db_utils", "message": "Auth query 'auth_lookup_e4ae2d8f-b32f-52d7-99cd-8a33b19652c7' successful, result format preserved", "module": "db_utils", "function": "execute_query_with_timeout", "line": 72}
{"timestamp": "2025-06-12T17:34:30.605334", "level": "DEBUG", "logger": "backend.services.auth", "message": "Authentication attempt with token prefix: eyJhbGciOi...", "module": "auth", "function": "get_current_user", "line": 727}
{"timestamp": "2025-06-12T17:34:30.605438", "level": "DEBUG", "logger": "backend.core.security.jwt_keys", "message": "Token contains key ID: 0bf271b1-2fbf-4d96-987f-e0b06b2fd890", "module": "jwt_keys", "function": "decode_token", "line": 188}
{"timestamp": "2025-06-12T17:34:30.605479", "level": "DEBUG", "logger": "backend.core.security.jwt_keys", "message": "Using key ID 0bf271b1-2fbf-4d96-987f-e0b06b2fd890 for verification", "module": "jwt_keys", "function": "decode_token", "line": 195}
{"timestamp": "2025-06-12T17:34:30.605595", "level": "DEBUG", "logger": "backend.services.auth", "message": "JWT decoded successfully with key manager", "module": "auth", "function": "authenticate_token", "line": 683}
{"timestamp": "2025-06-12T17:34:30.607301", "level": "DEBUG", "logger": "backend.services.db_utils", "message": "Query 'auth_lookup_e4ae2d8f-b32f-52d7-99cd-8a33b19652c7' completed in 0.002s", "module": "db_utils", "function": "execute_query_with_timeout", "line": 68}
{"timestamp": "2025-06-12T17:34:30.607343", "level": "DEBUG", "logger": "backend.services.db_utils", "message": "Auth query 'auth_lookup_e4ae2d8f-b32f-52d7-99cd-8a33b19652c7' successful, result format preserved", "module": "db_utils", "function": "execute_query_with_timeout", "line": 72}
{"timestamp": "2025-06-12T17:34:30.607398", "level": "DEBUG", "logger": "backend.services.auth", "message": "Successfully authenticated user: <EMAIL>", "module": "auth", "function": "get_current_user", "line": 732}
{"timestamp": "2025-06-12T17:34:30.607477", "level": "DEBUG", "logger": "backend.services.auth", "message": "Successfully authenticated user: <EMAIL>", "module": "auth", "function": "get_current_user", "line": 738}
{"timestamp": "2025-06-12T17:34:30.609335", "level": "DEBUG", "logger": "backend.routes.qr_preview", "message": "Received QR preview SVG request: {\"qr_type\": \"scooter_unlock\", \"data\": {\"scooter_id\": \"SCOOT123\", \"unlock_code\": \"UNLOCK42\", \"title\": \"Scooter_unlock QR Code\", \"description\": \"Scan to view scooter_unlock content\", \"service_provider\": \"Lime\", \"rate_info\": \"$20\"}, \"designOptions\": {\"size\": 256, \"margin\": 10, \"pattern\": \"square\", \"marker_border_style\": \"square\", \"marker_center_style\": \"square\", \"logo_size\": 0.3, \"logo_position\": \"center\", \"logo_opacity\": 1, \"logo_remove_background\": true, \"frame_style\": \"none\", \"frame_font\": \"SF P", "module": "qr_preview", "function": "preview_qr_svg", "line": 242}
{"timestamp": "2025-06-12T17:34:30.609526", "level": "DEBUG", "logger": "backend.routes.qr_preview", "message": "Form data keys: ['request']", "module": "qr_preview", "function": "preview_qr_svg", "line": 297}
{"timestamp": "2025-06-12T17:34:30.609840", "level": "DEBUG", "logger": "backend.routes.qr_preview", "message": "No logo key found in form data", "module": "qr_preview", "function": "preview_qr_svg", "line": 309}
{"timestamp": "2025-06-12T17:34:30.609936", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Formatting content for QR type: qrtype.scooter_unlock, content keys: ['scooter_id', 'unlock_code', 'title', 'description', 'service_provider', 'rate_info']", "module": "qr_preview", "function": "_format_qr_data", "line": 876}
{"timestamp": "2025-06-12T17:34:30.632847", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Logo remove background setting: True, has logo: False", "module": "qr_preview", "function": "generate_preview_svg_modular", "line": 727}
{"timestamp": "2025-06-12T17:34:30.650862", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Drawing 3 finder patterns at positions: [(0, 0), (50, 0), (0, 50)]", "module": "qr_preview", "function": "generate_preview_svg_modular", "line": 775}
{"timestamp": "2025-06-12T17:34:30.650917", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "QR size: 57, border_style: square, center_style: square", "module": "qr_preview", "function": "generate_preview_svg_modular", "line": 776}
{"timestamp": "2025-06-12T17:34:30.650951", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Drawing finder pattern 1/3 at position (0, 0)", "module": "qr_preview", "function": "generate_preview_svg_modular", "line": 780}
{"timestamp": "2025-06-12T17:34:30.651008", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "_draw_finder_marker called: mx=0, my=0, module_size=5.087719298245614, border_style=square, center_style=square, border_color=#000000, center_color=#000000, offset=(20, 20)", "module": "qr_preview", "function": "_draw_finder_marker", "line": 97}
{"timestamp": "2025-06-12T17:34:30.651064", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Creating finder pattern group with id='marker-0-0'", "module": "qr_preview", "function": "_draw_finder_marker", "line": 110}
{"timestamp": "2025-06-12T17:34:30.651096", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Drawing square border:", "module": "qr_preview", "function": "_draw_finder_marker", "line": 160}
{"timestamp": "2025-06-12T17:34:30.651427", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "  Outer: insert=(20.0, 20.0), size=(35.614035087719294, 35.614035087719294)", "module": "qr_preview", "function": "_draw_finder_marker", "line": 161}
{"timestamp": "2025-06-12T17:34:30.651460", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "  Inner: insert=(26.105263157894736, 26.105263157894736), size=(23.403508771929822, 23.403508771929822)", "module": "qr_preview", "function": "_draw_finder_marker", "line": 162}
{"timestamp": "2025-06-12T17:34:30.651554", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Drawing marker center: style=square, color=#000000, pos=(32.719298245614034, 32.719298245614034), size=10.175438596491228", "module": "qr_preview", "function": "_draw_marker_center", "line": 179}
{"timestamp": "2025-06-12T17:34:30.651611", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Successfully added finder pattern group to SVG", "module": "qr_preview", "function": "_draw_finder_marker", "line": 171}
{"timestamp": "2025-06-12T17:34:30.651819", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Successfully drew finder pattern 1/3", "module": "qr_preview", "function": "generate_preview_svg_modular", "line": 782}
{"timestamp": "2025-06-12T17:34:30.651850", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Drawing finder pattern 2/3 at position (50, 0)", "module": "qr_preview", "function": "generate_preview_svg_modular", "line": 780}
{"timestamp": "2025-06-12T17:34:30.651878", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "_draw_finder_marker called: mx=50, my=0, module_size=5.087719298245614, border_style=square, center_style=square, border_color=#000000, center_color=#000000, offset=(20, 20)", "module": "qr_preview", "function": "_draw_finder_marker", "line": 97}
{"timestamp": "2025-06-12T17:34:30.651914", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Creating finder pattern group with id='marker-50-0'", "module": "qr_preview", "function": "_draw_finder_marker", "line": 110}
{"timestamp": "2025-06-12T17:34:30.651941", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Drawing square border:", "module": "qr_preview", "function": "_draw_finder_marker", "line": 160}
{"timestamp": "2025-06-12T17:34:30.651974", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "  Outer: insert=(274.38596491228066, 20.0), size=(35.614035087719294, 35.614035087719294)", "module": "qr_preview", "function": "_draw_finder_marker", "line": 161}
{"timestamp": "2025-06-12T17:34:30.654244", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "  Inner: insert=(280.4912280701754, 26.105263157894736), size=(23.403508771929822, 23.403508771929822)", "module": "qr_preview", "function": "_draw_finder_marker", "line": 162}
{"timestamp": "2025-06-12T17:34:30.654303", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Drawing marker center: style=square, color=#000000, pos=(287.10526315789474, 32.719298245614034), size=10.175438596491228", "module": "qr_preview", "function": "_draw_marker_center", "line": 179}
{"timestamp": "2025-06-12T17:34:30.654346", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Successfully added finder pattern group to SVG", "module": "qr_preview", "function": "_draw_finder_marker", "line": 171}
{"timestamp": "2025-06-12T17:34:30.654375", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Successfully drew finder pattern 2/3", "module": "qr_preview", "function": "generate_preview_svg_modular", "line": 782}
{"timestamp": "2025-06-12T17:34:30.654748", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Drawing finder pattern 3/3 at position (0, 50)", "module": "qr_preview", "function": "generate_preview_svg_modular", "line": 780}
{"timestamp": "2025-06-12T17:34:30.654782", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "_draw_finder_marker called: mx=0, my=50, module_size=5.087719298245614, border_style=square, center_style=square, border_color=#000000, center_color=#000000, offset=(20, 20)", "module": "qr_preview", "function": "_draw_finder_marker", "line": 97}
{"timestamp": "2025-06-12T17:34:30.654818", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Creating finder pattern group with id='marker-0-50'", "module": "qr_preview", "function": "_draw_finder_marker", "line": 110}
{"timestamp": "2025-06-12T17:34:30.654860", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Drawing square border:", "module": "qr_preview", "function": "_draw_finder_marker", "line": 160}
{"timestamp": "2025-06-12T17:34:30.654890", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "  Outer: insert=(20.0, 274.38596491228066), size=(35.614035087719294, 35.614035087719294)", "module": "qr_preview", "function": "_draw_finder_marker", "line": 161}
{"timestamp": "2025-06-12T17:34:30.654921", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "  Inner: insert=(26.105263157894736, 280.4912280701754), size=(23.403508771929822, 23.403508771929822)", "module": "qr_preview", "function": "_draw_finder_marker", "line": 162}
{"timestamp": "2025-06-12T17:34:30.654977", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Drawing marker center: style=square, color=#000000, pos=(32.719298245614034, 287.10526315789474), size=10.175438596491228", "module": "qr_preview", "function": "_draw_marker_center", "line": 179}
{"timestamp": "2025-06-12T17:34:30.655019", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Successfully added finder pattern group to SVG", "module": "qr_preview", "function": "_draw_finder_marker", "line": 171}
{"timestamp": "2025-06-12T17:34:30.655051", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Successfully drew finder pattern 3/3", "module": "qr_preview", "function": "generate_preview_svg_modular", "line": 782}
{"timestamp": "2025-06-12T17:34:30.681135", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Generated SVG length: 247431", "module": "qr_preview", "function": "generate_preview_svg_modular", "line": 802}
{"timestamp": "2025-06-12T17:34:30.681280", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "SVG contains marker-0-0: True", "module": "qr_preview", "function": "generate_preview_svg_modular", "line": 803}
{"timestamp": "2025-06-12T17:34:30.681408", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "SVG contains marker-18-0: False", "module": "qr_preview", "function": "generate_preview_svg_modular", "line": 804}
{"timestamp": "2025-06-12T17:34:30.681534", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "SVG contains marker-0-18: False", "module": "qr_preview", "function": "generate_preview_svg_modular", "line": 805}
{"timestamp": "2025-06-12T17:34:30.681915", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "SVG marker counts - outer: 3, inner: 3, dot: 0", "module": "qr_preview", "function": "generate_preview_svg_modular", "line": 813}
{"timestamp": "2025-06-12T17:34:30.681946", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "SVG label counts - text: 0, background: 0", "module": "qr_preview", "function": "generate_preview_svg_modular", "line": 814}
{"timestamp": "2025-06-12T17:34:30.684198", "level": "DEBUG", "logger": "backend.main", "message": "[Auth] Enforced Bearer token authentication for request", "module": "main", "function": "bearer_token_auth_middleware", "line": 274}
{"timestamp": "2025-06-12T17:34:31.510488", "level": "DEBUG", "logger": "backend.core.security.jwt_keys", "message": "Token contains key ID: 0bf271b1-2fbf-4d96-987f-e0b06b2fd890", "module": "jwt_keys", "function": "decode_token", "line": 188}
{"timestamp": "2025-06-12T17:34:31.510561", "level": "DEBUG", "logger": "backend.core.security.jwt_keys", "message": "Using key ID 0bf271b1-2fbf-4d96-987f-e0b06b2fd890 for verification", "module": "jwt_keys", "function": "decode_token", "line": 195}
{"timestamp": "2025-06-12T17:34:31.510688", "level": "DEBUG", "logger": "backend.services.auth", "message": "JWT decoded successfully with key manager", "module": "auth", "function": "authenticate_token", "line": 683}
{"timestamp": "2025-06-12T17:34:31.512528", "level": "DEBUG", "logger": "backend.services.db_utils", "message": "Query 'auth_lookup_e4ae2d8f-b32f-52d7-99cd-8a33b19652c7' completed in 0.002s", "module": "db_utils", "function": "execute_query_with_timeout", "line": 68}
{"timestamp": "2025-06-12T17:34:31.512659", "level": "DEBUG", "logger": "backend.services.db_utils", "message": "Auth query 'auth_lookup_e4ae2d8f-b32f-52d7-99cd-8a33b19652c7' successful, result format preserved", "module": "db_utils", "function": "execute_query_with_timeout", "line": 72}
{"timestamp": "2025-06-12T17:34:31.523250", "level": "DEBUG", "logger": "backend.services.auth", "message": "Authentication attempt with token prefix: eyJhbGciOi...", "module": "auth", "function": "get_current_user", "line": 727}
{"timestamp": "2025-06-12T17:34:31.523493", "level": "DEBUG", "logger": "backend.core.security.jwt_keys", "message": "Token contains key ID: 0bf271b1-2fbf-4d96-987f-e0b06b2fd890", "module": "jwt_keys", "function": "decode_token", "line": 188}
{"timestamp": "2025-06-12T17:34:31.523539", "level": "DEBUG", "logger": "backend.core.security.jwt_keys", "message": "Using key ID 0bf271b1-2fbf-4d96-987f-e0b06b2fd890 for verification", "module": "jwt_keys", "function": "decode_token", "line": 195}
{"timestamp": "2025-06-12T17:34:31.523846", "level": "DEBUG", "logger": "backend.services.auth", "message": "JWT decoded successfully with key manager", "module": "auth", "function": "authenticate_token", "line": 683}
{"timestamp": "2025-06-12T17:34:31.525215", "level": "DEBUG", "logger": "backend.services.db_utils", "message": "Query 'auth_lookup_e4ae2d8f-b32f-52d7-99cd-8a33b19652c7' completed in 0.001s", "module": "db_utils", "function": "execute_query_with_timeout", "line": 68}
{"timestamp": "2025-06-12T17:34:31.525277", "level": "DEBUG", "logger": "backend.services.db_utils", "message": "Auth query 'auth_lookup_e4ae2d8f-b32f-52d7-99cd-8a33b19652c7' successful, result format preserved", "module": "db_utils", "function": "execute_query_with_timeout", "line": 72}
{"timestamp": "2025-06-12T17:34:31.525342", "level": "DEBUG", "logger": "backend.services.auth", "message": "Successfully authenticated user: <EMAIL>", "module": "auth", "function": "get_current_user", "line": 732}
{"timestamp": "2025-06-12T17:34:31.525385", "level": "DEBUG", "logger": "backend.services.auth", "message": "Successfully authenticated user: <EMAIL>", "module": "auth", "function": "get_current_user", "line": 738}
{"timestamp": "2025-06-12T17:34:31.527126", "level": "DEBUG", "logger": "backend.routes.qr_preview", "message": "Received QR preview SVG request: {\"qr_type\": \"scooter_unlock\", \"data\": {\"scooter_id\": \"SCOOT123\", \"unlock_code\": \"UNLOCK42\", \"title\": \"Scooter_unlock QR Code\", \"description\": \"Scan to view scooter_unlock content\", \"service_provider\": \"Lime\", \"rate_info\": \"$20\"}, \"designOptions\": {\"size\": 256, \"margin\": 10, \"pattern\": \"square\", \"marker_border_style\": \"square\", \"marker_border_color\": \"#000000\", \"marker_center_style\": \"square\", \"marker_center_color\": \"#000000\", \"logo_size\": 0.3, \"logo_position\": \"center\", \"logo_opacity\": 1, \"logo_", "module": "qr_preview", "function": "preview_qr_svg", "line": 242}
{"timestamp": "2025-06-12T17:34:31.527229", "level": "DEBUG", "logger": "backend.routes.qr_preview", "message": "Form data keys: ['request']", "module": "qr_preview", "function": "preview_qr_svg", "line": 297}
{"timestamp": "2025-06-12T17:34:31.527301", "level": "DEBUG", "logger": "backend.routes.qr_preview", "message": "No logo key found in form data", "module": "qr_preview", "function": "preview_qr_svg", "line": 309}
{"timestamp": "2025-06-12T17:34:31.527365", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Formatting content for QR type: qrtype.scooter_unlock, content keys: ['scooter_id', 'unlock_code', 'title', 'description', 'service_provider', 'rate_info']", "module": "qr_preview", "function": "_format_qr_data", "line": 876}
{"timestamp": "2025-06-12T17:34:31.550373", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Logo remove background setting: True, has logo: False", "module": "qr_preview", "function": "generate_preview_svg_modular", "line": 727}
{"timestamp": "2025-06-12T17:34:31.570436", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Drawing 3 finder patterns at positions: [(0, 0), (50, 0), (0, 50)]", "module": "qr_preview", "function": "generate_preview_svg_modular", "line": 775}
{"timestamp": "2025-06-12T17:34:31.570498", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "QR size: 57, border_style: square, center_style: square", "module": "qr_preview", "function": "generate_preview_svg_modular", "line": 776}
{"timestamp": "2025-06-12T17:34:31.570536", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Drawing finder pattern 1/3 at position (0, 0)", "module": "qr_preview", "function": "generate_preview_svg_modular", "line": 780}
{"timestamp": "2025-06-12T17:34:31.576244", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "_draw_finder_marker called: mx=0, my=0, module_size=5.087719298245614, border_style=square, center_style=square, border_color=#000000, center_color=#000000, offset=(20, 20)", "module": "qr_preview", "function": "_draw_finder_marker", "line": 97}
{"timestamp": "2025-06-12T17:34:31.576366", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Creating finder pattern group with id='marker-0-0'", "module": "qr_preview", "function": "_draw_finder_marker", "line": 110}
{"timestamp": "2025-06-12T17:34:31.576459", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Drawing square border:", "module": "qr_preview", "function": "_draw_finder_marker", "line": 160}
{"timestamp": "2025-06-12T17:34:31.576510", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "  Outer: insert=(20.0, 20.0), size=(35.614035087719294, 35.614035087719294)", "module": "qr_preview", "function": "_draw_finder_marker", "line": 161}
{"timestamp": "2025-06-12T17:34:31.576552", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "  Inner: insert=(26.105263157894736, 26.105263157894736), size=(23.403508771929822, 23.403508771929822)", "module": "qr_preview", "function": "_draw_finder_marker", "line": 162}
{"timestamp": "2025-06-12T17:34:31.576649", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Drawing marker center: style=square, color=#000000, pos=(32.719298245614034, 32.719298245614034), size=10.175438596491228", "module": "qr_preview", "function": "_draw_marker_center", "line": 179}
{"timestamp": "2025-06-12T17:34:31.576711", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Successfully added finder pattern group to SVG", "module": "qr_preview", "function": "_draw_finder_marker", "line": 171}
{"timestamp": "2025-06-12T17:34:31.576807", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Successfully drew finder pattern 1/3", "module": "qr_preview", "function": "generate_preview_svg_modular", "line": 782}
{"timestamp": "2025-06-12T17:34:31.576861", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Drawing finder pattern 2/3 at position (50, 0)", "module": "qr_preview", "function": "generate_preview_svg_modular", "line": 780}
{"timestamp": "2025-06-12T17:34:31.576909", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "_draw_finder_marker called: mx=50, my=0, module_size=5.087719298245614, border_style=square, center_style=square, border_color=#000000, center_color=#000000, offset=(20, 20)", "module": "qr_preview", "function": "_draw_finder_marker", "line": 97}
{"timestamp": "2025-06-12T17:34:31.576963", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Creating finder pattern group with id='marker-50-0'", "module": "qr_preview", "function": "_draw_finder_marker", "line": 110}
{"timestamp": "2025-06-12T17:34:31.577003", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Drawing square border:", "module": "qr_preview", "function": "_draw_finder_marker", "line": 160}
{"timestamp": "2025-06-12T17:34:31.577073", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "  Outer: insert=(274.38596491228066, 20.0), size=(35.614035087719294, 35.614035087719294)", "module": "qr_preview", "function": "_draw_finder_marker", "line": 161}
{"timestamp": "2025-06-12T17:34:31.577113", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "  Inner: insert=(280.4912280701754, 26.105263157894736), size=(23.403508771929822, 23.403508771929822)", "module": "qr_preview", "function": "_draw_finder_marker", "line": 162}
{"timestamp": "2025-06-12T17:34:31.577190", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Drawing marker center: style=square, color=#000000, pos=(287.10526315789474, 32.719298245614034), size=10.175438596491228", "module": "qr_preview", "function": "_draw_marker_center", "line": 179}
{"timestamp": "2025-06-12T17:34:31.577249", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Successfully added finder pattern group to SVG", "module": "qr_preview", "function": "_draw_finder_marker", "line": 171}
{"timestamp": "2025-06-12T17:34:31.577287", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Successfully drew finder pattern 2/3", "module": "qr_preview", "function": "generate_preview_svg_modular", "line": 782}
{"timestamp": "2025-06-12T17:34:31.577324", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Drawing finder pattern 3/3 at position (0, 50)", "module": "qr_preview", "function": "generate_preview_svg_modular", "line": 780}
{"timestamp": "2025-06-12T17:34:31.577361", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "_draw_finder_marker called: mx=0, my=50, module_size=5.087719298245614, border_style=square, center_style=square, border_color=#000000, center_color=#000000, offset=(20, 20)", "module": "qr_preview", "function": "_draw_finder_marker", "line": 97}
{"timestamp": "2025-06-12T17:34:31.577404", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Creating finder pattern group with id='marker-0-50'", "module": "qr_preview", "function": "_draw_finder_marker", "line": 110}
{"timestamp": "2025-06-12T17:34:31.577441", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Drawing square border:", "module": "qr_preview", "function": "_draw_finder_marker", "line": 160}
{"timestamp": "2025-06-12T17:34:31.577480", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "  Outer: insert=(20.0, 274.38596491228066), size=(35.614035087719294, 35.614035087719294)", "module": "qr_preview", "function": "_draw_finder_marker", "line": 161}
{"timestamp": "2025-06-12T17:34:31.577520", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "  Inner: insert=(26.105263157894736, 280.4912280701754), size=(23.403508771929822, 23.403508771929822)", "module": "qr_preview", "function": "_draw_finder_marker", "line": 162}
{"timestamp": "2025-06-12T17:34:31.577581", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Drawing marker center: style=square, color=#000000, pos=(32.719298245614034, 287.10526315789474), size=10.175438596491228", "module": "qr_preview", "function": "_draw_marker_center", "line": 179}
{"timestamp": "2025-06-12T17:34:31.577634", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Successfully added finder pattern group to SVG", "module": "qr_preview", "function": "_draw_finder_marker", "line": 171}
{"timestamp": "2025-06-12T17:34:31.577670", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Successfully drew finder pattern 3/3", "module": "qr_preview", "function": "generate_preview_svg_modular", "line": 782}
{"timestamp": "2025-06-12T17:34:31.604082", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Generated SVG length: 247431", "module": "qr_preview", "function": "generate_preview_svg_modular", "line": 802}
{"timestamp": "2025-06-12T17:34:31.604237", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "SVG contains marker-0-0: True", "module": "qr_preview", "function": "generate_preview_svg_modular", "line": 803}
{"timestamp": "2025-06-12T17:34:31.604403", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "SVG contains marker-18-0: False", "module": "qr_preview", "function": "generate_preview_svg_modular", "line": 804}
{"timestamp": "2025-06-12T17:34:31.604528", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "SVG contains marker-0-18: False", "module": "qr_preview", "function": "generate_preview_svg_modular", "line": 805}
{"timestamp": "2025-06-12T17:34:31.604909", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "SVG marker counts - outer: 3, inner: 3, dot: 0", "module": "qr_preview", "function": "generate_preview_svg_modular", "line": 813}
{"timestamp": "2025-06-12T17:34:31.604938", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "SVG label counts - text: 0, background: 0", "module": "qr_preview", "function": "generate_preview_svg_modular", "line": 814}
{"timestamp": "2025-06-12T17:34:31.607393", "level": "DEBUG", "logger": "backend.main", "message": "[Auth] Enforced Bearer token authentication for request", "module": "main", "function": "bearer_token_auth_middleware", "line": 274}
{"timestamp": "2025-06-12T17:34:35.496045", "level": "DEBUG", "logger": "backend.core.security.jwt_keys", "message": "Token contains key ID: 0bf271b1-2fbf-4d96-987f-e0b06b2fd890", "module": "jwt_keys", "function": "decode_token", "line": 188}
{"timestamp": "2025-06-12T17:34:35.496124", "level": "DEBUG", "logger": "backend.core.security.jwt_keys", "message": "Using key ID 0bf271b1-2fbf-4d96-987f-e0b06b2fd890 for verification", "module": "jwt_keys", "function": "decode_token", "line": 195}
{"timestamp": "2025-06-12T17:34:35.496256", "level": "DEBUG", "logger": "backend.services.auth", "message": "JWT decoded successfully with key manager", "module": "auth", "function": "authenticate_token", "line": 683}
{"timestamp": "2025-06-12T17:34:35.497736", "level": "DEBUG", "logger": "backend.services.db_utils", "message": "Query 'auth_lookup_e4ae2d8f-b32f-52d7-99cd-8a33b19652c7' completed in 0.001s", "module": "db_utils", "function": "execute_query_with_timeout", "line": 68}
{"timestamp": "2025-06-12T17:34:35.497784", "level": "DEBUG", "logger": "backend.services.db_utils", "message": "Auth query 'auth_lookup_e4ae2d8f-b32f-52d7-99cd-8a33b19652c7' successful, result format preserved", "module": "db_utils", "function": "execute_query_with_timeout", "line": 72}
{"timestamp": "2025-06-12T17:34:35.501754", "level": "DEBUG", "logger": "backend.services.auth", "message": "Authentication attempt with token prefix: eyJhbGciOi...", "module": "auth", "function": "get_current_user", "line": 727}
{"timestamp": "2025-06-12T17:34:35.501829", "level": "DEBUG", "logger": "backend.core.security.jwt_keys", "message": "Token contains key ID: 0bf271b1-2fbf-4d96-987f-e0b06b2fd890", "module": "jwt_keys", "function": "decode_token", "line": 188}
{"timestamp": "2025-06-12T17:34:35.501864", "level": "DEBUG", "logger": "backend.core.security.jwt_keys", "message": "Using key ID 0bf271b1-2fbf-4d96-987f-e0b06b2fd890 for verification", "module": "jwt_keys", "function": "decode_token", "line": 195}
{"timestamp": "2025-06-12T17:34:35.501947", "level": "DEBUG", "logger": "backend.services.auth", "message": "JWT decoded successfully with key manager", "module": "auth", "function": "authenticate_token", "line": 683}
{"timestamp": "2025-06-12T17:34:35.503518", "level": "DEBUG", "logger": "backend.services.db_utils", "message": "Query 'auth_lookup_e4ae2d8f-b32f-52d7-99cd-8a33b19652c7' completed in 0.001s", "module": "db_utils", "function": "execute_query_with_timeout", "line": 68}
{"timestamp": "2025-06-12T17:34:35.503572", "level": "DEBUG", "logger": "backend.services.db_utils", "message": "Auth query 'auth_lookup_e4ae2d8f-b32f-52d7-99cd-8a33b19652c7' successful, result format preserved", "module": "db_utils", "function": "execute_query_with_timeout", "line": 72}
{"timestamp": "2025-06-12T17:34:35.503629", "level": "DEBUG", "logger": "backend.services.auth", "message": "Successfully authenticated user: <EMAIL>", "module": "auth", "function": "get_current_user", "line": 732}
{"timestamp": "2025-06-12T17:34:35.503709", "level": "DEBUG", "logger": "backend.services.auth", "message": "Successfully authenticated user: <EMAIL>", "module": "auth", "function": "get_current_user", "line": 738}
{"timestamp": "2025-06-12T17:34:35.508397", "level": "DEBUG", "logger": "backend.routes.qr_preview", "message": "Received QR preview SVG request: {\"qr_type\": \"scooter_unlock\", \"data\": {\"scooter_id\": \"SCOOT123\", \"unlock_code\": \"UNLOCK42\", \"title\": \"Scooter_unlock QR Code\", \"description\": \"Scan to view scooter_unlock content\", \"service_provider\": \"Lime\", \"rate_info\": \"$20\"}, \"designOptions\": {\"size\": 256, \"margin\": 10, \"pattern\": \"square\", \"marker_border_style\": \"square\", \"marker_center_style\": \"square\", \"logo_size\": 0.3, \"logo_position\": \"center\", \"logo_opacity\": 1, \"logo_remove_background\": true, \"frame_style\": \"none\", \"frame_font\": \"SF P", "module": "qr_preview", "function": "preview_qr_svg", "line": 242}
{"timestamp": "2025-06-12T17:34:35.508499", "level": "DEBUG", "logger": "backend.routes.qr_preview", "message": "Form data keys: ['request', 'logo']", "module": "qr_preview", "function": "preview_qr_svg", "line": 297}
{"timestamp": "2025-06-12T17:34:35.508539", "level": "DEBUG", "logger": "backend.routes.qr_preview", "message": "Logo file found: type=<class 'starlette.datastructures.UploadFile'>, filename=LOGO - 1080 - ORANGE-02.png", "module": "qr_preview", "function": "preview_qr_svg", "line": 300}
{"timestamp": "2025-06-12T17:34:35.508585", "level": "DEBUG", "logger": "backend.routes.qr_preview", "message": "Logo content read: 142167 bytes", "module": "qr_preview", "function": "preview_qr_svg", "line": 305}
{"timestamp": "2025-06-12T17:34:35.508626", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Formatting content for QR type: qrtype.scooter_unlock, content keys: ['scooter_id', 'unlock_code', 'title', 'description', 'service_provider', 'rate_info']", "module": "qr_preview", "function": "_format_qr_data", "line": 876}
{"timestamp": "2025-06-12T17:34:35.532164", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Logo remove background setting: True, has logo: True", "module": "qr_preview", "function": "generate_preview_svg_modular", "line": 727}
{"timestamp": "2025-06-12T17:34:35.532238", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Logo area calculated: (20, 20, 37, 37), size_fraction: 0.3, size_modules: 17", "module": "qr_preview", "function": "generate_preview_svg_modular", "line": 739}
{"timestamp": "2025-06-12T17:34:35.549086", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Skipped 151 modules out of 1668 total modules for logo background removal", "module": "qr_preview", "function": "generate_preview_svg_modular", "line": 766}
{"timestamp": "2025-06-12T17:34:35.549153", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Drawing 3 finder patterns at positions: [(0, 0), (50, 0), (0, 50)]", "module": "qr_preview", "function": "generate_preview_svg_modular", "line": 775}
{"timestamp": "2025-06-12T17:34:35.549189", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "QR size: 57, border_style: square, center_style: square", "module": "qr_preview", "function": "generate_preview_svg_modular", "line": 776}
{"timestamp": "2025-06-12T17:34:35.549227", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Drawing finder pattern 1/3 at position (0, 0)", "module": "qr_preview", "function": "generate_preview_svg_modular", "line": 780}
{"timestamp": "2025-06-12T17:34:35.549270", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "_draw_finder_marker called: mx=0, my=0, module_size=5.087719298245614, border_style=square, center_style=square, border_color=#000000, center_color=#000000, offset=(20, 20)", "module": "qr_preview", "function": "_draw_finder_marker", "line": 97}
{"timestamp": "2025-06-12T17:34:35.549343", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Creating finder pattern group with id='marker-0-0'", "module": "qr_preview", "function": "_draw_finder_marker", "line": 110}
{"timestamp": "2025-06-12T17:34:35.550304", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Drawing square border:", "module": "qr_preview", "function": "_draw_finder_marker", "line": 160}
{"timestamp": "2025-06-12T17:34:35.550395", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "  Outer: insert=(20.0, 20.0), size=(35.614035087719294, 35.614035087719294)", "module": "qr_preview", "function": "_draw_finder_marker", "line": 161}
{"timestamp": "2025-06-12T17:34:35.550446", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "  Inner: insert=(26.105263157894736, 26.105263157894736), size=(23.403508771929822, 23.403508771929822)", "module": "qr_preview", "function": "_draw_finder_marker", "line": 162}
{"timestamp": "2025-06-12T17:34:35.550564", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Drawing marker center: style=square, color=#000000, pos=(32.719298245614034, 32.719298245614034), size=10.175438596491228", "module": "qr_preview", "function": "_draw_marker_center", "line": 179}
{"timestamp": "2025-06-12T17:34:35.550622", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Successfully added finder pattern group to SVG", "module": "qr_preview", "function": "_draw_finder_marker", "line": 171}
{"timestamp": "2025-06-12T17:34:35.550665", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Successfully drew finder pattern 1/3", "module": "qr_preview", "function": "generate_preview_svg_modular", "line": 782}
{"timestamp": "2025-06-12T17:34:35.550705", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Drawing finder pattern 2/3 at position (50, 0)", "module": "qr_preview", "function": "generate_preview_svg_modular", "line": 780}
{"timestamp": "2025-06-12T17:34:35.550746", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "_draw_finder_marker called: mx=50, my=0, module_size=5.087719298245614, border_style=square, center_style=square, border_color=#000000, center_color=#000000, offset=(20, 20)", "module": "qr_preview", "function": "_draw_finder_marker", "line": 97}
{"timestamp": "2025-06-12T17:34:35.550783", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Creating finder pattern group with id='marker-50-0'", "module": "qr_preview", "function": "_draw_finder_marker", "line": 110}
{"timestamp": "2025-06-12T17:34:35.550817", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Drawing square border:", "module": "qr_preview", "function": "_draw_finder_marker", "line": 160}
{"timestamp": "2025-06-12T17:34:35.550910", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "  Outer: insert=(274.38596491228066, 20.0), size=(35.614035087719294, 35.614035087719294)", "module": "qr_preview", "function": "_draw_finder_marker", "line": 161}
{"timestamp": "2025-06-12T17:34:35.550968", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "  Inner: insert=(280.4912280701754, 26.105263157894736), size=(23.403508771929822, 23.403508771929822)", "module": "qr_preview", "function": "_draw_finder_marker", "line": 162}
{"timestamp": "2025-06-12T17:34:35.551050", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Drawing marker center: style=square, color=#000000, pos=(287.10526315789474, 32.719298245614034), size=10.175438596491228", "module": "qr_preview", "function": "_draw_marker_center", "line": 179}
{"timestamp": "2025-06-12T17:34:35.551101", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Successfully added finder pattern group to SVG", "module": "qr_preview", "function": "_draw_finder_marker", "line": 171}
{"timestamp": "2025-06-12T17:34:35.551138", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Successfully drew finder pattern 2/3", "module": "qr_preview", "function": "generate_preview_svg_modular", "line": 782}
{"timestamp": "2025-06-12T17:34:35.551195", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Drawing finder pattern 3/3 at position (0, 50)", "module": "qr_preview", "function": "generate_preview_svg_modular", "line": 780}
{"timestamp": "2025-06-12T17:34:35.551227", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "_draw_finder_marker called: mx=0, my=50, module_size=5.087719298245614, border_style=square, center_style=square, border_color=#000000, center_color=#000000, offset=(20, 20)", "module": "qr_preview", "function": "_draw_finder_marker", "line": 97}
{"timestamp": "2025-06-12T17:34:35.551267", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Creating finder pattern group with id='marker-0-50'", "module": "qr_preview", "function": "_draw_finder_marker", "line": 110}
{"timestamp": "2025-06-12T17:34:35.551346", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Drawing square border:", "module": "qr_preview", "function": "_draw_finder_marker", "line": 160}
{"timestamp": "2025-06-12T17:34:35.551379", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "  Outer: insert=(20.0, 274.38596491228066), size=(35.614035087719294, 35.614035087719294)", "module": "qr_preview", "function": "_draw_finder_marker", "line": 161}
{"timestamp": "2025-06-12T17:34:35.551412", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "  Inner: insert=(26.105263157894736, 280.4912280701754), size=(23.403508771929822, 23.403508771929822)", "module": "qr_preview", "function": "_draw_finder_marker", "line": 162}
{"timestamp": "2025-06-12T17:34:35.551473", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Drawing marker center: style=square, color=#000000, pos=(32.719298245614034, 287.10526315789474), size=10.175438596491228", "module": "qr_preview", "function": "_draw_marker_center", "line": 179}
{"timestamp": "2025-06-12T17:34:35.551520", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Successfully added finder pattern group to SVG", "module": "qr_preview", "function": "_draw_finder_marker", "line": 171}
{"timestamp": "2025-06-12T17:34:35.551553", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Successfully drew finder pattern 3/3", "module": "qr_preview", "function": "generate_preview_svg_modular", "line": 782}
{"timestamp": "2025-06-12T17:34:35.551613", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "_draw_logo called with logo type: <class 'bytes'>, dimensions: 300", "module": "qr_preview", "function": "_draw_logo", "line": 434}
{"timestamp": "2025-06-12T17:34:35.902568", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Generated SVG length: 227869", "module": "qr_preview", "function": "generate_preview_svg_modular", "line": 802}
{"timestamp": "2025-06-12T17:34:35.902728", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "SVG contains marker-0-0: True", "module": "qr_preview", "function": "generate_preview_svg_modular", "line": 803}
{"timestamp": "2025-06-12T17:34:35.902858", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "SVG contains marker-18-0: False", "module": "qr_preview", "function": "generate_preview_svg_modular", "line": 804}
{"timestamp": "2025-06-12T17:34:35.902982", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "SVG contains marker-0-18: False", "module": "qr_preview", "function": "generate_preview_svg_modular", "line": 805}
{"timestamp": "2025-06-12T17:34:35.903347", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "SVG marker counts - outer: 3, inner: 3, dot: 0", "module": "qr_preview", "function": "generate_preview_svg_modular", "line": 813}
{"timestamp": "2025-06-12T17:34:35.903379", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "SVG label counts - text: 0, background: 0", "module": "qr_preview", "function": "generate_preview_svg_modular", "line": 814}
{"timestamp": "2025-06-12T17:34:35.905501", "level": "DEBUG", "logger": "backend.main", "message": "[Auth] Enforced Bearer token authentication for request", "module": "main", "function": "bearer_token_auth_middleware", "line": 274}
{"timestamp": "2025-06-12T17:34:36.733520", "level": "DEBUG", "logger": "backend.core.security.jwt_keys", "message": "Token contains key ID: 0bf271b1-2fbf-4d96-987f-e0b06b2fd890", "module": "jwt_keys", "function": "decode_token", "line": 188}
{"timestamp": "2025-06-12T17:34:36.733580", "level": "DEBUG", "logger": "backend.core.security.jwt_keys", "message": "Using key ID 0bf271b1-2fbf-4d96-987f-e0b06b2fd890 for verification", "module": "jwt_keys", "function": "decode_token", "line": 195}
{"timestamp": "2025-06-12T17:34:36.733699", "level": "DEBUG", "logger": "backend.services.auth", "message": "JWT decoded successfully with key manager", "module": "auth", "function": "authenticate_token", "line": 683}
{"timestamp": "2025-06-12T17:34:36.735596", "level": "DEBUG", "logger": "backend.services.db_utils", "message": "Query 'auth_lookup_e4ae2d8f-b32f-52d7-99cd-8a33b19652c7' completed in 0.002s", "module": "db_utils", "function": "execute_query_with_timeout", "line": 68}
{"timestamp": "2025-06-12T17:34:36.735646", "level": "DEBUG", "logger": "backend.services.db_utils", "message": "Auth query 'auth_lookup_e4ae2d8f-b32f-52d7-99cd-8a33b19652c7' successful, result format preserved", "module": "db_utils", "function": "execute_query_with_timeout", "line": 72}
{"timestamp": "2025-06-12T17:34:36.739586", "level": "DEBUG", "logger": "backend.services.auth", "message": "Authentication attempt with token prefix: eyJhbGciOi...", "module": "auth", "function": "get_current_user", "line": 727}
{"timestamp": "2025-06-12T17:34:36.739711", "level": "DEBUG", "logger": "backend.core.security.jwt_keys", "message": "Token contains key ID: 0bf271b1-2fbf-4d96-987f-e0b06b2fd890", "module": "jwt_keys", "function": "decode_token", "line": 188}
{"timestamp": "2025-06-12T17:34:36.739758", "level": "DEBUG", "logger": "backend.core.security.jwt_keys", "message": "Using key ID 0bf271b1-2fbf-4d96-987f-e0b06b2fd890 for verification", "module": "jwt_keys", "function": "decode_token", "line": 195}
{"timestamp": "2025-06-12T17:34:36.739887", "level": "DEBUG", "logger": "backend.services.auth", "message": "JWT decoded successfully with key manager", "module": "auth", "function": "authenticate_token", "line": 683}
{"timestamp": "2025-06-12T17:34:36.741076", "level": "DEBUG", "logger": "backend.services.db_utils", "message": "Query 'auth_lookup_e4ae2d8f-b32f-52d7-99cd-8a33b19652c7' completed in 0.001s", "module": "db_utils", "function": "execute_query_with_timeout", "line": 68}
{"timestamp": "2025-06-12T17:34:36.741123", "level": "DEBUG", "logger": "backend.services.db_utils", "message": "Auth query 'auth_lookup_e4ae2d8f-b32f-52d7-99cd-8a33b19652c7' successful, result format preserved", "module": "db_utils", "function": "execute_query_with_timeout", "line": 72}
{"timestamp": "2025-06-12T17:34:36.741176", "level": "DEBUG", "logger": "backend.services.auth", "message": "Successfully authenticated user: <EMAIL>", "module": "auth", "function": "get_current_user", "line": 732}
{"timestamp": "2025-06-12T17:34:36.741216", "level": "DEBUG", "logger": "backend.services.auth", "message": "Successfully authenticated user: <EMAIL>", "module": "auth", "function": "get_current_user", "line": 738}
{"timestamp": "2025-06-12T17:34:36.742851", "level": "DEBUG", "logger": "backend.routes.qr_preview", "message": "Received QR preview SVG request: {\"qr_type\": \"scooter_unlock\", \"data\": {\"scooter_id\": \"SCOOT123\", \"unlock_code\": \"UNLOCK42\", \"title\": \"Scooter_unlock QR Code\", \"description\": \"Scan to view scooter_unlock content\", \"service_provider\": \"Lime\", \"rate_info\": \"$20\"}, \"designOptions\": {\"size\": 256, \"margin\": 10, \"pattern\": \"square\", \"marker_border_style\": \"square\", \"marker_border_color\": \"#000000\", \"marker_center_style\": \"square\", \"marker_center_color\": \"#000000\", \"logo_size\": 0.3, \"logo_position\": \"center\", \"logo_opacity\": 1, \"logo_", "module": "qr_preview", "function": "preview_qr_svg", "line": 242}
{"timestamp": "2025-06-12T17:34:36.742977", "level": "DEBUG", "logger": "backend.routes.qr_preview", "message": "Form data keys: ['request', 'logo']", "module": "qr_preview", "function": "preview_qr_svg", "line": 297}
{"timestamp": "2025-06-12T17:34:36.743448", "level": "DEBUG", "logger": "backend.routes.qr_preview", "message": "Logo file found: type=<class 'starlette.datastructures.UploadFile'>, filename=LOGO - 1080 - ORANGE-02.png", "module": "qr_preview", "function": "preview_qr_svg", "line": 300}
{"timestamp": "2025-06-12T17:34:36.743502", "level": "DEBUG", "logger": "backend.routes.qr_preview", "message": "Logo content read: 142167 bytes", "module": "qr_preview", "function": "preview_qr_svg", "line": 305}
{"timestamp": "2025-06-12T17:34:36.743550", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Formatting content for QR type: qrtype.scooter_unlock, content keys: ['scooter_id', 'unlock_code', 'title', 'description', 'service_provider', 'rate_info']", "module": "qr_preview", "function": "_format_qr_data", "line": 876}
{"timestamp": "2025-06-12T17:34:36.766658", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Logo remove background setting: True, has logo: True", "module": "qr_preview", "function": "generate_preview_svg_modular", "line": 727}
{"timestamp": "2025-06-12T17:34:36.766734", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Logo area calculated: (20, 20, 37, 37), size_fraction: 0.3, size_modules: 17", "module": "qr_preview", "function": "generate_preview_svg_modular", "line": 739}
{"timestamp": "2025-06-12T17:34:36.783716", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Skipped 151 modules out of 1668 total modules for logo background removal", "module": "qr_preview", "function": "generate_preview_svg_modular", "line": 766}
{"timestamp": "2025-06-12T17:34:36.783781", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Drawing 3 finder patterns at positions: [(0, 0), (50, 0), (0, 50)]", "module": "qr_preview", "function": "generate_preview_svg_modular", "line": 775}
{"timestamp": "2025-06-12T17:34:36.783819", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "QR size: 57, border_style: square, center_style: square", "module": "qr_preview", "function": "generate_preview_svg_modular", "line": 776}
{"timestamp": "2025-06-12T17:34:36.783856", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Drawing finder pattern 1/3 at position (0, 0)", "module": "qr_preview", "function": "generate_preview_svg_modular", "line": 780}
{"timestamp": "2025-06-12T17:34:36.783898", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "_draw_finder_marker called: mx=0, my=0, module_size=5.087719298245614, border_style=square, center_style=square, border_color=#000000, center_color=#000000, offset=(20, 20)", "module": "qr_preview", "function": "_draw_finder_marker", "line": 97}
{"timestamp": "2025-06-12T17:34:36.783945", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Creating finder pattern group with id='marker-0-0'", "module": "qr_preview", "function": "_draw_finder_marker", "line": 110}
{"timestamp": "2025-06-12T17:34:36.784025", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Drawing square border:", "module": "qr_preview", "function": "_draw_finder_marker", "line": 160}
{"timestamp": "2025-06-12T17:34:36.784060", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "  Outer: insert=(20.0, 20.0), size=(35.614035087719294, 35.614035087719294)", "module": "qr_preview", "function": "_draw_finder_marker", "line": 161}
{"timestamp": "2025-06-12T17:34:36.784094", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "  Inner: insert=(26.105263157894736, 26.105263157894736), size=(23.403508771929822, 23.403508771929822)", "module": "qr_preview", "function": "_draw_finder_marker", "line": 162}
{"timestamp": "2025-06-12T17:34:36.784188", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Drawing marker center: style=square, color=#000000, pos=(32.719298245614034, 32.719298245614034), size=10.175438596491228", "module": "qr_preview", "function": "_draw_marker_center", "line": 179}
{"timestamp": "2025-06-12T17:34:36.784239", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Successfully added finder pattern group to SVG", "module": "qr_preview", "function": "_draw_finder_marker", "line": 171}
{"timestamp": "2025-06-12T17:34:36.784271", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Successfully drew finder pattern 1/3", "module": "qr_preview", "function": "generate_preview_svg_modular", "line": 782}
{"timestamp": "2025-06-12T17:34:36.784301", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Drawing finder pattern 2/3 at position (50, 0)", "module": "qr_preview", "function": "generate_preview_svg_modular", "line": 780}
{"timestamp": "2025-06-12T17:34:36.784921", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "_draw_finder_marker called: mx=50, my=0, module_size=5.087719298245614, border_style=square, center_style=square, border_color=#000000, center_color=#000000, offset=(20, 20)", "module": "qr_preview", "function": "_draw_finder_marker", "line": 97}
{"timestamp": "2025-06-12T17:34:36.784987", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Creating finder pattern group with id='marker-50-0'", "module": "qr_preview", "function": "_draw_finder_marker", "line": 110}
{"timestamp": "2025-06-12T17:34:36.785029", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Drawing square border:", "module": "qr_preview", "function": "_draw_finder_marker", "line": 160}
{"timestamp": "2025-06-12T17:34:36.785073", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "  Outer: insert=(274.38596491228066, 20.0), size=(35.614035087719294, 35.614035087719294)", "module": "qr_preview", "function": "_draw_finder_marker", "line": 161}
{"timestamp": "2025-06-12T17:34:36.785108", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "  Inner: insert=(280.4912280701754, 26.105263157894736), size=(23.403508771929822, 23.403508771929822)", "module": "qr_preview", "function": "_draw_finder_marker", "line": 162}
{"timestamp": "2025-06-12T17:34:36.785186", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Drawing marker center: style=square, color=#000000, pos=(287.10526315789474, 32.719298245614034), size=10.175438596491228", "module": "qr_preview", "function": "_draw_marker_center", "line": 179}
{"timestamp": "2025-06-12T17:34:36.785237", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Successfully added finder pattern group to SVG", "module": "qr_preview", "function": "_draw_finder_marker", "line": 171}
{"timestamp": "2025-06-12T17:34:36.785272", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Successfully drew finder pattern 2/3", "module": "qr_preview", "function": "generate_preview_svg_modular", "line": 782}
{"timestamp": "2025-06-12T17:34:36.785305", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Drawing finder pattern 3/3 at position (0, 50)", "module": "qr_preview", "function": "generate_preview_svg_modular", "line": 780}
{"timestamp": "2025-06-12T17:34:36.785340", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "_draw_finder_marker called: mx=0, my=50, module_size=5.087719298245614, border_style=square, center_style=square, border_color=#000000, center_color=#000000, offset=(20, 20)", "module": "qr_preview", "function": "_draw_finder_marker", "line": 97}
{"timestamp": "2025-06-12T17:34:36.785382", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Creating finder pattern group with id='marker-0-50'", "module": "qr_preview", "function": "_draw_finder_marker", "line": 110}
{"timestamp": "2025-06-12T17:34:36.785431", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Drawing square border:", "module": "qr_preview", "function": "_draw_finder_marker", "line": 160}
{"timestamp": "2025-06-12T17:34:36.785463", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "  Outer: insert=(20.0, 274.38596491228066), size=(35.614035087719294, 35.614035087719294)", "module": "qr_preview", "function": "_draw_finder_marker", "line": 161}
{"timestamp": "2025-06-12T17:34:36.785881", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "  Inner: insert=(26.105263157894736, 280.4912280701754), size=(23.403508771929822, 23.403508771929822)", "module": "qr_preview", "function": "_draw_finder_marker", "line": 162}
{"timestamp": "2025-06-12T17:34:36.785941", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Drawing marker center: style=square, color=#000000, pos=(32.719298245614034, 287.10526315789474), size=10.175438596491228", "module": "qr_preview", "function": "_draw_marker_center", "line": 179}
{"timestamp": "2025-06-12T17:34:36.785984", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Successfully added finder pattern group to SVG", "module": "qr_preview", "function": "_draw_finder_marker", "line": 171}
{"timestamp": "2025-06-12T17:34:36.786016", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Successfully drew finder pattern 3/3", "module": "qr_preview", "function": "generate_preview_svg_modular", "line": 782}
{"timestamp": "2025-06-12T17:34:36.786053", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "_draw_logo called with logo type: <class 'bytes'>, dimensions: 300", "module": "qr_preview", "function": "_draw_logo", "line": 434}
{"timestamp": "2025-06-12T17:34:37.111425", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Generated SVG length: 227869", "module": "qr_preview", "function": "generate_preview_svg_modular", "line": 802}
{"timestamp": "2025-06-12T17:34:37.111595", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "SVG contains marker-0-0: True", "module": "qr_preview", "function": "generate_preview_svg_modular", "line": 803}
{"timestamp": "2025-06-12T17:34:37.111720", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "SVG contains marker-18-0: False", "module": "qr_preview", "function": "generate_preview_svg_modular", "line": 804}
{"timestamp": "2025-06-12T17:34:37.111845", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "SVG contains marker-0-18: False", "module": "qr_preview", "function": "generate_preview_svg_modular", "line": 805}
{"timestamp": "2025-06-12T17:34:37.112201", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "SVG marker counts - outer: 3, inner: 3, dot: 0", "module": "qr_preview", "function": "generate_preview_svg_modular", "line": 813}
{"timestamp": "2025-06-12T17:34:37.112244", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "SVG label counts - text: 0, background: 0", "module": "qr_preview", "function": "generate_preview_svg_modular", "line": 814}
{"timestamp": "2025-06-12T17:34:37.114511", "level": "DEBUG", "logger": "backend.main", "message": "[Auth] Enforced Bearer token authentication for request", "module": "main", "function": "bearer_token_auth_middleware", "line": 274}
{"timestamp": "2025-06-12T17:34:52.303010", "level": "DEBUG", "logger": "backend.core.security.jwt_keys", "message": "Token contains key ID: 0bf271b1-2fbf-4d96-987f-e0b06b2fd890", "module": "jwt_keys", "function": "decode_token", "line": 188}
{"timestamp": "2025-06-12T17:34:52.303095", "level": "DEBUG", "logger": "backend.core.security.jwt_keys", "message": "Using key ID 0bf271b1-2fbf-4d96-987f-e0b06b2fd890 for verification", "module": "jwt_keys", "function": "decode_token", "line": 195}
{"timestamp": "2025-06-12T17:34:52.303617", "level": "DEBUG", "logger": "backend.services.auth", "message": "JWT decoded successfully with key manager", "module": "auth", "function": "authenticate_token", "line": 683}
{"timestamp": "2025-06-12T17:34:52.311809", "level": "DEBUG", "logger": "backend.services.db_utils", "message": "Query 'auth_lookup_e4ae2d8f-b32f-52d7-99cd-8a33b19652c7' completed in 0.007s", "module": "db_utils", "function": "execute_query_with_timeout", "line": 68}
{"timestamp": "2025-06-12T17:34:52.311863", "level": "DEBUG", "logger": "backend.services.db_utils", "message": "Auth query 'auth_lookup_e4ae2d8f-b32f-52d7-99cd-8a33b19652c7' successful, result format preserved", "module": "db_utils", "function": "execute_query_with_timeout", "line": 72}
{"timestamp": "2025-06-12T17:34:52.321799", "level": "DEBUG", "logger": "backend.main", "message": "[Auth] Enforced Bearer token authentication for request", "module": "main", "function": "bearer_token_auth_middleware", "line": 274}
{"timestamp": "2025-06-12T17:34:52.666320", "level": "DEBUG", "logger": "backend.core.security.jwt_keys", "message": "Token contains key ID: 0bf271b1-2fbf-4d96-987f-e0b06b2fd890", "module": "jwt_keys", "function": "decode_token", "line": 188}
{"timestamp": "2025-06-12T17:34:52.666391", "level": "DEBUG", "logger": "backend.core.security.jwt_keys", "message": "Using key ID 0bf271b1-2fbf-4d96-987f-e0b06b2fd890 for verification", "module": "jwt_keys", "function": "decode_token", "line": 195}
{"timestamp": "2025-06-12T17:34:52.666774", "level": "DEBUG", "logger": "backend.services.auth", "message": "JWT decoded successfully with key manager", "module": "auth", "function": "authenticate_token", "line": 683}
{"timestamp": "2025-06-12T17:34:52.672975", "level": "DEBUG", "logger": "backend.services.db_utils", "message": "Query 'auth_lookup_e4ae2d8f-b32f-52d7-99cd-8a33b19652c7' completed in 0.006s", "module": "db_utils", "function": "execute_query_with_timeout", "line": 68}
{"timestamp": "2025-06-12T17:34:52.673041", "level": "DEBUG", "logger": "backend.services.db_utils", "message": "Auth query 'auth_lookup_e4ae2d8f-b32f-52d7-99cd-8a33b19652c7' successful, result format preserved", "module": "db_utils", "function": "execute_query_with_timeout", "line": 72}
{"timestamp": "2025-06-12T17:34:52.682468", "level": "DEBUG", "logger": "backend.services.auth", "message": "Authentication attempt with token prefix: eyJhbGciOi...", "module": "auth", "function": "get_current_user", "line": 727}
{"timestamp": "2025-06-12T17:34:52.682598", "level": "DEBUG", "logger": "backend.core.security.jwt_keys", "message": "Token contains key ID: 0bf271b1-2fbf-4d96-987f-e0b06b2fd890", "module": "jwt_keys", "function": "decode_token", "line": 188}
{"timestamp": "2025-06-12T17:34:52.682638", "level": "DEBUG", "logger": "backend.core.security.jwt_keys", "message": "Using key ID 0bf271b1-2fbf-4d96-987f-e0b06b2fd890 for verification", "module": "jwt_keys", "function": "decode_token", "line": 195}
{"timestamp": "2025-06-12T17:34:52.682793", "level": "DEBUG", "logger": "backend.services.auth", "message": "JWT decoded successfully with key manager", "module": "auth", "function": "authenticate_token", "line": 683}
{"timestamp": "2025-06-12T17:34:52.684555", "level": "DEBUG", "logger": "backend.services.db_utils", "message": "Query 'auth_lookup_e4ae2d8f-b32f-52d7-99cd-8a33b19652c7' completed in 0.002s", "module": "db_utils", "function": "execute_query_with_timeout", "line": 68}
{"timestamp": "2025-06-12T17:34:52.684606", "level": "DEBUG", "logger": "backend.services.db_utils", "message": "Auth query 'auth_lookup_e4ae2d8f-b32f-52d7-99cd-8a33b19652c7' successful, result format preserved", "module": "db_utils", "function": "execute_query_with_timeout", "line": 72}
{"timestamp": "2025-06-12T17:34:52.684693", "level": "DEBUG", "logger": "backend.services.auth", "message": "Successfully authenticated user: <EMAIL>", "module": "auth", "function": "get_current_user", "line": 732}
{"timestamp": "2025-06-12T17:34:52.684735", "level": "DEBUG", "logger": "backend.services.auth", "message": "Successfully authenticated user: <EMAIL>", "module": "auth", "function": "get_current_user", "line": 738}
{"timestamp": "2025-06-12T17:34:52.689577", "level": "DEBUG", "logger": "backend.routes.qr_preview", "message": "Received QR preview SVG request: {\"qr_type\": \"scooter_unlock\", \"data\": {\"scooter_id\": \"SCOOT123\", \"unlock_code\": \"UNLOCK42\", \"title\": \"Scooter_unlock QR Code\", \"description\": \"Scan to view scooter_unlock content\", \"service_provider\": \"Lime\", \"rate_info\": \"$20\"}, \"designOptions\": {\"size\": 256, \"margin\": 10, \"pattern\": \"square\", \"marker_border_style\": \"square\", \"marker_center_style\": \"square\", \"logo_size\": 0.3, \"logo_position\": \"center\", \"logo_opacity\": 1, \"logo_remove_background\": true, \"frame_style\": \"none\", \"frame_font\": \"SF P", "module": "qr_preview", "function": "preview_qr_svg", "line": 242}
{"timestamp": "2025-06-12T17:34:52.689847", "level": "DEBUG", "logger": "backend.routes.qr_preview", "message": "Form data keys: ['request', 'logo']", "module": "qr_preview", "function": "preview_qr_svg", "line": 297}
{"timestamp": "2025-06-12T17:34:52.689928", "level": "DEBUG", "logger": "backend.routes.qr_preview", "message": "Logo file found: type=<class 'starlette.datastructures.UploadFile'>, filename=LOGO - 1080 - ORANGE-02.png", "module": "qr_preview", "function": "preview_qr_svg", "line": 300}
{"timestamp": "2025-06-12T17:34:52.690014", "level": "DEBUG", "logger": "backend.routes.qr_preview", "message": "Logo content read: 142167 bytes", "module": "qr_preview", "function": "preview_qr_svg", "line": 305}
{"timestamp": "2025-06-12T17:34:52.695528", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Formatting content for QR type: qrtype.scooter_unlock, content keys: ['scooter_id', 'unlock_code', 'title', 'description', 'service_provider', 'rate_info']", "module": "qr_preview", "function": "_format_qr_data", "line": 876}
{"timestamp": "2025-06-12T17:34:52.720703", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Logo remove background setting: True, has logo: True", "module": "qr_preview", "function": "generate_preview_svg_modular", "line": 727}
{"timestamp": "2025-06-12T17:34:52.720769", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Logo area calculated: (20, 20, 37, 37), size_fraction: 0.3, size_modules: 17", "module": "qr_preview", "function": "generate_preview_svg_modular", "line": 739}
{"timestamp": "2025-06-12T17:34:52.739579", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Skipped 151 modules out of 1668 total modules for logo background removal", "module": "qr_preview", "function": "generate_preview_svg_modular", "line": 766}
{"timestamp": "2025-06-12T17:34:52.739647", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Drawing 3 finder patterns at positions: [(0, 0), (50, 0), (0, 50)]", "module": "qr_preview", "function": "generate_preview_svg_modular", "line": 775}
{"timestamp": "2025-06-12T17:34:52.739689", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "QR size: 57, border_style: square, center_style: square", "module": "qr_preview", "function": "generate_preview_svg_modular", "line": 776}
{"timestamp": "2025-06-12T17:34:52.758331", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Drawing finder pattern 1/3 at position (0, 0)", "module": "qr_preview", "function": "generate_preview_svg_modular", "line": 780}
{"timestamp": "2025-06-12T17:34:52.758443", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "_draw_finder_marker called: mx=0, my=0, module_size=5.087719298245614, border_style=square, center_style=square, border_color=#000000, center_color=#000000, offset=(20, 20)", "module": "qr_preview", "function": "_draw_finder_marker", "line": 97}
{"timestamp": "2025-06-12T17:34:52.758524", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Creating finder pattern group with id='marker-0-0'", "module": "qr_preview", "function": "_draw_finder_marker", "line": 110}
{"timestamp": "2025-06-12T17:34:52.758600", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Drawing square border:", "module": "qr_preview", "function": "_draw_finder_marker", "line": 160}
{"timestamp": "2025-06-12T17:34:52.758948", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "  Outer: insert=(20.0, 20.0), size=(35.614035087719294, 35.614035087719294)", "module": "qr_preview", "function": "_draw_finder_marker", "line": 161}
{"timestamp": "2025-06-12T17:34:52.759004", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "  Inner: insert=(26.105263157894736, 26.105263157894736), size=(23.403508771929822, 23.403508771929822)", "module": "qr_preview", "function": "_draw_finder_marker", "line": 162}
{"timestamp": "2025-06-12T17:34:52.759112", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Drawing marker center: style=square, color=#000000, pos=(32.719298245614034, 32.719298245614034), size=10.175438596491228", "module": "qr_preview", "function": "_draw_marker_center", "line": 179}
{"timestamp": "2025-06-12T17:34:52.759180", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Successfully added finder pattern group to SVG", "module": "qr_preview", "function": "_draw_finder_marker", "line": 171}
{"timestamp": "2025-06-12T17:34:52.759220", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Successfully drew finder pattern 1/3", "module": "qr_preview", "function": "generate_preview_svg_modular", "line": 782}
{"timestamp": "2025-06-12T17:34:52.759255", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Drawing finder pattern 2/3 at position (50, 0)", "module": "qr_preview", "function": "generate_preview_svg_modular", "line": 780}
{"timestamp": "2025-06-12T17:34:52.759292", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "_draw_finder_marker called: mx=50, my=0, module_size=5.087719298245614, border_style=square, center_style=square, border_color=#000000, center_color=#000000, offset=(20, 20)", "module": "qr_preview", "function": "_draw_finder_marker", "line": 97}
{"timestamp": "2025-06-12T17:34:52.759337", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Creating finder pattern group with id='marker-50-0'", "module": "qr_preview", "function": "_draw_finder_marker", "line": 110}
{"timestamp": "2025-06-12T17:34:52.759371", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Drawing square border:", "module": "qr_preview", "function": "_draw_finder_marker", "line": 160}
{"timestamp": "2025-06-12T17:34:52.759406", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "  Outer: insert=(274.38596491228066, 20.0), size=(35.614035087719294, 35.614035087719294)", "module": "qr_preview", "function": "_draw_finder_marker", "line": 161}
{"timestamp": "2025-06-12T17:34:52.759440", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "  Inner: insert=(280.4912280701754, 26.105263157894736), size=(23.403508771929822, 23.403508771929822)", "module": "qr_preview", "function": "_draw_finder_marker", "line": 162}
{"timestamp": "2025-06-12T17:34:52.759503", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Drawing marker center: style=square, color=#000000, pos=(287.10526315789474, 32.719298245614034), size=10.175438596491228", "module": "qr_preview", "function": "_draw_marker_center", "line": 179}
{"timestamp": "2025-06-12T17:34:52.759619", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Successfully added finder pattern group to SVG", "module": "qr_preview", "function": "_draw_finder_marker", "line": 171}
{"timestamp": "2025-06-12T17:34:52.759651", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Successfully drew finder pattern 2/3", "module": "qr_preview", "function": "generate_preview_svg_modular", "line": 782}
{"timestamp": "2025-06-12T17:34:52.759680", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Drawing finder pattern 3/3 at position (0, 50)", "module": "qr_preview", "function": "generate_preview_svg_modular", "line": 780}
{"timestamp": "2025-06-12T17:34:52.759730", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "_draw_finder_marker called: mx=0, my=50, module_size=5.087719298245614, border_style=square, center_style=square, border_color=#000000, center_color=#000000, offset=(20, 20)", "module": "qr_preview", "function": "_draw_finder_marker", "line": 97}
{"timestamp": "2025-06-12T17:34:52.759770", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Creating finder pattern group with id='marker-0-50'", "module": "qr_preview", "function": "_draw_finder_marker", "line": 110}
{"timestamp": "2025-06-12T17:34:52.759799", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Drawing square border:", "module": "qr_preview", "function": "_draw_finder_marker", "line": 160}
{"timestamp": "2025-06-12T17:34:52.759830", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "  Outer: insert=(20.0, 274.38596491228066), size=(35.614035087719294, 35.614035087719294)", "module": "qr_preview", "function": "_draw_finder_marker", "line": 161}
{"timestamp": "2025-06-12T17:34:52.759861", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "  Inner: insert=(26.105263157894736, 280.4912280701754), size=(23.403508771929822, 23.403508771929822)", "module": "qr_preview", "function": "_draw_finder_marker", "line": 162}
{"timestamp": "2025-06-12T17:34:52.759917", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Drawing marker center: style=square, color=#000000, pos=(32.719298245614034, 287.10526315789474), size=10.175438596491228", "module": "qr_preview", "function": "_draw_marker_center", "line": 179}
{"timestamp": "2025-06-12T17:34:52.759958", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Successfully added finder pattern group to SVG", "module": "qr_preview", "function": "_draw_finder_marker", "line": 171}
{"timestamp": "2025-06-12T17:34:52.759985", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Successfully drew finder pattern 3/3", "module": "qr_preview", "function": "generate_preview_svg_modular", "line": 782}
{"timestamp": "2025-06-12T17:34:52.760045", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "_draw_logo called with logo type: <class 'bytes'>, dimensions: 300", "module": "qr_preview", "function": "_draw_logo", "line": 434}
{"timestamp": "2025-06-12T17:34:53.116245", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Generated SVG length: 227869", "module": "qr_preview", "function": "generate_preview_svg_modular", "line": 802}
{"timestamp": "2025-06-12T17:34:53.116403", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "SVG contains marker-0-0: True", "module": "qr_preview", "function": "generate_preview_svg_modular", "line": 803}
{"timestamp": "2025-06-12T17:34:53.116543", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "SVG contains marker-18-0: False", "module": "qr_preview", "function": "generate_preview_svg_modular", "line": 804}
{"timestamp": "2025-06-12T17:34:53.116671", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "SVG contains marker-0-18: False", "module": "qr_preview", "function": "generate_preview_svg_modular", "line": 805}
{"timestamp": "2025-06-12T17:34:53.117077", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "SVG marker counts - outer: 3, inner: 3, dot: 0", "module": "qr_preview", "function": "generate_preview_svg_modular", "line": 813}
{"timestamp": "2025-06-12T17:34:53.117148", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "SVG label counts - text: 0, background: 0", "module": "qr_preview", "function": "generate_preview_svg_modular", "line": 814}
{"timestamp": "2025-06-12T17:34:53.119354", "level": "DEBUG", "logger": "backend.main", "message": "[Auth] Enforced Bearer token authentication for request", "module": "main", "function": "bearer_token_auth_middleware", "line": 274}
{"timestamp": "2025-06-12T17:34:53.168151", "level": "DEBUG", "logger": "backend.core.security.jwt_keys", "message": "Token contains key ID: 0bf271b1-2fbf-4d96-987f-e0b06b2fd890", "module": "jwt_keys", "function": "decode_token", "line": 188}
{"timestamp": "2025-06-12T17:34:53.168220", "level": "DEBUG", "logger": "backend.core.security.jwt_keys", "message": "Using key ID 0bf271b1-2fbf-4d96-987f-e0b06b2fd890 for verification", "module": "jwt_keys", "function": "decode_token", "line": 195}
{"timestamp": "2025-06-12T17:34:53.168355", "level": "DEBUG", "logger": "backend.services.auth", "message": "JWT decoded successfully with key manager", "module": "auth", "function": "authenticate_token", "line": 683}
{"timestamp": "2025-06-12T17:34:53.170002", "level": "DEBUG", "logger": "backend.services.db_utils", "message": "Query 'auth_lookup_e4ae2d8f-b32f-52d7-99cd-8a33b19652c7' completed in 0.001s", "module": "db_utils", "function": "execute_query_with_timeout", "line": 68}
{"timestamp": "2025-06-12T17:34:53.170051", "level": "DEBUG", "logger": "backend.services.db_utils", "message": "Auth query 'auth_lookup_e4ae2d8f-b32f-52d7-99cd-8a33b19652c7' successful, result format preserved", "module": "db_utils", "function": "execute_query_with_timeout", "line": 72}
{"timestamp": "2025-06-12T17:34:53.180124", "level": "DEBUG", "logger": "backend.services.auth", "message": "Authentication attempt with token prefix: eyJhbGciOi...", "module": "auth", "function": "get_current_user", "line": 727}
{"timestamp": "2025-06-12T17:34:53.180238", "level": "DEBUG", "logger": "backend.core.security.jwt_keys", "message": "Token contains key ID: 0bf271b1-2fbf-4d96-987f-e0b06b2fd890", "module": "jwt_keys", "function": "decode_token", "line": 188}
{"timestamp": "2025-06-12T17:34:53.180278", "level": "DEBUG", "logger": "backend.core.security.jwt_keys", "message": "Using key ID 0bf271b1-2fbf-4d96-987f-e0b06b2fd890 for verification", "module": "jwt_keys", "function": "decode_token", "line": 195}
{"timestamp": "2025-06-12T17:34:53.180410", "level": "DEBUG", "logger": "backend.services.auth", "message": "JWT decoded successfully with key manager", "module": "auth", "function": "authenticate_token", "line": 683}
{"timestamp": "2025-06-12T17:34:53.182413", "level": "DEBUG", "logger": "backend.services.db_utils", "message": "Query 'auth_lookup_e4ae2d8f-b32f-52d7-99cd-8a33b19652c7' completed in 0.001s", "module": "db_utils", "function": "execute_query_with_timeout", "line": 68}
{"timestamp": "2025-06-12T17:34:53.182478", "level": "DEBUG", "logger": "backend.services.db_utils", "message": "Auth query 'auth_lookup_e4ae2d8f-b32f-52d7-99cd-8a33b19652c7' successful, result format preserved", "module": "db_utils", "function": "execute_query_with_timeout", "line": 72}
{"timestamp": "2025-06-12T17:34:53.182546", "level": "DEBUG", "logger": "backend.services.auth", "message": "Successfully authenticated user: <EMAIL>", "module": "auth", "function": "get_current_user", "line": 732}
{"timestamp": "2025-06-12T17:34:53.182583", "level": "DEBUG", "logger": "backend.services.auth", "message": "Successfully authenticated user: <EMAIL>", "module": "auth", "function": "get_current_user", "line": 738}
{"timestamp": "2025-06-12T17:34:53.185057", "level": "DEBUG", "logger": "backend.routes.qr_preview", "message": "Received QR preview SVG request: {\"qr_type\": \"scooter_unlock\", \"data\": {\"scooter_id\": \"SCOOT123\", \"unlock_code\": \"UNLOCK42\", \"title\": \"Scooter_unlock QR Code\", \"description\": \"Scan to view scooter_unlock content\", \"service_provider\": \"Lime\", \"rate_info\": \"$20\"}, \"designOptions\": {\"size\": 256, \"margin\": 10, \"pattern\": \"square\", \"marker_border_style\": \"square\", \"marker_border_color\": \"#000000\", \"marker_center_style\": \"square\", \"marker_center_color\": \"#000000\", \"logo_size\": 0.3, \"logo_position\": \"center\", \"logo_opacity\": 1, \"logo_", "module": "qr_preview", "function": "preview_qr_svg", "line": 242}
{"timestamp": "2025-06-12T17:34:53.185247", "level": "DEBUG", "logger": "backend.routes.qr_preview", "message": "Form data keys: ['request', 'logo']", "module": "qr_preview", "function": "preview_qr_svg", "line": 297}
{"timestamp": "2025-06-12T17:34:53.185555", "level": "DEBUG", "logger": "backend.routes.qr_preview", "message": "Logo file found: type=<class 'starlette.datastructures.UploadFile'>, filename=LOGO - 1080 - ORANGE-02.png", "module": "qr_preview", "function": "preview_qr_svg", "line": 300}
{"timestamp": "2025-06-12T17:34:53.185608", "level": "DEBUG", "logger": "backend.routes.qr_preview", "message": "Logo content read: 142167 bytes", "module": "qr_preview", "function": "preview_qr_svg", "line": 305}
{"timestamp": "2025-06-12T17:34:53.185662", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Formatting content for QR type: qrtype.scooter_unlock, content keys: ['scooter_id', 'unlock_code', 'title', 'description', 'service_provider', 'rate_info']", "module": "qr_preview", "function": "_format_qr_data", "line": 876}
{"timestamp": "2025-06-12T17:34:53.208872", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Logo remove background setting: True, has logo: True", "module": "qr_preview", "function": "generate_preview_svg_modular", "line": 727}
{"timestamp": "2025-06-12T17:34:53.208942", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Logo area calculated: (20, 20, 37, 37), size_fraction: 0.3, size_modules: 17", "module": "qr_preview", "function": "generate_preview_svg_modular", "line": 739}
{"timestamp": "2025-06-12T17:34:53.225728", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Skipped 151 modules out of 1668 total modules for logo background removal", "module": "qr_preview", "function": "generate_preview_svg_modular", "line": 766}
{"timestamp": "2025-06-12T17:34:53.225886", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Drawing 3 finder patterns at positions: [(0, 0), (50, 0), (0, 50)]", "module": "qr_preview", "function": "generate_preview_svg_modular", "line": 775}
{"timestamp": "2025-06-12T17:34:53.225948", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "QR size: 57, border_style: square, center_style: square", "module": "qr_preview", "function": "generate_preview_svg_modular", "line": 776}
{"timestamp": "2025-06-12T17:34:53.226787", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Drawing finder pattern 1/3 at position (0, 0)", "module": "qr_preview", "function": "generate_preview_svg_modular", "line": 780}
{"timestamp": "2025-06-12T17:34:53.226857", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "_draw_finder_marker called: mx=0, my=0, module_size=5.087719298245614, border_style=square, center_style=square, border_color=#000000, center_color=#000000, offset=(20, 20)", "module": "qr_preview", "function": "_draw_finder_marker", "line": 97}
{"timestamp": "2025-06-12T17:34:53.226930", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Creating finder pattern group with id='marker-0-0'", "module": "qr_preview", "function": "_draw_finder_marker", "line": 110}
{"timestamp": "2025-06-12T17:34:53.226975", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Drawing square border:", "module": "qr_preview", "function": "_draw_finder_marker", "line": 160}
{"timestamp": "2025-06-12T17:34:53.227012", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "  Outer: insert=(20.0, 20.0), size=(35.614035087719294, 35.614035087719294)", "module": "qr_preview", "function": "_draw_finder_marker", "line": 161}
{"timestamp": "2025-06-12T17:34:53.227045", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "  Inner: insert=(26.105263157894736, 26.105263157894736), size=(23.403508771929822, 23.403508771929822)", "module": "qr_preview", "function": "_draw_finder_marker", "line": 162}
{"timestamp": "2025-06-12T17:34:53.227184", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Drawing marker center: style=square, color=#000000, pos=(32.719298245614034, 32.719298245614034), size=10.175438596491228", "module": "qr_preview", "function": "_draw_marker_center", "line": 179}
{"timestamp": "2025-06-12T17:34:53.227322", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Successfully added finder pattern group to SVG", "module": "qr_preview", "function": "_draw_finder_marker", "line": 171}
{"timestamp": "2025-06-12T17:34:53.227363", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Successfully drew finder pattern 1/3", "module": "qr_preview", "function": "generate_preview_svg_modular", "line": 782}
{"timestamp": "2025-06-12T17:34:53.227401", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Drawing finder pattern 2/3 at position (50, 0)", "module": "qr_preview", "function": "generate_preview_svg_modular", "line": 780}
{"timestamp": "2025-06-12T17:34:53.227440", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "_draw_finder_marker called: mx=50, my=0, module_size=5.087719298245614, border_style=square, center_style=square, border_color=#000000, center_color=#000000, offset=(20, 20)", "module": "qr_preview", "function": "_draw_finder_marker", "line": 97}
{"timestamp": "2025-06-12T17:34:53.227486", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Creating finder pattern group with id='marker-50-0'", "module": "qr_preview", "function": "_draw_finder_marker", "line": 110}
{"timestamp": "2025-06-12T17:34:53.227518", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Drawing square border:", "module": "qr_preview", "function": "_draw_finder_marker", "line": 160}
{"timestamp": "2025-06-12T17:34:53.227550", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "  Outer: insert=(274.38596491228066, 20.0), size=(35.614035087719294, 35.614035087719294)", "module": "qr_preview", "function": "_draw_finder_marker", "line": 161}
{"timestamp": "2025-06-12T17:34:53.227585", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "  Inner: insert=(280.4912280701754, 26.105263157894736), size=(23.403508771929822, 23.403508771929822)", "module": "qr_preview", "function": "_draw_finder_marker", "line": 162}
{"timestamp": "2025-06-12T17:34:53.227652", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Drawing marker center: style=square, color=#000000, pos=(287.10526315789474, 32.719298245614034), size=10.175438596491228", "module": "qr_preview", "function": "_draw_marker_center", "line": 179}
{"timestamp": "2025-06-12T17:34:53.227701", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Successfully added finder pattern group to SVG", "module": "qr_preview", "function": "_draw_finder_marker", "line": 171}
{"timestamp": "2025-06-12T17:34:53.227733", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Successfully drew finder pattern 2/3", "module": "qr_preview", "function": "generate_preview_svg_modular", "line": 782}
{"timestamp": "2025-06-12T17:34:53.227766", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Drawing finder pattern 3/3 at position (0, 50)", "module": "qr_preview", "function": "generate_preview_svg_modular", "line": 780}
{"timestamp": "2025-06-12T17:34:53.227798", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "_draw_finder_marker called: mx=0, my=50, module_size=5.087719298245614, border_style=square, center_style=square, border_color=#000000, center_color=#000000, offset=(20, 20)", "module": "qr_preview", "function": "_draw_finder_marker", "line": 97}
{"timestamp": "2025-06-12T17:34:53.227835", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Creating finder pattern group with id='marker-0-50'", "module": "qr_preview", "function": "_draw_finder_marker", "line": 110}
{"timestamp": "2025-06-12T17:34:53.227864", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Drawing square border:", "module": "qr_preview", "function": "_draw_finder_marker", "line": 160}
{"timestamp": "2025-06-12T17:34:53.227924", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "  Outer: insert=(20.0, 274.38596491228066), size=(35.614035087719294, 35.614035087719294)", "module": "qr_preview", "function": "_draw_finder_marker", "line": 161}
{"timestamp": "2025-06-12T17:34:53.227956", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "  Inner: insert=(26.105263157894736, 280.4912280701754), size=(23.403508771929822, 23.403508771929822)", "module": "qr_preview", "function": "_draw_finder_marker", "line": 162}
{"timestamp": "2025-06-12T17:34:53.228016", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Drawing marker center: style=square, color=#000000, pos=(32.719298245614034, 287.10526315789474), size=10.175438596491228", "module": "qr_preview", "function": "_draw_marker_center", "line": 179}
{"timestamp": "2025-06-12T17:34:53.228063", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Successfully added finder pattern group to SVG", "module": "qr_preview", "function": "_draw_finder_marker", "line": 171}
{"timestamp": "2025-06-12T17:34:53.228093", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Successfully drew finder pattern 3/3", "module": "qr_preview", "function": "generate_preview_svg_modular", "line": 782}
{"timestamp": "2025-06-12T17:34:53.228133", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "_draw_logo called with logo type: <class 'bytes'>, dimensions: 300", "module": "qr_preview", "function": "_draw_logo", "line": 434}
{"timestamp": "2025-06-12T17:34:53.556353", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Generated SVG length: 227869", "module": "qr_preview", "function": "generate_preview_svg_modular", "line": 802}
{"timestamp": "2025-06-12T17:34:53.556506", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "SVG contains marker-0-0: True", "module": "qr_preview", "function": "generate_preview_svg_modular", "line": 803}
{"timestamp": "2025-06-12T17:34:53.556632", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "SVG contains marker-18-0: False", "module": "qr_preview", "function": "generate_preview_svg_modular", "line": 804}
{"timestamp": "2025-06-12T17:34:53.556756", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "SVG contains marker-0-18: False", "module": "qr_preview", "function": "generate_preview_svg_modular", "line": 805}
{"timestamp": "2025-06-12T17:34:53.557111", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "SVG marker counts - outer: 3, inner: 3, dot: 0", "module": "qr_preview", "function": "generate_preview_svg_modular", "line": 813}
{"timestamp": "2025-06-12T17:34:53.558153", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "SVG label counts - text: 0, background: 0", "module": "qr_preview", "function": "generate_preview_svg_modular", "line": 814}
{"timestamp": "2025-06-12T17:34:53.560616", "level": "DEBUG", "logger": "backend.main", "message": "[Auth] Enforced Bearer token authentication for request", "module": "main", "function": "bearer_token_auth_middleware", "line": 274}
{"timestamp": "2025-06-12T17:34:56.362560", "level": "DEBUG", "logger": "backend.core.security.jwt_keys", "message": "Token contains key ID: 0bf271b1-2fbf-4d96-987f-e0b06b2fd890", "module": "jwt_keys", "function": "decode_token", "line": 188}
{"timestamp": "2025-06-12T17:34:56.362626", "level": "DEBUG", "logger": "backend.core.security.jwt_keys", "message": "Using key ID 0bf271b1-2fbf-4d96-987f-e0b06b2fd890 for verification", "module": "jwt_keys", "function": "decode_token", "line": 195}
{"timestamp": "2025-06-12T17:34:56.362766", "level": "DEBUG", "logger": "backend.services.auth", "message": "JWT decoded successfully with key manager", "module": "auth", "function": "authenticate_token", "line": 683}
{"timestamp": "2025-06-12T17:34:56.365595", "level": "DEBUG", "logger": "backend.services.db_utils", "message": "Query 'auth_lookup_e4ae2d8f-b32f-52d7-99cd-8a33b19652c7' completed in 0.003s", "module": "db_utils", "function": "execute_query_with_timeout", "line": 68}
{"timestamp": "2025-06-12T17:34:56.365898", "level": "DEBUG", "logger": "backend.services.db_utils", "message": "Auth query 'auth_lookup_e4ae2d8f-b32f-52d7-99cd-8a33b19652c7' successful, result format preserved", "module": "db_utils", "function": "execute_query_with_timeout", "line": 72}
{"timestamp": "2025-06-12T17:34:56.374579", "level": "DEBUG", "logger": "backend.main", "message": "[Auth] Enforced Bearer token authentication for request", "module": "main", "function": "bearer_token_auth_middleware", "line": 274}
{"timestamp": "2025-06-12T17:34:56.708362", "level": "DEBUG", "logger": "backend.core.security.jwt_keys", "message": "Token contains key ID: 0bf271b1-2fbf-4d96-987f-e0b06b2fd890", "module": "jwt_keys", "function": "decode_token", "line": 188}
{"timestamp": "2025-06-12T17:34:56.708497", "level": "DEBUG", "logger": "backend.core.security.jwt_keys", "message": "Using key ID 0bf271b1-2fbf-4d96-987f-e0b06b2fd890 for verification", "module": "jwt_keys", "function": "decode_token", "line": 195}
{"timestamp": "2025-06-12T17:34:56.708960", "level": "DEBUG", "logger": "backend.services.auth", "message": "JWT decoded successfully with key manager", "module": "auth", "function": "authenticate_token", "line": 683}
{"timestamp": "2025-06-12T17:34:56.710752", "level": "DEBUG", "logger": "backend.services.db_utils", "message": "Query 'auth_lookup_e4ae2d8f-b32f-52d7-99cd-8a33b19652c7' completed in 0.002s", "module": "db_utils", "function": "execute_query_with_timeout", "line": 68}
{"timestamp": "2025-06-12T17:34:56.710811", "level": "DEBUG", "logger": "backend.services.db_utils", "message": "Auth query 'auth_lookup_e4ae2d8f-b32f-52d7-99cd-8a33b19652c7' successful, result format preserved", "module": "db_utils", "function": "execute_query_with_timeout", "line": 72}
{"timestamp": "2025-06-12T17:34:56.718747", "level": "DEBUG", "logger": "backend.services.auth", "message": "Authentication attempt with token prefix: eyJhbGciOi...", "module": "auth", "function": "get_current_user", "line": 727}
{"timestamp": "2025-06-12T17:34:56.718869", "level": "DEBUG", "logger": "backend.core.security.jwt_keys", "message": "Token contains key ID: 0bf271b1-2fbf-4d96-987f-e0b06b2fd890", "module": "jwt_keys", "function": "decode_token", "line": 188}
{"timestamp": "2025-06-12T17:34:56.718908", "level": "DEBUG", "logger": "backend.core.security.jwt_keys", "message": "Using key ID 0bf271b1-2fbf-4d96-987f-e0b06b2fd890 for verification", "module": "jwt_keys", "function": "decode_token", "line": 195}
{"timestamp": "2025-06-12T17:34:56.719045", "level": "DEBUG", "logger": "backend.services.auth", "message": "JWT decoded successfully with key manager", "module": "auth", "function": "authenticate_token", "line": 683}
{"timestamp": "2025-06-12T17:34:56.720300", "level": "DEBUG", "logger": "backend.services.db_utils", "message": "Query 'auth_lookup_e4ae2d8f-b32f-52d7-99cd-8a33b19652c7' completed in 0.001s", "module": "db_utils", "function": "execute_query_with_timeout", "line": 68}
{"timestamp": "2025-06-12T17:34:56.720371", "level": "DEBUG", "logger": "backend.services.db_utils", "message": "Auth query 'auth_lookup_e4ae2d8f-b32f-52d7-99cd-8a33b19652c7' successful, result format preserved", "module": "db_utils", "function": "execute_query_with_timeout", "line": 72}
{"timestamp": "2025-06-12T17:34:56.720431", "level": "DEBUG", "logger": "backend.services.auth", "message": "Successfully authenticated user: <EMAIL>", "module": "auth", "function": "get_current_user", "line": 732}
{"timestamp": "2025-06-12T17:34:56.720468", "level": "DEBUG", "logger": "backend.services.auth", "message": "Successfully authenticated user: <EMAIL>", "module": "auth", "function": "get_current_user", "line": 738}
{"timestamp": "2025-06-12T17:34:56.722358", "level": "DEBUG", "logger": "backend.routes.qr_preview", "message": "Received QR preview SVG request: {\"qr_type\": \"scooter_unlock\", \"data\": {\"scooter_id\": \"SCOOT123\", \"unlock_code\": \"UNLOCK42\", \"title\": \"Scooter_unlock QR Code\", \"description\": \"Scan to view scooter_unlock content\", \"service_provider\": \"Lime\", \"rate_info\": \"$20\"}, \"designOptions\": {\"size\": 256, \"margin\": 10, \"pattern\": \"square\", \"marker_border_style\": \"square\", \"marker_center_style\": \"square\", \"logo_size\": 0.3, \"logo_position\": \"center\", \"logo_opacity\": 1, \"logo_remove_background\": true, \"frame_style\": \"none\", \"frame_font\": \"SF P", "module": "qr_preview", "function": "preview_qr_svg", "line": 242}
{"timestamp": "2025-06-12T17:34:56.722615", "level": "DEBUG", "logger": "backend.routes.qr_preview", "message": "Form data keys: ['request', 'logo']", "module": "qr_preview", "function": "preview_qr_svg", "line": 297}
{"timestamp": "2025-06-12T17:34:56.722656", "level": "DEBUG", "logger": "backend.routes.qr_preview", "message": "Logo file found: type=<class 'starlette.datastructures.UploadFile'>, filename=LOGO - 1080 - ORANGE-02.png", "module": "qr_preview", "function": "preview_qr_svg", "line": 300}
{"timestamp": "2025-06-12T17:34:56.722698", "level": "DEBUG", "logger": "backend.routes.qr_preview", "message": "Logo content read: 142167 bytes", "module": "qr_preview", "function": "preview_qr_svg", "line": 305}
{"timestamp": "2025-06-12T17:34:56.723031", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Formatting content for QR type: qrtype.scooter_unlock, content keys: ['scooter_id', 'unlock_code', 'title', 'description', 'service_provider', 'rate_info']", "module": "qr_preview", "function": "_format_qr_data", "line": 876}
{"timestamp": "2025-06-12T17:34:56.746732", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Logo remove background setting: True, has logo: True", "module": "qr_preview", "function": "generate_preview_svg_modular", "line": 727}
{"timestamp": "2025-06-12T17:34:56.746801", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Logo area calculated: (20, 20, 37, 37), size_fraction: 0.3, size_modules: 17", "module": "qr_preview", "function": "generate_preview_svg_modular", "line": 739}
{"timestamp": "2025-06-12T17:34:56.763772", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Skipped 151 modules out of 1668 total modules for logo background removal", "module": "qr_preview", "function": "generate_preview_svg_modular", "line": 766}
{"timestamp": "2025-06-12T17:34:56.763835", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Drawing 3 finder patterns at positions: [(0, 0), (50, 0), (0, 50)]", "module": "qr_preview", "function": "generate_preview_svg_modular", "line": 775}
{"timestamp": "2025-06-12T17:34:56.763873", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "QR size: 57, border_style: square, center_style: square", "module": "qr_preview", "function": "generate_preview_svg_modular", "line": 776}
{"timestamp": "2025-06-12T17:34:56.763909", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Drawing finder pattern 1/3 at position (0, 0)", "module": "qr_preview", "function": "generate_preview_svg_modular", "line": 780}
{"timestamp": "2025-06-12T17:34:56.763953", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "_draw_finder_marker called: mx=0, my=0, module_size=5.087719298245614, border_style=square, center_style=square, border_color=#000000, center_color=#000000, offset=(20, 20)", "module": "qr_preview", "function": "_draw_finder_marker", "line": 97}
{"timestamp": "2025-06-12T17:34:56.764007", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Creating finder pattern group with id='marker-0-0'", "module": "qr_preview", "function": "_draw_finder_marker", "line": 110}
{"timestamp": "2025-06-12T17:34:56.764040", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Drawing square border:", "module": "qr_preview", "function": "_draw_finder_marker", "line": 160}
{"timestamp": "2025-06-12T17:34:56.764076", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "  Outer: insert=(20.0, 20.0), size=(35.614035087719294, 35.614035087719294)", "module": "qr_preview", "function": "_draw_finder_marker", "line": 161}
{"timestamp": "2025-06-12T17:34:56.764158", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "  Inner: insert=(26.105263157894736, 26.105263157894736), size=(23.403508771929822, 23.403508771929822)", "module": "qr_preview", "function": "_draw_finder_marker", "line": 162}
{"timestamp": "2025-06-12T17:34:56.764228", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Drawing marker center: style=square, color=#000000, pos=(32.719298245614034, 32.719298245614034), size=10.175438596491228", "module": "qr_preview", "function": "_draw_marker_center", "line": 179}
{"timestamp": "2025-06-12T17:34:56.764283", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Successfully added finder pattern group to SVG", "module": "qr_preview", "function": "_draw_finder_marker", "line": 171}
{"timestamp": "2025-06-12T17:34:56.764315", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Successfully drew finder pattern 1/3", "module": "qr_preview", "function": "generate_preview_svg_modular", "line": 782}
{"timestamp": "2025-06-12T17:34:56.764346", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Drawing finder pattern 2/3 at position (50, 0)", "module": "qr_preview", "function": "generate_preview_svg_modular", "line": 780}
{"timestamp": "2025-06-12T17:34:56.764377", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "_draw_finder_marker called: mx=50, my=0, module_size=5.087719298245614, border_style=square, center_style=square, border_color=#000000, center_color=#000000, offset=(20, 20)", "module": "qr_preview", "function": "_draw_finder_marker", "line": 97}
{"timestamp": "2025-06-12T17:34:56.764592", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Creating finder pattern group with id='marker-50-0'", "module": "qr_preview", "function": "_draw_finder_marker", "line": 110}
{"timestamp": "2025-06-12T17:34:56.764624", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Drawing square border:", "module": "qr_preview", "function": "_draw_finder_marker", "line": 160}
{"timestamp": "2025-06-12T17:34:56.764658", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "  Outer: insert=(274.38596491228066, 20.0), size=(35.614035087719294, 35.614035087719294)", "module": "qr_preview", "function": "_draw_finder_marker", "line": 161}
{"timestamp": "2025-06-12T17:34:56.764692", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "  Inner: insert=(280.4912280701754, 26.105263157894736), size=(23.403508771929822, 23.403508771929822)", "module": "qr_preview", "function": "_draw_finder_marker", "line": 162}
{"timestamp": "2025-06-12T17:34:56.764750", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Drawing marker center: style=square, color=#000000, pos=(287.10526315789474, 32.719298245614034), size=10.175438596491228", "module": "qr_preview", "function": "_draw_marker_center", "line": 179}
{"timestamp": "2025-06-12T17:34:56.764839", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Successfully added finder pattern group to SVG", "module": "qr_preview", "function": "_draw_finder_marker", "line": 171}
{"timestamp": "2025-06-12T17:34:56.764870", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Successfully drew finder pattern 2/3", "module": "qr_preview", "function": "generate_preview_svg_modular", "line": 782}
{"timestamp": "2025-06-12T17:34:56.764905", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Drawing finder pattern 3/3 at position (0, 50)", "module": "qr_preview", "function": "generate_preview_svg_modular", "line": 780}
{"timestamp": "2025-06-12T17:34:56.764937", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "_draw_finder_marker called: mx=0, my=50, module_size=5.087719298245614, border_style=square, center_style=square, border_color=#000000, center_color=#000000, offset=(20, 20)", "module": "qr_preview", "function": "_draw_finder_marker", "line": 97}
{"timestamp": "2025-06-12T17:34:56.765011", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Creating finder pattern group with id='marker-0-50'", "module": "qr_preview", "function": "_draw_finder_marker", "line": 110}
{"timestamp": "2025-06-12T17:34:56.765042", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Drawing square border:", "module": "qr_preview", "function": "_draw_finder_marker", "line": 160}
{"timestamp": "2025-06-12T17:34:56.765074", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "  Outer: insert=(20.0, 274.38596491228066), size=(35.614035087719294, 35.614035087719294)", "module": "qr_preview", "function": "_draw_finder_marker", "line": 161}
{"timestamp": "2025-06-12T17:34:56.765110", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "  Inner: insert=(26.105263157894736, 280.4912280701754), size=(23.403508771929822, 23.403508771929822)", "module": "qr_preview", "function": "_draw_finder_marker", "line": 162}
{"timestamp": "2025-06-12T17:34:56.765166", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Drawing marker center: style=square, color=#000000, pos=(32.719298245614034, 287.10526315789474), size=10.175438596491228", "module": "qr_preview", "function": "_draw_marker_center", "line": 179}
{"timestamp": "2025-06-12T17:34:56.765209", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Successfully added finder pattern group to SVG", "module": "qr_preview", "function": "_draw_finder_marker", "line": 171}
{"timestamp": "2025-06-12T17:34:56.766742", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Successfully drew finder pattern 3/3", "module": "qr_preview", "function": "generate_preview_svg_modular", "line": 782}
{"timestamp": "2025-06-12T17:34:56.766806", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "_draw_logo called with logo type: <class 'bytes'>, dimensions: 300", "module": "qr_preview", "function": "_draw_logo", "line": 434}
{"timestamp": "2025-06-12T17:34:57.107743", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Generated SVG length: 227869", "module": "qr_preview", "function": "generate_preview_svg_modular", "line": 802}
{"timestamp": "2025-06-12T17:34:57.107914", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "SVG contains marker-0-0: True", "module": "qr_preview", "function": "generate_preview_svg_modular", "line": 803}
{"timestamp": "2025-06-12T17:34:57.108043", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "SVG contains marker-18-0: False", "module": "qr_preview", "function": "generate_preview_svg_modular", "line": 804}
{"timestamp": "2025-06-12T17:34:57.108172", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "SVG contains marker-0-18: False", "module": "qr_preview", "function": "generate_preview_svg_modular", "line": 805}
{"timestamp": "2025-06-12T17:34:57.108542", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "SVG marker counts - outer: 3, inner: 3, dot: 0", "module": "qr_preview", "function": "generate_preview_svg_modular", "line": 813}
{"timestamp": "2025-06-12T17:34:57.108576", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "SVG label counts - text: 0, background: 0", "module": "qr_preview", "function": "generate_preview_svg_modular", "line": 814}
{"timestamp": "2025-06-12T17:34:57.110765", "level": "DEBUG", "logger": "backend.main", "message": "[Auth] Enforced Bearer token authentication for request", "module": "main", "function": "bearer_token_auth_middleware", "line": 274}
{"timestamp": "2025-06-12T17:34:57.205727", "level": "DEBUG", "logger": "backend.core.security.jwt_keys", "message": "Token contains key ID: 0bf271b1-2fbf-4d96-987f-e0b06b2fd890", "module": "jwt_keys", "function": "decode_token", "line": 188}
{"timestamp": "2025-06-12T17:34:57.205811", "level": "DEBUG", "logger": "backend.core.security.jwt_keys", "message": "Using key ID 0bf271b1-2fbf-4d96-987f-e0b06b2fd890 for verification", "module": "jwt_keys", "function": "decode_token", "line": 195}
{"timestamp": "2025-06-12T17:34:57.205953", "level": "DEBUG", "logger": "backend.services.auth", "message": "JWT decoded successfully with key manager", "module": "auth", "function": "authenticate_token", "line": 683}
{"timestamp": "2025-06-12T17:34:57.207401", "level": "DEBUG", "logger": "backend.services.db_utils", "message": "Query 'auth_lookup_e4ae2d8f-b32f-52d7-99cd-8a33b19652c7' completed in 0.001s", "module": "db_utils", "function": "execute_query_with_timeout", "line": 68}
{"timestamp": "2025-06-12T17:34:57.207454", "level": "DEBUG", "logger": "backend.services.db_utils", "message": "Auth query 'auth_lookup_e4ae2d8f-b32f-52d7-99cd-8a33b19652c7' successful, result format preserved", "module": "db_utils", "function": "execute_query_with_timeout", "line": 72}
{"timestamp": "2025-06-12T17:34:57.219362", "level": "DEBUG", "logger": "backend.services.auth", "message": "Authentication attempt with token prefix: eyJhbGciOi...", "module": "auth", "function": "get_current_user", "line": 727}
{"timestamp": "2025-06-12T17:34:57.219490", "level": "DEBUG", "logger": "backend.core.security.jwt_keys", "message": "Token contains key ID: 0bf271b1-2fbf-4d96-987f-e0b06b2fd890", "module": "jwt_keys", "function": "decode_token", "line": 188}
{"timestamp": "2025-06-12T17:34:57.219539", "level": "DEBUG", "logger": "backend.core.security.jwt_keys", "message": "Using key ID 0bf271b1-2fbf-4d96-987f-e0b06b2fd890 for verification", "module": "jwt_keys", "function": "decode_token", "line": 195}
{"timestamp": "2025-06-12T17:34:57.219666", "level": "DEBUG", "logger": "backend.services.auth", "message": "JWT decoded successfully with key manager", "module": "auth", "function": "authenticate_token", "line": 683}
{"timestamp": "2025-06-12T17:34:57.223721", "level": "DEBUG", "logger": "backend.services.db_utils", "message": "Query 'auth_lookup_e4ae2d8f-b32f-52d7-99cd-8a33b19652c7' completed in 0.004s", "module": "db_utils", "function": "execute_query_with_timeout", "line": 68}
{"timestamp": "2025-06-12T17:34:57.223780", "level": "DEBUG", "logger": "backend.services.db_utils", "message": "Auth query 'auth_lookup_e4ae2d8f-b32f-52d7-99cd-8a33b19652c7' successful, result format preserved", "module": "db_utils", "function": "execute_query_with_timeout", "line": 72}
{"timestamp": "2025-06-12T17:34:57.223844", "level": "DEBUG", "logger": "backend.services.auth", "message": "Successfully authenticated user: <EMAIL>", "module": "auth", "function": "get_current_user", "line": 732}
{"timestamp": "2025-06-12T17:34:57.223882", "level": "DEBUG", "logger": "backend.services.auth", "message": "Successfully authenticated user: <EMAIL>", "module": "auth", "function": "get_current_user", "line": 738}
{"timestamp": "2025-06-12T17:34:57.229133", "level": "DEBUG", "logger": "backend.routes.qr_preview", "message": "Received QR preview SVG request: {\"qr_type\": \"scooter_unlock\", \"data\": {\"scooter_id\": \"SCOOT123\", \"unlock_code\": \"UNLOCK42\", \"title\": \"Scooter_unlock QR Code\", \"description\": \"Scan to view scooter_unlock content\", \"service_provider\": \"Lime\", \"rate_info\": \"$20\"}, \"designOptions\": {\"size\": 256, \"margin\": 10, \"pattern\": \"square\", \"marker_border_style\": \"square\", \"marker_border_color\": \"#000000\", \"marker_center_style\": \"square\", \"marker_center_color\": \"#000000\", \"logo_size\": 0.3, \"logo_position\": \"center\", \"logo_opacity\": 1, \"logo_", "module": "qr_preview", "function": "preview_qr_svg", "line": 242}
{"timestamp": "2025-06-12T17:34:57.229216", "level": "DEBUG", "logger": "backend.routes.qr_preview", "message": "Form data keys: ['request', 'logo']", "module": "qr_preview", "function": "preview_qr_svg", "line": 297}
{"timestamp": "2025-06-12T17:34:57.229557", "level": "DEBUG", "logger": "backend.routes.qr_preview", "message": "Logo file found: type=<class 'starlette.datastructures.UploadFile'>, filename=LOGO - 1080 - ORANGE-02.png", "module": "qr_preview", "function": "preview_qr_svg", "line": 300}
{"timestamp": "2025-06-12T17:34:57.229683", "level": "DEBUG", "logger": "backend.routes.qr_preview", "message": "Logo content read: 142167 bytes", "module": "qr_preview", "function": "preview_qr_svg", "line": 305}
{"timestamp": "2025-06-12T17:34:57.229729", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Formatting content for QR type: qrtype.scooter_unlock, content keys: ['scooter_id', 'unlock_code', 'title', 'description', 'service_provider', 'rate_info']", "module": "qr_preview", "function": "_format_qr_data", "line": 876}
{"timestamp": "2025-06-12T17:34:57.253023", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Logo remove background setting: True, has logo: True", "module": "qr_preview", "function": "generate_preview_svg_modular", "line": 727}
{"timestamp": "2025-06-12T17:34:57.253089", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Logo area calculated: (20, 20, 37, 37), size_fraction: 0.3, size_modules: 17", "module": "qr_preview", "function": "generate_preview_svg_modular", "line": 739}
{"timestamp": "2025-06-12T17:34:57.270323", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Skipped 151 modules out of 1668 total modules for logo background removal", "module": "qr_preview", "function": "generate_preview_svg_modular", "line": 766}
{"timestamp": "2025-06-12T17:34:57.270394", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Drawing 3 finder patterns at positions: [(0, 0), (50, 0), (0, 50)]", "module": "qr_preview", "function": "generate_preview_svg_modular", "line": 775}
{"timestamp": "2025-06-12T17:34:57.270437", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "QR size: 57, border_style: square, center_style: square", "module": "qr_preview", "function": "generate_preview_svg_modular", "line": 776}
{"timestamp": "2025-06-12T17:34:57.270477", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Drawing finder pattern 1/3 at position (0, 0)", "module": "qr_preview", "function": "generate_preview_svg_modular", "line": 780}
{"timestamp": "2025-06-12T17:34:57.270733", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "_draw_finder_marker called: mx=0, my=0, module_size=5.087719298245614, border_style=square, center_style=square, border_color=#000000, center_color=#000000, offset=(20, 20)", "module": "qr_preview", "function": "_draw_finder_marker", "line": 97}
{"timestamp": "2025-06-12T17:34:57.270789", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Creating finder pattern group with id='marker-0-0'", "module": "qr_preview", "function": "_draw_finder_marker", "line": 110}
{"timestamp": "2025-06-12T17:34:57.270831", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Drawing square border:", "module": "qr_preview", "function": "_draw_finder_marker", "line": 160}
{"timestamp": "2025-06-12T17:34:57.270872", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "  Outer: insert=(20.0, 20.0), size=(35.614035087719294, 35.614035087719294)", "module": "qr_preview", "function": "_draw_finder_marker", "line": 161}
{"timestamp": "2025-06-12T17:34:57.270914", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "  Inner: insert=(26.105263157894736, 26.105263157894736), size=(23.403508771929822, 23.403508771929822)", "module": "qr_preview", "function": "_draw_finder_marker", "line": 162}
{"timestamp": "2025-06-12T17:34:57.270994", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Drawing marker center: style=square, color=#000000, pos=(32.719298245614034, 32.719298245614034), size=10.175438596491228", "module": "qr_preview", "function": "_draw_marker_center", "line": 179}
{"timestamp": "2025-06-12T17:34:57.271046", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Successfully added finder pattern group to SVG", "module": "qr_preview", "function": "_draw_finder_marker", "line": 171}
{"timestamp": "2025-06-12T17:34:57.271138", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Successfully drew finder pattern 1/3", "module": "qr_preview", "function": "generate_preview_svg_modular", "line": 782}
{"timestamp": "2025-06-12T17:34:57.271175", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Drawing finder pattern 2/3 at position (50, 0)", "module": "qr_preview", "function": "generate_preview_svg_modular", "line": 780}
{"timestamp": "2025-06-12T17:34:57.271218", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "_draw_finder_marker called: mx=50, my=0, module_size=5.087719298245614, border_style=square, center_style=square, border_color=#000000, center_color=#000000, offset=(20, 20)", "module": "qr_preview", "function": "_draw_finder_marker", "line": 97}
{"timestamp": "2025-06-12T17:34:57.271268", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Creating finder pattern group with id='marker-50-0'", "module": "qr_preview", "function": "_draw_finder_marker", "line": 110}
{"timestamp": "2025-06-12T17:34:57.271302", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Drawing square border:", "module": "qr_preview", "function": "_draw_finder_marker", "line": 160}
{"timestamp": "2025-06-12T17:34:57.271337", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "  Outer: insert=(274.38596491228066, 20.0), size=(35.614035087719294, 35.614035087719294)", "module": "qr_preview", "function": "_draw_finder_marker", "line": 161}
{"timestamp": "2025-06-12T17:34:57.271443", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "  Inner: insert=(280.4912280701754, 26.105263157894736), size=(23.403508771929822, 23.403508771929822)", "module": "qr_preview", "function": "_draw_finder_marker", "line": 162}
{"timestamp": "2025-06-12T17:34:57.271505", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Drawing marker center: style=square, color=#000000, pos=(287.10526315789474, 32.719298245614034), size=10.175438596491228", "module": "qr_preview", "function": "_draw_marker_center", "line": 179}
{"timestamp": "2025-06-12T17:34:57.271554", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Successfully added finder pattern group to SVG", "module": "qr_preview", "function": "_draw_finder_marker", "line": 171}
{"timestamp": "2025-06-12T17:34:57.271590", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Successfully drew finder pattern 2/3", "module": "qr_preview", "function": "generate_preview_svg_modular", "line": 782}
{"timestamp": "2025-06-12T17:34:57.273038", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Drawing finder pattern 3/3 at position (0, 50)", "module": "qr_preview", "function": "generate_preview_svg_modular", "line": 780}
{"timestamp": "2025-06-12T17:34:57.273075", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "_draw_finder_marker called: mx=0, my=50, module_size=5.087719298245614, border_style=square, center_style=square, border_color=#000000, center_color=#000000, offset=(20, 20)", "module": "qr_preview", "function": "_draw_finder_marker", "line": 97}
{"timestamp": "2025-06-12T17:34:57.273119", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Creating finder pattern group with id='marker-0-50'", "module": "qr_preview", "function": "_draw_finder_marker", "line": 110}
{"timestamp": "2025-06-12T17:34:57.273153", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Drawing square border:", "module": "qr_preview", "function": "_draw_finder_marker", "line": 160}
{"timestamp": "2025-06-12T17:34:57.273188", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "  Outer: insert=(20.0, 274.38596491228066), size=(35.614035087719294, 35.614035087719294)", "module": "qr_preview", "function": "_draw_finder_marker", "line": 161}
{"timestamp": "2025-06-12T17:34:57.276892", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "  Inner: insert=(26.105263157894736, 280.4912280701754), size=(23.403508771929822, 23.403508771929822)", "module": "qr_preview", "function": "_draw_finder_marker", "line": 162}
{"timestamp": "2025-06-12T17:34:57.277041", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Drawing marker center: style=square, color=#000000, pos=(32.719298245614034, 287.10526315789474), size=10.175438596491228", "module": "qr_preview", "function": "_draw_marker_center", "line": 179}
{"timestamp": "2025-06-12T17:34:57.277105", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Successfully added finder pattern group to SVG", "module": "qr_preview", "function": "_draw_finder_marker", "line": 171}
{"timestamp": "2025-06-12T17:34:57.277147", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Successfully drew finder pattern 3/3", "module": "qr_preview", "function": "generate_preview_svg_modular", "line": 782}
{"timestamp": "2025-06-12T17:34:57.278128", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "_draw_logo called with logo type: <class 'bytes'>, dimensions: 300", "module": "qr_preview", "function": "_draw_logo", "line": 434}
{"timestamp": "2025-06-12T17:34:57.605913", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Generated SVG length: 227869", "module": "qr_preview", "function": "generate_preview_svg_modular", "line": 802}
{"timestamp": "2025-06-12T17:34:57.606061", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "SVG contains marker-0-0: True", "module": "qr_preview", "function": "generate_preview_svg_modular", "line": 803}
{"timestamp": "2025-06-12T17:34:57.606178", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "SVG contains marker-18-0: False", "module": "qr_preview", "function": "generate_preview_svg_modular", "line": 804}
{"timestamp": "2025-06-12T17:34:57.606295", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "SVG contains marker-0-18: False", "module": "qr_preview", "function": "generate_preview_svg_modular", "line": 805}
{"timestamp": "2025-06-12T17:34:57.606642", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "SVG marker counts - outer: 3, inner: 3, dot: 0", "module": "qr_preview", "function": "generate_preview_svg_modular", "line": 813}
{"timestamp": "2025-06-12T17:34:57.612325", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "SVG label counts - text: 0, background: 0", "module": "qr_preview", "function": "generate_preview_svg_modular", "line": 814}
{"timestamp": "2025-06-12T17:34:57.614442", "level": "DEBUG", "logger": "backend.main", "message": "[Auth] Enforced Bearer token authentication for request", "module": "main", "function": "bearer_token_auth_middleware", "line": 274}
{"timestamp": "2025-06-12T17:34:59.137304", "level": "DEBUG", "logger": "backend.main", "message": "[Auth] Enforced Bearer token authentication for request", "module": "main", "function": "bearer_token_auth_middleware", "line": 274}
{"timestamp": "2025-06-12T17:35:29.089946", "level": "DEBUG", "logger": "backend.main", "message": "[Auth] Enforced Bearer token authentication for request", "module": "main", "function": "bearer_token_auth_middleware", "line": 274}
{"timestamp": "2025-06-12T17:35:29.114786", "level": "DEBUG", "logger": "backend.main", "message": "[Auth] Enforced Bearer token authentication for request", "module": "main", "function": "bearer_token_auth_middleware", "line": 274}
{"timestamp": "2025-06-12T17:35:59.322310", "level": "DEBUG", "logger": "backend.main", "message": "[Auth] Enforced Bearer token authentication for request", "module": "main", "function": "bearer_token_auth_middleware", "line": 274}
{"timestamp": "2025-06-12T17:36:29.848581", "level": "DEBUG", "logger": "backend.main", "message": "[Auth] Enforced Bearer token authentication for request", "module": "main", "function": "bearer_token_auth_middleware", "line": 274}
{"timestamp": "2025-06-12T17:36:30.796425", "level": "DEBUG", "logger": "backend.main", "message": "[Auth] Enforced Bearer token authentication for request", "module": "main", "function": "bearer_token_auth_middleware", "line": 274}
{"timestamp": "2025-06-12T17:37:00.857592", "level": "DEBUG", "logger": "backend.main", "message": "[Auth] Enforced Bearer token authentication for request", "module": "main", "function": "bearer_token_auth_middleware", "line": 274}
{"timestamp": "2025-06-12T17:37:29.782442", "level": "DEBUG", "logger": "backend.main", "message": "[Auth] Enforced Bearer token authentication for request", "module": "main", "function": "bearer_token_auth_middleware", "line": 274}
{"timestamp": "2025-06-12T17:37:31.687809", "level": "DEBUG", "logger": "backend.main", "message": "[Auth] Enforced Bearer token authentication for request", "module": "main", "function": "bearer_token_auth_middleware", "line": 274}
{"timestamp": "2025-06-12T17:38:01.853291", "level": "DEBUG", "logger": "backend.main", "message": "[Auth] Enforced Bearer token authentication for request", "module": "main", "function": "bearer_token_auth_middleware", "line": 274}
{"timestamp": "2025-06-12T17:38:34.821478", "level": "DEBUG", "logger": "backend.main", "message": "[Auth] Enforced Bearer token authentication for request", "module": "main", "function": "bearer_token_auth_middleware", "line": 274}
{"timestamp": "2025-06-12T17:38:35.904643", "level": "DEBUG", "logger": "backend.main", "message": "[Auth] Enforced Bearer token authentication for request", "module": "main", "function": "bearer_token_auth_middleware", "line": 274}
{"timestamp": "2025-06-12T17:39:05.902828", "level": "DEBUG", "logger": "backend.main", "message": "[Auth] Enforced Bearer token authentication for request", "module": "main", "function": "bearer_token_auth_middleware", "line": 274}
{"timestamp": "2025-06-12T17:39:34.984179", "level": "DEBUG", "logger": "backend.main", "message": "[Auth] Enforced Bearer token authentication for request", "module": "main", "function": "bearer_token_auth_middleware", "line": 274}
{"timestamp": "2025-06-12T17:39:35.826909", "level": "DEBUG", "logger": "backend.main", "message": "[Auth] Enforced Bearer token authentication for request", "module": "main", "function": "bearer_token_auth_middleware", "line": 274}
{"timestamp": "2025-06-12T17:40:06.068379", "level": "DEBUG", "logger": "backend.main", "message": "[Auth] Enforced Bearer token authentication for request", "module": "main", "function": "bearer_token_auth_middleware", "line": 274}
{"timestamp": "2025-06-12T17:40:34.891879", "level": "DEBUG", "logger": "backend.main", "message": "[Auth] Enforced Bearer token authentication for request", "module": "main", "function": "bearer_token_auth_middleware", "line": 274}
{"timestamp": "2025-06-12T17:40:36.799145", "level": "DEBUG", "logger": "backend.main", "message": "[Auth] Enforced Bearer token authentication for request", "module": "main", "function": "bearer_token_auth_middleware", "line": 274}
{"timestamp": "2025-06-12T17:41:14.819810", "level": "DEBUG", "logger": "backend.main", "message": "[Auth] Enforced Bearer token authentication for request", "module": "main", "function": "bearer_token_auth_middleware", "line": 274}
{"timestamp": "2025-06-12T17:41:34.896685", "level": "DEBUG", "logger": "backend.main", "message": "[Auth] Enforced Bearer token authentication for request", "module": "main", "function": "bearer_token_auth_middleware", "line": 274}
{"timestamp": "2025-06-12T17:41:38.240735", "level": "DEBUG", "logger": "backend.main", "message": "[Auth] Enforced Bearer token authentication for request", "module": "main", "function": "bearer_token_auth_middleware", "line": 274}
{"timestamp": "2025-06-12T17:42:09.831758", "level": "DEBUG", "logger": "backend.main", "message": "[Auth] Enforced Bearer token authentication for request", "module": "main", "function": "bearer_token_auth_middleware", "line": 274}
{"timestamp": "2025-06-12T17:42:34.859036", "level": "DEBUG", "logger": "backend.main", "message": "[Auth] Enforced Bearer token authentication for request", "module": "main", "function": "bearer_token_auth_middleware", "line": 274}
{"timestamp": "2025-06-12T17:42:39.258723", "level": "DEBUG", "logger": "backend.main", "message": "[Auth] Enforced Bearer token authentication for request", "module": "main", "function": "bearer_token_auth_middleware", "line": 274}
{"timestamp": "2025-06-12T17:43:04.370020", "level": "INFO", "logger": "backend.services.qr_preview_cache", "message": "QR preview cache cleanup task stopped", "module": "qr_preview_cache", "function": "stop", "line": 257}
{"timestamp": "2025-06-12T17:43:04.370250", "level": "INFO", "logger": "backend.main", "message": "QR preview cache stopped", "module": "main", "function": "shutdown_qr_preview_cache", "line": 352}
{"timestamp": "2025-06-12T17:43:11.330206", "level": "INFO", "logger": "backend.main", "message": "=== QRVibe API Startup: Logging to file is enabled ===", "module": "main", "function": "<module>", "line": 37}
{"timestamp": "2025-06-12T17:43:11.330332", "level": "INFO", "logger": "backend.main", "message": "Applying critical patches before initialization", "module": "main", "function": "<module>", "line": 43}
{"timestamp": "2025-06-12T17:43:11.330402", "level": "INFO", "logger": "backend.patches", "message": "Applying patches (none required)", "module": "__init__", "function": "apply_patches", "line": 11}
{"timestamp": "2025-06-12T17:43:11.330650", "level": "INFO", "logger": "backend.core.middleware.cors", "message": "Setting up CORS middleware with origins: ['http://localhost:3000', 'https://localhost:3000', 'https://127.0.0.1:3000', 'http://127.0.0.1:3000']", "module": "cors", "function": "setup_cors", "line": 86}
{"timestamp": "2025-06-12T17:43:11.330717", "level": "INFO", "logger": "backend.core.middleware.cors", "message": "CORS middleware configured - preflight requests should be handled automatically", "module": "cors", "function": "setup_cors", "line": 98}
{"timestamp": "2025-06-12T17:43:11.332537", "level": "INFO", "logger": "backend.middleware.security_middleware", "message": "Added Enhanced Security Middleware with tiered rate limiting", "module": "security_middleware", "function": "add_security_middleware", "line": 176}
{"timestamp": "2025-06-12T17:43:11.333127", "level": "INFO", "logger": "backend.main", "message": "Mounting storage directory: /Users/<USER>/aihub/qrvibe/storage", "module": "main", "function": "<module>", "line": 107}
{"timestamp": "2025-06-12T17:43:11.333277", "level": "INFO", "logger": "backend.main", "message": "Storage directory absolute path: /Users/<USER>/aihub/qrvibe/storage", "module": "main", "function": "<module>", "line": 119}
{"timestamp": "2025-06-12T17:43:11.812276", "level": "INFO", "logger": "root", "message": "Storage initialized: environment=test, storage_dir=/Users/<USER>/aihub/qrvibe/storage, s3_enabled=True", "module": "storage", "function": "__init__", "line": 41}
{"timestamp": "2025-06-12T17:43:11.815448", "level": "INFO", "logger": "root", "message": "S3 storage enabled with bucket: qrverse and CDN: https://dev-cdn.qrvibe.com", "module": "storage", "function": "__init__", "line": 59}
{"timestamp": "2025-06-12T17:43:11.883607", "level": "INFO", "logger": "root", "message": "Storage initialized: environment=test, storage_dir=/Users/<USER>/aihub/qrvibe/storage, s3_enabled=True", "module": "storage", "function": "__init__", "line": 41}
{"timestamp": "2025-06-12T17:43:11.893850", "level": "INFO", "logger": "root", "message": "S3 storage enabled with bucket: qrverse and CDN: https://dev-cdn.qrvibe.com", "module": "storage", "function": "__init__", "line": 59}
{"timestamp": "2025-06-12T17:43:11.950295", "level": "INFO", "logger": "backend.middleware.security_middleware", "message": "Enhanced security middleware initialized with tiered rate limiting", "module": "security_middleware", "function": "__init__", "line": 87}
{"timestamp": "2025-06-12T17:43:11.950472", "level": "INFO", "logger": "backend.core.middleware.csrf", "message": "CSRF Protection Middleware initialized with exclude paths: ['/auth/jwt/login', '/auth/logout', '/auth/register', '/auth/2fa', '/auth/me', '/auth/verify-email', '/auth/reset-password', '/admin/login', '/admin/logout']", "module": "csrf", "function": "__init__", "line": 44}
{"timestamp": "2025-06-12T17:43:12.105011", "level": "INFO", "logger": "backend.services.token_blacklist", "message": "Token blacklist service initialized with Redis", "module": "token_blacklist", "function": "initialize", "line": 59}
{"timestamp": "2025-06-12T17:43:12.105097", "level": "INFO", "logger": "backend.main", "message": "Token blacklist service initialized", "module": "main", "function": "initialize_token_blacklist", "line": 98}
{"timestamp": "2025-06-12T17:43:12.108163", "level": "INFO", "logger": "backend.services.qr_preview_cache", "message": "QR preview cache initialized at /Users/<USER>/aihub/qrvibe/storage/cache/qr_preview_cache.db", "module": "qr_preview_cache", "function": "_init_db", "line": 56}
{"timestamp": "2025-06-12T17:43:12.108528", "level": "INFO", "logger": "backend.services.qr_preview_cache", "message": "Started QR preview cache cleanup task", "module": "qr_preview_cache", "function": "start_cleanup_task", "line": 62}
{"timestamp": "2025-06-12T17:43:12.109016", "level": "INFO", "logger": "backend.main", "message": "QR preview cache initialized and cleanup task started", "module": "main", "function": "init_qr_preview_cache", "line": 349}
{"timestamp": "2025-06-12T17:43:12.122559", "level": "DEBUG", "logger": "backend.main", "message": "[Auth] Enforced Bearer token authentication for request", "module": "main", "function": "bearer_token_auth_middleware", "line": 274}
{"timestamp": "2025-06-12T17:43:22.649478", "level": "INFO", "logger": "backend.services.qr_preview_cache", "message": "QR preview cache cleanup task stopped", "module": "qr_preview_cache", "function": "stop", "line": 257}
{"timestamp": "2025-06-12T17:43:22.649778", "level": "INFO", "logger": "backend.main", "message": "QR preview cache stopped", "module": "main", "function": "shutdown_qr_preview_cache", "line": 356}
