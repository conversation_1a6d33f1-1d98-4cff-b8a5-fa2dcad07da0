{"timestamp": "2025-06-12T17:25:28.066117", "level": "DEBUG", "logger": "backend.main", "message": "[Auth] Enforced Bearer token authentication for request", "module": "main", "function": "bearer_token_auth_middleware", "line": 274}
{"timestamp": "2025-06-12T17:25:29.413890", "level": "DEBUG", "logger": "backend.core.security.jwt_keys", "message": "Token contains key ID: a69f821a-65e8-4292-8feb-985dc7ac94d9", "module": "jwt_keys", "function": "decode_token", "line": 188}
{"timestamp": "2025-06-12T17:25:29.413961", "level": "DEBUG", "logger": "backend.core.security.jwt_keys", "message": "Using key ID a69f821a-65e8-4292-8feb-985dc7ac94d9 for verification", "module": "jwt_keys", "function": "decode_token", "line": 195}
{"timestamp": "2025-06-12T17:25:29.414414", "level": "DEBUG", "logger": "backend.services.auth", "message": "JWT decoded successfully with key manager", "module": "auth", "function": "authenticate_token", "line": 683}
{"timestamp": "2025-06-12T17:25:29.425648", "level": "DEBUG", "logger": "backend.services.db_utils", "message": "Query 'auth_lookup_e4ae2d8f-b32f-52d7-99cd-8a33b19652c7' completed in 0.010s", "module": "db_utils", "function": "execute_query_with_timeout", "line": 68}
{"timestamp": "2025-06-12T17:25:29.425713", "level": "DEBUG", "logger": "backend.services.db_utils", "message": "Auth query 'auth_lookup_e4ae2d8f-b32f-52d7-99cd-8a33b19652c7' successful, result format preserved", "module": "db_utils", "function": "execute_query_with_timeout", "line": 72}
{"timestamp": "2025-06-12T17:25:29.440057", "level": "DEBUG", "logger": "backend.services.auth", "message": "Authentication attempt with token prefix: eyJhbGciOi...", "module": "auth", "function": "get_current_user", "line": 727}
{"timestamp": "2025-06-12T17:25:29.440181", "level": "DEBUG", "logger": "backend.core.security.jwt_keys", "message": "Token contains key ID: a69f821a-65e8-4292-8feb-985dc7ac94d9", "module": "jwt_keys", "function": "decode_token", "line": 188}
{"timestamp": "2025-06-12T17:25:29.440225", "level": "DEBUG", "logger": "backend.core.security.jwt_keys", "message": "Using key ID a69f821a-65e8-4292-8feb-985dc7ac94d9 for verification", "module": "jwt_keys", "function": "decode_token", "line": 195}
{"timestamp": "2025-06-12T17:25:29.440349", "level": "DEBUG", "logger": "backend.services.auth", "message": "JWT decoded successfully with key manager", "module": "auth", "function": "authenticate_token", "line": 683}
{"timestamp": "2025-06-12T17:25:29.443802", "level": "DEBUG", "logger": "backend.services.db_utils", "message": "Query 'auth_lookup_e4ae2d8f-b32f-52d7-99cd-8a33b19652c7' completed in 0.001s", "module": "db_utils", "function": "execute_query_with_timeout", "line": 68}
{"timestamp": "2025-06-12T17:25:29.443863", "level": "DEBUG", "logger": "backend.services.db_utils", "message": "Auth query 'auth_lookup_e4ae2d8f-b32f-52d7-99cd-8a33b19652c7' successful, result format preserved", "module": "db_utils", "function": "execute_query_with_timeout", "line": 72}
{"timestamp": "2025-06-12T17:25:29.443933", "level": "DEBUG", "logger": "backend.services.auth", "message": "Successfully authenticated user: <EMAIL>", "module": "auth", "function": "get_current_user", "line": 732}
{"timestamp": "2025-06-12T17:25:29.443989", "level": "DEBUG", "logger": "backend.services.auth", "message": "Successfully authenticated user: <EMAIL>", "module": "auth", "function": "get_current_user", "line": 738}
{"timestamp": "2025-06-12T17:25:29.448723", "level": "DEBUG", "logger": "backend.routes.qr_preview", "message": "Received QR preview SVG request: {\"qr_type\": \"scooter_unlock\", \"data\": {\"scooter_id\": \"SCOOT123\", \"unlock_code\": \"UNLOCK42\", \"title\": \"Scooter_unlock QR Code\", \"description\": \"Scan to view scooter_unlock content\"}, \"designOptions\": {\"size\": 256, \"margin\": 10, \"pattern\": \"square\", \"marker_border_style\": \"square\", \"marker_center_style\": \"square\", \"logo_size\": 0.3, \"logo_position\": \"center\", \"logo_opacity\": 1, \"logo_remove_background\": false, \"frame_style\": \"none\", \"frame_font\": \"SF Pro\", \"frame_font_size\": 16, \"foreground_color\":", "module": "qr_preview", "function": "preview_qr_svg", "line": 242}
{"timestamp": "2025-06-12T17:25:29.449875", "level": "DEBUG", "logger": "backend.routes.qr_preview", "message": "Form data keys: ['request']", "module": "qr_preview", "function": "preview_qr_svg", "line": 297}
{"timestamp": "2025-06-12T17:25:29.449919", "level": "DEBUG", "logger": "backend.routes.qr_preview", "message": "No logo key found in form data", "module": "qr_preview", "function": "preview_qr_svg", "line": 309}
{"timestamp": "2025-06-12T17:25:29.450206", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Formatting content for QR type: qrtype.scooter_unlock, content keys: ['scooter_id', 'unlock_code', 'title', 'description']", "module": "qr_preview", "function": "_format_qr_data", "line": 876}
{"timestamp": "2025-06-12T17:25:29.468942", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Logo remove background setting: False, has logo: False", "module": "qr_preview", "function": "generate_preview_svg_modular", "line": 727}
{"timestamp": "2025-06-12T17:25:29.484622", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Drawing 3 finder patterns at positions: [(0, 0), (42, 0), (0, 42)]", "module": "qr_preview", "function": "generate_preview_svg_modular", "line": 775}
{"timestamp": "2025-06-12T17:25:29.484691", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "QR size: 49, border_style: square, center_style: square", "module": "qr_preview", "function": "generate_preview_svg_modular", "line": 776}
{"timestamp": "2025-06-12T17:25:29.484739", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Drawing finder pattern 1/3 at position (0, 0)", "module": "qr_preview", "function": "generate_preview_svg_modular", "line": 780}
{"timestamp": "2025-06-12T17:25:29.485093", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "_draw_finder_marker called: mx=0, my=0, module_size=5.918367346938775, border_style=square, center_style=square, border_color=#000000, center_color=#000000, offset=(20, 20)", "module": "qr_preview", "function": "_draw_finder_marker", "line": 97}
{"timestamp": "2025-06-12T17:25:29.485233", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Creating finder pattern group with id='marker-0-0'", "module": "qr_preview", "function": "_draw_finder_marker", "line": 110}
{"timestamp": "2025-06-12T17:25:29.485281", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Drawing square border:", "module": "qr_preview", "function": "_draw_finder_marker", "line": 160}
{"timestamp": "2025-06-12T17:25:29.485321", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "  Outer: insert=(20.0, 20.0), size=(41.42857142857143, 41.42857142857143)", "module": "qr_preview", "function": "_draw_finder_marker", "line": 161}
{"timestamp": "2025-06-12T17:25:29.485360", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "  Inner: insert=(27.10204081632653, 27.10204081632653), size=(27.224489795918366, 27.224489795918366)", "module": "qr_preview", "function": "_draw_finder_marker", "line": 162}
{"timestamp": "2025-06-12T17:25:29.485729", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Drawing marker center: style=square, color=#000000, pos=(34.79591836734694, 34.79591836734694), size=11.83673469387755", "module": "qr_preview", "function": "_draw_marker_center", "line": 179}
{"timestamp": "2025-06-12T17:25:29.485836", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Successfully added finder pattern group to SVG", "module": "qr_preview", "function": "_draw_finder_marker", "line": 171}
{"timestamp": "2025-06-12T17:25:29.485880", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Successfully drew finder pattern 1/3", "module": "qr_preview", "function": "generate_preview_svg_modular", "line": 782}
{"timestamp": "2025-06-12T17:25:29.486003", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Drawing finder pattern 2/3 at position (42, 0)", "module": "qr_preview", "function": "generate_preview_svg_modular", "line": 780}
{"timestamp": "2025-06-12T17:25:29.486066", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "_draw_finder_marker called: mx=42, my=0, module_size=5.918367346938775, border_style=square, center_style=square, border_color=#000000, center_color=#000000, offset=(20, 20)", "module": "qr_preview", "function": "_draw_finder_marker", "line": 97}
{"timestamp": "2025-06-12T17:25:29.486115", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Creating finder pattern group with id='marker-42-0'", "module": "qr_preview", "function": "_draw_finder_marker", "line": 110}
{"timestamp": "2025-06-12T17:25:29.486150", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Drawing square border:", "module": "qr_preview", "function": "_draw_finder_marker", "line": 160}
{"timestamp": "2025-06-12T17:25:29.486188", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "  Outer: insert=(268.57142857142856, 20.0), size=(41.42857142857143, 41.42857142857143)", "module": "qr_preview", "function": "_draw_finder_marker", "line": 161}
{"timestamp": "2025-06-12T17:25:29.486223", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "  Inner: insert=(275.67346938775506, 27.10204081632653), size=(27.224489795918366, 27.224489795918366)", "module": "qr_preview", "function": "_draw_finder_marker", "line": 162}
{"timestamp": "2025-06-12T17:25:29.486532", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Drawing marker center: style=square, color=#000000, pos=(283.3673469387755, 34.79591836734694), size=11.83673469387755", "module": "qr_preview", "function": "_draw_marker_center", "line": 179}
{"timestamp": "2025-06-12T17:25:29.486582", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Successfully added finder pattern group to SVG", "module": "qr_preview", "function": "_draw_finder_marker", "line": 171}
{"timestamp": "2025-06-12T17:25:29.486615", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Successfully drew finder pattern 2/3", "module": "qr_preview", "function": "generate_preview_svg_modular", "line": 782}
{"timestamp": "2025-06-12T17:25:29.486644", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Drawing finder pattern 3/3 at position (0, 42)", "module": "qr_preview", "function": "generate_preview_svg_modular", "line": 780}
{"timestamp": "2025-06-12T17:25:29.486682", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "_draw_finder_marker called: mx=0, my=42, module_size=5.918367346938775, border_style=square, center_style=square, border_color=#000000, center_color=#000000, offset=(20, 20)", "module": "qr_preview", "function": "_draw_finder_marker", "line": 97}
{"timestamp": "2025-06-12T17:25:29.486722", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Creating finder pattern group with id='marker-0-42'", "module": "qr_preview", "function": "_draw_finder_marker", "line": 110}
{"timestamp": "2025-06-12T17:25:29.486755", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Drawing square border:", "module": "qr_preview", "function": "_draw_finder_marker", "line": 160}
{"timestamp": "2025-06-12T17:25:29.486788", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "  Outer: insert=(20.0, 268.57142857142856), size=(41.42857142857143, 41.42857142857143)", "module": "qr_preview", "function": "_draw_finder_marker", "line": 161}
{"timestamp": "2025-06-12T17:25:29.486823", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "  Inner: insert=(27.10204081632653, 275.67346938775506), size=(27.224489795918366, 27.224489795918366)", "module": "qr_preview", "function": "_draw_finder_marker", "line": 162}
{"timestamp": "2025-06-12T17:25:29.486885", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Drawing marker center: style=square, color=#000000, pos=(34.79591836734694, 283.3673469387755), size=11.83673469387755", "module": "qr_preview", "function": "_draw_marker_center", "line": 179}
{"timestamp": "2025-06-12T17:25:29.486967", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Successfully added finder pattern group to SVG", "module": "qr_preview", "function": "_draw_finder_marker", "line": 171}
{"timestamp": "2025-06-12T17:25:29.487000", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Successfully drew finder pattern 3/3", "module": "qr_preview", "function": "generate_preview_svg_modular", "line": 782}
{"timestamp": "2025-06-12T17:25:29.514446", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Generated SVG length: 176251", "module": "qr_preview", "function": "generate_preview_svg_modular", "line": 802}
{"timestamp": "2025-06-12T17:25:29.514577", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "SVG contains marker-0-0: True", "module": "qr_preview", "function": "generate_preview_svg_modular", "line": 803}
{"timestamp": "2025-06-12T17:25:29.514681", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "SVG contains marker-18-0: False", "module": "qr_preview", "function": "generate_preview_svg_modular", "line": 804}
{"timestamp": "2025-06-12T17:25:29.514788", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "SVG contains marker-0-18: False", "module": "qr_preview", "function": "generate_preview_svg_modular", "line": 805}
{"timestamp": "2025-06-12T17:25:29.515071", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "SVG marker counts - outer: 3, inner: 3, dot: 0", "module": "qr_preview", "function": "generate_preview_svg_modular", "line": 813}
{"timestamp": "2025-06-12T17:25:29.515112", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "SVG label counts - text: 0, background: 0", "module": "qr_preview", "function": "generate_preview_svg_modular", "line": 814}
{"timestamp": "2025-06-12T17:25:29.521942", "level": "DEBUG", "logger": "backend.main", "message": "[Auth] Enforced Bearer token authentication for request", "module": "main", "function": "bearer_token_auth_middleware", "line": 274}
{"timestamp": "2025-06-12T17:25:32.221169", "level": "DEBUG", "logger": "backend.core.security.jwt_keys", "message": "Token contains key ID: a69f821a-65e8-4292-8feb-985dc7ac94d9", "module": "jwt_keys", "function": "decode_token", "line": 188}
{"timestamp": "2025-06-12T17:25:32.221247", "level": "DEBUG", "logger": "backend.core.security.jwt_keys", "message": "Using key ID a69f821a-65e8-4292-8feb-985dc7ac94d9 for verification", "module": "jwt_keys", "function": "decode_token", "line": 195}
{"timestamp": "2025-06-12T17:25:32.221472", "level": "DEBUG", "logger": "backend.services.auth", "message": "JWT decoded successfully with key manager", "module": "auth", "function": "authenticate_token", "line": 683}
{"timestamp": "2025-06-12T17:25:32.223944", "level": "DEBUG", "logger": "backend.services.db_utils", "message": "Query 'auth_lookup_e4ae2d8f-b32f-52d7-99cd-8a33b19652c7' completed in 0.002s", "module": "db_utils", "function": "execute_query_with_timeout", "line": 68}
{"timestamp": "2025-06-12T17:25:32.224013", "level": "DEBUG", "logger": "backend.services.db_utils", "message": "Auth query 'auth_lookup_e4ae2d8f-b32f-52d7-99cd-8a33b19652c7' successful, result format preserved", "module": "db_utils", "function": "execute_query_with_timeout", "line": 72}
{"timestamp": "2025-06-12T17:25:32.230845", "level": "DEBUG", "logger": "backend.services.auth", "message": "Authentication attempt with token prefix: eyJhbGciOi...", "module": "auth", "function": "get_current_user", "line": 727}
{"timestamp": "2025-06-12T17:25:32.230972", "level": "DEBUG", "logger": "backend.core.security.jwt_keys", "message": "Token contains key ID: a69f821a-65e8-4292-8feb-985dc7ac94d9", "module": "jwt_keys", "function": "decode_token", "line": 188}
{"timestamp": "2025-06-12T17:25:32.231015", "level": "DEBUG", "logger": "backend.core.security.jwt_keys", "message": "Using key ID a69f821a-65e8-4292-8feb-985dc7ac94d9 for verification", "module": "jwt_keys", "function": "decode_token", "line": 195}
{"timestamp": "2025-06-12T17:25:32.231993", "level": "DEBUG", "logger": "backend.services.auth", "message": "JWT decoded successfully with key manager", "module": "auth", "function": "authenticate_token", "line": 683}
{"timestamp": "2025-06-12T17:25:32.233885", "level": "DEBUG", "logger": "backend.services.db_utils", "message": "Query 'auth_lookup_e4ae2d8f-b32f-52d7-99cd-8a33b19652c7' completed in 0.002s", "module": "db_utils", "function": "execute_query_with_timeout", "line": 68}
{"timestamp": "2025-06-12T17:25:32.233957", "level": "DEBUG", "logger": "backend.services.db_utils", "message": "Auth query 'auth_lookup_e4ae2d8f-b32f-52d7-99cd-8a33b19652c7' successful, result format preserved", "module": "db_utils", "function": "execute_query_with_timeout", "line": 72}
{"timestamp": "2025-06-12T17:25:32.234028", "level": "DEBUG", "logger": "backend.services.auth", "message": "Successfully authenticated user: <EMAIL>", "module": "auth", "function": "get_current_user", "line": 732}
{"timestamp": "2025-06-12T17:25:32.234204", "level": "DEBUG", "logger": "backend.services.auth", "message": "Successfully authenticated user: <EMAIL>", "module": "auth", "function": "get_current_user", "line": 738}
{"timestamp": "2025-06-12T17:25:32.239182", "level": "DEBUG", "logger": "backend.routes.qr_preview", "message": "Received QR preview SVG request: {\"qr_type\": \"scooter_unlock\", \"data\": {\"scooter_id\": \"SCOOT123\", \"unlock_code\": \"UNLOCK42\", \"title\": \"Scooter_unlock QR Code\", \"description\": \"Scan to view scooter_unlock content\"}, \"designOptions\": {\"size\": 256, \"margin\": 10, \"pattern\": \"square\", \"marker_border_style\": \"square\", \"marker_center_style\": \"square\", \"logo_size\": 0.3, \"logo_position\": \"center\", \"logo_opacity\": 1, \"logo_remove_background\": false, \"frame_style\": \"none\", \"frame_font\": \"SF Pro\", \"frame_font_size\": 16, \"foreground_color\":", "module": "qr_preview", "function": "preview_qr_svg", "line": 242}
{"timestamp": "2025-06-12T17:25:32.239435", "level": "DEBUG", "logger": "backend.routes.qr_preview", "message": "Form data keys: ['request']", "module": "qr_preview", "function": "preview_qr_svg", "line": 297}
{"timestamp": "2025-06-12T17:25:32.239542", "level": "DEBUG", "logger": "backend.routes.qr_preview", "message": "No logo key found in form data", "module": "qr_preview", "function": "preview_qr_svg", "line": 309}
{"timestamp": "2025-06-12T17:25:32.239684", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Formatting content for QR type: qrtype.scooter_unlock, content keys: ['scooter_id', 'unlock_code', 'title', 'description']", "module": "qr_preview", "function": "_format_qr_data", "line": 876}
{"timestamp": "2025-06-12T17:25:32.257805", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Logo remove background setting: False, has logo: False", "module": "qr_preview", "function": "generate_preview_svg_modular", "line": 727}
{"timestamp": "2025-06-12T17:25:32.271446", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Drawing 3 finder patterns at positions: [(0, 0), (42, 0), (0, 42)]", "module": "qr_preview", "function": "generate_preview_svg_modular", "line": 775}
{"timestamp": "2025-06-12T17:25:32.271521", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "QR size: 49, border_style: square, center_style: square", "module": "qr_preview", "function": "generate_preview_svg_modular", "line": 776}
{"timestamp": "2025-06-12T17:25:32.271563", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Drawing finder pattern 1/3 at position (0, 0)", "module": "qr_preview", "function": "generate_preview_svg_modular", "line": 780}
{"timestamp": "2025-06-12T17:25:32.271612", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "_draw_finder_marker called: mx=0, my=0, module_size=5.918367346938775, border_style=square, center_style=square, border_color=#000000, center_color=#000000, offset=(20, 20)", "module": "qr_preview", "function": "_draw_finder_marker", "line": 97}
{"timestamp": "2025-06-12T17:25:32.271713", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Creating finder pattern group with id='marker-0-0'", "module": "qr_preview", "function": "_draw_finder_marker", "line": 110}
{"timestamp": "2025-06-12T17:25:32.271752", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Drawing square border:", "module": "qr_preview", "function": "_draw_finder_marker", "line": 160}
{"timestamp": "2025-06-12T17:25:32.271792", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "  Outer: insert=(20.0, 20.0), size=(41.42857142857143, 41.42857142857143)", "module": "qr_preview", "function": "_draw_finder_marker", "line": 161}
{"timestamp": "2025-06-12T17:25:32.271829", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "  Inner: insert=(27.10204081632653, 27.10204081632653), size=(27.224489795918366, 27.224489795918366)", "module": "qr_preview", "function": "_draw_finder_marker", "line": 162}
{"timestamp": "2025-06-12T17:25:32.271902", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Drawing marker center: style=square, color=#000000, pos=(34.79591836734694, 34.79591836734694), size=11.83673469387755", "module": "qr_preview", "function": "_draw_marker_center", "line": 179}
{"timestamp": "2025-06-12T17:25:32.271960", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Successfully added finder pattern group to SVG", "module": "qr_preview", "function": "_draw_finder_marker", "line": 171}
{"timestamp": "2025-06-12T17:25:32.272002", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Successfully drew finder pattern 1/3", "module": "qr_preview", "function": "generate_preview_svg_modular", "line": 782}
{"timestamp": "2025-06-12T17:25:32.272039", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Drawing finder pattern 2/3 at position (42, 0)", "module": "qr_preview", "function": "generate_preview_svg_modular", "line": 780}
{"timestamp": "2025-06-12T17:25:32.272081", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "_draw_finder_marker called: mx=42, my=0, module_size=5.918367346938775, border_style=square, center_style=square, border_color=#000000, center_color=#000000, offset=(20, 20)", "module": "qr_preview", "function": "_draw_finder_marker", "line": 97}
{"timestamp": "2025-06-12T17:25:32.272123", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Creating finder pattern group with id='marker-42-0'", "module": "qr_preview", "function": "_draw_finder_marker", "line": 110}
{"timestamp": "2025-06-12T17:25:32.272160", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Drawing square border:", "module": "qr_preview", "function": "_draw_finder_marker", "line": 160}
{"timestamp": "2025-06-12T17:25:32.272194", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "  Outer: insert=(268.57142857142856, 20.0), size=(41.42857142857143, 41.42857142857143)", "module": "qr_preview", "function": "_draw_finder_marker", "line": 161}
{"timestamp": "2025-06-12T17:25:32.272229", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "  Inner: insert=(275.67346938775506, 27.10204081632653), size=(27.224489795918366, 27.224489795918366)", "module": "qr_preview", "function": "_draw_finder_marker", "line": 162}
{"timestamp": "2025-06-12T17:25:32.272294", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Drawing marker center: style=square, color=#000000, pos=(283.3673469387755, 34.79591836734694), size=11.83673469387755", "module": "qr_preview", "function": "_draw_marker_center", "line": 179}
{"timestamp": "2025-06-12T17:25:32.272341", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Successfully added finder pattern group to SVG", "module": "qr_preview", "function": "_draw_finder_marker", "line": 171}
{"timestamp": "2025-06-12T17:25:32.272406", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Successfully drew finder pattern 2/3", "module": "qr_preview", "function": "generate_preview_svg_modular", "line": 782}
{"timestamp": "2025-06-12T17:25:32.272439", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Drawing finder pattern 3/3 at position (0, 42)", "module": "qr_preview", "function": "generate_preview_svg_modular", "line": 780}
{"timestamp": "2025-06-12T17:25:32.272474", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "_draw_finder_marker called: mx=0, my=42, module_size=5.918367346938775, border_style=square, center_style=square, border_color=#000000, center_color=#000000, offset=(20, 20)", "module": "qr_preview", "function": "_draw_finder_marker", "line": 97}
{"timestamp": "2025-06-12T17:25:32.272516", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Creating finder pattern group with id='marker-0-42'", "module": "qr_preview", "function": "_draw_finder_marker", "line": 110}
{"timestamp": "2025-06-12T17:25:32.272548", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Drawing square border:", "module": "qr_preview", "function": "_draw_finder_marker", "line": 160}
{"timestamp": "2025-06-12T17:25:32.272582", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "  Outer: insert=(20.0, 268.57142857142856), size=(41.42857142857143, 41.42857142857143)", "module": "qr_preview", "function": "_draw_finder_marker", "line": 161}
{"timestamp": "2025-06-12T17:25:32.272618", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "  Inner: insert=(27.10204081632653, 275.67346938775506), size=(27.224489795918366, 27.224489795918366)", "module": "qr_preview", "function": "_draw_finder_marker", "line": 162}
{"timestamp": "2025-06-12T17:25:32.272680", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Drawing marker center: style=square, color=#000000, pos=(34.79591836734694, 283.3673469387755), size=11.83673469387755", "module": "qr_preview", "function": "_draw_marker_center", "line": 179}
{"timestamp": "2025-06-12T17:25:32.272727", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Successfully added finder pattern group to SVG", "module": "qr_preview", "function": "_draw_finder_marker", "line": 171}
{"timestamp": "2025-06-12T17:25:32.272761", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Successfully drew finder pattern 3/3", "module": "qr_preview", "function": "generate_preview_svg_modular", "line": 782}
{"timestamp": "2025-06-12T17:25:32.293204", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Generated SVG length: 176251", "module": "qr_preview", "function": "generate_preview_svg_modular", "line": 802}
{"timestamp": "2025-06-12T17:25:32.293342", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "SVG contains marker-0-0: True", "module": "qr_preview", "function": "generate_preview_svg_modular", "line": 803}
{"timestamp": "2025-06-12T17:25:32.293453", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "SVG contains marker-18-0: False", "module": "qr_preview", "function": "generate_preview_svg_modular", "line": 804}
{"timestamp": "2025-06-12T17:25:32.293567", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "SVG contains marker-0-18: False", "module": "qr_preview", "function": "generate_preview_svg_modular", "line": 805}
{"timestamp": "2025-06-12T17:25:32.293857", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "SVG marker counts - outer: 3, inner: 3, dot: 0", "module": "qr_preview", "function": "generate_preview_svg_modular", "line": 813}
{"timestamp": "2025-06-12T17:25:32.293895", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "SVG label counts - text: 0, background: 0", "module": "qr_preview", "function": "generate_preview_svg_modular", "line": 814}
{"timestamp": "2025-06-12T17:25:32.297209", "level": "DEBUG", "logger": "backend.main", "message": "[Auth] Enforced Bearer token authentication for request", "module": "main", "function": "bearer_token_auth_middleware", "line": 274}
{"timestamp": "2025-06-12T17:25:32.539873", "level": "DEBUG", "logger": "backend.core.security.jwt_keys", "message": "Token contains key ID: a69f821a-65e8-4292-8feb-985dc7ac94d9", "module": "jwt_keys", "function": "decode_token", "line": 188}
{"timestamp": "2025-06-12T17:25:32.539932", "level": "DEBUG", "logger": "backend.core.security.jwt_keys", "message": "Using key ID a69f821a-65e8-4292-8feb-985dc7ac94d9 for verification", "module": "jwt_keys", "function": "decode_token", "line": 195}
{"timestamp": "2025-06-12T17:25:32.540068", "level": "DEBUG", "logger": "backend.services.auth", "message": "JWT decoded successfully with key manager", "module": "auth", "function": "authenticate_token", "line": 683}
{"timestamp": "2025-06-12T17:25:32.541814", "level": "DEBUG", "logger": "backend.services.db_utils", "message": "Query 'auth_lookup_e4ae2d8f-b32f-52d7-99cd-8a33b19652c7' completed in 0.002s", "module": "db_utils", "function": "execute_query_with_timeout", "line": 68}
{"timestamp": "2025-06-12T17:25:32.541870", "level": "DEBUG", "logger": "backend.services.db_utils", "message": "Auth query 'auth_lookup_e4ae2d8f-b32f-52d7-99cd-8a33b19652c7' successful, result format preserved", "module": "db_utils", "function": "execute_query_with_timeout", "line": 72}
{"timestamp": "2025-06-12T17:25:32.547512", "level": "DEBUG", "logger": "backend.services.auth", "message": "Authentication attempt with token prefix: eyJhbGciOi...", "module": "auth", "function": "get_current_user", "line": 727}
{"timestamp": "2025-06-12T17:25:32.547636", "level": "DEBUG", "logger": "backend.core.security.jwt_keys", "message": "Token contains key ID: a69f821a-65e8-4292-8feb-985dc7ac94d9", "module": "jwt_keys", "function": "decode_token", "line": 188}
{"timestamp": "2025-06-12T17:25:32.547669", "level": "DEBUG", "logger": "backend.core.security.jwt_keys", "message": "Using key ID a69f821a-65e8-4292-8feb-985dc7ac94d9 for verification", "module": "jwt_keys", "function": "decode_token", "line": 195}
{"timestamp": "2025-06-12T17:25:32.547802", "level": "DEBUG", "logger": "backend.services.auth", "message": "JWT decoded successfully with key manager", "module": "auth", "function": "authenticate_token", "line": 683}
{"timestamp": "2025-06-12T17:25:32.549034", "level": "DEBUG", "logger": "backend.services.db_utils", "message": "Query 'auth_lookup_e4ae2d8f-b32f-52d7-99cd-8a33b19652c7' completed in 0.001s", "module": "db_utils", "function": "execute_query_with_timeout", "line": 68}
{"timestamp": "2025-06-12T17:25:32.549109", "level": "DEBUG", "logger": "backend.services.db_utils", "message": "Auth query 'auth_lookup_e4ae2d8f-b32f-52d7-99cd-8a33b19652c7' successful, result format preserved", "module": "db_utils", "function": "execute_query_with_timeout", "line": 72}
{"timestamp": "2025-06-12T17:25:32.549167", "level": "DEBUG", "logger": "backend.services.auth", "message": "Successfully authenticated user: <EMAIL>", "module": "auth", "function": "get_current_user", "line": 732}
{"timestamp": "2025-06-12T17:25:32.549199", "level": "DEBUG", "logger": "backend.services.auth", "message": "Successfully authenticated user: <EMAIL>", "module": "auth", "function": "get_current_user", "line": 738}
{"timestamp": "2025-06-12T17:25:32.550996", "level": "DEBUG", "logger": "backend.routes.qr_preview", "message": "Received QR preview SVG request: {\"qr_type\": \"scooter_unlock\", \"data\": {\"scooter_id\": \"SCOOT123\", \"unlock_code\": \"UNLOCK42\", \"title\": \"Scooter_unlock QR Code\", \"description\": \"Scan to view scooter_unlock content\"}, \"designOptions\": {\"size\": 256, \"margin\": 10, \"pattern\": \"square\", \"marker_border_style\": \"square\", \"marker_center_style\": \"square\", \"logo_size\": 0.3, \"logo_position\": \"center\", \"logo_opacity\": 1, \"logo_remove_background\": false, \"frame_style\": \"none\", \"frame_font\": \"SF Pro\", \"frame_font_size\": 16, \"foreground_color\":", "module": "qr_preview", "function": "preview_qr_svg", "line": 242}
{"timestamp": "2025-06-12T17:25:32.551140", "level": "DEBUG", "logger": "backend.routes.qr_preview", "message": "Form data keys: ['request']", "module": "qr_preview", "function": "preview_qr_svg", "line": 297}
{"timestamp": "2025-06-12T17:25:32.551201", "level": "DEBUG", "logger": "backend.routes.qr_preview", "message": "No logo key found in form data", "module": "qr_preview", "function": "preview_qr_svg", "line": 309}
{"timestamp": "2025-06-12T17:25:32.551262", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Formatting content for QR type: qrtype.scooter_unlock, content keys: ['scooter_id', 'unlock_code', 'title', 'description']", "module": "qr_preview", "function": "_format_qr_data", "line": 876}
{"timestamp": "2025-06-12T17:25:32.567610", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Logo remove background setting: False, has logo: False", "module": "qr_preview", "function": "generate_preview_svg_modular", "line": 727}
{"timestamp": "2025-06-12T17:25:32.581615", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Drawing 3 finder patterns at positions: [(0, 0), (42, 0), (0, 42)]", "module": "qr_preview", "function": "generate_preview_svg_modular", "line": 775}
{"timestamp": "2025-06-12T17:25:32.581686", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "QR size: 49, border_style: square, center_style: square", "module": "qr_preview", "function": "generate_preview_svg_modular", "line": 776}
{"timestamp": "2025-06-12T17:25:32.581731", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Drawing finder pattern 1/3 at position (0, 0)", "module": "qr_preview", "function": "generate_preview_svg_modular", "line": 780}
{"timestamp": "2025-06-12T17:25:32.583210", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "_draw_finder_marker called: mx=0, my=0, module_size=5.918367346938775, border_style=square, center_style=square, border_color=#000000, center_color=#000000, offset=(20, 20)", "module": "qr_preview", "function": "_draw_finder_marker", "line": 97}
{"timestamp": "2025-06-12T17:25:32.583302", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Creating finder pattern group with id='marker-0-0'", "module": "qr_preview", "function": "_draw_finder_marker", "line": 110}
{"timestamp": "2025-06-12T17:25:32.583341", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Drawing square border:", "module": "qr_preview", "function": "_draw_finder_marker", "line": 160}
{"timestamp": "2025-06-12T17:25:32.583380", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "  Outer: insert=(20.0, 20.0), size=(41.42857142857143, 41.42857142857143)", "module": "qr_preview", "function": "_draw_finder_marker", "line": 161}
{"timestamp": "2025-06-12T17:25:32.583428", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "  Inner: insert=(27.10204081632653, 27.10204081632653), size=(27.224489795918366, 27.224489795918366)", "module": "qr_preview", "function": "_draw_finder_marker", "line": 162}
{"timestamp": "2025-06-12T17:25:32.583543", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Drawing marker center: style=square, color=#000000, pos=(34.79591836734694, 34.79591836734694), size=11.83673469387755", "module": "qr_preview", "function": "_draw_marker_center", "line": 179}
{"timestamp": "2025-06-12T17:25:32.583601", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Successfully added finder pattern group to SVG", "module": "qr_preview", "function": "_draw_finder_marker", "line": 171}
{"timestamp": "2025-06-12T17:25:32.583637", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Successfully drew finder pattern 1/3", "module": "qr_preview", "function": "generate_preview_svg_modular", "line": 782}
{"timestamp": "2025-06-12T17:25:32.583670", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Drawing finder pattern 2/3 at position (42, 0)", "module": "qr_preview", "function": "generate_preview_svg_modular", "line": 780}
{"timestamp": "2025-06-12T17:25:32.583745", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "_draw_finder_marker called: mx=42, my=0, module_size=5.918367346938775, border_style=square, center_style=square, border_color=#000000, center_color=#000000, offset=(20, 20)", "module": "qr_preview", "function": "_draw_finder_marker", "line": 97}
{"timestamp": "2025-06-12T17:25:32.583784", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Creating finder pattern group with id='marker-42-0'", "module": "qr_preview", "function": "_draw_finder_marker", "line": 110}
{"timestamp": "2025-06-12T17:25:32.583814", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Drawing square border:", "module": "qr_preview", "function": "_draw_finder_marker", "line": 160}
{"timestamp": "2025-06-12T17:25:32.583845", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "  Outer: insert=(268.57142857142856, 20.0), size=(41.42857142857143, 41.42857142857143)", "module": "qr_preview", "function": "_draw_finder_marker", "line": 161}
{"timestamp": "2025-06-12T17:25:32.583876", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "  Inner: insert=(275.67346938775506, 27.10204081632653), size=(27.224489795918366, 27.224489795918366)", "module": "qr_preview", "function": "_draw_finder_marker", "line": 162}
{"timestamp": "2025-06-12T17:25:32.583934", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Drawing marker center: style=square, color=#000000, pos=(283.3673469387755, 34.79591836734694), size=11.83673469387755", "module": "qr_preview", "function": "_draw_marker_center", "line": 179}
{"timestamp": "2025-06-12T17:25:32.583978", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Successfully added finder pattern group to SVG", "module": "qr_preview", "function": "_draw_finder_marker", "line": 171}
{"timestamp": "2025-06-12T17:25:32.584009", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Successfully drew finder pattern 2/3", "module": "qr_preview", "function": "generate_preview_svg_modular", "line": 782}
{"timestamp": "2025-06-12T17:25:32.584037", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Drawing finder pattern 3/3 at position (0, 42)", "module": "qr_preview", "function": "generate_preview_svg_modular", "line": 780}
{"timestamp": "2025-06-12T17:25:32.584066", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "_draw_finder_marker called: mx=0, my=42, module_size=5.918367346938775, border_style=square, center_style=square, border_color=#000000, center_color=#000000, offset=(20, 20)", "module": "qr_preview", "function": "_draw_finder_marker", "line": 97}
{"timestamp": "2025-06-12T17:25:32.584103", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Creating finder pattern group with id='marker-0-42'", "module": "qr_preview", "function": "_draw_finder_marker", "line": 110}
{"timestamp": "2025-06-12T17:25:32.584130", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Drawing square border:", "module": "qr_preview", "function": "_draw_finder_marker", "line": 160}
{"timestamp": "2025-06-12T17:25:32.584160", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "  Outer: insert=(20.0, 268.57142857142856), size=(41.42857142857143, 41.42857142857143)", "module": "qr_preview", "function": "_draw_finder_marker", "line": 161}
{"timestamp": "2025-06-12T17:25:32.584189", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "  Inner: insert=(27.10204081632653, 275.67346938775506), size=(27.224489795918366, 27.224489795918366)", "module": "qr_preview", "function": "_draw_finder_marker", "line": 162}
{"timestamp": "2025-06-12T17:25:32.584242", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Drawing marker center: style=square, color=#000000, pos=(34.79591836734694, 283.3673469387755), size=11.83673469387755", "module": "qr_preview", "function": "_draw_marker_center", "line": 179}
{"timestamp": "2025-06-12T17:25:32.584317", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Successfully added finder pattern group to SVG", "module": "qr_preview", "function": "_draw_finder_marker", "line": 171}
{"timestamp": "2025-06-12T17:25:32.584345", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Successfully drew finder pattern 3/3", "module": "qr_preview", "function": "generate_preview_svg_modular", "line": 782}
{"timestamp": "2025-06-12T17:25:32.604954", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Generated SVG length: 176251", "module": "qr_preview", "function": "generate_preview_svg_modular", "line": 802}
{"timestamp": "2025-06-12T17:25:32.605077", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "SVG contains marker-0-0: True", "module": "qr_preview", "function": "generate_preview_svg_modular", "line": 803}
{"timestamp": "2025-06-12T17:25:32.605176", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "SVG contains marker-18-0: False", "module": "qr_preview", "function": "generate_preview_svg_modular", "line": 804}
{"timestamp": "2025-06-12T17:25:32.605278", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "SVG contains marker-0-18: False", "module": "qr_preview", "function": "generate_preview_svg_modular", "line": 805}
{"timestamp": "2025-06-12T17:25:32.605553", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "SVG marker counts - outer: 3, inner: 3, dot: 0", "module": "qr_preview", "function": "generate_preview_svg_modular", "line": 813}
{"timestamp": "2025-06-12T17:25:32.605586", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "SVG label counts - text: 0, background: 0", "module": "qr_preview", "function": "generate_preview_svg_modular", "line": 814}
{"timestamp": "2025-06-12T17:25:32.608027", "level": "DEBUG", "logger": "backend.main", "message": "[Auth] Enforced Bearer token authentication for request", "module": "main", "function": "bearer_token_auth_middleware", "line": 274}
{"timestamp": "2025-06-12T17:25:33.041002", "level": "DEBUG", "logger": "backend.core.security.jwt_keys", "message": "Token contains key ID: a69f821a-65e8-4292-8feb-985dc7ac94d9", "module": "jwt_keys", "function": "decode_token", "line": 188}
{"timestamp": "2025-06-12T17:25:33.041401", "level": "DEBUG", "logger": "backend.core.security.jwt_keys", "message": "Using key ID a69f821a-65e8-4292-8feb-985dc7ac94d9 for verification", "module": "jwt_keys", "function": "decode_token", "line": 195}
{"timestamp": "2025-06-12T17:25:33.041651", "level": "DEBUG", "logger": "backend.services.auth", "message": "JWT decoded successfully with key manager", "module": "auth", "function": "authenticate_token", "line": 683}
{"timestamp": "2025-06-12T17:25:33.044570", "level": "DEBUG", "logger": "backend.services.db_utils", "message": "Query 'auth_lookup_e4ae2d8f-b32f-52d7-99cd-8a33b19652c7' completed in 0.002s", "module": "db_utils", "function": "execute_query_with_timeout", "line": 68}
{"timestamp": "2025-06-12T17:25:33.044627", "level": "DEBUG", "logger": "backend.services.db_utils", "message": "Auth query 'auth_lookup_e4ae2d8f-b32f-52d7-99cd-8a33b19652c7' successful, result format preserved", "module": "db_utils", "function": "execute_query_with_timeout", "line": 72}
{"timestamp": "2025-06-12T17:25:33.049208", "level": "DEBUG", "logger": "backend.services.auth", "message": "Authentication attempt with token prefix: eyJhbGciOi...", "module": "auth", "function": "get_current_user", "line": 727}
{"timestamp": "2025-06-12T17:25:33.049322", "level": "DEBUG", "logger": "backend.core.security.jwt_keys", "message": "Token contains key ID: a69f821a-65e8-4292-8feb-985dc7ac94d9", "module": "jwt_keys", "function": "decode_token", "line": 188}
{"timestamp": "2025-06-12T17:25:33.049366", "level": "DEBUG", "logger": "backend.core.security.jwt_keys", "message": "Using key ID a69f821a-65e8-4292-8feb-985dc7ac94d9 for verification", "module": "jwt_keys", "function": "decode_token", "line": 195}
{"timestamp": "2025-06-12T17:25:33.049544", "level": "DEBUG", "logger": "backend.services.auth", "message": "JWT decoded successfully with key manager", "module": "auth", "function": "authenticate_token", "line": 683}
{"timestamp": "2025-06-12T17:25:33.050674", "level": "DEBUG", "logger": "backend.services.db_utils", "message": "Query 'auth_lookup_e4ae2d8f-b32f-52d7-99cd-8a33b19652c7' completed in 0.001s", "module": "db_utils", "function": "execute_query_with_timeout", "line": 68}
{"timestamp": "2025-06-12T17:25:33.050722", "level": "DEBUG", "logger": "backend.services.db_utils", "message": "Auth query 'auth_lookup_e4ae2d8f-b32f-52d7-99cd-8a33b19652c7' successful, result format preserved", "module": "db_utils", "function": "execute_query_with_timeout", "line": 72}
{"timestamp": "2025-06-12T17:25:33.050777", "level": "DEBUG", "logger": "backend.services.auth", "message": "Successfully authenticated user: <EMAIL>", "module": "auth", "function": "get_current_user", "line": 732}
{"timestamp": "2025-06-12T17:25:33.050827", "level": "DEBUG", "logger": "backend.services.auth", "message": "Successfully authenticated user: <EMAIL>", "module": "auth", "function": "get_current_user", "line": 738}
{"timestamp": "2025-06-12T17:25:33.052411", "level": "DEBUG", "logger": "backend.routes.qr_preview", "message": "Received QR preview SVG request: {\"qr_type\": \"scooter_unlock\", \"data\": {\"scooter_id\": \"SCOOT123\", \"unlock_code\": \"UNLOCK42\", \"title\": \"Scooter_unlock QR Code\", \"description\": \"Scan to view scooter_unlock content\"}, \"designOptions\": {\"size\": 256, \"margin\": 10, \"pattern\": \"square\", \"marker_border_style\": \"square\", \"marker_border_color\": \"#000000\", \"marker_center_style\": \"square\", \"marker_center_color\": \"#000000\", \"logo_size\": 0.3, \"logo_position\": \"center\", \"logo_opacity\": 1, \"frame_style\": \"none\", \"frame_color\": \"#000000\", \"fram", "module": "qr_preview", "function": "preview_qr_svg", "line": 242}
{"timestamp": "2025-06-12T17:25:33.052854", "level": "DEBUG", "logger": "backend.routes.qr_preview", "message": "Form data keys: ['request']", "module": "qr_preview", "function": "preview_qr_svg", "line": 297}
{"timestamp": "2025-06-12T17:25:33.052916", "level": "DEBUG", "logger": "backend.routes.qr_preview", "message": "No logo key found in form data", "module": "qr_preview", "function": "preview_qr_svg", "line": 309}
{"timestamp": "2025-06-12T17:25:33.052988", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Formatting content for QR type: qrtype.scooter_unlock, content keys: ['scooter_id', 'unlock_code', 'title', 'description']", "module": "qr_preview", "function": "_format_qr_data", "line": 876}
{"timestamp": "2025-06-12T17:25:33.069816", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Logo remove background setting: False, has logo: False", "module": "qr_preview", "function": "generate_preview_svg_modular", "line": 727}
{"timestamp": "2025-06-12T17:25:33.083155", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Drawing 3 finder patterns at positions: [(0, 0), (42, 0), (0, 42)]", "module": "qr_preview", "function": "generate_preview_svg_modular", "line": 775}
{"timestamp": "2025-06-12T17:25:33.083300", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "QR size: 49, border_style: square, center_style: square", "module": "qr_preview", "function": "generate_preview_svg_modular", "line": 776}
{"timestamp": "2025-06-12T17:25:33.083402", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Drawing finder pattern 1/3 at position (0, 0)", "module": "qr_preview", "function": "generate_preview_svg_modular", "line": 780}
{"timestamp": "2025-06-12T17:25:33.083464", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "_draw_finder_marker called: mx=0, my=0, module_size=5.918367346938775, border_style=square, center_style=square, border_color=#000000, center_color=#000000, offset=(20, 20)", "module": "qr_preview", "function": "_draw_finder_marker", "line": 97}
{"timestamp": "2025-06-12T17:25:33.085055", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Creating finder pattern group with id='marker-0-0'", "module": "qr_preview", "function": "_draw_finder_marker", "line": 110}
{"timestamp": "2025-06-12T17:25:33.085113", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Drawing square border:", "module": "qr_preview", "function": "_draw_finder_marker", "line": 160}
{"timestamp": "2025-06-12T17:25:33.085163", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "  Outer: insert=(20.0, 20.0), size=(41.42857142857143, 41.42857142857143)", "module": "qr_preview", "function": "_draw_finder_marker", "line": 161}
{"timestamp": "2025-06-12T17:25:33.085207", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "  Inner: insert=(27.10204081632653, 27.10204081632653), size=(27.224489795918366, 27.224489795918366)", "module": "qr_preview", "function": "_draw_finder_marker", "line": 162}
{"timestamp": "2025-06-12T17:25:33.085329", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Drawing marker center: style=square, color=#000000, pos=(34.79591836734694, 34.79591836734694), size=11.83673469387755", "module": "qr_preview", "function": "_draw_marker_center", "line": 179}
{"timestamp": "2025-06-12T17:25:33.085390", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Successfully added finder pattern group to SVG", "module": "qr_preview", "function": "_draw_finder_marker", "line": 171}
{"timestamp": "2025-06-12T17:25:33.085433", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Successfully drew finder pattern 1/3", "module": "qr_preview", "function": "generate_preview_svg_modular", "line": 782}
{"timestamp": "2025-06-12T17:25:33.085472", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Drawing finder pattern 2/3 at position (42, 0)", "module": "qr_preview", "function": "generate_preview_svg_modular", "line": 780}
{"timestamp": "2025-06-12T17:25:33.085512", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "_draw_finder_marker called: mx=42, my=0, module_size=5.918367346938775, border_style=square, center_style=square, border_color=#000000, center_color=#000000, offset=(20, 20)", "module": "qr_preview", "function": "_draw_finder_marker", "line": 97}
{"timestamp": "2025-06-12T17:25:33.085557", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Creating finder pattern group with id='marker-42-0'", "module": "qr_preview", "function": "_draw_finder_marker", "line": 110}
{"timestamp": "2025-06-12T17:25:33.085596", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Drawing square border:", "module": "qr_preview", "function": "_draw_finder_marker", "line": 160}
{"timestamp": "2025-06-12T17:25:33.085661", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "  Outer: insert=(268.57142857142856, 20.0), size=(41.42857142857143, 41.42857142857143)", "module": "qr_preview", "function": "_draw_finder_marker", "line": 161}
{"timestamp": "2025-06-12T17:25:33.085725", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "  Inner: insert=(275.67346938775506, 27.10204081632653), size=(27.224489795918366, 27.224489795918366)", "module": "qr_preview", "function": "_draw_finder_marker", "line": 162}
{"timestamp": "2025-06-12T17:25:33.085821", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Drawing marker center: style=square, color=#000000, pos=(283.3673469387755, 34.79591836734694), size=11.83673469387755", "module": "qr_preview", "function": "_draw_marker_center", "line": 179}
{"timestamp": "2025-06-12T17:25:33.085885", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Successfully added finder pattern group to SVG", "module": "qr_preview", "function": "_draw_finder_marker", "line": 171}
{"timestamp": "2025-06-12T17:25:33.085964", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Successfully drew finder pattern 2/3", "module": "qr_preview", "function": "generate_preview_svg_modular", "line": 782}
{"timestamp": "2025-06-12T17:25:33.086005", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Drawing finder pattern 3/3 at position (0, 42)", "module": "qr_preview", "function": "generate_preview_svg_modular", "line": 780}
{"timestamp": "2025-06-12T17:25:33.086046", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "_draw_finder_marker called: mx=0, my=42, module_size=5.918367346938775, border_style=square, center_style=square, border_color=#000000, center_color=#000000, offset=(20, 20)", "module": "qr_preview", "function": "_draw_finder_marker", "line": 97}
{"timestamp": "2025-06-12T17:25:33.086095", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Creating finder pattern group with id='marker-0-42'", "module": "qr_preview", "function": "_draw_finder_marker", "line": 110}
{"timestamp": "2025-06-12T17:25:33.086133", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Drawing square border:", "module": "qr_preview", "function": "_draw_finder_marker", "line": 160}
{"timestamp": "2025-06-12T17:25:33.086172", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "  Outer: insert=(20.0, 268.57142857142856), size=(41.42857142857143, 41.42857142857143)", "module": "qr_preview", "function": "_draw_finder_marker", "line": 161}
{"timestamp": "2025-06-12T17:25:33.086211", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "  Inner: insert=(27.10204081632653, 275.67346938775506), size=(27.224489795918366, 27.224489795918366)", "module": "qr_preview", "function": "_draw_finder_marker", "line": 162}
{"timestamp": "2025-06-12T17:25:33.086275", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Drawing marker center: style=square, color=#000000, pos=(34.79591836734694, 283.3673469387755), size=11.83673469387755", "module": "qr_preview", "function": "_draw_marker_center", "line": 179}
{"timestamp": "2025-06-12T17:25:33.086329", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Successfully added finder pattern group to SVG", "module": "qr_preview", "function": "_draw_finder_marker", "line": 171}
{"timestamp": "2025-06-12T17:25:33.086367", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Successfully drew finder pattern 3/3", "module": "qr_preview", "function": "generate_preview_svg_modular", "line": 782}
{"timestamp": "2025-06-12T17:25:33.105263", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Generated SVG length: 176251", "module": "qr_preview", "function": "generate_preview_svg_modular", "line": 802}
{"timestamp": "2025-06-12T17:25:33.105398", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "SVG contains marker-0-0: True", "module": "qr_preview", "function": "generate_preview_svg_modular", "line": 803}
{"timestamp": "2025-06-12T17:25:33.105513", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "SVG contains marker-18-0: False", "module": "qr_preview", "function": "generate_preview_svg_modular", "line": 804}
{"timestamp": "2025-06-12T17:25:33.105632", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "SVG contains marker-0-18: False", "module": "qr_preview", "function": "generate_preview_svg_modular", "line": 805}
{"timestamp": "2025-06-12T17:25:33.105918", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "SVG marker counts - outer: 3, inner: 3, dot: 0", "module": "qr_preview", "function": "generate_preview_svg_modular", "line": 813}
{"timestamp": "2025-06-12T17:25:33.105955", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "SVG label counts - text: 0, background: 0", "module": "qr_preview", "function": "generate_preview_svg_modular", "line": 814}
{"timestamp": "2025-06-12T17:25:33.108584", "level": "DEBUG", "logger": "backend.main", "message": "[Auth] Enforced Bearer token authentication for request", "module": "main", "function": "bearer_token_auth_middleware", "line": 274}
{"timestamp": "2025-06-12T17:25:34.908257", "level": "DEBUG", "logger": "backend.core.security.jwt_keys", "message": "Token contains key ID: a69f821a-65e8-4292-8feb-985dc7ac94d9", "module": "jwt_keys", "function": "decode_token", "line": 188}
{"timestamp": "2025-06-12T17:25:34.908329", "level": "DEBUG", "logger": "backend.core.security.jwt_keys", "message": "Using key ID a69f821a-65e8-4292-8feb-985dc7ac94d9 for verification", "module": "jwt_keys", "function": "decode_token", "line": 195}
{"timestamp": "2025-06-12T17:25:34.908490", "level": "DEBUG", "logger": "backend.services.auth", "message": "JWT decoded successfully with key manager", "module": "auth", "function": "authenticate_token", "line": 683}
{"timestamp": "2025-06-12T17:25:34.910489", "level": "DEBUG", "logger": "backend.services.db_utils", "message": "Query 'auth_lookup_e4ae2d8f-b32f-52d7-99cd-8a33b19652c7' completed in 0.002s", "module": "db_utils", "function": "execute_query_with_timeout", "line": 68}
{"timestamp": "2025-06-12T17:25:34.910547", "level": "DEBUG", "logger": "backend.services.db_utils", "message": "Auth query 'auth_lookup_e4ae2d8f-b32f-52d7-99cd-8a33b19652c7' successful, result format preserved", "module": "db_utils", "function": "execute_query_with_timeout", "line": 72}
{"timestamp": "2025-06-12T17:25:34.913920", "level": "DEBUG", "logger": "backend.services.auth", "message": "Authentication attempt with token prefix: eyJhbGciOi...", "module": "auth", "function": "get_current_user", "line": 727}
{"timestamp": "2025-06-12T17:25:34.914032", "level": "DEBUG", "logger": "backend.core.security.jwt_keys", "message": "Token contains key ID: a69f821a-65e8-4292-8feb-985dc7ac94d9", "module": "jwt_keys", "function": "decode_token", "line": 188}
{"timestamp": "2025-06-12T17:25:34.914076", "level": "DEBUG", "logger": "backend.core.security.jwt_keys", "message": "Using key ID a69f821a-65e8-4292-8feb-985dc7ac94d9 for verification", "module": "jwt_keys", "function": "decode_token", "line": 195}
{"timestamp": "2025-06-12T17:25:34.914188", "level": "DEBUG", "logger": "backend.services.auth", "message": "JWT decoded successfully with key manager", "module": "auth", "function": "authenticate_token", "line": 683}
{"timestamp": "2025-06-12T17:25:34.915387", "level": "DEBUG", "logger": "backend.services.db_utils", "message": "Query 'auth_lookup_e4ae2d8f-b32f-52d7-99cd-8a33b19652c7' completed in 0.001s", "module": "db_utils", "function": "execute_query_with_timeout", "line": 68}
{"timestamp": "2025-06-12T17:25:34.915437", "level": "DEBUG", "logger": "backend.services.db_utils", "message": "Auth query 'auth_lookup_e4ae2d8f-b32f-52d7-99cd-8a33b19652c7' successful, result format preserved", "module": "db_utils", "function": "execute_query_with_timeout", "line": 72}
{"timestamp": "2025-06-12T17:25:34.915493", "level": "DEBUG", "logger": "backend.services.auth", "message": "Successfully authenticated user: <EMAIL>", "module": "auth", "function": "get_current_user", "line": 732}
{"timestamp": "2025-06-12T17:25:34.915532", "level": "DEBUG", "logger": "backend.services.auth", "message": "Successfully authenticated user: <EMAIL>", "module": "auth", "function": "get_current_user", "line": 738}
{"timestamp": "2025-06-12T17:25:34.917249", "level": "DEBUG", "logger": "backend.routes.qr_preview", "message": "Received QR preview SVG request: {\"qr_type\": \"scooter_unlock\", \"data\": {\"scooter_id\": \"SCOOT123\", \"unlock_code\": \"UNLOCK42\", \"title\": \"Scooter_unlock QR Code\", \"description\": \"Scan to view scooter_unlock content\", \"service_provider\": \"L\"}, \"designOptions\": {\"size\": 256, \"margin\": 10, \"pattern\": \"square\", \"marker_border_style\": \"square\", \"marker_center_style\": \"square\", \"logo_size\": 0.3, \"logo_position\": \"center\", \"logo_opacity\": 1, \"logo_remove_background\": false, \"frame_style\": \"none\", \"frame_font\": \"SF Pro\", \"frame_font_size\"", "module": "qr_preview", "function": "preview_qr_svg", "line": 242}
{"timestamp": "2025-06-12T17:25:34.917404", "level": "DEBUG", "logger": "backend.routes.qr_preview", "message": "Form data keys: ['request']", "module": "qr_preview", "function": "preview_qr_svg", "line": 297}
{"timestamp": "2025-06-12T17:25:34.917447", "level": "DEBUG", "logger": "backend.routes.qr_preview", "message": "No logo key found in form data", "module": "qr_preview", "function": "preview_qr_svg", "line": 309}
{"timestamp": "2025-06-12T17:25:34.917495", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Formatting content for QR type: qrtype.scooter_unlock, content keys: ['scooter_id', 'unlock_code', 'title', 'description', 'service_provider']", "module": "qr_preview", "function": "_format_qr_data", "line": 876}
{"timestamp": "2025-06-12T17:25:34.936230", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Logo remove background setting: False, has logo: False", "module": "qr_preview", "function": "generate_preview_svg_modular", "line": 727}
{"timestamp": "2025-06-12T17:25:34.951827", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Drawing 3 finder patterns at positions: [(0, 0), (46, 0), (0, 46)]", "module": "qr_preview", "function": "generate_preview_svg_modular", "line": 775}
{"timestamp": "2025-06-12T17:25:34.951898", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "QR size: 53, border_style: square, center_style: square", "module": "qr_preview", "function": "generate_preview_svg_modular", "line": 776}
{"timestamp": "2025-06-12T17:25:34.951942", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Drawing finder pattern 1/3 at position (0, 0)", "module": "qr_preview", "function": "generate_preview_svg_modular", "line": 780}
{"timestamp": "2025-06-12T17:25:34.952007", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "_draw_finder_marker called: mx=0, my=0, module_size=5.471698113207547, border_style=square, center_style=square, border_color=#000000, center_color=#000000, offset=(20, 20)", "module": "qr_preview", "function": "_draw_finder_marker", "line": 97}
{"timestamp": "2025-06-12T17:25:34.952060", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Creating finder pattern group with id='marker-0-0'", "module": "qr_preview", "function": "_draw_finder_marker", "line": 110}
{"timestamp": "2025-06-12T17:25:34.952103", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Drawing square border:", "module": "qr_preview", "function": "_draw_finder_marker", "line": 160}
{"timestamp": "2025-06-12T17:25:34.952145", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "  Outer: insert=(20.0, 20.0), size=(38.301886792452834, 38.301886792452834)", "module": "qr_preview", "function": "_draw_finder_marker", "line": 161}
{"timestamp": "2025-06-12T17:25:34.952186", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "  Inner: insert=(26.566037735849058, 26.566037735849058), size=(25.169811320754715, 25.169811320754715)", "module": "qr_preview", "function": "_draw_finder_marker", "line": 162}
{"timestamp": "2025-06-12T17:25:34.952270", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Drawing marker center: style=square, color=#000000, pos=(33.67924528301887, 33.67924528301887), size=10.943396226415095", "module": "qr_preview", "function": "_draw_marker_center", "line": 179}
{"timestamp": "2025-06-12T17:25:34.952322", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Successfully added finder pattern group to SVG", "module": "qr_preview", "function": "_draw_finder_marker", "line": 171}
{"timestamp": "2025-06-12T17:25:34.952358", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Successfully drew finder pattern 1/3", "module": "qr_preview", "function": "generate_preview_svg_modular", "line": 782}
{"timestamp": "2025-06-12T17:25:34.952389", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Drawing finder pattern 2/3 at position (46, 0)", "module": "qr_preview", "function": "generate_preview_svg_modular", "line": 780}
{"timestamp": "2025-06-12T17:25:34.952468", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "_draw_finder_marker called: mx=46, my=0, module_size=5.471698113207547, border_style=square, center_style=square, border_color=#000000, center_color=#000000, offset=(20, 20)", "module": "qr_preview", "function": "_draw_finder_marker", "line": 97}
{"timestamp": "2025-06-12T17:25:34.952511", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Creating finder pattern group with id='marker-46-0'", "module": "qr_preview", "function": "_draw_finder_marker", "line": 110}
{"timestamp": "2025-06-12T17:25:34.952541", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Drawing square border:", "module": "qr_preview", "function": "_draw_finder_marker", "line": 160}
{"timestamp": "2025-06-12T17:25:34.952574", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "  Outer: insert=(271.6981132075472, 20.0), size=(38.301886792452834, 38.301886792452834)", "module": "qr_preview", "function": "_draw_finder_marker", "line": 161}
{"timestamp": "2025-06-12T17:25:34.952607", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "  Inner: insert=(278.26415094339626, 26.566037735849058), size=(25.169811320754715, 25.169811320754715)", "module": "qr_preview", "function": "_draw_finder_marker", "line": 162}
{"timestamp": "2025-06-12T17:25:34.952668", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Drawing marker center: style=square, color=#000000, pos=(285.37735849056605, 33.67924528301887), size=10.943396226415095", "module": "qr_preview", "function": "_draw_marker_center", "line": 179}
{"timestamp": "2025-06-12T17:25:34.952716", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Successfully added finder pattern group to SVG", "module": "qr_preview", "function": "_draw_finder_marker", "line": 171}
{"timestamp": "2025-06-12T17:25:34.952747", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Successfully drew finder pattern 2/3", "module": "qr_preview", "function": "generate_preview_svg_modular", "line": 782}
{"timestamp": "2025-06-12T17:25:34.952781", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Drawing finder pattern 3/3 at position (0, 46)", "module": "qr_preview", "function": "generate_preview_svg_modular", "line": 780}
{"timestamp": "2025-06-12T17:25:34.952815", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "_draw_finder_marker called: mx=0, my=46, module_size=5.471698113207547, border_style=square, center_style=square, border_color=#000000, center_color=#000000, offset=(20, 20)", "module": "qr_preview", "function": "_draw_finder_marker", "line": 97}
{"timestamp": "2025-06-12T17:25:34.952856", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Creating finder pattern group with id='marker-0-46'", "module": "qr_preview", "function": "_draw_finder_marker", "line": 110}
{"timestamp": "2025-06-12T17:25:34.952886", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Drawing square border:", "module": "qr_preview", "function": "_draw_finder_marker", "line": 160}
{"timestamp": "2025-06-12T17:25:34.952917", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "  Outer: insert=(20.0, 271.6981132075472), size=(38.301886792452834, 38.301886792452834)", "module": "qr_preview", "function": "_draw_finder_marker", "line": 161}
{"timestamp": "2025-06-12T17:25:34.952949", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "  Inner: insert=(26.566037735849058, 278.26415094339626), size=(25.169811320754715, 25.169811320754715)", "module": "qr_preview", "function": "_draw_finder_marker", "line": 162}
{"timestamp": "2025-06-12T17:25:34.953011", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Drawing marker center: style=square, color=#000000, pos=(33.67924528301887, 285.37735849056605), size=10.943396226415095", "module": "qr_preview", "function": "_draw_marker_center", "line": 179}
{"timestamp": "2025-06-12T17:25:34.953086", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Successfully added finder pattern group to SVG", "module": "qr_preview", "function": "_draw_finder_marker", "line": 171}
{"timestamp": "2025-06-12T17:25:34.953117", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Successfully drew finder pattern 3/3", "module": "qr_preview", "function": "generate_preview_svg_modular", "line": 782}
{"timestamp": "2025-06-12T17:25:34.976463", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Generated SVG length: 206209", "module": "qr_preview", "function": "generate_preview_svg_modular", "line": 802}
{"timestamp": "2025-06-12T17:25:34.976601", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "SVG contains marker-0-0: True", "module": "qr_preview", "function": "generate_preview_svg_modular", "line": 803}
{"timestamp": "2025-06-12T17:25:34.976718", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "SVG contains marker-18-0: False", "module": "qr_preview", "function": "generate_preview_svg_modular", "line": 804}
{"timestamp": "2025-06-12T17:25:34.976824", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "SVG contains marker-0-18: False", "module": "qr_preview", "function": "generate_preview_svg_modular", "line": 805}
{"timestamp": "2025-06-12T17:25:34.977152", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "SVG marker counts - outer: 3, inner: 3, dot: 0", "module": "qr_preview", "function": "generate_preview_svg_modular", "line": 813}
{"timestamp": "2025-06-12T17:25:34.977187", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "SVG label counts - text: 0, background: 0", "module": "qr_preview", "function": "generate_preview_svg_modular", "line": 814}
{"timestamp": "2025-06-12T17:25:34.979179", "level": "DEBUG", "logger": "backend.main", "message": "[Auth] Enforced Bearer token authentication for request", "module": "main", "function": "bearer_token_auth_middleware", "line": 274}
{"timestamp": "2025-06-12T17:25:35.827185", "level": "DEBUG", "logger": "backend.core.security.jwt_keys", "message": "Token contains key ID: a69f821a-65e8-4292-8feb-985dc7ac94d9", "module": "jwt_keys", "function": "decode_token", "line": 188}
{"timestamp": "2025-06-12T17:25:35.827394", "level": "DEBUG", "logger": "backend.core.security.jwt_keys", "message": "Using key ID a69f821a-65e8-4292-8feb-985dc7ac94d9 for verification", "module": "jwt_keys", "function": "decode_token", "line": 195}
{"timestamp": "2025-06-12T17:25:35.828504", "level": "DEBUG", "logger": "backend.services.auth", "message": "JWT decoded successfully with key manager", "module": "auth", "function": "authenticate_token", "line": 683}
{"timestamp": "2025-06-12T17:25:35.840476", "level": "DEBUG", "logger": "backend.services.db_utils", "message": "Query 'auth_lookup_e4ae2d8f-b32f-52d7-99cd-8a33b19652c7' completed in 0.010s", "module": "db_utils", "function": "execute_query_with_timeout", "line": 68}
{"timestamp": "2025-06-12T17:25:35.840868", "level": "DEBUG", "logger": "backend.services.db_utils", "message": "Auth query 'auth_lookup_e4ae2d8f-b32f-52d7-99cd-8a33b19652c7' successful, result format preserved", "module": "db_utils", "function": "execute_query_with_timeout", "line": 72}
{"timestamp": "2025-06-12T17:25:35.861963", "level": "DEBUG", "logger": "backend.services.auth", "message": "Authentication attempt with token prefix: eyJhbGciOi...", "module": "auth", "function": "get_current_user", "line": 727}
{"timestamp": "2025-06-12T17:25:35.862083", "level": "DEBUG", "logger": "backend.core.security.jwt_keys", "message": "Token contains key ID: a69f821a-65e8-4292-8feb-985dc7ac94d9", "module": "jwt_keys", "function": "decode_token", "line": 188}
{"timestamp": "2025-06-12T17:25:35.862167", "level": "DEBUG", "logger": "backend.core.security.jwt_keys", "message": "Using key ID a69f821a-65e8-4292-8feb-985dc7ac94d9 for verification", "module": "jwt_keys", "function": "decode_token", "line": 195}
{"timestamp": "2025-06-12T17:25:35.862307", "level": "DEBUG", "logger": "backend.services.auth", "message": "JWT decoded successfully with key manager", "module": "auth", "function": "authenticate_token", "line": 683}
{"timestamp": "2025-06-12T17:25:35.866366", "level": "DEBUG", "logger": "backend.services.db_utils", "message": "Query 'auth_lookup_e4ae2d8f-b32f-52d7-99cd-8a33b19652c7' completed in 0.004s", "module": "db_utils", "function": "execute_query_with_timeout", "line": 68}
{"timestamp": "2025-06-12T17:25:35.866413", "level": "DEBUG", "logger": "backend.services.db_utils", "message": "Auth query 'auth_lookup_e4ae2d8f-b32f-52d7-99cd-8a33b19652c7' successful, result format preserved", "module": "db_utils", "function": "execute_query_with_timeout", "line": 72}
{"timestamp": "2025-06-12T17:25:35.866477", "level": "DEBUG", "logger": "backend.services.auth", "message": "Successfully authenticated user: <EMAIL>", "module": "auth", "function": "get_current_user", "line": 732}
{"timestamp": "2025-06-12T17:25:35.866508", "level": "DEBUG", "logger": "backend.services.auth", "message": "Successfully authenticated user: <EMAIL>", "module": "auth", "function": "get_current_user", "line": 738}
{"timestamp": "2025-06-12T17:25:35.883348", "level": "DEBUG", "logger": "backend.routes.qr_preview", "message": "Received QR preview SVG request: {\"qr_type\": \"scooter_unlock\", \"data\": {\"scooter_id\": \"SCOOT123\", \"unlock_code\": \"UNLOCK42\", \"title\": \"Scooter_unlock QR Code\", \"description\": \"Scan to view scooter_unlock content\", \"service_provider\": \"L\"}, \"designOptions\": {\"size\": 256, \"margin\": 10, \"pattern\": \"square\", \"marker_border_style\": \"square\", \"marker_border_color\": \"#000000\", \"marker_center_style\": \"square\", \"marker_center_color\": \"#000000\", \"logo_size\": 0.3, \"logo_position\": \"center\", \"logo_opacity\": 1, \"frame_style\": \"none\", \"frame", "module": "qr_preview", "function": "preview_qr_svg", "line": 242}
{"timestamp": "2025-06-12T17:25:35.883510", "level": "DEBUG", "logger": "backend.routes.qr_preview", "message": "Form data keys: ['request']", "module": "qr_preview", "function": "preview_qr_svg", "line": 297}
{"timestamp": "2025-06-12T17:25:35.884162", "level": "DEBUG", "logger": "backend.routes.qr_preview", "message": "No logo key found in form data", "module": "qr_preview", "function": "preview_qr_svg", "line": 309}
{"timestamp": "2025-06-12T17:25:35.884438", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Formatting content for QR type: qrtype.scooter_unlock, content keys: ['scooter_id', 'unlock_code', 'title', 'description', 'service_provider']", "module": "qr_preview", "function": "_format_qr_data", "line": 876}
{"timestamp": "2025-06-12T17:25:35.931521", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Logo remove background setting: False, has logo: False", "module": "qr_preview", "function": "generate_preview_svg_modular", "line": 727}
{"timestamp": "2025-06-12T17:25:35.962740", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Drawing 3 finder patterns at positions: [(0, 0), (46, 0), (0, 46)]", "module": "qr_preview", "function": "generate_preview_svg_modular", "line": 775}
{"timestamp": "2025-06-12T17:25:35.962804", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "QR size: 53, border_style: square, center_style: square", "module": "qr_preview", "function": "generate_preview_svg_modular", "line": 776}
{"timestamp": "2025-06-12T17:25:35.962874", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Drawing finder pattern 1/3 at position (0, 0)", "module": "qr_preview", "function": "generate_preview_svg_modular", "line": 780}
{"timestamp": "2025-06-12T17:25:35.962946", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "_draw_finder_marker called: mx=0, my=0, module_size=5.471698113207547, border_style=square, center_style=square, border_color=#000000, center_color=#000000, offset=(20, 20)", "module": "qr_preview", "function": "_draw_finder_marker", "line": 97}
{"timestamp": "2025-06-12T17:25:35.963290", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Creating finder pattern group with id='marker-0-0'", "module": "qr_preview", "function": "_draw_finder_marker", "line": 110}
{"timestamp": "2025-06-12T17:25:35.963388", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Drawing square border:", "module": "qr_preview", "function": "_draw_finder_marker", "line": 160}
{"timestamp": "2025-06-12T17:25:35.963468", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "  Outer: insert=(20.0, 20.0), size=(38.301886792452834, 38.301886792452834)", "module": "qr_preview", "function": "_draw_finder_marker", "line": 161}
{"timestamp": "2025-06-12T17:25:35.963622", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "  Inner: insert=(26.566037735849058, 26.566037735849058), size=(25.169811320754715, 25.169811320754715)", "module": "qr_preview", "function": "_draw_finder_marker", "line": 162}
{"timestamp": "2025-06-12T17:25:35.963731", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Drawing marker center: style=square, color=#000000, pos=(33.67924528301887, 33.67924528301887), size=10.943396226415095", "module": "qr_preview", "function": "_draw_marker_center", "line": 179}
{"timestamp": "2025-06-12T17:25:35.963789", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Successfully added finder pattern group to SVG", "module": "qr_preview", "function": "_draw_finder_marker", "line": 171}
{"timestamp": "2025-06-12T17:25:35.963832", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Successfully drew finder pattern 1/3", "module": "qr_preview", "function": "generate_preview_svg_modular", "line": 782}
{"timestamp": "2025-06-12T17:25:35.963869", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Drawing finder pattern 2/3 at position (46, 0)", "module": "qr_preview", "function": "generate_preview_svg_modular", "line": 780}
{"timestamp": "2025-06-12T17:25:35.963993", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "_draw_finder_marker called: mx=46, my=0, module_size=5.471698113207547, border_style=square, center_style=square, border_color=#000000, center_color=#000000, offset=(20, 20)", "module": "qr_preview", "function": "_draw_finder_marker", "line": 97}
{"timestamp": "2025-06-12T17:25:35.964316", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Creating finder pattern group with id='marker-46-0'", "module": "qr_preview", "function": "_draw_finder_marker", "line": 110}
{"timestamp": "2025-06-12T17:25:35.964488", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Drawing square border:", "module": "qr_preview", "function": "_draw_finder_marker", "line": 160}
{"timestamp": "2025-06-12T17:25:35.964619", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "  Outer: insert=(271.6981132075472, 20.0), size=(38.301886792452834, 38.301886792452834)", "module": "qr_preview", "function": "_draw_finder_marker", "line": 161}
{"timestamp": "2025-06-12T17:25:35.964744", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "  Inner: insert=(278.26415094339626, 26.566037735849058), size=(25.169811320754715, 25.169811320754715)", "module": "qr_preview", "function": "_draw_finder_marker", "line": 162}
{"timestamp": "2025-06-12T17:25:35.965032", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Drawing marker center: style=square, color=#000000, pos=(285.37735849056605, 33.67924528301887), size=10.943396226415095", "module": "qr_preview", "function": "_draw_marker_center", "line": 179}
{"timestamp": "2025-06-12T17:25:35.965204", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Successfully added finder pattern group to SVG", "module": "qr_preview", "function": "_draw_finder_marker", "line": 171}
{"timestamp": "2025-06-12T17:25:35.966140", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Successfully drew finder pattern 2/3", "module": "qr_preview", "function": "generate_preview_svg_modular", "line": 782}
{"timestamp": "2025-06-12T17:25:35.966899", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Drawing finder pattern 3/3 at position (0, 46)", "module": "qr_preview", "function": "generate_preview_svg_modular", "line": 780}
{"timestamp": "2025-06-12T17:25:35.967003", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "_draw_finder_marker called: mx=0, my=46, module_size=5.471698113207547, border_style=square, center_style=square, border_color=#000000, center_color=#000000, offset=(20, 20)", "module": "qr_preview", "function": "_draw_finder_marker", "line": 97}
{"timestamp": "2025-06-12T17:25:35.967059", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Creating finder pattern group with id='marker-0-46'", "module": "qr_preview", "function": "_draw_finder_marker", "line": 110}
{"timestamp": "2025-06-12T17:25:35.968658", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Drawing square border:", "module": "qr_preview", "function": "_draw_finder_marker", "line": 160}
{"timestamp": "2025-06-12T17:25:35.968720", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "  Outer: insert=(20.0, 271.6981132075472), size=(38.301886792452834, 38.301886792452834)", "module": "qr_preview", "function": "_draw_finder_marker", "line": 161}
{"timestamp": "2025-06-12T17:25:35.968757", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "  Inner: insert=(26.566037735849058, 278.26415094339626), size=(25.169811320754715, 25.169811320754715)", "module": "qr_preview", "function": "_draw_finder_marker", "line": 162}
{"timestamp": "2025-06-12T17:25:35.968847", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Drawing marker center: style=square, color=#000000, pos=(33.67924528301887, 285.37735849056605), size=10.943396226415095", "module": "qr_preview", "function": "_draw_marker_center", "line": 179}
{"timestamp": "2025-06-12T17:25:35.968899", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Successfully added finder pattern group to SVG", "module": "qr_preview", "function": "_draw_finder_marker", "line": 171}
{"timestamp": "2025-06-12T17:25:35.968931", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Successfully drew finder pattern 3/3", "module": "qr_preview", "function": "generate_preview_svg_modular", "line": 782}
{"timestamp": "2025-06-12T17:25:36.007789", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Generated SVG length: 206209", "module": "qr_preview", "function": "generate_preview_svg_modular", "line": 802}
{"timestamp": "2025-06-12T17:25:36.007942", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "SVG contains marker-0-0: True", "module": "qr_preview", "function": "generate_preview_svg_modular", "line": 803}
{"timestamp": "2025-06-12T17:25:36.008152", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "SVG contains marker-18-0: False", "module": "qr_preview", "function": "generate_preview_svg_modular", "line": 804}
{"timestamp": "2025-06-12T17:25:36.008291", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "SVG contains marker-0-18: False", "module": "qr_preview", "function": "generate_preview_svg_modular", "line": 805}
{"timestamp": "2025-06-12T17:25:36.008644", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "SVG marker counts - outer: 3, inner: 3, dot: 0", "module": "qr_preview", "function": "generate_preview_svg_modular", "line": 813}
{"timestamp": "2025-06-12T17:25:36.008677", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "SVG label counts - text: 0, background: 0", "module": "qr_preview", "function": "generate_preview_svg_modular", "line": 814}
{"timestamp": "2025-06-12T17:25:36.018318", "level": "DEBUG", "logger": "backend.main", "message": "[Auth] Enforced Bearer token authentication for request", "module": "main", "function": "bearer_token_auth_middleware", "line": 274}
{"timestamp": "2025-06-12T17:25:37.506782", "level": "DEBUG", "logger": "backend.core.security.jwt_keys", "message": "Token contains key ID: a69f821a-65e8-4292-8feb-985dc7ac94d9", "module": "jwt_keys", "function": "decode_token", "line": 188}
{"timestamp": "2025-06-12T17:25:37.506863", "level": "DEBUG", "logger": "backend.core.security.jwt_keys", "message": "Using key ID a69f821a-65e8-4292-8feb-985dc7ac94d9 for verification", "module": "jwt_keys", "function": "decode_token", "line": 195}
{"timestamp": "2025-06-12T17:25:37.507222", "level": "DEBUG", "logger": "backend.services.auth", "message": "JWT decoded successfully with key manager", "module": "auth", "function": "authenticate_token", "line": 683}
{"timestamp": "2025-06-12T17:25:37.513375", "level": "DEBUG", "logger": "backend.services.db_utils", "message": "Query 'auth_lookup_e4ae2d8f-b32f-52d7-99cd-8a33b19652c7' completed in 0.004s", "module": "db_utils", "function": "execute_query_with_timeout", "line": 68}
{"timestamp": "2025-06-12T17:25:37.513454", "level": "DEBUG", "logger": "backend.services.db_utils", "message": "Auth query 'auth_lookup_e4ae2d8f-b32f-52d7-99cd-8a33b19652c7' successful, result format preserved", "module": "db_utils", "function": "execute_query_with_timeout", "line": 72}
{"timestamp": "2025-06-12T17:25:37.521583", "level": "DEBUG", "logger": "backend.services.auth", "message": "Authentication attempt with token prefix: eyJhbGciOi...", "module": "auth", "function": "get_current_user", "line": 727}
{"timestamp": "2025-06-12T17:25:37.521712", "level": "DEBUG", "logger": "backend.core.security.jwt_keys", "message": "Token contains key ID: a69f821a-65e8-4292-8feb-985dc7ac94d9", "module": "jwt_keys", "function": "decode_token", "line": 188}
{"timestamp": "2025-06-12T17:25:37.521755", "level": "DEBUG", "logger": "backend.core.security.jwt_keys", "message": "Using key ID a69f821a-65e8-4292-8feb-985dc7ac94d9 for verification", "module": "jwt_keys", "function": "decode_token", "line": 195}
{"timestamp": "2025-06-12T17:25:37.522095", "level": "DEBUG", "logger": "backend.services.auth", "message": "JWT decoded successfully with key manager", "module": "auth", "function": "authenticate_token", "line": 683}
{"timestamp": "2025-06-12T17:25:37.523380", "level": "DEBUG", "logger": "backend.services.db_utils", "message": "Query 'auth_lookup_e4ae2d8f-b32f-52d7-99cd-8a33b19652c7' completed in 0.001s", "module": "db_utils", "function": "execute_query_with_timeout", "line": 68}
{"timestamp": "2025-06-12T17:25:37.523436", "level": "DEBUG", "logger": "backend.services.db_utils", "message": "Auth query 'auth_lookup_e4ae2d8f-b32f-52d7-99cd-8a33b19652c7' successful, result format preserved", "module": "db_utils", "function": "execute_query_with_timeout", "line": 72}
{"timestamp": "2025-06-12T17:25:37.523492", "level": "DEBUG", "logger": "backend.services.auth", "message": "Successfully authenticated user: <EMAIL>", "module": "auth", "function": "get_current_user", "line": 732}
{"timestamp": "2025-06-12T17:25:37.523537", "level": "DEBUG", "logger": "backend.services.auth", "message": "Successfully authenticated user: <EMAIL>", "module": "auth", "function": "get_current_user", "line": 738}
{"timestamp": "2025-06-12T17:25:37.526166", "level": "DEBUG", "logger": "backend.routes.qr_preview", "message": "Received QR preview SVG request: {\"qr_type\": \"scooter_unlock\", \"data\": {\"scooter_id\": \"SCOOT123\", \"unlock_code\": \"UNLOCK42\", \"title\": \"Scooter_unlock QR Code\", \"description\": \"Scan to view scooter_unlock content\", \"service_provider\": \"Lime\"}, \"designOptions\": {\"size\": 256, \"margin\": 10, \"pattern\": \"square\", \"marker_border_style\": \"square\", \"marker_center_style\": \"square\", \"logo_size\": 0.3, \"logo_position\": \"center\", \"logo_opacity\": 1, \"logo_remove_background\": false, \"frame_style\": \"none\", \"frame_font\": \"SF Pro\", \"frame_font_si", "module": "qr_preview", "function": "preview_qr_svg", "line": 242}
{"timestamp": "2025-06-12T17:25:37.526350", "level": "DEBUG", "logger": "backend.routes.qr_preview", "message": "Form data keys: ['request']", "module": "qr_preview", "function": "preview_qr_svg", "line": 297}
{"timestamp": "2025-06-12T17:25:37.526390", "level": "DEBUG", "logger": "backend.routes.qr_preview", "message": "No logo key found in form data", "module": "qr_preview", "function": "preview_qr_svg", "line": 309}
{"timestamp": "2025-06-12T17:25:37.526441", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Formatting content for QR type: qrtype.scooter_unlock, content keys: ['scooter_id', 'unlock_code', 'title', 'description', 'service_provider']", "module": "qr_preview", "function": "_format_qr_data", "line": 876}
{"timestamp": "2025-06-12T17:25:37.545536", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Logo remove background setting: False, has logo: False", "module": "qr_preview", "function": "generate_preview_svg_modular", "line": 727}
{"timestamp": "2025-06-12T17:25:37.562465", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Drawing 3 finder patterns at positions: [(0, 0), (46, 0), (0, 46)]", "module": "qr_preview", "function": "generate_preview_svg_modular", "line": 775}
{"timestamp": "2025-06-12T17:25:37.562533", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "QR size: 53, border_style: square, center_style: square", "module": "qr_preview", "function": "generate_preview_svg_modular", "line": 776}
{"timestamp": "2025-06-12T17:25:37.562575", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Drawing finder pattern 1/3 at position (0, 0)", "module": "qr_preview", "function": "generate_preview_svg_modular", "line": 780}
{"timestamp": "2025-06-12T17:25:37.562621", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "_draw_finder_marker called: mx=0, my=0, module_size=5.471698113207547, border_style=square, center_style=square, border_color=#000000, center_color=#000000, offset=(20, 20)", "module": "qr_preview", "function": "_draw_finder_marker", "line": 97}
{"timestamp": "2025-06-12T17:25:37.562673", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Creating finder pattern group with id='marker-0-0'", "module": "qr_preview", "function": "_draw_finder_marker", "line": 110}
{"timestamp": "2025-06-12T17:25:37.562712", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Drawing square border:", "module": "qr_preview", "function": "_draw_finder_marker", "line": 160}
{"timestamp": "2025-06-12T17:25:37.562750", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "  Outer: insert=(20.0, 20.0), size=(38.301886792452834, 38.301886792452834)", "module": "qr_preview", "function": "_draw_finder_marker", "line": 161}
{"timestamp": "2025-06-12T17:25:37.562786", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "  Inner: insert=(26.566037735849058, 26.566037735849058), size=(25.169811320754715, 25.169811320754715)", "module": "qr_preview", "function": "_draw_finder_marker", "line": 162}
{"timestamp": "2025-06-12T17:25:37.563124", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Drawing marker center: style=square, color=#000000, pos=(33.67924528301887, 33.67924528301887), size=10.943396226415095", "module": "qr_preview", "function": "_draw_marker_center", "line": 179}
{"timestamp": "2025-06-12T17:25:37.563266", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Successfully added finder pattern group to SVG", "module": "qr_preview", "function": "_draw_finder_marker", "line": 171}
{"timestamp": "2025-06-12T17:25:37.563314", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Successfully drew finder pattern 1/3", "module": "qr_preview", "function": "generate_preview_svg_modular", "line": 782}
{"timestamp": "2025-06-12T17:25:37.563396", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Drawing finder pattern 2/3 at position (46, 0)", "module": "qr_preview", "function": "generate_preview_svg_modular", "line": 780}
{"timestamp": "2025-06-12T17:25:37.563444", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "_draw_finder_marker called: mx=46, my=0, module_size=5.471698113207547, border_style=square, center_style=square, border_color=#000000, center_color=#000000, offset=(20, 20)", "module": "qr_preview", "function": "_draw_finder_marker", "line": 97}
{"timestamp": "2025-06-12T17:25:37.563496", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Creating finder pattern group with id='marker-46-0'", "module": "qr_preview", "function": "_draw_finder_marker", "line": 110}
{"timestamp": "2025-06-12T17:25:37.563534", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Drawing square border:", "module": "qr_preview", "function": "_draw_finder_marker", "line": 160}
{"timestamp": "2025-06-12T17:25:37.563582", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "  Outer: insert=(271.6981132075472, 20.0), size=(38.301886792452834, 38.301886792452834)", "module": "qr_preview", "function": "_draw_finder_marker", "line": 161}
{"timestamp": "2025-06-12T17:25:37.563620", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "  Inner: insert=(278.26415094339626, 26.566037735849058), size=(25.169811320754715, 25.169811320754715)", "module": "qr_preview", "function": "_draw_finder_marker", "line": 162}
{"timestamp": "2025-06-12T17:25:37.563690", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Drawing marker center: style=square, color=#000000, pos=(285.37735849056605, 33.67924528301887), size=10.943396226415095", "module": "qr_preview", "function": "_draw_marker_center", "line": 179}
{"timestamp": "2025-06-12T17:25:37.563744", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Successfully added finder pattern group to SVG", "module": "qr_preview", "function": "_draw_finder_marker", "line": 171}
{"timestamp": "2025-06-12T17:25:37.563792", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Successfully drew finder pattern 2/3", "module": "qr_preview", "function": "generate_preview_svg_modular", "line": 782}
{"timestamp": "2025-06-12T17:25:37.563827", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Drawing finder pattern 3/3 at position (0, 46)", "module": "qr_preview", "function": "generate_preview_svg_modular", "line": 780}
{"timestamp": "2025-06-12T17:25:37.563862", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "_draw_finder_marker called: mx=0, my=46, module_size=5.471698113207547, border_style=square, center_style=square, border_color=#000000, center_color=#000000, offset=(20, 20)", "module": "qr_preview", "function": "_draw_finder_marker", "line": 97}
{"timestamp": "2025-06-12T17:25:37.563907", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Creating finder pattern group with id='marker-0-46'", "module": "qr_preview", "function": "_draw_finder_marker", "line": 110}
{"timestamp": "2025-06-12T17:25:37.563944", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Drawing square border:", "module": "qr_preview", "function": "_draw_finder_marker", "line": 160}
{"timestamp": "2025-06-12T17:25:37.563983", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "  Outer: insert=(20.0, 271.6981132075472), size=(38.301886792452834, 38.301886792452834)", "module": "qr_preview", "function": "_draw_finder_marker", "line": 161}
{"timestamp": "2025-06-12T17:25:37.564022", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "  Inner: insert=(26.566037735849058, 278.26415094339626), size=(25.169811320754715, 25.169811320754715)", "module": "qr_preview", "function": "_draw_finder_marker", "line": 162}
{"timestamp": "2025-06-12T17:25:37.564117", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Drawing marker center: style=square, color=#000000, pos=(33.67924528301887, 285.37735849056605), size=10.943396226415095", "module": "qr_preview", "function": "_draw_marker_center", "line": 179}
{"timestamp": "2025-06-12T17:25:37.564215", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Successfully added finder pattern group to SVG", "module": "qr_preview", "function": "_draw_finder_marker", "line": 171}
{"timestamp": "2025-06-12T17:25:37.564311", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Successfully drew finder pattern 3/3", "module": "qr_preview", "function": "generate_preview_svg_modular", "line": 782}
{"timestamp": "2025-06-12T17:25:37.590228", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Generated SVG length: 201904", "module": "qr_preview", "function": "generate_preview_svg_modular", "line": 802}
{"timestamp": "2025-06-12T17:25:37.590374", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "SVG contains marker-0-0: True", "module": "qr_preview", "function": "generate_preview_svg_modular", "line": 803}
{"timestamp": "2025-06-12T17:25:37.590492", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "SVG contains marker-18-0: False", "module": "qr_preview", "function": "generate_preview_svg_modular", "line": 804}
{"timestamp": "2025-06-12T17:25:37.590684", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "SVG contains marker-0-18: False", "module": "qr_preview", "function": "generate_preview_svg_modular", "line": 805}
{"timestamp": "2025-06-12T17:25:37.591030", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "SVG marker counts - outer: 3, inner: 3, dot: 0", "module": "qr_preview", "function": "generate_preview_svg_modular", "line": 813}
{"timestamp": "2025-06-12T17:25:37.591068", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "SVG label counts - text: 0, background: 0", "module": "qr_preview", "function": "generate_preview_svg_modular", "line": 814}
{"timestamp": "2025-06-12T17:25:37.594010", "level": "DEBUG", "logger": "backend.main", "message": "[Auth] Enforced Bearer token authentication for request", "module": "main", "function": "bearer_token_auth_middleware", "line": 274}
{"timestamp": "2025-06-12T17:25:38.453035", "level": "DEBUG", "logger": "backend.core.security.jwt_keys", "message": "Token contains key ID: a69f821a-65e8-4292-8feb-985dc7ac94d9", "module": "jwt_keys", "function": "decode_token", "line": 188}
{"timestamp": "2025-06-12T17:25:38.455229", "level": "DEBUG", "logger": "backend.core.security.jwt_keys", "message": "Using key ID a69f821a-65e8-4292-8feb-985dc7ac94d9 for verification", "module": "jwt_keys", "function": "decode_token", "line": 195}
{"timestamp": "2025-06-12T17:25:38.455944", "level": "DEBUG", "logger": "backend.services.auth", "message": "JWT decoded successfully with key manager", "module": "auth", "function": "authenticate_token", "line": 683}
{"timestamp": "2025-06-12T17:25:38.461940", "level": "DEBUG", "logger": "backend.services.db_utils", "message": "Query 'auth_lookup_e4ae2d8f-b32f-52d7-99cd-8a33b19652c7' completed in 0.004s", "module": "db_utils", "function": "execute_query_with_timeout", "line": 68}
{"timestamp": "2025-06-12T17:25:38.462008", "level": "DEBUG", "logger": "backend.services.db_utils", "message": "Auth query 'auth_lookup_e4ae2d8f-b32f-52d7-99cd-8a33b19652c7' successful, result format preserved", "module": "db_utils", "function": "execute_query_with_timeout", "line": 72}
{"timestamp": "2025-06-12T17:25:38.474511", "level": "DEBUG", "logger": "backend.services.auth", "message": "Authentication attempt with token prefix: eyJhbGciOi...", "module": "auth", "function": "get_current_user", "line": 727}
{"timestamp": "2025-06-12T17:25:38.474651", "level": "DEBUG", "logger": "backend.core.security.jwt_keys", "message": "Token contains key ID: a69f821a-65e8-4292-8feb-985dc7ac94d9", "module": "jwt_keys", "function": "decode_token", "line": 188}
{"timestamp": "2025-06-12T17:25:38.474827", "level": "DEBUG", "logger": "backend.core.security.jwt_keys", "message": "Using key ID a69f821a-65e8-4292-8feb-985dc7ac94d9 for verification", "module": "jwt_keys", "function": "decode_token", "line": 195}
{"timestamp": "2025-06-12T17:25:38.475091", "level": "DEBUG", "logger": "backend.services.auth", "message": "JWT decoded successfully with key manager", "module": "auth", "function": "authenticate_token", "line": 683}
{"timestamp": "2025-06-12T17:25:38.477873", "level": "DEBUG", "logger": "backend.services.db_utils", "message": "Query 'auth_lookup_e4ae2d8f-b32f-52d7-99cd-8a33b19652c7' completed in 0.002s", "module": "db_utils", "function": "execute_query_with_timeout", "line": 68}
{"timestamp": "2025-06-12T17:25:38.478001", "level": "DEBUG", "logger": "backend.services.db_utils", "message": "Auth query 'auth_lookup_e4ae2d8f-b32f-52d7-99cd-8a33b19652c7' successful, result format preserved", "module": "db_utils", "function": "execute_query_with_timeout", "line": 72}
{"timestamp": "2025-06-12T17:25:38.478102", "level": "DEBUG", "logger": "backend.services.auth", "message": "Successfully authenticated user: <EMAIL>", "module": "auth", "function": "get_current_user", "line": 732}
{"timestamp": "2025-06-12T17:25:38.478156", "level": "DEBUG", "logger": "backend.services.auth", "message": "Successfully authenticated user: <EMAIL>", "module": "auth", "function": "get_current_user", "line": 738}
{"timestamp": "2025-06-12T17:25:38.480508", "level": "DEBUG", "logger": "backend.routes.qr_preview", "message": "Received QR preview SVG request: {\"qr_type\": \"scooter_unlock\", \"data\": {\"scooter_id\": \"SCOOT123\", \"unlock_code\": \"UNLOCK42\", \"title\": \"Scooter_unlock QR Code\", \"description\": \"Scan to view scooter_unlock content\", \"service_provider\": \"Lime\"}, \"designOptions\": {\"size\": 256, \"margin\": 10, \"pattern\": \"square\", \"marker_border_style\": \"square\", \"marker_border_color\": \"#000000\", \"marker_center_style\": \"square\", \"marker_center_color\": \"#000000\", \"logo_size\": 0.3, \"logo_position\": \"center\", \"logo_opacity\": 1, \"frame_style\": \"none\", \"fr", "module": "qr_preview", "function": "preview_qr_svg", "line": 242}
{"timestamp": "2025-06-12T17:25:38.481097", "level": "DEBUG", "logger": "backend.routes.qr_preview", "message": "Form data keys: ['request']", "module": "qr_preview", "function": "preview_qr_svg", "line": 297}
{"timestamp": "2025-06-12T17:25:38.481169", "level": "DEBUG", "logger": "backend.routes.qr_preview", "message": "No logo key found in form data", "module": "qr_preview", "function": "preview_qr_svg", "line": 309}
{"timestamp": "2025-06-12T17:25:38.481278", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Formatting content for QR type: qrtype.scooter_unlock, content keys: ['scooter_id', 'unlock_code', 'title', 'description', 'service_provider']", "module": "qr_preview", "function": "_format_qr_data", "line": 876}
{"timestamp": "2025-06-12T17:25:38.501759", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Logo remove background setting: False, has logo: False", "module": "qr_preview", "function": "generate_preview_svg_modular", "line": 727}
{"timestamp": "2025-06-12T17:25:38.518149", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Drawing 3 finder patterns at positions: [(0, 0), (46, 0), (0, 46)]", "module": "qr_preview", "function": "generate_preview_svg_modular", "line": 775}
{"timestamp": "2025-06-12T17:25:38.518214", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "QR size: 53, border_style: square, center_style: square", "module": "qr_preview", "function": "generate_preview_svg_modular", "line": 776}
{"timestamp": "2025-06-12T17:25:38.518254", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Drawing finder pattern 1/3 at position (0, 0)", "module": "qr_preview", "function": "generate_preview_svg_modular", "line": 780}
{"timestamp": "2025-06-12T17:25:38.518349", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "_draw_finder_marker called: mx=0, my=0, module_size=5.471698113207547, border_style=square, center_style=square, border_color=#000000, center_color=#000000, offset=(20, 20)", "module": "qr_preview", "function": "_draw_finder_marker", "line": 97}
{"timestamp": "2025-06-12T17:25:38.518401", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Creating finder pattern group with id='marker-0-0'", "module": "qr_preview", "function": "_draw_finder_marker", "line": 110}
{"timestamp": "2025-06-12T17:25:38.518434", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Drawing square border:", "module": "qr_preview", "function": "_draw_finder_marker", "line": 160}
{"timestamp": "2025-06-12T17:25:38.518466", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "  Outer: insert=(20.0, 20.0), size=(38.301886792452834, 38.301886792452834)", "module": "qr_preview", "function": "_draw_finder_marker", "line": 161}
{"timestamp": "2025-06-12T17:25:38.518548", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "  Inner: insert=(26.566037735849058, 26.566037735849058), size=(25.169811320754715, 25.169811320754715)", "module": "qr_preview", "function": "_draw_finder_marker", "line": 162}
{"timestamp": "2025-06-12T17:25:38.518646", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Drawing marker center: style=square, color=#000000, pos=(33.67924528301887, 33.67924528301887), size=10.943396226415095", "module": "qr_preview", "function": "_draw_marker_center", "line": 179}
{"timestamp": "2025-06-12T17:25:38.518712", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Successfully added finder pattern group to SVG", "module": "qr_preview", "function": "_draw_finder_marker", "line": 171}
{"timestamp": "2025-06-12T17:25:38.518752", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Successfully drew finder pattern 1/3", "module": "qr_preview", "function": "generate_preview_svg_modular", "line": 782}
{"timestamp": "2025-06-12T17:25:38.518795", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Drawing finder pattern 2/3 at position (46, 0)", "module": "qr_preview", "function": "generate_preview_svg_modular", "line": 780}
{"timestamp": "2025-06-12T17:25:38.518842", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "_draw_finder_marker called: mx=46, my=0, module_size=5.471698113207547, border_style=square, center_style=square, border_color=#000000, center_color=#000000, offset=(20, 20)", "module": "qr_preview", "function": "_draw_finder_marker", "line": 97}
{"timestamp": "2025-06-12T17:25:38.518884", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Creating finder pattern group with id='marker-46-0'", "module": "qr_preview", "function": "_draw_finder_marker", "line": 110}
{"timestamp": "2025-06-12T17:25:38.518916", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Drawing square border:", "module": "qr_preview", "function": "_draw_finder_marker", "line": 160}
{"timestamp": "2025-06-12T17:25:38.518954", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "  Outer: insert=(271.6981132075472, 20.0), size=(38.301886792452834, 38.301886792452834)", "module": "qr_preview", "function": "_draw_finder_marker", "line": 161}
{"timestamp": "2025-06-12T17:25:38.518992", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "  Inner: insert=(278.26415094339626, 26.566037735849058), size=(25.169811320754715, 25.169811320754715)", "module": "qr_preview", "function": "_draw_finder_marker", "line": 162}
{"timestamp": "2025-06-12T17:25:38.519052", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Drawing marker center: style=square, color=#000000, pos=(285.37735849056605, 33.67924528301887), size=10.943396226415095", "module": "qr_preview", "function": "_draw_marker_center", "line": 179}
{"timestamp": "2025-06-12T17:25:38.519152", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Successfully added finder pattern group to SVG", "module": "qr_preview", "function": "_draw_finder_marker", "line": 171}
{"timestamp": "2025-06-12T17:25:38.519187", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Successfully drew finder pattern 2/3", "module": "qr_preview", "function": "generate_preview_svg_modular", "line": 782}
{"timestamp": "2025-06-12T17:25:38.519219", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Drawing finder pattern 3/3 at position (0, 46)", "module": "qr_preview", "function": "generate_preview_svg_modular", "line": 780}
{"timestamp": "2025-06-12T17:25:38.519252", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "_draw_finder_marker called: mx=0, my=46, module_size=5.471698113207547, border_style=square, center_style=square, border_color=#000000, center_color=#000000, offset=(20, 20)", "module": "qr_preview", "function": "_draw_finder_marker", "line": 97}
{"timestamp": "2025-06-12T17:25:38.519379", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Creating finder pattern group with id='marker-0-46'", "module": "qr_preview", "function": "_draw_finder_marker", "line": 110}
{"timestamp": "2025-06-12T17:25:38.519465", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Drawing square border:", "module": "qr_preview", "function": "_draw_finder_marker", "line": 160}
{"timestamp": "2025-06-12T17:25:38.519513", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "  Outer: insert=(20.0, 271.6981132075472), size=(38.301886792452834, 38.301886792452834)", "module": "qr_preview", "function": "_draw_finder_marker", "line": 161}
{"timestamp": "2025-06-12T17:25:38.519578", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "  Inner: insert=(26.566037735849058, 278.26415094339626), size=(25.169811320754715, 25.169811320754715)", "module": "qr_preview", "function": "_draw_finder_marker", "line": 162}
{"timestamp": "2025-06-12T17:25:38.519703", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Drawing marker center: style=square, color=#000000, pos=(33.67924528301887, 285.37735849056605), size=10.943396226415095", "module": "qr_preview", "function": "_draw_marker_center", "line": 179}
{"timestamp": "2025-06-12T17:25:38.519764", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Successfully added finder pattern group to SVG", "module": "qr_preview", "function": "_draw_finder_marker", "line": 171}
{"timestamp": "2025-06-12T17:25:38.519846", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Successfully drew finder pattern 3/3", "module": "qr_preview", "function": "generate_preview_svg_modular", "line": 782}
{"timestamp": "2025-06-12T17:25:38.545123", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Generated SVG length: 201904", "module": "qr_preview", "function": "generate_preview_svg_modular", "line": 802}
{"timestamp": "2025-06-12T17:25:38.545266", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "SVG contains marker-0-0: True", "module": "qr_preview", "function": "generate_preview_svg_modular", "line": 803}
{"timestamp": "2025-06-12T17:25:38.545379", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "SVG contains marker-18-0: False", "module": "qr_preview", "function": "generate_preview_svg_modular", "line": 804}
{"timestamp": "2025-06-12T17:25:38.545489", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "SVG contains marker-0-18: False", "module": "qr_preview", "function": "generate_preview_svg_modular", "line": 805}
{"timestamp": "2025-06-12T17:25:38.545819", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "SVG marker counts - outer: 3, inner: 3, dot: 0", "module": "qr_preview", "function": "generate_preview_svg_modular", "line": 813}
{"timestamp": "2025-06-12T17:25:38.545854", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "SVG label counts - text: 0, background: 0", "module": "qr_preview", "function": "generate_preview_svg_modular", "line": 814}
{"timestamp": "2025-06-12T17:25:38.548184", "level": "DEBUG", "logger": "backend.main", "message": "[Auth] Enforced Bearer token authentication for request", "module": "main", "function": "bearer_token_auth_middleware", "line": 274}
{"timestamp": "2025-06-12T17:25:41.394543", "level": "DEBUG", "logger": "backend.core.security.jwt_keys", "message": "Token contains key ID: a69f821a-65e8-4292-8feb-985dc7ac94d9", "module": "jwt_keys", "function": "decode_token", "line": 188}
{"timestamp": "2025-06-12T17:25:41.394639", "level": "DEBUG", "logger": "backend.core.security.jwt_keys", "message": "Using key ID a69f821a-65e8-4292-8feb-985dc7ac94d9 for verification", "module": "jwt_keys", "function": "decode_token", "line": 195}
{"timestamp": "2025-06-12T17:25:41.395189", "level": "DEBUG", "logger": "backend.services.auth", "message": "JWT decoded successfully with key manager", "module": "auth", "function": "authenticate_token", "line": 683}
{"timestamp": "2025-06-12T17:25:41.403701", "level": "DEBUG", "logger": "backend.services.db_utils", "message": "Query 'auth_lookup_e4ae2d8f-b32f-52d7-99cd-8a33b19652c7' completed in 0.007s", "module": "db_utils", "function": "execute_query_with_timeout", "line": 68}
{"timestamp": "2025-06-12T17:25:41.403769", "level": "DEBUG", "logger": "backend.services.db_utils", "message": "Auth query 'auth_lookup_e4ae2d8f-b32f-52d7-99cd-8a33b19652c7' successful, result format preserved", "module": "db_utils", "function": "execute_query_with_timeout", "line": 72}
{"timestamp": "2025-06-12T17:25:41.409412", "level": "DEBUG", "logger": "backend.services.auth", "message": "Authentication attempt with token prefix: eyJhbGciOi...", "module": "auth", "function": "get_current_user", "line": 727}
{"timestamp": "2025-06-12T17:25:41.409523", "level": "DEBUG", "logger": "backend.core.security.jwt_keys", "message": "Token contains key ID: a69f821a-65e8-4292-8feb-985dc7ac94d9", "module": "jwt_keys", "function": "decode_token", "line": 188}
{"timestamp": "2025-06-12T17:25:41.409564", "level": "DEBUG", "logger": "backend.core.security.jwt_keys", "message": "Using key ID a69f821a-65e8-4292-8feb-985dc7ac94d9 for verification", "module": "jwt_keys", "function": "decode_token", "line": 195}
{"timestamp": "2025-06-12T17:25:41.409704", "level": "DEBUG", "logger": "backend.services.auth", "message": "JWT decoded successfully with key manager", "module": "auth", "function": "authenticate_token", "line": 683}
{"timestamp": "2025-06-12T17:25:41.411048", "level": "DEBUG", "logger": "backend.services.db_utils", "message": "Query 'auth_lookup_e4ae2d8f-b32f-52d7-99cd-8a33b19652c7' completed in 0.001s", "module": "db_utils", "function": "execute_query_with_timeout", "line": 68}
{"timestamp": "2025-06-12T17:25:41.411087", "level": "DEBUG", "logger": "backend.services.db_utils", "message": "Auth query 'auth_lookup_e4ae2d8f-b32f-52d7-99cd-8a33b19652c7' successful, result format preserved", "module": "db_utils", "function": "execute_query_with_timeout", "line": 72}
{"timestamp": "2025-06-12T17:25:41.411142", "level": "DEBUG", "logger": "backend.services.auth", "message": "Successfully authenticated user: <EMAIL>", "module": "auth", "function": "get_current_user", "line": 732}
{"timestamp": "2025-06-12T17:25:41.411180", "level": "DEBUG", "logger": "backend.services.auth", "message": "Successfully authenticated user: <EMAIL>", "module": "auth", "function": "get_current_user", "line": 738}
{"timestamp": "2025-06-12T17:25:41.414451", "level": "DEBUG", "logger": "backend.routes.qr_preview", "message": "Received QR preview SVG request: {\"qr_type\": \"scooter_unlock\", \"data\": {\"scooter_id\": \"SCOOT123\", \"unlock_code\": \"UNLOCK42\", \"title\": \"Scooter_unlock QR Code\", \"description\": \"Scan to view scooter_unlock content\", \"service_provider\": \"Lime\", \"rate_info\": \"$\"}, \"designOptions\": {\"size\": 256, \"margin\": 10, \"pattern\": \"square\", \"marker_border_style\": \"square\", \"marker_center_style\": \"square\", \"logo_size\": 0.3, \"logo_position\": \"center\", \"logo_opacity\": 1, \"logo_remove_background\": false, \"frame_style\": \"none\", \"frame_font\": \"SF Pr", "module": "qr_preview", "function": "preview_qr_svg", "line": 242}
{"timestamp": "2025-06-12T17:25:41.415214", "level": "DEBUG", "logger": "backend.routes.qr_preview", "message": "Form data keys: ['request']", "module": "qr_preview", "function": "preview_qr_svg", "line": 297}
{"timestamp": "2025-06-12T17:25:41.415256", "level": "DEBUG", "logger": "backend.routes.qr_preview", "message": "No logo key found in form data", "module": "qr_preview", "function": "preview_qr_svg", "line": 309}
{"timestamp": "2025-06-12T17:25:41.415379", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Formatting content for QR type: qrtype.scooter_unlock, content keys: ['scooter_id', 'unlock_code', 'title', 'description', 'service_provider', 'rate_info']", "module": "qr_preview", "function": "_format_qr_data", "line": 876}
{"timestamp": "2025-06-12T17:25:41.438439", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Logo remove background setting: False, has logo: False", "module": "qr_preview", "function": "generate_preview_svg_modular", "line": 727}
{"timestamp": "2025-06-12T17:25:41.457361", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Drawing 3 finder patterns at positions: [(0, 0), (50, 0), (0, 50)]", "module": "qr_preview", "function": "generate_preview_svg_modular", "line": 775}
{"timestamp": "2025-06-12T17:25:41.457430", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "QR size: 57, border_style: square, center_style: square", "module": "qr_preview", "function": "generate_preview_svg_modular", "line": 776}
{"timestamp": "2025-06-12T17:25:41.457471", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Drawing finder pattern 1/3 at position (0, 0)", "module": "qr_preview", "function": "generate_preview_svg_modular", "line": 780}
{"timestamp": "2025-06-12T17:25:41.457527", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "_draw_finder_marker called: mx=0, my=0, module_size=5.087719298245614, border_style=square, center_style=square, border_color=#000000, center_color=#000000, offset=(20, 20)", "module": "qr_preview", "function": "_draw_finder_marker", "line": 97}
{"timestamp": "2025-06-12T17:25:41.457589", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Creating finder pattern group with id='marker-0-0'", "module": "qr_preview", "function": "_draw_finder_marker", "line": 110}
{"timestamp": "2025-06-12T17:25:41.457626", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Drawing square border:", "module": "qr_preview", "function": "_draw_finder_marker", "line": 160}
{"timestamp": "2025-06-12T17:25:41.457663", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "  Outer: insert=(20.0, 20.0), size=(35.614035087719294, 35.614035087719294)", "module": "qr_preview", "function": "_draw_finder_marker", "line": 161}
{"timestamp": "2025-06-12T17:25:41.457699", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "  Inner: insert=(26.105263157894736, 26.105263157894736), size=(23.403508771929822, 23.403508771929822)", "module": "qr_preview", "function": "_draw_finder_marker", "line": 162}
{"timestamp": "2025-06-12T17:25:41.457776", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Drawing marker center: style=square, color=#000000, pos=(32.719298245614034, 32.719298245614034), size=10.175438596491228", "module": "qr_preview", "function": "_draw_marker_center", "line": 179}
{"timestamp": "2025-06-12T17:25:41.457849", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Successfully added finder pattern group to SVG", "module": "qr_preview", "function": "_draw_finder_marker", "line": 171}
{"timestamp": "2025-06-12T17:25:41.457886", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Successfully drew finder pattern 1/3", "module": "qr_preview", "function": "generate_preview_svg_modular", "line": 782}
{"timestamp": "2025-06-12T17:25:41.457962", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Drawing finder pattern 2/3 at position (50, 0)", "module": "qr_preview", "function": "generate_preview_svg_modular", "line": 780}
{"timestamp": "2025-06-12T17:25:41.457997", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "_draw_finder_marker called: mx=50, my=0, module_size=5.087719298245614, border_style=square, center_style=square, border_color=#000000, center_color=#000000, offset=(20, 20)", "module": "qr_preview", "function": "_draw_finder_marker", "line": 97}
{"timestamp": "2025-06-12T17:25:41.458037", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Creating finder pattern group with id='marker-50-0'", "module": "qr_preview", "function": "_draw_finder_marker", "line": 110}
{"timestamp": "2025-06-12T17:25:41.458069", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Drawing square border:", "module": "qr_preview", "function": "_draw_finder_marker", "line": 160}
{"timestamp": "2025-06-12T17:25:41.458103", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "  Outer: insert=(274.38596491228066, 20.0), size=(35.614035087719294, 35.614035087719294)", "module": "qr_preview", "function": "_draw_finder_marker", "line": 161}
{"timestamp": "2025-06-12T17:25:41.458138", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "  Inner: insert=(280.4912280701754, 26.105263157894736), size=(23.403508771929822, 23.403508771929822)", "module": "qr_preview", "function": "_draw_finder_marker", "line": 162}
{"timestamp": "2025-06-12T17:25:41.458195", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Drawing marker center: style=square, color=#000000, pos=(287.10526315789474, 32.719298245614034), size=10.175438596491228", "module": "qr_preview", "function": "_draw_marker_center", "line": 179}
{"timestamp": "2025-06-12T17:25:41.458241", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Successfully added finder pattern group to SVG", "module": "qr_preview", "function": "_draw_finder_marker", "line": 171}
{"timestamp": "2025-06-12T17:25:41.458275", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Successfully drew finder pattern 2/3", "module": "qr_preview", "function": "generate_preview_svg_modular", "line": 782}
{"timestamp": "2025-06-12T17:25:41.458307", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Drawing finder pattern 3/3 at position (0, 50)", "module": "qr_preview", "function": "generate_preview_svg_modular", "line": 780}
{"timestamp": "2025-06-12T17:25:41.458341", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "_draw_finder_marker called: mx=0, my=50, module_size=5.087719298245614, border_style=square, center_style=square, border_color=#000000, center_color=#000000, offset=(20, 20)", "module": "qr_preview", "function": "_draw_finder_marker", "line": 97}
{"timestamp": "2025-06-12T17:25:41.458382", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Creating finder pattern group with id='marker-0-50'", "module": "qr_preview", "function": "_draw_finder_marker", "line": 110}
{"timestamp": "2025-06-12T17:25:41.458414", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Drawing square border:", "module": "qr_preview", "function": "_draw_finder_marker", "line": 160}
{"timestamp": "2025-06-12T17:25:41.458449", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "  Outer: insert=(20.0, 274.38596491228066), size=(35.614035087719294, 35.614035087719294)", "module": "qr_preview", "function": "_draw_finder_marker", "line": 161}
{"timestamp": "2025-06-12T17:25:41.458483", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "  Inner: insert=(26.105263157894736, 280.4912280701754), size=(23.403508771929822, 23.403508771929822)", "module": "qr_preview", "function": "_draw_finder_marker", "line": 162}
{"timestamp": "2025-06-12T17:25:41.458568", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Drawing marker center: style=square, color=#000000, pos=(32.719298245614034, 287.10526315789474), size=10.175438596491228", "module": "qr_preview", "function": "_draw_marker_center", "line": 179}
{"timestamp": "2025-06-12T17:25:41.458616", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Successfully added finder pattern group to SVG", "module": "qr_preview", "function": "_draw_finder_marker", "line": 171}
{"timestamp": "2025-06-12T17:25:41.458649", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Successfully drew finder pattern 3/3", "module": "qr_preview", "function": "generate_preview_svg_modular", "line": 782}
{"timestamp": "2025-06-12T17:25:41.485315", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Generated SVG length: 243761", "module": "qr_preview", "function": "generate_preview_svg_modular", "line": 802}
{"timestamp": "2025-06-12T17:25:41.485462", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "SVG contains marker-0-0: True", "module": "qr_preview", "function": "generate_preview_svg_modular", "line": 803}
{"timestamp": "2025-06-12T17:25:41.485595", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "SVG contains marker-18-0: False", "module": "qr_preview", "function": "generate_preview_svg_modular", "line": 804}
{"timestamp": "2025-06-12T17:25:41.485729", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "SVG contains marker-0-18: False", "module": "qr_preview", "function": "generate_preview_svg_modular", "line": 805}
{"timestamp": "2025-06-12T17:25:41.486194", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "SVG marker counts - outer: 3, inner: 3, dot: 0", "module": "qr_preview", "function": "generate_preview_svg_modular", "line": 813}
{"timestamp": "2025-06-12T17:25:41.486258", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "SVG label counts - text: 0, background: 0", "module": "qr_preview", "function": "generate_preview_svg_modular", "line": 814}
{"timestamp": "2025-06-12T17:25:41.488942", "level": "DEBUG", "logger": "backend.main", "message": "[Auth] Enforced Bearer token authentication for request", "module": "main", "function": "bearer_token_auth_middleware", "line": 274}
{"timestamp": "2025-06-12T17:25:42.327891", "level": "DEBUG", "logger": "backend.core.security.jwt_keys", "message": "Token contains key ID: a69f821a-65e8-4292-8feb-985dc7ac94d9", "module": "jwt_keys", "function": "decode_token", "line": 188}
{"timestamp": "2025-06-12T17:25:42.327965", "level": "DEBUG", "logger": "backend.core.security.jwt_keys", "message": "Using key ID a69f821a-65e8-4292-8feb-985dc7ac94d9 for verification", "module": "jwt_keys", "function": "decode_token", "line": 195}
{"timestamp": "2025-06-12T17:25:42.328367", "level": "DEBUG", "logger": "backend.services.auth", "message": "JWT decoded successfully with key manager", "module": "auth", "function": "authenticate_token", "line": 683}
{"timestamp": "2025-06-12T17:25:42.329695", "level": "DEBUG", "logger": "backend.services.db_utils", "message": "Query 'auth_lookup_e4ae2d8f-b32f-52d7-99cd-8a33b19652c7' completed in 0.001s", "module": "db_utils", "function": "execute_query_with_timeout", "line": 68}
{"timestamp": "2025-06-12T17:25:42.329747", "level": "DEBUG", "logger": "backend.services.db_utils", "message": "Auth query 'auth_lookup_e4ae2d8f-b32f-52d7-99cd-8a33b19652c7' successful, result format preserved", "module": "db_utils", "function": "execute_query_with_timeout", "line": 72}
{"timestamp": "2025-06-12T17:25:42.336853", "level": "DEBUG", "logger": "backend.services.auth", "message": "Authentication attempt with token prefix: eyJhbGciOi...", "module": "auth", "function": "get_current_user", "line": 727}
{"timestamp": "2025-06-12T17:25:42.336970", "level": "DEBUG", "logger": "backend.core.security.jwt_keys", "message": "Token contains key ID: a69f821a-65e8-4292-8feb-985dc7ac94d9", "module": "jwt_keys", "function": "decode_token", "line": 188}
{"timestamp": "2025-06-12T17:25:42.337057", "level": "DEBUG", "logger": "backend.core.security.jwt_keys", "message": "Using key ID a69f821a-65e8-4292-8feb-985dc7ac94d9 for verification", "module": "jwt_keys", "function": "decode_token", "line": 195}
{"timestamp": "2025-06-12T17:25:42.337186", "level": "DEBUG", "logger": "backend.services.auth", "message": "JWT decoded successfully with key manager", "module": "auth", "function": "authenticate_token", "line": 683}
{"timestamp": "2025-06-12T17:25:42.339738", "level": "DEBUG", "logger": "backend.services.db_utils", "message": "Query 'auth_lookup_e4ae2d8f-b32f-52d7-99cd-8a33b19652c7' completed in 0.002s", "module": "db_utils", "function": "execute_query_with_timeout", "line": 68}
{"timestamp": "2025-06-12T17:25:42.339807", "level": "DEBUG", "logger": "backend.services.db_utils", "message": "Auth query 'auth_lookup_e4ae2d8f-b32f-52d7-99cd-8a33b19652c7' successful, result format preserved", "module": "db_utils", "function": "execute_query_with_timeout", "line": 72}
{"timestamp": "2025-06-12T17:25:42.339880", "level": "DEBUG", "logger": "backend.services.auth", "message": "Successfully authenticated user: <EMAIL>", "module": "auth", "function": "get_current_user", "line": 732}
{"timestamp": "2025-06-12T17:25:42.339949", "level": "DEBUG", "logger": "backend.services.auth", "message": "Successfully authenticated user: <EMAIL>", "module": "auth", "function": "get_current_user", "line": 738}
{"timestamp": "2025-06-12T17:25:42.342401", "level": "DEBUG", "logger": "backend.routes.qr_preview", "message": "Received QR preview SVG request: {\"qr_type\": \"scooter_unlock\", \"data\": {\"scooter_id\": \"SCOOT123\", \"unlock_code\": \"UNLOCK42\", \"title\": \"Scooter_unlock QR Code\", \"description\": \"Scan to view scooter_unlock content\", \"service_provider\": \"Lime\", \"rate_info\": \"$\"}, \"designOptions\": {\"size\": 256, \"margin\": 10, \"pattern\": \"square\", \"marker_border_style\": \"square\", \"marker_border_color\": \"#000000\", \"marker_center_style\": \"square\", \"marker_center_color\": \"#000000\", \"logo_size\": 0.3, \"logo_position\": \"center\", \"logo_opacity\": 1, \"frame_s", "module": "qr_preview", "function": "preview_qr_svg", "line": 242}
{"timestamp": "2025-06-12T17:25:42.342515", "level": "DEBUG", "logger": "backend.routes.qr_preview", "message": "Form data keys: ['request']", "module": "qr_preview", "function": "preview_qr_svg", "line": 297}
{"timestamp": "2025-06-12T17:25:42.342554", "level": "DEBUG", "logger": "backend.routes.qr_preview", "message": "No logo key found in form data", "module": "qr_preview", "function": "preview_qr_svg", "line": 309}
{"timestamp": "2025-06-12T17:25:42.342597", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Formatting content for QR type: qrtype.scooter_unlock, content keys: ['scooter_id', 'unlock_code', 'title', 'description', 'service_provider', 'rate_info']", "module": "qr_preview", "function": "_format_qr_data", "line": 876}
{"timestamp": "2025-06-12T17:25:42.365910", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Logo remove background setting: False, has logo: False", "module": "qr_preview", "function": "generate_preview_svg_modular", "line": 727}
{"timestamp": "2025-06-12T17:25:42.383999", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Drawing 3 finder patterns at positions: [(0, 0), (50, 0), (0, 50)]", "module": "qr_preview", "function": "generate_preview_svg_modular", "line": 775}
{"timestamp": "2025-06-12T17:25:42.384070", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "QR size: 57, border_style: square, center_style: square", "module": "qr_preview", "function": "generate_preview_svg_modular", "line": 776}
{"timestamp": "2025-06-12T17:25:42.384112", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Drawing finder pattern 1/3 at position (0, 0)", "module": "qr_preview", "function": "generate_preview_svg_modular", "line": 780}
{"timestamp": "2025-06-12T17:25:42.384510", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "_draw_finder_marker called: mx=0, my=0, module_size=5.087719298245614, border_style=square, center_style=square, border_color=#000000, center_color=#000000, offset=(20, 20)", "module": "qr_preview", "function": "_draw_finder_marker", "line": 97}
{"timestamp": "2025-06-12T17:25:42.384606", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Creating finder pattern group with id='marker-0-0'", "module": "qr_preview", "function": "_draw_finder_marker", "line": 110}
{"timestamp": "2025-06-12T17:25:42.384651", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Drawing square border:", "module": "qr_preview", "function": "_draw_finder_marker", "line": 160}
{"timestamp": "2025-06-12T17:25:42.384689", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "  Outer: insert=(20.0, 20.0), size=(35.614035087719294, 35.614035087719294)", "module": "qr_preview", "function": "_draw_finder_marker", "line": 161}
{"timestamp": "2025-06-12T17:25:42.384779", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "  Inner: insert=(26.105263157894736, 26.105263157894736), size=(23.403508771929822, 23.403508771929822)", "module": "qr_preview", "function": "_draw_finder_marker", "line": 162}
{"timestamp": "2025-06-12T17:25:42.384908", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Drawing marker center: style=square, color=#000000, pos=(32.719298245614034, 32.719298245614034), size=10.175438596491228", "module": "qr_preview", "function": "_draw_marker_center", "line": 179}
{"timestamp": "2025-06-12T17:25:42.384971", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Successfully added finder pattern group to SVG", "module": "qr_preview", "function": "_draw_finder_marker", "line": 171}
{"timestamp": "2025-06-12T17:25:42.385009", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Successfully drew finder pattern 1/3", "module": "qr_preview", "function": "generate_preview_svg_modular", "line": 782}
{"timestamp": "2025-06-12T17:25:42.385049", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Drawing finder pattern 2/3 at position (50, 0)", "module": "qr_preview", "function": "generate_preview_svg_modular", "line": 780}
{"timestamp": "2025-06-12T17:25:42.385094", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "_draw_finder_marker called: mx=50, my=0, module_size=5.087719298245614, border_style=square, center_style=square, border_color=#000000, center_color=#000000, offset=(20, 20)", "module": "qr_preview", "function": "_draw_finder_marker", "line": 97}
{"timestamp": "2025-06-12T17:25:42.385137", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Creating finder pattern group with id='marker-50-0'", "module": "qr_preview", "function": "_draw_finder_marker", "line": 110}
{"timestamp": "2025-06-12T17:25:42.385170", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Drawing square border:", "module": "qr_preview", "function": "_draw_finder_marker", "line": 160}
{"timestamp": "2025-06-12T17:25:42.385205", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "  Outer: insert=(274.38596491228066, 20.0), size=(35.614035087719294, 35.614035087719294)", "module": "qr_preview", "function": "_draw_finder_marker", "line": 161}
{"timestamp": "2025-06-12T17:25:42.385238", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "  Inner: insert=(280.4912280701754, 26.105263157894736), size=(23.403508771929822, 23.403508771929822)", "module": "qr_preview", "function": "_draw_finder_marker", "line": 162}
{"timestamp": "2025-06-12T17:25:42.385300", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Drawing marker center: style=square, color=#000000, pos=(287.10526315789474, 32.719298245614034), size=10.175438596491228", "module": "qr_preview", "function": "_draw_marker_center", "line": 179}
{"timestamp": "2025-06-12T17:25:42.385385", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Successfully added finder pattern group to SVG", "module": "qr_preview", "function": "_draw_finder_marker", "line": 171}
{"timestamp": "2025-06-12T17:25:42.385416", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Successfully drew finder pattern 2/3", "module": "qr_preview", "function": "generate_preview_svg_modular", "line": 782}
{"timestamp": "2025-06-12T17:25:42.385446", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Drawing finder pattern 3/3 at position (0, 50)", "module": "qr_preview", "function": "generate_preview_svg_modular", "line": 780}
{"timestamp": "2025-06-12T17:25:42.385479", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "_draw_finder_marker called: mx=0, my=50, module_size=5.087719298245614, border_style=square, center_style=square, border_color=#000000, center_color=#000000, offset=(20, 20)", "module": "qr_preview", "function": "_draw_finder_marker", "line": 97}
{"timestamp": "2025-06-12T17:25:42.385520", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Creating finder pattern group with id='marker-0-50'", "module": "qr_preview", "function": "_draw_finder_marker", "line": 110}
{"timestamp": "2025-06-12T17:25:42.385551", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Drawing square border:", "module": "qr_preview", "function": "_draw_finder_marker", "line": 160}
{"timestamp": "2025-06-12T17:25:42.385584", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "  Outer: insert=(20.0, 274.38596491228066), size=(35.614035087719294, 35.614035087719294)", "module": "qr_preview", "function": "_draw_finder_marker", "line": 161}
{"timestamp": "2025-06-12T17:25:42.385614", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "  Inner: insert=(26.105263157894736, 280.4912280701754), size=(23.403508771929822, 23.403508771929822)", "module": "qr_preview", "function": "_draw_finder_marker", "line": 162}
{"timestamp": "2025-06-12T17:25:42.385675", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Drawing marker center: style=square, color=#000000, pos=(32.719298245614034, 287.10526315789474), size=10.175438596491228", "module": "qr_preview", "function": "_draw_marker_center", "line": 179}
{"timestamp": "2025-06-12T17:25:42.385720", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Successfully added finder pattern group to SVG", "module": "qr_preview", "function": "_draw_finder_marker", "line": 171}
{"timestamp": "2025-06-12T17:25:42.385751", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Successfully drew finder pattern 3/3", "module": "qr_preview", "function": "generate_preview_svg_modular", "line": 782}
{"timestamp": "2025-06-12T17:25:42.413234", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Generated SVG length: 243761", "module": "qr_preview", "function": "generate_preview_svg_modular", "line": 802}
{"timestamp": "2025-06-12T17:25:42.413406", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "SVG contains marker-0-0: True", "module": "qr_preview", "function": "generate_preview_svg_modular", "line": 803}
{"timestamp": "2025-06-12T17:25:42.413540", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "SVG contains marker-18-0: False", "module": "qr_preview", "function": "generate_preview_svg_modular", "line": 804}
{"timestamp": "2025-06-12T17:25:42.413685", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "SVG contains marker-0-18: False", "module": "qr_preview", "function": "generate_preview_svg_modular", "line": 805}
{"timestamp": "2025-06-12T17:25:42.414135", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "SVG marker counts - outer: 3, inner: 3, dot: 0", "module": "qr_preview", "function": "generate_preview_svg_modular", "line": 813}
{"timestamp": "2025-06-12T17:25:42.414243", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "SVG label counts - text: 0, background: 0", "module": "qr_preview", "function": "generate_preview_svg_modular", "line": 814}
{"timestamp": "2025-06-12T17:25:42.417613", "level": "DEBUG", "logger": "backend.main", "message": "[Auth] Enforced Bearer token authentication for request", "module": "main", "function": "bearer_token_auth_middleware", "line": 274}
{"timestamp": "2025-06-12T17:25:44.439283", "level": "DEBUG", "logger": "backend.core.security.jwt_keys", "message": "Token contains key ID: a69f821a-65e8-4292-8feb-985dc7ac94d9", "module": "jwt_keys", "function": "decode_token", "line": 188}
{"timestamp": "2025-06-12T17:25:44.439433", "level": "DEBUG", "logger": "backend.core.security.jwt_keys", "message": "Using key ID a69f821a-65e8-4292-8feb-985dc7ac94d9 for verification", "module": "jwt_keys", "function": "decode_token", "line": 195}
{"timestamp": "2025-06-12T17:25:44.439780", "level": "DEBUG", "logger": "backend.services.auth", "message": "JWT decoded successfully with key manager", "module": "auth", "function": "authenticate_token", "line": 683}
{"timestamp": "2025-06-12T17:25:44.442498", "level": "DEBUG", "logger": "backend.services.db_utils", "message": "Query 'auth_lookup_e4ae2d8f-b32f-52d7-99cd-8a33b19652c7' completed in 0.002s", "module": "db_utils", "function": "execute_query_with_timeout", "line": 68}
{"timestamp": "2025-06-12T17:25:44.442551", "level": "DEBUG", "logger": "backend.services.db_utils", "message": "Auth query 'auth_lookup_e4ae2d8f-b32f-52d7-99cd-8a33b19652c7' successful, result format preserved", "module": "db_utils", "function": "execute_query_with_timeout", "line": 72}
{"timestamp": "2025-06-12T17:25:44.458307", "level": "DEBUG", "logger": "backend.services.auth", "message": "Authentication attempt with token prefix: eyJhbGciOi...", "module": "auth", "function": "get_current_user", "line": 727}
{"timestamp": "2025-06-12T17:25:44.458443", "level": "DEBUG", "logger": "backend.core.security.jwt_keys", "message": "Token contains key ID: a69f821a-65e8-4292-8feb-985dc7ac94d9", "module": "jwt_keys", "function": "decode_token", "line": 188}
{"timestamp": "2025-06-12T17:25:44.458488", "level": "DEBUG", "logger": "backend.core.security.jwt_keys", "message": "Using key ID a69f821a-65e8-4292-8feb-985dc7ac94d9 for verification", "module": "jwt_keys", "function": "decode_token", "line": 195}
{"timestamp": "2025-06-12T17:25:44.458615", "level": "DEBUG", "logger": "backend.services.auth", "message": "JWT decoded successfully with key manager", "module": "auth", "function": "authenticate_token", "line": 683}
{"timestamp": "2025-06-12T17:25:44.461781", "level": "DEBUG", "logger": "backend.services.db_utils", "message": "Query 'auth_lookup_e4ae2d8f-b32f-52d7-99cd-8a33b19652c7' completed in 0.003s", "module": "db_utils", "function": "execute_query_with_timeout", "line": 68}
{"timestamp": "2025-06-12T17:25:44.461839", "level": "DEBUG", "logger": "backend.services.db_utils", "message": "Auth query 'auth_lookup_e4ae2d8f-b32f-52d7-99cd-8a33b19652c7' successful, result format preserved", "module": "db_utils", "function": "execute_query_with_timeout", "line": 72}
{"timestamp": "2025-06-12T17:25:44.461896", "level": "DEBUG", "logger": "backend.services.auth", "message": "Successfully authenticated user: <EMAIL>", "module": "auth", "function": "get_current_user", "line": 732}
{"timestamp": "2025-06-12T17:25:44.461927", "level": "DEBUG", "logger": "backend.services.auth", "message": "Successfully authenticated user: <EMAIL>", "module": "auth", "function": "get_current_user", "line": 738}
{"timestamp": "2025-06-12T17:25:44.463652", "level": "DEBUG", "logger": "backend.routes.qr_preview", "message": "Received QR preview SVG request: {\"qr_type\": \"scooter_unlock\", \"data\": {\"scooter_id\": \"SCOOT123\", \"unlock_code\": \"UNLOCK42\", \"title\": \"Scooter_unlock QR Code\", \"description\": \"Scan to view scooter_unlock content\", \"service_provider\": \"Lime\", \"rate_info\": \"$20\"}, \"designOptions\": {\"size\": 256, \"margin\": 10, \"pattern\": \"square\", \"marker_border_style\": \"square\", \"marker_center_style\": \"square\", \"logo_size\": 0.3, \"logo_position\": \"center\", \"logo_opacity\": 1, \"logo_remove_background\": false, \"frame_style\": \"none\", \"frame_font\": \"SF ", "module": "qr_preview", "function": "preview_qr_svg", "line": 242}
{"timestamp": "2025-06-12T17:25:44.463794", "level": "DEBUG", "logger": "backend.routes.qr_preview", "message": "Form data keys: ['request']", "module": "qr_preview", "function": "preview_qr_svg", "line": 297}
{"timestamp": "2025-06-12T17:25:44.463831", "level": "DEBUG", "logger": "backend.routes.qr_preview", "message": "No logo key found in form data", "module": "qr_preview", "function": "preview_qr_svg", "line": 309}
{"timestamp": "2025-06-12T17:25:44.463873", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Formatting content for QR type: qrtype.scooter_unlock, content keys: ['scooter_id', 'unlock_code', 'title', 'description', 'service_provider', 'rate_info']", "module": "qr_preview", "function": "_format_qr_data", "line": 876}
{"timestamp": "2025-06-12T17:25:44.494278", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Logo remove background setting: False, has logo: False", "module": "qr_preview", "function": "generate_preview_svg_modular", "line": 727}
{"timestamp": "2025-06-12T17:25:44.514777", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Drawing 3 finder patterns at positions: [(0, 0), (50, 0), (0, 50)]", "module": "qr_preview", "function": "generate_preview_svg_modular", "line": 775}
{"timestamp": "2025-06-12T17:25:44.514856", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "QR size: 57, border_style: square, center_style: square", "module": "qr_preview", "function": "generate_preview_svg_modular", "line": 776}
{"timestamp": "2025-06-12T17:25:44.514900", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Drawing finder pattern 1/3 at position (0, 0)", "module": "qr_preview", "function": "generate_preview_svg_modular", "line": 780}
{"timestamp": "2025-06-12T17:25:44.514950", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "_draw_finder_marker called: mx=0, my=0, module_size=5.087719298245614, border_style=square, center_style=square, border_color=#000000, center_color=#000000, offset=(20, 20)", "module": "qr_preview", "function": "_draw_finder_marker", "line": 97}
{"timestamp": "2025-06-12T17:25:44.515006", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Creating finder pattern group with id='marker-0-0'", "module": "qr_preview", "function": "_draw_finder_marker", "line": 110}
{"timestamp": "2025-06-12T17:25:44.515043", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Drawing square border:", "module": "qr_preview", "function": "_draw_finder_marker", "line": 160}
{"timestamp": "2025-06-12T17:25:44.515080", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "  Outer: insert=(20.0, 20.0), size=(35.614035087719294, 35.614035087719294)", "module": "qr_preview", "function": "_draw_finder_marker", "line": 161}
{"timestamp": "2025-06-12T17:25:44.515119", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "  Inner: insert=(26.105263157894736, 26.105263157894736), size=(23.403508771929822, 23.403508771929822)", "module": "qr_preview", "function": "_draw_finder_marker", "line": 162}
{"timestamp": "2025-06-12T17:25:44.515195", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Drawing marker center: style=square, color=#000000, pos=(32.719298245614034, 32.719298245614034), size=10.175438596491228", "module": "qr_preview", "function": "_draw_marker_center", "line": 179}
{"timestamp": "2025-06-12T17:25:44.515248", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Successfully added finder pattern group to SVG", "module": "qr_preview", "function": "_draw_finder_marker", "line": 171}
{"timestamp": "2025-06-12T17:25:44.515331", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Successfully drew finder pattern 1/3", "module": "qr_preview", "function": "generate_preview_svg_modular", "line": 782}
{"timestamp": "2025-06-12T17:25:44.515366", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Drawing finder pattern 2/3 at position (50, 0)", "module": "qr_preview", "function": "generate_preview_svg_modular", "line": 780}
{"timestamp": "2025-06-12T17:25:44.515401", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "_draw_finder_marker called: mx=50, my=0, module_size=5.087719298245614, border_style=square, center_style=square, border_color=#000000, center_color=#000000, offset=(20, 20)", "module": "qr_preview", "function": "_draw_finder_marker", "line": 97}
{"timestamp": "2025-06-12T17:25:44.515444", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Creating finder pattern group with id='marker-50-0'", "module": "qr_preview", "function": "_draw_finder_marker", "line": 110}
{"timestamp": "2025-06-12T17:25:44.515474", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Drawing square border:", "module": "qr_preview", "function": "_draw_finder_marker", "line": 160}
{"timestamp": "2025-06-12T17:25:44.515507", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "  Outer: insert=(274.38596491228066, 20.0), size=(35.614035087719294, 35.614035087719294)", "module": "qr_preview", "function": "_draw_finder_marker", "line": 161}
{"timestamp": "2025-06-12T17:25:44.515539", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "  Inner: insert=(280.4912280701754, 26.105263157894736), size=(23.403508771929822, 23.403508771929822)", "module": "qr_preview", "function": "_draw_finder_marker", "line": 162}
{"timestamp": "2025-06-12T17:25:44.515596", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Drawing marker center: style=square, color=#000000, pos=(287.10526315789474, 32.719298245614034), size=10.175438596491228", "module": "qr_preview", "function": "_draw_marker_center", "line": 179}
{"timestamp": "2025-06-12T17:25:44.515642", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Successfully added finder pattern group to SVG", "module": "qr_preview", "function": "_draw_finder_marker", "line": 171}
{"timestamp": "2025-06-12T17:25:44.515672", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Successfully drew finder pattern 2/3", "module": "qr_preview", "function": "generate_preview_svg_modular", "line": 782}
{"timestamp": "2025-06-12T17:25:44.515702", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Drawing finder pattern 3/3 at position (0, 50)", "module": "qr_preview", "function": "generate_preview_svg_modular", "line": 780}
{"timestamp": "2025-06-12T17:25:44.515733", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "_draw_finder_marker called: mx=0, my=50, module_size=5.087719298245614, border_style=square, center_style=square, border_color=#000000, center_color=#000000, offset=(20, 20)", "module": "qr_preview", "function": "_draw_finder_marker", "line": 97}
{"timestamp": "2025-06-12T17:25:44.515771", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Creating finder pattern group with id='marker-0-50'", "module": "qr_preview", "function": "_draw_finder_marker", "line": 110}
{"timestamp": "2025-06-12T17:25:44.515798", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Drawing square border:", "module": "qr_preview", "function": "_draw_finder_marker", "line": 160}
{"timestamp": "2025-06-12T17:25:44.515830", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "  Outer: insert=(20.0, 274.38596491228066), size=(35.614035087719294, 35.614035087719294)", "module": "qr_preview", "function": "_draw_finder_marker", "line": 161}
{"timestamp": "2025-06-12T17:25:44.515860", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "  Inner: insert=(26.105263157894736, 280.4912280701754), size=(23.403508771929822, 23.403508771929822)", "module": "qr_preview", "function": "_draw_finder_marker", "line": 162}
{"timestamp": "2025-06-12T17:25:44.515947", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Drawing marker center: style=square, color=#000000, pos=(32.719298245614034, 287.10526315789474), size=10.175438596491228", "module": "qr_preview", "function": "_draw_marker_center", "line": 179}
{"timestamp": "2025-06-12T17:25:44.515992", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Successfully added finder pattern group to SVG", "module": "qr_preview", "function": "_draw_finder_marker", "line": 171}
{"timestamp": "2025-06-12T17:25:44.516020", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Successfully drew finder pattern 3/3", "module": "qr_preview", "function": "generate_preview_svg_modular", "line": 782}
{"timestamp": "2025-06-12T17:25:44.543962", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Generated SVG length: 247431", "module": "qr_preview", "function": "generate_preview_svg_modular", "line": 802}
{"timestamp": "2025-06-12T17:25:44.544129", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "SVG contains marker-0-0: True", "module": "qr_preview", "function": "generate_preview_svg_modular", "line": 803}
{"timestamp": "2025-06-12T17:25:44.544265", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "SVG contains marker-18-0: False", "module": "qr_preview", "function": "generate_preview_svg_modular", "line": 804}
{"timestamp": "2025-06-12T17:25:44.544401", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "SVG contains marker-0-18: False", "module": "qr_preview", "function": "generate_preview_svg_modular", "line": 805}
{"timestamp": "2025-06-12T17:25:44.544801", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "SVG marker counts - outer: 3, inner: 3, dot: 0", "module": "qr_preview", "function": "generate_preview_svg_modular", "line": 813}
{"timestamp": "2025-06-12T17:25:44.544837", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "SVG label counts - text: 0, background: 0", "module": "qr_preview", "function": "generate_preview_svg_modular", "line": 814}
{"timestamp": "2025-06-12T17:25:44.548839", "level": "DEBUG", "logger": "backend.main", "message": "[Auth] Enforced Bearer token authentication for request", "module": "main", "function": "bearer_token_auth_middleware", "line": 274}
{"timestamp": "2025-06-12T17:25:45.394484", "level": "DEBUG", "logger": "backend.core.security.jwt_keys", "message": "Token contains key ID: a69f821a-65e8-4292-8feb-985dc7ac94d9", "module": "jwt_keys", "function": "decode_token", "line": 188}
{"timestamp": "2025-06-12T17:25:45.394552", "level": "DEBUG", "logger": "backend.core.security.jwt_keys", "message": "Using key ID a69f821a-65e8-4292-8feb-985dc7ac94d9 for verification", "module": "jwt_keys", "function": "decode_token", "line": 195}
{"timestamp": "2025-06-12T17:25:45.394690", "level": "DEBUG", "logger": "backend.services.auth", "message": "JWT decoded successfully with key manager", "module": "auth", "function": "authenticate_token", "line": 683}
{"timestamp": "2025-06-12T17:25:45.398205", "level": "DEBUG", "logger": "backend.services.db_utils", "message": "Query 'auth_lookup_e4ae2d8f-b32f-52d7-99cd-8a33b19652c7' completed in 0.003s", "module": "db_utils", "function": "execute_query_with_timeout", "line": 68}
{"timestamp": "2025-06-12T17:25:45.398262", "level": "DEBUG", "logger": "backend.services.db_utils", "message": "Auth query 'auth_lookup_e4ae2d8f-b32f-52d7-99cd-8a33b19652c7' successful, result format preserved", "module": "db_utils", "function": "execute_query_with_timeout", "line": 72}
{"timestamp": "2025-06-12T17:25:45.402604", "level": "DEBUG", "logger": "backend.services.auth", "message": "Authentication attempt with token prefix: eyJhbGciOi...", "module": "auth", "function": "get_current_user", "line": 727}
{"timestamp": "2025-06-12T17:25:45.402761", "level": "DEBUG", "logger": "backend.core.security.jwt_keys", "message": "Token contains key ID: a69f821a-65e8-4292-8feb-985dc7ac94d9", "module": "jwt_keys", "function": "decode_token", "line": 188}
{"timestamp": "2025-06-12T17:25:45.402806", "level": "DEBUG", "logger": "backend.core.security.jwt_keys", "message": "Using key ID a69f821a-65e8-4292-8feb-985dc7ac94d9 for verification", "module": "jwt_keys", "function": "decode_token", "line": 195}
{"timestamp": "2025-06-12T17:25:45.402933", "level": "DEBUG", "logger": "backend.services.auth", "message": "JWT decoded successfully with key manager", "module": "auth", "function": "authenticate_token", "line": 683}
{"timestamp": "2025-06-12T17:25:45.404224", "level": "DEBUG", "logger": "backend.services.db_utils", "message": "Query 'auth_lookup_e4ae2d8f-b32f-52d7-99cd-8a33b19652c7' completed in 0.001s", "module": "db_utils", "function": "execute_query_with_timeout", "line": 68}
{"timestamp": "2025-06-12T17:25:45.404272", "level": "DEBUG", "logger": "backend.services.db_utils", "message": "Auth query 'auth_lookup_e4ae2d8f-b32f-52d7-99cd-8a33b19652c7' successful, result format preserved", "module": "db_utils", "function": "execute_query_with_timeout", "line": 72}
{"timestamp": "2025-06-12T17:25:45.404332", "level": "DEBUG", "logger": "backend.services.auth", "message": "Successfully authenticated user: <EMAIL>", "module": "auth", "function": "get_current_user", "line": 732}
{"timestamp": "2025-06-12T17:25:45.404372", "level": "DEBUG", "logger": "backend.services.auth", "message": "Successfully authenticated user: <EMAIL>", "module": "auth", "function": "get_current_user", "line": 738}
{"timestamp": "2025-06-12T17:25:45.405564", "level": "DEBUG", "logger": "backend.routes.qr_preview", "message": "Received QR preview SVG request: {\"qr_type\": \"scooter_unlock\", \"data\": {\"scooter_id\": \"SCOOT123\", \"unlock_code\": \"UNLOCK42\", \"title\": \"Scooter_unlock QR Code\", \"description\": \"Scan to view scooter_unlock content\", \"service_provider\": \"Lime\", \"rate_info\": \"$20\"}, \"designOptions\": {\"size\": 256, \"margin\": 10, \"pattern\": \"square\", \"marker_border_style\": \"square\", \"marker_border_color\": \"#000000\", \"marker_center_style\": \"square\", \"marker_center_color\": \"#000000\", \"logo_size\": 0.3, \"logo_position\": \"center\", \"logo_opacity\": 1, \"frame", "module": "qr_preview", "function": "preview_qr_svg", "line": 242}
{"timestamp": "2025-06-12T17:25:45.405661", "level": "DEBUG", "logger": "backend.routes.qr_preview", "message": "Form data keys: ['request']", "module": "qr_preview", "function": "preview_qr_svg", "line": 297}
{"timestamp": "2025-06-12T17:25:45.405703", "level": "DEBUG", "logger": "backend.routes.qr_preview", "message": "No logo key found in form data", "module": "qr_preview", "function": "preview_qr_svg", "line": 309}
{"timestamp": "2025-06-12T17:25:45.405749", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Formatting content for QR type: qrtype.scooter_unlock, content keys: ['scooter_id', 'unlock_code', 'title', 'description', 'service_provider', 'rate_info']", "module": "qr_preview", "function": "_format_qr_data", "line": 876}
{"timestamp": "2025-06-12T17:25:45.428726", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Logo remove background setting: False, has logo: False", "module": "qr_preview", "function": "generate_preview_svg_modular", "line": 727}
{"timestamp": "2025-06-12T17:25:45.447603", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Drawing 3 finder patterns at positions: [(0, 0), (50, 0), (0, 50)]", "module": "qr_preview", "function": "generate_preview_svg_modular", "line": 775}
{"timestamp": "2025-06-12T17:25:45.447669", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "QR size: 57, border_style: square, center_style: square", "module": "qr_preview", "function": "generate_preview_svg_modular", "line": 776}
{"timestamp": "2025-06-12T17:25:45.447819", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Drawing finder pattern 1/3 at position (0, 0)", "module": "qr_preview", "function": "generate_preview_svg_modular", "line": 780}
{"timestamp": "2025-06-12T17:25:45.447926", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "_draw_finder_marker called: mx=0, my=0, module_size=5.087719298245614, border_style=square, center_style=square, border_color=#000000, center_color=#000000, offset=(20, 20)", "module": "qr_preview", "function": "_draw_finder_marker", "line": 97}
{"timestamp": "2025-06-12T17:25:45.448005", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Creating finder pattern group with id='marker-0-0'", "module": "qr_preview", "function": "_draw_finder_marker", "line": 110}
{"timestamp": "2025-06-12T17:25:45.448044", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Drawing square border:", "module": "qr_preview", "function": "_draw_finder_marker", "line": 160}
{"timestamp": "2025-06-12T17:25:45.448082", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "  Outer: insert=(20.0, 20.0), size=(35.614035087719294, 35.614035087719294)", "module": "qr_preview", "function": "_draw_finder_marker", "line": 161}
{"timestamp": "2025-06-12T17:25:45.448117", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "  Inner: insert=(26.105263157894736, 26.105263157894736), size=(23.403508771929822, 23.403508771929822)", "module": "qr_preview", "function": "_draw_finder_marker", "line": 162}
{"timestamp": "2025-06-12T17:25:45.448264", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Drawing marker center: style=square, color=#000000, pos=(32.719298245614034, 32.719298245614034), size=10.175438596491228", "module": "qr_preview", "function": "_draw_marker_center", "line": 179}
{"timestamp": "2025-06-12T17:25:45.448356", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Successfully added finder pattern group to SVG", "module": "qr_preview", "function": "_draw_finder_marker", "line": 171}
{"timestamp": "2025-06-12T17:25:45.448402", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Successfully drew finder pattern 1/3", "module": "qr_preview", "function": "generate_preview_svg_modular", "line": 782}
{"timestamp": "2025-06-12T17:25:45.448444", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Drawing finder pattern 2/3 at position (50, 0)", "module": "qr_preview", "function": "generate_preview_svg_modular", "line": 780}
{"timestamp": "2025-06-12T17:25:45.448490", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "_draw_finder_marker called: mx=50, my=0, module_size=5.087719298245614, border_style=square, center_style=square, border_color=#000000, center_color=#000000, offset=(20, 20)", "module": "qr_preview", "function": "_draw_finder_marker", "line": 97}
{"timestamp": "2025-06-12T17:25:45.448534", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Creating finder pattern group with id='marker-50-0'", "module": "qr_preview", "function": "_draw_finder_marker", "line": 110}
{"timestamp": "2025-06-12T17:25:45.448570", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Drawing square border:", "module": "qr_preview", "function": "_draw_finder_marker", "line": 160}
{"timestamp": "2025-06-12T17:25:45.448606", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "  Outer: insert=(274.38596491228066, 20.0), size=(35.614035087719294, 35.614035087719294)", "module": "qr_preview", "function": "_draw_finder_marker", "line": 161}
{"timestamp": "2025-06-12T17:25:45.448642", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "  Inner: insert=(280.4912280701754, 26.105263157894736), size=(23.403508771929822, 23.403508771929822)", "module": "qr_preview", "function": "_draw_finder_marker", "line": 162}
{"timestamp": "2025-06-12T17:25:45.448706", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Drawing marker center: style=square, color=#000000, pos=(287.10526315789474, 32.719298245614034), size=10.175438596491228", "module": "qr_preview", "function": "_draw_marker_center", "line": 179}
{"timestamp": "2025-06-12T17:25:45.448787", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Successfully added finder pattern group to SVG", "module": "qr_preview", "function": "_draw_finder_marker", "line": 171}
{"timestamp": "2025-06-12T17:25:45.448820", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Successfully drew finder pattern 2/3", "module": "qr_preview", "function": "generate_preview_svg_modular", "line": 782}
{"timestamp": "2025-06-12T17:25:45.448852", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Drawing finder pattern 3/3 at position (0, 50)", "module": "qr_preview", "function": "generate_preview_svg_modular", "line": 780}
{"timestamp": "2025-06-12T17:25:45.448886", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "_draw_finder_marker called: mx=0, my=50, module_size=5.087719298245614, border_style=square, center_style=square, border_color=#000000, center_color=#000000, offset=(20, 20)", "module": "qr_preview", "function": "_draw_finder_marker", "line": 97}
{"timestamp": "2025-06-12T17:25:45.448926", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Creating finder pattern group with id='marker-0-50'", "module": "qr_preview", "function": "_draw_finder_marker", "line": 110}
{"timestamp": "2025-06-12T17:25:45.448959", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Drawing square border:", "module": "qr_preview", "function": "_draw_finder_marker", "line": 160}
{"timestamp": "2025-06-12T17:25:45.448992", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "  Outer: insert=(20.0, 274.38596491228066), size=(35.614035087719294, 35.614035087719294)", "module": "qr_preview", "function": "_draw_finder_marker", "line": 161}
{"timestamp": "2025-06-12T17:25:45.449027", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "  Inner: insert=(26.105263157894736, 280.4912280701754), size=(23.403508771929822, 23.403508771929822)", "module": "qr_preview", "function": "_draw_finder_marker", "line": 162}
{"timestamp": "2025-06-12T17:25:45.449088", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Drawing marker center: style=square, color=#000000, pos=(32.719298245614034, 287.10526315789474), size=10.175438596491228", "module": "qr_preview", "function": "_draw_marker_center", "line": 179}
{"timestamp": "2025-06-12T17:25:45.449135", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Successfully added finder pattern group to SVG", "module": "qr_preview", "function": "_draw_finder_marker", "line": 171}
{"timestamp": "2025-06-12T17:25:45.449169", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Successfully drew finder pattern 3/3", "module": "qr_preview", "function": "generate_preview_svg_modular", "line": 782}
{"timestamp": "2025-06-12T17:25:45.475764", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Generated SVG length: 247431", "module": "qr_preview", "function": "generate_preview_svg_modular", "line": 802}
{"timestamp": "2025-06-12T17:25:45.475927", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "SVG contains marker-0-0: True", "module": "qr_preview", "function": "generate_preview_svg_modular", "line": 803}
{"timestamp": "2025-06-12T17:25:45.476136", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "SVG contains marker-18-0: False", "module": "qr_preview", "function": "generate_preview_svg_modular", "line": 804}
{"timestamp": "2025-06-12T17:25:45.476297", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "SVG contains marker-0-18: False", "module": "qr_preview", "function": "generate_preview_svg_modular", "line": 805}
{"timestamp": "2025-06-12T17:25:45.476710", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "SVG marker counts - outer: 3, inner: 3, dot: 0", "module": "qr_preview", "function": "generate_preview_svg_modular", "line": 813}
{"timestamp": "2025-06-12T17:25:45.476789", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "SVG label counts - text: 0, background: 0", "module": "qr_preview", "function": "generate_preview_svg_modular", "line": 814}
{"timestamp": "2025-06-12T17:25:45.478874", "level": "DEBUG", "logger": "backend.main", "message": "[Auth] Enforced Bearer token authentication for request", "module": "main", "function": "bearer_token_auth_middleware", "line": 274}
{"timestamp": "2025-06-12T17:25:57.999664", "level": "DEBUG", "logger": "backend.main", "message": "[Auth] Enforced Bearer token authentication for request", "module": "main", "function": "bearer_token_auth_middleware", "line": 274}
{"timestamp": "2025-06-12T17:25:58.000354", "level": "DEBUG", "logger": "backend.main", "message": "[Auth] Enforced Bearer token authentication for request", "module": "main", "function": "bearer_token_auth_middleware", "line": 274}
{"timestamp": "2025-06-12T17:25:58.080162", "level": "DEBUG", "logger": "backend.core.security.jwt_keys", "message": "Token contains key ID: a69f821a-65e8-4292-8feb-985dc7ac94d9", "module": "jwt_keys", "function": "decode_token", "line": 188}
{"timestamp": "2025-06-12T17:25:58.080241", "level": "DEBUG", "logger": "backend.core.security.jwt_keys", "message": "Using key ID a69f821a-65e8-4292-8feb-985dc7ac94d9 for verification", "module": "jwt_keys", "function": "decode_token", "line": 195}
{"timestamp": "2025-06-12T17:25:58.080937", "level": "DEBUG", "logger": "backend.services.auth", "message": "JWT decoded successfully with key manager", "module": "auth", "function": "authenticate_token", "line": 683}
{"timestamp": "2025-06-12T17:25:58.088867", "level": "DEBUG", "logger": "backend.services.db_utils", "message": "Query 'auth_lookup_e4ae2d8f-b32f-52d7-99cd-8a33b19652c7' completed in 0.005s", "module": "db_utils", "function": "execute_query_with_timeout", "line": 68}
{"timestamp": "2025-06-12T17:25:58.088928", "level": "DEBUG", "logger": "backend.services.db_utils", "message": "Auth query 'auth_lookup_e4ae2d8f-b32f-52d7-99cd-8a33b19652c7' successful, result format preserved", "module": "db_utils", "function": "execute_query_with_timeout", "line": 72}
{"timestamp": "2025-06-12T17:25:58.098126", "level": "DEBUG", "logger": "backend.services.auth", "message": "Authentication attempt with token prefix: eyJhbGciOi...", "module": "auth", "function": "get_current_user", "line": 727}
{"timestamp": "2025-06-12T17:25:58.098276", "level": "DEBUG", "logger": "backend.core.security.jwt_keys", "message": "Token contains key ID: a69f821a-65e8-4292-8feb-985dc7ac94d9", "module": "jwt_keys", "function": "decode_token", "line": 188}
{"timestamp": "2025-06-12T17:25:58.098319", "level": "DEBUG", "logger": "backend.core.security.jwt_keys", "message": "Using key ID a69f821a-65e8-4292-8feb-985dc7ac94d9 for verification", "module": "jwt_keys", "function": "decode_token", "line": 195}
{"timestamp": "2025-06-12T17:25:58.098458", "level": "DEBUG", "logger": "backend.services.auth", "message": "JWT decoded successfully with key manager", "module": "auth", "function": "authenticate_token", "line": 683}
{"timestamp": "2025-06-12T17:25:58.101150", "level": "DEBUG", "logger": "backend.services.db_utils", "message": "Query 'auth_lookup_e4ae2d8f-b32f-52d7-99cd-8a33b19652c7' completed in 0.003s", "module": "db_utils", "function": "execute_query_with_timeout", "line": 68}
{"timestamp": "2025-06-12T17:25:58.101200", "level": "DEBUG", "logger": "backend.services.db_utils", "message": "Auth query 'auth_lookup_e4ae2d8f-b32f-52d7-99cd-8a33b19652c7' successful, result format preserved", "module": "db_utils", "function": "execute_query_with_timeout", "line": 72}
{"timestamp": "2025-06-12T17:25:58.101263", "level": "DEBUG", "logger": "backend.services.auth", "message": "Successfully authenticated user: <EMAIL>", "module": "auth", "function": "get_current_user", "line": 732}
{"timestamp": "2025-06-12T17:25:58.101339", "level": "DEBUG", "logger": "backend.services.auth", "message": "Successfully authenticated user: <EMAIL>", "module": "auth", "function": "get_current_user", "line": 738}
{"timestamp": "2025-06-12T17:25:58.107031", "level": "DEBUG", "logger": "backend.routes.qr_preview", "message": "Received QR preview SVG request: {\"qr_type\": \"scooter_unlock\", \"data\": {\"scooter_id\": \"SCOOT123\", \"unlock_code\": \"UNLOCK42\", \"title\": \"Scooter_unlock QR Code\", \"description\": \"Scan to view scooter_unlock content\", \"service_provider\": \"Lime\", \"rate_info\": \"$20\"}, \"designOptions\": {\"size\": 256, \"margin\": 10, \"pattern\": \"square\", \"marker_border_style\": \"square\", \"marker_center_style\": \"square\", \"logo_size\": 0.3, \"logo_position\": \"center\", \"logo_opacity\": 1, \"logo_remove_background\": false, \"frame_style\": \"none\", \"frame_font\": \"SF ", "module": "qr_preview", "function": "preview_qr_svg", "line": 242}
{"timestamp": "2025-06-12T17:25:58.107324", "level": "DEBUG", "logger": "backend.routes.qr_preview", "message": "Form data keys: ['request', 'logo']", "module": "qr_preview", "function": "preview_qr_svg", "line": 297}
{"timestamp": "2025-06-12T17:25:58.107375", "level": "DEBUG", "logger": "backend.routes.qr_preview", "message": "Logo file found: type=<class 'starlette.datastructures.UploadFile'>, filename=LOGO - 1080 - ORANGE.png", "module": "qr_preview", "function": "preview_qr_svg", "line": 300}
{"timestamp": "2025-06-12T17:25:58.107417", "level": "DEBUG", "logger": "backend.routes.qr_preview", "message": "Logo content read: 127404 bytes", "module": "qr_preview", "function": "preview_qr_svg", "line": 305}
{"timestamp": "2025-06-12T17:25:58.107522", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Formatting content for QR type: qrtype.scooter_unlock, content keys: ['scooter_id', 'unlock_code', 'title', 'description', 'service_provider', 'rate_info']", "module": "qr_preview", "function": "_format_qr_data", "line": 876}
{"timestamp": "2025-06-12T17:25:58.131876", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Logo remove background setting: False, has logo: True", "module": "qr_preview", "function": "generate_preview_svg_modular", "line": 727}
{"timestamp": "2025-06-12T17:25:58.131931", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Logo present but background removal disabled", "module": "qr_preview", "function": "generate_preview_svg_modular", "line": 741}
{"timestamp": "2025-06-12T17:25:58.150551", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Drawing 3 finder patterns at positions: [(0, 0), (50, 0), (0, 50)]", "module": "qr_preview", "function": "generate_preview_svg_modular", "line": 775}
{"timestamp": "2025-06-12T17:25:58.150606", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "QR size: 57, border_style: square, center_style: square", "module": "qr_preview", "function": "generate_preview_svg_modular", "line": 776}
{"timestamp": "2025-06-12T17:25:58.150640", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Drawing finder pattern 1/3 at position (0, 0)", "module": "qr_preview", "function": "generate_preview_svg_modular", "line": 780}
{"timestamp": "2025-06-12T17:25:58.151078", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "_draw_finder_marker called: mx=0, my=0, module_size=5.087719298245614, border_style=square, center_style=square, border_color=#000000, center_color=#000000, offset=(20, 20)", "module": "qr_preview", "function": "_draw_finder_marker", "line": 97}
{"timestamp": "2025-06-12T17:25:58.151280", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Creating finder pattern group with id='marker-0-0'", "module": "qr_preview", "function": "_draw_finder_marker", "line": 110}
{"timestamp": "2025-06-12T17:25:58.151318", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Drawing square border:", "module": "qr_preview", "function": "_draw_finder_marker", "line": 160}
{"timestamp": "2025-06-12T17:25:58.151415", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "  Outer: insert=(20.0, 20.0), size=(35.614035087719294, 35.614035087719294)", "module": "qr_preview", "function": "_draw_finder_marker", "line": 161}
{"timestamp": "2025-06-12T17:25:58.151449", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "  Inner: insert=(26.105263157894736, 26.105263157894736), size=(23.403508771929822, 23.403508771929822)", "module": "qr_preview", "function": "_draw_finder_marker", "line": 162}
{"timestamp": "2025-06-12T17:25:58.151865", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Drawing marker center: style=square, color=#000000, pos=(32.719298245614034, 32.719298245614034), size=10.175438596491228", "module": "qr_preview", "function": "_draw_marker_center", "line": 179}
{"timestamp": "2025-06-12T17:25:58.152058", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Successfully added finder pattern group to SVG", "module": "qr_preview", "function": "_draw_finder_marker", "line": 171}
{"timestamp": "2025-06-12T17:25:58.152093", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Successfully drew finder pattern 1/3", "module": "qr_preview", "function": "generate_preview_svg_modular", "line": 782}
{"timestamp": "2025-06-12T17:25:58.152124", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Drawing finder pattern 2/3 at position (50, 0)", "module": "qr_preview", "function": "generate_preview_svg_modular", "line": 780}
{"timestamp": "2025-06-12T17:25:58.152323", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "_draw_finder_marker called: mx=50, my=0, module_size=5.087719298245614, border_style=square, center_style=square, border_color=#000000, center_color=#000000, offset=(20, 20)", "module": "qr_preview", "function": "_draw_finder_marker", "line": 97}
{"timestamp": "2025-06-12T17:25:58.152364", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Creating finder pattern group with id='marker-50-0'", "module": "qr_preview", "function": "_draw_finder_marker", "line": 110}
{"timestamp": "2025-06-12T17:25:58.152395", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Drawing square border:", "module": "qr_preview", "function": "_draw_finder_marker", "line": 160}
{"timestamp": "2025-06-12T17:25:58.152429", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "  Outer: insert=(274.38596491228066, 20.0), size=(35.614035087719294, 35.614035087719294)", "module": "qr_preview", "function": "_draw_finder_marker", "line": 161}
{"timestamp": "2025-06-12T17:25:58.152460", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "  Inner: insert=(280.4912280701754, 26.105263157894736), size=(23.403508771929822, 23.403508771929822)", "module": "qr_preview", "function": "_draw_finder_marker", "line": 162}
{"timestamp": "2025-06-12T17:25:58.152520", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Drawing marker center: style=square, color=#000000, pos=(287.10526315789474, 32.719298245614034), size=10.175438596491228", "module": "qr_preview", "function": "_draw_marker_center", "line": 179}
{"timestamp": "2025-06-12T17:25:58.152564", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Successfully added finder pattern group to SVG", "module": "qr_preview", "function": "_draw_finder_marker", "line": 171}
{"timestamp": "2025-06-12T17:25:58.152593", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Successfully drew finder pattern 2/3", "module": "qr_preview", "function": "generate_preview_svg_modular", "line": 782}
{"timestamp": "2025-06-12T17:25:58.152620", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Drawing finder pattern 3/3 at position (0, 50)", "module": "qr_preview", "function": "generate_preview_svg_modular", "line": 780}
{"timestamp": "2025-06-12T17:25:58.152650", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "_draw_finder_marker called: mx=0, my=50, module_size=5.087719298245614, border_style=square, center_style=square, border_color=#000000, center_color=#000000, offset=(20, 20)", "module": "qr_preview", "function": "_draw_finder_marker", "line": 97}
{"timestamp": "2025-06-12T17:25:58.152707", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Creating finder pattern group with id='marker-0-50'", "module": "qr_preview", "function": "_draw_finder_marker", "line": 110}
{"timestamp": "2025-06-12T17:25:58.152735", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Drawing square border:", "module": "qr_preview", "function": "_draw_finder_marker", "line": 160}
{"timestamp": "2025-06-12T17:25:58.152765", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "  Outer: insert=(20.0, 274.38596491228066), size=(35.614035087719294, 35.614035087719294)", "module": "qr_preview", "function": "_draw_finder_marker", "line": 161}
{"timestamp": "2025-06-12T17:25:58.152793", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "  Inner: insert=(26.105263157894736, 280.4912280701754), size=(23.403508771929822, 23.403508771929822)", "module": "qr_preview", "function": "_draw_finder_marker", "line": 162}
{"timestamp": "2025-06-12T17:25:58.152845", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Drawing marker center: style=square, color=#000000, pos=(32.719298245614034, 287.10526315789474), size=10.175438596491228", "module": "qr_preview", "function": "_draw_marker_center", "line": 179}
{"timestamp": "2025-06-12T17:25:58.153612", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Successfully added finder pattern group to SVG", "module": "qr_preview", "function": "_draw_finder_marker", "line": 171}
{"timestamp": "2025-06-12T17:25:58.153644", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Successfully drew finder pattern 3/3", "module": "qr_preview", "function": "generate_preview_svg_modular", "line": 782}
{"timestamp": "2025-06-12T17:25:58.154043", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "_draw_logo called with logo type: <class 'bytes'>, dimensions: 300", "module": "qr_preview", "function": "_draw_logo", "line": 434}
{"timestamp": "2025-06-12T17:25:58.557539", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Generated SVG length: 251209", "module": "qr_preview", "function": "generate_preview_svg_modular", "line": 802}
{"timestamp": "2025-06-12T17:25:58.557705", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "SVG contains marker-0-0: True", "module": "qr_preview", "function": "generate_preview_svg_modular", "line": 803}
{"timestamp": "2025-06-12T17:25:58.557841", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "SVG contains marker-18-0: False", "module": "qr_preview", "function": "generate_preview_svg_modular", "line": 804}
{"timestamp": "2025-06-12T17:25:58.557977", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "SVG contains marker-0-18: False", "module": "qr_preview", "function": "generate_preview_svg_modular", "line": 805}
{"timestamp": "2025-06-12T17:25:58.558380", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "SVG marker counts - outer: 3, inner: 3, dot: 0", "module": "qr_preview", "function": "generate_preview_svg_modular", "line": 813}
{"timestamp": "2025-06-12T17:25:58.558413", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "SVG label counts - text: 0, background: 0", "module": "qr_preview", "function": "generate_preview_svg_modular", "line": 814}
{"timestamp": "2025-06-12T17:25:58.561462", "level": "DEBUG", "logger": "backend.main", "message": "[Auth] Enforced Bearer token authentication for request", "module": "main", "function": "bearer_token_auth_middleware", "line": 274}
{"timestamp": "2025-06-12T17:25:59.393507", "level": "DEBUG", "logger": "backend.core.security.jwt_keys", "message": "Token contains key ID: a69f821a-65e8-4292-8feb-985dc7ac94d9", "module": "jwt_keys", "function": "decode_token", "line": 188}
{"timestamp": "2025-06-12T17:25:59.393627", "level": "DEBUG", "logger": "backend.core.security.jwt_keys", "message": "Using key ID a69f821a-65e8-4292-8feb-985dc7ac94d9 for verification", "module": "jwt_keys", "function": "decode_token", "line": 195}
{"timestamp": "2025-06-12T17:25:59.394152", "level": "DEBUG", "logger": "backend.services.auth", "message": "JWT decoded successfully with key manager", "module": "auth", "function": "authenticate_token", "line": 683}
{"timestamp": "2025-06-12T17:25:59.407631", "level": "DEBUG", "logger": "backend.services.db_utils", "message": "Query 'auth_lookup_e4ae2d8f-b32f-52d7-99cd-8a33b19652c7' completed in 0.013s", "module": "db_utils", "function": "execute_query_with_timeout", "line": 68}
{"timestamp": "2025-06-12T17:25:59.407710", "level": "DEBUG", "logger": "backend.services.db_utils", "message": "Auth query 'auth_lookup_e4ae2d8f-b32f-52d7-99cd-8a33b19652c7' successful, result format preserved", "module": "db_utils", "function": "execute_query_with_timeout", "line": 72}
{"timestamp": "2025-06-12T17:25:59.413907", "level": "DEBUG", "logger": "backend.services.auth", "message": "Authentication attempt with token prefix: eyJhbGciOi...", "module": "auth", "function": "get_current_user", "line": 727}
{"timestamp": "2025-06-12T17:25:59.414065", "level": "DEBUG", "logger": "backend.core.security.jwt_keys", "message": "Token contains key ID: a69f821a-65e8-4292-8feb-985dc7ac94d9", "module": "jwt_keys", "function": "decode_token", "line": 188}
{"timestamp": "2025-06-12T17:25:59.414113", "level": "DEBUG", "logger": "backend.core.security.jwt_keys", "message": "Using key ID a69f821a-65e8-4292-8feb-985dc7ac94d9 for verification", "module": "jwt_keys", "function": "decode_token", "line": 195}
{"timestamp": "2025-06-12T17:25:59.414275", "level": "DEBUG", "logger": "backend.services.auth", "message": "JWT decoded successfully with key manager", "module": "auth", "function": "authenticate_token", "line": 683}
{"timestamp": "2025-06-12T17:25:59.416056", "level": "DEBUG", "logger": "backend.services.db_utils", "message": "Query 'auth_lookup_e4ae2d8f-b32f-52d7-99cd-8a33b19652c7' completed in 0.002s", "module": "db_utils", "function": "execute_query_with_timeout", "line": 68}
{"timestamp": "2025-06-12T17:25:59.416115", "level": "DEBUG", "logger": "backend.services.db_utils", "message": "Auth query 'auth_lookup_e4ae2d8f-b32f-52d7-99cd-8a33b19652c7' successful, result format preserved", "module": "db_utils", "function": "execute_query_with_timeout", "line": 72}
{"timestamp": "2025-06-12T17:25:59.416175", "level": "DEBUG", "logger": "backend.services.auth", "message": "Successfully authenticated user: <EMAIL>", "module": "auth", "function": "get_current_user", "line": 732}
{"timestamp": "2025-06-12T17:25:59.416213", "level": "DEBUG", "logger": "backend.services.auth", "message": "Successfully authenticated user: <EMAIL>", "module": "auth", "function": "get_current_user", "line": 738}
{"timestamp": "2025-06-12T17:25:59.417929", "level": "DEBUG", "logger": "backend.routes.qr_preview", "message": "Received QR preview SVG request: {\"qr_type\": \"scooter_unlock\", \"data\": {\"scooter_id\": \"SCOOT123\", \"unlock_code\": \"UNLOCK42\", \"title\": \"Scooter_unlock QR Code\", \"description\": \"Scan to view scooter_unlock content\", \"service_provider\": \"Lime\", \"rate_info\": \"$20\"}, \"designOptions\": {\"size\": 256, \"margin\": 10, \"pattern\": \"square\", \"marker_border_style\": \"square\", \"marker_border_color\": \"#000000\", \"marker_center_style\": \"square\", \"marker_center_color\": \"#000000\", \"logo_size\": 0.3, \"logo_position\": \"center\", \"logo_opacity\": 1, \"frame", "module": "qr_preview", "function": "preview_qr_svg", "line": 242}
{"timestamp": "2025-06-12T17:25:59.418047", "level": "DEBUG", "logger": "backend.routes.qr_preview", "message": "Form data keys: ['request', 'logo']", "module": "qr_preview", "function": "preview_qr_svg", "line": 297}
{"timestamp": "2025-06-12T17:25:59.418140", "level": "DEBUG", "logger": "backend.routes.qr_preview", "message": "Logo file found: type=<class 'starlette.datastructures.UploadFile'>, filename=LOGO - 1080 - ORANGE.png", "module": "qr_preview", "function": "preview_qr_svg", "line": 300}
{"timestamp": "2025-06-12T17:25:59.418194", "level": "DEBUG", "logger": "backend.routes.qr_preview", "message": "Logo content read: 127404 bytes", "module": "qr_preview", "function": "preview_qr_svg", "line": 305}
{"timestamp": "2025-06-12T17:25:59.418245", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Formatting content for QR type: qrtype.scooter_unlock, content keys: ['scooter_id', 'unlock_code', 'title', 'description', 'service_provider', 'rate_info']", "module": "qr_preview", "function": "_format_qr_data", "line": 876}
{"timestamp": "2025-06-12T17:25:59.448728", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Logo remove background setting: False, has logo: True", "module": "qr_preview", "function": "generate_preview_svg_modular", "line": 727}
{"timestamp": "2025-06-12T17:25:59.448786", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Logo present but background removal disabled", "module": "qr_preview", "function": "generate_preview_svg_modular", "line": 741}
{"timestamp": "2025-06-12T17:25:59.468434", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Drawing 3 finder patterns at positions: [(0, 0), (50, 0), (0, 50)]", "module": "qr_preview", "function": "generate_preview_svg_modular", "line": 775}
{"timestamp": "2025-06-12T17:25:59.468494", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "QR size: 57, border_style: square, center_style: square", "module": "qr_preview", "function": "generate_preview_svg_modular", "line": 776}
{"timestamp": "2025-06-12T17:25:59.468530", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Drawing finder pattern 1/3 at position (0, 0)", "module": "qr_preview", "function": "generate_preview_svg_modular", "line": 780}
{"timestamp": "2025-06-12T17:25:59.468573", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "_draw_finder_marker called: mx=0, my=0, module_size=5.087719298245614, border_style=square, center_style=square, border_color=#000000, center_color=#000000, offset=(20, 20)", "module": "qr_preview", "function": "_draw_finder_marker", "line": 97}
{"timestamp": "2025-06-12T17:25:59.470186", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Creating finder pattern group with id='marker-0-0'", "module": "qr_preview", "function": "_draw_finder_marker", "line": 110}
{"timestamp": "2025-06-12T17:25:59.470252", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Drawing square border:", "module": "qr_preview", "function": "_draw_finder_marker", "line": 160}
{"timestamp": "2025-06-12T17:25:59.470295", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "  Outer: insert=(20.0, 20.0), size=(35.614035087719294, 35.614035087719294)", "module": "qr_preview", "function": "_draw_finder_marker", "line": 161}
{"timestamp": "2025-06-12T17:25:59.470333", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "  Inner: insert=(26.105263157894736, 26.105263157894736), size=(23.403508771929822, 23.403508771929822)", "module": "qr_preview", "function": "_draw_finder_marker", "line": 162}
{"timestamp": "2025-06-12T17:25:59.470418", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Drawing marker center: style=square, color=#000000, pos=(32.719298245614034, 32.719298245614034), size=10.175438596491228", "module": "qr_preview", "function": "_draw_marker_center", "line": 179}
{"timestamp": "2025-06-12T17:25:59.470466", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Successfully added finder pattern group to SVG", "module": "qr_preview", "function": "_draw_finder_marker", "line": 171}
{"timestamp": "2025-06-12T17:25:59.471311", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Successfully drew finder pattern 1/3", "module": "qr_preview", "function": "generate_preview_svg_modular", "line": 782}
{"timestamp": "2025-06-12T17:25:59.471530", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Drawing finder pattern 2/3 at position (50, 0)", "module": "qr_preview", "function": "generate_preview_svg_modular", "line": 780}
{"timestamp": "2025-06-12T17:25:59.471567", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "_draw_finder_marker called: mx=50, my=0, module_size=5.087719298245614, border_style=square, center_style=square, border_color=#000000, center_color=#000000, offset=(20, 20)", "module": "qr_preview", "function": "_draw_finder_marker", "line": 97}
{"timestamp": "2025-06-12T17:25:59.471610", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Creating finder pattern group with id='marker-50-0'", "module": "qr_preview", "function": "_draw_finder_marker", "line": 110}
{"timestamp": "2025-06-12T17:25:59.471641", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Drawing square border:", "module": "qr_preview", "function": "_draw_finder_marker", "line": 160}
{"timestamp": "2025-06-12T17:25:59.471679", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "  Outer: insert=(274.38596491228066, 20.0), size=(35.614035087719294, 35.614035087719294)", "module": "qr_preview", "function": "_draw_finder_marker", "line": 161}
{"timestamp": "2025-06-12T17:25:59.471711", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "  Inner: insert=(280.4912280701754, 26.105263157894736), size=(23.403508771929822, 23.403508771929822)", "module": "qr_preview", "function": "_draw_finder_marker", "line": 162}
{"timestamp": "2025-06-12T17:25:59.471796", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Drawing marker center: style=square, color=#000000, pos=(287.10526315789474, 32.719298245614034), size=10.175438596491228", "module": "qr_preview", "function": "_draw_marker_center", "line": 179}
{"timestamp": "2025-06-12T17:25:59.471876", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Successfully added finder pattern group to SVG", "module": "qr_preview", "function": "_draw_finder_marker", "line": 171}
{"timestamp": "2025-06-12T17:25:59.471919", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Successfully drew finder pattern 2/3", "module": "qr_preview", "function": "generate_preview_svg_modular", "line": 782}
{"timestamp": "2025-06-12T17:25:59.471953", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Drawing finder pattern 3/3 at position (0, 50)", "module": "qr_preview", "function": "generate_preview_svg_modular", "line": 780}
{"timestamp": "2025-06-12T17:25:59.471989", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "_draw_finder_marker called: mx=0, my=50, module_size=5.087719298245614, border_style=square, center_style=square, border_color=#000000, center_color=#000000, offset=(20, 20)", "module": "qr_preview", "function": "_draw_finder_marker", "line": 97}
{"timestamp": "2025-06-12T17:25:59.472029", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Creating finder pattern group with id='marker-0-50'", "module": "qr_preview", "function": "_draw_finder_marker", "line": 110}
{"timestamp": "2025-06-12T17:25:59.472059", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Drawing square border:", "module": "qr_preview", "function": "_draw_finder_marker", "line": 160}
{"timestamp": "2025-06-12T17:25:59.472090", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "  Outer: insert=(20.0, 274.38596491228066), size=(35.614035087719294, 35.614035087719294)", "module": "qr_preview", "function": "_draw_finder_marker", "line": 161}
{"timestamp": "2025-06-12T17:25:59.472121", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "  Inner: insert=(26.105263157894736, 280.4912280701754), size=(23.403508771929822, 23.403508771929822)", "module": "qr_preview", "function": "_draw_finder_marker", "line": 162}
{"timestamp": "2025-06-12T17:25:59.472203", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Drawing marker center: style=square, color=#000000, pos=(32.719298245614034, 287.10526315789474), size=10.175438596491228", "module": "qr_preview", "function": "_draw_marker_center", "line": 179}
{"timestamp": "2025-06-12T17:25:59.472249", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Successfully added finder pattern group to SVG", "module": "qr_preview", "function": "_draw_finder_marker", "line": 171}
{"timestamp": "2025-06-12T17:25:59.472279", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Successfully drew finder pattern 3/3", "module": "qr_preview", "function": "generate_preview_svg_modular", "line": 782}
{"timestamp": "2025-06-12T17:25:59.472317", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "_draw_logo called with logo type: <class 'bytes'>, dimensions: 300", "module": "qr_preview", "function": "_draw_logo", "line": 434}
{"timestamp": "2025-06-12T17:25:59.801772", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Generated SVG length: 251209", "module": "qr_preview", "function": "generate_preview_svg_modular", "line": 802}
{"timestamp": "2025-06-12T17:25:59.801946", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "SVG contains marker-0-0: True", "module": "qr_preview", "function": "generate_preview_svg_modular", "line": 803}
{"timestamp": "2025-06-12T17:25:59.802082", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "SVG contains marker-18-0: False", "module": "qr_preview", "function": "generate_preview_svg_modular", "line": 804}
{"timestamp": "2025-06-12T17:25:59.802217", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "SVG contains marker-0-18: False", "module": "qr_preview", "function": "generate_preview_svg_modular", "line": 805}
{"timestamp": "2025-06-12T17:25:59.802614", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "SVG marker counts - outer: 3, inner: 3, dot: 0", "module": "qr_preview", "function": "generate_preview_svg_modular", "line": 813}
{"timestamp": "2025-06-12T17:25:59.803770", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "SVG label counts - text: 0, background: 0", "module": "qr_preview", "function": "generate_preview_svg_modular", "line": 814}
{"timestamp": "2025-06-12T17:25:59.806138", "level": "DEBUG", "logger": "backend.main", "message": "[Auth] Enforced Bearer token authentication for request", "module": "main", "function": "bearer_token_auth_middleware", "line": 274}
{"timestamp": "2025-06-12T17:26:04.150669", "level": "DEBUG", "logger": "backend.core.security.jwt_keys", "message": "Token contains key ID: a69f821a-65e8-4292-8feb-985dc7ac94d9", "module": "jwt_keys", "function": "decode_token", "line": 188}
{"timestamp": "2025-06-12T17:26:04.150745", "level": "DEBUG", "logger": "backend.core.security.jwt_keys", "message": "Using key ID a69f821a-65e8-4292-8feb-985dc7ac94d9 for verification", "module": "jwt_keys", "function": "decode_token", "line": 195}
{"timestamp": "2025-06-12T17:26:04.150867", "level": "DEBUG", "logger": "backend.services.auth", "message": "JWT decoded successfully with key manager", "module": "auth", "function": "authenticate_token", "line": 683}
{"timestamp": "2025-06-12T17:26:04.152323", "level": "DEBUG", "logger": "backend.services.db_utils", "message": "Query 'auth_lookup_e4ae2d8f-b32f-52d7-99cd-8a33b19652c7' completed in 0.001s", "module": "db_utils", "function": "execute_query_with_timeout", "line": 68}
{"timestamp": "2025-06-12T17:26:04.152362", "level": "DEBUG", "logger": "backend.services.db_utils", "message": "Auth query 'auth_lookup_e4ae2d8f-b32f-52d7-99cd-8a33b19652c7' successful, result format preserved", "module": "db_utils", "function": "execute_query_with_timeout", "line": 72}
{"timestamp": "2025-06-12T17:26:04.155747", "level": "DEBUG", "logger": "backend.services.auth", "message": "Authentication attempt with token prefix: eyJhbGciOi...", "module": "auth", "function": "get_current_user", "line": 727}
{"timestamp": "2025-06-12T17:26:04.155818", "level": "DEBUG", "logger": "backend.core.security.jwt_keys", "message": "Token contains key ID: a69f821a-65e8-4292-8feb-985dc7ac94d9", "module": "jwt_keys", "function": "decode_token", "line": 188}
{"timestamp": "2025-06-12T17:26:04.155850", "level": "DEBUG", "logger": "backend.core.security.jwt_keys", "message": "Using key ID a69f821a-65e8-4292-8feb-985dc7ac94d9 for verification", "module": "jwt_keys", "function": "decode_token", "line": 195}
{"timestamp": "2025-06-12T17:26:04.155937", "level": "DEBUG", "logger": "backend.services.auth", "message": "JWT decoded successfully with key manager", "module": "auth", "function": "authenticate_token", "line": 683}
{"timestamp": "2025-06-12T17:26:04.157595", "level": "DEBUG", "logger": "backend.services.db_utils", "message": "Query 'auth_lookup_e4ae2d8f-b32f-52d7-99cd-8a33b19652c7' completed in 0.002s", "module": "db_utils", "function": "execute_query_with_timeout", "line": 68}
{"timestamp": "2025-06-12T17:26:04.157629", "level": "DEBUG", "logger": "backend.services.db_utils", "message": "Auth query 'auth_lookup_e4ae2d8f-b32f-52d7-99cd-8a33b19652c7' successful, result format preserved", "module": "db_utils", "function": "execute_query_with_timeout", "line": 72}
{"timestamp": "2025-06-12T17:26:04.157673", "level": "DEBUG", "logger": "backend.services.auth", "message": "Successfully authenticated user: <EMAIL>", "module": "auth", "function": "get_current_user", "line": 732}
{"timestamp": "2025-06-12T17:26:04.157703", "level": "DEBUG", "logger": "backend.services.auth", "message": "Successfully authenticated user: <EMAIL>", "module": "auth", "function": "get_current_user", "line": 738}
{"timestamp": "2025-06-12T17:26:04.160065", "level": "DEBUG", "logger": "backend.routes.qr_preview", "message": "Received QR preview SVG request: {\"qr_type\": \"scooter_unlock\", \"data\": {\"scooter_id\": \"SCOOT123\", \"unlock_code\": \"UNLOCK42\", \"title\": \"Scooter_unlock QR Code\", \"description\": \"Scan to view scooter_unlock content\", \"service_provider\": \"Lime\", \"rate_info\": \"$20\"}, \"designOptions\": {\"size\": 256, \"margin\": 10, \"pattern\": \"square\", \"marker_border_style\": \"square\", \"marker_center_style\": \"square\", \"logo_size\": 0.3, \"logo_position\": \"center\", \"logo_opacity\": 1, \"logo_remove_background\": true, \"frame_style\": \"none\", \"frame_font\": \"SF P", "module": "qr_preview", "function": "preview_qr_svg", "line": 242}
{"timestamp": "2025-06-12T17:26:04.160132", "level": "DEBUG", "logger": "backend.routes.qr_preview", "message": "Form data keys: ['request', 'logo']", "module": "qr_preview", "function": "preview_qr_svg", "line": 297}
{"timestamp": "2025-06-12T17:26:04.161507", "level": "DEBUG", "logger": "backend.routes.qr_preview", "message": "Logo file found: type=<class 'starlette.datastructures.UploadFile'>, filename=LOGO - 1080 - ORANGE.png", "module": "qr_preview", "function": "preview_qr_svg", "line": 300}
{"timestamp": "2025-06-12T17:26:04.161544", "level": "DEBUG", "logger": "backend.routes.qr_preview", "message": "Logo content read: 127404 bytes", "module": "qr_preview", "function": "preview_qr_svg", "line": 305}
{"timestamp": "2025-06-12T17:26:04.161580", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Formatting content for QR type: qrtype.scooter_unlock, content keys: ['scooter_id', 'unlock_code', 'title', 'description', 'service_provider', 'rate_info']", "module": "qr_preview", "function": "_format_qr_data", "line": 876}
{"timestamp": "2025-06-12T17:26:04.183936", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Logo remove background setting: True, has logo: True", "module": "qr_preview", "function": "generate_preview_svg_modular", "line": 727}
{"timestamp": "2025-06-12T17:26:04.183977", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Logo area calculated: (20, 20, 37, 37), size_fraction: 0.3, size_modules: 17", "module": "qr_preview", "function": "generate_preview_svg_modular", "line": 739}
{"timestamp": "2025-06-12T17:26:04.199998", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Skipped 151 modules out of 1668 total modules for logo background removal", "module": "qr_preview", "function": "generate_preview_svg_modular", "line": 766}
{"timestamp": "2025-06-12T17:26:04.200038", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Drawing 3 finder patterns at positions: [(0, 0), (50, 0), (0, 50)]", "module": "qr_preview", "function": "generate_preview_svg_modular", "line": 775}
{"timestamp": "2025-06-12T17:26:04.200069", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "QR size: 57, border_style: square, center_style: square", "module": "qr_preview", "function": "generate_preview_svg_modular", "line": 776}
{"timestamp": "2025-06-12T17:26:04.200100", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Drawing finder pattern 1/3 at position (0, 0)", "module": "qr_preview", "function": "generate_preview_svg_modular", "line": 780}
{"timestamp": "2025-06-12T17:26:04.200184", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "_draw_finder_marker called: mx=0, my=0, module_size=5.087719298245614, border_style=square, center_style=square, border_color=#000000, center_color=#000000, offset=(20, 20)", "module": "qr_preview", "function": "_draw_finder_marker", "line": 97}
{"timestamp": "2025-06-12T17:26:04.200232", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Creating finder pattern group with id='marker-0-0'", "module": "qr_preview", "function": "_draw_finder_marker", "line": 110}
{"timestamp": "2025-06-12T17:26:04.200263", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Drawing square border:", "module": "qr_preview", "function": "_draw_finder_marker", "line": 160}
{"timestamp": "2025-06-12T17:26:04.200294", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "  Outer: insert=(20.0, 20.0), size=(35.614035087719294, 35.614035087719294)", "module": "qr_preview", "function": "_draw_finder_marker", "line": 161}
{"timestamp": "2025-06-12T17:26:04.200328", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "  Inner: insert=(26.105263157894736, 26.105263157894736), size=(23.403508771929822, 23.403508771929822)", "module": "qr_preview", "function": "_draw_finder_marker", "line": 162}
{"timestamp": "2025-06-12T17:26:04.200388", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Drawing marker center: style=square, color=#000000, pos=(32.719298245614034, 32.719298245614034), size=10.175438596491228", "module": "qr_preview", "function": "_draw_marker_center", "line": 179}
{"timestamp": "2025-06-12T17:26:04.200432", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Successfully added finder pattern group to SVG", "module": "qr_preview", "function": "_draw_finder_marker", "line": 171}
{"timestamp": "2025-06-12T17:26:04.200461", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Successfully drew finder pattern 1/3", "module": "qr_preview", "function": "generate_preview_svg_modular", "line": 782}
{"timestamp": "2025-06-12T17:26:04.200528", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Drawing finder pattern 2/3 at position (50, 0)", "module": "qr_preview", "function": "generate_preview_svg_modular", "line": 780}
{"timestamp": "2025-06-12T17:26:04.200559", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "_draw_finder_marker called: mx=50, my=0, module_size=5.087719298245614, border_style=square, center_style=square, border_color=#000000, center_color=#000000, offset=(20, 20)", "module": "qr_preview", "function": "_draw_finder_marker", "line": 97}
{"timestamp": "2025-06-12T17:26:04.200597", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Creating finder pattern group with id='marker-50-0'", "module": "qr_preview", "function": "_draw_finder_marker", "line": 110}
{"timestamp": "2025-06-12T17:26:04.200651", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Drawing square border:", "module": "qr_preview", "function": "_draw_finder_marker", "line": 160}
{"timestamp": "2025-06-12T17:26:04.200681", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "  Outer: insert=(274.38596491228066, 20.0), size=(35.614035087719294, 35.614035087719294)", "module": "qr_preview", "function": "_draw_finder_marker", "line": 161}
{"timestamp": "2025-06-12T17:26:04.200711", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "  Inner: insert=(280.4912280701754, 26.105263157894736), size=(23.403508771929822, 23.403508771929822)", "module": "qr_preview", "function": "_draw_finder_marker", "line": 162}
{"timestamp": "2025-06-12T17:26:04.200766", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Drawing marker center: style=square, color=#000000, pos=(287.10526315789474, 32.719298245614034), size=10.175438596491228", "module": "qr_preview", "function": "_draw_marker_center", "line": 179}
{"timestamp": "2025-06-12T17:26:04.200807", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Successfully added finder pattern group to SVG", "module": "qr_preview", "function": "_draw_finder_marker", "line": 171}
{"timestamp": "2025-06-12T17:26:04.200839", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Successfully drew finder pattern 2/3", "module": "qr_preview", "function": "generate_preview_svg_modular", "line": 782}
{"timestamp": "2025-06-12T17:26:04.200868", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Drawing finder pattern 3/3 at position (0, 50)", "module": "qr_preview", "function": "generate_preview_svg_modular", "line": 780}
{"timestamp": "2025-06-12T17:26:04.200897", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "_draw_finder_marker called: mx=0, my=50, module_size=5.087719298245614, border_style=square, center_style=square, border_color=#000000, center_color=#000000, offset=(20, 20)", "module": "qr_preview", "function": "_draw_finder_marker", "line": 97}
{"timestamp": "2025-06-12T17:26:04.200934", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Creating finder pattern group with id='marker-0-50'", "module": "qr_preview", "function": "_draw_finder_marker", "line": 110}
{"timestamp": "2025-06-12T17:26:04.200966", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Drawing square border:", "module": "qr_preview", "function": "_draw_finder_marker", "line": 160}
{"timestamp": "2025-06-12T17:26:04.200997", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "  Outer: insert=(20.0, 274.38596491228066), size=(35.614035087719294, 35.614035087719294)", "module": "qr_preview", "function": "_draw_finder_marker", "line": 161}
{"timestamp": "2025-06-12T17:26:04.201031", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "  Inner: insert=(26.105263157894736, 280.4912280701754), size=(23.403508771929822, 23.403508771929822)", "module": "qr_preview", "function": "_draw_finder_marker", "line": 162}
{"timestamp": "2025-06-12T17:26:04.201083", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Drawing marker center: style=square, color=#000000, pos=(32.719298245614034, 287.10526315789474), size=10.175438596491228", "module": "qr_preview", "function": "_draw_marker_center", "line": 179}
{"timestamp": "2025-06-12T17:26:04.201123", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Successfully added finder pattern group to SVG", "module": "qr_preview", "function": "_draw_finder_marker", "line": 171}
{"timestamp": "2025-06-12T17:26:04.201434", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Successfully drew finder pattern 3/3", "module": "qr_preview", "function": "generate_preview_svg_modular", "line": 782}
{"timestamp": "2025-06-12T17:26:04.201485", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "_draw_logo called with logo type: <class 'bytes'>, dimensions: 300", "module": "qr_preview", "function": "_draw_logo", "line": 434}
{"timestamp": "2025-06-12T17:26:04.526104", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Generated SVG length: 227445", "module": "qr_preview", "function": "generate_preview_svg_modular", "line": 802}
{"timestamp": "2025-06-12T17:26:04.526250", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "SVG contains marker-0-0: True", "module": "qr_preview", "function": "generate_preview_svg_modular", "line": 803}
{"timestamp": "2025-06-12T17:26:04.526365", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "SVG contains marker-18-0: False", "module": "qr_preview", "function": "generate_preview_svg_modular", "line": 804}
{"timestamp": "2025-06-12T17:26:04.526480", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "SVG contains marker-0-18: False", "module": "qr_preview", "function": "generate_preview_svg_modular", "line": 805}
{"timestamp": "2025-06-12T17:26:04.526822", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "SVG marker counts - outer: 3, inner: 3, dot: 0", "module": "qr_preview", "function": "generate_preview_svg_modular", "line": 813}
{"timestamp": "2025-06-12T17:26:04.527029", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "SVG label counts - text: 0, background: 0", "module": "qr_preview", "function": "generate_preview_svg_modular", "line": 814}
{"timestamp": "2025-06-12T17:26:04.528779", "level": "DEBUG", "logger": "backend.main", "message": "[Auth] Enforced Bearer token authentication for request", "module": "main", "function": "bearer_token_auth_middleware", "line": 274}
{"timestamp": "2025-06-12T17:26:05.368789", "level": "DEBUG", "logger": "backend.core.security.jwt_keys", "message": "Token contains key ID: a69f821a-65e8-4292-8feb-985dc7ac94d9", "module": "jwt_keys", "function": "decode_token", "line": 188}
{"timestamp": "2025-06-12T17:26:05.368904", "level": "DEBUG", "logger": "backend.core.security.jwt_keys", "message": "Using key ID a69f821a-65e8-4292-8feb-985dc7ac94d9 for verification", "module": "jwt_keys", "function": "decode_token", "line": 195}
{"timestamp": "2025-06-12T17:26:05.369439", "level": "DEBUG", "logger": "backend.services.auth", "message": "JWT decoded successfully with key manager", "module": "auth", "function": "authenticate_token", "line": 683}
{"timestamp": "2025-06-12T17:26:05.373256", "level": "DEBUG", "logger": "backend.services.db_utils", "message": "Query 'auth_lookup_e4ae2d8f-b32f-52d7-99cd-8a33b19652c7' completed in 0.003s", "module": "db_utils", "function": "execute_query_with_timeout", "line": 68}
{"timestamp": "2025-06-12T17:26:05.373311", "level": "DEBUG", "logger": "backend.services.db_utils", "message": "Auth query 'auth_lookup_e4ae2d8f-b32f-52d7-99cd-8a33b19652c7' successful, result format preserved", "module": "db_utils", "function": "execute_query_with_timeout", "line": 72}
{"timestamp": "2025-06-12T17:26:05.385033", "level": "DEBUG", "logger": "backend.services.auth", "message": "Authentication attempt with token prefix: eyJhbGciOi...", "module": "auth", "function": "get_current_user", "line": 727}
{"timestamp": "2025-06-12T17:26:05.385151", "level": "DEBUG", "logger": "backend.core.security.jwt_keys", "message": "Token contains key ID: a69f821a-65e8-4292-8feb-985dc7ac94d9", "module": "jwt_keys", "function": "decode_token", "line": 188}
{"timestamp": "2025-06-12T17:26:05.385192", "level": "DEBUG", "logger": "backend.core.security.jwt_keys", "message": "Using key ID a69f821a-65e8-4292-8feb-985dc7ac94d9 for verification", "module": "jwt_keys", "function": "decode_token", "line": 195}
{"timestamp": "2025-06-12T17:26:05.385584", "level": "DEBUG", "logger": "backend.services.auth", "message": "JWT decoded successfully with key manager", "module": "auth", "function": "authenticate_token", "line": 683}
{"timestamp": "2025-06-12T17:26:05.404714", "level": "DEBUG", "logger": "backend.services.db_utils", "message": "Query 'auth_lookup_e4ae2d8f-b32f-52d7-99cd-8a33b19652c7' completed in 0.008s", "module": "db_utils", "function": "execute_query_with_timeout", "line": 68}
{"timestamp": "2025-06-12T17:26:05.404804", "level": "DEBUG", "logger": "backend.services.db_utils", "message": "Auth query 'auth_lookup_e4ae2d8f-b32f-52d7-99cd-8a33b19652c7' successful, result format preserved", "module": "db_utils", "function": "execute_query_with_timeout", "line": 72}
{"timestamp": "2025-06-12T17:26:05.404886", "level": "DEBUG", "logger": "backend.services.auth", "message": "Successfully authenticated user: <EMAIL>", "module": "auth", "function": "get_current_user", "line": 732}
{"timestamp": "2025-06-12T17:26:05.404923", "level": "DEBUG", "logger": "backend.services.auth", "message": "Successfully authenticated user: <EMAIL>", "module": "auth", "function": "get_current_user", "line": 738}
{"timestamp": "2025-06-12T17:26:05.415052", "level": "DEBUG", "logger": "backend.routes.qr_preview", "message": "Received QR preview SVG request: {\"qr_type\": \"scooter_unlock\", \"data\": {\"scooter_id\": \"SCOOT123\", \"unlock_code\": \"UNLOCK42\", \"title\": \"Scooter_unlock QR Code\", \"description\": \"Scan to view scooter_unlock content\", \"service_provider\": \"Lime\", \"rate_info\": \"$20\"}, \"designOptions\": {\"size\": 256, \"margin\": 10, \"pattern\": \"square\", \"marker_border_style\": \"square\", \"marker_border_color\": \"#000000\", \"marker_center_style\": \"square\", \"marker_center_color\": \"#000000\", \"logo_size\": 0.3, \"logo_position\": \"center\", \"logo_opacity\": 1, \"frame", "module": "qr_preview", "function": "preview_qr_svg", "line": 242}
{"timestamp": "2025-06-12T17:26:05.415744", "level": "DEBUG", "logger": "backend.routes.qr_preview", "message": "Form data keys: ['request', 'logo']", "module": "qr_preview", "function": "preview_qr_svg", "line": 297}
{"timestamp": "2025-06-12T17:26:05.415808", "level": "DEBUG", "logger": "backend.routes.qr_preview", "message": "Logo file found: type=<class 'starlette.datastructures.UploadFile'>, filename=LOGO - 1080 - ORANGE.png", "module": "qr_preview", "function": "preview_qr_svg", "line": 300}
{"timestamp": "2025-06-12T17:26:05.415857", "level": "DEBUG", "logger": "backend.routes.qr_preview", "message": "Logo content read: 127404 bytes", "module": "qr_preview", "function": "preview_qr_svg", "line": 305}
{"timestamp": "2025-06-12T17:26:05.421171", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Formatting content for QR type: qrtype.scooter_unlock, content keys: ['scooter_id', 'unlock_code', 'title', 'description', 'service_provider', 'rate_info']", "module": "qr_preview", "function": "_format_qr_data", "line": 876}
{"timestamp": "2025-06-12T17:26:05.451323", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Logo remove background setting: False, has logo: True", "module": "qr_preview", "function": "generate_preview_svg_modular", "line": 727}
{"timestamp": "2025-06-12T17:26:05.451374", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Logo present but background removal disabled", "module": "qr_preview", "function": "generate_preview_svg_modular", "line": 741}
{"timestamp": "2025-06-12T17:26:05.474699", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Drawing 3 finder patterns at positions: [(0, 0), (50, 0), (0, 50)]", "module": "qr_preview", "function": "generate_preview_svg_modular", "line": 775}
{"timestamp": "2025-06-12T17:26:05.474759", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "QR size: 57, border_style: square, center_style: square", "module": "qr_preview", "function": "generate_preview_svg_modular", "line": 776}
{"timestamp": "2025-06-12T17:26:05.474899", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Drawing finder pattern 1/3 at position (0, 0)", "module": "qr_preview", "function": "generate_preview_svg_modular", "line": 780}
{"timestamp": "2025-06-12T17:26:05.475813", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "_draw_finder_marker called: mx=0, my=0, module_size=5.087719298245614, border_style=square, center_style=square, border_color=#000000, center_color=#000000, offset=(20, 20)", "module": "qr_preview", "function": "_draw_finder_marker", "line": 97}
{"timestamp": "2025-06-12T17:26:05.481809", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Creating finder pattern group with id='marker-0-0'", "module": "qr_preview", "function": "_draw_finder_marker", "line": 110}
{"timestamp": "2025-06-12T17:26:05.510013", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Drawing square border:", "module": "qr_preview", "function": "_draw_finder_marker", "line": 160}
{"timestamp": "2025-06-12T17:26:05.513429", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "  Outer: insert=(20.0, 20.0), size=(35.614035087719294, 35.614035087719294)", "module": "qr_preview", "function": "_draw_finder_marker", "line": 161}
{"timestamp": "2025-06-12T17:26:05.522524", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "  Inner: insert=(26.105263157894736, 26.105263157894736), size=(23.403508771929822, 23.403508771929822)", "module": "qr_preview", "function": "_draw_finder_marker", "line": 162}
{"timestamp": "2025-06-12T17:26:05.522867", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Drawing marker center: style=square, color=#000000, pos=(32.719298245614034, 32.719298245614034), size=10.175438596491228", "module": "qr_preview", "function": "_draw_marker_center", "line": 179}
{"timestamp": "2025-06-12T17:26:05.529725", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Successfully added finder pattern group to SVG", "module": "qr_preview", "function": "_draw_finder_marker", "line": 171}
{"timestamp": "2025-06-12T17:26:05.529776", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Successfully drew finder pattern 1/3", "module": "qr_preview", "function": "generate_preview_svg_modular", "line": 782}
{"timestamp": "2025-06-12T17:26:05.529817", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Drawing finder pattern 2/3 at position (50, 0)", "module": "qr_preview", "function": "generate_preview_svg_modular", "line": 780}
{"timestamp": "2025-06-12T17:26:05.529860", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "_draw_finder_marker called: mx=50, my=0, module_size=5.087719298245614, border_style=square, center_style=square, border_color=#000000, center_color=#000000, offset=(20, 20)", "module": "qr_preview", "function": "_draw_finder_marker", "line": 97}
{"timestamp": "2025-06-12T17:26:05.529914", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Creating finder pattern group with id='marker-50-0'", "module": "qr_preview", "function": "_draw_finder_marker", "line": 110}
{"timestamp": "2025-06-12T17:26:05.529954", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Drawing square border:", "module": "qr_preview", "function": "_draw_finder_marker", "line": 160}
{"timestamp": "2025-06-12T17:26:05.529996", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "  Outer: insert=(274.38596491228066, 20.0), size=(35.614035087719294, 35.614035087719294)", "module": "qr_preview", "function": "_draw_finder_marker", "line": 161}
{"timestamp": "2025-06-12T17:26:05.530030", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "  Inner: insert=(280.4912280701754, 26.105263157894736), size=(23.403508771929822, 23.403508771929822)", "module": "qr_preview", "function": "_draw_finder_marker", "line": 162}
{"timestamp": "2025-06-12T17:26:05.530113", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Drawing marker center: style=square, color=#000000, pos=(287.10526315789474, 32.719298245614034), size=10.175438596491228", "module": "qr_preview", "function": "_draw_marker_center", "line": 179}
{"timestamp": "2025-06-12T17:26:05.530564", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Successfully added finder pattern group to SVG", "module": "qr_preview", "function": "_draw_finder_marker", "line": 171}
{"timestamp": "2025-06-12T17:26:05.530614", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Successfully drew finder pattern 2/3", "module": "qr_preview", "function": "generate_preview_svg_modular", "line": 782}
{"timestamp": "2025-06-12T17:26:05.530648", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Drawing finder pattern 3/3 at position (0, 50)", "module": "qr_preview", "function": "generate_preview_svg_modular", "line": 780}
{"timestamp": "2025-06-12T17:26:05.530687", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "_draw_finder_marker called: mx=0, my=50, module_size=5.087719298245614, border_style=square, center_style=square, border_color=#000000, center_color=#000000, offset=(20, 20)", "module": "qr_preview", "function": "_draw_finder_marker", "line": 97}
{"timestamp": "2025-06-12T17:26:05.530734", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Creating finder pattern group with id='marker-0-50'", "module": "qr_preview", "function": "_draw_finder_marker", "line": 110}
{"timestamp": "2025-06-12T17:26:05.530764", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Drawing square border:", "module": "qr_preview", "function": "_draw_finder_marker", "line": 160}
{"timestamp": "2025-06-12T17:26:05.530873", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "  Outer: insert=(20.0, 274.38596491228066), size=(35.614035087719294, 35.614035087719294)", "module": "qr_preview", "function": "_draw_finder_marker", "line": 161}
{"timestamp": "2025-06-12T17:26:05.530970", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "  Inner: insert=(26.105263157894736, 280.4912280701754), size=(23.403508771929822, 23.403508771929822)", "module": "qr_preview", "function": "_draw_finder_marker", "line": 162}
{"timestamp": "2025-06-12T17:26:05.531053", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Drawing marker center: style=square, color=#000000, pos=(32.719298245614034, 287.10526315789474), size=10.175438596491228", "module": "qr_preview", "function": "_draw_marker_center", "line": 179}
{"timestamp": "2025-06-12T17:26:05.531109", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Successfully added finder pattern group to SVG", "module": "qr_preview", "function": "_draw_finder_marker", "line": 171}
{"timestamp": "2025-06-12T17:26:05.531144", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Successfully drew finder pattern 3/3", "module": "qr_preview", "function": "generate_preview_svg_modular", "line": 782}
{"timestamp": "2025-06-12T17:26:05.531200", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "_draw_logo called with logo type: <class 'bytes'>, dimensions: 300", "module": "qr_preview", "function": "_draw_logo", "line": 434}
{"timestamp": "2025-06-12T17:26:06.009408", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Generated SVG length: 251209", "module": "qr_preview", "function": "generate_preview_svg_modular", "line": 802}
{"timestamp": "2025-06-12T17:26:06.009700", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "SVG contains marker-0-0: True", "module": "qr_preview", "function": "generate_preview_svg_modular", "line": 803}
{"timestamp": "2025-06-12T17:26:06.009930", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "SVG contains marker-18-0: False", "module": "qr_preview", "function": "generate_preview_svg_modular", "line": 804}
{"timestamp": "2025-06-12T17:26:06.010174", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "SVG contains marker-0-18: False", "module": "qr_preview", "function": "generate_preview_svg_modular", "line": 805}
{"timestamp": "2025-06-12T17:26:06.010796", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "SVG marker counts - outer: 3, inner: 3, dot: 0", "module": "qr_preview", "function": "generate_preview_svg_modular", "line": 813}
{"timestamp": "2025-06-12T17:26:06.011041", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "SVG label counts - text: 0, background: 0", "module": "qr_preview", "function": "generate_preview_svg_modular", "line": 814}
{"timestamp": "2025-06-12T17:26:06.021556", "level": "DEBUG", "logger": "backend.main", "message": "[Auth] Enforced Bearer token authentication for request", "module": "main", "function": "bearer_token_auth_middleware", "line": 274}
{"timestamp": "2025-06-12T17:26:28.772049", "level": "DEBUG", "logger": "backend.main", "message": "[Auth] Enforced Bearer token authentication for request", "module": "main", "function": "bearer_token_auth_middleware", "line": 274}
{"timestamp": "2025-06-12T17:26:58.032087", "level": "DEBUG", "logger": "backend.main", "message": "[Auth] Enforced Bearer token authentication for request", "module": "main", "function": "bearer_token_auth_middleware", "line": 274}
{"timestamp": "2025-06-12T17:27:00.002938", "level": "DEBUG", "logger": "backend.main", "message": "[Auth] Enforced Bearer token authentication for request", "module": "main", "function": "bearer_token_auth_middleware", "line": 274}
{"timestamp": "2025-06-12T17:27:22.995369", "level": "DEBUG", "logger": "backend.core.security.jwt_keys", "message": "Token contains key ID: a69f821a-65e8-4292-8feb-985dc7ac94d9", "module": "jwt_keys", "function": "decode_token", "line": 188}
{"timestamp": "2025-06-12T17:27:22.995465", "level": "DEBUG", "logger": "backend.core.security.jwt_keys", "message": "Using key ID a69f821a-65e8-4292-8feb-985dc7ac94d9 for verification", "module": "jwt_keys", "function": "decode_token", "line": 195}
{"timestamp": "2025-06-12T17:27:22.995992", "level": "DEBUG", "logger": "backend.services.auth", "message": "JWT decoded successfully with key manager", "module": "auth", "function": "authenticate_token", "line": 683}
{"timestamp": "2025-06-12T17:27:23.019796", "level": "DEBUG", "logger": "backend.services.db_utils", "message": "Query 'auth_lookup_e4ae2d8f-b32f-52d7-99cd-8a33b19652c7' completed in 0.021s", "module": "db_utils", "function": "execute_query_with_timeout", "line": 68}
{"timestamp": "2025-06-12T17:27:23.019893", "level": "DEBUG", "logger": "backend.services.db_utils", "message": "Auth query 'auth_lookup_e4ae2d8f-b32f-52d7-99cd-8a33b19652c7' successful, result format preserved", "module": "db_utils", "function": "execute_query_with_timeout", "line": 72}
{"timestamp": "2025-06-12T17:27:23.030454", "level": "DEBUG", "logger": "backend.main", "message": "[Auth] Enforced Bearer token authentication for request", "module": "main", "function": "bearer_token_auth_middleware", "line": 274}
{"timestamp": "2025-06-12T17:27:23.383776", "level": "DEBUG", "logger": "backend.core.security.jwt_keys", "message": "Token contains key ID: a69f821a-65e8-4292-8feb-985dc7ac94d9", "module": "jwt_keys", "function": "decode_token", "line": 188}
{"timestamp": "2025-06-12T17:27:23.383843", "level": "DEBUG", "logger": "backend.core.security.jwt_keys", "message": "Using key ID a69f821a-65e8-4292-8feb-985dc7ac94d9 for verification", "module": "jwt_keys", "function": "decode_token", "line": 195}
{"timestamp": "2025-06-12T17:27:23.383989", "level": "DEBUG", "logger": "backend.services.auth", "message": "JWT decoded successfully with key manager", "module": "auth", "function": "authenticate_token", "line": 683}
{"timestamp": "2025-06-12T17:27:23.390063", "level": "DEBUG", "logger": "backend.services.db_utils", "message": "Query 'auth_lookup_e4ae2d8f-b32f-52d7-99cd-8a33b19652c7' completed in 0.006s", "module": "db_utils", "function": "execute_query_with_timeout", "line": 68}
{"timestamp": "2025-06-12T17:27:23.390163", "level": "DEBUG", "logger": "backend.services.db_utils", "message": "Auth query 'auth_lookup_e4ae2d8f-b32f-52d7-99cd-8a33b19652c7' successful, result format preserved", "module": "db_utils", "function": "execute_query_with_timeout", "line": 72}
{"timestamp": "2025-06-12T17:27:23.398846", "level": "DEBUG", "logger": "backend.services.auth", "message": "Authentication attempt with token prefix: eyJhbGciOi...", "module": "auth", "function": "get_current_user", "line": 727}
{"timestamp": "2025-06-12T17:27:23.398993", "level": "DEBUG", "logger": "backend.core.security.jwt_keys", "message": "Token contains key ID: a69f821a-65e8-4292-8feb-985dc7ac94d9", "module": "jwt_keys", "function": "decode_token", "line": 188}
{"timestamp": "2025-06-12T17:27:23.399046", "level": "DEBUG", "logger": "backend.core.security.jwt_keys", "message": "Using key ID a69f821a-65e8-4292-8feb-985dc7ac94d9 for verification", "module": "jwt_keys", "function": "decode_token", "line": 195}
{"timestamp": "2025-06-12T17:27:23.399183", "level": "DEBUG", "logger": "backend.services.auth", "message": "JWT decoded successfully with key manager", "module": "auth", "function": "authenticate_token", "line": 683}
{"timestamp": "2025-06-12T17:27:23.401123", "level": "DEBUG", "logger": "backend.services.db_utils", "message": "Query 'auth_lookup_e4ae2d8f-b32f-52d7-99cd-8a33b19652c7' completed in 0.002s", "module": "db_utils", "function": "execute_query_with_timeout", "line": 68}
{"timestamp": "2025-06-12T17:27:23.401185", "level": "DEBUG", "logger": "backend.services.db_utils", "message": "Auth query 'auth_lookup_e4ae2d8f-b32f-52d7-99cd-8a33b19652c7' successful, result format preserved", "module": "db_utils", "function": "execute_query_with_timeout", "line": 72}
{"timestamp": "2025-06-12T17:27:23.401257", "level": "DEBUG", "logger": "backend.services.auth", "message": "Successfully authenticated user: <EMAIL>", "module": "auth", "function": "get_current_user", "line": 732}
{"timestamp": "2025-06-12T17:27:23.401308", "level": "DEBUG", "logger": "backend.services.auth", "message": "Successfully authenticated user: <EMAIL>", "module": "auth", "function": "get_current_user", "line": 738}
{"timestamp": "2025-06-12T17:27:23.407300", "level": "DEBUG", "logger": "backend.routes.qr_preview", "message": "Received QR preview SVG request: {\"qr_type\": \"scooter_unlock\", \"data\": {\"scooter_id\": \"SCOOT123\", \"unlock_code\": \"UNLOCK42\", \"title\": \"Scooter_unlock QR Code\", \"description\": \"Scan to view scooter_unlock content\", \"service_provider\": \"Lime\", \"rate_info\": \"$20\"}, \"designOptions\": {\"size\": 256, \"margin\": 10, \"pattern\": \"square\", \"marker_border_style\": \"square\", \"marker_center_style\": \"square\", \"logo_size\": 0.3, \"logo_position\": \"center\", \"logo_opacity\": 1, \"logo_remove_background\": true, \"frame_style\": \"none\", \"frame_font\": \"SF P", "module": "qr_preview", "function": "preview_qr_svg", "line": 242}
{"timestamp": "2025-06-12T17:27:23.407604", "level": "DEBUG", "logger": "backend.routes.qr_preview", "message": "Form data keys: ['request', 'logo']", "module": "qr_preview", "function": "preview_qr_svg", "line": 297}
{"timestamp": "2025-06-12T17:27:23.407659", "level": "DEBUG", "logger": "backend.routes.qr_preview", "message": "Logo file found: type=<class 'starlette.datastructures.UploadFile'>, filename=LOGO - 1080 - ORANGE.png", "module": "qr_preview", "function": "preview_qr_svg", "line": 300}
{"timestamp": "2025-06-12T17:27:23.407703", "level": "DEBUG", "logger": "backend.routes.qr_preview", "message": "Logo content read: 127404 bytes", "module": "qr_preview", "function": "preview_qr_svg", "line": 305}
{"timestamp": "2025-06-12T17:27:23.407804", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Formatting content for QR type: qrtype.scooter_unlock, content keys: ['scooter_id', 'unlock_code', 'title', 'description', 'service_provider', 'rate_info']", "module": "qr_preview", "function": "_format_qr_data", "line": 876}
{"timestamp": "2025-06-12T17:27:23.436937", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Logo remove background setting: True, has logo: True", "module": "qr_preview", "function": "generate_preview_svg_modular", "line": 727}
{"timestamp": "2025-06-12T17:27:23.437033", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Logo area calculated: (20, 20, 37, 37), size_fraction: 0.3, size_modules: 17", "module": "qr_preview", "function": "generate_preview_svg_modular", "line": 739}
{"timestamp": "2025-06-12T17:27:23.454882", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Skipped 151 modules out of 1668 total modules for logo background removal", "module": "qr_preview", "function": "generate_preview_svg_modular", "line": 766}
{"timestamp": "2025-06-12T17:27:23.454926", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Drawing 3 finder patterns at positions: [(0, 0), (50, 0), (0, 50)]", "module": "qr_preview", "function": "generate_preview_svg_modular", "line": 775}
{"timestamp": "2025-06-12T17:27:23.454957", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "QR size: 57, border_style: square, center_style: square", "module": "qr_preview", "function": "generate_preview_svg_modular", "line": 776}
{"timestamp": "2025-06-12T17:27:23.454987", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Drawing finder pattern 1/3 at position (0, 0)", "module": "qr_preview", "function": "generate_preview_svg_modular", "line": 780}
{"timestamp": "2025-06-12T17:27:23.455028", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "_draw_finder_marker called: mx=0, my=0, module_size=5.087719298245614, border_style=square, center_style=square, border_color=#000000, center_color=#000000, offset=(20, 20)", "module": "qr_preview", "function": "_draw_finder_marker", "line": 97}
{"timestamp": "2025-06-12T17:27:23.455078", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Creating finder pattern group with id='marker-0-0'", "module": "qr_preview", "function": "_draw_finder_marker", "line": 110}
{"timestamp": "2025-06-12T17:27:23.455108", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Drawing square border:", "module": "qr_preview", "function": "_draw_finder_marker", "line": 160}
{"timestamp": "2025-06-12T17:27:23.455139", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "  Outer: insert=(20.0, 20.0), size=(35.614035087719294, 35.614035087719294)", "module": "qr_preview", "function": "_draw_finder_marker", "line": 161}
{"timestamp": "2025-06-12T17:27:23.455169", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "  Inner: insert=(26.105263157894736, 26.105263157894736), size=(23.403508771929822, 23.403508771929822)", "module": "qr_preview", "function": "_draw_finder_marker", "line": 162}
{"timestamp": "2025-06-12T17:27:23.455228", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Drawing marker center: style=square, color=#000000, pos=(32.719298245614034, 32.719298245614034), size=10.175438596491228", "module": "qr_preview", "function": "_draw_marker_center", "line": 179}
{"timestamp": "2025-06-12T17:27:23.455284", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Successfully added finder pattern group to SVG", "module": "qr_preview", "function": "_draw_finder_marker", "line": 171}
{"timestamp": "2025-06-12T17:27:23.455313", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Successfully drew finder pattern 1/3", "module": "qr_preview", "function": "generate_preview_svg_modular", "line": 782}
{"timestamp": "2025-06-12T17:27:23.455340", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Drawing finder pattern 2/3 at position (50, 0)", "module": "qr_preview", "function": "generate_preview_svg_modular", "line": 780}
{"timestamp": "2025-06-12T17:27:23.455368", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "_draw_finder_marker called: mx=50, my=0, module_size=5.087719298245614, border_style=square, center_style=square, border_color=#000000, center_color=#000000, offset=(20, 20)", "module": "qr_preview", "function": "_draw_finder_marker", "line": 97}
{"timestamp": "2025-06-12T17:27:23.455425", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Creating finder pattern group with id='marker-50-0'", "module": "qr_preview", "function": "_draw_finder_marker", "line": 110}
{"timestamp": "2025-06-12T17:27:23.455452", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Drawing square border:", "module": "qr_preview", "function": "_draw_finder_marker", "line": 160}
{"timestamp": "2025-06-12T17:27:23.455482", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "  Outer: insert=(274.38596491228066, 20.0), size=(35.614035087719294, 35.614035087719294)", "module": "qr_preview", "function": "_draw_finder_marker", "line": 161}
{"timestamp": "2025-06-12T17:27:23.455511", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "  Inner: insert=(280.4912280701754, 26.105263157894736), size=(23.403508771929822, 23.403508771929822)", "module": "qr_preview", "function": "_draw_finder_marker", "line": 162}
{"timestamp": "2025-06-12T17:27:23.455566", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Drawing marker center: style=square, color=#000000, pos=(287.10526315789474, 32.719298245614034), size=10.175438596491228", "module": "qr_preview", "function": "_draw_marker_center", "line": 179}
{"timestamp": "2025-06-12T17:27:23.455611", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Successfully added finder pattern group to SVG", "module": "qr_preview", "function": "_draw_finder_marker", "line": 171}
{"timestamp": "2025-06-12T17:27:23.455637", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Successfully drew finder pattern 2/3", "module": "qr_preview", "function": "generate_preview_svg_modular", "line": 782}
{"timestamp": "2025-06-12T17:27:23.455663", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Drawing finder pattern 3/3 at position (0, 50)", "module": "qr_preview", "function": "generate_preview_svg_modular", "line": 780}
{"timestamp": "2025-06-12T17:27:23.455691", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "_draw_finder_marker called: mx=0, my=50, module_size=5.087719298245614, border_style=square, center_style=square, border_color=#000000, center_color=#000000, offset=(20, 20)", "module": "qr_preview", "function": "_draw_finder_marker", "line": 97}
{"timestamp": "2025-06-12T17:27:23.455726", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Creating finder pattern group with id='marker-0-50'", "module": "qr_preview", "function": "_draw_finder_marker", "line": 110}
{"timestamp": "2025-06-12T17:27:23.455751", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Drawing square border:", "module": "qr_preview", "function": "_draw_finder_marker", "line": 160}
{"timestamp": "2025-06-12T17:27:23.455781", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "  Outer: insert=(20.0, 274.38596491228066), size=(35.614035087719294, 35.614035087719294)", "module": "qr_preview", "function": "_draw_finder_marker", "line": 161}
{"timestamp": "2025-06-12T17:27:23.455809", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "  Inner: insert=(26.105263157894736, 280.4912280701754), size=(23.403508771929822, 23.403508771929822)", "module": "qr_preview", "function": "_draw_finder_marker", "line": 162}
{"timestamp": "2025-06-12T17:27:23.455859", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Drawing marker center: style=square, color=#000000, pos=(32.719298245614034, 287.10526315789474), size=10.175438596491228", "module": "qr_preview", "function": "_draw_marker_center", "line": 179}
{"timestamp": "2025-06-12T17:27:23.455899", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Successfully added finder pattern group to SVG", "module": "qr_preview", "function": "_draw_finder_marker", "line": 171}
{"timestamp": "2025-06-12T17:27:23.455925", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Successfully drew finder pattern 3/3", "module": "qr_preview", "function": "generate_preview_svg_modular", "line": 782}
{"timestamp": "2025-06-12T17:27:23.456007", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "_draw_logo called with logo type: <class 'bytes'>, dimensions: 300", "module": "qr_preview", "function": "_draw_logo", "line": 434}
{"timestamp": "2025-06-12T17:27:23.805978", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Generated SVG length: 227445", "module": "qr_preview", "function": "generate_preview_svg_modular", "line": 802}
{"timestamp": "2025-06-12T17:27:23.806136", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "SVG contains marker-0-0: True", "module": "qr_preview", "function": "generate_preview_svg_modular", "line": 803}
{"timestamp": "2025-06-12T17:27:23.806265", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "SVG contains marker-18-0: False", "module": "qr_preview", "function": "generate_preview_svg_modular", "line": 804}
{"timestamp": "2025-06-12T17:27:23.806398", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "SVG contains marker-0-18: False", "module": "qr_preview", "function": "generate_preview_svg_modular", "line": 805}
{"timestamp": "2025-06-12T17:27:23.806760", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "SVG marker counts - outer: 3, inner: 3, dot: 0", "module": "qr_preview", "function": "generate_preview_svg_modular", "line": 813}
{"timestamp": "2025-06-12T17:27:23.806795", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "SVG label counts - text: 0, background: 0", "module": "qr_preview", "function": "generate_preview_svg_modular", "line": 814}
{"timestamp": "2025-06-12T17:27:23.810155", "level": "DEBUG", "logger": "backend.main", "message": "[Auth] Enforced Bearer token authentication for request", "module": "main", "function": "bearer_token_auth_middleware", "line": 274}
{"timestamp": "2025-06-12T17:27:23.883410", "level": "DEBUG", "logger": "backend.core.security.jwt_keys", "message": "Token contains key ID: a69f821a-65e8-4292-8feb-985dc7ac94d9", "module": "jwt_keys", "function": "decode_token", "line": 188}
{"timestamp": "2025-06-12T17:27:23.883477", "level": "DEBUG", "logger": "backend.core.security.jwt_keys", "message": "Using key ID a69f821a-65e8-4292-8feb-985dc7ac94d9 for verification", "module": "jwt_keys", "function": "decode_token", "line": 195}
{"timestamp": "2025-06-12T17:27:23.883624", "level": "DEBUG", "logger": "backend.services.auth", "message": "JWT decoded successfully with key manager", "module": "auth", "function": "authenticate_token", "line": 683}
{"timestamp": "2025-06-12T17:27:23.885945", "level": "DEBUG", "logger": "backend.services.db_utils", "message": "Query 'auth_lookup_e4ae2d8f-b32f-52d7-99cd-8a33b19652c7' completed in 0.002s", "module": "db_utils", "function": "execute_query_with_timeout", "line": 68}
{"timestamp": "2025-06-12T17:27:23.886033", "level": "DEBUG", "logger": "backend.services.db_utils", "message": "Auth query 'auth_lookup_e4ae2d8f-b32f-52d7-99cd-8a33b19652c7' successful, result format preserved", "module": "db_utils", "function": "execute_query_with_timeout", "line": 72}
{"timestamp": "2025-06-12T17:27:23.892901", "level": "DEBUG", "logger": "backend.services.auth", "message": "Authentication attempt with token prefix: eyJhbGciOi...", "module": "auth", "function": "get_current_user", "line": 727}
{"timestamp": "2025-06-12T17:27:23.893025", "level": "DEBUG", "logger": "backend.core.security.jwt_keys", "message": "Token contains key ID: a69f821a-65e8-4292-8feb-985dc7ac94d9", "module": "jwt_keys", "function": "decode_token", "line": 188}
{"timestamp": "2025-06-12T17:27:23.893065", "level": "DEBUG", "logger": "backend.core.security.jwt_keys", "message": "Using key ID a69f821a-65e8-4292-8feb-985dc7ac94d9 for verification", "module": "jwt_keys", "function": "decode_token", "line": 195}
{"timestamp": "2025-06-12T17:27:23.893208", "level": "DEBUG", "logger": "backend.services.auth", "message": "JWT decoded successfully with key manager", "module": "auth", "function": "authenticate_token", "line": 683}
{"timestamp": "2025-06-12T17:27:23.895612", "level": "DEBUG", "logger": "backend.services.db_utils", "message": "Query 'auth_lookup_e4ae2d8f-b32f-52d7-99cd-8a33b19652c7' completed in 0.002s", "module": "db_utils", "function": "execute_query_with_timeout", "line": 68}
{"timestamp": "2025-06-12T17:27:23.895657", "level": "DEBUG", "logger": "backend.services.db_utils", "message": "Auth query 'auth_lookup_e4ae2d8f-b32f-52d7-99cd-8a33b19652c7' successful, result format preserved", "module": "db_utils", "function": "execute_query_with_timeout", "line": 72}
{"timestamp": "2025-06-12T17:27:23.895713", "level": "DEBUG", "logger": "backend.services.auth", "message": "Successfully authenticated user: <EMAIL>", "module": "auth", "function": "get_current_user", "line": 732}
{"timestamp": "2025-06-12T17:27:23.895746", "level": "DEBUG", "logger": "backend.services.auth", "message": "Successfully authenticated user: <EMAIL>", "module": "auth", "function": "get_current_user", "line": 738}
{"timestamp": "2025-06-12T17:27:23.899309", "level": "DEBUG", "logger": "backend.routes.qr_preview", "message": "Received QR preview SVG request: {\"qr_type\": \"scooter_unlock\", \"data\": {\"scooter_id\": \"SCOOT123\", \"unlock_code\": \"UNLOCK42\", \"title\": \"Scooter_unlock QR Code\", \"description\": \"Scan to view scooter_unlock content\", \"service_provider\": \"Lime\", \"rate_info\": \"$20\"}, \"designOptions\": {\"size\": 256, \"margin\": 10, \"pattern\": \"square\", \"marker_border_style\": \"square\", \"marker_border_color\": \"#000000\", \"marker_center_style\": \"square\", \"marker_center_color\": \"#000000\", \"logo_size\": 0.3, \"logo_position\": \"center\", \"logo_opacity\": 1, \"frame", "module": "qr_preview", "function": "preview_qr_svg", "line": 242}
{"timestamp": "2025-06-12T17:27:23.899409", "level": "DEBUG", "logger": "backend.routes.qr_preview", "message": "Form data keys: ['request', 'logo']", "module": "qr_preview", "function": "preview_qr_svg", "line": 297}
{"timestamp": "2025-06-12T17:27:23.899442", "level": "DEBUG", "logger": "backend.routes.qr_preview", "message": "Logo file found: type=<class 'starlette.datastructures.UploadFile'>, filename=LOGO - 1080 - ORANGE.png", "module": "qr_preview", "function": "preview_qr_svg", "line": 300}
{"timestamp": "2025-06-12T17:27:23.899487", "level": "DEBUG", "logger": "backend.routes.qr_preview", "message": "Logo content read: 127404 bytes", "module": "qr_preview", "function": "preview_qr_svg", "line": 305}
{"timestamp": "2025-06-12T17:27:23.899529", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Formatting content for QR type: qrtype.scooter_unlock, content keys: ['scooter_id', 'unlock_code', 'title', 'description', 'service_provider', 'rate_info']", "module": "qr_preview", "function": "_format_qr_data", "line": 876}
{"timestamp": "2025-06-12T17:27:23.922895", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Logo remove background setting: False, has logo: True", "module": "qr_preview", "function": "generate_preview_svg_modular", "line": 727}
{"timestamp": "2025-06-12T17:27:23.922942", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Logo present but background removal disabled", "module": "qr_preview", "function": "generate_preview_svg_modular", "line": 741}
{"timestamp": "2025-06-12T17:27:23.941777", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Drawing 3 finder patterns at positions: [(0, 0), (50, 0), (0, 50)]", "module": "qr_preview", "function": "generate_preview_svg_modular", "line": 775}
{"timestamp": "2025-06-12T17:27:23.941849", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "QR size: 57, border_style: square, center_style: square", "module": "qr_preview", "function": "generate_preview_svg_modular", "line": 776}
{"timestamp": "2025-06-12T17:27:23.941892", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Drawing finder pattern 1/3 at position (0, 0)", "module": "qr_preview", "function": "generate_preview_svg_modular", "line": 780}
{"timestamp": "2025-06-12T17:27:23.941976", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "_draw_finder_marker called: mx=0, my=0, module_size=5.087719298245614, border_style=square, center_style=square, border_color=#000000, center_color=#000000, offset=(20, 20)", "module": "qr_preview", "function": "_draw_finder_marker", "line": 97}
{"timestamp": "2025-06-12T17:27:23.942033", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Creating finder pattern group with id='marker-0-0'", "module": "qr_preview", "function": "_draw_finder_marker", "line": 110}
{"timestamp": "2025-06-12T17:27:23.942067", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Drawing square border:", "module": "qr_preview", "function": "_draw_finder_marker", "line": 160}
{"timestamp": "2025-06-12T17:27:23.942098", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "  Outer: insert=(20.0, 20.0), size=(35.614035087719294, 35.614035087719294)", "module": "qr_preview", "function": "_draw_finder_marker", "line": 161}
{"timestamp": "2025-06-12T17:27:23.942135", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "  Inner: insert=(26.105263157894736, 26.105263157894736), size=(23.403508771929822, 23.403508771929822)", "module": "qr_preview", "function": "_draw_finder_marker", "line": 162}
{"timestamp": "2025-06-12T17:27:23.942207", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Drawing marker center: style=square, color=#000000, pos=(32.719298245614034, 32.719298245614034), size=10.175438596491228", "module": "qr_preview", "function": "_draw_marker_center", "line": 179}
{"timestamp": "2025-06-12T17:27:23.942253", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Successfully added finder pattern group to SVG", "module": "qr_preview", "function": "_draw_finder_marker", "line": 171}
{"timestamp": "2025-06-12T17:27:23.942286", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Successfully drew finder pattern 1/3", "module": "qr_preview", "function": "generate_preview_svg_modular", "line": 782}
{"timestamp": "2025-06-12T17:27:23.942318", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Drawing finder pattern 2/3 at position (50, 0)", "module": "qr_preview", "function": "generate_preview_svg_modular", "line": 780}
{"timestamp": "2025-06-12T17:27:23.942350", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "_draw_finder_marker called: mx=50, my=0, module_size=5.087719298245614, border_style=square, center_style=square, border_color=#000000, center_color=#000000, offset=(20, 20)", "module": "qr_preview", "function": "_draw_finder_marker", "line": 97}
{"timestamp": "2025-06-12T17:27:23.942391", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Creating finder pattern group with id='marker-50-0'", "module": "qr_preview", "function": "_draw_finder_marker", "line": 110}
{"timestamp": "2025-06-12T17:27:23.942423", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Drawing square border:", "module": "qr_preview", "function": "_draw_finder_marker", "line": 160}
{"timestamp": "2025-06-12T17:27:23.942462", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "  Outer: insert=(274.38596491228066, 20.0), size=(35.614035087719294, 35.614035087719294)", "module": "qr_preview", "function": "_draw_finder_marker", "line": 161}
{"timestamp": "2025-06-12T17:27:23.942497", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "  Inner: insert=(280.4912280701754, 26.105263157894736), size=(23.403508771929822, 23.403508771929822)", "module": "qr_preview", "function": "_draw_finder_marker", "line": 162}
{"timestamp": "2025-06-12T17:27:23.942554", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Drawing marker center: style=square, color=#000000, pos=(287.10526315789474, 32.719298245614034), size=10.175438596491228", "module": "qr_preview", "function": "_draw_marker_center", "line": 179}
{"timestamp": "2025-06-12T17:27:23.942627", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Successfully added finder pattern group to SVG", "module": "qr_preview", "function": "_draw_finder_marker", "line": 171}
{"timestamp": "2025-06-12T17:27:23.942659", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Successfully drew finder pattern 2/3", "module": "qr_preview", "function": "generate_preview_svg_modular", "line": 782}
{"timestamp": "2025-06-12T17:27:23.942692", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Drawing finder pattern 3/3 at position (0, 50)", "module": "qr_preview", "function": "generate_preview_svg_modular", "line": 780}
{"timestamp": "2025-06-12T17:27:23.942725", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "_draw_finder_marker called: mx=0, my=50, module_size=5.087719298245614, border_style=square, center_style=square, border_color=#000000, center_color=#000000, offset=(20, 20)", "module": "qr_preview", "function": "_draw_finder_marker", "line": 97}
{"timestamp": "2025-06-12T17:27:23.942764", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Creating finder pattern group with id='marker-0-50'", "module": "qr_preview", "function": "_draw_finder_marker", "line": 110}
{"timestamp": "2025-06-12T17:27:23.942796", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Drawing square border:", "module": "qr_preview", "function": "_draw_finder_marker", "line": 160}
{"timestamp": "2025-06-12T17:27:23.942829", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "  Outer: insert=(20.0, 274.38596491228066), size=(35.614035087719294, 35.614035087719294)", "module": "qr_preview", "function": "_draw_finder_marker", "line": 161}
{"timestamp": "2025-06-12T17:27:23.942863", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "  Inner: insert=(26.105263157894736, 280.4912280701754), size=(23.403508771929822, 23.403508771929822)", "module": "qr_preview", "function": "_draw_finder_marker", "line": 162}
{"timestamp": "2025-06-12T17:27:23.942920", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Drawing marker center: style=square, color=#000000, pos=(32.719298245614034, 287.10526315789474), size=10.175438596491228", "module": "qr_preview", "function": "_draw_marker_center", "line": 179}
{"timestamp": "2025-06-12T17:27:23.942965", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Successfully added finder pattern group to SVG", "module": "qr_preview", "function": "_draw_finder_marker", "line": 171}
{"timestamp": "2025-06-12T17:27:23.942997", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Successfully drew finder pattern 3/3", "module": "qr_preview", "function": "generate_preview_svg_modular", "line": 782}
{"timestamp": "2025-06-12T17:27:23.943035", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "_draw_logo called with logo type: <class 'bytes'>, dimensions: 300", "module": "qr_preview", "function": "_draw_logo", "line": 434}
{"timestamp": "2025-06-12T17:27:24.276074", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "Generated SVG length: 251209", "module": "qr_preview", "function": "generate_preview_svg_modular", "line": 802}
{"timestamp": "2025-06-12T17:27:24.276246", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "SVG contains marker-0-0: True", "module": "qr_preview", "function": "generate_preview_svg_modular", "line": 803}
{"timestamp": "2025-06-12T17:27:24.276381", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "SVG contains marker-18-0: False", "module": "qr_preview", "function": "generate_preview_svg_modular", "line": 804}
{"timestamp": "2025-06-12T17:27:24.276516", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "SVG contains marker-0-18: False", "module": "qr_preview", "function": "generate_preview_svg_modular", "line": 805}
{"timestamp": "2025-06-12T17:27:24.276943", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "SVG marker counts - outer: 3, inner: 3, dot: 0", "module": "qr_preview", "function": "generate_preview_svg_modular", "line": 813}
{"timestamp": "2025-06-12T17:27:24.276980", "level": "DEBUG", "logger": "backend.services.qr_preview", "message": "SVG label counts - text: 0, background: 0", "module": "qr_preview", "function": "generate_preview_svg_modular", "line": 814}
{"timestamp": "2025-06-12T17:27:24.279863", "level": "DEBUG", "logger": "backend.main", "message": "[Auth] Enforced Bearer token authentication for request", "module": "main", "function": "bearer_token_auth_middleware", "line": 274}
{"timestamp": "2025-06-12T17:27:30.023811", "level": "DEBUG", "logger": "backend.main", "message": "[Auth] Enforced Bearer token authentication for request", "module": "main", "function": "bearer_token_auth_middleware", "line": 274}
{"timestamp": "2025-06-12T17:27:58.006840", "level": "DEBUG", "logger": "backend.main", "message": "[Auth] Enforced Bearer token authentication for request", "module": "main", "function": "bearer_token_auth_middleware", "line": 274}
{"timestamp": "2025-06-12T17:28:00.024483", "level": "DEBUG", "logger": "backend.main", "message": "[Auth] Enforced Bearer token authentication for request", "module": "main", "function": "bearer_token_auth_middleware", "line": 274}
