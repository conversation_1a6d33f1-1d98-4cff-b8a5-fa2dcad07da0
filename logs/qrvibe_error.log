{"timestamp": "2025-06-11T14:25:07.884813", "level": "ERROR", "logger": "backend.services.qr_preview", "message": "Error formatting QR data for type qrtype.url: TRANSLATION_SERVICE", "module": "qr_preview", "function": "_format_qr_data", "line": 1639, "exception": "Traceback (most recent call last):\n  File \"/Users/<USER>/aihub/qrvibe/backend/services/qr_preview.py\", line 1602, in _format_qr_data\n    elif qr_type_str == QRType.TRANSLATION_SERVICE.lower():\n                        ^^^^^^^^^^^^^^^^^^^^^^^^^^\n  File \"/opt/homebrew/Cellar/python@3.11/3.11.11/Frameworks/Python.framework/Versions/3.11/lib/python3.11/enum.py\", line 786, in __getattr__\n    raise AttributeError(name) from None\nAttributeError: TRANSLATION_SERVICE"}
{"timestamp": "2025-06-11T14:25:14.050594", "level": "ERROR", "logger": "backend.services.qr_preview", "message": "Error formatting QR data for type qrtype.scooter_unlock: TRANSLATION_SERVICE", "module": "qr_preview", "function": "_format_qr_data", "line": 1639, "exception": "Traceback (most recent call last):\n  File \"/Users/<USER>/aihub/qrvibe/backend/services/qr_preview.py\", line 1602, in _format_qr_data\n    elif qr_type_str == QRType.TRANSLATION_SERVICE.lower():\n                        ^^^^^^^^^^^^^^^^^^^^^^^^^^\n  File \"/opt/homebrew/Cellar/python@3.11/3.11.11/Frameworks/Python.framework/Versions/3.11/lib/python3.11/enum.py\", line 786, in __getattr__\n    raise AttributeError(name) from None\nAttributeError: TRANSLATION_SERVICE"}
{"timestamp": "2025-06-11T14:25:17.169903", "level": "ERROR", "logger": "backend.services.qr_preview", "message": "Error formatting QR data for type qrtype.scooter_unlock: TRANSLATION_SERVICE", "module": "qr_preview", "function": "_format_qr_data", "line": 1639, "exception": "Traceback (most recent call last):\n  File \"/Users/<USER>/aihub/qrvibe/backend/services/qr_preview.py\", line 1602, in _format_qr_data\n    elif qr_type_str == QRType.TRANSLATION_SERVICE.lower():\n                        ^^^^^^^^^^^^^^^^^^^^^^^^^^\n  File \"/opt/homebrew/Cellar/python@3.11/3.11.11/Frameworks/Python.framework/Versions/3.11/lib/python3.11/enum.py\", line 786, in __getattr__\n    raise AttributeError(name) from None\nAttributeError: TRANSLATION_SERVICE"}
{"timestamp": "2025-06-11T14:25:17.233949", "level": "ERROR", "logger": "backend.services.qr_preview", "message": "Error formatting QR data for type qrtype.scooter_unlock: TRANSLATION_SERVICE", "module": "qr_preview", "function": "_format_qr_data", "line": 1639, "exception": "Traceback (most recent call last):\n  File \"/Users/<USER>/aihub/qrvibe/backend/services/qr_preview.py\", line 1602, in _format_qr_data\n    elif qr_type_str == QRType.TRANSLATION_SERVICE.lower():\n                        ^^^^^^^^^^^^^^^^^^^^^^^^^^\n  File \"/opt/homebrew/Cellar/python@3.11/3.11.11/Frameworks/Python.framework/Versions/3.11/lib/python3.11/enum.py\", line 786, in __getattr__\n    raise AttributeError(name) from None\nAttributeError: TRANSLATION_SERVICE"}
{"timestamp": "2025-06-11T14:25:17.792800", "level": "ERROR", "logger": "backend.services.qr_preview", "message": "Error formatting QR data for type qrtype.scooter_unlock: TRANSLATION_SERVICE", "module": "qr_preview", "function": "_format_qr_data", "line": 1639, "exception": "Traceback (most recent call last):\n  File \"/Users/<USER>/aihub/qrvibe/backend/services/qr_preview.py\", line 1602, in _format_qr_data\n    elif qr_type_str == QRType.TRANSLATION_SERVICE.lower():\n                        ^^^^^^^^^^^^^^^^^^^^^^^^^^\n  File \"/opt/homebrew/Cellar/python@3.11/3.11.11/Frameworks/Python.framework/Versions/3.11/lib/python3.11/enum.py\", line 786, in __getattr__\n    raise AttributeError(name) from None\nAttributeError: TRANSLATION_SERVICE"}
{"timestamp": "2025-06-11T14:25:17.850283", "level": "ERROR", "logger": "backend.services.qr_preview", "message": "Error formatting QR data for type qrtype.scooter_unlock: TRANSLATION_SERVICE", "module": "qr_preview", "function": "_format_qr_data", "line": 1639, "exception": "Traceback (most recent call last):\n  File \"/Users/<USER>/aihub/qrvibe/backend/services/qr_preview.py\", line 1602, in _format_qr_data\n    elif qr_type_str == QRType.TRANSLATION_SERVICE.lower():\n                        ^^^^^^^^^^^^^^^^^^^^^^^^^^\n  File \"/opt/homebrew/Cellar/python@3.11/3.11.11/Frameworks/Python.framework/Versions/3.11/lib/python3.11/enum.py\", line 786, in __getattr__\n    raise AttributeError(name) from None\nAttributeError: TRANSLATION_SERVICE"}
