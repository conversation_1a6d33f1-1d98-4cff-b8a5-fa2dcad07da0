{"timestamp": "2025-06-11T21:46:26.346364", "level": "ERROR", "logger": "backend.core.security.jwt_keys", "message": "Token verification failed with all keys. Errors: Key 55c8121a-4e96-43fe-bf5d-4977abf8897a: Signature verification failed.", "module": "jwt_keys", "function": "decode_token", "line": 239}
{"timestamp": "2025-06-11T21:46:26.347155", "level": "ERROR", "logger": "backend.core.security.jwt_keys", "message": "Token decode error: Token verification failed with all keys. Errors: Key 55c8121a-4e96-43fe-bf5d-4977abf8897a: Signature verification failed.", "module": "jwt_keys", "function": "decode_token", "line": 244}
{"timestamp": "2025-06-11T21:46:26.347372", "level": "ERROR", "logger": "backend.services.auth", "message": "JWT token validation failed: Failed to decode token: Token verification failed with all keys. Errors: Key 55c8121a-4e96-43fe-bf5d-4977abf8897a: Signature verification failed.", "module": "auth", "function": "authenticate_token", "line": 686}
{"timestamp": "2025-06-11T21:46:26.365148", "level": "ERROR", "logger": "backend.core.security.jwt_keys", "message": "Token verification failed with all keys. Errors: Key 55c8121a-4e96-43fe-bf5d-4977abf8897a: Signature verification failed.", "module": "jwt_keys", "function": "decode_token", "line": 239}
{"timestamp": "2025-06-11T21:46:26.365241", "level": "ERROR", "logger": "backend.core.security.jwt_keys", "message": "Token decode error: Token verification failed with all keys. Errors: Key 55c8121a-4e96-43fe-bf5d-4977abf8897a: Signature verification failed.", "module": "jwt_keys", "function": "decode_token", "line": 244}
{"timestamp": "2025-06-11T21:46:26.365323", "level": "ERROR", "logger": "backend.services.auth", "message": "JWT token validation failed: Failed to decode token: Token verification failed with all keys. Errors: Key 55c8121a-4e96-43fe-bf5d-4977abf8897a: Signature verification failed.", "module": "auth", "function": "authenticate_token", "line": 686}
{"timestamp": "2025-06-11T21:46:26.369111", "level": "ERROR", "logger": "backend.core.security.jwt_keys", "message": "Token verification failed with all keys. Errors: Key 55c8121a-4e96-43fe-bf5d-4977abf8897a: Signature verification failed.", "module": "jwt_keys", "function": "decode_token", "line": 239}
{"timestamp": "2025-06-11T21:46:26.369218", "level": "ERROR", "logger": "backend.core.security.jwt_keys", "message": "Token decode error: Token verification failed with all keys. Errors: Key 55c8121a-4e96-43fe-bf5d-4977abf8897a: Signature verification failed.", "module": "jwt_keys", "function": "decode_token", "line": 244}
{"timestamp": "2025-06-11T21:46:26.369307", "level": "ERROR", "logger": "backend.services.auth", "message": "JWT token validation failed: Failed to decode token: Token verification failed with all keys. Errors: Key 55c8121a-4e96-43fe-bf5d-4977abf8897a: Signature verification failed.", "module": "auth", "function": "authenticate_token", "line": 686}
{"timestamp": "2025-06-11T21:46:42.602544", "level": "ERROR", "logger": "backend.core.security.jwt_keys", "message": "Token verification failed with all keys. Errors: Key 55c8121a-4e96-43fe-bf5d-4977abf8897a: Signature verification failed.", "module": "jwt_keys", "function": "decode_token", "line": 239}
{"timestamp": "2025-06-11T21:46:42.602917", "level": "ERROR", "logger": "backend.core.security.jwt_keys", "message": "Token decode error: Token verification failed with all keys. Errors: Key 55c8121a-4e96-43fe-bf5d-4977abf8897a: Signature verification failed.", "module": "jwt_keys", "function": "decode_token", "line": 244}
{"timestamp": "2025-06-11T21:46:42.603096", "level": "ERROR", "logger": "backend.services.auth", "message": "JWT token validation failed: Failed to decode token: Token verification failed with all keys. Errors: Key 55c8121a-4e96-43fe-bf5d-4977abf8897a: Signature verification failed.", "module": "auth", "function": "authenticate_token", "line": 686}
{"timestamp": "2025-06-11T21:46:42.612534", "level": "ERROR", "logger": "backend.core.security.jwt_keys", "message": "Token verification failed with all keys. Errors: Key 55c8121a-4e96-43fe-bf5d-4977abf8897a: Signature verification failed.", "module": "jwt_keys", "function": "decode_token", "line": 239}
{"timestamp": "2025-06-11T21:46:42.612846", "level": "ERROR", "logger": "backend.core.security.jwt_keys", "message": "Token decode error: Token verification failed with all keys. Errors: Key 55c8121a-4e96-43fe-bf5d-4977abf8897a: Signature verification failed.", "module": "jwt_keys", "function": "decode_token", "line": 244}
{"timestamp": "2025-06-11T21:46:42.613000", "level": "ERROR", "logger": "backend.services.auth", "message": "JWT token validation failed: Failed to decode token: Token verification failed with all keys. Errors: Key 55c8121a-4e96-43fe-bf5d-4977abf8897a: Signature verification failed.", "module": "auth", "function": "authenticate_token", "line": 686}
{"timestamp": "2025-06-11T21:46:42.615257", "level": "ERROR", "logger": "backend.core.security.jwt_keys", "message": "Token verification failed with all keys. Errors: Key 55c8121a-4e96-43fe-bf5d-4977abf8897a: Signature verification failed.", "module": "jwt_keys", "function": "decode_token", "line": 239}
{"timestamp": "2025-06-11T21:46:42.615327", "level": "ERROR", "logger": "backend.core.security.jwt_keys", "message": "Token decode error: Token verification failed with all keys. Errors: Key 55c8121a-4e96-43fe-bf5d-4977abf8897a: Signature verification failed.", "module": "jwt_keys", "function": "decode_token", "line": 244}
{"timestamp": "2025-06-11T21:46:42.615387", "level": "ERROR", "logger": "backend.services.auth", "message": "JWT token validation failed: Failed to decode token: Token verification failed with all keys. Errors: Key 55c8121a-4e96-43fe-bf5d-4977abf8897a: Signature verification failed.", "module": "auth", "function": "authenticate_token", "line": 686}
{"timestamp": "2025-06-11T21:47:16.842665", "level": "ERROR", "logger": "backend.core.security.jwt_keys", "message": "Token verification failed with all keys. Errors: Key 55c8121a-4e96-43fe-bf5d-4977abf8897a: Signature verification failed.", "module": "jwt_keys", "function": "decode_token", "line": 239}
{"timestamp": "2025-06-11T21:47:16.842928", "level": "ERROR", "logger": "backend.core.security.jwt_keys", "message": "Token decode error: Token verification failed with all keys. Errors: Key 55c8121a-4e96-43fe-bf5d-4977abf8897a: Signature verification failed.", "module": "jwt_keys", "function": "decode_token", "line": 244}
{"timestamp": "2025-06-11T21:47:16.843621", "level": "ERROR", "logger": "backend.services.auth", "message": "JWT token validation failed: Failed to decode token: Token verification failed with all keys. Errors: Key 55c8121a-4e96-43fe-bf5d-4977abf8897a: Signature verification failed.", "module": "auth", "function": "authenticate_token", "line": 686}
{"timestamp": "2025-06-11T21:47:16.845339", "level": "ERROR", "logger": "backend.core.security.jwt_keys", "message": "Token verification failed with all keys. Errors: Key 55c8121a-4e96-43fe-bf5d-4977abf8897a: Signature verification failed.", "module": "jwt_keys", "function": "decode_token", "line": 239}
{"timestamp": "2025-06-11T21:47:16.845448", "level": "ERROR", "logger": "backend.core.security.jwt_keys", "message": "Token decode error: Token verification failed with all keys. Errors: Key 55c8121a-4e96-43fe-bf5d-4977abf8897a: Signature verification failed.", "module": "jwt_keys", "function": "decode_token", "line": 244}
{"timestamp": "2025-06-11T21:47:16.845525", "level": "ERROR", "logger": "backend.services.auth", "message": "JWT token validation failed: Failed to decode token: Token verification failed with all keys. Errors: Key 55c8121a-4e96-43fe-bf5d-4977abf8897a: Signature verification failed.", "module": "auth", "function": "authenticate_token", "line": 686}
{"timestamp": "2025-06-11T21:47:16.848663", "level": "ERROR", "logger": "backend.core.security.jwt_keys", "message": "Token verification failed with all keys. Errors: Key 55c8121a-4e96-43fe-bf5d-4977abf8897a: Signature verification failed.", "module": "jwt_keys", "function": "decode_token", "line": 239}
{"timestamp": "2025-06-11T21:47:16.848764", "level": "ERROR", "logger": "backend.core.security.jwt_keys", "message": "Token decode error: Token verification failed with all keys. Errors: Key 55c8121a-4e96-43fe-bf5d-4977abf8897a: Signature verification failed.", "module": "jwt_keys", "function": "decode_token", "line": 244}
{"timestamp": "2025-06-11T21:47:16.848940", "level": "ERROR", "logger": "backend.auth", "message": "Token validation error: Failed to decode token: Token verification failed with all keys. Errors: Key 55c8121a-4e96-43fe-bf5d-4977abf8897a: Signature verification failed.", "module": "__init__", "function": "get_current_user", "line": 193}
{"timestamp": "2025-06-11T21:47:16.851459", "level": "ERROR", "logger": "backend.core.security.jwt_keys", "message": "Token verification failed with all keys. Errors: Key 55c8121a-4e96-43fe-bf5d-4977abf8897a: Signature verification failed.", "module": "jwt_keys", "function": "decode_token", "line": 239}
{"timestamp": "2025-06-11T21:47:16.851548", "level": "ERROR", "logger": "backend.core.security.jwt_keys", "message": "Token decode error: Token verification failed with all keys. Errors: Key 55c8121a-4e96-43fe-bf5d-4977abf8897a: Signature verification failed.", "module": "jwt_keys", "function": "decode_token", "line": 244}
{"timestamp": "2025-06-11T21:47:16.851642", "level": "ERROR", "logger": "backend.auth", "message": "Token validation error: Failed to decode token: Token verification failed with all keys. Errors: Key 55c8121a-4e96-43fe-bf5d-4977abf8897a: Signature verification failed.", "module": "__init__", "function": "get_current_user", "line": 193}
{"timestamp": "2025-06-11T21:47:16.852789", "level": "ERROR", "logger": "backend.core.security.jwt_keys", "message": "Token verification failed with all keys. Errors: Key 55c8121a-4e96-43fe-bf5d-4977abf8897a: Signature verification failed.", "module": "jwt_keys", "function": "decode_token", "line": 239}
{"timestamp": "2025-06-11T21:47:16.852854", "level": "ERROR", "logger": "backend.core.security.jwt_keys", "message": "Token decode error: Token verification failed with all keys. Errors: Key 55c8121a-4e96-43fe-bf5d-4977abf8897a: Signature verification failed.", "module": "jwt_keys", "function": "decode_token", "line": 244}
{"timestamp": "2025-06-11T21:47:16.852914", "level": "ERROR", "logger": "backend.auth", "message": "Token validation error: Failed to decode token: Token verification failed with all keys. Errors: Key 55c8121a-4e96-43fe-bf5d-4977abf8897a: Signature verification failed.", "module": "__init__", "function": "get_current_user", "line": 193}
