{"timestamp": "2025-06-11T15:25:14.339040", "level": "INFO", "logger": "backend.services.enhanced_rate_limit", "message": "Auth event for anonymous: login_attempt (failure)", "module": "enhanced_rate_limit", "function": "track_auth_event", "line": 182}
{"timestamp": "2025-06-11T15:25:14.819870", "level": "INFO", "logger": "backend.services.auth", "message": "Authentication successful for user: <EMAIL>", "module": "auth", "function": "authenticate_user", "line": 266}
{"timestamp": "2025-06-11T15:25:14.820983", "level": "INFO", "logger": "backend.routes.auth", "message": "User logged in successfully: <EMAIL>", "module": "auth", "function": "login", "line": 218}
{"timestamp": "2025-06-11T15:25:14.825727", "level": "INFO", "logger": "backend.services.enhanced_rate_limit", "message": "Auth event for user e4ae2d8f-b32f-52d7-99cd-8a33b19652c7: login_success (success)", "module": "enhanced_rate_limit", "function": "track_auth_event", "line": 182}
{"timestamp": "2025-06-11T15:25:14.948856", "level": "INFO", "logger": "backend.services.adaptive_session", "message": "Adaptive session for user e4ae2d8f-b32f-52d7-99cd-8a33b19652c7: risk=low, duration=86400s", "module": "adaptive_session", "function": "get_session_expiry", "line": 161}
{"timestamp": "2025-06-11T15:25:14.948954", "level": "INFO", "logger": "backend.routes.auth", "message": "Adaptive session <NAME_EMAIL>: duration=86400s", "module": "auth", "function": "login", "line": 279}
{"timestamp": "2025-06-11T15:25:14.949005", "level": "INFO", "logger": "backend.routes.auth", "message": "Bearer token authentication issued <NAME_EMAIL>", "module": "auth", "function": "login", "line": 284}
{"timestamp": "2025-06-11T15:25:18.086334", "level": "INFO", "logger": "root", "message": "Getting feature matrix for user e4ae2d8f-b32f-52d7-99cd-8a33b19652c7 (email: <EMAIL>)", "module": "subscription", "function": "get_user_feature_matrix", "line": 142}
{"timestamp": "2025-06-11T15:25:18.102793", "level": "INFO", "logger": "root", "message": "Found subscription for user e4ae2d8f-b32f-52d7-99cd-8a33b19652c7: plan=enterprise, status=active", "module": "subscription", "function": "get_user_feature_matrix", "line": 152}
{"timestamp": "2025-06-11T15:25:18.121446", "level": "INFO", "logger": "root", "message": "Found 0 team addons for user e4ae2d8f-b32f-52d7-99cd-8a33b19652c7", "module": "subscription", "function": "get_user_feature_matrix", "line": 181}
{"timestamp": "2025-06-11T15:25:18.121587", "level": "INFO", "logger": "root", "message": "Retrieved base features for plan enterprise", "module": "subscription", "function": "get_user_feature_matrix", "line": 191}
{"timestamp": "2025-06-11T15:25:18.169625", "level": "INFO", "logger": "root", "message": "Successfully generated feature matrix for user e4ae2d8f-b32f-52d7-99cd-8a33b19652c7", "module": "subscription", "function": "get_user_feature_matrix", "line": 284}
{"timestamp": "2025-06-11T15:36:13.124784", "level": "INFO", "logger": "root", "message": "Getting feature matrix for user e4ae2d8f-b32f-52d7-99cd-8a33b19652c7 (email: <EMAIL>)", "module": "subscription", "function": "get_user_feature_matrix", "line": 142}
{"timestamp": "2025-06-11T15:36:13.511360", "level": "INFO", "logger": "root", "message": "Found subscription for user e4ae2d8f-b32f-52d7-99cd-8a33b19652c7: plan=enterprise, status=active", "module": "subscription", "function": "get_user_feature_matrix", "line": 152}
{"timestamp": "2025-06-11T15:36:13.587892", "level": "INFO", "logger": "root", "message": "Found 0 team addons for user e4ae2d8f-b32f-52d7-99cd-8a33b19652c7", "module": "subscription", "function": "get_user_feature_matrix", "line": 181}
{"timestamp": "2025-06-11T15:36:13.588125", "level": "INFO", "logger": "root", "message": "Retrieved base features for plan enterprise", "module": "subscription", "function": "get_user_feature_matrix", "line": 191}
{"timestamp": "2025-06-11T15:36:13.635921", "level": "INFO", "logger": "root", "message": "Successfully generated feature matrix for user e4ae2d8f-b32f-52d7-99cd-8a33b19652c7", "module": "subscription", "function": "get_user_feature_matrix", "line": 284}
{"timestamp": "2025-06-11T15:39:04.521085", "level": "INFO", "logger": "backend.services.qr_preview_cache", "message": "QR preview cache cleanup task stopped", "module": "qr_preview_cache", "function": "stop", "line": 257}
{"timestamp": "2025-06-11T15:39:04.521995", "level": "INFO", "logger": "backend.main", "message": "QR preview cache stopped", "module": "main", "function": "shutdown_qr_preview_cache", "line": 352}
{"timestamp": "2025-06-11T15:39:08.770417", "level": "INFO", "logger": "backend.main", "message": "=== QRVibe API Startup: Logging to file is enabled ===", "module": "main", "function": "<module>", "line": 37}
{"timestamp": "2025-06-11T15:39:08.770508", "level": "INFO", "logger": "backend.main", "message": "Applying critical patches before initialization", "module": "main", "function": "<module>", "line": 43}
{"timestamp": "2025-06-11T15:39:08.770571", "level": "INFO", "logger": "backend.patches", "message": "Applying patches (none required)", "module": "__init__", "function": "apply_patches", "line": 11}
{"timestamp": "2025-06-11T15:39:08.770834", "level": "INFO", "logger": "backend.core.middleware.cors", "message": "Setting up CORS middleware with origins: ['https://localhost:3000', 'http://localhost:3000', 'https://127.0.0.1:3000', 'http://127.0.0.1:3000']", "module": "cors", "function": "setup_cors", "line": 86}
{"timestamp": "2025-06-11T15:39:08.770906", "level": "INFO", "logger": "backend.core.middleware.cors", "message": "CORS middleware configured - preflight requests should be handled automatically", "module": "cors", "function": "setup_cors", "line": 98}
{"timestamp": "2025-06-11T15:39:08.772068", "level": "INFO", "logger": "backend.middleware.security_middleware", "message": "Added Enhanced Security Middleware with tiered rate limiting", "module": "security_middleware", "function": "add_security_middleware", "line": 176}
{"timestamp": "2025-06-11T15:39:08.772170", "level": "INFO", "logger": "backend.main", "message": "Mounting storage directory: /Users/<USER>/aihub/qrvibe/storage", "module": "main", "function": "<module>", "line": 107}
{"timestamp": "2025-06-11T15:39:08.772282", "level": "INFO", "logger": "backend.main", "message": "Storage directory absolute path: /Users/<USER>/aihub/qrvibe/storage", "module": "main", "function": "<module>", "line": 119}
{"timestamp": "2025-06-11T15:39:09.254628", "level": "INFO", "logger": "root", "message": "Storage initialized: environment=test, storage_dir=/Users/<USER>/aihub/qrvibe/storage, s3_enabled=True", "module": "storage", "function": "__init__", "line": 41}
{"timestamp": "2025-06-11T15:39:09.257519", "level": "INFO", "logger": "root", "message": "S3 storage enabled with bucket: qrverse and CDN: https://dev-cdn.qrvibe.com", "module": "storage", "function": "__init__", "line": 59}
{"timestamp": "2025-06-11T15:39:09.303691", "level": "INFO", "logger": "root", "message": "Storage initialized: environment=test, storage_dir=/Users/<USER>/aihub/qrvibe/storage, s3_enabled=True", "module": "storage", "function": "__init__", "line": 41}
{"timestamp": "2025-06-11T15:39:09.307023", "level": "INFO", "logger": "root", "message": "S3 storage enabled with bucket: qrverse and CDN: https://dev-cdn.qrvibe.com", "module": "storage", "function": "__init__", "line": 59}
{"timestamp": "2025-06-11T15:39:09.337793", "level": "INFO", "logger": "backend.middleware.security_middleware", "message": "Enhanced security middleware initialized with tiered rate limiting", "module": "security_middleware", "function": "__init__", "line": 87}
{"timestamp": "2025-06-11T15:39:09.337891", "level": "INFO", "logger": "backend.core.middleware.csrf", "message": "CSRF Protection Middleware initialized with exclude paths: ['/auth/jwt/login', '/auth/logout', '/auth/register', '/auth/2fa', '/auth/me', '/auth/verify-email', '/auth/reset-password', '/admin/login', '/admin/logout']", "module": "csrf", "function": "__init__", "line": 44}
{"timestamp": "2025-06-11T15:39:09.342762", "level": "INFO", "logger": "backend.services.token_blacklist", "message": "Token blacklist service initialized with Redis", "module": "token_blacklist", "function": "initialize", "line": 59}
{"timestamp": "2025-06-11T15:39:09.342879", "level": "INFO", "logger": "backend.main", "message": "Token blacklist service initialized", "module": "main", "function": "initialize_token_blacklist", "line": 98}
{"timestamp": "2025-06-11T15:39:09.345531", "level": "INFO", "logger": "backend.services.qr_preview_cache", "message": "QR preview cache initialized at /Users/<USER>/aihub/qrvibe/storage/cache/qr_preview_cache.db", "module": "qr_preview_cache", "function": "_init_db", "line": 56}
{"timestamp": "2025-06-11T15:39:09.345605", "level": "INFO", "logger": "backend.services.qr_preview_cache", "message": "Started QR preview cache cleanup task", "module": "qr_preview_cache", "function": "start_cleanup_task", "line": 62}
{"timestamp": "2025-06-11T15:39:09.346126", "level": "INFO", "logger": "backend.main", "message": "QR preview cache initialized and cleanup task started", "module": "main", "function": "init_qr_preview_cache", "line": 345}
{"timestamp": "2025-06-11T15:39:45.052234", "level": "INFO", "logger": "backend.services.qr_preview_cache", "message": "QR preview cache cleanup task stopped", "module": "qr_preview_cache", "function": "stop", "line": 257}
{"timestamp": "2025-06-11T15:39:45.052368", "level": "INFO", "logger": "backend.main", "message": "QR preview cache stopped", "module": "main", "function": "shutdown_qr_preview_cache", "line": 352}
{"timestamp": "2025-06-11T15:39:50.092570", "level": "INFO", "logger": "backend.main", "message": "=== QRVibe API Startup: Logging to file is enabled ===", "module": "main", "function": "<module>", "line": 37}
{"timestamp": "2025-06-11T15:39:50.092692", "level": "INFO", "logger": "backend.main", "message": "Applying critical patches before initialization", "module": "main", "function": "<module>", "line": 43}
{"timestamp": "2025-06-11T15:39:50.092770", "level": "INFO", "logger": "backend.patches", "message": "Applying patches (none required)", "module": "__init__", "function": "apply_patches", "line": 11}
{"timestamp": "2025-06-11T15:39:50.093028", "level": "INFO", "logger": "backend.core.middleware.cors", "message": "Setting up CORS middleware with origins: ['https://127.0.0.1:3000', 'http://localhost:3000', 'https://localhost:3000', 'http://127.0.0.1:3000']", "module": "cors", "function": "setup_cors", "line": 86}
{"timestamp": "2025-06-11T15:39:50.095931", "level": "INFO", "logger": "backend.core.middleware.cors", "message": "CORS middleware configured - preflight requests should be handled automatically", "module": "cors", "function": "setup_cors", "line": 98}
{"timestamp": "2025-06-11T15:39:50.097522", "level": "INFO", "logger": "backend.middleware.security_middleware", "message": "Added Enhanced Security Middleware with tiered rate limiting", "module": "security_middleware", "function": "add_security_middleware", "line": 176}
{"timestamp": "2025-06-11T15:39:50.097675", "level": "INFO", "logger": "backend.main", "message": "Mounting storage directory: /Users/<USER>/aihub/qrvibe/storage", "module": "main", "function": "<module>", "line": 107}
{"timestamp": "2025-06-11T15:39:50.097806", "level": "INFO", "logger": "backend.main", "message": "Storage directory absolute path: /Users/<USER>/aihub/qrvibe/storage", "module": "main", "function": "<module>", "line": 119}
{"timestamp": "2025-06-11T15:39:50.698348", "level": "INFO", "logger": "root", "message": "Storage initialized: environment=test, storage_dir=/Users/<USER>/aihub/qrvibe/storage, s3_enabled=True", "module": "storage", "function": "__init__", "line": 41}
{"timestamp": "2025-06-11T15:39:50.706953", "level": "INFO", "logger": "root", "message": "S3 storage enabled with bucket: qrverse and CDN: https://dev-cdn.qrvibe.com", "module": "storage", "function": "__init__", "line": 59}
{"timestamp": "2025-06-11T15:39:50.775746", "level": "INFO", "logger": "root", "message": "Storage initialized: environment=test, storage_dir=/Users/<USER>/aihub/qrvibe/storage, s3_enabled=True", "module": "storage", "function": "__init__", "line": 41}
{"timestamp": "2025-06-11T15:39:50.779595", "level": "INFO", "logger": "root", "message": "S3 storage enabled with bucket: qrverse and CDN: https://dev-cdn.qrvibe.com", "module": "storage", "function": "__init__", "line": 59}
{"timestamp": "2025-06-11T15:39:51.404735", "level": "INFO", "logger": "backend.middleware.security_middleware", "message": "Enhanced security middleware initialized with tiered rate limiting", "module": "security_middleware", "function": "__init__", "line": 87}
{"timestamp": "2025-06-11T15:39:51.406283", "level": "INFO", "logger": "backend.core.middleware.csrf", "message": "CSRF Protection Middleware initialized with exclude paths: ['/auth/jwt/login', '/auth/logout', '/auth/register', '/auth/2fa', '/auth/me', '/auth/verify-email', '/auth/reset-password', '/admin/login', '/admin/logout']", "module": "csrf", "function": "__init__", "line": 44}
{"timestamp": "2025-06-11T15:39:51.480276", "level": "INFO", "logger": "backend.services.token_blacklist", "message": "Token blacklist service initialized with Redis", "module": "token_blacklist", "function": "initialize", "line": 59}
{"timestamp": "2025-06-11T15:39:51.480483", "level": "INFO", "logger": "backend.main", "message": "Token blacklist service initialized", "module": "main", "function": "initialize_token_blacklist", "line": 98}
{"timestamp": "2025-06-11T15:39:51.482523", "level": "INFO", "logger": "backend.services.qr_preview_cache", "message": "QR preview cache initialized at /Users/<USER>/aihub/qrvibe/storage/cache/qr_preview_cache.db", "module": "qr_preview_cache", "function": "_init_db", "line": 56}
{"timestamp": "2025-06-11T15:39:51.482716", "level": "INFO", "logger": "backend.services.qr_preview_cache", "message": "Started QR preview cache cleanup task", "module": "qr_preview_cache", "function": "start_cleanup_task", "line": 62}
{"timestamp": "2025-06-11T15:39:51.489355", "level": "INFO", "logger": "backend.main", "message": "QR preview cache initialized and cleanup task started", "module": "main", "function": "init_qr_preview_cache", "line": 345}
{"timestamp": "2025-06-11T15:39:59.074589", "level": "INFO", "logger": "backend.services.qr_preview_cache", "message": "QR preview cache cleanup task stopped", "module": "qr_preview_cache", "function": "stop", "line": 257}
{"timestamp": "2025-06-11T15:39:59.074897", "level": "INFO", "logger": "backend.main", "message": "QR preview cache stopped", "module": "main", "function": "shutdown_qr_preview_cache", "line": 352}
{"timestamp": "2025-06-11T15:40:02.527223", "level": "INFO", "logger": "backend.main", "message": "=== QRVibe API Startup: Logging to file is enabled ===", "module": "main", "function": "<module>", "line": 37}
{"timestamp": "2025-06-11T15:40:02.527316", "level": "INFO", "logger": "backend.main", "message": "Applying critical patches before initialization", "module": "main", "function": "<module>", "line": 43}
{"timestamp": "2025-06-11T15:40:02.527374", "level": "INFO", "logger": "backend.patches", "message": "Applying patches (none required)", "module": "__init__", "function": "apply_patches", "line": 11}
{"timestamp": "2025-06-11T15:40:02.527586", "level": "INFO", "logger": "backend.core.middleware.cors", "message": "Setting up CORS middleware with origins: ['https://127.0.0.1:3000', 'https://localhost:3000', 'http://localhost:3000', 'http://127.0.0.1:3000']", "module": "cors", "function": "setup_cors", "line": 86}
{"timestamp": "2025-06-11T15:40:02.527644", "level": "INFO", "logger": "backend.core.middleware.cors", "message": "CORS middleware configured - preflight requests should be handled automatically", "module": "cors", "function": "setup_cors", "line": 98}
{"timestamp": "2025-06-11T15:40:02.528631", "level": "INFO", "logger": "backend.middleware.security_middleware", "message": "Added Enhanced Security Middleware with tiered rate limiting", "module": "security_middleware", "function": "add_security_middleware", "line": 176}
{"timestamp": "2025-06-11T15:40:02.528738", "level": "INFO", "logger": "backend.main", "message": "Mounting storage directory: /Users/<USER>/aihub/qrvibe/storage", "module": "main", "function": "<module>", "line": 107}
{"timestamp": "2025-06-11T15:40:02.528868", "level": "INFO", "logger": "backend.main", "message": "Storage directory absolute path: /Users/<USER>/aihub/qrvibe/storage", "module": "main", "function": "<module>", "line": 119}
{"timestamp": "2025-06-11T15:40:02.993305", "level": "INFO", "logger": "root", "message": "Storage initialized: environment=test, storage_dir=/Users/<USER>/aihub/qrvibe/storage, s3_enabled=True", "module": "storage", "function": "__init__", "line": 41}
{"timestamp": "2025-06-11T15:40:02.996127", "level": "INFO", "logger": "root", "message": "S3 storage enabled with bucket: qrverse and CDN: https://dev-cdn.qrvibe.com", "module": "storage", "function": "__init__", "line": 59}
{"timestamp": "2025-06-11T15:40:03.045891", "level": "INFO", "logger": "root", "message": "Storage initialized: environment=test, storage_dir=/Users/<USER>/aihub/qrvibe/storage, s3_enabled=True", "module": "storage", "function": "__init__", "line": 41}
{"timestamp": "2025-06-11T15:40:03.049151", "level": "INFO", "logger": "root", "message": "S3 storage enabled with bucket: qrverse and CDN: https://dev-cdn.qrvibe.com", "module": "storage", "function": "__init__", "line": 59}
{"timestamp": "2025-06-11T15:40:03.172625", "level": "INFO", "logger": "backend.middleware.security_middleware", "message": "Enhanced security middleware initialized with tiered rate limiting", "module": "security_middleware", "function": "__init__", "line": 87}
{"timestamp": "2025-06-11T15:40:03.172733", "level": "INFO", "logger": "backend.core.middleware.csrf", "message": "CSRF Protection Middleware initialized with exclude paths: ['/auth/jwt/login', '/auth/logout', '/auth/register', '/auth/2fa', '/auth/me', '/auth/verify-email', '/auth/reset-password', '/admin/login', '/admin/logout']", "module": "csrf", "function": "__init__", "line": 44}
{"timestamp": "2025-06-11T15:40:03.178000", "level": "INFO", "logger": "backend.services.token_blacklist", "message": "Token blacklist service initialized with Redis", "module": "token_blacklist", "function": "initialize", "line": 59}
{"timestamp": "2025-06-11T15:40:03.178102", "level": "INFO", "logger": "backend.main", "message": "Token blacklist service initialized", "module": "main", "function": "initialize_token_blacklist", "line": 98}
{"timestamp": "2025-06-11T15:40:03.178741", "level": "INFO", "logger": "backend.services.qr_preview_cache", "message": "QR preview cache initialized at /Users/<USER>/aihub/qrvibe/storage/cache/qr_preview_cache.db", "module": "qr_preview_cache", "function": "_init_db", "line": 56}
{"timestamp": "2025-06-11T15:40:03.178835", "level": "INFO", "logger": "backend.services.qr_preview_cache", "message": "Started QR preview cache cleanup task", "module": "qr_preview_cache", "function": "start_cleanup_task", "line": 62}
{"timestamp": "2025-06-11T15:40:03.178895", "level": "INFO", "logger": "backend.main", "message": "QR preview cache initialized and cleanup task started", "module": "main", "function": "init_qr_preview_cache", "line": 345}
{"timestamp": "2025-06-11T15:40:27.778624", "level": "INFO", "logger": "backend.services.qr_preview_cache", "message": "QR preview cache cleanup task stopped", "module": "qr_preview_cache", "function": "stop", "line": 257}
{"timestamp": "2025-06-11T15:40:27.779229", "level": "INFO", "logger": "backend.main", "message": "QR preview cache stopped", "module": "main", "function": "shutdown_qr_preview_cache", "line": 352}
{"timestamp": "2025-06-11T15:40:31.284768", "level": "INFO", "logger": "backend.main", "message": "=== QRVibe API Startup: Logging to file is enabled ===", "module": "main", "function": "<module>", "line": 37}
{"timestamp": "2025-06-11T15:40:31.284937", "level": "INFO", "logger": "backend.main", "message": "Applying critical patches before initialization", "module": "main", "function": "<module>", "line": 43}
{"timestamp": "2025-06-11T15:40:31.285027", "level": "INFO", "logger": "backend.patches", "message": "Applying patches (none required)", "module": "__init__", "function": "apply_patches", "line": 11}
{"timestamp": "2025-06-11T15:40:31.285264", "level": "INFO", "logger": "backend.core.middleware.cors", "message": "Setting up CORS middleware with origins: ['https://127.0.0.1:3000', 'http://localhost:3000', 'http://127.0.0.1:3000', 'https://localhost:3000']", "module": "cors", "function": "setup_cors", "line": 86}
{"timestamp": "2025-06-11T15:40:31.285347", "level": "INFO", "logger": "backend.core.middleware.cors", "message": "CORS middleware configured - preflight requests should be handled automatically", "module": "cors", "function": "setup_cors", "line": 98}
{"timestamp": "2025-06-11T15:40:31.286288", "level": "INFO", "logger": "backend.middleware.security_middleware", "message": "Added Enhanced Security Middleware with tiered rate limiting", "module": "security_middleware", "function": "add_security_middleware", "line": 176}
{"timestamp": "2025-06-11T15:40:31.286398", "level": "INFO", "logger": "backend.main", "message": "Mounting storage directory: /Users/<USER>/aihub/qrvibe/storage", "module": "main", "function": "<module>", "line": 107}
{"timestamp": "2025-06-11T15:40:31.286523", "level": "INFO", "logger": "backend.main", "message": "Storage directory absolute path: /Users/<USER>/aihub/qrvibe/storage", "module": "main", "function": "<module>", "line": 119}
{"timestamp": "2025-06-11T15:40:31.722215", "level": "INFO", "logger": "root", "message": "Storage initialized: environment=test, storage_dir=/Users/<USER>/aihub/qrvibe/storage, s3_enabled=True", "module": "storage", "function": "__init__", "line": 41}
{"timestamp": "2025-06-11T15:40:31.739484", "level": "INFO", "logger": "root", "message": "S3 storage enabled with bucket: qrverse and CDN: https://dev-cdn.qrvibe.com", "module": "storage", "function": "__init__", "line": 59}
{"timestamp": "2025-06-11T15:40:31.796139", "level": "INFO", "logger": "root", "message": "Storage initialized: environment=test, storage_dir=/Users/<USER>/aihub/qrvibe/storage, s3_enabled=True", "module": "storage", "function": "__init__", "line": 41}
{"timestamp": "2025-06-11T15:40:31.799657", "level": "INFO", "logger": "root", "message": "S3 storage enabled with bucket: qrverse and CDN: https://dev-cdn.qrvibe.com", "module": "storage", "function": "__init__", "line": 59}
{"timestamp": "2025-06-11T15:40:31.826312", "level": "INFO", "logger": "backend.middleware.security_middleware", "message": "Enhanced security middleware initialized with tiered rate limiting", "module": "security_middleware", "function": "__init__", "line": 87}
{"timestamp": "2025-06-11T15:40:31.826399", "level": "INFO", "logger": "backend.core.middleware.csrf", "message": "CSRF Protection Middleware initialized with exclude paths: ['/auth/jwt/login', '/auth/logout', '/auth/register', '/auth/2fa', '/auth/me', '/auth/verify-email', '/auth/reset-password', '/admin/login', '/admin/logout']", "module": "csrf", "function": "__init__", "line": 44}
{"timestamp": "2025-06-11T15:40:31.830550", "level": "INFO", "logger": "backend.services.token_blacklist", "message": "Token blacklist service initialized with Redis", "module": "token_blacklist", "function": "initialize", "line": 59}
{"timestamp": "2025-06-11T15:40:31.830640", "level": "INFO", "logger": "backend.main", "message": "Token blacklist service initialized", "module": "main", "function": "initialize_token_blacklist", "line": 98}
{"timestamp": "2025-06-11T15:40:31.831351", "level": "INFO", "logger": "backend.services.qr_preview_cache", "message": "QR preview cache initialized at /Users/<USER>/aihub/qrvibe/storage/cache/qr_preview_cache.db", "module": "qr_preview_cache", "function": "_init_db", "line": 56}
{"timestamp": "2025-06-11T15:40:31.831436", "level": "INFO", "logger": "backend.services.qr_preview_cache", "message": "Started QR preview cache cleanup task", "module": "qr_preview_cache", "function": "start_cleanup_task", "line": 62}
{"timestamp": "2025-06-11T15:40:31.831491", "level": "INFO", "logger": "backend.main", "message": "QR preview cache initialized and cleanup task started", "module": "main", "function": "init_qr_preview_cache", "line": 345}
{"timestamp": "2025-06-11T15:41:03.710533", "level": "INFO", "logger": "backend.services.qr_preview_cache", "message": "QR preview cache cleanup task stopped", "module": "qr_preview_cache", "function": "stop", "line": 257}
{"timestamp": "2025-06-11T15:41:03.710832", "level": "INFO", "logger": "backend.main", "message": "QR preview cache stopped", "module": "main", "function": "shutdown_qr_preview_cache", "line": 352}
{"timestamp": "2025-06-11T15:41:07.197513", "level": "INFO", "logger": "backend.main", "message": "=== QRVibe API Startup: Logging to file is enabled ===", "module": "main", "function": "<module>", "line": 37}
{"timestamp": "2025-06-11T15:41:07.197602", "level": "INFO", "logger": "backend.main", "message": "Applying critical patches before initialization", "module": "main", "function": "<module>", "line": 43}
{"timestamp": "2025-06-11T15:41:07.197662", "level": "INFO", "logger": "backend.patches", "message": "Applying patches (none required)", "module": "__init__", "function": "apply_patches", "line": 11}
{"timestamp": "2025-06-11T15:41:07.197882", "level": "INFO", "logger": "backend.core.middleware.cors", "message": "Setting up CORS middleware with origins: ['http://localhost:3000', 'https://127.0.0.1:3000', 'https://localhost:3000', 'http://127.0.0.1:3000']", "module": "cors", "function": "setup_cors", "line": 86}
{"timestamp": "2025-06-11T15:41:07.197997", "level": "INFO", "logger": "backend.core.middleware.cors", "message": "CORS middleware configured - preflight requests should be handled automatically", "module": "cors", "function": "setup_cors", "line": 98}
{"timestamp": "2025-06-11T15:41:07.198903", "level": "INFO", "logger": "backend.middleware.security_middleware", "message": "Added Enhanced Security Middleware with tiered rate limiting", "module": "security_middleware", "function": "add_security_middleware", "line": 176}
{"timestamp": "2025-06-11T15:41:07.198977", "level": "INFO", "logger": "backend.main", "message": "Mounting storage directory: /Users/<USER>/aihub/qrvibe/storage", "module": "main", "function": "<module>", "line": 107}
{"timestamp": "2025-06-11T15:41:07.199075", "level": "INFO", "logger": "backend.main", "message": "Storage directory absolute path: /Users/<USER>/aihub/qrvibe/storage", "module": "main", "function": "<module>", "line": 119}
{"timestamp": "2025-06-11T15:41:07.616203", "level": "INFO", "logger": "root", "message": "Storage initialized: environment=test, storage_dir=/Users/<USER>/aihub/qrvibe/storage, s3_enabled=True", "module": "storage", "function": "__init__", "line": 41}
{"timestamp": "2025-06-11T15:41:07.619357", "level": "INFO", "logger": "root", "message": "S3 storage enabled with bucket: qrverse and CDN: https://dev-cdn.qrvibe.com", "module": "storage", "function": "__init__", "line": 59}
{"timestamp": "2025-06-11T15:41:07.725621", "level": "INFO", "logger": "root", "message": "Storage initialized: environment=test, storage_dir=/Users/<USER>/aihub/qrvibe/storage, s3_enabled=True", "module": "storage", "function": "__init__", "line": 41}
{"timestamp": "2025-06-11T15:41:07.728588", "level": "INFO", "logger": "root", "message": "S3 storage enabled with bucket: qrverse and CDN: https://dev-cdn.qrvibe.com", "module": "storage", "function": "__init__", "line": 59}
{"timestamp": "2025-06-11T15:41:07.751426", "level": "INFO", "logger": "backend.middleware.security_middleware", "message": "Enhanced security middleware initialized with tiered rate limiting", "module": "security_middleware", "function": "__init__", "line": 87}
{"timestamp": "2025-06-11T15:41:07.751537", "level": "INFO", "logger": "backend.core.middleware.csrf", "message": "CSRF Protection Middleware initialized with exclude paths: ['/auth/jwt/login', '/auth/logout', '/auth/register', '/auth/2fa', '/auth/me', '/auth/verify-email', '/auth/reset-password', '/admin/login', '/admin/logout']", "module": "csrf", "function": "__init__", "line": 44}
{"timestamp": "2025-06-11T15:41:07.756132", "level": "INFO", "logger": "backend.services.token_blacklist", "message": "Token blacklist service initialized with Redis", "module": "token_blacklist", "function": "initialize", "line": 59}
{"timestamp": "2025-06-11T15:41:07.756231", "level": "INFO", "logger": "backend.main", "message": "Token blacklist service initialized", "module": "main", "function": "initialize_token_blacklist", "line": 98}
{"timestamp": "2025-06-11T15:41:07.756857", "level": "INFO", "logger": "backend.services.qr_preview_cache", "message": "QR preview cache initialized at /Users/<USER>/aihub/qrvibe/storage/cache/qr_preview_cache.db", "module": "qr_preview_cache", "function": "_init_db", "line": 56}
{"timestamp": "2025-06-11T15:41:07.756923", "level": "INFO", "logger": "backend.services.qr_preview_cache", "message": "Started QR preview cache cleanup task", "module": "qr_preview_cache", "function": "start_cleanup_task", "line": 62}
{"timestamp": "2025-06-11T15:41:07.756978", "level": "INFO", "logger": "backend.main", "message": "QR preview cache initialized and cleanup task started", "module": "main", "function": "init_qr_preview_cache", "line": 345}
{"timestamp": "2025-06-11T15:42:14.148793", "level": "INFO", "logger": "backend.services.qr_preview_cache", "message": "QR preview cache cleanup task stopped", "module": "qr_preview_cache", "function": "stop", "line": 257}
{"timestamp": "2025-06-11T15:42:14.149114", "level": "INFO", "logger": "backend.main", "message": "QR preview cache stopped", "module": "main", "function": "shutdown_qr_preview_cache", "line": 352}
{"timestamp": "2025-06-11T15:42:30.631377", "level": "INFO", "logger": "backend.main", "message": "=== QRVibe API Startup: Logging to file is enabled ===", "module": "main", "function": "<module>", "line": 37}
{"timestamp": "2025-06-11T15:42:30.631860", "level": "INFO", "logger": "backend.main", "message": "Applying critical patches before initialization", "module": "main", "function": "<module>", "line": 43}
{"timestamp": "2025-06-11T15:42:30.631945", "level": "INFO", "logger": "backend.patches", "message": "Applying patches (none required)", "module": "__init__", "function": "apply_patches", "line": 11}
{"timestamp": "2025-06-11T15:42:30.632201", "level": "INFO", "logger": "backend.core.middleware.cors", "message": "Setting up CORS middleware with origins: ['https://localhost:3000', 'https://127.0.0.1:3000', 'http://127.0.0.1:3000', 'http://localhost:3000']", "module": "cors", "function": "setup_cors", "line": 86}
{"timestamp": "2025-06-11T15:42:30.632266", "level": "INFO", "logger": "backend.core.middleware.cors", "message": "CORS middleware configured - preflight requests should be handled automatically", "module": "cors", "function": "setup_cors", "line": 98}
{"timestamp": "2025-06-11T15:42:30.633402", "level": "INFO", "logger": "backend.middleware.security_middleware", "message": "Added Enhanced Security Middleware with tiered rate limiting", "module": "security_middleware", "function": "add_security_middleware", "line": 176}
{"timestamp": "2025-06-11T15:42:30.633528", "level": "INFO", "logger": "backend.main", "message": "Mounting storage directory: /Users/<USER>/aihub/qrvibe/storage", "module": "main", "function": "<module>", "line": 107}
{"timestamp": "2025-06-11T15:42:30.633691", "level": "INFO", "logger": "backend.main", "message": "Storage directory absolute path: /Users/<USER>/aihub/qrvibe/storage", "module": "main", "function": "<module>", "line": 119}
{"timestamp": "2025-06-11T15:42:31.068178", "level": "INFO", "logger": "root", "message": "Storage initialized: environment=test, storage_dir=/Users/<USER>/aihub/qrvibe/storage, s3_enabled=True", "module": "storage", "function": "__init__", "line": 41}
{"timestamp": "2025-06-11T15:42:31.071233", "level": "INFO", "logger": "root", "message": "S3 storage enabled with bucket: qrverse and CDN: https://dev-cdn.qrvibe.com", "module": "storage", "function": "__init__", "line": 59}
{"timestamp": "2025-06-11T15:42:31.118104", "level": "INFO", "logger": "root", "message": "Storage initialized: environment=test, storage_dir=/Users/<USER>/aihub/qrvibe/storage, s3_enabled=True", "module": "storage", "function": "__init__", "line": 41}
{"timestamp": "2025-06-11T15:42:31.121210", "level": "INFO", "logger": "root", "message": "S3 storage enabled with bucket: qrverse and CDN: https://dev-cdn.qrvibe.com", "module": "storage", "function": "__init__", "line": 59}
{"timestamp": "2025-06-11T15:42:31.143929", "level": "INFO", "logger": "backend.middleware.security_middleware", "message": "Enhanced security middleware initialized with tiered rate limiting", "module": "security_middleware", "function": "__init__", "line": 87}
{"timestamp": "2025-06-11T15:42:31.144040", "level": "INFO", "logger": "backend.core.middleware.csrf", "message": "CSRF Protection Middleware initialized with exclude paths: ['/auth/jwt/login', '/auth/logout', '/auth/register', '/auth/2fa', '/auth/me', '/auth/verify-email', '/auth/reset-password', '/admin/login', '/admin/logout']", "module": "csrf", "function": "__init__", "line": 44}
{"timestamp": "2025-06-11T15:42:31.172973", "level": "INFO", "logger": "backend.services.token_blacklist", "message": "Token blacklist service initialized with Redis", "module": "token_blacklist", "function": "initialize", "line": 59}
{"timestamp": "2025-06-11T15:42:31.173149", "level": "INFO", "logger": "backend.main", "message": "Token blacklist service initialized", "module": "main", "function": "initialize_token_blacklist", "line": 98}
{"timestamp": "2025-06-11T15:42:31.173893", "level": "INFO", "logger": "backend.services.qr_preview_cache", "message": "QR preview cache initialized at /Users/<USER>/aihub/qrvibe/storage/cache/qr_preview_cache.db", "module": "qr_preview_cache", "function": "_init_db", "line": 56}
{"timestamp": "2025-06-11T15:42:31.174053", "level": "INFO", "logger": "backend.services.qr_preview_cache", "message": "Started QR preview cache cleanup task", "module": "qr_preview_cache", "function": "start_cleanup_task", "line": 62}
{"timestamp": "2025-06-11T15:42:31.174139", "level": "INFO", "logger": "backend.main", "message": "QR preview cache initialized and cleanup task started", "module": "main", "function": "init_qr_preview_cache", "line": 345}
{"timestamp": "2025-06-11T15:42:50.915168", "level": "WARNING", "logger": "backend.core.security.jwt_keys", "message": "Unknown JWT kid requested: 244d2fff-8665-4c6c-b84d-c7c2b581bb0c", "module": "jwt_keys", "function": "get_verification_key", "line": 105}
{"timestamp": "2025-06-11T15:42:50.917158", "level": "WARNING", "logger": "backend.core.security.jwt_keys", "message": "Verification failed with key ID 244d2fff-8665-4c6c-b84d-c7c2b581bb0c: Unknown kid: 244d2fff-8665-4c6c-b84d-c7c2b581bb0c", "module": "jwt_keys", "function": "decode_token", "line": 203}
{"timestamp": "2025-06-11T15:42:50.990016", "level": "WARNING", "logger": "backend.core.security.jwt_keys", "message": "Verification failed with current key: Signature verification failed.", "module": "jwt_keys", "function": "decode_token", "line": 216}
{"timestamp": "2025-06-11T15:42:50.990492", "level": "ERROR", "logger": "backend.core.security.jwt_keys", "message": "Token verification failed with all keys. Errors: Key 69b5ada0-20e9-42b1-85e8-1ec80762bb7c: Signature verification failed.", "module": "jwt_keys", "function": "decode_token", "line": 239}
{"timestamp": "2025-06-11T15:42:50.990593", "level": "ERROR", "logger": "backend.core.security.jwt_keys", "message": "Token decode error: Token verification failed with all keys. Errors: Key 69b5ada0-20e9-42b1-85e8-1ec80762bb7c: Signature verification failed.", "module": "jwt_keys", "function": "decode_token", "line": 244}
{"timestamp": "2025-06-11T15:42:50.990692", "level": "ERROR", "logger": "backend.services.auth", "message": "JWT token validation failed: Failed to decode token: Token verification failed with all keys. Errors: Key 69b5ada0-20e9-42b1-85e8-1ec80762bb7c: Signature verification failed.", "module": "auth", "function": "authenticate_token", "line": 686}
{"timestamp": "2025-06-11T15:42:50.991540", "level": "WARNING", "logger": "backend.middleware.subscription_limits", "message": "Error authenticating token in subscription middleware: name 'ValidationError' is not defined", "module": "subscription_limits", "function": "_get_current_user", "line": 308}
{"timestamp": "2025-06-11T15:42:50.995754", "level": "WARNING", "logger": "backend.core.security.jwt_keys", "message": "Unknown JWT kid requested: 244d2fff-8665-4c6c-b84d-c7c2b581bb0c", "module": "jwt_keys", "function": "get_verification_key", "line": 105}
{"timestamp": "2025-06-11T15:42:50.996027", "level": "WARNING", "logger": "backend.core.security.jwt_keys", "message": "Verification failed with key ID 244d2fff-8665-4c6c-b84d-c7c2b581bb0c: Unknown kid: 244d2fff-8665-4c6c-b84d-c7c2b581bb0c", "module": "jwt_keys", "function": "decode_token", "line": 203}
{"timestamp": "2025-06-11T15:42:50.996215", "level": "WARNING", "logger": "backend.core.security.jwt_keys", "message": "Verification failed with current key: Signature verification failed.", "module": "jwt_keys", "function": "decode_token", "line": 216}
{"timestamp": "2025-06-11T15:42:50.996375", "level": "ERROR", "logger": "backend.core.security.jwt_keys", "message": "Token verification failed with all keys. Errors: Key 69b5ada0-20e9-42b1-85e8-1ec80762bb7c: Signature verification failed.", "module": "jwt_keys", "function": "decode_token", "line": 239}
{"timestamp": "2025-06-11T15:42:50.996462", "level": "ERROR", "logger": "backend.core.security.jwt_keys", "message": "Token decode error: Token verification failed with all keys. Errors: Key 69b5ada0-20e9-42b1-85e8-1ec80762bb7c: Signature verification failed.", "module": "jwt_keys", "function": "decode_token", "line": 244}
{"timestamp": "2025-06-11T15:42:50.996537", "level": "ERROR", "logger": "backend.services.auth", "message": "JWT token validation failed: Failed to decode token: Token verification failed with all keys. Errors: Key 69b5ada0-20e9-42b1-85e8-1ec80762bb7c: Signature verification failed.", "module": "auth", "function": "authenticate_token", "line": 686}
{"timestamp": "2025-06-11T15:42:50.997143", "level": "WARNING", "logger": "backend.middleware.subscription_limits", "message": "Auth error in subscription middleware: name 'ValidationError' is not defined", "module": "subscription_limits", "function": "dispatch", "line": 234}
{"timestamp": "2025-06-11T15:42:50.997660", "level": "WARNING", "logger": "backend.core.security.jwt_keys", "message": "Unknown JWT kid requested: 244d2fff-8665-4c6c-b84d-c7c2b581bb0c", "module": "jwt_keys", "function": "get_verification_key", "line": 105}
{"timestamp": "2025-06-11T15:42:50.997759", "level": "WARNING", "logger": "backend.core.security.jwt_keys", "message": "Verification failed with key ID 244d2fff-8665-4c6c-b84d-c7c2b581bb0c: Unknown kid: 244d2fff-8665-4c6c-b84d-c7c2b581bb0c", "module": "jwt_keys", "function": "decode_token", "line": 203}
{"timestamp": "2025-06-11T15:42:50.998053", "level": "WARNING", "logger": "backend.core.security.jwt_keys", "message": "Verification failed with current key: Signature verification failed.", "module": "jwt_keys", "function": "decode_token", "line": 216}
{"timestamp": "2025-06-11T15:42:50.998264", "level": "ERROR", "logger": "backend.core.security.jwt_keys", "message": "Token verification failed with all keys. Errors: Key 69b5ada0-20e9-42b1-85e8-1ec80762bb7c: Signature verification failed.", "module": "jwt_keys", "function": "decode_token", "line": 239}
{"timestamp": "2025-06-11T15:42:50.998518", "level": "ERROR", "logger": "backend.core.security.jwt_keys", "message": "Token decode error: Token verification failed with all keys. Errors: Key 69b5ada0-20e9-42b1-85e8-1ec80762bb7c: Signature verification failed.", "module": "jwt_keys", "function": "decode_token", "line": 244}
{"timestamp": "2025-06-11T15:42:50.998668", "level": "ERROR", "logger": "backend.services.auth", "message": "JWT token validation failed: Failed to decode token: Token verification failed with all keys. Errors: Key 69b5ada0-20e9-42b1-85e8-1ec80762bb7c: Signature verification failed.", "module": "auth", "function": "authenticate_token", "line": 686}
{"timestamp": "2025-06-11T15:43:20.981776", "level": "WARNING", "logger": "backend.core.security.jwt_keys", "message": "Unknown JWT kid requested: 244d2fff-8665-4c6c-b84d-c7c2b581bb0c", "module": "jwt_keys", "function": "get_verification_key", "line": 105}
{"timestamp": "2025-06-11T15:43:20.981891", "level": "WARNING", "logger": "backend.core.security.jwt_keys", "message": "Verification failed with key ID 244d2fff-8665-4c6c-b84d-c7c2b581bb0c: Unknown kid: 244d2fff-8665-4c6c-b84d-c7c2b581bb0c", "module": "jwt_keys", "function": "decode_token", "line": 203}
{"timestamp": "2025-06-11T15:43:20.984702", "level": "WARNING", "logger": "backend.core.security.jwt_keys", "message": "Verification failed with current key: Signature verification failed.", "module": "jwt_keys", "function": "decode_token", "line": 216}
{"timestamp": "2025-06-11T15:43:20.985014", "level": "ERROR", "logger": "backend.core.security.jwt_keys", "message": "Token verification failed with all keys. Errors: Key 69b5ada0-20e9-42b1-85e8-1ec80762bb7c: Signature verification failed.", "module": "jwt_keys", "function": "decode_token", "line": 239}
{"timestamp": "2025-06-11T15:43:20.985102", "level": "ERROR", "logger": "backend.core.security.jwt_keys", "message": "Token decode error: Token verification failed with all keys. Errors: Key 69b5ada0-20e9-42b1-85e8-1ec80762bb7c: Signature verification failed.", "module": "jwt_keys", "function": "decode_token", "line": 244}
{"timestamp": "2025-06-11T15:43:20.985199", "level": "ERROR", "logger": "backend.services.auth", "message": "JWT token validation failed: Failed to decode token: Token verification failed with all keys. Errors: Key 69b5ada0-20e9-42b1-85e8-1ec80762bb7c: Signature verification failed.", "module": "auth", "function": "authenticate_token", "line": 686}
{"timestamp": "2025-06-11T15:43:20.985695", "level": "WARNING", "logger": "backend.middleware.subscription_limits", "message": "Error authenticating token in subscription middleware: name 'ValidationError' is not defined", "module": "subscription_limits", "function": "_get_current_user", "line": 308}
{"timestamp": "2025-06-11T15:43:20.986399", "level": "WARNING", "logger": "backend.core.security.jwt_keys", "message": "Unknown JWT kid requested: 244d2fff-8665-4c6c-b84d-c7c2b581bb0c", "module": "jwt_keys", "function": "get_verification_key", "line": 105}
{"timestamp": "2025-06-11T15:43:20.986460", "level": "WARNING", "logger": "backend.core.security.jwt_keys", "message": "Verification failed with key ID 244d2fff-8665-4c6c-b84d-c7c2b581bb0c: Unknown kid: 244d2fff-8665-4c6c-b84d-c7c2b581bb0c", "module": "jwt_keys", "function": "decode_token", "line": 203}
{"timestamp": "2025-06-11T15:43:20.986613", "level": "WARNING", "logger": "backend.core.security.jwt_keys", "message": "Verification failed with current key: Signature verification failed.", "module": "jwt_keys", "function": "decode_token", "line": 216}
{"timestamp": "2025-06-11T15:43:20.986755", "level": "ERROR", "logger": "backend.core.security.jwt_keys", "message": "Token verification failed with all keys. Errors: Key 69b5ada0-20e9-42b1-85e8-1ec80762bb7c: Signature verification failed.", "module": "jwt_keys", "function": "decode_token", "line": 239}
{"timestamp": "2025-06-11T15:43:20.986823", "level": "ERROR", "logger": "backend.core.security.jwt_keys", "message": "Token decode error: Token verification failed with all keys. Errors: Key 69b5ada0-20e9-42b1-85e8-1ec80762bb7c: Signature verification failed.", "module": "jwt_keys", "function": "decode_token", "line": 244}
{"timestamp": "2025-06-11T15:43:20.986882", "level": "ERROR", "logger": "backend.services.auth", "message": "JWT token validation failed: Failed to decode token: Token verification failed with all keys. Errors: Key 69b5ada0-20e9-42b1-85e8-1ec80762bb7c: Signature verification failed.", "module": "auth", "function": "authenticate_token", "line": 686}
{"timestamp": "2025-06-11T15:43:20.987213", "level": "WARNING", "logger": "backend.middleware.subscription_limits", "message": "Auth error in subscription middleware: name 'ValidationError' is not defined", "module": "subscription_limits", "function": "dispatch", "line": 234}
{"timestamp": "2025-06-11T15:43:20.987531", "level": "WARNING", "logger": "backend.core.security.jwt_keys", "message": "Unknown JWT kid requested: 244d2fff-8665-4c6c-b84d-c7c2b581bb0c", "module": "jwt_keys", "function": "get_verification_key", "line": 105}
{"timestamp": "2025-06-11T15:43:20.987585", "level": "WARNING", "logger": "backend.core.security.jwt_keys", "message": "Verification failed with key ID 244d2fff-8665-4c6c-b84d-c7c2b581bb0c: Unknown kid: 244d2fff-8665-4c6c-b84d-c7c2b581bb0c", "module": "jwt_keys", "function": "decode_token", "line": 203}
{"timestamp": "2025-06-11T15:43:20.988705", "level": "WARNING", "logger": "backend.core.security.jwt_keys", "message": "Verification failed with current key: Signature verification failed.", "module": "jwt_keys", "function": "decode_token", "line": 216}
{"timestamp": "2025-06-11T15:43:20.988839", "level": "ERROR", "logger": "backend.core.security.jwt_keys", "message": "Token verification failed with all keys. Errors: Key 69b5ada0-20e9-42b1-85e8-1ec80762bb7c: Signature verification failed.", "module": "jwt_keys", "function": "decode_token", "line": 239}
{"timestamp": "2025-06-11T15:43:20.988928", "level": "ERROR", "logger": "backend.core.security.jwt_keys", "message": "Token decode error: Token verification failed with all keys. Errors: Key 69b5ada0-20e9-42b1-85e8-1ec80762bb7c: Signature verification failed.", "module": "jwt_keys", "function": "decode_token", "line": 244}
{"timestamp": "2025-06-11T15:43:20.988988", "level": "ERROR", "logger": "backend.services.auth", "message": "JWT token validation failed: Failed to decode token: Token verification failed with all keys. Errors: Key 69b5ada0-20e9-42b1-85e8-1ec80762bb7c: Signature verification failed.", "module": "auth", "function": "authenticate_token", "line": 686}
{"timestamp": "2025-06-11T15:43:39.524943", "level": "WARNING", "logger": "backend.core.security.jwt_keys", "message": "Unknown JWT kid requested: 244d2fff-8665-4c6c-b84d-c7c2b581bb0c", "module": "jwt_keys", "function": "get_verification_key", "line": 105}
{"timestamp": "2025-06-11T15:43:39.525283", "level": "WARNING", "logger": "backend.core.security.jwt_keys", "message": "Verification failed with key ID 244d2fff-8665-4c6c-b84d-c7c2b581bb0c: Unknown kid: 244d2fff-8665-4c6c-b84d-c7c2b581bb0c", "module": "jwt_keys", "function": "decode_token", "line": 203}
{"timestamp": "2025-06-11T15:43:39.526189", "level": "WARNING", "logger": "backend.core.security.jwt_keys", "message": "Verification failed with current key: Signature verification failed.", "module": "jwt_keys", "function": "decode_token", "line": 216}
{"timestamp": "2025-06-11T15:43:39.526889", "level": "ERROR", "logger": "backend.core.security.jwt_keys", "message": "Token verification failed with all keys. Errors: Key 69b5ada0-20e9-42b1-85e8-1ec80762bb7c: Signature verification failed.", "module": "jwt_keys", "function": "decode_token", "line": 239}
{"timestamp": "2025-06-11T15:43:39.527011", "level": "ERROR", "logger": "backend.core.security.jwt_keys", "message": "Token decode error: Token verification failed with all keys. Errors: Key 69b5ada0-20e9-42b1-85e8-1ec80762bb7c: Signature verification failed.", "module": "jwt_keys", "function": "decode_token", "line": 244}
{"timestamp": "2025-06-11T15:43:39.527194", "level": "ERROR", "logger": "backend.auth", "message": "Token validation error: Failed to decode token: Token verification failed with all keys. Errors: Key 69b5ada0-20e9-42b1-85e8-1ec80762bb7c: Signature verification failed.", "module": "__init__", "function": "get_current_user", "line": 193}
{"timestamp": "2025-06-11T15:43:39.528973", "level": "WARNING", "logger": "backend.core.security.jwt_keys", "message": "Unknown JWT kid requested: 244d2fff-8665-4c6c-b84d-c7c2b581bb0c", "module": "jwt_keys", "function": "get_verification_key", "line": 105}
{"timestamp": "2025-06-11T15:43:39.529040", "level": "WARNING", "logger": "backend.core.security.jwt_keys", "message": "Verification failed with key ID 244d2fff-8665-4c6c-b84d-c7c2b581bb0c: Unknown kid: 244d2fff-8665-4c6c-b84d-c7c2b581bb0c", "module": "jwt_keys", "function": "decode_token", "line": 203}
{"timestamp": "2025-06-11T15:43:39.529233", "level": "WARNING", "logger": "backend.core.security.jwt_keys", "message": "Verification failed with current key: Signature verification failed.", "module": "jwt_keys", "function": "decode_token", "line": 216}
{"timestamp": "2025-06-11T15:43:39.529392", "level": "ERROR", "logger": "backend.core.security.jwt_keys", "message": "Token verification failed with all keys. Errors: Key 69b5ada0-20e9-42b1-85e8-1ec80762bb7c: Signature verification failed.", "module": "jwt_keys", "function": "decode_token", "line": 239}
{"timestamp": "2025-06-11T15:43:39.529456", "level": "ERROR", "logger": "backend.core.security.jwt_keys", "message": "Token decode error: Token verification failed with all keys. Errors: Key 69b5ada0-20e9-42b1-85e8-1ec80762bb7c: Signature verification failed.", "module": "jwt_keys", "function": "decode_token", "line": 244}
{"timestamp": "2025-06-11T15:43:39.529553", "level": "ERROR", "logger": "backend.services.auth", "message": "JWT token validation failed: Failed to decode token: Token verification failed with all keys. Errors: Key 69b5ada0-20e9-42b1-85e8-1ec80762bb7c: Signature verification failed.", "module": "auth", "function": "authenticate_token", "line": 686}
{"timestamp": "2025-06-11T15:43:39.530505", "level": "WARNING", "logger": "backend.middleware.subscription_limits", "message": "Error authenticating token in subscription middleware: name 'ValidationError' is not defined", "module": "subscription_limits", "function": "_get_current_user", "line": 308}
{"timestamp": "2025-06-11T15:43:39.531119", "level": "WARNING", "logger": "backend.core.security.jwt_keys", "message": "Unknown JWT kid requested: 244d2fff-8665-4c6c-b84d-c7c2b581bb0c", "module": "jwt_keys", "function": "get_verification_key", "line": 105}
{"timestamp": "2025-06-11T15:43:39.531174", "level": "WARNING", "logger": "backend.core.security.jwt_keys", "message": "Verification failed with key ID 244d2fff-8665-4c6c-b84d-c7c2b581bb0c: Unknown kid: 244d2fff-8665-4c6c-b84d-c7c2b581bb0c", "module": "jwt_keys", "function": "decode_token", "line": 203}
{"timestamp": "2025-06-11T15:43:39.531315", "level": "WARNING", "logger": "backend.core.security.jwt_keys", "message": "Verification failed with current key: Signature verification failed.", "module": "jwt_keys", "function": "decode_token", "line": 216}
{"timestamp": "2025-06-11T15:43:39.531454", "level": "ERROR", "logger": "backend.core.security.jwt_keys", "message": "Token verification failed with all keys. Errors: Key 69b5ada0-20e9-42b1-85e8-1ec80762bb7c: Signature verification failed.", "module": "jwt_keys", "function": "decode_token", "line": 239}
{"timestamp": "2025-06-11T15:43:39.531526", "level": "ERROR", "logger": "backend.core.security.jwt_keys", "message": "Token decode error: Token verification failed with all keys. Errors: Key 69b5ada0-20e9-42b1-85e8-1ec80762bb7c: Signature verification failed.", "module": "jwt_keys", "function": "decode_token", "line": 244}
{"timestamp": "2025-06-11T15:43:39.531603", "level": "ERROR", "logger": "backend.auth", "message": "Token validation error: Failed to decode token: Token verification failed with all keys. Errors: Key 69b5ada0-20e9-42b1-85e8-1ec80762bb7c: Signature verification failed.", "module": "__init__", "function": "get_current_user", "line": 193}
{"timestamp": "2025-06-11T15:43:51.093014", "level": "INFO", "logger": "backend.services.enhanced_rate_limit", "message": "Auth event for anonymous: login_attempt (failure)", "module": "enhanced_rate_limit", "function": "track_auth_event", "line": 182}
{"timestamp": "2025-06-11T15:43:53.422274", "level": "INFO", "logger": "backend.services.auth", "message": "Authentication successful for user: <EMAIL>", "module": "auth", "function": "authenticate_user", "line": 266}
{"timestamp": "2025-06-11T15:43:53.426274", "level": "INFO", "logger": "backend.routes.auth", "message": "User logged in successfully: <EMAIL>", "module": "auth", "function": "login", "line": 218}
{"timestamp": "2025-06-11T15:43:53.427919", "level": "INFO", "logger": "backend.services.enhanced_rate_limit", "message": "Auth event for user e4ae2d8f-b32f-52d7-99cd-8a33b19652c7: login_success (success)", "module": "enhanced_rate_limit", "function": "track_auth_event", "line": 182}
{"timestamp": "2025-06-11T15:43:54.071912", "level": "INFO", "logger": "backend.services.adaptive_session", "message": "Adaptive session for user e4ae2d8f-b32f-52d7-99cd-8a33b19652c7: risk=low, duration=86400s", "module": "adaptive_session", "function": "get_session_expiry", "line": 161}
{"timestamp": "2025-06-11T15:43:54.072018", "level": "INFO", "logger": "backend.routes.auth", "message": "Adaptive session <NAME_EMAIL>: duration=86400s", "module": "auth", "function": "login", "line": 279}
{"timestamp": "2025-06-11T15:43:54.072072", "level": "INFO", "logger": "backend.routes.auth", "message": "Bearer token authentication issued <NAME_EMAIL>", "module": "auth", "function": "login", "line": 284}
{"timestamp": "2025-06-11T15:47:19.897947", "level": "INFO", "logger": "backend.services.qr_preview_cache", "message": "QR preview cache cleanup task stopped", "module": "qr_preview_cache", "function": "stop", "line": 257}
{"timestamp": "2025-06-11T15:47:19.898560", "level": "INFO", "logger": "backend.main", "message": "QR preview cache stopped", "module": "main", "function": "shutdown_qr_preview_cache", "line": 352}
{"timestamp": "2025-06-11T15:47:23.888550", "level": "INFO", "logger": "backend.main", "message": "=== QRVibe API Startup: Logging to file is enabled ===", "module": "main", "function": "<module>", "line": 37}
{"timestamp": "2025-06-11T15:47:23.888643", "level": "INFO", "logger": "backend.main", "message": "Applying critical patches before initialization", "module": "main", "function": "<module>", "line": 43}
{"timestamp": "2025-06-11T15:47:23.888722", "level": "INFO", "logger": "backend.patches", "message": "Applying patches (none required)", "module": "__init__", "function": "apply_patches", "line": 11}
{"timestamp": "2025-06-11T15:47:23.888973", "level": "INFO", "logger": "backend.core.middleware.cors", "message": "Setting up CORS middleware with origins: ['https://127.0.0.1:3000', 'http://127.0.0.1:3000', 'http://localhost:3000', 'https://localhost:3000']", "module": "cors", "function": "setup_cors", "line": 86}
{"timestamp": "2025-06-11T15:47:23.889038", "level": "INFO", "logger": "backend.core.middleware.cors", "message": "CORS middleware configured - preflight requests should be handled automatically", "module": "cors", "function": "setup_cors", "line": 98}
{"timestamp": "2025-06-11T15:47:23.890124", "level": "INFO", "logger": "backend.middleware.security_middleware", "message": "Added Enhanced Security Middleware with tiered rate limiting", "module": "security_middleware", "function": "add_security_middleware", "line": 176}
{"timestamp": "2025-06-11T15:47:23.890230", "level": "INFO", "logger": "backend.main", "message": "Mounting storage directory: /Users/<USER>/aihub/qrvibe/storage", "module": "main", "function": "<module>", "line": 107}
{"timestamp": "2025-06-11T15:47:23.890343", "level": "INFO", "logger": "backend.main", "message": "Storage directory absolute path: /Users/<USER>/aihub/qrvibe/storage", "module": "main", "function": "<module>", "line": 119}
{"timestamp": "2025-06-11T15:47:24.318431", "level": "INFO", "logger": "root", "message": "Storage initialized: environment=test, storage_dir=/Users/<USER>/aihub/qrvibe/storage, s3_enabled=True", "module": "storage", "function": "__init__", "line": 41}
{"timestamp": "2025-06-11T15:47:24.321232", "level": "INFO", "logger": "root", "message": "S3 storage enabled with bucket: qrverse and CDN: https://dev-cdn.qrvibe.com", "module": "storage", "function": "__init__", "line": 59}
{"timestamp": "2025-06-11T15:47:24.368855", "level": "INFO", "logger": "root", "message": "Storage initialized: environment=test, storage_dir=/Users/<USER>/aihub/qrvibe/storage, s3_enabled=True", "module": "storage", "function": "__init__", "line": 41}
{"timestamp": "2025-06-11T15:47:24.372281", "level": "INFO", "logger": "root", "message": "S3 storage enabled with bucket: qrverse and CDN: https://dev-cdn.qrvibe.com", "module": "storage", "function": "__init__", "line": 59}
{"timestamp": "2025-06-11T15:47:24.398439", "level": "INFO", "logger": "backend.middleware.security_middleware", "message": "Enhanced security middleware initialized with tiered rate limiting", "module": "security_middleware", "function": "__init__", "line": 87}
{"timestamp": "2025-06-11T15:47:24.398559", "level": "INFO", "logger": "backend.core.middleware.csrf", "message": "CSRF Protection Middleware initialized with exclude paths: ['/auth/jwt/login', '/auth/logout', '/auth/register', '/auth/2fa', '/auth/me', '/auth/verify-email', '/auth/reset-password', '/admin/login', '/admin/logout']", "module": "csrf", "function": "__init__", "line": 44}
{"timestamp": "2025-06-11T15:47:24.405052", "level": "INFO", "logger": "backend.services.token_blacklist", "message": "Token blacklist service initialized with Redis", "module": "token_blacklist", "function": "initialize", "line": 59}
{"timestamp": "2025-06-11T15:47:24.405134", "level": "INFO", "logger": "backend.main", "message": "Token blacklist service initialized", "module": "main", "function": "initialize_token_blacklist", "line": 98}
{"timestamp": "2025-06-11T15:47:24.408449", "level": "INFO", "logger": "backend.services.qr_preview_cache", "message": "QR preview cache initialized at /Users/<USER>/aihub/qrvibe/storage/cache/qr_preview_cache.db", "module": "qr_preview_cache", "function": "_init_db", "line": 56}
{"timestamp": "2025-06-11T15:47:24.408531", "level": "INFO", "logger": "backend.services.qr_preview_cache", "message": "Started QR preview cache cleanup task", "module": "qr_preview_cache", "function": "start_cleanup_task", "line": 62}
{"timestamp": "2025-06-11T15:47:24.408591", "level": "INFO", "logger": "backend.main", "message": "QR preview cache initialized and cleanup task started", "module": "main", "function": "init_qr_preview_cache", "line": 345}
{"timestamp": "2025-06-11T15:47:48.220066", "level": "WARNING", "logger": "backend.core.security.jwt_keys", "message": "Unknown JWT kid requested: 69b5ada0-20e9-42b1-85e8-1ec80762bb7c", "module": "jwt_keys", "function": "get_verification_key", "line": 105}
{"timestamp": "2025-06-11T15:47:48.220263", "level": "WARNING", "logger": "backend.core.security.jwt_keys", "message": "Verification failed with key ID 69b5ada0-20e9-42b1-85e8-1ec80762bb7c: Unknown kid: 69b5ada0-20e9-42b1-85e8-1ec80762bb7c", "module": "jwt_keys", "function": "decode_token", "line": 203}
{"timestamp": "2025-06-11T15:47:48.226591", "level": "WARNING", "logger": "backend.core.security.jwt_keys", "message": "Verification failed with current key: Signature verification failed.", "module": "jwt_keys", "function": "decode_token", "line": 216}
{"timestamp": "2025-06-11T15:47:48.226871", "level": "ERROR", "logger": "backend.core.security.jwt_keys", "message": "Token verification failed with all keys. Errors: Key 8b5606ee-4d52-44eb-b3da-2d209354097c: Signature verification failed.", "module": "jwt_keys", "function": "decode_token", "line": 239}
{"timestamp": "2025-06-11T15:47:48.226950", "level": "ERROR", "logger": "backend.core.security.jwt_keys", "message": "Token decode error: Token verification failed with all keys. Errors: Key 8b5606ee-4d52-44eb-b3da-2d209354097c: Signature verification failed.", "module": "jwt_keys", "function": "decode_token", "line": 244}
{"timestamp": "2025-06-11T15:47:48.227042", "level": "ERROR", "logger": "backend.services.auth", "message": "JWT token validation failed: Failed to decode token: Token verification failed with all keys. Errors: Key 8b5606ee-4d52-44eb-b3da-2d209354097c: Signature verification failed.", "module": "auth", "function": "authenticate_token", "line": 686}
{"timestamp": "2025-06-11T15:47:48.227868", "level": "WARNING", "logger": "backend.middleware.subscription_limits", "message": "Error authenticating token in subscription middleware: name 'ValidationError' is not defined", "module": "subscription_limits", "function": "_get_current_user", "line": 308}
{"timestamp": "2025-06-11T15:47:48.229283", "level": "WARNING", "logger": "backend.core.security.jwt_keys", "message": "Unknown JWT kid requested: 69b5ada0-20e9-42b1-85e8-1ec80762bb7c", "module": "jwt_keys", "function": "get_verification_key", "line": 105}
{"timestamp": "2025-06-11T15:47:48.229937", "level": "WARNING", "logger": "backend.core.security.jwt_keys", "message": "Verification failed with key ID 69b5ada0-20e9-42b1-85e8-1ec80762bb7c: Unknown kid: 69b5ada0-20e9-42b1-85e8-1ec80762bb7c", "module": "jwt_keys", "function": "decode_token", "line": 203}
{"timestamp": "2025-06-11T15:47:48.230223", "level": "WARNING", "logger": "backend.core.security.jwt_keys", "message": "Verification failed with current key: Signature verification failed.", "module": "jwt_keys", "function": "decode_token", "line": 216}
{"timestamp": "2025-06-11T15:47:48.230396", "level": "ERROR", "logger": "backend.core.security.jwt_keys", "message": "Token verification failed with all keys. Errors: Key 8b5606ee-4d52-44eb-b3da-2d209354097c: Signature verification failed.", "module": "jwt_keys", "function": "decode_token", "line": 239}
{"timestamp": "2025-06-11T15:47:48.230464", "level": "ERROR", "logger": "backend.core.security.jwt_keys", "message": "Token decode error: Token verification failed with all keys. Errors: Key 8b5606ee-4d52-44eb-b3da-2d209354097c: Signature verification failed.", "module": "jwt_keys", "function": "decode_token", "line": 244}
{"timestamp": "2025-06-11T15:47:48.230542", "level": "ERROR", "logger": "backend.services.auth", "message": "JWT token validation failed: Failed to decode token: Token verification failed with all keys. Errors: Key 8b5606ee-4d52-44eb-b3da-2d209354097c: Signature verification failed.", "module": "auth", "function": "authenticate_token", "line": 686}
{"timestamp": "2025-06-11T15:47:48.231108", "level": "WARNING", "logger": "backend.middleware.subscription_limits", "message": "Auth error in subscription middleware: name 'ValidationError' is not defined", "module": "subscription_limits", "function": "dispatch", "line": 234}
{"timestamp": "2025-06-11T15:47:48.231771", "level": "WARNING", "logger": "backend.core.security.jwt_keys", "message": "Unknown JWT kid requested: 69b5ada0-20e9-42b1-85e8-1ec80762bb7c", "module": "jwt_keys", "function": "get_verification_key", "line": 105}
{"timestamp": "2025-06-11T15:47:48.231828", "level": "WARNING", "logger": "backend.core.security.jwt_keys", "message": "Verification failed with key ID 69b5ada0-20e9-42b1-85e8-1ec80762bb7c: Unknown kid: 69b5ada0-20e9-42b1-85e8-1ec80762bb7c", "module": "jwt_keys", "function": "decode_token", "line": 203}
{"timestamp": "2025-06-11T15:47:48.232002", "level": "WARNING", "logger": "backend.core.security.jwt_keys", "message": "Verification failed with current key: Signature verification failed.", "module": "jwt_keys", "function": "decode_token", "line": 216}
{"timestamp": "2025-06-11T15:47:48.232151", "level": "ERROR", "logger": "backend.core.security.jwt_keys", "message": "Token verification failed with all keys. Errors: Key 8b5606ee-4d52-44eb-b3da-2d209354097c: Signature verification failed.", "module": "jwt_keys", "function": "decode_token", "line": 239}
{"timestamp": "2025-06-11T15:47:48.232214", "level": "ERROR", "logger": "backend.core.security.jwt_keys", "message": "Token decode error: Token verification failed with all keys. Errors: Key 8b5606ee-4d52-44eb-b3da-2d209354097c: Signature verification failed.", "module": "jwt_keys", "function": "decode_token", "line": 244}
{"timestamp": "2025-06-11T15:47:48.232279", "level": "ERROR", "logger": "backend.services.auth", "message": "JWT token validation failed: Failed to decode token: Token verification failed with all keys. Errors: Key 8b5606ee-4d52-44eb-b3da-2d209354097c: Signature verification failed.", "module": "auth", "function": "authenticate_token", "line": 686}
{"timestamp": "2025-06-11T15:48:22.986628", "level": "INFO", "logger": "backend.services.qr_preview_cache", "message": "QR preview cache cleanup task stopped", "module": "qr_preview_cache", "function": "stop", "line": 257}
{"timestamp": "2025-06-11T15:48:23.151914", "level": "INFO", "logger": "backend.main", "message": "QR preview cache stopped", "module": "main", "function": "shutdown_qr_preview_cache", "line": 352}
{"timestamp": "2025-06-11T15:48:30.863358", "level": "INFO", "logger": "backend.main", "message": "=== QRVibe API Startup: Logging to file is enabled ===", "module": "main", "function": "<module>", "line": 37}
{"timestamp": "2025-06-11T15:48:30.863501", "level": "INFO", "logger": "backend.main", "message": "Applying critical patches before initialization", "module": "main", "function": "<module>", "line": 43}
{"timestamp": "2025-06-11T15:48:30.863584", "level": "INFO", "logger": "backend.patches", "message": "Applying patches (none required)", "module": "__init__", "function": "apply_patches", "line": 11}
{"timestamp": "2025-06-11T15:48:30.863871", "level": "INFO", "logger": "backend.core.middleware.cors", "message": "Setting up CORS middleware with origins: ['https://127.0.0.1:3000', 'http://localhost:3000', 'https://localhost:3000', 'http://127.0.0.1:3000']", "module": "cors", "function": "setup_cors", "line": 86}
{"timestamp": "2025-06-11T15:48:30.863954", "level": "INFO", "logger": "backend.core.middleware.cors", "message": "CORS middleware configured - preflight requests should be handled automatically", "module": "cors", "function": "setup_cors", "line": 98}
{"timestamp": "2025-06-11T15:48:30.865041", "level": "INFO", "logger": "backend.middleware.security_middleware", "message": "Added Enhanced Security Middleware with tiered rate limiting", "module": "security_middleware", "function": "add_security_middleware", "line": 176}
{"timestamp": "2025-06-11T15:48:30.865157", "level": "INFO", "logger": "backend.main", "message": "Mounting storage directory: /Users/<USER>/aihub/qrvibe/storage", "module": "main", "function": "<module>", "line": 107}
{"timestamp": "2025-06-11T15:48:30.865296", "level": "INFO", "logger": "backend.main", "message": "Storage directory absolute path: /Users/<USER>/aihub/qrvibe/storage", "module": "main", "function": "<module>", "line": 119}
{"timestamp": "2025-06-11T15:48:31.369288", "level": "INFO", "logger": "root", "message": "Storage initialized: environment=test, storage_dir=/Users/<USER>/aihub/qrvibe/storage, s3_enabled=True", "module": "storage", "function": "__init__", "line": 41}
{"timestamp": "2025-06-11T15:48:31.372139", "level": "INFO", "logger": "root", "message": "S3 storage enabled with bucket: qrverse and CDN: https://dev-cdn.qrvibe.com", "module": "storage", "function": "__init__", "line": 59}
{"timestamp": "2025-06-11T15:48:31.435049", "level": "INFO", "logger": "root", "message": "Storage initialized: environment=test, storage_dir=/Users/<USER>/aihub/qrvibe/storage, s3_enabled=True", "module": "storage", "function": "__init__", "line": 41}
{"timestamp": "2025-06-11T15:48:31.438250", "level": "INFO", "logger": "root", "message": "S3 storage enabled with bucket: qrverse and CDN: https://dev-cdn.qrvibe.com", "module": "storage", "function": "__init__", "line": 59}
{"timestamp": "2025-06-11T15:48:31.460924", "level": "INFO", "logger": "backend.middleware.security_middleware", "message": "Enhanced security middleware initialized with tiered rate limiting", "module": "security_middleware", "function": "__init__", "line": 87}
{"timestamp": "2025-06-11T15:48:31.461034", "level": "INFO", "logger": "backend.core.middleware.csrf", "message": "CSRF Protection Middleware initialized with exclude paths: ['/auth/jwt/login', '/auth/logout', '/auth/register', '/auth/2fa', '/auth/me', '/auth/verify-email', '/auth/reset-password', '/admin/login', '/admin/logout']", "module": "csrf", "function": "__init__", "line": 44}
{"timestamp": "2025-06-11T15:48:31.469410", "level": "INFO", "logger": "backend.services.token_blacklist", "message": "Token blacklist service initialized with Redis", "module": "token_blacklist", "function": "initialize", "line": 59}
{"timestamp": "2025-06-11T15:48:31.469582", "level": "INFO", "logger": "backend.main", "message": "Token blacklist service initialized", "module": "main", "function": "initialize_token_blacklist", "line": 98}
{"timestamp": "2025-06-11T15:48:31.471685", "level": "INFO", "logger": "backend.services.qr_preview_cache", "message": "QR preview cache initialized at /Users/<USER>/aihub/qrvibe/storage/cache/qr_preview_cache.db", "module": "qr_preview_cache", "function": "_init_db", "line": 56}
{"timestamp": "2025-06-11T15:48:31.471775", "level": "INFO", "logger": "backend.services.qr_preview_cache", "message": "Started QR preview cache cleanup task", "module": "qr_preview_cache", "function": "start_cleanup_task", "line": 62}
{"timestamp": "2025-06-11T15:48:31.471836", "level": "INFO", "logger": "backend.main", "message": "QR preview cache initialized and cleanup task started", "module": "main", "function": "init_qr_preview_cache", "line": 345}
{"timestamp": "2025-06-11T15:49:10.268883", "level": "INFO", "logger": "backend.services.enhanced_rate_limit", "message": "Auth event for anonymous: login_attempt (failure)", "module": "enhanced_rate_limit", "function": "track_auth_event", "line": 182}
{"timestamp": "2025-06-11T15:49:10.681566", "level": "INFO", "logger": "backend.services.auth", "message": "Authentication successful for user: <EMAIL>", "module": "auth", "function": "authenticate_user", "line": 266}
{"timestamp": "2025-06-11T15:49:10.682709", "level": "INFO", "logger": "backend.routes.auth", "message": "User logged in successfully: <EMAIL>", "module": "auth", "function": "login", "line": 218}
{"timestamp": "2025-06-11T15:49:10.683460", "level": "INFO", "logger": "backend.services.enhanced_rate_limit", "message": "Auth event for user e4ae2d8f-b32f-52d7-99cd-8a33b19652c7: login_success (success)", "module": "enhanced_rate_limit", "function": "track_auth_event", "line": 182}
{"timestamp": "2025-06-11T15:49:10.749764", "level": "INFO", "logger": "backend.services.adaptive_session", "message": "Adaptive session for user e4ae2d8f-b32f-52d7-99cd-8a33b19652c7: risk=low, duration=86400s", "module": "adaptive_session", "function": "get_session_expiry", "line": 161}
{"timestamp": "2025-06-11T15:49:10.749862", "level": "INFO", "logger": "backend.routes.auth", "message": "Adaptive session <NAME_EMAIL>: duration=86400s", "module": "auth", "function": "login", "line": 279}
{"timestamp": "2025-06-11T15:49:10.749922", "level": "INFO", "logger": "backend.routes.auth", "message": "Bearer token authentication issued <NAME_EMAIL>", "module": "auth", "function": "login", "line": 284}
{"timestamp": "2025-06-11T15:49:13.748138", "level": "INFO", "logger": "root", "message": "Getting feature matrix for user e4ae2d8f-b32f-52d7-99cd-8a33b19652c7 (email: <EMAIL>)", "module": "subscription", "function": "get_user_feature_matrix", "line": 142}
{"timestamp": "2025-06-11T15:49:13.862914", "level": "INFO", "logger": "root", "message": "Found subscription for user e4ae2d8f-b32f-52d7-99cd-8a33b19652c7: plan=enterprise, status=active", "module": "subscription", "function": "get_user_feature_matrix", "line": 152}
{"timestamp": "2025-06-11T15:49:13.910939", "level": "INFO", "logger": "root", "message": "Found 0 team addons for user e4ae2d8f-b32f-52d7-99cd-8a33b19652c7", "module": "subscription", "function": "get_user_feature_matrix", "line": 181}
{"timestamp": "2025-06-11T15:49:13.911161", "level": "INFO", "logger": "root", "message": "Retrieved base features for plan enterprise", "module": "subscription", "function": "get_user_feature_matrix", "line": 191}
{"timestamp": "2025-06-11T15:49:13.954076", "level": "INFO", "logger": "root", "message": "Successfully generated feature matrix for user e4ae2d8f-b32f-52d7-99cd-8a33b19652c7", "module": "subscription", "function": "get_user_feature_matrix", "line": 284}
{"timestamp": "2025-06-11T15:58:28.470936", "level": "INFO", "logger": "root", "message": "Getting feature matrix for user e4ae2d8f-b32f-52d7-99cd-8a33b19652c7 (email: <EMAIL>)", "module": "subscription", "function": "get_user_feature_matrix", "line": 142}
{"timestamp": "2025-06-11T15:58:28.488604", "level": "INFO", "logger": "root", "message": "Found subscription for user e4ae2d8f-b32f-52d7-99cd-8a33b19652c7: plan=enterprise, status=active", "module": "subscription", "function": "get_user_feature_matrix", "line": 152}
{"timestamp": "2025-06-11T15:58:28.508826", "level": "INFO", "logger": "root", "message": "Found 0 team addons for user e4ae2d8f-b32f-52d7-99cd-8a33b19652c7", "module": "subscription", "function": "get_user_feature_matrix", "line": 181}
{"timestamp": "2025-06-11T15:58:28.509397", "level": "INFO", "logger": "root", "message": "Retrieved base features for plan enterprise", "module": "subscription", "function": "get_user_feature_matrix", "line": 191}
{"timestamp": "2025-06-11T15:58:28.534161", "level": "INFO", "logger": "root", "message": "Successfully generated feature matrix for user e4ae2d8f-b32f-52d7-99cd-8a33b19652c7", "module": "subscription", "function": "get_user_feature_matrix", "line": 284}
{"timestamp": "2025-06-11T16:05:06.544521", "level": "INFO", "logger": "root", "message": "Getting feature matrix for user e4ae2d8f-b32f-52d7-99cd-8a33b19652c7 (email: <EMAIL>)", "module": "subscription", "function": "get_user_feature_matrix", "line": 142}
{"timestamp": "2025-06-11T16:05:06.557208", "level": "INFO", "logger": "root", "message": "Found subscription for user e4ae2d8f-b32f-52d7-99cd-8a33b19652c7: plan=enterprise, status=active", "module": "subscription", "function": "get_user_feature_matrix", "line": 152}
{"timestamp": "2025-06-11T16:05:06.636271", "level": "INFO", "logger": "root", "message": "Found 0 team addons for user e4ae2d8f-b32f-52d7-99cd-8a33b19652c7", "module": "subscription", "function": "get_user_feature_matrix", "line": 181}
{"timestamp": "2025-06-11T16:05:06.636438", "level": "INFO", "logger": "root", "message": "Retrieved base features for plan enterprise", "module": "subscription", "function": "get_user_feature_matrix", "line": 191}
{"timestamp": "2025-06-11T16:05:06.663449", "level": "INFO", "logger": "root", "message": "Successfully generated feature matrix for user e4ae2d8f-b32f-52d7-99cd-8a33b19652c7", "module": "subscription", "function": "get_user_feature_matrix", "line": 284}
{"timestamp": "2025-06-11T16:19:27.866534", "level": "INFO", "logger": "backend.services.qr_preview_cache", "message": "QR preview cache cleanup task stopped", "module": "qr_preview_cache", "function": "stop", "line": 257}
{"timestamp": "2025-06-11T16:19:27.867655", "level": "INFO", "logger": "backend.main", "message": "QR preview cache stopped", "module": "main", "function": "shutdown_qr_preview_cache", "line": 352}
{"timestamp": "2025-06-11T16:19:31.801041", "level": "INFO", "logger": "backend.main", "message": "=== QRVibe API Startup: Logging to file is enabled ===", "module": "main", "function": "<module>", "line": 37}
{"timestamp": "2025-06-11T16:19:31.801156", "level": "INFO", "logger": "backend.main", "message": "Applying critical patches before initialization", "module": "main", "function": "<module>", "line": 43}
{"timestamp": "2025-06-11T16:19:31.801278", "level": "INFO", "logger": "backend.patches", "message": "Applying patches (none required)", "module": "__init__", "function": "apply_patches", "line": 11}
{"timestamp": "2025-06-11T16:19:31.801567", "level": "INFO", "logger": "backend.core.middleware.cors", "message": "Setting up CORS middleware with origins: ['http://localhost:3000', 'http://127.0.0.1:3000', 'https://127.0.0.1:3000', 'https://localhost:3000']", "module": "cors", "function": "setup_cors", "line": 86}
{"timestamp": "2025-06-11T16:19:31.801663", "level": "INFO", "logger": "backend.core.middleware.cors", "message": "CORS middleware configured - preflight requests should be handled automatically", "module": "cors", "function": "setup_cors", "line": 98}
{"timestamp": "2025-06-11T16:19:31.802902", "level": "INFO", "logger": "backend.middleware.security_middleware", "message": "Added Enhanced Security Middleware with tiered rate limiting", "module": "security_middleware", "function": "add_security_middleware", "line": 176}
{"timestamp": "2025-06-11T16:19:31.803039", "level": "INFO", "logger": "backend.main", "message": "Mounting storage directory: /Users/<USER>/aihub/qrvibe/storage", "module": "main", "function": "<module>", "line": 107}
{"timestamp": "2025-06-11T16:19:31.803212", "level": "INFO", "logger": "backend.main", "message": "Storage directory absolute path: /Users/<USER>/aihub/qrvibe/storage", "module": "main", "function": "<module>", "line": 119}
{"timestamp": "2025-06-11T16:19:32.212967", "level": "INFO", "logger": "root", "message": "Storage initialized: environment=test, storage_dir=/Users/<USER>/aihub/qrvibe/storage, s3_enabled=True", "module": "storage", "function": "__init__", "line": 41}
{"timestamp": "2025-06-11T16:19:32.216054", "level": "INFO", "logger": "root", "message": "S3 storage enabled with bucket: qrverse and CDN: https://dev-cdn.qrvibe.com", "module": "storage", "function": "__init__", "line": 59}
{"timestamp": "2025-06-11T16:19:32.263866", "level": "INFO", "logger": "root", "message": "Storage initialized: environment=test, storage_dir=/Users/<USER>/aihub/qrvibe/storage, s3_enabled=True", "module": "storage", "function": "__init__", "line": 41}
{"timestamp": "2025-06-11T16:19:32.267126", "level": "INFO", "logger": "root", "message": "S3 storage enabled with bucket: qrverse and CDN: https://dev-cdn.qrvibe.com", "module": "storage", "function": "__init__", "line": 59}
{"timestamp": "2025-06-11T16:19:32.377604", "level": "INFO", "logger": "backend.middleware.security_middleware", "message": "Enhanced security middleware initialized with tiered rate limiting", "module": "security_middleware", "function": "__init__", "line": 87}
{"timestamp": "2025-06-11T16:19:32.377732", "level": "INFO", "logger": "backend.core.middleware.csrf", "message": "CSRF Protection Middleware initialized with exclude paths: ['/auth/jwt/login', '/auth/logout', '/auth/register', '/auth/2fa', '/auth/me', '/auth/verify-email', '/auth/reset-password', '/admin/login', '/admin/logout']", "module": "csrf", "function": "__init__", "line": 44}
{"timestamp": "2025-06-11T16:19:32.395425", "level": "INFO", "logger": "backend.services.token_blacklist", "message": "Token blacklist service initialized with Redis", "module": "token_blacklist", "function": "initialize", "line": 59}
{"timestamp": "2025-06-11T16:19:32.395528", "level": "INFO", "logger": "backend.main", "message": "Token blacklist service initialized", "module": "main", "function": "initialize_token_blacklist", "line": 98}
{"timestamp": "2025-06-11T16:19:32.396264", "level": "INFO", "logger": "backend.services.qr_preview_cache", "message": "QR preview cache initialized at /Users/<USER>/aihub/qrvibe/storage/cache/qr_preview_cache.db", "module": "qr_preview_cache", "function": "_init_db", "line": 56}
{"timestamp": "2025-06-11T16:19:32.396347", "level": "INFO", "logger": "backend.services.qr_preview_cache", "message": "Started QR preview cache cleanup task", "module": "qr_preview_cache", "function": "start_cleanup_task", "line": 62}
{"timestamp": "2025-06-11T16:19:32.396418", "level": "INFO", "logger": "backend.main", "message": "QR preview cache initialized and cleanup task started", "module": "main", "function": "init_qr_preview_cache", "line": 345}
{"timestamp": "2025-06-11T16:20:22.945572", "level": "INFO", "logger": "backend.services.qr_preview_cache", "message": "QR preview cache cleanup task stopped", "module": "qr_preview_cache", "function": "stop", "line": 257}
{"timestamp": "2025-06-11T16:20:22.946108", "level": "INFO", "logger": "backend.main", "message": "QR preview cache stopped", "module": "main", "function": "shutdown_qr_preview_cache", "line": 352}
{"timestamp": "2025-06-11T16:20:26.379512", "level": "INFO", "logger": "backend.main", "message": "=== QRVibe API Startup: Logging to file is enabled ===", "module": "main", "function": "<module>", "line": 37}
{"timestamp": "2025-06-11T16:20:26.379603", "level": "INFO", "logger": "backend.main", "message": "Applying critical patches before initialization", "module": "main", "function": "<module>", "line": 43}
{"timestamp": "2025-06-11T16:20:26.379662", "level": "INFO", "logger": "backend.patches", "message": "Applying patches (none required)", "module": "__init__", "function": "apply_patches", "line": 11}
{"timestamp": "2025-06-11T16:20:26.379886", "level": "INFO", "logger": "backend.core.middleware.cors", "message": "Setting up CORS middleware with origins: ['http://localhost:3000', 'https://localhost:3000', 'http://127.0.0.1:3000', 'https://127.0.0.1:3000']", "module": "cors", "function": "setup_cors", "line": 86}
{"timestamp": "2025-06-11T16:20:26.379942", "level": "INFO", "logger": "backend.core.middleware.cors", "message": "CORS middleware configured - preflight requests should be handled automatically", "module": "cors", "function": "setup_cors", "line": 98}
{"timestamp": "2025-06-11T16:20:26.386195", "level": "INFO", "logger": "backend.middleware.security_middleware", "message": "Added Enhanced Security Middleware with tiered rate limiting", "module": "security_middleware", "function": "add_security_middleware", "line": 176}
{"timestamp": "2025-06-11T16:20:26.386382", "level": "INFO", "logger": "backend.main", "message": "Mounting storage directory: /Users/<USER>/aihub/qrvibe/storage", "module": "main", "function": "<module>", "line": 107}
{"timestamp": "2025-06-11T16:20:26.386576", "level": "INFO", "logger": "backend.main", "message": "Storage directory absolute path: /Users/<USER>/aihub/qrvibe/storage", "module": "main", "function": "<module>", "line": 119}
{"timestamp": "2025-06-11T16:20:26.851615", "level": "INFO", "logger": "root", "message": "Storage initialized: environment=test, storage_dir=/Users/<USER>/aihub/qrvibe/storage, s3_enabled=True", "module": "storage", "function": "__init__", "line": 41}
{"timestamp": "2025-06-11T16:20:26.854683", "level": "INFO", "logger": "root", "message": "S3 storage enabled with bucket: qrverse and CDN: https://dev-cdn.qrvibe.com", "module": "storage", "function": "__init__", "line": 59}
{"timestamp": "2025-06-11T16:20:26.916387", "level": "INFO", "logger": "root", "message": "Storage initialized: environment=test, storage_dir=/Users/<USER>/aihub/qrvibe/storage, s3_enabled=True", "module": "storage", "function": "__init__", "line": 41}
{"timestamp": "2025-06-11T16:20:26.920102", "level": "INFO", "logger": "root", "message": "S3 storage enabled with bucket: qrverse and CDN: https://dev-cdn.qrvibe.com", "module": "storage", "function": "__init__", "line": 59}
{"timestamp": "2025-06-11T16:20:27.039142", "level": "INFO", "logger": "backend.middleware.security_middleware", "message": "Enhanced security middleware initialized with tiered rate limiting", "module": "security_middleware", "function": "__init__", "line": 87}
{"timestamp": "2025-06-11T16:20:27.039247", "level": "INFO", "logger": "backend.core.middleware.csrf", "message": "CSRF Protection Middleware initialized with exclude paths: ['/auth/jwt/login', '/auth/logout', '/auth/register', '/auth/2fa', '/auth/me', '/auth/verify-email', '/auth/reset-password', '/admin/login', '/admin/logout']", "module": "csrf", "function": "__init__", "line": 44}
{"timestamp": "2025-06-11T16:20:27.045027", "level": "INFO", "logger": "backend.services.token_blacklist", "message": "Token blacklist service initialized with Redis", "module": "token_blacklist", "function": "initialize", "line": 59}
{"timestamp": "2025-06-11T16:20:27.045184", "level": "INFO", "logger": "backend.main", "message": "Token blacklist service initialized", "module": "main", "function": "initialize_token_blacklist", "line": 98}
{"timestamp": "2025-06-11T16:20:27.046194", "level": "INFO", "logger": "backend.services.qr_preview_cache", "message": "QR preview cache initialized at /Users/<USER>/aihub/qrvibe/storage/cache/qr_preview_cache.db", "module": "qr_preview_cache", "function": "_init_db", "line": 56}
{"timestamp": "2025-06-11T16:20:27.046298", "level": "INFO", "logger": "backend.services.qr_preview_cache", "message": "Started QR preview cache cleanup task", "module": "qr_preview_cache", "function": "start_cleanup_task", "line": 62}
{"timestamp": "2025-06-11T16:20:27.046364", "level": "INFO", "logger": "backend.main", "message": "QR preview cache initialized and cleanup task started", "module": "main", "function": "init_qr_preview_cache", "line": 345}
{"timestamp": "2025-06-11T16:20:43.624839", "level": "INFO", "logger": "backend.services.qr_preview_cache", "message": "QR preview cache cleanup task stopped", "module": "qr_preview_cache", "function": "stop", "line": 257}
{"timestamp": "2025-06-11T16:20:43.625484", "level": "INFO", "logger": "backend.main", "message": "QR preview cache stopped", "module": "main", "function": "shutdown_qr_preview_cache", "line": 352}
{"timestamp": "2025-06-11T16:20:46.946372", "level": "INFO", "logger": "backend.main", "message": "=== QRVibe API Startup: Logging to file is enabled ===", "module": "main", "function": "<module>", "line": 37}
{"timestamp": "2025-06-11T16:20:46.946467", "level": "INFO", "logger": "backend.main", "message": "Applying critical patches before initialization", "module": "main", "function": "<module>", "line": 43}
{"timestamp": "2025-06-11T16:20:46.946527", "level": "INFO", "logger": "backend.patches", "message": "Applying patches (none required)", "module": "__init__", "function": "apply_patches", "line": 11}
{"timestamp": "2025-06-11T16:20:46.946760", "level": "INFO", "logger": "backend.core.middleware.cors", "message": "Setting up CORS middleware with origins: ['http://localhost:3000', 'https://127.0.0.1:3000', 'https://localhost:3000', 'http://127.0.0.1:3000']", "module": "cors", "function": "setup_cors", "line": 86}
{"timestamp": "2025-06-11T16:20:46.946968", "level": "INFO", "logger": "backend.core.middleware.cors", "message": "CORS middleware configured - preflight requests should be handled automatically", "module": "cors", "function": "setup_cors", "line": 98}
{"timestamp": "2025-06-11T16:20:46.947994", "level": "INFO", "logger": "backend.middleware.security_middleware", "message": "Added Enhanced Security Middleware with tiered rate limiting", "module": "security_middleware", "function": "add_security_middleware", "line": 176}
{"timestamp": "2025-06-11T16:20:46.948083", "level": "INFO", "logger": "backend.main", "message": "Mounting storage directory: /Users/<USER>/aihub/qrvibe/storage", "module": "main", "function": "<module>", "line": 107}
{"timestamp": "2025-06-11T16:20:46.948201", "level": "INFO", "logger": "backend.main", "message": "Storage directory absolute path: /Users/<USER>/aihub/qrvibe/storage", "module": "main", "function": "<module>", "line": 119}
{"timestamp": "2025-06-11T16:20:47.409761", "level": "INFO", "logger": "root", "message": "Storage initialized: environment=test, storage_dir=/Users/<USER>/aihub/qrvibe/storage, s3_enabled=True", "module": "storage", "function": "__init__", "line": 41}
{"timestamp": "2025-06-11T16:20:47.412747", "level": "INFO", "logger": "root", "message": "S3 storage enabled with bucket: qrverse and CDN: https://dev-cdn.qrvibe.com", "module": "storage", "function": "__init__", "line": 59}
{"timestamp": "2025-06-11T16:20:47.462772", "level": "INFO", "logger": "root", "message": "Storage initialized: environment=test, storage_dir=/Users/<USER>/aihub/qrvibe/storage, s3_enabled=True", "module": "storage", "function": "__init__", "line": 41}
{"timestamp": "2025-06-11T16:20:47.466108", "level": "INFO", "logger": "root", "message": "S3 storage enabled with bucket: qrverse and CDN: https://dev-cdn.qrvibe.com", "module": "storage", "function": "__init__", "line": 59}
{"timestamp": "2025-06-11T16:20:47.724145", "level": "INFO", "logger": "backend.middleware.security_middleware", "message": "Enhanced security middleware initialized with tiered rate limiting", "module": "security_middleware", "function": "__init__", "line": 87}
{"timestamp": "2025-06-11T16:20:47.724252", "level": "INFO", "logger": "backend.core.middleware.csrf", "message": "CSRF Protection Middleware initialized with exclude paths: ['/auth/jwt/login', '/auth/logout', '/auth/register', '/auth/2fa', '/auth/me', '/auth/verify-email', '/auth/reset-password', '/admin/login', '/admin/logout']", "module": "csrf", "function": "__init__", "line": 44}
{"timestamp": "2025-06-11T16:20:47.728768", "level": "INFO", "logger": "backend.services.token_blacklist", "message": "Token blacklist service initialized with Redis", "module": "token_blacklist", "function": "initialize", "line": 59}
{"timestamp": "2025-06-11T16:20:47.728847", "level": "INFO", "logger": "backend.main", "message": "Token blacklist service initialized", "module": "main", "function": "initialize_token_blacklist", "line": 98}
{"timestamp": "2025-06-11T16:20:47.729620", "level": "INFO", "logger": "backend.services.qr_preview_cache", "message": "QR preview cache initialized at /Users/<USER>/aihub/qrvibe/storage/cache/qr_preview_cache.db", "module": "qr_preview_cache", "function": "_init_db", "line": 56}
{"timestamp": "2025-06-11T16:20:47.729727", "level": "INFO", "logger": "backend.services.qr_preview_cache", "message": "Started QR preview cache cleanup task", "module": "qr_preview_cache", "function": "start_cleanup_task", "line": 62}
{"timestamp": "2025-06-11T16:20:47.729790", "level": "INFO", "logger": "backend.main", "message": "QR preview cache initialized and cleanup task started", "module": "main", "function": "init_qr_preview_cache", "line": 345}
{"timestamp": "2025-06-11T16:20:56.607188", "level": "WARNING", "logger": "backend.core.middleware.csrf", "message": "CSRF token missing for POST /qr/preview/svg - Header present: False, Cookie present: False", "module": "csrf", "function": "dispatch", "line": 66}
{"timestamp": "2025-06-11T16:20:56.607336", "level": "WARNING", "logger": "backend.core.middleware.csrf", "message": "CSRF validation failed for POST /qr/preview/svg - IP: 127.0.0.1", "module": "csrf", "function": "dispatch", "line": 75}
{"timestamp": "2025-06-11T16:20:56.607687", "level": "WARNING", "logger": "backend.core.middleware.csrf", "message": "CSRF token missing for POST /qr/preview/svg - Header present: False, Cookie present: False", "module": "csrf", "function": "dispatch", "line": 66}
{"timestamp": "2025-06-11T16:20:56.607760", "level": "WARNING", "logger": "backend.core.middleware.csrf", "message": "CSRF validation failed for POST /qr/preview/svg - IP: 127.0.0.1", "module": "csrf", "function": "dispatch", "line": 75}
{"timestamp": "2025-06-11T16:20:58.895527", "level": "WARNING", "logger": "backend.core.middleware.csrf", "message": "CSRF token missing for POST /qr/preview/svg - Header present: False, Cookie present: False", "module": "csrf", "function": "dispatch", "line": 66}
{"timestamp": "2025-06-11T16:20:58.895629", "level": "WARNING", "logger": "backend.core.middleware.csrf", "message": "CSRF validation failed for POST /qr/preview/svg - IP: 127.0.0.1", "module": "csrf", "function": "dispatch", "line": 75}
{"timestamp": "2025-06-11T16:20:58.896664", "level": "WARNING", "logger": "backend.core.middleware.csrf", "message": "CSRF token missing for POST /qr/preview/svg - Header present: False, Cookie present: False", "module": "csrf", "function": "dispatch", "line": 66}
{"timestamp": "2025-06-11T16:20:58.896775", "level": "WARNING", "logger": "backend.core.middleware.csrf", "message": "CSRF validation failed for POST /qr/preview/svg - IP: 127.0.0.1", "module": "csrf", "function": "dispatch", "line": 75}
{"timestamp": "2025-06-11T16:21:01.070900", "level": "WARNING", "logger": "backend.core.middleware.csrf", "message": "CSRF token missing for POST /qr/preview/svg - Header present: False, Cookie present: False", "module": "csrf", "function": "dispatch", "line": 66}
{"timestamp": "2025-06-11T16:21:01.070993", "level": "WARNING", "logger": "backend.core.middleware.csrf", "message": "CSRF validation failed for POST /qr/preview/svg - IP: 127.0.0.1", "module": "csrf", "function": "dispatch", "line": 75}
{"timestamp": "2025-06-11T16:21:01.074835", "level": "WARNING", "logger": "backend.core.middleware.csrf", "message": "CSRF token missing for POST /qr/preview/svg - Header present: False, Cookie present: False", "module": "csrf", "function": "dispatch", "line": 66}
{"timestamp": "2025-06-11T16:21:01.074924", "level": "WARNING", "logger": "backend.core.middleware.csrf", "message": "CSRF validation failed for POST /qr/preview/svg - IP: 127.0.0.1", "module": "csrf", "function": "dispatch", "line": 75}
{"timestamp": "2025-06-11T16:21:01.893667", "level": "WARNING", "logger": "backend.core.middleware.csrf", "message": "CSRF token missing for POST /qr/preview/svg - Header present: False, Cookie present: False", "module": "csrf", "function": "dispatch", "line": 66}
{"timestamp": "2025-06-11T16:21:01.893793", "level": "WARNING", "logger": "backend.core.middleware.csrf", "message": "CSRF validation failed for POST /qr/preview/svg - IP: 127.0.0.1", "module": "csrf", "function": "dispatch", "line": 75}
{"timestamp": "2025-06-11T16:21:01.905305", "level": "WARNING", "logger": "backend.core.middleware.csrf", "message": "CSRF token missing for POST /qr/preview/svg - Header present: False, Cookie present: False", "module": "csrf", "function": "dispatch", "line": 66}
{"timestamp": "2025-06-11T16:21:01.905415", "level": "WARNING", "logger": "backend.core.middleware.csrf", "message": "CSRF validation failed for POST /qr/preview/svg - IP: 127.0.0.1", "module": "csrf", "function": "dispatch", "line": 75}
{"timestamp": "2025-06-11T16:21:20.701304", "level": "WARNING", "logger": "backend.core.middleware.csrf", "message": "CSRF token missing for POST /qr/preview/svg - Header present: False, Cookie present: False", "module": "csrf", "function": "dispatch", "line": 66}
{"timestamp": "2025-06-11T16:21:20.701633", "level": "WARNING", "logger": "backend.core.middleware.csrf", "message": "CSRF validation failed for POST /qr/preview/svg - IP: 127.0.0.1", "module": "csrf", "function": "dispatch", "line": 75}
{"timestamp": "2025-06-11T16:21:37.170317", "level": "WARNING", "logger": "backend.core.middleware.csrf", "message": "CSRF token missing for POST /qr/preview/svg - Header present: False, Cookie present: False", "module": "csrf", "function": "dispatch", "line": 66}
{"timestamp": "2025-06-11T16:21:37.171867", "level": "WARNING", "logger": "backend.core.middleware.csrf", "message": "CSRF validation failed for POST /qr/preview/svg - IP: 127.0.0.1", "module": "csrf", "function": "dispatch", "line": 75}
{"timestamp": "2025-06-11T16:21:51.278758", "level": "WARNING", "logger": "backend.core.middleware.csrf", "message": "CSRF token missing for POST /qr/preview/svg - Header present: False, Cookie present: False", "module": "csrf", "function": "dispatch", "line": 66}
{"timestamp": "2025-06-11T16:21:51.278994", "level": "WARNING", "logger": "backend.core.middleware.csrf", "message": "CSRF validation failed for POST /qr/preview/svg - IP: 127.0.0.1", "module": "csrf", "function": "dispatch", "line": 75}
{"timestamp": "2025-06-11T16:22:08.259891", "level": "WARNING", "logger": "backend.core.middleware.csrf", "message": "CSRF token missing for POST /qr/preview/svg - Header present: False, Cookie present: False", "module": "csrf", "function": "dispatch", "line": 66}
{"timestamp": "2025-06-11T16:22:08.260467", "level": "WARNING", "logger": "backend.core.middleware.csrf", "message": "CSRF validation failed for POST /qr/preview/svg - IP: 127.0.0.1", "module": "csrf", "function": "dispatch", "line": 75}
{"timestamp": "2025-06-11T16:22:17.785586", "level": "WARNING", "logger": "backend.core.middleware.csrf", "message": "CSRF token missing for POST /qr/preview/svg - Header present: False, Cookie present: False", "module": "csrf", "function": "dispatch", "line": 66}
{"timestamp": "2025-06-11T16:22:17.785929", "level": "WARNING", "logger": "backend.core.middleware.csrf", "message": "CSRF validation failed for POST /qr/preview/svg - IP: 127.0.0.1", "module": "csrf", "function": "dispatch", "line": 75}
{"timestamp": "2025-06-11T16:22:29.062187", "level": "WARNING", "logger": "backend.core.middleware.csrf", "message": "CSRF token missing for POST /qr/preview/svg - Header present: False, Cookie present: False", "module": "csrf", "function": "dispatch", "line": 66}
{"timestamp": "2025-06-11T16:22:29.062737", "level": "WARNING", "logger": "backend.core.middleware.csrf", "message": "CSRF validation failed for POST /qr/preview/svg - IP: 127.0.0.1", "module": "csrf", "function": "dispatch", "line": 75}
{"timestamp": "2025-06-11T16:23:07.600177", "level": "INFO", "logger": "backend.services.qr_preview_cache", "message": "QR preview cache cleanup task stopped", "module": "qr_preview_cache", "function": "stop", "line": 257}
{"timestamp": "2025-06-11T16:23:07.602075", "level": "INFO", "logger": "backend.main", "message": "QR preview cache stopped", "module": "main", "function": "shutdown_qr_preview_cache", "line": 352}
{"timestamp": "2025-06-11T16:23:25.784799", "level": "INFO", "logger": "backend.main", "message": "=== QRVibe API Startup: Logging to file is enabled ===", "module": "main", "function": "<module>", "line": 37}
{"timestamp": "2025-06-11T16:23:25.785276", "level": "INFO", "logger": "backend.main", "message": "Applying critical patches before initialization", "module": "main", "function": "<module>", "line": 43}
{"timestamp": "2025-06-11T16:23:25.785353", "level": "INFO", "logger": "backend.patches", "message": "Applying patches (none required)", "module": "__init__", "function": "apply_patches", "line": 11}
{"timestamp": "2025-06-11T16:23:25.785588", "level": "INFO", "logger": "backend.core.middleware.cors", "message": "Setting up CORS middleware with origins: ['http://localhost:3000', 'https://localhost:3000', 'https://127.0.0.1:3000', 'http://127.0.0.1:3000']", "module": "cors", "function": "setup_cors", "line": 86}
{"timestamp": "2025-06-11T16:23:25.785649", "level": "INFO", "logger": "backend.core.middleware.cors", "message": "CORS middleware configured - preflight requests should be handled automatically", "module": "cors", "function": "setup_cors", "line": 98}
{"timestamp": "2025-06-11T16:23:25.786903", "level": "INFO", "logger": "backend.middleware.security_middleware", "message": "Added Enhanced Security Middleware with tiered rate limiting", "module": "security_middleware", "function": "add_security_middleware", "line": 176}
{"timestamp": "2025-06-11T16:23:25.787033", "level": "INFO", "logger": "backend.main", "message": "Mounting storage directory: /Users/<USER>/aihub/qrvibe/storage", "module": "main", "function": "<module>", "line": 107}
{"timestamp": "2025-06-11T16:23:25.787180", "level": "INFO", "logger": "backend.main", "message": "Storage directory absolute path: /Users/<USER>/aihub/qrvibe/storage", "module": "main", "function": "<module>", "line": 119}
{"timestamp": "2025-06-11T16:23:26.191380", "level": "INFO", "logger": "root", "message": "Storage initialized: environment=test, storage_dir=/Users/<USER>/aihub/qrvibe/storage, s3_enabled=True", "module": "storage", "function": "__init__", "line": 41}
{"timestamp": "2025-06-11T16:23:26.194230", "level": "INFO", "logger": "root", "message": "S3 storage enabled with bucket: qrverse and CDN: https://dev-cdn.qrvibe.com", "module": "storage", "function": "__init__", "line": 59}
{"timestamp": "2025-06-11T16:23:26.240490", "level": "INFO", "logger": "root", "message": "Storage initialized: environment=test, storage_dir=/Users/<USER>/aihub/qrvibe/storage, s3_enabled=True", "module": "storage", "function": "__init__", "line": 41}
{"timestamp": "2025-06-11T16:23:26.243824", "level": "INFO", "logger": "root", "message": "S3 storage enabled with bucket: qrverse and CDN: https://dev-cdn.qrvibe.com", "module": "storage", "function": "__init__", "line": 59}
{"timestamp": "2025-06-11T16:23:26.267282", "level": "INFO", "logger": "backend.middleware.security_middleware", "message": "Enhanced security middleware initialized with tiered rate limiting", "module": "security_middleware", "function": "__init__", "line": 87}
{"timestamp": "2025-06-11T16:23:26.267471", "level": "INFO", "logger": "backend.core.middleware.csrf", "message": "CSRF Protection Middleware initialized with exclude paths: ['/auth/jwt/login', '/auth/logout', '/auth/register', '/auth/2fa', '/auth/me', '/auth/verify-email', '/auth/reset-password', '/admin/login', '/admin/logout']", "module": "csrf", "function": "__init__", "line": 44}
{"timestamp": "2025-06-11T16:23:26.272631", "level": "INFO", "logger": "backend.services.token_blacklist", "message": "Token blacklist service initialized with Redis", "module": "token_blacklist", "function": "initialize", "line": 59}
{"timestamp": "2025-06-11T16:23:26.272723", "level": "INFO", "logger": "backend.main", "message": "Token blacklist service initialized", "module": "main", "function": "initialize_token_blacklist", "line": 98}
{"timestamp": "2025-06-11T16:23:26.273374", "level": "INFO", "logger": "backend.services.qr_preview_cache", "message": "QR preview cache initialized at /Users/<USER>/aihub/qrvibe/storage/cache/qr_preview_cache.db", "module": "qr_preview_cache", "function": "_init_db", "line": 56}
{"timestamp": "2025-06-11T16:23:26.273451", "level": "INFO", "logger": "backend.services.qr_preview_cache", "message": "Started QR preview cache cleanup task", "module": "qr_preview_cache", "function": "start_cleanup_task", "line": 62}
{"timestamp": "2025-06-11T16:23:26.273515", "level": "INFO", "logger": "backend.main", "message": "QR preview cache initialized and cleanup task started", "module": "main", "function": "init_qr_preview_cache", "line": 345}
{"timestamp": "2025-06-11T16:24:14.792527", "level": "INFO", "logger": "backend.services.enhanced_rate_limit", "message": "Auth event for anonymous: login_attempt (failure)", "module": "enhanced_rate_limit", "function": "track_auth_event", "line": 182}
{"timestamp": "2025-06-11T16:24:15.398002", "level": "INFO", "logger": "backend.services.auth", "message": "Authentication successful for user: <EMAIL>", "module": "auth", "function": "authenticate_user", "line": 266}
{"timestamp": "2025-06-11T16:24:15.399838", "level": "INFO", "logger": "backend.routes.auth", "message": "User logged in successfully: <EMAIL>", "module": "auth", "function": "login", "line": 218}
{"timestamp": "2025-06-11T16:24:15.402964", "level": "INFO", "logger": "backend.services.enhanced_rate_limit", "message": "Auth event for user e4ae2d8f-b32f-52d7-99cd-8a33b19652c7: login_success (success)", "module": "enhanced_rate_limit", "function": "track_auth_event", "line": 182}
{"timestamp": "2025-06-11T16:24:15.447120", "level": "INFO", "logger": "backend.services.adaptive_session", "message": "Adaptive session for user e4ae2d8f-b32f-52d7-99cd-8a33b19652c7: risk=low, duration=86400s", "module": "adaptive_session", "function": "get_session_expiry", "line": 161}
{"timestamp": "2025-06-11T16:24:15.447219", "level": "INFO", "logger": "backend.routes.auth", "message": "Adaptive session <NAME_EMAIL>: duration=86400s", "module": "auth", "function": "login", "line": 279}
{"timestamp": "2025-06-11T16:24:15.447276", "level": "INFO", "logger": "backend.routes.auth", "message": "Bearer token authentication issued <NAME_EMAIL>", "module": "auth", "function": "login", "line": 284}
{"timestamp": "2025-06-11T16:24:18.712755", "level": "INFO", "logger": "root", "message": "Getting feature matrix for user e4ae2d8f-b32f-52d7-99cd-8a33b19652c7 (email: <EMAIL>)", "module": "subscription", "function": "get_user_feature_matrix", "line": 142}
{"timestamp": "2025-06-11T16:24:18.746845", "level": "INFO", "logger": "root", "message": "Found subscription for user e4ae2d8f-b32f-52d7-99cd-8a33b19652c7: plan=enterprise, status=active", "module": "subscription", "function": "get_user_feature_matrix", "line": 152}
{"timestamp": "2025-06-11T16:24:18.777539", "level": "INFO", "logger": "root", "message": "Found 0 team addons for user e4ae2d8f-b32f-52d7-99cd-8a33b19652c7", "module": "subscription", "function": "get_user_feature_matrix", "line": 181}
{"timestamp": "2025-06-11T16:24:18.777694", "level": "INFO", "logger": "root", "message": "Retrieved base features for plan enterprise", "module": "subscription", "function": "get_user_feature_matrix", "line": 191}
{"timestamp": "2025-06-11T16:24:18.805519", "level": "INFO", "logger": "root", "message": "Successfully generated feature matrix for user e4ae2d8f-b32f-52d7-99cd-8a33b19652c7", "module": "subscription", "function": "get_user_feature_matrix", "line": 284}
{"timestamp": "2025-06-11T16:30:53.444935", "level": "INFO", "logger": "root", "message": "Getting feature matrix for user e4ae2d8f-b32f-52d7-99cd-8a33b19652c7 (email: <EMAIL>)", "module": "subscription", "function": "get_user_feature_matrix", "line": 142}
{"timestamp": "2025-06-11T16:30:53.476106", "level": "INFO", "logger": "root", "message": "Found subscription for user e4ae2d8f-b32f-52d7-99cd-8a33b19652c7: plan=enterprise, status=active", "module": "subscription", "function": "get_user_feature_matrix", "line": 152}
{"timestamp": "2025-06-11T16:30:53.549835", "level": "INFO", "logger": "root", "message": "Found 0 team addons for user e4ae2d8f-b32f-52d7-99cd-8a33b19652c7", "module": "subscription", "function": "get_user_feature_matrix", "line": 181}
{"timestamp": "2025-06-11T16:30:53.552232", "level": "INFO", "logger": "root", "message": "Retrieved base features for plan enterprise", "module": "subscription", "function": "get_user_feature_matrix", "line": 191}
{"timestamp": "2025-06-11T16:30:53.900904", "level": "INFO", "logger": "root", "message": "Successfully generated feature matrix for user e4ae2d8f-b32f-52d7-99cd-8a33b19652c7", "module": "subscription", "function": "get_user_feature_matrix", "line": 284}
{"timestamp": "2025-06-11T21:34:00.260449", "level": "INFO", "logger": "backend.services.enhanced_rate_limit", "message": "Auth event for anonymous: login_attempt (failure)", "module": "enhanced_rate_limit", "function": "track_auth_event", "line": 182}
{"timestamp": "2025-06-11T21:34:00.475129", "level": "INFO", "logger": "backend.services.auth", "message": "Authentication successful for user: <EMAIL>", "module": "auth", "function": "authenticate_user", "line": 266}
{"timestamp": "2025-06-11T21:34:00.480315", "level": "INFO", "logger": "backend.routes.auth", "message": "User logged in successfully: <EMAIL>", "module": "auth", "function": "login", "line": 218}
{"timestamp": "2025-06-11T21:34:00.482969", "level": "INFO", "logger": "backend.services.enhanced_rate_limit", "message": "Auth event for user e4ae2d8f-b32f-52d7-99cd-8a33b19652c7: login_success (success)", "module": "enhanced_rate_limit", "function": "track_auth_event", "line": 182}
{"timestamp": "2025-06-11T21:34:00.552361", "level": "INFO", "logger": "backend.services.adaptive_session", "message": "Adaptive session for user e4ae2d8f-b32f-52d7-99cd-8a33b19652c7: risk=low, duration=86400s", "module": "adaptive_session", "function": "get_session_expiry", "line": 161}
{"timestamp": "2025-06-11T21:34:00.552465", "level": "INFO", "logger": "backend.routes.auth", "message": "Adaptive session <NAME_EMAIL>: duration=86400s", "module": "auth", "function": "login", "line": 279}
{"timestamp": "2025-06-11T21:34:00.552527", "level": "INFO", "logger": "backend.routes.auth", "message": "Bearer token authentication issued <NAME_EMAIL>", "module": "auth", "function": "login", "line": 284}
{"timestamp": "2025-06-11T21:34:03.081016", "level": "INFO", "logger": "root", "message": "Getting feature matrix for user e4ae2d8f-b32f-52d7-99cd-8a33b19652c7 (email: <EMAIL>)", "module": "subscription", "function": "get_user_feature_matrix", "line": 142}
{"timestamp": "2025-06-11T21:34:03.125196", "level": "INFO", "logger": "root", "message": "Found subscription for user e4ae2d8f-b32f-52d7-99cd-8a33b19652c7: plan=enterprise, status=active", "module": "subscription", "function": "get_user_feature_matrix", "line": 152}
{"timestamp": "2025-06-11T21:34:03.150173", "level": "INFO", "logger": "root", "message": "Found 0 team addons for user e4ae2d8f-b32f-52d7-99cd-8a33b19652c7", "module": "subscription", "function": "get_user_feature_matrix", "line": 181}
{"timestamp": "2025-06-11T21:34:03.150910", "level": "INFO", "logger": "root", "message": "Retrieved base features for plan enterprise", "module": "subscription", "function": "get_user_feature_matrix", "line": 191}
{"timestamp": "2025-06-11T21:34:03.171866", "level": "INFO", "logger": "root", "message": "Successfully generated feature matrix for user e4ae2d8f-b32f-52d7-99cd-8a33b19652c7", "module": "subscription", "function": "get_user_feature_matrix", "line": 284}
{"timestamp": "2025-06-11T21:46:03.210444", "level": "INFO", "logger": "backend.services.qr_preview_cache", "message": "QR preview cache cleanup task stopped", "module": "qr_preview_cache", "function": "stop", "line": 257}
{"timestamp": "2025-06-11T21:46:03.211296", "level": "INFO", "logger": "backend.main", "message": "QR preview cache stopped", "module": "main", "function": "shutdown_qr_preview_cache", "line": 352}
{"timestamp": "2025-06-11T21:46:08.676997", "level": "INFO", "logger": "backend.main", "message": "=== QRVibe API Startup: Logging to file is enabled ===", "module": "main", "function": "<module>", "line": 37}
{"timestamp": "2025-06-11T21:46:08.677147", "level": "INFO", "logger": "backend.main", "message": "Applying critical patches before initialization", "module": "main", "function": "<module>", "line": 43}
{"timestamp": "2025-06-11T21:46:08.677263", "level": "INFO", "logger": "backend.patches", "message": "Applying patches (none required)", "module": "__init__", "function": "apply_patches", "line": 11}
{"timestamp": "2025-06-11T21:46:08.677563", "level": "INFO", "logger": "backend.core.middleware.cors", "message": "Setting up CORS middleware with origins: ['https://localhost:3000', 'http://127.0.0.1:3000', 'http://localhost:3000', 'https://127.0.0.1:3000']", "module": "cors", "function": "setup_cors", "line": 86}
{"timestamp": "2025-06-11T21:46:08.677633", "level": "INFO", "logger": "backend.core.middleware.cors", "message": "CORS middleware configured - preflight requests should be handled automatically", "module": "cors", "function": "setup_cors", "line": 98}
{"timestamp": "2025-06-11T21:46:08.679286", "level": "INFO", "logger": "backend.middleware.security_middleware", "message": "Added Enhanced Security Middleware with tiered rate limiting", "module": "security_middleware", "function": "add_security_middleware", "line": 176}
{"timestamp": "2025-06-11T21:46:08.679598", "level": "INFO", "logger": "backend.main", "message": "Mounting storage directory: /Users/<USER>/aihub/qrvibe/storage", "module": "main", "function": "<module>", "line": 107}
{"timestamp": "2025-06-11T21:46:08.679760", "level": "INFO", "logger": "backend.main", "message": "Storage directory absolute path: /Users/<USER>/aihub/qrvibe/storage", "module": "main", "function": "<module>", "line": 119}
{"timestamp": "2025-06-11T21:46:09.097873", "level": "INFO", "logger": "root", "message": "Storage initialized: environment=test, storage_dir=/Users/<USER>/aihub/qrvibe/storage, s3_enabled=True", "module": "storage", "function": "__init__", "line": 41}
{"timestamp": "2025-06-11T21:46:09.100674", "level": "INFO", "logger": "root", "message": "S3 storage enabled with bucket: qrverse and CDN: https://dev-cdn.qrvibe.com", "module": "storage", "function": "__init__", "line": 59}
{"timestamp": "2025-06-11T21:46:09.147501", "level": "INFO", "logger": "root", "message": "Storage initialized: environment=test, storage_dir=/Users/<USER>/aihub/qrvibe/storage, s3_enabled=True", "module": "storage", "function": "__init__", "line": 41}
{"timestamp": "2025-06-11T21:46:09.150704", "level": "INFO", "logger": "root", "message": "S3 storage enabled with bucket: qrverse and CDN: https://dev-cdn.qrvibe.com", "module": "storage", "function": "__init__", "line": 59}
{"timestamp": "2025-06-11T21:46:09.184872", "level": "INFO", "logger": "backend.middleware.security_middleware", "message": "Enhanced security middleware initialized with tiered rate limiting", "module": "security_middleware", "function": "__init__", "line": 87}
{"timestamp": "2025-06-11T21:46:09.184977", "level": "INFO", "logger": "backend.core.middleware.csrf", "message": "CSRF Protection Middleware initialized with exclude paths: ['/auth/jwt/login', '/auth/logout', '/auth/register', '/auth/2fa', '/auth/me', '/auth/verify-email', '/auth/reset-password', '/admin/login', '/admin/logout']", "module": "csrf", "function": "__init__", "line": 44}
{"timestamp": "2025-06-11T21:46:09.190508", "level": "INFO", "logger": "backend.services.token_blacklist", "message": "Token blacklist service initialized with Redis", "module": "token_blacklist", "function": "initialize", "line": 59}
{"timestamp": "2025-06-11T21:46:09.190594", "level": "INFO", "logger": "backend.main", "message": "Token blacklist service initialized", "module": "main", "function": "initialize_token_blacklist", "line": 98}
{"timestamp": "2025-06-11T21:46:09.193462", "level": "INFO", "logger": "backend.services.qr_preview_cache", "message": "QR preview cache initialized at /Users/<USER>/aihub/qrvibe/storage/cache/qr_preview_cache.db", "module": "qr_preview_cache", "function": "_init_db", "line": 56}
{"timestamp": "2025-06-11T21:46:09.193538", "level": "INFO", "logger": "backend.services.qr_preview_cache", "message": "Started QR preview cache cleanup task", "module": "qr_preview_cache", "function": "start_cleanup_task", "line": 62}
{"timestamp": "2025-06-11T21:46:09.193591", "level": "INFO", "logger": "backend.main", "message": "QR preview cache initialized and cleanup task started", "module": "main", "function": "init_qr_preview_cache", "line": 345}
{"timestamp": "2025-06-11T21:46:26.336099", "level": "WARNING", "logger": "backend.core.security.jwt_keys", "message": "Unknown JWT kid requested: 130c1a46-1cee-41c5-8820-dcbc48e9741e", "module": "jwt_keys", "function": "get_verification_key", "line": 105}
{"timestamp": "2025-06-11T21:46:26.336436", "level": "WARNING", "logger": "backend.core.security.jwt_keys", "message": "Verification failed with key ID 130c1a46-1cee-41c5-8820-dcbc48e9741e: Unknown kid: 130c1a46-1cee-41c5-8820-dcbc48e9741e", "module": "jwt_keys", "function": "decode_token", "line": 203}
{"timestamp": "2025-06-11T21:46:26.346104", "level": "WARNING", "logger": "backend.core.security.jwt_keys", "message": "Verification failed with current key: Signature verification failed.", "module": "jwt_keys", "function": "decode_token", "line": 216}
{"timestamp": "2025-06-11T21:46:26.346413", "level": "ERROR", "logger": "backend.core.security.jwt_keys", "message": "Token verification failed with all keys. Errors: Key 55c8121a-4e96-43fe-bf5d-4977abf8897a: Signature verification failed.", "module": "jwt_keys", "function": "decode_token", "line": 239}
{"timestamp": "2025-06-11T21:46:26.347192", "level": "ERROR", "logger": "backend.core.security.jwt_keys", "message": "Token decode error: Token verification failed with all keys. Errors: Key 55c8121a-4e96-43fe-bf5d-4977abf8897a: Signature verification failed.", "module": "jwt_keys", "function": "decode_token", "line": 244}
{"timestamp": "2025-06-11T21:46:26.347400", "level": "ERROR", "logger": "backend.services.auth", "message": "JWT token validation failed: Failed to decode token: Token verification failed with all keys. Errors: Key 55c8121a-4e96-43fe-bf5d-4977abf8897a: Signature verification failed.", "module": "auth", "function": "authenticate_token", "line": 686}
{"timestamp": "2025-06-11T21:46:26.349530", "level": "WARNING", "logger": "backend.middleware.subscription_limits", "message": "Error authenticating token in subscription middleware: name 'ValidationError' is not defined", "module": "subscription_limits", "function": "_get_current_user", "line": 308}
{"timestamp": "2025-06-11T21:46:26.360943", "level": "WARNING", "logger": "backend.core.security.jwt_keys", "message": "Unknown JWT kid requested: 130c1a46-1cee-41c5-8820-dcbc48e9741e", "module": "jwt_keys", "function": "get_verification_key", "line": 105}
{"timestamp": "2025-06-11T21:46:26.361065", "level": "WARNING", "logger": "backend.core.security.jwt_keys", "message": "Verification failed with key ID 130c1a46-1cee-41c5-8820-dcbc48e9741e: Unknown kid: 130c1a46-1cee-41c5-8820-dcbc48e9741e", "module": "jwt_keys", "function": "decode_token", "line": 203}
{"timestamp": "2025-06-11T21:46:26.361286", "level": "WARNING", "logger": "backend.core.security.jwt_keys", "message": "Verification failed with current key: Signature verification failed.", "module": "jwt_keys", "function": "decode_token", "line": 216}
{"timestamp": "2025-06-11T21:46:26.365178", "level": "ERROR", "logger": "backend.core.security.jwt_keys", "message": "Token verification failed with all keys. Errors: Key 55c8121a-4e96-43fe-bf5d-4977abf8897a: Signature verification failed.", "module": "jwt_keys", "function": "decode_token", "line": 239}
{"timestamp": "2025-06-11T21:46:26.365264", "level": "ERROR", "logger": "backend.core.security.jwt_keys", "message": "Token decode error: Token verification failed with all keys. Errors: Key 55c8121a-4e96-43fe-bf5d-4977abf8897a: Signature verification failed.", "module": "jwt_keys", "function": "decode_token", "line": 244}
{"timestamp": "2025-06-11T21:46:26.365339", "level": "ERROR", "logger": "backend.services.auth", "message": "JWT token validation failed: Failed to decode token: Token verification failed with all keys. Errors: Key 55c8121a-4e96-43fe-bf5d-4977abf8897a: Signature verification failed.", "module": "auth", "function": "authenticate_token", "line": 686}
{"timestamp": "2025-06-11T21:46:26.365869", "level": "WARNING", "logger": "backend.middleware.subscription_limits", "message": "Auth error in subscription middleware: name 'ValidationError' is not defined", "module": "subscription_limits", "function": "dispatch", "line": 234}
{"timestamp": "2025-06-11T21:46:26.366412", "level": "WARNING", "logger": "backend.core.security.jwt_keys", "message": "Unknown JWT kid requested: 130c1a46-1cee-41c5-8820-dcbc48e9741e", "module": "jwt_keys", "function": "get_verification_key", "line": 105}
{"timestamp": "2025-06-11T21:46:26.368681", "level": "WARNING", "logger": "backend.core.security.jwt_keys", "message": "Verification failed with key ID 130c1a46-1cee-41c5-8820-dcbc48e9741e: Unknown kid: 130c1a46-1cee-41c5-8820-dcbc48e9741e", "module": "jwt_keys", "function": "decode_token", "line": 203}
{"timestamp": "2025-06-11T21:46:26.368920", "level": "WARNING", "logger": "backend.core.security.jwt_keys", "message": "Verification failed with current key: Signature verification failed.", "module": "jwt_keys", "function": "decode_token", "line": 216}
{"timestamp": "2025-06-11T21:46:26.369137", "level": "ERROR", "logger": "backend.core.security.jwt_keys", "message": "Token verification failed with all keys. Errors: Key 55c8121a-4e96-43fe-bf5d-4977abf8897a: Signature verification failed.", "module": "jwt_keys", "function": "decode_token", "line": 239}
{"timestamp": "2025-06-11T21:46:26.369241", "level": "ERROR", "logger": "backend.core.security.jwt_keys", "message": "Token decode error: Token verification failed with all keys. Errors: Key 55c8121a-4e96-43fe-bf5d-4977abf8897a: Signature verification failed.", "module": "jwt_keys", "function": "decode_token", "line": 244}
{"timestamp": "2025-06-11T21:46:26.369329", "level": "ERROR", "logger": "backend.services.auth", "message": "JWT token validation failed: Failed to decode token: Token verification failed with all keys. Errors: Key 55c8121a-4e96-43fe-bf5d-4977abf8897a: Signature verification failed.", "module": "auth", "function": "authenticate_token", "line": 686}
{"timestamp": "2025-06-11T21:46:42.600664", "level": "WARNING", "logger": "backend.core.security.jwt_keys", "message": "Unknown JWT kid requested: 130c1a46-1cee-41c5-8820-dcbc48e9741e", "module": "jwt_keys", "function": "get_verification_key", "line": 105}
{"timestamp": "2025-06-11T21:46:42.601174", "level": "WARNING", "logger": "backend.core.security.jwt_keys", "message": "Verification failed with key ID 130c1a46-1cee-41c5-8820-dcbc48e9741e: Unknown kid: 130c1a46-1cee-41c5-8820-dcbc48e9741e", "module": "jwt_keys", "function": "decode_token", "line": 203}
{"timestamp": "2025-06-11T21:46:42.602293", "level": "WARNING", "logger": "backend.core.security.jwt_keys", "message": "Verification failed with current key: Signature verification failed.", "module": "jwt_keys", "function": "decode_token", "line": 216}
{"timestamp": "2025-06-11T21:46:42.602774", "level": "ERROR", "logger": "backend.core.security.jwt_keys", "message": "Token verification failed with all keys. Errors: Key 55c8121a-4e96-43fe-bf5d-4977abf8897a: Signature verification failed.", "module": "jwt_keys", "function": "decode_token", "line": 239}
{"timestamp": "2025-06-11T21:46:42.602948", "level": "ERROR", "logger": "backend.core.security.jwt_keys", "message": "Token decode error: Token verification failed with all keys. Errors: Key 55c8121a-4e96-43fe-bf5d-4977abf8897a: Signature verification failed.", "module": "jwt_keys", "function": "decode_token", "line": 244}
{"timestamp": "2025-06-11T21:46:42.603125", "level": "ERROR", "logger": "backend.services.auth", "message": "JWT token validation failed: Failed to decode token: Token verification failed with all keys. Errors: Key 55c8121a-4e96-43fe-bf5d-4977abf8897a: Signature verification failed.", "module": "auth", "function": "authenticate_token", "line": 686}
{"timestamp": "2025-06-11T21:46:42.603971", "level": "WARNING", "logger": "backend.middleware.subscription_limits", "message": "Error authenticating token in subscription middleware: name 'ValidationError' is not defined", "module": "subscription_limits", "function": "_get_current_user", "line": 308}
{"timestamp": "2025-06-11T21:46:42.609335", "level": "WARNING", "logger": "backend.core.security.jwt_keys", "message": "Unknown JWT kid requested: 130c1a46-1cee-41c5-8820-dcbc48e9741e", "module": "jwt_keys", "function": "get_verification_key", "line": 105}
{"timestamp": "2025-06-11T21:46:42.610858", "level": "WARNING", "logger": "backend.core.security.jwt_keys", "message": "Verification failed with key ID 130c1a46-1cee-41c5-8820-dcbc48e9741e: Unknown kid: 130c1a46-1cee-41c5-8820-dcbc48e9741e", "module": "jwt_keys", "function": "decode_token", "line": 203}
{"timestamp": "2025-06-11T21:46:42.611119", "level": "WARNING", "logger": "backend.core.security.jwt_keys", "message": "Verification failed with current key: Signature verification failed.", "module": "jwt_keys", "function": "decode_token", "line": 216}
{"timestamp": "2025-06-11T21:46:42.612631", "level": "ERROR", "logger": "backend.core.security.jwt_keys", "message": "Token verification failed with all keys. Errors: Key 55c8121a-4e96-43fe-bf5d-4977abf8897a: Signature verification failed.", "module": "jwt_keys", "function": "decode_token", "line": 239}
{"timestamp": "2025-06-11T21:46:42.612888", "level": "ERROR", "logger": "backend.core.security.jwt_keys", "message": "Token decode error: Token verification failed with all keys. Errors: Key 55c8121a-4e96-43fe-bf5d-4977abf8897a: Signature verification failed.", "module": "jwt_keys", "function": "decode_token", "line": 244}
{"timestamp": "2025-06-11T21:46:42.613032", "level": "ERROR", "logger": "backend.services.auth", "message": "JWT token validation failed: Failed to decode token: Token verification failed with all keys. Errors: Key 55c8121a-4e96-43fe-bf5d-4977abf8897a: Signature verification failed.", "module": "auth", "function": "authenticate_token", "line": 686}
{"timestamp": "2025-06-11T21:46:42.614069", "level": "WARNING", "logger": "backend.middleware.subscription_limits", "message": "Auth error in subscription middleware: name 'ValidationError' is not defined", "module": "subscription_limits", "function": "dispatch", "line": 234}
{"timestamp": "2025-06-11T21:46:42.614843", "level": "WARNING", "logger": "backend.core.security.jwt_keys", "message": "Unknown JWT kid requested: 130c1a46-1cee-41c5-8820-dcbc48e9741e", "module": "jwt_keys", "function": "get_verification_key", "line": 105}
{"timestamp": "2025-06-11T21:46:42.614953", "level": "WARNING", "logger": "backend.core.security.jwt_keys", "message": "Verification failed with key ID 130c1a46-1cee-41c5-8820-dcbc48e9741e: Unknown kid: 130c1a46-1cee-41c5-8820-dcbc48e9741e", "module": "jwt_keys", "function": "decode_token", "line": 203}
{"timestamp": "2025-06-11T21:46:42.615143", "level": "WARNING", "logger": "backend.core.security.jwt_keys", "message": "Verification failed with current key: Signature verification failed.", "module": "jwt_keys", "function": "decode_token", "line": 216}
{"timestamp": "2025-06-11T21:46:42.615275", "level": "ERROR", "logger": "backend.core.security.jwt_keys", "message": "Token verification failed with all keys. Errors: Key 55c8121a-4e96-43fe-bf5d-4977abf8897a: Signature verification failed.", "module": "jwt_keys", "function": "decode_token", "line": 239}
{"timestamp": "2025-06-11T21:46:42.615341", "level": "ERROR", "logger": "backend.core.security.jwt_keys", "message": "Token decode error: Token verification failed with all keys. Errors: Key 55c8121a-4e96-43fe-bf5d-4977abf8897a: Signature verification failed.", "module": "jwt_keys", "function": "decode_token", "line": 244}
{"timestamp": "2025-06-11T21:46:42.615402", "level": "ERROR", "logger": "backend.services.auth", "message": "JWT token validation failed: Failed to decode token: Token verification failed with all keys. Errors: Key 55c8121a-4e96-43fe-bf5d-4977abf8897a: Signature verification failed.", "module": "auth", "function": "authenticate_token", "line": 686}
{"timestamp": "2025-06-11T21:47:16.840010", "level": "WARNING", "logger": "backend.core.security.jwt_keys", "message": "Unknown JWT kid requested: 130c1a46-1cee-41c5-8820-dcbc48e9741e", "module": "jwt_keys", "function": "get_verification_key", "line": 105}
{"timestamp": "2025-06-11T21:47:16.840260", "level": "WARNING", "logger": "backend.core.security.jwt_keys", "message": "Verification failed with key ID 130c1a46-1cee-41c5-8820-dcbc48e9741e: Unknown kid: 130c1a46-1cee-41c5-8820-dcbc48e9741e", "module": "jwt_keys", "function": "decode_token", "line": 203}
{"timestamp": "2025-06-11T21:47:16.842396", "level": "WARNING", "logger": "backend.core.security.jwt_keys", "message": "Verification failed with current key: Signature verification failed.", "module": "jwt_keys", "function": "decode_token", "line": 216}
{"timestamp": "2025-06-11T21:47:16.842855", "level": "ERROR", "logger": "backend.core.security.jwt_keys", "message": "Token verification failed with all keys. Errors: Key 55c8121a-4e96-43fe-bf5d-4977abf8897a: Signature verification failed.", "module": "jwt_keys", "function": "decode_token", "line": 239}
{"timestamp": "2025-06-11T21:47:16.842954", "level": "ERROR", "logger": "backend.core.security.jwt_keys", "message": "Token decode error: Token verification failed with all keys. Errors: Key 55c8121a-4e96-43fe-bf5d-4977abf8897a: Signature verification failed.", "module": "jwt_keys", "function": "decode_token", "line": 244}
{"timestamp": "2025-06-11T21:47:16.843653", "level": "ERROR", "logger": "backend.services.auth", "message": "JWT token validation failed: Failed to decode token: Token verification failed with all keys. Errors: Key 55c8121a-4e96-43fe-bf5d-4977abf8897a: Signature verification failed.", "module": "auth", "function": "authenticate_token", "line": 686}
{"timestamp": "2025-06-11T21:47:16.844696", "level": "WARNING", "logger": "backend.core.security.jwt_keys", "message": "Unknown JWT kid requested: 130c1a46-1cee-41c5-8820-dcbc48e9741e", "module": "jwt_keys", "function": "get_verification_key", "line": 105}
{"timestamp": "2025-06-11T21:47:16.844764", "level": "WARNING", "logger": "backend.core.security.jwt_keys", "message": "Verification failed with key ID 130c1a46-1cee-41c5-8820-dcbc48e9741e: Unknown kid: 130c1a46-1cee-41c5-8820-dcbc48e9741e", "module": "jwt_keys", "function": "decode_token", "line": 203}
{"timestamp": "2025-06-11T21:47:16.844936", "level": "WARNING", "logger": "backend.core.security.jwt_keys", "message": "Verification failed with current key: Signature verification failed.", "module": "jwt_keys", "function": "decode_token", "line": 216}
{"timestamp": "2025-06-11T21:47:16.845382", "level": "ERROR", "logger": "backend.core.security.jwt_keys", "message": "Token verification failed with all keys. Errors: Key 55c8121a-4e96-43fe-bf5d-4977abf8897a: Signature verification failed.", "module": "jwt_keys", "function": "decode_token", "line": 239}
{"timestamp": "2025-06-11T21:47:16.845466", "level": "ERROR", "logger": "backend.core.security.jwt_keys", "message": "Token decode error: Token verification failed with all keys. Errors: Key 55c8121a-4e96-43fe-bf5d-4977abf8897a: Signature verification failed.", "module": "jwt_keys", "function": "decode_token", "line": 244}
{"timestamp": "2025-06-11T21:47:16.845546", "level": "ERROR", "logger": "backend.services.auth", "message": "JWT token validation failed: Failed to decode token: Token verification failed with all keys. Errors: Key 55c8121a-4e96-43fe-bf5d-4977abf8897a: Signature verification failed.", "module": "auth", "function": "authenticate_token", "line": 686}
{"timestamp": "2025-06-11T21:47:16.848060", "level": "WARNING", "logger": "backend.core.security.jwt_keys", "message": "Unknown JWT kid requested: 130c1a46-1cee-41c5-8820-dcbc48e9741e", "module": "jwt_keys", "function": "get_verification_key", "line": 105}
{"timestamp": "2025-06-11T21:47:16.848129", "level": "WARNING", "logger": "backend.core.security.jwt_keys", "message": "Verification failed with key ID 130c1a46-1cee-41c5-8820-dcbc48e9741e: Unknown kid: 130c1a46-1cee-41c5-8820-dcbc48e9741e", "module": "jwt_keys", "function": "decode_token", "line": 203}
{"timestamp": "2025-06-11T21:47:16.848305", "level": "WARNING", "logger": "backend.core.security.jwt_keys", "message": "Verification failed with current key: Signature verification failed.", "module": "jwt_keys", "function": "decode_token", "line": 216}
{"timestamp": "2025-06-11T21:47:16.848695", "level": "ERROR", "logger": "backend.core.security.jwt_keys", "message": "Token verification failed with all keys. Errors: Key 55c8121a-4e96-43fe-bf5d-4977abf8897a: Signature verification failed.", "module": "jwt_keys", "function": "decode_token", "line": 239}
{"timestamp": "2025-06-11T21:47:16.848823", "level": "ERROR", "logger": "backend.core.security.jwt_keys", "message": "Token decode error: Token verification failed with all keys. Errors: Key 55c8121a-4e96-43fe-bf5d-4977abf8897a: Signature verification failed.", "module": "jwt_keys", "function": "decode_token", "line": 244}
{"timestamp": "2025-06-11T21:47:16.848966", "level": "ERROR", "logger": "backend.auth", "message": "Token validation error: Failed to decode token: Token verification failed with all keys. Errors: Key 55c8121a-4e96-43fe-bf5d-4977abf8897a: Signature verification failed.", "module": "__init__", "function": "get_current_user", "line": 193}
{"timestamp": "2025-06-11T21:47:16.849396", "level": "WARNING", "logger": "backend.middleware.subscription_limits", "message": "Error authenticating token in subscription middleware: name 'ValidationError' is not defined", "module": "subscription_limits", "function": "_get_current_user", "line": 308}
{"timestamp": "2025-06-11T21:47:16.850064", "level": "WARNING", "logger": "backend.middleware.subscription_limits", "message": "Error authenticating token in subscription middleware: name 'ValidationError' is not defined", "module": "subscription_limits", "function": "_get_current_user", "line": 308}
{"timestamp": "2025-06-11T21:47:16.851001", "level": "WARNING", "logger": "backend.core.security.jwt_keys", "message": "Unknown JWT kid requested: 130c1a46-1cee-41c5-8820-dcbc48e9741e", "module": "jwt_keys", "function": "get_verification_key", "line": 105}
{"timestamp": "2025-06-11T21:47:16.851071", "level": "WARNING", "logger": "backend.core.security.jwt_keys", "message": "Verification failed with key ID 130c1a46-1cee-41c5-8820-dcbc48e9741e: Unknown kid: 130c1a46-1cee-41c5-8820-dcbc48e9741e", "module": "jwt_keys", "function": "decode_token", "line": 203}
{"timestamp": "2025-06-11T21:47:16.851288", "level": "WARNING", "logger": "backend.core.security.jwt_keys", "message": "Verification failed with current key: Signature verification failed.", "module": "jwt_keys", "function": "decode_token", "line": 216}
{"timestamp": "2025-06-11T21:47:16.851485", "level": "ERROR", "logger": "backend.core.security.jwt_keys", "message": "Token verification failed with all keys. Errors: Key 55c8121a-4e96-43fe-bf5d-4977abf8897a: Signature verification failed.", "module": "jwt_keys", "function": "decode_token", "line": 239}
{"timestamp": "2025-06-11T21:47:16.851571", "level": "ERROR", "logger": "backend.core.security.jwt_keys", "message": "Token decode error: Token verification failed with all keys. Errors: Key 55c8121a-4e96-43fe-bf5d-4977abf8897a: Signature verification failed.", "module": "jwt_keys", "function": "decode_token", "line": 244}
{"timestamp": "2025-06-11T21:47:16.851669", "level": "ERROR", "logger": "backend.auth", "message": "Token validation error: Failed to decode token: Token verification failed with all keys. Errors: Key 55c8121a-4e96-43fe-bf5d-4977abf8897a: Signature verification failed.", "module": "__init__", "function": "get_current_user", "line": 193}
{"timestamp": "2025-06-11T21:47:16.852409", "level": "WARNING", "logger": "backend.core.security.jwt_keys", "message": "Unknown JWT kid requested: 130c1a46-1cee-41c5-8820-dcbc48e9741e", "module": "jwt_keys", "function": "get_verification_key", "line": 105}
{"timestamp": "2025-06-11T21:47:16.852491", "level": "WARNING", "logger": "backend.core.security.jwt_keys", "message": "Verification failed with key ID 130c1a46-1cee-41c5-8820-dcbc48e9741e: Unknown kid: 130c1a46-1cee-41c5-8820-dcbc48e9741e", "module": "jwt_keys", "function": "decode_token", "line": 203}
{"timestamp": "2025-06-11T21:47:16.852666", "level": "WARNING", "logger": "backend.core.security.jwt_keys", "message": "Verification failed with current key: Signature verification failed.", "module": "jwt_keys", "function": "decode_token", "line": 216}
{"timestamp": "2025-06-11T21:47:16.852806", "level": "ERROR", "logger": "backend.core.security.jwt_keys", "message": "Token verification failed with all keys. Errors: Key 55c8121a-4e96-43fe-bf5d-4977abf8897a: Signature verification failed.", "module": "jwt_keys", "function": "decode_token", "line": 239}
{"timestamp": "2025-06-11T21:47:16.852869", "level": "ERROR", "logger": "backend.core.security.jwt_keys", "message": "Token decode error: Token verification failed with all keys. Errors: Key 55c8121a-4e96-43fe-bf5d-4977abf8897a: Signature verification failed.", "module": "jwt_keys", "function": "decode_token", "line": 244}
{"timestamp": "2025-06-11T21:47:16.852929", "level": "ERROR", "logger": "backend.auth", "message": "Token validation error: Failed to decode token: Token verification failed with all keys. Errors: Key 55c8121a-4e96-43fe-bf5d-4977abf8897a: Signature verification failed.", "module": "__init__", "function": "get_current_user", "line": 193}
{"timestamp": "2025-06-11T21:47:37.029563", "level": "INFO", "logger": "backend.services.enhanced_rate_limit", "message": "Auth event for anonymous: login_attempt (failure)", "module": "enhanced_rate_limit", "function": "track_auth_event", "line": 182}
{"timestamp": "2025-06-11T21:47:37.598041", "level": "INFO", "logger": "backend.services.auth", "message": "Authentication successful for user: <EMAIL>", "module": "auth", "function": "authenticate_user", "line": 266}
{"timestamp": "2025-06-11T21:47:37.599281", "level": "INFO", "logger": "backend.routes.auth", "message": "User logged in successfully: <EMAIL>", "module": "auth", "function": "login", "line": 218}
{"timestamp": "2025-06-11T21:47:37.600278", "level": "INFO", "logger": "backend.services.enhanced_rate_limit", "message": "Auth event for user e4ae2d8f-b32f-52d7-99cd-8a33b19652c7: login_success (success)", "module": "enhanced_rate_limit", "function": "track_auth_event", "line": 182}
{"timestamp": "2025-06-11T21:47:37.637772", "level": "INFO", "logger": "backend.services.adaptive_session", "message": "Adaptive session for user e4ae2d8f-b32f-52d7-99cd-8a33b19652c7: risk=low, duration=86400s", "module": "adaptive_session", "function": "get_session_expiry", "line": 161}
{"timestamp": "2025-06-11T21:47:37.637880", "level": "INFO", "logger": "backend.routes.auth", "message": "Adaptive session <NAME_EMAIL>: duration=86400s", "module": "auth", "function": "login", "line": 279}
{"timestamp": "2025-06-11T21:47:37.637945", "level": "INFO", "logger": "backend.routes.auth", "message": "Bearer token authentication issued <NAME_EMAIL>", "module": "auth", "function": "login", "line": 284}
{"timestamp": "2025-06-11T21:56:54.894158", "level": "INFO", "logger": "backend.services.qr_preview_cache", "message": "QR preview cache cleanup task stopped", "module": "qr_preview_cache", "function": "stop", "line": 257}
{"timestamp": "2025-06-11T21:56:54.894638", "level": "INFO", "logger": "backend.main", "message": "QR preview cache stopped", "module": "main", "function": "shutdown_qr_preview_cache", "line": 352}
{"timestamp": "2025-06-11T21:56:59.141643", "level": "INFO", "logger": "backend.main", "message": "=== QRVibe API Startup: Logging to file is enabled ===", "module": "main", "function": "<module>", "line": 37}
{"timestamp": "2025-06-11T21:56:59.141752", "level": "INFO", "logger": "backend.main", "message": "Applying critical patches before initialization", "module": "main", "function": "<module>", "line": 43}
{"timestamp": "2025-06-11T21:56:59.141825", "level": "INFO", "logger": "backend.patches", "message": "Applying patches (none required)", "module": "__init__", "function": "apply_patches", "line": 11}
{"timestamp": "2025-06-11T21:56:59.142052", "level": "INFO", "logger": "backend.core.middleware.cors", "message": "Setting up CORS middleware with origins: ['https://localhost:3000', 'http://127.0.0.1:3000', 'https://127.0.0.1:3000', 'http://localhost:3000']", "module": "cors", "function": "setup_cors", "line": 86}
{"timestamp": "2025-06-11T21:56:59.142124", "level": "INFO", "logger": "backend.core.middleware.cors", "message": "CORS middleware configured - preflight requests should be handled automatically", "module": "cors", "function": "setup_cors", "line": 98}
{"timestamp": "2025-06-11T21:56:59.144078", "level": "INFO", "logger": "backend.middleware.security_middleware", "message": "Added Enhanced Security Middleware with tiered rate limiting", "module": "security_middleware", "function": "add_security_middleware", "line": 176}
{"timestamp": "2025-06-11T21:56:59.144223", "level": "INFO", "logger": "backend.main", "message": "Mounting storage directory: /Users/<USER>/aihub/qrvibe/storage", "module": "main", "function": "<module>", "line": 107}
{"timestamp": "2025-06-11T21:56:59.144360", "level": "INFO", "logger": "backend.main", "message": "Storage directory absolute path: /Users/<USER>/aihub/qrvibe/storage", "module": "main", "function": "<module>", "line": 119}
{"timestamp": "2025-06-11T21:56:59.557734", "level": "INFO", "logger": "root", "message": "Storage initialized: environment=test, storage_dir=/Users/<USER>/aihub/qrvibe/storage, s3_enabled=True", "module": "storage", "function": "__init__", "line": 41}
{"timestamp": "2025-06-11T21:56:59.560795", "level": "INFO", "logger": "root", "message": "S3 storage enabled with bucket: qrverse and CDN: https://dev-cdn.qrvibe.com", "module": "storage", "function": "__init__", "line": 59}
{"timestamp": "2025-06-11T21:56:59.608401", "level": "INFO", "logger": "root", "message": "Storage initialized: environment=test, storage_dir=/Users/<USER>/aihub/qrvibe/storage, s3_enabled=True", "module": "storage", "function": "__init__", "line": 41}
{"timestamp": "2025-06-11T21:56:59.611526", "level": "INFO", "logger": "root", "message": "S3 storage enabled with bucket: qrverse and CDN: https://dev-cdn.qrvibe.com", "module": "storage", "function": "__init__", "line": 59}
{"timestamp": "2025-06-11T21:56:59.640684", "level": "INFO", "logger": "backend.middleware.security_middleware", "message": "Enhanced security middleware initialized with tiered rate limiting", "module": "security_middleware", "function": "__init__", "line": 87}
{"timestamp": "2025-06-11T21:56:59.640806", "level": "INFO", "logger": "backend.core.middleware.csrf", "message": "CSRF Protection Middleware initialized with exclude paths: ['/auth/jwt/login', '/auth/logout', '/auth/register', '/auth/2fa', '/auth/me', '/auth/verify-email', '/auth/reset-password', '/admin/login', '/admin/logout']", "module": "csrf", "function": "__init__", "line": 44}
{"timestamp": "2025-06-11T21:56:59.645619", "level": "INFO", "logger": "backend.services.token_blacklist", "message": "Token blacklist service initialized with Redis", "module": "token_blacklist", "function": "initialize", "line": 59}
{"timestamp": "2025-06-11T21:56:59.645716", "level": "INFO", "logger": "backend.main", "message": "Token blacklist service initialized", "module": "main", "function": "initialize_token_blacklist", "line": 98}
{"timestamp": "2025-06-11T21:56:59.648512", "level": "INFO", "logger": "backend.services.qr_preview_cache", "message": "QR preview cache initialized at /Users/<USER>/aihub/qrvibe/storage/cache/qr_preview_cache.db", "module": "qr_preview_cache", "function": "_init_db", "line": 56}
{"timestamp": "2025-06-11T21:56:59.648591", "level": "INFO", "logger": "backend.services.qr_preview_cache", "message": "Started QR preview cache cleanup task", "module": "qr_preview_cache", "function": "start_cleanup_task", "line": 62}
{"timestamp": "2025-06-11T21:56:59.648653", "level": "INFO", "logger": "backend.main", "message": "QR preview cache initialized and cleanup task started", "module": "main", "function": "init_qr_preview_cache", "line": 345}
{"timestamp": "2025-06-11T21:57:22.197100", "level": "INFO", "logger": "backend.services.qr_preview_cache", "message": "QR preview cache cleanup task stopped", "module": "qr_preview_cache", "function": "stop", "line": 257}
{"timestamp": "2025-06-11T21:57:22.197257", "level": "INFO", "logger": "backend.main", "message": "QR preview cache stopped", "module": "main", "function": "shutdown_qr_preview_cache", "line": 352}
{"timestamp": "2025-06-11T21:57:25.591010", "level": "INFO", "logger": "backend.main", "message": "=== QRVibe API Startup: Logging to file is enabled ===", "module": "main", "function": "<module>", "line": 37}
{"timestamp": "2025-06-11T21:57:25.591105", "level": "INFO", "logger": "backend.main", "message": "Applying critical patches before initialization", "module": "main", "function": "<module>", "line": 43}
{"timestamp": "2025-06-11T21:57:25.591166", "level": "INFO", "logger": "backend.patches", "message": "Applying patches (none required)", "module": "__init__", "function": "apply_patches", "line": 11}
{"timestamp": "2025-06-11T21:57:25.591385", "level": "INFO", "logger": "backend.core.middleware.cors", "message": "Setting up CORS middleware with origins: ['https://127.0.0.1:3000', 'http://127.0.0.1:3000', 'http://localhost:3000', 'https://localhost:3000']", "module": "cors", "function": "setup_cors", "line": 86}
{"timestamp": "2025-06-11T21:57:25.591443", "level": "INFO", "logger": "backend.core.middleware.cors", "message": "CORS middleware configured - preflight requests should be handled automatically", "module": "cors", "function": "setup_cors", "line": 98}
{"timestamp": "2025-06-11T21:57:25.592420", "level": "INFO", "logger": "backend.middleware.security_middleware", "message": "Added Enhanced Security Middleware with tiered rate limiting", "module": "security_middleware", "function": "add_security_middleware", "line": 176}
{"timestamp": "2025-06-11T21:57:25.592502", "level": "INFO", "logger": "backend.main", "message": "Mounting storage directory: /Users/<USER>/aihub/qrvibe/storage", "module": "main", "function": "<module>", "line": 107}
{"timestamp": "2025-06-11T21:57:25.592609", "level": "INFO", "logger": "backend.main", "message": "Storage directory absolute path: /Users/<USER>/aihub/qrvibe/storage", "module": "main", "function": "<module>", "line": 119}
{"timestamp": "2025-06-11T21:57:25.999267", "level": "INFO", "logger": "root", "message": "Storage initialized: environment=test, storage_dir=/Users/<USER>/aihub/qrvibe/storage, s3_enabled=True", "module": "storage", "function": "__init__", "line": 41}
{"timestamp": "2025-06-11T21:57:26.001997", "level": "INFO", "logger": "root", "message": "S3 storage enabled with bucket: qrverse and CDN: https://dev-cdn.qrvibe.com", "module": "storage", "function": "__init__", "line": 59}
{"timestamp": "2025-06-11T21:57:26.048338", "level": "INFO", "logger": "root", "message": "Storage initialized: environment=test, storage_dir=/Users/<USER>/aihub/qrvibe/storage, s3_enabled=True", "module": "storage", "function": "__init__", "line": 41}
{"timestamp": "2025-06-11T21:57:26.051316", "level": "INFO", "logger": "root", "message": "S3 storage enabled with bucket: qrverse and CDN: https://dev-cdn.qrvibe.com", "module": "storage", "function": "__init__", "line": 59}
{"timestamp": "2025-06-11T21:57:26.103824", "level": "INFO", "logger": "backend.middleware.security_middleware", "message": "Enhanced security middleware initialized with tiered rate limiting", "module": "security_middleware", "function": "__init__", "line": 87}
{"timestamp": "2025-06-11T21:57:26.103943", "level": "INFO", "logger": "backend.core.middleware.csrf", "message": "CSRF Protection Middleware initialized with exclude paths: ['/auth/jwt/login', '/auth/logout', '/auth/register', '/auth/2fa', '/auth/me', '/auth/verify-email', '/auth/reset-password', '/admin/login', '/admin/logout']", "module": "csrf", "function": "__init__", "line": 44}
{"timestamp": "2025-06-11T21:57:26.115394", "level": "INFO", "logger": "backend.services.token_blacklist", "message": "Token blacklist service initialized with Redis", "module": "token_blacklist", "function": "initialize", "line": 59}
{"timestamp": "2025-06-11T21:57:26.122055", "level": "INFO", "logger": "backend.main", "message": "Token blacklist service initialized", "module": "main", "function": "initialize_token_blacklist", "line": 98}
{"timestamp": "2025-06-11T21:57:26.122789", "level": "INFO", "logger": "backend.services.qr_preview_cache", "message": "QR preview cache initialized at /Users/<USER>/aihub/qrvibe/storage/cache/qr_preview_cache.db", "module": "qr_preview_cache", "function": "_init_db", "line": 56}
{"timestamp": "2025-06-11T21:57:26.122889", "level": "INFO", "logger": "backend.services.qr_preview_cache", "message": "Started QR preview cache cleanup task", "module": "qr_preview_cache", "function": "start_cleanup_task", "line": 62}
{"timestamp": "2025-06-11T21:57:26.122971", "level": "INFO", "logger": "backend.main", "message": "QR preview cache initialized and cleanup task started", "module": "main", "function": "init_qr_preview_cache", "line": 345}
{"timestamp": "2025-06-11T21:58:04.176133", "level": "WARNING", "logger": "backend.core.security.jwt_keys", "message": "Unknown JWT kid requested: 55c8121a-4e96-43fe-bf5d-4977abf8897a", "module": "jwt_keys", "function": "get_verification_key", "line": 105}
{"timestamp": "2025-06-11T21:58:04.182730", "level": "WARNING", "logger": "backend.core.security.jwt_keys", "message": "Verification failed with key ID 55c8121a-4e96-43fe-bf5d-4977abf8897a: Unknown kid: 55c8121a-4e96-43fe-bf5d-4977abf8897a", "module": "jwt_keys", "function": "decode_token", "line": 203}
{"timestamp": "2025-06-11T21:58:04.209668", "level": "WARNING", "logger": "backend.core.security.jwt_keys", "message": "Verification failed with current key: Signature verification failed.", "module": "jwt_keys", "function": "decode_token", "line": 216}
{"timestamp": "2025-06-11T21:58:04.210016", "level": "ERROR", "logger": "backend.core.security.jwt_keys", "message": "Token verification failed with all keys. Errors: Key 031a5f05-a8d4-404e-87c7-d79789ffffc2: Signature verification failed.", "module": "jwt_keys", "function": "decode_token", "line": 239}
{"timestamp": "2025-06-11T21:58:04.210101", "level": "ERROR", "logger": "backend.core.security.jwt_keys", "message": "Token decode error: Token verification failed with all keys. Errors: Key 031a5f05-a8d4-404e-87c7-d79789ffffc2: Signature verification failed.", "module": "jwt_keys", "function": "decode_token", "line": 244}
{"timestamp": "2025-06-11T21:58:04.210221", "level": "ERROR", "logger": "backend.services.auth", "message": "JWT token validation failed: Failed to decode token: Token verification failed with all keys. Errors: Key 031a5f05-a8d4-404e-87c7-d79789ffffc2: Signature verification failed.", "module": "auth", "function": "authenticate_token", "line": 686}
{"timestamp": "2025-06-11T21:58:04.211235", "level": "WARNING", "logger": "backend.middleware.subscription_limits", "message": "Error authenticating token in subscription middleware: name 'ValidationError' is not defined", "module": "subscription_limits", "function": "_get_current_user", "line": 308}
{"timestamp": "2025-06-11T21:58:04.216852", "level": "WARNING", "logger": "backend.core.security.jwt_keys", "message": "Unknown JWT kid requested: 55c8121a-4e96-43fe-bf5d-4977abf8897a", "module": "jwt_keys", "function": "get_verification_key", "line": 105}
{"timestamp": "2025-06-11T21:58:04.217016", "level": "WARNING", "logger": "backend.core.security.jwt_keys", "message": "Verification failed with key ID 55c8121a-4e96-43fe-bf5d-4977abf8897a: Unknown kid: 55c8121a-4e96-43fe-bf5d-4977abf8897a", "module": "jwt_keys", "function": "decode_token", "line": 203}
{"timestamp": "2025-06-11T21:58:04.217655", "level": "WARNING", "logger": "backend.core.security.jwt_keys", "message": "Verification failed with current key: Signature verification failed.", "module": "jwt_keys", "function": "decode_token", "line": 216}
{"timestamp": "2025-06-11T21:58:04.218559", "level": "ERROR", "logger": "backend.core.security.jwt_keys", "message": "Token verification failed with all keys. Errors: Key 031a5f05-a8d4-404e-87c7-d79789ffffc2: Signature verification failed.", "module": "jwt_keys", "function": "decode_token", "line": 239}
{"timestamp": "2025-06-11T21:58:04.218732", "level": "ERROR", "logger": "backend.core.security.jwt_keys", "message": "Token decode error: Token verification failed with all keys. Errors: Key 031a5f05-a8d4-404e-87c7-d79789ffffc2: Signature verification failed.", "module": "jwt_keys", "function": "decode_token", "line": 244}
{"timestamp": "2025-06-11T21:58:04.218927", "level": "ERROR", "logger": "backend.services.auth", "message": "JWT token validation failed: Failed to decode token: Token verification failed with all keys. Errors: Key 031a5f05-a8d4-404e-87c7-d79789ffffc2: Signature verification failed.", "module": "auth", "function": "authenticate_token", "line": 686}
{"timestamp": "2025-06-11T21:58:04.221023", "level": "WARNING", "logger": "backend.middleware.subscription_limits", "message": "Auth error in subscription middleware: name 'ValidationError' is not defined", "module": "subscription_limits", "function": "dispatch", "line": 234}
{"timestamp": "2025-06-11T21:58:04.221793", "level": "WARNING", "logger": "backend.core.security.jwt_keys", "message": "Unknown JWT kid requested: 55c8121a-4e96-43fe-bf5d-4977abf8897a", "module": "jwt_keys", "function": "get_verification_key", "line": 105}
{"timestamp": "2025-06-11T21:58:04.223379", "level": "WARNING", "logger": "backend.core.security.jwt_keys", "message": "Verification failed with key ID 55c8121a-4e96-43fe-bf5d-4977abf8897a: Unknown kid: 55c8121a-4e96-43fe-bf5d-4977abf8897a", "module": "jwt_keys", "function": "decode_token", "line": 203}
{"timestamp": "2025-06-11T21:58:04.223773", "level": "WARNING", "logger": "backend.core.security.jwt_keys", "message": "Verification failed with current key: Signature verification failed.", "module": "jwt_keys", "function": "decode_token", "line": 216}
{"timestamp": "2025-06-11T21:58:04.226629", "level": "ERROR", "logger": "backend.core.security.jwt_keys", "message": "Token verification failed with all keys. Errors: Key 031a5f05-a8d4-404e-87c7-d79789ffffc2: Signature verification failed.", "module": "jwt_keys", "function": "decode_token", "line": 239}
{"timestamp": "2025-06-11T21:58:04.226728", "level": "ERROR", "logger": "backend.core.security.jwt_keys", "message": "Token decode error: Token verification failed with all keys. Errors: Key 031a5f05-a8d4-404e-87c7-d79789ffffc2: Signature verification failed.", "module": "jwt_keys", "function": "decode_token", "line": 244}
{"timestamp": "2025-06-11T21:58:04.226827", "level": "ERROR", "logger": "backend.services.auth", "message": "JWT token validation failed: Failed to decode token: Token verification failed with all keys. Errors: Key 031a5f05-a8d4-404e-87c7-d79789ffffc2: Signature verification failed.", "module": "auth", "function": "authenticate_token", "line": 686}
{"timestamp": "2025-06-11T21:58:47.090170", "level": "WARNING", "logger": "backend.core.security.jwt_keys", "message": "Unknown JWT kid requested: 55c8121a-4e96-43fe-bf5d-4977abf8897a", "module": "jwt_keys", "function": "get_verification_key", "line": 105}
{"timestamp": "2025-06-11T21:58:47.090572", "level": "WARNING", "logger": "backend.core.security.jwt_keys", "message": "Verification failed with key ID 55c8121a-4e96-43fe-bf5d-4977abf8897a: Unknown kid: 55c8121a-4e96-43fe-bf5d-4977abf8897a", "module": "jwt_keys", "function": "decode_token", "line": 203}
{"timestamp": "2025-06-11T21:58:47.094926", "level": "WARNING", "logger": "backend.core.security.jwt_keys", "message": "Verification failed with current key: Signature verification failed.", "module": "jwt_keys", "function": "decode_token", "line": 216}
{"timestamp": "2025-06-11T21:58:47.095386", "level": "ERROR", "logger": "backend.core.security.jwt_keys", "message": "Token verification failed with all keys. Errors: Key 031a5f05-a8d4-404e-87c7-d79789ffffc2: Signature verification failed.", "module": "jwt_keys", "function": "decode_token", "line": 239}
{"timestamp": "2025-06-11T21:58:47.095590", "level": "ERROR", "logger": "backend.core.security.jwt_keys", "message": "Token decode error: Token verification failed with all keys. Errors: Key 031a5f05-a8d4-404e-87c7-d79789ffffc2: Signature verification failed.", "module": "jwt_keys", "function": "decode_token", "line": 244}
{"timestamp": "2025-06-11T21:58:47.095772", "level": "ERROR", "logger": "backend.services.auth", "message": "JWT token validation failed: Failed to decode token: Token verification failed with all keys. Errors: Key 031a5f05-a8d4-404e-87c7-d79789ffffc2: Signature verification failed.", "module": "auth", "function": "authenticate_token", "line": 686}
{"timestamp": "2025-06-11T21:58:47.096483", "level": "WARNING", "logger": "backend.middleware.subscription_limits", "message": "Error authenticating token in subscription middleware: name 'ValidationError' is not defined", "module": "subscription_limits", "function": "_get_current_user", "line": 308}
{"timestamp": "2025-06-11T21:58:47.098114", "level": "WARNING", "logger": "backend.core.security.jwt_keys", "message": "Unknown JWT kid requested: 55c8121a-4e96-43fe-bf5d-4977abf8897a", "module": "jwt_keys", "function": "get_verification_key", "line": 105}
{"timestamp": "2025-06-11T21:58:47.098186", "level": "WARNING", "logger": "backend.core.security.jwt_keys", "message": "Verification failed with key ID 55c8121a-4e96-43fe-bf5d-4977abf8897a: Unknown kid: 55c8121a-4e96-43fe-bf5d-4977abf8897a", "module": "jwt_keys", "function": "decode_token", "line": 203}
{"timestamp": "2025-06-11T21:58:47.098350", "level": "WARNING", "logger": "backend.core.security.jwt_keys", "message": "Verification failed with current key: Signature verification failed.", "module": "jwt_keys", "function": "decode_token", "line": 216}
{"timestamp": "2025-06-11T21:58:47.098513", "level": "ERROR", "logger": "backend.core.security.jwt_keys", "message": "Token verification failed with all keys. Errors: Key 031a5f05-a8d4-404e-87c7-d79789ffffc2: Signature verification failed.", "module": "jwt_keys", "function": "decode_token", "line": 239}
{"timestamp": "2025-06-11T21:58:47.099297", "level": "ERROR", "logger": "backend.core.security.jwt_keys", "message": "Token decode error: Token verification failed with all keys. Errors: Key 031a5f05-a8d4-404e-87c7-d79789ffffc2: Signature verification failed.", "module": "jwt_keys", "function": "decode_token", "line": 244}
{"timestamp": "2025-06-11T21:58:47.099494", "level": "ERROR", "logger": "backend.services.auth", "message": "JWT token validation failed: Failed to decode token: Token verification failed with all keys. Errors: Key 031a5f05-a8d4-404e-87c7-d79789ffffc2: Signature verification failed.", "module": "auth", "function": "authenticate_token", "line": 686}
{"timestamp": "2025-06-11T21:58:47.101684", "level": "WARNING", "logger": "backend.middleware.subscription_limits", "message": "Auth error in subscription middleware: name 'ValidationError' is not defined", "module": "subscription_limits", "function": "dispatch", "line": 234}
{"timestamp": "2025-06-11T21:58:47.102902", "level": "WARNING", "logger": "backend.core.security.jwt_keys", "message": "Unknown JWT kid requested: 55c8121a-4e96-43fe-bf5d-4977abf8897a", "module": "jwt_keys", "function": "get_verification_key", "line": 105}
{"timestamp": "2025-06-11T21:58:47.102965", "level": "WARNING", "logger": "backend.core.security.jwt_keys", "message": "Verification failed with key ID 55c8121a-4e96-43fe-bf5d-4977abf8897a: Unknown kid: 55c8121a-4e96-43fe-bf5d-4977abf8897a", "module": "jwt_keys", "function": "decode_token", "line": 203}
{"timestamp": "2025-06-11T21:58:47.103444", "level": "WARNING", "logger": "backend.core.security.jwt_keys", "message": "Verification failed with current key: Signature verification failed.", "module": "jwt_keys", "function": "decode_token", "line": 216}
{"timestamp": "2025-06-11T21:58:47.103659", "level": "ERROR", "logger": "backend.core.security.jwt_keys", "message": "Token verification failed with all keys. Errors: Key 031a5f05-a8d4-404e-87c7-d79789ffffc2: Signature verification failed.", "module": "jwt_keys", "function": "decode_token", "line": 239}
{"timestamp": "2025-06-11T21:58:47.103734", "level": "ERROR", "logger": "backend.core.security.jwt_keys", "message": "Token decode error: Token verification failed with all keys. Errors: Key 031a5f05-a8d4-404e-87c7-d79789ffffc2: Signature verification failed.", "module": "jwt_keys", "function": "decode_token", "line": 244}
{"timestamp": "2025-06-11T21:58:47.103812", "level": "ERROR", "logger": "backend.services.auth", "message": "JWT token validation failed: Failed to decode token: Token verification failed with all keys. Errors: Key 031a5f05-a8d4-404e-87c7-d79789ffffc2: Signature verification failed.", "module": "auth", "function": "authenticate_token", "line": 686}
{"timestamp": "2025-06-11T21:59:23.834345", "level": "WARNING", "logger": "backend.core.security.jwt_keys", "message": "Unknown JWT kid requested: 55c8121a-4e96-43fe-bf5d-4977abf8897a", "module": "jwt_keys", "function": "get_verification_key", "line": 105}
{"timestamp": "2025-06-11T21:59:23.834567", "level": "WARNING", "logger": "backend.core.security.jwt_keys", "message": "Verification failed with key ID 55c8121a-4e96-43fe-bf5d-4977abf8897a: Unknown kid: 55c8121a-4e96-43fe-bf5d-4977abf8897a", "module": "jwt_keys", "function": "decode_token", "line": 203}
{"timestamp": "2025-06-11T21:59:23.835215", "level": "WARNING", "logger": "backend.core.security.jwt_keys", "message": "Verification failed with current key: Signature verification failed.", "module": "jwt_keys", "function": "decode_token", "line": 216}
{"timestamp": "2025-06-11T21:59:23.835790", "level": "ERROR", "logger": "backend.core.security.jwt_keys", "message": "Token verification failed with all keys. Errors: Key 031a5f05-a8d4-404e-87c7-d79789ffffc2: Signature verification failed.", "module": "jwt_keys", "function": "decode_token", "line": 239}
{"timestamp": "2025-06-11T21:59:23.835863", "level": "ERROR", "logger": "backend.core.security.jwt_keys", "message": "Token decode error: Token verification failed with all keys. Errors: Key 031a5f05-a8d4-404e-87c7-d79789ffffc2: Signature verification failed.", "module": "jwt_keys", "function": "decode_token", "line": 244}
{"timestamp": "2025-06-11T21:59:23.835976", "level": "ERROR", "logger": "backend.services.auth", "message": "JWT token validation failed: Failed to decode token: Token verification failed with all keys. Errors: Key 031a5f05-a8d4-404e-87c7-d79789ffffc2: Signature verification failed.", "module": "auth", "function": "authenticate_token", "line": 686}
{"timestamp": "2025-06-11T21:59:23.838327", "level": "WARNING", "logger": "backend.core.security.jwt_keys", "message": "Unknown JWT kid requested: 55c8121a-4e96-43fe-bf5d-4977abf8897a", "module": "jwt_keys", "function": "get_verification_key", "line": 105}
{"timestamp": "2025-06-11T21:59:23.838395", "level": "WARNING", "logger": "backend.core.security.jwt_keys", "message": "Verification failed with key ID 55c8121a-4e96-43fe-bf5d-4977abf8897a: Unknown kid: 55c8121a-4e96-43fe-bf5d-4977abf8897a", "module": "jwt_keys", "function": "decode_token", "line": 203}
{"timestamp": "2025-06-11T21:59:23.838633", "level": "WARNING", "logger": "backend.core.security.jwt_keys", "message": "Verification failed with current key: Signature verification failed.", "module": "jwt_keys", "function": "decode_token", "line": 216}
{"timestamp": "2025-06-11T21:59:23.838807", "level": "ERROR", "logger": "backend.core.security.jwt_keys", "message": "Token verification failed with all keys. Errors: Key 031a5f05-a8d4-404e-87c7-d79789ffffc2: Signature verification failed.", "module": "jwt_keys", "function": "decode_token", "line": 239}
{"timestamp": "2025-06-11T21:59:23.838924", "level": "ERROR", "logger": "backend.core.security.jwt_keys", "message": "Token decode error: Token verification failed with all keys. Errors: Key 031a5f05-a8d4-404e-87c7-d79789ffffc2: Signature verification failed.", "module": "jwt_keys", "function": "decode_token", "line": 244}
{"timestamp": "2025-06-11T21:59:23.839155", "level": "ERROR", "logger": "backend.auth", "message": "Token validation error: Failed to decode token: Token verification failed with all keys. Errors: Key 031a5f05-a8d4-404e-87c7-d79789ffffc2: Signature verification failed.", "module": "__init__", "function": "get_current_user", "line": 193}
{"timestamp": "2025-06-11T21:59:23.841733", "level": "WARNING", "logger": "backend.middleware.subscription_limits", "message": "Error authenticating token in subscription middleware: name 'ValidationError' is not defined", "module": "subscription_limits", "function": "_get_current_user", "line": 308}
{"timestamp": "2025-06-11T21:59:23.845344", "level": "WARNING", "logger": "backend.core.security.jwt_keys", "message": "Unknown JWT kid requested: 55c8121a-4e96-43fe-bf5d-4977abf8897a", "module": "jwt_keys", "function": "get_verification_key", "line": 105}
{"timestamp": "2025-06-11T21:59:23.845427", "level": "WARNING", "logger": "backend.core.security.jwt_keys", "message": "Verification failed with key ID 55c8121a-4e96-43fe-bf5d-4977abf8897a: Unknown kid: 55c8121a-4e96-43fe-bf5d-4977abf8897a", "module": "jwt_keys", "function": "decode_token", "line": 203}
{"timestamp": "2025-06-11T21:59:23.849353", "level": "WARNING", "logger": "backend.core.security.jwt_keys", "message": "Verification failed with current key: Signature verification failed.", "module": "jwt_keys", "function": "decode_token", "line": 216}
{"timestamp": "2025-06-11T21:59:23.849817", "level": "ERROR", "logger": "backend.core.security.jwt_keys", "message": "Token verification failed with all keys. Errors: Key 031a5f05-a8d4-404e-87c7-d79789ffffc2: Signature verification failed.", "module": "jwt_keys", "function": "decode_token", "line": 239}
{"timestamp": "2025-06-11T21:59:23.849913", "level": "ERROR", "logger": "backend.core.security.jwt_keys", "message": "Token decode error: Token verification failed with all keys. Errors: Key 031a5f05-a8d4-404e-87c7-d79789ffffc2: Signature verification failed.", "module": "jwt_keys", "function": "decode_token", "line": 244}
{"timestamp": "2025-06-11T21:59:23.850006", "level": "ERROR", "logger": "backend.auth", "message": "Token validation error: Failed to decode token: Token verification failed with all keys. Errors: Key 031a5f05-a8d4-404e-87c7-d79789ffffc2: Signature verification failed.", "module": "__init__", "function": "get_current_user", "line": 193}
{"timestamp": "2025-06-11T21:59:44.286593", "level": "INFO", "logger": "backend.services.enhanced_rate_limit", "message": "Auth event for anonymous: login_attempt (failure)", "module": "enhanced_rate_limit", "function": "track_auth_event", "line": 182}
{"timestamp": "2025-06-11T21:59:44.795424", "level": "INFO", "logger": "backend.services.auth", "message": "Authentication successful for user: <EMAIL>", "module": "auth", "function": "authenticate_user", "line": 266}
{"timestamp": "2025-06-11T21:59:44.797183", "level": "INFO", "logger": "backend.routes.auth", "message": "User logged in successfully: <EMAIL>", "module": "auth", "function": "login", "line": 218}
{"timestamp": "2025-06-11T21:59:44.798512", "level": "INFO", "logger": "backend.services.enhanced_rate_limit", "message": "Auth event for user e4ae2d8f-b32f-52d7-99cd-8a33b19652c7: login_success (success)", "module": "enhanced_rate_limit", "function": "track_auth_event", "line": 182}
{"timestamp": "2025-06-11T21:59:44.836366", "level": "INFO", "logger": "backend.services.adaptive_session", "message": "Adaptive session for user e4ae2d8f-b32f-52d7-99cd-8a33b19652c7: risk=low, duration=86400s", "module": "adaptive_session", "function": "get_session_expiry", "line": 161}
{"timestamp": "2025-06-11T21:59:44.836456", "level": "INFO", "logger": "backend.routes.auth", "message": "Adaptive session <NAME_EMAIL>: duration=86400s", "module": "auth", "function": "login", "line": 279}
{"timestamp": "2025-06-11T21:59:44.836510", "level": "INFO", "logger": "backend.routes.auth", "message": "Bearer token authentication issued <NAME_EMAIL>", "module": "auth", "function": "login", "line": 284}
{"timestamp": "2025-06-11T22:06:56.122341", "level": "INFO", "logger": "backend.services.qr_preview_cache", "message": "QR preview cache cleanup task stopped", "module": "qr_preview_cache", "function": "stop", "line": 257}
{"timestamp": "2025-06-11T22:06:56.122938", "level": "INFO", "logger": "backend.main", "message": "QR preview cache stopped", "module": "main", "function": "shutdown_qr_preview_cache", "line": 352}
{"timestamp": "2025-06-11T22:07:00.110989", "level": "INFO", "logger": "backend.main", "message": "=== QRVibe API Startup: Logging to file is enabled ===", "module": "main", "function": "<module>", "line": 37}
{"timestamp": "2025-06-11T22:07:00.111168", "level": "INFO", "logger": "backend.main", "message": "Applying critical patches before initialization", "module": "main", "function": "<module>", "line": 43}
{"timestamp": "2025-06-11T22:07:00.111248", "level": "INFO", "logger": "backend.patches", "message": "Applying patches (none required)", "module": "__init__", "function": "apply_patches", "line": 11}
{"timestamp": "2025-06-11T22:07:00.111595", "level": "INFO", "logger": "backend.core.middleware.cors", "message": "Setting up CORS middleware with origins: ['http://127.0.0.1:3000', 'https://127.0.0.1:3000', 'http://localhost:3000', 'https://localhost:3000']", "module": "cors", "function": "setup_cors", "line": 86}
{"timestamp": "2025-06-11T22:07:00.111909", "level": "INFO", "logger": "backend.core.middleware.cors", "message": "CORS middleware configured - preflight requests should be handled automatically", "module": "cors", "function": "setup_cors", "line": 98}
{"timestamp": "2025-06-11T22:07:00.113514", "level": "INFO", "logger": "backend.middleware.security_middleware", "message": "Added Enhanced Security Middleware with tiered rate limiting", "module": "security_middleware", "function": "add_security_middleware", "line": 176}
{"timestamp": "2025-06-11T22:07:00.113627", "level": "INFO", "logger": "backend.main", "message": "Mounting storage directory: /Users/<USER>/aihub/qrvibe/storage", "module": "main", "function": "<module>", "line": 107}
{"timestamp": "2025-06-11T22:07:00.113762", "level": "INFO", "logger": "backend.main", "message": "Storage directory absolute path: /Users/<USER>/aihub/qrvibe/storage", "module": "main", "function": "<module>", "line": 119}
{"timestamp": "2025-06-11T22:07:00.562988", "level": "INFO", "logger": "root", "message": "Storage initialized: environment=test, storage_dir=/Users/<USER>/aihub/qrvibe/storage, s3_enabled=True", "module": "storage", "function": "__init__", "line": 41}
{"timestamp": "2025-06-11T22:07:00.565868", "level": "INFO", "logger": "root", "message": "S3 storage enabled with bucket: qrverse and CDN: https://dev-cdn.qrvibe.com", "module": "storage", "function": "__init__", "line": 59}
{"timestamp": "2025-06-11T22:07:00.612643", "level": "INFO", "logger": "root", "message": "Storage initialized: environment=test, storage_dir=/Users/<USER>/aihub/qrvibe/storage, s3_enabled=True", "module": "storage", "function": "__init__", "line": 41}
{"timestamp": "2025-06-11T22:07:00.615711", "level": "INFO", "logger": "root", "message": "S3 storage enabled with bucket: qrverse and CDN: https://dev-cdn.qrvibe.com", "module": "storage", "function": "__init__", "line": 59}
{"timestamp": "2025-06-11T22:07:00.638307", "level": "INFO", "logger": "backend.middleware.security_middleware", "message": "Enhanced security middleware initialized with tiered rate limiting", "module": "security_middleware", "function": "__init__", "line": 87}
{"timestamp": "2025-06-11T22:07:00.638403", "level": "INFO", "logger": "backend.core.middleware.csrf", "message": "CSRF Protection Middleware initialized with exclude paths: ['/auth/jwt/login', '/auth/logout', '/auth/register', '/auth/2fa', '/auth/me', '/auth/verify-email', '/auth/reset-password', '/admin/login', '/admin/logout']", "module": "csrf", "function": "__init__", "line": 44}
{"timestamp": "2025-06-11T22:07:00.642726", "level": "INFO", "logger": "backend.services.token_blacklist", "message": "Token blacklist service initialized with Redis", "module": "token_blacklist", "function": "initialize", "line": 59}
{"timestamp": "2025-06-11T22:07:00.642790", "level": "INFO", "logger": "backend.main", "message": "Token blacklist service initialized", "module": "main", "function": "initialize_token_blacklist", "line": 98}
{"timestamp": "2025-06-11T22:07:00.643359", "level": "INFO", "logger": "backend.services.qr_preview_cache", "message": "QR preview cache initialized at /Users/<USER>/aihub/qrvibe/storage/cache/qr_preview_cache.db", "module": "qr_preview_cache", "function": "_init_db", "line": 56}
{"timestamp": "2025-06-11T22:07:00.643419", "level": "INFO", "logger": "backend.services.qr_preview_cache", "message": "Started QR preview cache cleanup task", "module": "qr_preview_cache", "function": "start_cleanup_task", "line": 62}
{"timestamp": "2025-06-11T22:07:00.643467", "level": "INFO", "logger": "backend.main", "message": "QR preview cache initialized and cleanup task started", "module": "main", "function": "init_qr_preview_cache", "line": 345}
{"timestamp": "2025-06-11T22:07:13.900278", "level": "INFO", "logger": "backend.services.qr_preview_cache", "message": "QR preview cache cleanup task stopped", "module": "qr_preview_cache", "function": "stop", "line": 257}
{"timestamp": "2025-06-11T22:07:13.900392", "level": "INFO", "logger": "backend.main", "message": "QR preview cache stopped", "module": "main", "function": "shutdown_qr_preview_cache", "line": 352}
{"timestamp": "2025-06-11T22:07:17.231630", "level": "INFO", "logger": "backend.main", "message": "=== QRVibe API Startup: Logging to file is enabled ===", "module": "main", "function": "<module>", "line": 37}
{"timestamp": "2025-06-11T22:07:17.231725", "level": "INFO", "logger": "backend.main", "message": "Applying critical patches before initialization", "module": "main", "function": "<module>", "line": 43}
{"timestamp": "2025-06-11T22:07:17.231793", "level": "INFO", "logger": "backend.patches", "message": "Applying patches (none required)", "module": "__init__", "function": "apply_patches", "line": 11}
{"timestamp": "2025-06-11T22:07:17.232005", "level": "INFO", "logger": "backend.core.middleware.cors", "message": "Setting up CORS middleware with origins: ['http://127.0.0.1:3000', 'http://localhost:3000', 'https://localhost:3000', 'https://127.0.0.1:3000']", "module": "cors", "function": "setup_cors", "line": 86}
{"timestamp": "2025-06-11T22:07:17.232069", "level": "INFO", "logger": "backend.core.middleware.cors", "message": "CORS middleware configured - preflight requests should be handled automatically", "module": "cors", "function": "setup_cors", "line": 98}
{"timestamp": "2025-06-11T22:07:17.233465", "level": "INFO", "logger": "backend.middleware.security_middleware", "message": "Added Enhanced Security Middleware with tiered rate limiting", "module": "security_middleware", "function": "add_security_middleware", "line": 176}
{"timestamp": "2025-06-11T22:07:17.233592", "level": "INFO", "logger": "backend.main", "message": "Mounting storage directory: /Users/<USER>/aihub/qrvibe/storage", "module": "main", "function": "<module>", "line": 107}
{"timestamp": "2025-06-11T22:07:17.233738", "level": "INFO", "logger": "backend.main", "message": "Storage directory absolute path: /Users/<USER>/aihub/qrvibe/storage", "module": "main", "function": "<module>", "line": 119}
{"timestamp": "2025-06-11T22:07:17.719779", "level": "INFO", "logger": "root", "message": "Storage initialized: environment=test, storage_dir=/Users/<USER>/aihub/qrvibe/storage, s3_enabled=True", "module": "storage", "function": "__init__", "line": 41}
{"timestamp": "2025-06-11T22:07:17.722820", "level": "INFO", "logger": "root", "message": "S3 storage enabled with bucket: qrverse and CDN: https://dev-cdn.qrvibe.com", "module": "storage", "function": "__init__", "line": 59}
{"timestamp": "2025-06-11T22:07:17.771421", "level": "INFO", "logger": "root", "message": "Storage initialized: environment=test, storage_dir=/Users/<USER>/aihub/qrvibe/storage, s3_enabled=True", "module": "storage", "function": "__init__", "line": 41}
{"timestamp": "2025-06-11T22:07:17.774292", "level": "INFO", "logger": "root", "message": "S3 storage enabled with bucket: qrverse and CDN: https://dev-cdn.qrvibe.com", "module": "storage", "function": "__init__", "line": 59}
{"timestamp": "2025-06-11T22:07:17.796928", "level": "INFO", "logger": "backend.middleware.security_middleware", "message": "Enhanced security middleware initialized with tiered rate limiting", "module": "security_middleware", "function": "__init__", "line": 87}
{"timestamp": "2025-06-11T22:07:17.805589", "level": "INFO", "logger": "backend.core.middleware.csrf", "message": "CSRF Protection Middleware initialized with exclude paths: ['/auth/jwt/login', '/auth/logout', '/auth/register', '/auth/2fa', '/auth/me', '/auth/verify-email', '/auth/reset-password', '/admin/login', '/admin/logout']", "module": "csrf", "function": "__init__", "line": 44}
{"timestamp": "2025-06-11T22:07:17.809601", "level": "INFO", "logger": "backend.services.token_blacklist", "message": "Token blacklist service initialized with Redis", "module": "token_blacklist", "function": "initialize", "line": 59}
{"timestamp": "2025-06-11T22:07:17.809693", "level": "INFO", "logger": "backend.main", "message": "Token blacklist service initialized", "module": "main", "function": "initialize_token_blacklist", "line": 98}
{"timestamp": "2025-06-11T22:07:17.810337", "level": "INFO", "logger": "backend.services.qr_preview_cache", "message": "QR preview cache initialized at /Users/<USER>/aihub/qrvibe/storage/cache/qr_preview_cache.db", "module": "qr_preview_cache", "function": "_init_db", "line": 56}
{"timestamp": "2025-06-11T22:07:17.810440", "level": "INFO", "logger": "backend.services.qr_preview_cache", "message": "Started QR preview cache cleanup task", "module": "qr_preview_cache", "function": "start_cleanup_task", "line": 62}
{"timestamp": "2025-06-11T22:07:17.810697", "level": "INFO", "logger": "backend.main", "message": "QR preview cache initialized and cleanup task started", "module": "main", "function": "init_qr_preview_cache", "line": 345}
{"timestamp": "2025-06-11T22:07:29.650004", "level": "INFO", "logger": "backend.services.qr_preview_cache", "message": "QR preview cache cleanup task stopped", "module": "qr_preview_cache", "function": "stop", "line": 257}
{"timestamp": "2025-06-11T22:07:29.650475", "level": "INFO", "logger": "backend.main", "message": "QR preview cache stopped", "module": "main", "function": "shutdown_qr_preview_cache", "line": 352}
{"timestamp": "2025-06-11T22:07:32.954223", "level": "INFO", "logger": "backend.main", "message": "=== QRVibe API Startup: Logging to file is enabled ===", "module": "main", "function": "<module>", "line": 37}
{"timestamp": "2025-06-11T22:07:32.954317", "level": "INFO", "logger": "backend.main", "message": "Applying critical patches before initialization", "module": "main", "function": "<module>", "line": 43}
{"timestamp": "2025-06-11T22:07:32.954379", "level": "INFO", "logger": "backend.patches", "message": "Applying patches (none required)", "module": "__init__", "function": "apply_patches", "line": 11}
{"timestamp": "2025-06-11T22:07:32.954612", "level": "INFO", "logger": "backend.core.middleware.cors", "message": "Setting up CORS middleware with origins: ['https://127.0.0.1:3000', 'http://localhost:3000', 'https://localhost:3000', 'http://127.0.0.1:3000']", "module": "cors", "function": "setup_cors", "line": 86}
{"timestamp": "2025-06-11T22:07:32.954668", "level": "INFO", "logger": "backend.core.middleware.cors", "message": "CORS middleware configured - preflight requests should be handled automatically", "module": "cors", "function": "setup_cors", "line": 98}
{"timestamp": "2025-06-11T22:07:32.955617", "level": "INFO", "logger": "backend.middleware.security_middleware", "message": "Added Enhanced Security Middleware with tiered rate limiting", "module": "security_middleware", "function": "add_security_middleware", "line": 176}
{"timestamp": "2025-06-11T22:07:32.955704", "level": "INFO", "logger": "backend.main", "message": "Mounting storage directory: /Users/<USER>/aihub/qrvibe/storage", "module": "main", "function": "<module>", "line": 107}
{"timestamp": "2025-06-11T22:07:32.955806", "level": "INFO", "logger": "backend.main", "message": "Storage directory absolute path: /Users/<USER>/aihub/qrvibe/storage", "module": "main", "function": "<module>", "line": 119}
{"timestamp": "2025-06-11T22:07:33.340985", "level": "INFO", "logger": "root", "message": "Storage initialized: environment=test, storage_dir=/Users/<USER>/aihub/qrvibe/storage, s3_enabled=True", "module": "storage", "function": "__init__", "line": 41}
{"timestamp": "2025-06-11T22:07:33.343790", "level": "INFO", "logger": "root", "message": "S3 storage enabled with bucket: qrverse and CDN: https://dev-cdn.qrvibe.com", "module": "storage", "function": "__init__", "line": 59}
{"timestamp": "2025-06-11T22:07:33.390140", "level": "INFO", "logger": "root", "message": "Storage initialized: environment=test, storage_dir=/Users/<USER>/aihub/qrvibe/storage, s3_enabled=True", "module": "storage", "function": "__init__", "line": 41}
{"timestamp": "2025-06-11T22:07:33.394191", "level": "INFO", "logger": "root", "message": "S3 storage enabled with bucket: qrverse and CDN: https://dev-cdn.qrvibe.com", "module": "storage", "function": "__init__", "line": 59}
{"timestamp": "2025-06-11T22:07:33.420121", "level": "INFO", "logger": "backend.middleware.security_middleware", "message": "Enhanced security middleware initialized with tiered rate limiting", "module": "security_middleware", "function": "__init__", "line": 87}
{"timestamp": "2025-06-11T22:07:33.420315", "level": "INFO", "logger": "backend.core.middleware.csrf", "message": "CSRF Protection Middleware initialized with exclude paths: ['/auth/jwt/login', '/auth/logout', '/auth/register', '/auth/2fa', '/auth/me', '/auth/verify-email', '/auth/reset-password', '/admin/login', '/admin/logout']", "module": "csrf", "function": "__init__", "line": 44}
{"timestamp": "2025-06-11T22:07:33.424442", "level": "INFO", "logger": "backend.services.token_blacklist", "message": "Token blacklist service initialized with Redis", "module": "token_blacklist", "function": "initialize", "line": 59}
{"timestamp": "2025-06-11T22:07:33.424552", "level": "INFO", "logger": "backend.main", "message": "Token blacklist service initialized", "module": "main", "function": "initialize_token_blacklist", "line": 98}
{"timestamp": "2025-06-11T22:07:33.425382", "level": "INFO", "logger": "backend.services.qr_preview_cache", "message": "QR preview cache initialized at /Users/<USER>/aihub/qrvibe/storage/cache/qr_preview_cache.db", "module": "qr_preview_cache", "function": "_init_db", "line": 56}
{"timestamp": "2025-06-11T22:07:33.425505", "level": "INFO", "logger": "backend.services.qr_preview_cache", "message": "Started QR preview cache cleanup task", "module": "qr_preview_cache", "function": "start_cleanup_task", "line": 62}
{"timestamp": "2025-06-11T22:07:33.425578", "level": "INFO", "logger": "backend.main", "message": "QR preview cache initialized and cleanup task started", "module": "main", "function": "init_qr_preview_cache", "line": 345}
{"timestamp": "2025-06-11T22:07:47.795982", "level": "INFO", "logger": "backend.services.qr_preview_cache", "message": "QR preview cache cleanup task stopped", "module": "qr_preview_cache", "function": "stop", "line": 257}
{"timestamp": "2025-06-11T22:07:47.796260", "level": "INFO", "logger": "backend.main", "message": "QR preview cache stopped", "module": "main", "function": "shutdown_qr_preview_cache", "line": 352}
{"timestamp": "2025-06-11T22:07:51.183498", "level": "INFO", "logger": "backend.main", "message": "=== QRVibe API Startup: Logging to file is enabled ===", "module": "main", "function": "<module>", "line": 37}
{"timestamp": "2025-06-11T22:07:51.183588", "level": "INFO", "logger": "backend.main", "message": "Applying critical patches before initialization", "module": "main", "function": "<module>", "line": 43}
{"timestamp": "2025-06-11T22:07:51.183647", "level": "INFO", "logger": "backend.patches", "message": "Applying patches (none required)", "module": "__init__", "function": "apply_patches", "line": 11}
{"timestamp": "2025-06-11T22:07:51.183860", "level": "INFO", "logger": "backend.core.middleware.cors", "message": "Setting up CORS middleware with origins: ['https://localhost:3000', 'https://127.0.0.1:3000', 'http://127.0.0.1:3000', 'http://localhost:3000']", "module": "cors", "function": "setup_cors", "line": 86}
{"timestamp": "2025-06-11T22:07:51.183919", "level": "INFO", "logger": "backend.core.middleware.cors", "message": "CORS middleware configured - preflight requests should be handled automatically", "module": "cors", "function": "setup_cors", "line": 98}
{"timestamp": "2025-06-11T22:07:51.184854", "level": "INFO", "logger": "backend.middleware.security_middleware", "message": "Added Enhanced Security Middleware with tiered rate limiting", "module": "security_middleware", "function": "add_security_middleware", "line": 176}
{"timestamp": "2025-06-11T22:07:51.184970", "level": "INFO", "logger": "backend.main", "message": "Mounting storage directory: /Users/<USER>/aihub/qrvibe/storage", "module": "main", "function": "<module>", "line": 107}
{"timestamp": "2025-06-11T22:07:51.185099", "level": "INFO", "logger": "backend.main", "message": "Storage directory absolute path: /Users/<USER>/aihub/qrvibe/storage", "module": "main", "function": "<module>", "line": 119}
{"timestamp": "2025-06-11T22:07:51.732554", "level": "INFO", "logger": "root", "message": "Storage initialized: environment=test, storage_dir=/Users/<USER>/aihub/qrvibe/storage, s3_enabled=True", "module": "storage", "function": "__init__", "line": 41}
{"timestamp": "2025-06-11T22:07:51.755022", "level": "INFO", "logger": "root", "message": "S3 storage enabled with bucket: qrverse and CDN: https://dev-cdn.qrvibe.com", "module": "storage", "function": "__init__", "line": 59}
{"timestamp": "2025-06-11T22:07:51.830952", "level": "INFO", "logger": "root", "message": "Storage initialized: environment=test, storage_dir=/Users/<USER>/aihub/qrvibe/storage, s3_enabled=True", "module": "storage", "function": "__init__", "line": 41}
{"timestamp": "2025-06-11T22:07:51.835023", "level": "INFO", "logger": "root", "message": "S3 storage enabled with bucket: qrverse and CDN: https://dev-cdn.qrvibe.com", "module": "storage", "function": "__init__", "line": 59}
{"timestamp": "2025-06-11T22:07:51.883018", "level": "INFO", "logger": "backend.middleware.security_middleware", "message": "Enhanced security middleware initialized with tiered rate limiting", "module": "security_middleware", "function": "__init__", "line": 87}
{"timestamp": "2025-06-11T22:07:51.883157", "level": "INFO", "logger": "backend.core.middleware.csrf", "message": "CSRF Protection Middleware initialized with exclude paths: ['/auth/jwt/login', '/auth/logout', '/auth/register', '/auth/2fa', '/auth/me', '/auth/verify-email', '/auth/reset-password', '/admin/login', '/admin/logout']", "module": "csrf", "function": "__init__", "line": 44}
{"timestamp": "2025-06-11T22:07:51.887514", "level": "INFO", "logger": "backend.services.token_blacklist", "message": "Token blacklist service initialized with Redis", "module": "token_blacklist", "function": "initialize", "line": 59}
{"timestamp": "2025-06-11T22:07:51.887607", "level": "INFO", "logger": "backend.main", "message": "Token blacklist service initialized", "module": "main", "function": "initialize_token_blacklist", "line": 98}
{"timestamp": "2025-06-11T22:07:51.888634", "level": "INFO", "logger": "backend.services.qr_preview_cache", "message": "QR preview cache initialized at /Users/<USER>/aihub/qrvibe/storage/cache/qr_preview_cache.db", "module": "qr_preview_cache", "function": "_init_db", "line": 56}
{"timestamp": "2025-06-11T22:07:51.888834", "level": "INFO", "logger": "backend.services.qr_preview_cache", "message": "Started QR preview cache cleanup task", "module": "qr_preview_cache", "function": "start_cleanup_task", "line": 62}
{"timestamp": "2025-06-11T22:07:51.888906", "level": "INFO", "logger": "backend.main", "message": "QR preview cache initialized and cleanup task started", "module": "main", "function": "init_qr_preview_cache", "line": 345}
{"timestamp": "2025-06-11T22:08:06.139400", "level": "INFO", "logger": "backend.services.qr_preview_cache", "message": "QR preview cache cleanup task stopped", "module": "qr_preview_cache", "function": "stop", "line": 257}
{"timestamp": "2025-06-11T22:08:06.139960", "level": "INFO", "logger": "backend.main", "message": "QR preview cache stopped", "module": "main", "function": "shutdown_qr_preview_cache", "line": 352}
{"timestamp": "2025-06-11T22:08:09.401232", "level": "INFO", "logger": "backend.main", "message": "=== QRVibe API Startup: Logging to file is enabled ===", "module": "main", "function": "<module>", "line": 37}
{"timestamp": "2025-06-11T22:08:09.401336", "level": "INFO", "logger": "backend.main", "message": "Applying critical patches before initialization", "module": "main", "function": "<module>", "line": 43}
{"timestamp": "2025-06-11T22:08:09.401403", "level": "INFO", "logger": "backend.patches", "message": "Applying patches (none required)", "module": "__init__", "function": "apply_patches", "line": 11}
{"timestamp": "2025-06-11T22:08:09.401628", "level": "INFO", "logger": "backend.core.middleware.cors", "message": "Setting up CORS middleware with origins: ['http://localhost:3000', 'https://localhost:3000', 'https://127.0.0.1:3000', 'http://127.0.0.1:3000']", "module": "cors", "function": "setup_cors", "line": 86}
{"timestamp": "2025-06-11T22:08:09.401692", "level": "INFO", "logger": "backend.core.middleware.cors", "message": "CORS middleware configured - preflight requests should be handled automatically", "module": "cors", "function": "setup_cors", "line": 98}
{"timestamp": "2025-06-11T22:08:09.402635", "level": "INFO", "logger": "backend.middleware.security_middleware", "message": "Added Enhanced Security Middleware with tiered rate limiting", "module": "security_middleware", "function": "add_security_middleware", "line": 176}
{"timestamp": "2025-06-11T22:08:09.402725", "level": "INFO", "logger": "backend.main", "message": "Mounting storage directory: /Users/<USER>/aihub/qrvibe/storage", "module": "main", "function": "<module>", "line": 107}
{"timestamp": "2025-06-11T22:08:09.402860", "level": "INFO", "logger": "backend.main", "message": "Storage directory absolute path: /Users/<USER>/aihub/qrvibe/storage", "module": "main", "function": "<module>", "line": 119}
{"timestamp": "2025-06-11T22:08:09.858918", "level": "INFO", "logger": "root", "message": "Storage initialized: environment=test, storage_dir=/Users/<USER>/aihub/qrvibe/storage, s3_enabled=True", "module": "storage", "function": "__init__", "line": 41}
{"timestamp": "2025-06-11T22:08:09.861603", "level": "INFO", "logger": "root", "message": "S3 storage enabled with bucket: qrverse and CDN: https://dev-cdn.qrvibe.com", "module": "storage", "function": "__init__", "line": 59}
{"timestamp": "2025-06-11T22:08:09.908883", "level": "INFO", "logger": "root", "message": "Storage initialized: environment=test, storage_dir=/Users/<USER>/aihub/qrvibe/storage, s3_enabled=True", "module": "storage", "function": "__init__", "line": 41}
{"timestamp": "2025-06-11T22:08:09.911944", "level": "INFO", "logger": "root", "message": "S3 storage enabled with bucket: qrverse and CDN: https://dev-cdn.qrvibe.com", "module": "storage", "function": "__init__", "line": 59}
{"timestamp": "2025-06-11T22:08:09.937836", "level": "INFO", "logger": "backend.middleware.security_middleware", "message": "Enhanced security middleware initialized with tiered rate limiting", "module": "security_middleware", "function": "__init__", "line": 87}
{"timestamp": "2025-06-11T22:08:09.937941", "level": "INFO", "logger": "backend.core.middleware.csrf", "message": "CSRF Protection Middleware initialized with exclude paths: ['/auth/jwt/login', '/auth/logout', '/auth/register', '/auth/2fa', '/auth/me', '/auth/verify-email', '/auth/reset-password', '/admin/login', '/admin/logout']", "module": "csrf", "function": "__init__", "line": 44}
{"timestamp": "2025-06-11T22:08:09.941241", "level": "INFO", "logger": "backend.services.token_blacklist", "message": "Token blacklist service initialized with Redis", "module": "token_blacklist", "function": "initialize", "line": 59}
{"timestamp": "2025-06-11T22:08:09.941328", "level": "INFO", "logger": "backend.main", "message": "Token blacklist service initialized", "module": "main", "function": "initialize_token_blacklist", "line": 98}
{"timestamp": "2025-06-11T22:08:09.942060", "level": "INFO", "logger": "backend.services.qr_preview_cache", "message": "QR preview cache initialized at /Users/<USER>/aihub/qrvibe/storage/cache/qr_preview_cache.db", "module": "qr_preview_cache", "function": "_init_db", "line": 56}
{"timestamp": "2025-06-11T22:08:09.942135", "level": "INFO", "logger": "backend.services.qr_preview_cache", "message": "Started QR preview cache cleanup task", "module": "qr_preview_cache", "function": "start_cleanup_task", "line": 62}
{"timestamp": "2025-06-11T22:08:09.942194", "level": "INFO", "logger": "backend.main", "message": "QR preview cache initialized and cleanup task started", "module": "main", "function": "init_qr_preview_cache", "line": 345}
{"timestamp": "2025-06-11T22:08:24.152811", "level": "INFO", "logger": "backend.services.qr_preview_cache", "message": "QR preview cache cleanup task stopped", "module": "qr_preview_cache", "function": "stop", "line": 257}
{"timestamp": "2025-06-11T22:08:24.153494", "level": "INFO", "logger": "backend.main", "message": "QR preview cache stopped", "module": "main", "function": "shutdown_qr_preview_cache", "line": 352}
{"timestamp": "2025-06-11T22:08:27.405312", "level": "INFO", "logger": "backend.main", "message": "=== QRVibe API Startup: Logging to file is enabled ===", "module": "main", "function": "<module>", "line": 37}
{"timestamp": "2025-06-11T22:08:27.405404", "level": "INFO", "logger": "backend.main", "message": "Applying critical patches before initialization", "module": "main", "function": "<module>", "line": 43}
{"timestamp": "2025-06-11T22:08:27.405483", "level": "INFO", "logger": "backend.patches", "message": "Applying patches (none required)", "module": "__init__", "function": "apply_patches", "line": 11}
{"timestamp": "2025-06-11T22:08:27.405716", "level": "INFO", "logger": "backend.core.middleware.cors", "message": "Setting up CORS middleware with origins: ['http://localhost:3000', 'https://127.0.0.1:3000', 'http://127.0.0.1:3000', 'https://localhost:3000']", "module": "cors", "function": "setup_cors", "line": 86}
{"timestamp": "2025-06-11T22:08:27.406270", "level": "INFO", "logger": "backend.core.middleware.cors", "message": "CORS middleware configured - preflight requests should be handled automatically", "module": "cors", "function": "setup_cors", "line": 98}
{"timestamp": "2025-06-11T22:08:27.407297", "level": "INFO", "logger": "backend.middleware.security_middleware", "message": "Added Enhanced Security Middleware with tiered rate limiting", "module": "security_middleware", "function": "add_security_middleware", "line": 176}
{"timestamp": "2025-06-11T22:08:27.407395", "level": "INFO", "logger": "backend.main", "message": "Mounting storage directory: /Users/<USER>/aihub/qrvibe/storage", "module": "main", "function": "<module>", "line": 107}
{"timestamp": "2025-06-11T22:08:27.407507", "level": "INFO", "logger": "backend.main", "message": "Storage directory absolute path: /Users/<USER>/aihub/qrvibe/storage", "module": "main", "function": "<module>", "line": 119}
{"timestamp": "2025-06-11T22:08:27.815179", "level": "INFO", "logger": "root", "message": "Storage initialized: environment=test, storage_dir=/Users/<USER>/aihub/qrvibe/storage, s3_enabled=True", "module": "storage", "function": "__init__", "line": 41}
{"timestamp": "2025-06-11T22:08:27.818210", "level": "INFO", "logger": "root", "message": "S3 storage enabled with bucket: qrverse and CDN: https://dev-cdn.qrvibe.com", "module": "storage", "function": "__init__", "line": 59}
{"timestamp": "2025-06-11T22:08:27.864962", "level": "INFO", "logger": "root", "message": "Storage initialized: environment=test, storage_dir=/Users/<USER>/aihub/qrvibe/storage, s3_enabled=True", "module": "storage", "function": "__init__", "line": 41}
{"timestamp": "2025-06-11T22:08:27.867830", "level": "INFO", "logger": "root", "message": "S3 storage enabled with bucket: qrverse and CDN: https://dev-cdn.qrvibe.com", "module": "storage", "function": "__init__", "line": 59}
{"timestamp": "2025-06-11T22:08:27.889254", "level": "INFO", "logger": "backend.middleware.security_middleware", "message": "Enhanced security middleware initialized with tiered rate limiting", "module": "security_middleware", "function": "__init__", "line": 87}
{"timestamp": "2025-06-11T22:08:27.889367", "level": "INFO", "logger": "backend.core.middleware.csrf", "message": "CSRF Protection Middleware initialized with exclude paths: ['/auth/jwt/login', '/auth/logout', '/auth/register', '/auth/2fa', '/auth/me', '/auth/verify-email', '/auth/reset-password', '/admin/login', '/admin/logout']", "module": "csrf", "function": "__init__", "line": 44}
{"timestamp": "2025-06-11T22:08:27.892807", "level": "INFO", "logger": "backend.services.token_blacklist", "message": "Token blacklist service initialized with Redis", "module": "token_blacklist", "function": "initialize", "line": 59}
{"timestamp": "2025-06-11T22:08:27.892896", "level": "INFO", "logger": "backend.main", "message": "Token blacklist service initialized", "module": "main", "function": "initialize_token_blacklist", "line": 98}
{"timestamp": "2025-06-11T22:08:27.893565", "level": "INFO", "logger": "backend.services.qr_preview_cache", "message": "QR preview cache initialized at /Users/<USER>/aihub/qrvibe/storage/cache/qr_preview_cache.db", "module": "qr_preview_cache", "function": "_init_db", "line": 56}
{"timestamp": "2025-06-11T22:08:27.893636", "level": "INFO", "logger": "backend.services.qr_preview_cache", "message": "Started QR preview cache cleanup task", "module": "qr_preview_cache", "function": "start_cleanup_task", "line": 62}
{"timestamp": "2025-06-11T22:08:27.893700", "level": "INFO", "logger": "backend.main", "message": "QR preview cache initialized and cleanup task started", "module": "main", "function": "init_qr_preview_cache", "line": 345}
{"timestamp": "2025-06-11T22:08:42.163125", "level": "INFO", "logger": "backend.services.qr_preview_cache", "message": "QR preview cache cleanup task stopped", "module": "qr_preview_cache", "function": "stop", "line": 257}
{"timestamp": "2025-06-11T22:08:42.163765", "level": "INFO", "logger": "backend.main", "message": "QR preview cache stopped", "module": "main", "function": "shutdown_qr_preview_cache", "line": 352}
{"timestamp": "2025-06-11T22:08:45.524955", "level": "INFO", "logger": "backend.main", "message": "=== QRVibe API Startup: Logging to file is enabled ===", "module": "main", "function": "<module>", "line": 37}
{"timestamp": "2025-06-11T22:08:45.525049", "level": "INFO", "logger": "backend.main", "message": "Applying critical patches before initialization", "module": "main", "function": "<module>", "line": 43}
{"timestamp": "2025-06-11T22:08:45.525111", "level": "INFO", "logger": "backend.patches", "message": "Applying patches (none required)", "module": "__init__", "function": "apply_patches", "line": 11}
{"timestamp": "2025-06-11T22:08:45.525349", "level": "INFO", "logger": "backend.core.middleware.cors", "message": "Setting up CORS middleware with origins: ['https://127.0.0.1:3000', 'http://127.0.0.1:3000', 'http://localhost:3000', 'https://localhost:3000']", "module": "cors", "function": "setup_cors", "line": 86}
{"timestamp": "2025-06-11T22:08:45.525411", "level": "INFO", "logger": "backend.core.middleware.cors", "message": "CORS middleware configured - preflight requests should be handled automatically", "module": "cors", "function": "setup_cors", "line": 98}
{"timestamp": "2025-06-11T22:08:45.526455", "level": "INFO", "logger": "backend.middleware.security_middleware", "message": "Added Enhanced Security Middleware with tiered rate limiting", "module": "security_middleware", "function": "add_security_middleware", "line": 176}
{"timestamp": "2025-06-11T22:08:45.526557", "level": "INFO", "logger": "backend.main", "message": "Mounting storage directory: /Users/<USER>/aihub/qrvibe/storage", "module": "main", "function": "<module>", "line": 107}
{"timestamp": "2025-06-11T22:08:45.526690", "level": "INFO", "logger": "backend.main", "message": "Storage directory absolute path: /Users/<USER>/aihub/qrvibe/storage", "module": "main", "function": "<module>", "line": 119}
{"timestamp": "2025-06-11T22:08:46.050331", "level": "INFO", "logger": "root", "message": "Storage initialized: environment=test, storage_dir=/Users/<USER>/aihub/qrvibe/storage, s3_enabled=True", "module": "storage", "function": "__init__", "line": 41}
{"timestamp": "2025-06-11T22:08:46.057217", "level": "INFO", "logger": "root", "message": "S3 storage enabled with bucket: qrverse and CDN: https://dev-cdn.qrvibe.com", "module": "storage", "function": "__init__", "line": 59}
{"timestamp": "2025-06-11T22:08:46.107803", "level": "INFO", "logger": "root", "message": "Storage initialized: environment=test, storage_dir=/Users/<USER>/aihub/qrvibe/storage, s3_enabled=True", "module": "storage", "function": "__init__", "line": 41}
{"timestamp": "2025-06-11T22:08:46.111119", "level": "INFO", "logger": "root", "message": "S3 storage enabled with bucket: qrverse and CDN: https://dev-cdn.qrvibe.com", "module": "storage", "function": "__init__", "line": 59}
{"timestamp": "2025-06-11T22:08:46.136107", "level": "INFO", "logger": "backend.middleware.security_middleware", "message": "Enhanced security middleware initialized with tiered rate limiting", "module": "security_middleware", "function": "__init__", "line": 87}
{"timestamp": "2025-06-11T22:08:46.136713", "level": "INFO", "logger": "backend.core.middleware.csrf", "message": "CSRF Protection Middleware initialized with exclude paths: ['/auth/jwt/login', '/auth/logout', '/auth/register', '/auth/2fa', '/auth/me', '/auth/verify-email', '/auth/reset-password', '/admin/login', '/admin/logout']", "module": "csrf", "function": "__init__", "line": 44}
{"timestamp": "2025-06-11T22:08:46.142080", "level": "INFO", "logger": "backend.services.token_blacklist", "message": "Token blacklist service initialized with Redis", "module": "token_blacklist", "function": "initialize", "line": 59}
{"timestamp": "2025-06-11T22:08:46.142173", "level": "INFO", "logger": "backend.main", "message": "Token blacklist service initialized", "module": "main", "function": "initialize_token_blacklist", "line": 98}
{"timestamp": "2025-06-11T22:08:46.142851", "level": "INFO", "logger": "backend.services.qr_preview_cache", "message": "QR preview cache initialized at /Users/<USER>/aihub/qrvibe/storage/cache/qr_preview_cache.db", "module": "qr_preview_cache", "function": "_init_db", "line": 56}
{"timestamp": "2025-06-11T22:08:46.143178", "level": "INFO", "logger": "backend.services.qr_preview_cache", "message": "Started QR preview cache cleanup task", "module": "qr_preview_cache", "function": "start_cleanup_task", "line": 62}
{"timestamp": "2025-06-11T22:08:46.143302", "level": "INFO", "logger": "backend.main", "message": "QR preview cache initialized and cleanup task started", "module": "main", "function": "init_qr_preview_cache", "line": 345}
{"timestamp": "2025-06-11T22:09:04.376753", "level": "INFO", "logger": "backend.services.qr_preview_cache", "message": "QR preview cache cleanup task stopped", "module": "qr_preview_cache", "function": "stop", "line": 257}
{"timestamp": "2025-06-11T22:09:04.376980", "level": "INFO", "logger": "backend.main", "message": "QR preview cache stopped", "module": "main", "function": "shutdown_qr_preview_cache", "line": 352}
{"timestamp": "2025-06-11T22:09:07.773793", "level": "INFO", "logger": "backend.main", "message": "=== QRVibe API Startup: Logging to file is enabled ===", "module": "main", "function": "<module>", "line": 37}
{"timestamp": "2025-06-11T22:09:07.773885", "level": "INFO", "logger": "backend.main", "message": "Applying critical patches before initialization", "module": "main", "function": "<module>", "line": 43}
{"timestamp": "2025-06-11T22:09:07.773946", "level": "INFO", "logger": "backend.patches", "message": "Applying patches (none required)", "module": "__init__", "function": "apply_patches", "line": 11}
{"timestamp": "2025-06-11T22:09:07.774158", "level": "INFO", "logger": "backend.core.middleware.cors", "message": "Setting up CORS middleware with origins: ['http://127.0.0.1:3000', 'https://localhost:3000', 'http://localhost:3000', 'https://127.0.0.1:3000']", "module": "cors", "function": "setup_cors", "line": 86}
{"timestamp": "2025-06-11T22:09:07.774218", "level": "INFO", "logger": "backend.core.middleware.cors", "message": "CORS middleware configured - preflight requests should be handled automatically", "module": "cors", "function": "setup_cors", "line": 98}
{"timestamp": "2025-06-11T22:09:07.775160", "level": "INFO", "logger": "backend.middleware.security_middleware", "message": "Added Enhanced Security Middleware with tiered rate limiting", "module": "security_middleware", "function": "add_security_middleware", "line": 176}
{"timestamp": "2025-06-11T22:09:07.775264", "level": "INFO", "logger": "backend.main", "message": "Mounting storage directory: /Users/<USER>/aihub/qrvibe/storage", "module": "main", "function": "<module>", "line": 107}
{"timestamp": "2025-06-11T22:09:07.775377", "level": "INFO", "logger": "backend.main", "message": "Storage directory absolute path: /Users/<USER>/aihub/qrvibe/storage", "module": "main", "function": "<module>", "line": 119}
{"timestamp": "2025-06-11T22:09:08.166639", "level": "INFO", "logger": "root", "message": "Storage initialized: environment=test, storage_dir=/Users/<USER>/aihub/qrvibe/storage, s3_enabled=True", "module": "storage", "function": "__init__", "line": 41}
{"timestamp": "2025-06-11T22:09:08.169564", "level": "INFO", "logger": "root", "message": "S3 storage enabled with bucket: qrverse and CDN: https://dev-cdn.qrvibe.com", "module": "storage", "function": "__init__", "line": 59}
{"timestamp": "2025-06-11T22:09:08.214841", "level": "INFO", "logger": "root", "message": "Storage initialized: environment=test, storage_dir=/Users/<USER>/aihub/qrvibe/storage, s3_enabled=True", "module": "storage", "function": "__init__", "line": 41}
{"timestamp": "2025-06-11T22:09:08.217955", "level": "INFO", "logger": "root", "message": "S3 storage enabled with bucket: qrverse and CDN: https://dev-cdn.qrvibe.com", "module": "storage", "function": "__init__", "line": 59}
{"timestamp": "2025-06-11T22:09:08.241784", "level": "INFO", "logger": "backend.middleware.security_middleware", "message": "Enhanced security middleware initialized with tiered rate limiting", "module": "security_middleware", "function": "__init__", "line": 87}
{"timestamp": "2025-06-11T22:09:08.241898", "level": "INFO", "logger": "backend.core.middleware.csrf", "message": "CSRF Protection Middleware initialized with exclude paths: ['/auth/jwt/login', '/auth/logout', '/auth/register', '/auth/2fa', '/auth/me', '/auth/verify-email', '/auth/reset-password', '/admin/login', '/admin/logout']", "module": "csrf", "function": "__init__", "line": 44}
{"timestamp": "2025-06-11T22:09:08.245764", "level": "INFO", "logger": "backend.services.token_blacklist", "message": "Token blacklist service initialized with Redis", "module": "token_blacklist", "function": "initialize", "line": 59}
{"timestamp": "2025-06-11T22:09:08.245841", "level": "INFO", "logger": "backend.main", "message": "Token blacklist service initialized", "module": "main", "function": "initialize_token_blacklist", "line": 98}
{"timestamp": "2025-06-11T22:09:08.246635", "level": "INFO", "logger": "backend.services.qr_preview_cache", "message": "QR preview cache initialized at /Users/<USER>/aihub/qrvibe/storage/cache/qr_preview_cache.db", "module": "qr_preview_cache", "function": "_init_db", "line": 56}
{"timestamp": "2025-06-11T22:09:08.246722", "level": "INFO", "logger": "backend.services.qr_preview_cache", "message": "Started QR preview cache cleanup task", "module": "qr_preview_cache", "function": "start_cleanup_task", "line": 62}
{"timestamp": "2025-06-11T22:09:08.246776", "level": "INFO", "logger": "backend.main", "message": "QR preview cache initialized and cleanup task started", "module": "main", "function": "init_qr_preview_cache", "line": 345}
{"timestamp": "2025-06-11T22:09:23.610253", "level": "INFO", "logger": "backend.services.qr_preview_cache", "message": "QR preview cache cleanup task stopped", "module": "qr_preview_cache", "function": "stop", "line": 257}
{"timestamp": "2025-06-11T22:09:23.610367", "level": "INFO", "logger": "backend.main", "message": "QR preview cache stopped", "module": "main", "function": "shutdown_qr_preview_cache", "line": 352}
{"timestamp": "2025-06-11T22:09:27.055866", "level": "INFO", "logger": "backend.main", "message": "=== QRVibe API Startup: Logging to file is enabled ===", "module": "main", "function": "<module>", "line": 37}
{"timestamp": "2025-06-11T22:09:27.055955", "level": "INFO", "logger": "backend.main", "message": "Applying critical patches before initialization", "module": "main", "function": "<module>", "line": 43}
{"timestamp": "2025-06-11T22:09:27.056017", "level": "INFO", "logger": "backend.patches", "message": "Applying patches (none required)", "module": "__init__", "function": "apply_patches", "line": 11}
{"timestamp": "2025-06-11T22:09:27.056240", "level": "INFO", "logger": "backend.core.middleware.cors", "message": "Setting up CORS middleware with origins: ['http://127.0.0.1:3000', 'http://localhost:3000', 'https://localhost:3000', 'https://127.0.0.1:3000']", "module": "cors", "function": "setup_cors", "line": 86}
{"timestamp": "2025-06-11T22:09:27.056305", "level": "INFO", "logger": "backend.core.middleware.cors", "message": "CORS middleware configured - preflight requests should be handled automatically", "module": "cors", "function": "setup_cors", "line": 98}
{"timestamp": "2025-06-11T22:09:27.059235", "level": "INFO", "logger": "backend.middleware.security_middleware", "message": "Added Enhanced Security Middleware with tiered rate limiting", "module": "security_middleware", "function": "add_security_middleware", "line": 176}
{"timestamp": "2025-06-11T22:09:27.059353", "level": "INFO", "logger": "backend.main", "message": "Mounting storage directory: /Users/<USER>/aihub/qrvibe/storage", "module": "main", "function": "<module>", "line": 107}
{"timestamp": "2025-06-11T22:09:27.059475", "level": "INFO", "logger": "backend.main", "message": "Storage directory absolute path: /Users/<USER>/aihub/qrvibe/storage", "module": "main", "function": "<module>", "line": 119}
{"timestamp": "2025-06-11T22:09:27.478590", "level": "INFO", "logger": "root", "message": "Storage initialized: environment=test, storage_dir=/Users/<USER>/aihub/qrvibe/storage, s3_enabled=True", "module": "storage", "function": "__init__", "line": 41}
{"timestamp": "2025-06-11T22:09:27.481517", "level": "INFO", "logger": "root", "message": "S3 storage enabled with bucket: qrverse and CDN: https://dev-cdn.qrvibe.com", "module": "storage", "function": "__init__", "line": 59}
{"timestamp": "2025-06-11T22:09:27.527587", "level": "INFO", "logger": "root", "message": "Storage initialized: environment=test, storage_dir=/Users/<USER>/aihub/qrvibe/storage, s3_enabled=True", "module": "storage", "function": "__init__", "line": 41}
{"timestamp": "2025-06-11T22:09:27.530761", "level": "INFO", "logger": "root", "message": "S3 storage enabled with bucket: qrverse and CDN: https://dev-cdn.qrvibe.com", "module": "storage", "function": "__init__", "line": 59}
{"timestamp": "2025-06-11T22:09:27.553444", "level": "INFO", "logger": "backend.middleware.security_middleware", "message": "Enhanced security middleware initialized with tiered rate limiting", "module": "security_middleware", "function": "__init__", "line": 87}
{"timestamp": "2025-06-11T22:09:27.553543", "level": "INFO", "logger": "backend.core.middleware.csrf", "message": "CSRF Protection Middleware initialized with exclude paths: ['/auth/jwt/login', '/auth/logout', '/auth/register', '/auth/2fa', '/auth/me', '/auth/verify-email', '/auth/reset-password', '/admin/login', '/admin/logout']", "module": "csrf", "function": "__init__", "line": 44}
{"timestamp": "2025-06-11T22:09:27.559144", "level": "INFO", "logger": "backend.services.token_blacklist", "message": "Token blacklist service initialized with Redis", "module": "token_blacklist", "function": "initialize", "line": 59}
{"timestamp": "2025-06-11T22:09:27.559210", "level": "INFO", "logger": "backend.main", "message": "Token blacklist service initialized", "module": "main", "function": "initialize_token_blacklist", "line": 98}
{"timestamp": "2025-06-11T22:09:27.559834", "level": "INFO", "logger": "backend.services.qr_preview_cache", "message": "QR preview cache initialized at /Users/<USER>/aihub/qrvibe/storage/cache/qr_preview_cache.db", "module": "qr_preview_cache", "function": "_init_db", "line": 56}
{"timestamp": "2025-06-11T22:09:27.559895", "level": "INFO", "logger": "backend.services.qr_preview_cache", "message": "Started QR preview cache cleanup task", "module": "qr_preview_cache", "function": "start_cleanup_task", "line": 62}
{"timestamp": "2025-06-11T22:09:27.559940", "level": "INFO", "logger": "backend.main", "message": "QR preview cache initialized and cleanup task started", "module": "main", "function": "init_qr_preview_cache", "line": 345}
{"timestamp": "2025-06-11T22:09:54.138647", "level": "INFO", "logger": "backend.services.qr_preview_cache", "message": "QR preview cache cleanup task stopped", "module": "qr_preview_cache", "function": "stop", "line": 257}
{"timestamp": "2025-06-11T22:09:54.141297", "level": "INFO", "logger": "backend.main", "message": "QR preview cache stopped", "module": "main", "function": "shutdown_qr_preview_cache", "line": 352}
{"timestamp": "2025-06-11T22:09:57.450752", "level": "INFO", "logger": "backend.main", "message": "=== QRVibe API Startup: Logging to file is enabled ===", "module": "main", "function": "<module>", "line": 37}
{"timestamp": "2025-06-11T22:09:57.450842", "level": "INFO", "logger": "backend.main", "message": "Applying critical patches before initialization", "module": "main", "function": "<module>", "line": 43}
{"timestamp": "2025-06-11T22:09:57.450900", "level": "INFO", "logger": "backend.patches", "message": "Applying patches (none required)", "module": "__init__", "function": "apply_patches", "line": 11}
{"timestamp": "2025-06-11T22:09:57.451121", "level": "INFO", "logger": "backend.core.middleware.cors", "message": "Setting up CORS middleware with origins: ['http://localhost:3000', 'https://localhost:3000', 'https://127.0.0.1:3000', 'http://127.0.0.1:3000']", "module": "cors", "function": "setup_cors", "line": 86}
{"timestamp": "2025-06-11T22:09:57.451302", "level": "INFO", "logger": "backend.core.middleware.cors", "message": "CORS middleware configured - preflight requests should be handled automatically", "module": "cors", "function": "setup_cors", "line": 98}
{"timestamp": "2025-06-11T22:09:57.452268", "level": "INFO", "logger": "backend.middleware.security_middleware", "message": "Added Enhanced Security Middleware with tiered rate limiting", "module": "security_middleware", "function": "add_security_middleware", "line": 176}
{"timestamp": "2025-06-11T22:09:57.452365", "level": "INFO", "logger": "backend.main", "message": "Mounting storage directory: /Users/<USER>/aihub/qrvibe/storage", "module": "main", "function": "<module>", "line": 107}
{"timestamp": "2025-06-11T22:09:57.452474", "level": "INFO", "logger": "backend.main", "message": "Storage directory absolute path: /Users/<USER>/aihub/qrvibe/storage", "module": "main", "function": "<module>", "line": 119}
{"timestamp": "2025-06-11T22:09:57.859504", "level": "INFO", "logger": "root", "message": "Storage initialized: environment=test, storage_dir=/Users/<USER>/aihub/qrvibe/storage, s3_enabled=True", "module": "storage", "function": "__init__", "line": 41}
{"timestamp": "2025-06-11T22:09:57.864350", "level": "INFO", "logger": "root", "message": "S3 storage enabled with bucket: qrverse and CDN: https://dev-cdn.qrvibe.com", "module": "storage", "function": "__init__", "line": 59}
{"timestamp": "2025-06-11T22:09:57.911280", "level": "INFO", "logger": "root", "message": "Storage initialized: environment=test, storage_dir=/Users/<USER>/aihub/qrvibe/storage, s3_enabled=True", "module": "storage", "function": "__init__", "line": 41}
{"timestamp": "2025-06-11T22:09:57.915016", "level": "INFO", "logger": "root", "message": "S3 storage enabled with bucket: qrverse and CDN: https://dev-cdn.qrvibe.com", "module": "storage", "function": "__init__", "line": 59}
{"timestamp": "2025-06-11T22:09:57.952361", "level": "INFO", "logger": "backend.middleware.security_middleware", "message": "Enhanced security middleware initialized with tiered rate limiting", "module": "security_middleware", "function": "__init__", "line": 87}
{"timestamp": "2025-06-11T22:09:57.952471", "level": "INFO", "logger": "backend.core.middleware.csrf", "message": "CSRF Protection Middleware initialized with exclude paths: ['/auth/jwt/login', '/auth/logout', '/auth/register', '/auth/2fa', '/auth/me', '/auth/verify-email', '/auth/reset-password', '/admin/login', '/admin/logout']", "module": "csrf", "function": "__init__", "line": 44}
{"timestamp": "2025-06-11T22:09:57.966456", "level": "INFO", "logger": "backend.services.token_blacklist", "message": "Token blacklist service initialized with Redis", "module": "token_blacklist", "function": "initialize", "line": 59}
{"timestamp": "2025-06-11T22:09:57.970054", "level": "INFO", "logger": "backend.main", "message": "Token blacklist service initialized", "module": "main", "function": "initialize_token_blacklist", "line": 98}
{"timestamp": "2025-06-11T22:09:57.970803", "level": "INFO", "logger": "backend.services.qr_preview_cache", "message": "QR preview cache initialized at /Users/<USER>/aihub/qrvibe/storage/cache/qr_preview_cache.db", "module": "qr_preview_cache", "function": "_init_db", "line": 56}
{"timestamp": "2025-06-11T22:09:57.970892", "level": "INFO", "logger": "backend.services.qr_preview_cache", "message": "Started QR preview cache cleanup task", "module": "qr_preview_cache", "function": "start_cleanup_task", "line": 62}
{"timestamp": "2025-06-11T22:09:57.970963", "level": "INFO", "logger": "backend.main", "message": "QR preview cache initialized and cleanup task started", "module": "main", "function": "init_qr_preview_cache", "line": 345}
{"timestamp": "2025-06-11T22:10:22.431825", "level": "INFO", "logger": "backend.services.qr_preview_cache", "message": "QR preview cache cleanup task stopped", "module": "qr_preview_cache", "function": "stop", "line": 257}
{"timestamp": "2025-06-11T22:10:22.431928", "level": "INFO", "logger": "backend.main", "message": "QR preview cache stopped", "module": "main", "function": "shutdown_qr_preview_cache", "line": 352}
{"timestamp": "2025-06-11T22:10:25.761852", "level": "INFO", "logger": "backend.main", "message": "=== QRVibe API Startup: Logging to file is enabled ===", "module": "main", "function": "<module>", "line": 37}
{"timestamp": "2025-06-11T22:10:25.761946", "level": "INFO", "logger": "backend.main", "message": "Applying critical patches before initialization", "module": "main", "function": "<module>", "line": 43}
{"timestamp": "2025-06-11T22:10:25.762004", "level": "INFO", "logger": "backend.patches", "message": "Applying patches (none required)", "module": "__init__", "function": "apply_patches", "line": 11}
{"timestamp": "2025-06-11T22:10:25.762211", "level": "INFO", "logger": "backend.core.middleware.cors", "message": "Setting up CORS middleware with origins: ['https://127.0.0.1:3000', 'http://localhost:3000', 'https://localhost:3000', 'http://127.0.0.1:3000']", "module": "cors", "function": "setup_cors", "line": 86}
{"timestamp": "2025-06-11T22:10:25.762266", "level": "INFO", "logger": "backend.core.middleware.cors", "message": "CORS middleware configured - preflight requests should be handled automatically", "module": "cors", "function": "setup_cors", "line": 98}
{"timestamp": "2025-06-11T22:10:25.763243", "level": "INFO", "logger": "backend.middleware.security_middleware", "message": "Added Enhanced Security Middleware with tiered rate limiting", "module": "security_middleware", "function": "add_security_middleware", "line": 176}
{"timestamp": "2025-06-11T22:10:25.763332", "level": "INFO", "logger": "backend.main", "message": "Mounting storage directory: /Users/<USER>/aihub/qrvibe/storage", "module": "main", "function": "<module>", "line": 107}
{"timestamp": "2025-06-11T22:10:25.763442", "level": "INFO", "logger": "backend.main", "message": "Storage directory absolute path: /Users/<USER>/aihub/qrvibe/storage", "module": "main", "function": "<module>", "line": 119}
{"timestamp": "2025-06-11T22:10:26.238043", "level": "INFO", "logger": "root", "message": "Storage initialized: environment=test, storage_dir=/Users/<USER>/aihub/qrvibe/storage, s3_enabled=True", "module": "storage", "function": "__init__", "line": 41}
{"timestamp": "2025-06-11T22:10:26.241069", "level": "INFO", "logger": "root", "message": "S3 storage enabled with bucket: qrverse and CDN: https://dev-cdn.qrvibe.com", "module": "storage", "function": "__init__", "line": 59}
{"timestamp": "2025-06-11T22:10:26.287827", "level": "INFO", "logger": "root", "message": "Storage initialized: environment=test, storage_dir=/Users/<USER>/aihub/qrvibe/storage, s3_enabled=True", "module": "storage", "function": "__init__", "line": 41}
{"timestamp": "2025-06-11T22:10:26.291068", "level": "INFO", "logger": "root", "message": "S3 storage enabled with bucket: qrverse and CDN: https://dev-cdn.qrvibe.com", "module": "storage", "function": "__init__", "line": 59}
{"timestamp": "2025-06-11T22:10:26.314906", "level": "INFO", "logger": "backend.middleware.security_middleware", "message": "Enhanced security middleware initialized with tiered rate limiting", "module": "security_middleware", "function": "__init__", "line": 87}
{"timestamp": "2025-06-11T22:10:26.315059", "level": "INFO", "logger": "backend.core.middleware.csrf", "message": "CSRF Protection Middleware initialized with exclude paths: ['/auth/jwt/login', '/auth/logout', '/auth/register', '/auth/2fa', '/auth/me', '/auth/verify-email', '/auth/reset-password', '/admin/login', '/admin/logout']", "module": "csrf", "function": "__init__", "line": 44}
{"timestamp": "2025-06-11T22:10:26.318430", "level": "INFO", "logger": "backend.services.token_blacklist", "message": "Token blacklist service initialized with Redis", "module": "token_blacklist", "function": "initialize", "line": 59}
{"timestamp": "2025-06-11T22:10:26.318520", "level": "INFO", "logger": "backend.main", "message": "Token blacklist service initialized", "module": "main", "function": "initialize_token_blacklist", "line": 98}
{"timestamp": "2025-06-11T22:10:26.319204", "level": "INFO", "logger": "backend.services.qr_preview_cache", "message": "QR preview cache initialized at /Users/<USER>/aihub/qrvibe/storage/cache/qr_preview_cache.db", "module": "qr_preview_cache", "function": "_init_db", "line": 56}
{"timestamp": "2025-06-11T22:10:26.319275", "level": "INFO", "logger": "backend.services.qr_preview_cache", "message": "Started QR preview cache cleanup task", "module": "qr_preview_cache", "function": "start_cleanup_task", "line": 62}
{"timestamp": "2025-06-11T22:10:26.319332", "level": "INFO", "logger": "backend.main", "message": "QR preview cache initialized and cleanup task started", "module": "main", "function": "init_qr_preview_cache", "line": 345}
{"timestamp": "2025-06-11T22:10:59.959064", "level": "WARNING", "logger": "backend.core.security.jwt_keys", "message": "Unknown JWT kid requested: 031a5f05-a8d4-404e-87c7-d79789ffffc2", "module": "jwt_keys", "function": "get_verification_key", "line": 105}
{"timestamp": "2025-06-11T22:10:59.959171", "level": "WARNING", "logger": "backend.core.security.jwt_keys", "message": "Verification failed with key ID 031a5f05-a8d4-404e-87c7-d79789ffffc2: Unknown kid: 031a5f05-a8d4-404e-87c7-d79789ffffc2", "module": "jwt_keys", "function": "decode_token", "line": 203}
{"timestamp": "2025-06-11T22:10:59.981453", "level": "WARNING", "logger": "backend.core.security.jwt_keys", "message": "Verification failed with current key: Signature verification failed.", "module": "jwt_keys", "function": "decode_token", "line": 216}
{"timestamp": "2025-06-11T22:10:59.981894", "level": "ERROR", "logger": "backend.core.security.jwt_keys", "message": "Token verification failed with all keys. Errors: Key ad4fbdf4-9e77-4c60-a6b8-e900a52a2a12: Signature verification failed.", "module": "jwt_keys", "function": "decode_token", "line": 239}
{"timestamp": "2025-06-11T22:10:59.982018", "level": "ERROR", "logger": "backend.core.security.jwt_keys", "message": "Token decode error: Token verification failed with all keys. Errors: Key ad4fbdf4-9e77-4c60-a6b8-e900a52a2a12: Signature verification failed.", "module": "jwt_keys", "function": "decode_token", "line": 244}
{"timestamp": "2025-06-11T22:10:59.982389", "level": "ERROR", "logger": "backend.services.auth", "message": "JWT token validation failed: Failed to decode token: Token verification failed with all keys. Errors: Key ad4fbdf4-9e77-4c60-a6b8-e900a52a2a12: Signature verification failed.", "module": "auth", "function": "authenticate_token", "line": 686}
{"timestamp": "2025-06-11T22:10:59.991582", "level": "WARNING", "logger": "backend.middleware.subscription_limits", "message": "Error authenticating token in subscription middleware: name 'ValidationError' is not defined", "module": "subscription_limits", "function": "_get_current_user", "line": 308}
{"timestamp": "2025-06-11T22:10:59.994015", "level": "WARNING", "logger": "backend.core.security.jwt_keys", "message": "Unknown JWT kid requested: 031a5f05-a8d4-404e-87c7-d79789ffffc2", "module": "jwt_keys", "function": "get_verification_key", "line": 105}
{"timestamp": "2025-06-11T22:10:59.994085", "level": "WARNING", "logger": "backend.core.security.jwt_keys", "message": "Verification failed with key ID 031a5f05-a8d4-404e-87c7-d79789ffffc2: Unknown kid: 031a5f05-a8d4-404e-87c7-d79789ffffc2", "module": "jwt_keys", "function": "decode_token", "line": 203}
{"timestamp": "2025-06-11T22:10:59.994277", "level": "WARNING", "logger": "backend.core.security.jwt_keys", "message": "Verification failed with current key: Signature verification failed.", "module": "jwt_keys", "function": "decode_token", "line": 216}
{"timestamp": "2025-06-11T22:10:59.994446", "level": "ERROR", "logger": "backend.core.security.jwt_keys", "message": "Token verification failed with all keys. Errors: Key ad4fbdf4-9e77-4c60-a6b8-e900a52a2a12: Signature verification failed.", "module": "jwt_keys", "function": "decode_token", "line": 239}
{"timestamp": "2025-06-11T22:10:59.994520", "level": "ERROR", "logger": "backend.core.security.jwt_keys", "message": "Token decode error: Token verification failed with all keys. Errors: Key ad4fbdf4-9e77-4c60-a6b8-e900a52a2a12: Signature verification failed.", "module": "jwt_keys", "function": "decode_token", "line": 244}
{"timestamp": "2025-06-11T22:10:59.994613", "level": "ERROR", "logger": "backend.services.auth", "message": "JWT token validation failed: Failed to decode token: Token verification failed with all keys. Errors: Key ad4fbdf4-9e77-4c60-a6b8-e900a52a2a12: Signature verification failed.", "module": "auth", "function": "authenticate_token", "line": 686}
{"timestamp": "2025-06-11T22:10:59.995439", "level": "WARNING", "logger": "backend.middleware.subscription_limits", "message": "Auth error in subscription middleware: name 'ValidationError' is not defined", "module": "subscription_limits", "function": "dispatch", "line": 234}
{"timestamp": "2025-06-11T22:10:59.995916", "level": "WARNING", "logger": "backend.core.security.jwt_keys", "message": "Unknown JWT kid requested: 031a5f05-a8d4-404e-87c7-d79789ffffc2", "module": "jwt_keys", "function": "get_verification_key", "line": 105}
{"timestamp": "2025-06-11T22:10:59.995969", "level": "WARNING", "logger": "backend.core.security.jwt_keys", "message": "Verification failed with key ID 031a5f05-a8d4-404e-87c7-d79789ffffc2: Unknown kid: 031a5f05-a8d4-404e-87c7-d79789ffffc2", "module": "jwt_keys", "function": "decode_token", "line": 203}
{"timestamp": "2025-06-11T22:10:59.996154", "level": "WARNING", "logger": "backend.core.security.jwt_keys", "message": "Verification failed with current key: Signature verification failed.", "module": "jwt_keys", "function": "decode_token", "line": 216}
{"timestamp": "2025-06-11T22:10:59.996313", "level": "ERROR", "logger": "backend.core.security.jwt_keys", "message": "Token verification failed with all keys. Errors: Key ad4fbdf4-9e77-4c60-a6b8-e900a52a2a12: Signature verification failed.", "module": "jwt_keys", "function": "decode_token", "line": 239}
{"timestamp": "2025-06-11T22:10:59.996381", "level": "ERROR", "logger": "backend.core.security.jwt_keys", "message": "Token decode error: Token verification failed with all keys. Errors: Key ad4fbdf4-9e77-4c60-a6b8-e900a52a2a12: Signature verification failed.", "module": "jwt_keys", "function": "decode_token", "line": 244}
{"timestamp": "2025-06-11T22:10:59.996464", "level": "ERROR", "logger": "backend.services.auth", "message": "JWT token validation failed: Failed to decode token: Token verification failed with all keys. Errors: Key ad4fbdf4-9e77-4c60-a6b8-e900a52a2a12: Signature verification failed.", "module": "auth", "function": "authenticate_token", "line": 686}
{"timestamp": "2025-06-11T22:11:37.426806", "level": "WARNING", "logger": "backend.core.security.jwt_keys", "message": "Unknown JWT kid requested: 031a5f05-a8d4-404e-87c7-d79789ffffc2", "module": "jwt_keys", "function": "get_verification_key", "line": 105}
{"timestamp": "2025-06-11T22:11:37.427113", "level": "WARNING", "logger": "backend.core.security.jwt_keys", "message": "Verification failed with key ID 031a5f05-a8d4-404e-87c7-d79789ffffc2: Unknown kid: 031a5f05-a8d4-404e-87c7-d79789ffffc2", "module": "jwt_keys", "function": "decode_token", "line": 203}
{"timestamp": "2025-06-11T22:11:37.431171", "level": "WARNING", "logger": "backend.core.security.jwt_keys", "message": "Verification failed with current key: Signature verification failed.", "module": "jwt_keys", "function": "decode_token", "line": 216}
{"timestamp": "2025-06-11T22:11:37.431654", "level": "ERROR", "logger": "backend.core.security.jwt_keys", "message": "Token verification failed with all keys. Errors: Key ad4fbdf4-9e77-4c60-a6b8-e900a52a2a12: Signature verification failed.", "module": "jwt_keys", "function": "decode_token", "line": 239}
{"timestamp": "2025-06-11T22:11:37.431749", "level": "ERROR", "logger": "backend.core.security.jwt_keys", "message": "Token decode error: Token verification failed with all keys. Errors: Key ad4fbdf4-9e77-4c60-a6b8-e900a52a2a12: Signature verification failed.", "module": "jwt_keys", "function": "decode_token", "line": 244}
{"timestamp": "2025-06-11T22:11:37.450690", "level": "ERROR", "logger": "backend.services.auth", "message": "JWT token validation failed: Failed to decode token: Token verification failed with all keys. Errors: Key ad4fbdf4-9e77-4c60-a6b8-e900a52a2a12: Signature verification failed.", "module": "auth", "function": "authenticate_token", "line": 686}
{"timestamp": "2025-06-11T22:11:37.451353", "level": "WARNING", "logger": "backend.middleware.subscription_limits", "message": "Error authenticating token in subscription middleware: name 'ValidationError' is not defined", "module": "subscription_limits", "function": "_get_current_user", "line": 308}
{"timestamp": "2025-06-11T22:11:37.453321", "level": "WARNING", "logger": "backend.core.security.jwt_keys", "message": "Unknown JWT kid requested: 031a5f05-a8d4-404e-87c7-d79789ffffc2", "module": "jwt_keys", "function": "get_verification_key", "line": 105}
{"timestamp": "2025-06-11T22:11:37.453381", "level": "WARNING", "logger": "backend.core.security.jwt_keys", "message": "Verification failed with key ID 031a5f05-a8d4-404e-87c7-d79789ffffc2: Unknown kid: 031a5f05-a8d4-404e-87c7-d79789ffffc2", "module": "jwt_keys", "function": "decode_token", "line": 203}
{"timestamp": "2025-06-11T22:11:37.453559", "level": "WARNING", "logger": "backend.core.security.jwt_keys", "message": "Verification failed with current key: Signature verification failed.", "module": "jwt_keys", "function": "decode_token", "line": 216}
{"timestamp": "2025-06-11T22:11:37.453703", "level": "ERROR", "logger": "backend.core.security.jwt_keys", "message": "Token verification failed with all keys. Errors: Key ad4fbdf4-9e77-4c60-a6b8-e900a52a2a12: Signature verification failed.", "module": "jwt_keys", "function": "decode_token", "line": 239}
{"timestamp": "2025-06-11T22:11:37.460371", "level": "ERROR", "logger": "backend.core.security.jwt_keys", "message": "Token decode error: Token verification failed with all keys. Errors: Key ad4fbdf4-9e77-4c60-a6b8-e900a52a2a12: Signature verification failed.", "module": "jwt_keys", "function": "decode_token", "line": 244}
{"timestamp": "2025-06-11T22:11:37.460481", "level": "ERROR", "logger": "backend.services.auth", "message": "JWT token validation failed: Failed to decode token: Token verification failed with all keys. Errors: Key ad4fbdf4-9e77-4c60-a6b8-e900a52a2a12: Signature verification failed.", "module": "auth", "function": "authenticate_token", "line": 686}
{"timestamp": "2025-06-11T22:11:37.461049", "level": "WARNING", "logger": "backend.middleware.subscription_limits", "message": "Auth error in subscription middleware: name 'ValidationError' is not defined", "module": "subscription_limits", "function": "dispatch", "line": 234}
{"timestamp": "2025-06-11T22:11:37.462054", "level": "WARNING", "logger": "backend.core.security.jwt_keys", "message": "Unknown JWT kid requested: 031a5f05-a8d4-404e-87c7-d79789ffffc2", "module": "jwt_keys", "function": "get_verification_key", "line": 105}
{"timestamp": "2025-06-11T22:11:37.463112", "level": "WARNING", "logger": "backend.core.security.jwt_keys", "message": "Verification failed with key ID 031a5f05-a8d4-404e-87c7-d79789ffffc2: Unknown kid: 031a5f05-a8d4-404e-87c7-d79789ffffc2", "module": "jwt_keys", "function": "decode_token", "line": 203}
{"timestamp": "2025-06-11T22:11:37.463290", "level": "WARNING", "logger": "backend.core.security.jwt_keys", "message": "Verification failed with current key: Signature verification failed.", "module": "jwt_keys", "function": "decode_token", "line": 216}
{"timestamp": "2025-06-11T22:11:37.469144", "level": "ERROR", "logger": "backend.core.security.jwt_keys", "message": "Token verification failed with all keys. Errors: Key ad4fbdf4-9e77-4c60-a6b8-e900a52a2a12: Signature verification failed.", "module": "jwt_keys", "function": "decode_token", "line": 239}
{"timestamp": "2025-06-11T22:11:37.469238", "level": "ERROR", "logger": "backend.core.security.jwt_keys", "message": "Token decode error: Token verification failed with all keys. Errors: Key ad4fbdf4-9e77-4c60-a6b8-e900a52a2a12: Signature verification failed.", "module": "jwt_keys", "function": "decode_token", "line": 244}
{"timestamp": "2025-06-11T22:11:37.469312", "level": "ERROR", "logger": "backend.services.auth", "message": "JWT token validation failed: Failed to decode token: Token verification failed with all keys. Errors: Key ad4fbdf4-9e77-4c60-a6b8-e900a52a2a12: Signature verification failed.", "module": "auth", "function": "authenticate_token", "line": 686}
{"timestamp": "2025-06-11T22:12:22.048635", "level": "WARNING", "logger": "backend.core.security.jwt_keys", "message": "Unknown JWT kid requested: 031a5f05-a8d4-404e-87c7-d79789ffffc2", "module": "jwt_keys", "function": "get_verification_key", "line": 105}
{"timestamp": "2025-06-11T22:12:22.054214", "level": "WARNING", "logger": "backend.core.security.jwt_keys", "message": "Verification failed with key ID 031a5f05-a8d4-404e-87c7-d79789ffffc2: Unknown kid: 031a5f05-a8d4-404e-87c7-d79789ffffc2", "module": "jwt_keys", "function": "decode_token", "line": 203}
{"timestamp": "2025-06-11T22:12:22.057295", "level": "WARNING", "logger": "backend.core.security.jwt_keys", "message": "Verification failed with current key: Signature verification failed.", "module": "jwt_keys", "function": "decode_token", "line": 216}
{"timestamp": "2025-06-11T22:12:22.061363", "level": "ERROR", "logger": "backend.core.security.jwt_keys", "message": "Token verification failed with all keys. Errors: Key ad4fbdf4-9e77-4c60-a6b8-e900a52a2a12: Signature verification failed.", "module": "jwt_keys", "function": "decode_token", "line": 239}
{"timestamp": "2025-06-11T22:12:22.061502", "level": "ERROR", "logger": "backend.core.security.jwt_keys", "message": "Token decode error: Token verification failed with all keys. Errors: Key ad4fbdf4-9e77-4c60-a6b8-e900a52a2a12: Signature verification failed.", "module": "jwt_keys", "function": "decode_token", "line": 244}
{"timestamp": "2025-06-11T22:12:22.061628", "level": "ERROR", "logger": "backend.services.auth", "message": "JWT token validation failed: Failed to decode token: Token verification failed with all keys. Errors: Key ad4fbdf4-9e77-4c60-a6b8-e900a52a2a12: Signature verification failed.", "module": "auth", "function": "authenticate_token", "line": 686}
{"timestamp": "2025-06-11T22:12:22.067114", "level": "WARNING", "logger": "backend.core.security.jwt_keys", "message": "Unknown JWT kid requested: 031a5f05-a8d4-404e-87c7-d79789ffffc2", "module": "jwt_keys", "function": "get_verification_key", "line": 105}
{"timestamp": "2025-06-11T22:12:22.067182", "level": "WARNING", "logger": "backend.core.security.jwt_keys", "message": "Verification failed with key ID 031a5f05-a8d4-404e-87c7-d79789ffffc2: Unknown kid: 031a5f05-a8d4-404e-87c7-d79789ffffc2", "module": "jwt_keys", "function": "decode_token", "line": 203}
{"timestamp": "2025-06-11T22:12:22.067615", "level": "WARNING", "logger": "backend.core.security.jwt_keys", "message": "Verification failed with current key: Signature verification failed.", "module": "jwt_keys", "function": "decode_token", "line": 216}
{"timestamp": "2025-06-11T22:12:22.067768", "level": "ERROR", "logger": "backend.core.security.jwt_keys", "message": "Token verification failed with all keys. Errors: Key ad4fbdf4-9e77-4c60-a6b8-e900a52a2a12: Signature verification failed.", "module": "jwt_keys", "function": "decode_token", "line": 239}
{"timestamp": "2025-06-11T22:12:22.068152", "level": "ERROR", "logger": "backend.core.security.jwt_keys", "message": "Token decode error: Token verification failed with all keys. Errors: Key ad4fbdf4-9e77-4c60-a6b8-e900a52a2a12: Signature verification failed.", "module": "jwt_keys", "function": "decode_token", "line": 244}
{"timestamp": "2025-06-11T22:12:22.068317", "level": "ERROR", "logger": "backend.services.auth", "message": "JWT token validation failed: Failed to decode token: Token verification failed with all keys. Errors: Key ad4fbdf4-9e77-4c60-a6b8-e900a52a2a12: Signature verification failed.", "module": "auth", "function": "authenticate_token", "line": 686}
{"timestamp": "2025-06-11T22:12:22.082593", "level": "WARNING", "logger": "backend.core.security.jwt_keys", "message": "Unknown JWT kid requested: 031a5f05-a8d4-404e-87c7-d79789ffffc2", "module": "jwt_keys", "function": "get_verification_key", "line": 105}
{"timestamp": "2025-06-11T22:12:22.082662", "level": "WARNING", "logger": "backend.core.security.jwt_keys", "message": "Verification failed with key ID 031a5f05-a8d4-404e-87c7-d79789ffffc2: Unknown kid: 031a5f05-a8d4-404e-87c7-d79789ffffc2", "module": "jwt_keys", "function": "decode_token", "line": 203}
{"timestamp": "2025-06-11T22:12:22.082931", "level": "WARNING", "logger": "backend.core.security.jwt_keys", "message": "Verification failed with current key: Signature verification failed.", "module": "jwt_keys", "function": "decode_token", "line": 216}
{"timestamp": "2025-06-11T22:12:22.083683", "level": "ERROR", "logger": "backend.core.security.jwt_keys", "message": "Token verification failed with all keys. Errors: Key ad4fbdf4-9e77-4c60-a6b8-e900a52a2a12: Signature verification failed.", "module": "jwt_keys", "function": "decode_token", "line": 239}
{"timestamp": "2025-06-11T22:12:22.083782", "level": "ERROR", "logger": "backend.core.security.jwt_keys", "message": "Token decode error: Token verification failed with all keys. Errors: Key ad4fbdf4-9e77-4c60-a6b8-e900a52a2a12: Signature verification failed.", "module": "jwt_keys", "function": "decode_token", "line": 244}
{"timestamp": "2025-06-11T22:12:22.083894", "level": "ERROR", "logger": "backend.auth", "message": "Token validation error: Failed to decode token: Token verification failed with all keys. Errors: Key ad4fbdf4-9e77-4c60-a6b8-e900a52a2a12: Signature verification failed.", "module": "__init__", "function": "get_current_user", "line": 193}
{"timestamp": "2025-06-11T22:12:22.084731", "level": "WARNING", "logger": "backend.middleware.subscription_limits", "message": "Error authenticating token in subscription middleware: name 'ValidationError' is not defined", "module": "subscription_limits", "function": "_get_current_user", "line": 308}
{"timestamp": "2025-06-11T22:12:22.085179", "level": "WARNING", "logger": "backend.middleware.subscription_limits", "message": "Error authenticating token in subscription middleware: name 'ValidationError' is not defined", "module": "subscription_limits", "function": "_get_current_user", "line": 308}
{"timestamp": "2025-06-11T22:12:22.086033", "level": "WARNING", "logger": "backend.core.security.jwt_keys", "message": "Unknown JWT kid requested: 031a5f05-a8d4-404e-87c7-d79789ffffc2", "module": "jwt_keys", "function": "get_verification_key", "line": 105}
{"timestamp": "2025-06-11T22:12:22.086148", "level": "WARNING", "logger": "backend.core.security.jwt_keys", "message": "Verification failed with key ID 031a5f05-a8d4-404e-87c7-d79789ffffc2: Unknown kid: 031a5f05-a8d4-404e-87c7-d79789ffffc2", "module": "jwt_keys", "function": "decode_token", "line": 203}
{"timestamp": "2025-06-11T22:12:22.090704", "level": "WARNING", "logger": "backend.core.security.jwt_keys", "message": "Verification failed with current key: Signature verification failed.", "module": "jwt_keys", "function": "decode_token", "line": 216}
{"timestamp": "2025-06-11T22:12:22.097063", "level": "ERROR", "logger": "backend.core.security.jwt_keys", "message": "Token verification failed with all keys. Errors: Key ad4fbdf4-9e77-4c60-a6b8-e900a52a2a12: Signature verification failed.", "module": "jwt_keys", "function": "decode_token", "line": 239}
{"timestamp": "2025-06-11T22:12:22.097154", "level": "ERROR", "logger": "backend.core.security.jwt_keys", "message": "Token decode error: Token verification failed with all keys. Errors: Key ad4fbdf4-9e77-4c60-a6b8-e900a52a2a12: Signature verification failed.", "module": "jwt_keys", "function": "decode_token", "line": 244}
{"timestamp": "2025-06-11T22:12:22.097243", "level": "ERROR", "logger": "backend.auth", "message": "Token validation error: Failed to decode token: Token verification failed with all keys. Errors: Key ad4fbdf4-9e77-4c60-a6b8-e900a52a2a12: Signature verification failed.", "module": "__init__", "function": "get_current_user", "line": 193}
{"timestamp": "2025-06-11T22:12:22.119522", "level": "WARNING", "logger": "backend.core.security.jwt_keys", "message": "Unknown JWT kid requested: 031a5f05-a8d4-404e-87c7-d79789ffffc2", "module": "jwt_keys", "function": "get_verification_key", "line": 105}
{"timestamp": "2025-06-11T22:12:22.119584", "level": "WARNING", "logger": "backend.core.security.jwt_keys", "message": "Verification failed with key ID 031a5f05-a8d4-404e-87c7-d79789ffffc2: Unknown kid: 031a5f05-a8d4-404e-87c7-d79789ffffc2", "module": "jwt_keys", "function": "decode_token", "line": 203}
{"timestamp": "2025-06-11T22:12:22.122730", "level": "WARNING", "logger": "backend.core.security.jwt_keys", "message": "Verification failed with current key: Signature verification failed.", "module": "jwt_keys", "function": "decode_token", "line": 216}
{"timestamp": "2025-06-11T22:12:22.122892", "level": "ERROR", "logger": "backend.core.security.jwt_keys", "message": "Token verification failed with all keys. Errors: Key ad4fbdf4-9e77-4c60-a6b8-e900a52a2a12: Signature verification failed.", "module": "jwt_keys", "function": "decode_token", "line": 239}
{"timestamp": "2025-06-11T22:12:22.128464", "level": "ERROR", "logger": "backend.core.security.jwt_keys", "message": "Token decode error: Token verification failed with all keys. Errors: Key ad4fbdf4-9e77-4c60-a6b8-e900a52a2a12: Signature verification failed.", "module": "jwt_keys", "function": "decode_token", "line": 244}
{"timestamp": "2025-06-11T22:12:22.128603", "level": "ERROR", "logger": "backend.auth", "message": "Token validation error: Failed to decode token: Token verification failed with all keys. Errors: Key ad4fbdf4-9e77-4c60-a6b8-e900a52a2a12: Signature verification failed.", "module": "__init__", "function": "get_current_user", "line": 193}
{"timestamp": "2025-06-11T22:12:30.496787", "level": "INFO", "logger": "backend.services.enhanced_rate_limit", "message": "Auth event for anonymous: login_attempt (failure)", "module": "enhanced_rate_limit", "function": "track_auth_event", "line": 182}
{"timestamp": "2025-06-11T22:12:33.435417", "level": "INFO", "logger": "backend.services.auth", "message": "Authentication successful for user: <EMAIL>", "module": "auth", "function": "authenticate_user", "line": 266}
{"timestamp": "2025-06-11T22:12:33.436613", "level": "INFO", "logger": "backend.routes.auth", "message": "User logged in successfully: <EMAIL>", "module": "auth", "function": "login", "line": 218}
{"timestamp": "2025-06-11T22:12:33.474624", "level": "INFO", "logger": "backend.services.enhanced_rate_limit", "message": "Auth event for user e4ae2d8f-b32f-52d7-99cd-8a33b19652c7: login_success (success)", "module": "enhanced_rate_limit", "function": "track_auth_event", "line": 182}
{"timestamp": "2025-06-11T22:12:34.109802", "level": "INFO", "logger": "backend.services.adaptive_session", "message": "Adaptive session for user e4ae2d8f-b32f-52d7-99cd-8a33b19652c7: risk=low, duration=86400s", "module": "adaptive_session", "function": "get_session_expiry", "line": 161}
{"timestamp": "2025-06-11T22:12:34.109944", "level": "INFO", "logger": "backend.routes.auth", "message": "Adaptive session <NAME_EMAIL>: duration=86400s", "module": "auth", "function": "login", "line": 279}
{"timestamp": "2025-06-11T22:12:34.110012", "level": "INFO", "logger": "backend.routes.auth", "message": "Bearer token authentication issued <NAME_EMAIL>", "module": "auth", "function": "login", "line": 284}
{"timestamp": "2025-06-11T22:12:54.475712", "level": "INFO", "logger": "root", "message": "Getting feature matrix for user e4ae2d8f-b32f-52d7-99cd-8a33b19652c7 (email: <EMAIL>)", "module": "subscription", "function": "get_user_feature_matrix", "line": 142}
{"timestamp": "2025-06-11T22:12:54.495994", "level": "INFO", "logger": "root", "message": "Found subscription for user e4ae2d8f-b32f-52d7-99cd-8a33b19652c7: plan=enterprise, status=active", "module": "subscription", "function": "get_user_feature_matrix", "line": 152}
{"timestamp": "2025-06-11T22:12:54.504232", "level": "INFO", "logger": "root", "message": "Found 0 team addons for user e4ae2d8f-b32f-52d7-99cd-8a33b19652c7", "module": "subscription", "function": "get_user_feature_matrix", "line": 181}
{"timestamp": "2025-06-11T22:12:54.504486", "level": "INFO", "logger": "root", "message": "Retrieved base features for plan enterprise", "module": "subscription", "function": "get_user_feature_matrix", "line": 191}
{"timestamp": "2025-06-11T22:12:54.535885", "level": "INFO", "logger": "root", "message": "Successfully generated feature matrix for user e4ae2d8f-b32f-52d7-99cd-8a33b19652c7", "module": "subscription", "function": "get_user_feature_matrix", "line": 284}
{"timestamp": "2025-06-11T23:30:53.417406", "level": "WARNING", "logger": "backend.core.middleware.csrf", "message": "CSRF token missing for POST /qr/preview/svg - Header present: False, Cookie present: False", "module": "csrf", "function": "dispatch", "line": 66}
{"timestamp": "2025-06-11T23:30:53.418417", "level": "WARNING", "logger": "backend.core.middleware.csrf", "message": "CSRF validation failed for POST /qr/preview/svg - IP: 127.0.0.1", "module": "csrf", "function": "dispatch", "line": 75}
{"timestamp": "2025-06-11T23:31:10.430240", "level": "INFO", "logger": "backend.services.enhanced_rate_limit", "message": "Auth event for anonymous: login_attempt (failure)", "module": "enhanced_rate_limit", "function": "track_auth_event", "line": 182}
{"timestamp": "2025-06-11T23:31:10.632578", "level": "INFO", "logger": "backend.services.auth", "message": "Authentication successful for user: <EMAIL>", "module": "auth", "function": "authenticate_user", "line": 266}
{"timestamp": "2025-06-11T23:31:10.638185", "level": "INFO", "logger": "backend.routes.auth", "message": "User logged in successfully: <EMAIL>", "module": "auth", "function": "login", "line": 218}
{"timestamp": "2025-06-11T23:31:10.639078", "level": "INFO", "logger": "backend.services.enhanced_rate_limit", "message": "Auth event for user e4ae2d8f-b32f-52d7-99cd-8a33b19652c7: login_success (success)", "module": "enhanced_rate_limit", "function": "track_auth_event", "line": 182}
{"timestamp": "2025-06-11T23:31:10.712563", "level": "INFO", "logger": "backend.services.adaptive_session", "message": "Adaptive session for user e4ae2d8f-b32f-52d7-99cd-8a33b19652c7: risk=low, duration=86400s", "module": "adaptive_session", "function": "get_session_expiry", "line": 161}
{"timestamp": "2025-06-11T23:31:10.712669", "level": "INFO", "logger": "backend.routes.auth", "message": "Adaptive session <NAME_EMAIL>: duration=86400s", "module": "auth", "function": "login", "line": 279}
{"timestamp": "2025-06-11T23:31:10.712721", "level": "INFO", "logger": "backend.routes.auth", "message": "Bearer token authentication issued <NAME_EMAIL>", "module": "auth", "function": "login", "line": 284}
{"timestamp": "2025-06-11T23:31:12.356997", "level": "INFO", "logger": "root", "message": "Getting feature matrix for user e4ae2d8f-b32f-52d7-99cd-8a33b19652c7 (email: <EMAIL>)", "module": "subscription", "function": "get_user_feature_matrix", "line": 142}
{"timestamp": "2025-06-11T23:31:12.388869", "level": "INFO", "logger": "root", "message": "Found subscription for user e4ae2d8f-b32f-52d7-99cd-8a33b19652c7: plan=enterprise, status=active", "module": "subscription", "function": "get_user_feature_matrix", "line": 152}
{"timestamp": "2025-06-11T23:31:12.421040", "level": "INFO", "logger": "root", "message": "Found 0 team addons for user e4ae2d8f-b32f-52d7-99cd-8a33b19652c7", "module": "subscription", "function": "get_user_feature_matrix", "line": 181}
{"timestamp": "2025-06-11T23:31:12.421473", "level": "INFO", "logger": "root", "message": "Retrieved base features for plan enterprise", "module": "subscription", "function": "get_user_feature_matrix", "line": 191}
{"timestamp": "2025-06-11T23:31:12.479001", "level": "INFO", "logger": "root", "message": "Successfully generated feature matrix for user e4ae2d8f-b32f-52d7-99cd-8a33b19652c7", "module": "subscription", "function": "get_user_feature_matrix", "line": 284}
{"timestamp": "2025-06-11T23:34:48.023051", "level": "INFO", "logger": "backend.services.qr_preview_cache", "message": "QR preview cache cleanup task stopped", "module": "qr_preview_cache", "function": "stop", "line": 257}
{"timestamp": "2025-06-11T23:34:48.023449", "level": "INFO", "logger": "backend.main", "message": "QR preview cache stopped", "module": "main", "function": "shutdown_qr_preview_cache", "line": 352}
{"timestamp": "2025-06-11T23:34:53.749910", "level": "INFO", "logger": "backend.main", "message": "=== QRVibe API Startup: Logging to file is enabled ===", "module": "main", "function": "<module>", "line": 37}
{"timestamp": "2025-06-11T23:34:53.750193", "level": "INFO", "logger": "backend.main", "message": "Applying critical patches before initialization", "module": "main", "function": "<module>", "line": 43}
{"timestamp": "2025-06-11T23:34:53.750558", "level": "INFO", "logger": "backend.patches", "message": "Applying patches (none required)", "module": "__init__", "function": "apply_patches", "line": 11}
{"timestamp": "2025-06-11T23:34:53.752864", "level": "INFO", "logger": "backend.core.middleware.cors", "message": "Setting up CORS middleware with origins: ['https://127.0.0.1:3000', 'http://127.0.0.1:3000', 'https://localhost:3000', 'http://localhost:3000']", "module": "cors", "function": "setup_cors", "line": 86}
{"timestamp": "2025-06-11T23:34:53.752937", "level": "INFO", "logger": "backend.core.middleware.cors", "message": "CORS middleware configured - preflight requests should be handled automatically", "module": "cors", "function": "setup_cors", "line": 98}
{"timestamp": "2025-06-11T23:34:53.754239", "level": "INFO", "logger": "backend.middleware.security_middleware", "message": "Added Enhanced Security Middleware with tiered rate limiting", "module": "security_middleware", "function": "add_security_middleware", "line": 176}
{"timestamp": "2025-06-11T23:34:53.754390", "level": "INFO", "logger": "backend.main", "message": "Mounting storage directory: /Users/<USER>/aihub/qrvibe/storage", "module": "main", "function": "<module>", "line": 107}
{"timestamp": "2025-06-11T23:34:53.754544", "level": "INFO", "logger": "backend.main", "message": "Storage directory absolute path: /Users/<USER>/aihub/qrvibe/storage", "module": "main", "function": "<module>", "line": 119}
{"timestamp": "2025-06-11T23:34:54.185903", "level": "INFO", "logger": "root", "message": "Storage initialized: environment=test, storage_dir=/Users/<USER>/aihub/qrvibe/storage, s3_enabled=True", "module": "storage", "function": "__init__", "line": 41}
{"timestamp": "2025-06-11T23:34:54.188778", "level": "INFO", "logger": "root", "message": "S3 storage enabled with bucket: qrverse and CDN: https://dev-cdn.qrvibe.com", "module": "storage", "function": "__init__", "line": 59}
{"timestamp": "2025-06-11T23:34:54.235247", "level": "INFO", "logger": "root", "message": "Storage initialized: environment=test, storage_dir=/Users/<USER>/aihub/qrvibe/storage, s3_enabled=True", "module": "storage", "function": "__init__", "line": 41}
{"timestamp": "2025-06-11T23:34:54.238390", "level": "INFO", "logger": "root", "message": "S3 storage enabled with bucket: qrverse and CDN: https://dev-cdn.qrvibe.com", "module": "storage", "function": "__init__", "line": 59}
{"timestamp": "2025-06-11T23:34:54.508650", "level": "INFO", "logger": "backend.middleware.security_middleware", "message": "Enhanced security middleware initialized with tiered rate limiting", "module": "security_middleware", "function": "__init__", "line": 87}
{"timestamp": "2025-06-11T23:34:54.516633", "level": "INFO", "logger": "backend.core.middleware.csrf", "message": "CSRF Protection Middleware initialized with exclude paths: ['/auth/jwt/login', '/auth/logout', '/auth/register', '/auth/2fa', '/auth/me', '/auth/verify-email', '/auth/reset-password', '/admin/login', '/admin/logout']", "module": "csrf", "function": "__init__", "line": 44}
{"timestamp": "2025-06-11T23:34:54.533002", "level": "INFO", "logger": "backend.services.token_blacklist", "message": "Token blacklist service initialized with Redis", "module": "token_blacklist", "function": "initialize", "line": 59}
{"timestamp": "2025-06-11T23:34:54.533230", "level": "INFO", "logger": "backend.main", "message": "Token blacklist service initialized", "module": "main", "function": "initialize_token_blacklist", "line": 98}
{"timestamp": "2025-06-11T23:34:54.538275", "level": "INFO", "logger": "backend.services.qr_preview_cache", "message": "QR preview cache initialized at /Users/<USER>/aihub/qrvibe/storage/cache/qr_preview_cache.db", "module": "qr_preview_cache", "function": "_init_db", "line": 56}
{"timestamp": "2025-06-11T23:34:54.538537", "level": "INFO", "logger": "backend.services.qr_preview_cache", "message": "Started QR preview cache cleanup task", "module": "qr_preview_cache", "function": "start_cleanup_task", "line": 62}
{"timestamp": "2025-06-11T23:34:54.538704", "level": "INFO", "logger": "backend.main", "message": "QR preview cache initialized and cleanup task started", "module": "main", "function": "init_qr_preview_cache", "line": 345}
{"timestamp": "2025-06-11T23:35:11.750608", "level": "INFO", "logger": "backend.services.qr_preview_cache", "message": "QR preview cache cleanup task stopped", "module": "qr_preview_cache", "function": "stop", "line": 257}
{"timestamp": "2025-06-11T23:35:11.750710", "level": "INFO", "logger": "backend.main", "message": "QR preview cache stopped", "module": "main", "function": "shutdown_qr_preview_cache", "line": 352}
{"timestamp": "2025-06-11T23:35:15.385254", "level": "INFO", "logger": "backend.main", "message": "=== QRVibe API Startup: Logging to file is enabled ===", "module": "main", "function": "<module>", "line": 37}
{"timestamp": "2025-06-11T23:35:15.385353", "level": "INFO", "logger": "backend.main", "message": "Applying critical patches before initialization", "module": "main", "function": "<module>", "line": 43}
{"timestamp": "2025-06-11T23:35:15.385414", "level": "INFO", "logger": "backend.patches", "message": "Applying patches (none required)", "module": "__init__", "function": "apply_patches", "line": 11}
{"timestamp": "2025-06-11T23:35:15.385660", "level": "INFO", "logger": "backend.core.middleware.cors", "message": "Setting up CORS middleware with origins: ['https://localhost:3000', 'http://localhost:3000', 'https://127.0.0.1:3000', 'http://127.0.0.1:3000']", "module": "cors", "function": "setup_cors", "line": 86}
{"timestamp": "2025-06-11T23:35:15.386278", "level": "INFO", "logger": "backend.core.middleware.cors", "message": "CORS middleware configured - preflight requests should be handled automatically", "module": "cors", "function": "setup_cors", "line": 98}
{"timestamp": "2025-06-11T23:35:15.387466", "level": "INFO", "logger": "backend.middleware.security_middleware", "message": "Added Enhanced Security Middleware with tiered rate limiting", "module": "security_middleware", "function": "add_security_middleware", "line": 176}
{"timestamp": "2025-06-11T23:35:15.387613", "level": "INFO", "logger": "backend.main", "message": "Mounting storage directory: /Users/<USER>/aihub/qrvibe/storage", "module": "main", "function": "<module>", "line": 107}
{"timestamp": "2025-06-11T23:35:15.387724", "level": "INFO", "logger": "backend.main", "message": "Storage directory absolute path: /Users/<USER>/aihub/qrvibe/storage", "module": "main", "function": "<module>", "line": 119}
{"timestamp": "2025-06-11T23:35:15.857027", "level": "INFO", "logger": "root", "message": "Storage initialized: environment=test, storage_dir=/Users/<USER>/aihub/qrvibe/storage, s3_enabled=True", "module": "storage", "function": "__init__", "line": 41}
{"timestamp": "2025-06-11T23:35:15.859730", "level": "INFO", "logger": "root", "message": "S3 storage enabled with bucket: qrverse and CDN: https://dev-cdn.qrvibe.com", "module": "storage", "function": "__init__", "line": 59}
{"timestamp": "2025-06-11T23:35:15.906297", "level": "INFO", "logger": "root", "message": "Storage initialized: environment=test, storage_dir=/Users/<USER>/aihub/qrvibe/storage, s3_enabled=True", "module": "storage", "function": "__init__", "line": 41}
{"timestamp": "2025-06-11T23:35:15.909399", "level": "INFO", "logger": "root", "message": "S3 storage enabled with bucket: qrverse and CDN: https://dev-cdn.qrvibe.com", "module": "storage", "function": "__init__", "line": 59}
{"timestamp": "2025-06-11T23:35:15.935121", "level": "INFO", "logger": "backend.middleware.security_middleware", "message": "Enhanced security middleware initialized with tiered rate limiting", "module": "security_middleware", "function": "__init__", "line": 87}
{"timestamp": "2025-06-11T23:35:15.935244", "level": "INFO", "logger": "backend.core.middleware.csrf", "message": "CSRF Protection Middleware initialized with exclude paths: ['/auth/jwt/login', '/auth/logout', '/auth/register', '/auth/2fa', '/auth/me', '/auth/verify-email', '/auth/reset-password', '/admin/login', '/admin/logout']", "module": "csrf", "function": "__init__", "line": 44}
{"timestamp": "2025-06-11T23:35:15.940287", "level": "INFO", "logger": "backend.services.token_blacklist", "message": "Token blacklist service initialized with Redis", "module": "token_blacklist", "function": "initialize", "line": 59}
{"timestamp": "2025-06-11T23:35:15.940372", "level": "INFO", "logger": "backend.main", "message": "Token blacklist service initialized", "module": "main", "function": "initialize_token_blacklist", "line": 98}
{"timestamp": "2025-06-11T23:35:15.941016", "level": "INFO", "logger": "backend.services.qr_preview_cache", "message": "QR preview cache initialized at /Users/<USER>/aihub/qrvibe/storage/cache/qr_preview_cache.db", "module": "qr_preview_cache", "function": "_init_db", "line": 56}
{"timestamp": "2025-06-11T23:35:15.941079", "level": "INFO", "logger": "backend.services.qr_preview_cache", "message": "Started QR preview cache cleanup task", "module": "qr_preview_cache", "function": "start_cleanup_task", "line": 62}
{"timestamp": "2025-06-11T23:35:15.941128", "level": "INFO", "logger": "backend.main", "message": "QR preview cache initialized and cleanup task started", "module": "main", "function": "init_qr_preview_cache", "line": 345}
{"timestamp": "2025-06-11T23:35:30.127237", "level": "INFO", "logger": "backend.services.qr_preview_cache", "message": "QR preview cache cleanup task stopped", "module": "qr_preview_cache", "function": "stop", "line": 257}
{"timestamp": "2025-06-11T23:35:30.127701", "level": "INFO", "logger": "backend.main", "message": "QR preview cache stopped", "module": "main", "function": "shutdown_qr_preview_cache", "line": 352}
{"timestamp": "2025-06-11T23:35:33.575020", "level": "INFO", "logger": "backend.main", "message": "=== QRVibe API Startup: Logging to file is enabled ===", "module": "main", "function": "<module>", "line": 37}
{"timestamp": "2025-06-11T23:35:33.575119", "level": "INFO", "logger": "backend.main", "message": "Applying critical patches before initialization", "module": "main", "function": "<module>", "line": 43}
{"timestamp": "2025-06-11T23:35:33.575177", "level": "INFO", "logger": "backend.patches", "message": "Applying patches (none required)", "module": "__init__", "function": "apply_patches", "line": 11}
{"timestamp": "2025-06-11T23:35:33.575445", "level": "INFO", "logger": "backend.core.middleware.cors", "message": "Setting up CORS middleware with origins: ['https://127.0.0.1:3000', 'http://localhost:3000', 'http://127.0.0.1:3000', 'https://localhost:3000']", "module": "cors", "function": "setup_cors", "line": 86}
{"timestamp": "2025-06-11T23:35:33.575501", "level": "INFO", "logger": "backend.core.middleware.cors", "message": "CORS middleware configured - preflight requests should be handled automatically", "module": "cors", "function": "setup_cors", "line": 98}
{"timestamp": "2025-06-11T23:35:33.577163", "level": "INFO", "logger": "backend.middleware.security_middleware", "message": "Added Enhanced Security Middleware with tiered rate limiting", "module": "security_middleware", "function": "add_security_middleware", "line": 176}
{"timestamp": "2025-06-11T23:35:33.577283", "level": "INFO", "logger": "backend.main", "message": "Mounting storage directory: /Users/<USER>/aihub/qrvibe/storage", "module": "main", "function": "<module>", "line": 107}
{"timestamp": "2025-06-11T23:35:33.577404", "level": "INFO", "logger": "backend.main", "message": "Storage directory absolute path: /Users/<USER>/aihub/qrvibe/storage", "module": "main", "function": "<module>", "line": 119}
{"timestamp": "2025-06-11T23:35:33.982030", "level": "INFO", "logger": "root", "message": "Storage initialized: environment=test, storage_dir=/Users/<USER>/aihub/qrvibe/storage, s3_enabled=True", "module": "storage", "function": "__init__", "line": 41}
{"timestamp": "2025-06-11T23:35:33.984901", "level": "INFO", "logger": "root", "message": "S3 storage enabled with bucket: qrverse and CDN: https://dev-cdn.qrvibe.com", "module": "storage", "function": "__init__", "line": 59}
{"timestamp": "2025-06-11T23:35:34.032117", "level": "INFO", "logger": "root", "message": "Storage initialized: environment=test, storage_dir=/Users/<USER>/aihub/qrvibe/storage, s3_enabled=True", "module": "storage", "function": "__init__", "line": 41}
{"timestamp": "2025-06-11T23:35:34.035480", "level": "INFO", "logger": "root", "message": "S3 storage enabled with bucket: qrverse and CDN: https://dev-cdn.qrvibe.com", "module": "storage", "function": "__init__", "line": 59}
{"timestamp": "2025-06-11T23:35:34.095470", "level": "INFO", "logger": "backend.middleware.security_middleware", "message": "Enhanced security middleware initialized with tiered rate limiting", "module": "security_middleware", "function": "__init__", "line": 87}
{"timestamp": "2025-06-11T23:35:34.095690", "level": "INFO", "logger": "backend.core.middleware.csrf", "message": "CSRF Protection Middleware initialized with exclude paths: ['/auth/jwt/login', '/auth/logout', '/auth/register', '/auth/2fa', '/auth/me', '/auth/verify-email', '/auth/reset-password', '/admin/login', '/admin/logout']", "module": "csrf", "function": "__init__", "line": 44}
{"timestamp": "2025-06-11T23:35:34.099052", "level": "INFO", "logger": "backend.services.token_blacklist", "message": "Token blacklist service initialized with Redis", "module": "token_blacklist", "function": "initialize", "line": 59}
{"timestamp": "2025-06-11T23:35:34.099137", "level": "INFO", "logger": "backend.main", "message": "Token blacklist service initialized", "module": "main", "function": "initialize_token_blacklist", "line": 98}
{"timestamp": "2025-06-11T23:35:34.099783", "level": "INFO", "logger": "backend.services.qr_preview_cache", "message": "QR preview cache initialized at /Users/<USER>/aihub/qrvibe/storage/cache/qr_preview_cache.db", "module": "qr_preview_cache", "function": "_init_db", "line": 56}
{"timestamp": "2025-06-11T23:35:34.099849", "level": "INFO", "logger": "backend.services.qr_preview_cache", "message": "Started QR preview cache cleanup task", "module": "qr_preview_cache", "function": "start_cleanup_task", "line": 62}
{"timestamp": "2025-06-11T23:35:34.101012", "level": "INFO", "logger": "backend.main", "message": "QR preview cache initialized and cleanup task started", "module": "main", "function": "init_qr_preview_cache", "line": 345}
{"timestamp": "2025-06-11T23:36:01.382923", "level": "INFO", "logger": "backend.services.qr_preview_cache", "message": "QR preview cache cleanup task stopped", "module": "qr_preview_cache", "function": "stop", "line": 257}
{"timestamp": "2025-06-11T23:36:01.383479", "level": "INFO", "logger": "backend.main", "message": "QR preview cache stopped", "module": "main", "function": "shutdown_qr_preview_cache", "line": 352}
{"timestamp": "2025-06-11T23:36:05.014763", "level": "INFO", "logger": "backend.main", "message": "=== QRVibe API Startup: Logging to file is enabled ===", "module": "main", "function": "<module>", "line": 37}
{"timestamp": "2025-06-11T23:36:05.014861", "level": "INFO", "logger": "backend.main", "message": "Applying critical patches before initialization", "module": "main", "function": "<module>", "line": 43}
{"timestamp": "2025-06-11T23:36:05.014924", "level": "INFO", "logger": "backend.patches", "message": "Applying patches (none required)", "module": "__init__", "function": "apply_patches", "line": 11}
{"timestamp": "2025-06-11T23:36:05.015147", "level": "INFO", "logger": "backend.core.middleware.cors", "message": "Setting up CORS middleware with origins: ['https://localhost:3000', 'http://localhost:3000', 'https://127.0.0.1:3000', 'http://127.0.0.1:3000']", "module": "cors", "function": "setup_cors", "line": 86}
{"timestamp": "2025-06-11T23:36:05.015206", "level": "INFO", "logger": "backend.core.middleware.cors", "message": "CORS middleware configured - preflight requests should be handled automatically", "module": "cors", "function": "setup_cors", "line": 98}
{"timestamp": "2025-06-11T23:36:05.016339", "level": "INFO", "logger": "backend.middleware.security_middleware", "message": "Added Enhanced Security Middleware with tiered rate limiting", "module": "security_middleware", "function": "add_security_middleware", "line": 176}
{"timestamp": "2025-06-11T23:36:05.016458", "level": "INFO", "logger": "backend.main", "message": "Mounting storage directory: /Users/<USER>/aihub/qrvibe/storage", "module": "main", "function": "<module>", "line": 107}
{"timestamp": "2025-06-11T23:36:05.016594", "level": "INFO", "logger": "backend.main", "message": "Storage directory absolute path: /Users/<USER>/aihub/qrvibe/storage", "module": "main", "function": "<module>", "line": 119}
{"timestamp": "2025-06-11T23:36:05.426496", "level": "INFO", "logger": "root", "message": "Storage initialized: environment=test, storage_dir=/Users/<USER>/aihub/qrvibe/storage, s3_enabled=True", "module": "storage", "function": "__init__", "line": 41}
{"timestamp": "2025-06-11T23:36:05.429439", "level": "INFO", "logger": "root", "message": "S3 storage enabled with bucket: qrverse and CDN: https://dev-cdn.qrvibe.com", "module": "storage", "function": "__init__", "line": 59}
{"timestamp": "2025-06-11T23:36:05.475285", "level": "INFO", "logger": "root", "message": "Storage initialized: environment=test, storage_dir=/Users/<USER>/aihub/qrvibe/storage, s3_enabled=True", "module": "storage", "function": "__init__", "line": 41}
{"timestamp": "2025-06-11T23:36:05.478437", "level": "INFO", "logger": "root", "message": "S3 storage enabled with bucket: qrverse and CDN: https://dev-cdn.qrvibe.com", "module": "storage", "function": "__init__", "line": 59}
{"timestamp": "2025-06-11T23:36:05.504605", "level": "INFO", "logger": "backend.middleware.security_middleware", "message": "Enhanced security middleware initialized with tiered rate limiting", "module": "security_middleware", "function": "__init__", "line": 87}
{"timestamp": "2025-06-11T23:36:05.504710", "level": "INFO", "logger": "backend.core.middleware.csrf", "message": "CSRF Protection Middleware initialized with exclude paths: ['/auth/jwt/login', '/auth/logout', '/auth/register', '/auth/2fa', '/auth/me', '/auth/verify-email', '/auth/reset-password', '/admin/login', '/admin/logout']", "module": "csrf", "function": "__init__", "line": 44}
{"timestamp": "2025-06-11T23:36:05.509362", "level": "INFO", "logger": "backend.services.token_blacklist", "message": "Token blacklist service initialized with Redis", "module": "token_blacklist", "function": "initialize", "line": 59}
{"timestamp": "2025-06-11T23:36:05.509429", "level": "INFO", "logger": "backend.main", "message": "Token blacklist service initialized", "module": "main", "function": "initialize_token_blacklist", "line": 98}
{"timestamp": "2025-06-11T23:36:05.510069", "level": "INFO", "logger": "backend.services.qr_preview_cache", "message": "QR preview cache initialized at /Users/<USER>/aihub/qrvibe/storage/cache/qr_preview_cache.db", "module": "qr_preview_cache", "function": "_init_db", "line": 56}
{"timestamp": "2025-06-11T23:36:05.510131", "level": "INFO", "logger": "backend.services.qr_preview_cache", "message": "Started QR preview cache cleanup task", "module": "qr_preview_cache", "function": "start_cleanup_task", "line": 62}
{"timestamp": "2025-06-11T23:36:05.510685", "level": "INFO", "logger": "backend.main", "message": "QR preview cache initialized and cleanup task started", "module": "main", "function": "init_qr_preview_cache", "line": 345}
{"timestamp": "2025-06-11T23:36:12.204546", "level": "INFO", "logger": "backend.services.qr_preview_cache", "message": "QR preview cache cleanup task stopped", "module": "qr_preview_cache", "function": "stop", "line": 257}
{"timestamp": "2025-06-11T23:36:12.204642", "level": "INFO", "logger": "backend.main", "message": "QR preview cache stopped", "module": "main", "function": "shutdown_qr_preview_cache", "line": 352}
{"timestamp": "2025-06-11T23:36:16.441483", "level": "INFO", "logger": "backend.main", "message": "=== QRVibe API Startup: Logging to file is enabled ===", "module": "main", "function": "<module>", "line": 37}
{"timestamp": "2025-06-11T23:36:16.441596", "level": "INFO", "logger": "backend.main", "message": "Applying critical patches before initialization", "module": "main", "function": "<module>", "line": 43}
{"timestamp": "2025-06-11T23:36:16.441679", "level": "INFO", "logger": "backend.patches", "message": "Applying patches (none required)", "module": "__init__", "function": "apply_patches", "line": 11}
{"timestamp": "2025-06-11T23:36:16.442303", "level": "INFO", "logger": "backend.core.middleware.cors", "message": "Setting up CORS middleware with origins: ['https://localhost:3000', 'http://localhost:3000', 'http://127.0.0.1:3000', 'https://127.0.0.1:3000']", "module": "cors", "function": "setup_cors", "line": 86}
{"timestamp": "2025-06-11T23:36:16.442694", "level": "INFO", "logger": "backend.core.middleware.cors", "message": "CORS middleware configured - preflight requests should be handled automatically", "module": "cors", "function": "setup_cors", "line": 98}
{"timestamp": "2025-06-11T23:36:16.444436", "level": "INFO", "logger": "backend.middleware.security_middleware", "message": "Added Enhanced Security Middleware with tiered rate limiting", "module": "security_middleware", "function": "add_security_middleware", "line": 176}
{"timestamp": "2025-06-11T23:36:16.444696", "level": "INFO", "logger": "backend.main", "message": "Mounting storage directory: /Users/<USER>/aihub/qrvibe/storage", "module": "main", "function": "<module>", "line": 107}
{"timestamp": "2025-06-11T23:36:16.445427", "level": "INFO", "logger": "backend.main", "message": "Storage directory absolute path: /Users/<USER>/aihub/qrvibe/storage", "module": "main", "function": "<module>", "line": 119}
{"timestamp": "2025-06-11T23:36:16.908150", "level": "INFO", "logger": "root", "message": "Storage initialized: environment=test, storage_dir=/Users/<USER>/aihub/qrvibe/storage, s3_enabled=True", "module": "storage", "function": "__init__", "line": 41}
{"timestamp": "2025-06-11T23:36:16.912706", "level": "INFO", "logger": "root", "message": "S3 storage enabled with bucket: qrverse and CDN: https://dev-cdn.qrvibe.com", "module": "storage", "function": "__init__", "line": 59}
{"timestamp": "2025-06-11T23:36:16.963032", "level": "INFO", "logger": "root", "message": "Storage initialized: environment=test, storage_dir=/Users/<USER>/aihub/qrvibe/storage, s3_enabled=True", "module": "storage", "function": "__init__", "line": 41}
{"timestamp": "2025-06-11T23:36:16.967056", "level": "INFO", "logger": "root", "message": "S3 storage enabled with bucket: qrverse and CDN: https://dev-cdn.qrvibe.com", "module": "storage", "function": "__init__", "line": 59}
{"timestamp": "2025-06-11T23:36:17.006131", "level": "INFO", "logger": "backend.middleware.security_middleware", "message": "Enhanced security middleware initialized with tiered rate limiting", "module": "security_middleware", "function": "__init__", "line": 87}
{"timestamp": "2025-06-11T23:36:17.006369", "level": "INFO", "logger": "backend.core.middleware.csrf", "message": "CSRF Protection Middleware initialized with exclude paths: ['/auth/jwt/login', '/auth/logout', '/auth/register', '/auth/2fa', '/auth/me', '/auth/verify-email', '/auth/reset-password', '/admin/login', '/admin/logout']", "module": "csrf", "function": "__init__", "line": 44}
{"timestamp": "2025-06-11T23:36:17.015457", "level": "INFO", "logger": "backend.services.token_blacklist", "message": "Token blacklist service initialized with Redis", "module": "token_blacklist", "function": "initialize", "line": 59}
{"timestamp": "2025-06-11T23:36:17.016631", "level": "INFO", "logger": "backend.main", "message": "Token blacklist service initialized", "module": "main", "function": "initialize_token_blacklist", "line": 98}
{"timestamp": "2025-06-11T23:36:17.017888", "level": "INFO", "logger": "backend.services.qr_preview_cache", "message": "QR preview cache initialized at /Users/<USER>/aihub/qrvibe/storage/cache/qr_preview_cache.db", "module": "qr_preview_cache", "function": "_init_db", "line": 56}
{"timestamp": "2025-06-11T23:36:17.018167", "level": "INFO", "logger": "backend.services.qr_preview_cache", "message": "Started QR preview cache cleanup task", "module": "qr_preview_cache", "function": "start_cleanup_task", "line": 62}
{"timestamp": "2025-06-11T23:36:17.018277", "level": "INFO", "logger": "backend.main", "message": "QR preview cache initialized and cleanup task started", "module": "main", "function": "init_qr_preview_cache", "line": 345}
{"timestamp": "2025-06-11T23:36:27.222243", "level": "INFO", "logger": "backend.services.qr_preview_cache", "message": "QR preview cache cleanup task stopped", "module": "qr_preview_cache", "function": "stop", "line": 257}
{"timestamp": "2025-06-11T23:36:27.222995", "level": "INFO", "logger": "backend.main", "message": "QR preview cache stopped", "module": "main", "function": "shutdown_qr_preview_cache", "line": 352}
{"timestamp": "2025-06-11T23:36:30.710375", "level": "INFO", "logger": "backend.main", "message": "=== QRVibe API Startup: Logging to file is enabled ===", "module": "main", "function": "<module>", "line": 37}
{"timestamp": "2025-06-11T23:36:30.710476", "level": "INFO", "logger": "backend.main", "message": "Applying critical patches before initialization", "module": "main", "function": "<module>", "line": 43}
{"timestamp": "2025-06-11T23:36:30.710537", "level": "INFO", "logger": "backend.patches", "message": "Applying patches (none required)", "module": "__init__", "function": "apply_patches", "line": 11}
{"timestamp": "2025-06-11T23:36:30.710772", "level": "INFO", "logger": "backend.core.middleware.cors", "message": "Setting up CORS middleware with origins: ['https://localhost:3000', 'http://localhost:3000', 'http://127.0.0.1:3000', 'https://127.0.0.1:3000']", "module": "cors", "function": "setup_cors", "line": 86}
{"timestamp": "2025-06-11T23:36:30.710829", "level": "INFO", "logger": "backend.core.middleware.cors", "message": "CORS middleware configured - preflight requests should be handled automatically", "module": "cors", "function": "setup_cors", "line": 98}
{"timestamp": "2025-06-11T23:36:30.711876", "level": "INFO", "logger": "backend.middleware.security_middleware", "message": "Added Enhanced Security Middleware with tiered rate limiting", "module": "security_middleware", "function": "add_security_middleware", "line": 176}
{"timestamp": "2025-06-11T23:36:30.712146", "level": "INFO", "logger": "backend.main", "message": "Mounting storage directory: /Users/<USER>/aihub/qrvibe/storage", "module": "main", "function": "<module>", "line": 107}
{"timestamp": "2025-06-11T23:36:30.712287", "level": "INFO", "logger": "backend.main", "message": "Storage directory absolute path: /Users/<USER>/aihub/qrvibe/storage", "module": "main", "function": "<module>", "line": 119}
{"timestamp": "2025-06-11T23:36:31.109589", "level": "INFO", "logger": "root", "message": "Storage initialized: environment=test, storage_dir=/Users/<USER>/aihub/qrvibe/storage, s3_enabled=True", "module": "storage", "function": "__init__", "line": 41}
{"timestamp": "2025-06-11T23:36:31.112353", "level": "INFO", "logger": "root", "message": "S3 storage enabled with bucket: qrverse and CDN: https://dev-cdn.qrvibe.com", "module": "storage", "function": "__init__", "line": 59}
{"timestamp": "2025-06-11T23:36:31.159136", "level": "INFO", "logger": "root", "message": "Storage initialized: environment=test, storage_dir=/Users/<USER>/aihub/qrvibe/storage, s3_enabled=True", "module": "storage", "function": "__init__", "line": 41}
{"timestamp": "2025-06-11T23:36:31.162737", "level": "INFO", "logger": "root", "message": "S3 storage enabled with bucket: qrverse and CDN: https://dev-cdn.qrvibe.com", "module": "storage", "function": "__init__", "line": 59}
{"timestamp": "2025-06-11T23:36:31.185124", "level": "INFO", "logger": "backend.middleware.security_middleware", "message": "Enhanced security middleware initialized with tiered rate limiting", "module": "security_middleware", "function": "__init__", "line": 87}
{"timestamp": "2025-06-11T23:36:31.185238", "level": "INFO", "logger": "backend.core.middleware.csrf", "message": "CSRF Protection Middleware initialized with exclude paths: ['/auth/jwt/login', '/auth/logout', '/auth/register', '/auth/2fa', '/auth/me', '/auth/verify-email', '/auth/reset-password', '/admin/login', '/admin/logout']", "module": "csrf", "function": "__init__", "line": 44}
{"timestamp": "2025-06-11T23:36:31.189702", "level": "INFO", "logger": "backend.services.token_blacklist", "message": "Token blacklist service initialized with Redis", "module": "token_blacklist", "function": "initialize", "line": 59}
{"timestamp": "2025-06-11T23:36:31.189790", "level": "INFO", "logger": "backend.main", "message": "Token blacklist service initialized", "module": "main", "function": "initialize_token_blacklist", "line": 98}
{"timestamp": "2025-06-11T23:36:31.190436", "level": "INFO", "logger": "backend.services.qr_preview_cache", "message": "QR preview cache initialized at /Users/<USER>/aihub/qrvibe/storage/cache/qr_preview_cache.db", "module": "qr_preview_cache", "function": "_init_db", "line": 56}
{"timestamp": "2025-06-11T23:36:31.190510", "level": "INFO", "logger": "backend.services.qr_preview_cache", "message": "Started QR preview cache cleanup task", "module": "qr_preview_cache", "function": "start_cleanup_task", "line": 62}
{"timestamp": "2025-06-11T23:36:31.192308", "level": "INFO", "logger": "backend.main", "message": "QR preview cache initialized and cleanup task started", "module": "main", "function": "init_qr_preview_cache", "line": 345}
{"timestamp": "2025-06-11T23:36:42.957600", "level": "INFO", "logger": "backend.services.qr_preview_cache", "message": "QR preview cache cleanup task stopped", "module": "qr_preview_cache", "function": "stop", "line": 257}
{"timestamp": "2025-06-11T23:36:42.957894", "level": "INFO", "logger": "backend.main", "message": "QR preview cache stopped", "module": "main", "function": "shutdown_qr_preview_cache", "line": 352}
{"timestamp": "2025-06-11T23:36:46.401315", "level": "INFO", "logger": "backend.main", "message": "=== QRVibe API Startup: Logging to file is enabled ===", "module": "main", "function": "<module>", "line": 37}
{"timestamp": "2025-06-11T23:36:46.401407", "level": "INFO", "logger": "backend.main", "message": "Applying critical patches before initialization", "module": "main", "function": "<module>", "line": 43}
{"timestamp": "2025-06-11T23:36:46.401466", "level": "INFO", "logger": "backend.patches", "message": "Applying patches (none required)", "module": "__init__", "function": "apply_patches", "line": 11}
{"timestamp": "2025-06-11T23:36:46.401677", "level": "INFO", "logger": "backend.core.middleware.cors", "message": "Setting up CORS middleware with origins: ['http://127.0.0.1:3000', 'https://127.0.0.1:3000', 'https://localhost:3000', 'http://localhost:3000']", "module": "cors", "function": "setup_cors", "line": 86}
{"timestamp": "2025-06-11T23:36:46.401737", "level": "INFO", "logger": "backend.core.middleware.cors", "message": "CORS middleware configured - preflight requests should be handled automatically", "module": "cors", "function": "setup_cors", "line": 98}
{"timestamp": "2025-06-11T23:36:46.402986", "level": "INFO", "logger": "backend.middleware.security_middleware", "message": "Added Enhanced Security Middleware with tiered rate limiting", "module": "security_middleware", "function": "add_security_middleware", "line": 176}
{"timestamp": "2025-06-11T23:36:46.403226", "level": "INFO", "logger": "backend.main", "message": "Mounting storage directory: /Users/<USER>/aihub/qrvibe/storage", "module": "main", "function": "<module>", "line": 107}
{"timestamp": "2025-06-11T23:36:46.403434", "level": "INFO", "logger": "backend.main", "message": "Storage directory absolute path: /Users/<USER>/aihub/qrvibe/storage", "module": "main", "function": "<module>", "line": 119}
{"timestamp": "2025-06-11T23:36:46.870242", "level": "INFO", "logger": "root", "message": "Storage initialized: environment=test, storage_dir=/Users/<USER>/aihub/qrvibe/storage, s3_enabled=True", "module": "storage", "function": "__init__", "line": 41}
{"timestamp": "2025-06-11T23:36:46.873123", "level": "INFO", "logger": "root", "message": "S3 storage enabled with bucket: qrverse and CDN: https://dev-cdn.qrvibe.com", "module": "storage", "function": "__init__", "line": 59}
{"timestamp": "2025-06-11T23:36:46.919332", "level": "INFO", "logger": "root", "message": "Storage initialized: environment=test, storage_dir=/Users/<USER>/aihub/qrvibe/storage, s3_enabled=True", "module": "storage", "function": "__init__", "line": 41}
{"timestamp": "2025-06-11T23:36:46.922790", "level": "INFO", "logger": "root", "message": "S3 storage enabled with bucket: qrverse and CDN: https://dev-cdn.qrvibe.com", "module": "storage", "function": "__init__", "line": 59}
{"timestamp": "2025-06-11T23:36:46.948949", "level": "INFO", "logger": "backend.middleware.security_middleware", "message": "Enhanced security middleware initialized with tiered rate limiting", "module": "security_middleware", "function": "__init__", "line": 87}
{"timestamp": "2025-06-11T23:36:46.949060", "level": "INFO", "logger": "backend.core.middleware.csrf", "message": "CSRF Protection Middleware initialized with exclude paths: ['/auth/jwt/login', '/auth/logout', '/auth/register', '/auth/2fa', '/auth/me', '/auth/verify-email', '/auth/reset-password', '/admin/login', '/admin/logout']", "module": "csrf", "function": "__init__", "line": 44}
{"timestamp": "2025-06-11T23:36:46.953311", "level": "INFO", "logger": "backend.services.token_blacklist", "message": "Token blacklist service initialized with Redis", "module": "token_blacklist", "function": "initialize", "line": 59}
{"timestamp": "2025-06-11T23:36:46.953417", "level": "INFO", "logger": "backend.main", "message": "Token blacklist service initialized", "module": "main", "function": "initialize_token_blacklist", "line": 98}
{"timestamp": "2025-06-11T23:36:46.954165", "level": "INFO", "logger": "backend.services.qr_preview_cache", "message": "QR preview cache initialized at /Users/<USER>/aihub/qrvibe/storage/cache/qr_preview_cache.db", "module": "qr_preview_cache", "function": "_init_db", "line": 56}
{"timestamp": "2025-06-11T23:36:46.954245", "level": "INFO", "logger": "backend.services.qr_preview_cache", "message": "Started QR preview cache cleanup task", "module": "qr_preview_cache", "function": "start_cleanup_task", "line": 62}
{"timestamp": "2025-06-11T23:36:46.954308", "level": "INFO", "logger": "backend.main", "message": "QR preview cache initialized and cleanup task started", "module": "main", "function": "init_qr_preview_cache", "line": 345}
{"timestamp": "2025-06-11T23:37:10.006672", "level": "INFO", "logger": "backend.services.qr_preview_cache", "message": "QR preview cache cleanup task stopped", "module": "qr_preview_cache", "function": "stop", "line": 257}
{"timestamp": "2025-06-11T23:37:10.007497", "level": "INFO", "logger": "backend.main", "message": "QR preview cache stopped", "module": "main", "function": "shutdown_qr_preview_cache", "line": 352}
{"timestamp": "2025-06-11T23:37:13.417921", "level": "INFO", "logger": "backend.main", "message": "=== QRVibe API Startup: Logging to file is enabled ===", "module": "main", "function": "<module>", "line": 37}
{"timestamp": "2025-06-11T23:37:13.418011", "level": "INFO", "logger": "backend.main", "message": "Applying critical patches before initialization", "module": "main", "function": "<module>", "line": 43}
{"timestamp": "2025-06-11T23:37:13.418067", "level": "INFO", "logger": "backend.patches", "message": "Applying patches (none required)", "module": "__init__", "function": "apply_patches", "line": 11}
{"timestamp": "2025-06-11T23:37:13.418273", "level": "INFO", "logger": "backend.core.middleware.cors", "message": "Setting up CORS middleware with origins: ['http://localhost:3000', 'https://127.0.0.1:3000', 'http://127.0.0.1:3000', 'https://localhost:3000']", "module": "cors", "function": "setup_cors", "line": 86}
{"timestamp": "2025-06-11T23:37:13.422998", "level": "INFO", "logger": "backend.core.middleware.cors", "message": "CORS middleware configured - preflight requests should be handled automatically", "module": "cors", "function": "setup_cors", "line": 98}
{"timestamp": "2025-06-11T23:37:13.424336", "level": "INFO", "logger": "backend.middleware.security_middleware", "message": "Added Enhanced Security Middleware with tiered rate limiting", "module": "security_middleware", "function": "add_security_middleware", "line": 176}
{"timestamp": "2025-06-11T23:37:13.424468", "level": "INFO", "logger": "backend.main", "message": "Mounting storage directory: /Users/<USER>/aihub/qrvibe/storage", "module": "main", "function": "<module>", "line": 107}
{"timestamp": "2025-06-11T23:37:13.424599", "level": "INFO", "logger": "backend.main", "message": "Storage directory absolute path: /Users/<USER>/aihub/qrvibe/storage", "module": "main", "function": "<module>", "line": 119}
{"timestamp": "2025-06-11T23:37:13.834989", "level": "INFO", "logger": "root", "message": "Storage initialized: environment=test, storage_dir=/Users/<USER>/aihub/qrvibe/storage, s3_enabled=True", "module": "storage", "function": "__init__", "line": 41}
{"timestamp": "2025-06-11T23:37:13.837901", "level": "INFO", "logger": "root", "message": "S3 storage enabled with bucket: qrverse and CDN: https://dev-cdn.qrvibe.com", "module": "storage", "function": "__init__", "line": 59}
{"timestamp": "2025-06-11T23:37:13.884412", "level": "INFO", "logger": "root", "message": "Storage initialized: environment=test, storage_dir=/Users/<USER>/aihub/qrvibe/storage, s3_enabled=True", "module": "storage", "function": "__init__", "line": 41}
{"timestamp": "2025-06-11T23:37:13.887738", "level": "INFO", "logger": "root", "message": "S3 storage enabled with bucket: qrverse and CDN: https://dev-cdn.qrvibe.com", "module": "storage", "function": "__init__", "line": 59}
{"timestamp": "2025-06-11T23:37:13.911989", "level": "INFO", "logger": "backend.middleware.security_middleware", "message": "Enhanced security middleware initialized with tiered rate limiting", "module": "security_middleware", "function": "__init__", "line": 87}
{"timestamp": "2025-06-11T23:37:13.912130", "level": "INFO", "logger": "backend.core.middleware.csrf", "message": "CSRF Protection Middleware initialized with exclude paths: ['/auth/jwt/login', '/auth/logout', '/auth/register', '/auth/2fa', '/auth/me', '/auth/verify-email', '/auth/reset-password', '/admin/login', '/admin/logout']", "module": "csrf", "function": "__init__", "line": 44}
{"timestamp": "2025-06-11T23:37:13.915836", "level": "INFO", "logger": "backend.services.token_blacklist", "message": "Token blacklist service initialized with Redis", "module": "token_blacklist", "function": "initialize", "line": 59}
{"timestamp": "2025-06-11T23:37:13.915928", "level": "INFO", "logger": "backend.main", "message": "Token blacklist service initialized", "module": "main", "function": "initialize_token_blacklist", "line": 98}
{"timestamp": "2025-06-11T23:37:13.916585", "level": "INFO", "logger": "backend.services.qr_preview_cache", "message": "QR preview cache initialized at /Users/<USER>/aihub/qrvibe/storage/cache/qr_preview_cache.db", "module": "qr_preview_cache", "function": "_init_db", "line": 56}
{"timestamp": "2025-06-11T23:37:13.916655", "level": "INFO", "logger": "backend.services.qr_preview_cache", "message": "Started QR preview cache cleanup task", "module": "qr_preview_cache", "function": "start_cleanup_task", "line": 62}
{"timestamp": "2025-06-11T23:37:13.916707", "level": "INFO", "logger": "backend.main", "message": "QR preview cache initialized and cleanup task started", "module": "main", "function": "init_qr_preview_cache", "line": 345}
{"timestamp": "2025-06-11T23:37:29.494231", "level": "INFO", "logger": "backend.services.qr_preview_cache", "message": "QR preview cache cleanup task stopped", "module": "qr_preview_cache", "function": "stop", "line": 257}
{"timestamp": "2025-06-11T23:37:29.494338", "level": "INFO", "logger": "backend.main", "message": "QR preview cache stopped", "module": "main", "function": "shutdown_qr_preview_cache", "line": 352}
{"timestamp": "2025-06-11T23:37:32.864581", "level": "INFO", "logger": "backend.main", "message": "=== QRVibe API Startup: Logging to file is enabled ===", "module": "main", "function": "<module>", "line": 37}
{"timestamp": "2025-06-11T23:37:32.864720", "level": "INFO", "logger": "backend.main", "message": "Applying critical patches before initialization", "module": "main", "function": "<module>", "line": 43}
{"timestamp": "2025-06-11T23:37:32.865036", "level": "INFO", "logger": "backend.patches", "message": "Applying patches (none required)", "module": "__init__", "function": "apply_patches", "line": 11}
{"timestamp": "2025-06-11T23:37:32.865309", "level": "INFO", "logger": "backend.core.middleware.cors", "message": "Setting up CORS middleware with origins: ['http://localhost:3000', 'https://localhost:3000', 'http://127.0.0.1:3000', 'https://127.0.0.1:3000']", "module": "cors", "function": "setup_cors", "line": 86}
{"timestamp": "2025-06-11T23:37:32.865418", "level": "INFO", "logger": "backend.core.middleware.cors", "message": "CORS middleware configured - preflight requests should be handled automatically", "module": "cors", "function": "setup_cors", "line": 98}
{"timestamp": "2025-06-11T23:37:32.866642", "level": "INFO", "logger": "backend.middleware.security_middleware", "message": "Added Enhanced Security Middleware with tiered rate limiting", "module": "security_middleware", "function": "add_security_middleware", "line": 176}
{"timestamp": "2025-06-11T23:37:32.866738", "level": "INFO", "logger": "backend.main", "message": "Mounting storage directory: /Users/<USER>/aihub/qrvibe/storage", "module": "main", "function": "<module>", "line": 107}
{"timestamp": "2025-06-11T23:37:32.866856", "level": "INFO", "logger": "backend.main", "message": "Storage directory absolute path: /Users/<USER>/aihub/qrvibe/storage", "module": "main", "function": "<module>", "line": 119}
{"timestamp": "2025-06-11T23:37:33.437149", "level": "INFO", "logger": "root", "message": "Storage initialized: environment=test, storage_dir=/Users/<USER>/aihub/qrvibe/storage, s3_enabled=True", "module": "storage", "function": "__init__", "line": 41}
{"timestamp": "2025-06-11T23:37:33.440048", "level": "INFO", "logger": "root", "message": "S3 storage enabled with bucket: qrverse and CDN: https://dev-cdn.qrvibe.com", "module": "storage", "function": "__init__", "line": 59}
{"timestamp": "2025-06-11T23:37:33.485549", "level": "INFO", "logger": "root", "message": "Storage initialized: environment=test, storage_dir=/Users/<USER>/aihub/qrvibe/storage, s3_enabled=True", "module": "storage", "function": "__init__", "line": 41}
{"timestamp": "2025-06-11T23:37:33.488543", "level": "INFO", "logger": "root", "message": "S3 storage enabled with bucket: qrverse and CDN: https://dev-cdn.qrvibe.com", "module": "storage", "function": "__init__", "line": 59}
{"timestamp": "2025-06-11T23:37:33.512281", "level": "INFO", "logger": "backend.middleware.security_middleware", "message": "Enhanced security middleware initialized with tiered rate limiting", "module": "security_middleware", "function": "__init__", "line": 87}
{"timestamp": "2025-06-11T23:37:33.512473", "level": "INFO", "logger": "backend.core.middleware.csrf", "message": "CSRF Protection Middleware initialized with exclude paths: ['/auth/jwt/login', '/auth/logout', '/auth/register', '/auth/2fa', '/auth/me', '/auth/verify-email', '/auth/reset-password', '/admin/login', '/admin/logout']", "module": "csrf", "function": "__init__", "line": 44}
{"timestamp": "2025-06-11T23:37:33.516036", "level": "INFO", "logger": "backend.services.token_blacklist", "message": "Token blacklist service initialized with Redis", "module": "token_blacklist", "function": "initialize", "line": 59}
{"timestamp": "2025-06-11T23:37:33.516125", "level": "INFO", "logger": "backend.main", "message": "Token blacklist service initialized", "module": "main", "function": "initialize_token_blacklist", "line": 98}
{"timestamp": "2025-06-11T23:37:33.516848", "level": "INFO", "logger": "backend.services.qr_preview_cache", "message": "QR preview cache initialized at /Users/<USER>/aihub/qrvibe/storage/cache/qr_preview_cache.db", "module": "qr_preview_cache", "function": "_init_db", "line": 56}
{"timestamp": "2025-06-11T23:37:33.516935", "level": "INFO", "logger": "backend.services.qr_preview_cache", "message": "Started QR preview cache cleanup task", "module": "qr_preview_cache", "function": "start_cleanup_task", "line": 62}
{"timestamp": "2025-06-11T23:37:33.517002", "level": "INFO", "logger": "backend.main", "message": "QR preview cache initialized and cleanup task started", "module": "main", "function": "init_qr_preview_cache", "line": 345}
{"timestamp": "2025-06-11T23:38:43.287255", "level": "WARNING", "logger": "backend.core.middleware.csrf", "message": "CSRF token missing for POST /api/qr/preview - Header present: False, Cookie present: False", "module": "csrf", "function": "dispatch", "line": 66}
{"timestamp": "2025-06-11T23:38:43.287568", "level": "WARNING", "logger": "backend.core.middleware.csrf", "message": "CSRF validation failed for POST /api/qr/preview - IP: 127.0.0.1", "module": "csrf", "function": "dispatch", "line": 75}
{"timestamp": "2025-06-11T23:40:07.131501", "level": "INFO", "logger": "backend.services.qr_preview_cache", "message": "QR preview cache cleanup task stopped", "module": "qr_preview_cache", "function": "stop", "line": 257}
{"timestamp": "2025-06-11T23:40:07.133453", "level": "INFO", "logger": "backend.main", "message": "QR preview cache stopped", "module": "main", "function": "shutdown_qr_preview_cache", "line": 352}
{"timestamp": "2025-06-11T23:40:10.618922", "level": "INFO", "logger": "backend.main", "message": "=== QRVibe API Startup: Logging to file is enabled ===", "module": "main", "function": "<module>", "line": 37}
{"timestamp": "2025-06-11T23:40:10.619032", "level": "INFO", "logger": "backend.main", "message": "Applying critical patches before initialization", "module": "main", "function": "<module>", "line": 43}
{"timestamp": "2025-06-11T23:40:10.619093", "level": "INFO", "logger": "backend.patches", "message": "Applying patches (none required)", "module": "__init__", "function": "apply_patches", "line": 11}
{"timestamp": "2025-06-11T23:40:10.619301", "level": "INFO", "logger": "backend.core.middleware.cors", "message": "Setting up CORS middleware with origins: ['http://localhost:3000', 'http://127.0.0.1:3000', 'https://localhost:3000', 'https://127.0.0.1:3000']", "module": "cors", "function": "setup_cors", "line": 86}
{"timestamp": "2025-06-11T23:40:10.619918", "level": "INFO", "logger": "backend.core.middleware.cors", "message": "CORS middleware configured - preflight requests should be handled automatically", "module": "cors", "function": "setup_cors", "line": 98}
{"timestamp": "2025-06-11T23:40:10.620928", "level": "INFO", "logger": "backend.middleware.security_middleware", "message": "Added Enhanced Security Middleware with tiered rate limiting", "module": "security_middleware", "function": "add_security_middleware", "line": 176}
{"timestamp": "2025-06-11T23:40:10.621012", "level": "INFO", "logger": "backend.main", "message": "Mounting storage directory: /Users/<USER>/aihub/qrvibe/storage", "module": "main", "function": "<module>", "line": 107}
{"timestamp": "2025-06-11T23:40:10.621114", "level": "INFO", "logger": "backend.main", "message": "Storage directory absolute path: /Users/<USER>/aihub/qrvibe/storage", "module": "main", "function": "<module>", "line": 119}
{"timestamp": "2025-06-11T23:40:11.067106", "level": "INFO", "logger": "root", "message": "Storage initialized: environment=test, storage_dir=/Users/<USER>/aihub/qrvibe/storage, s3_enabled=True", "module": "storage", "function": "__init__", "line": 41}
{"timestamp": "2025-06-11T23:40:11.070159", "level": "INFO", "logger": "root", "message": "S3 storage enabled with bucket: qrverse and CDN: https://dev-cdn.qrvibe.com", "module": "storage", "function": "__init__", "line": 59}
{"timestamp": "2025-06-11T23:40:11.120245", "level": "INFO", "logger": "root", "message": "Storage initialized: environment=test, storage_dir=/Users/<USER>/aihub/qrvibe/storage, s3_enabled=True", "module": "storage", "function": "__init__", "line": 41}
{"timestamp": "2025-06-11T23:40:11.123461", "level": "INFO", "logger": "root", "message": "S3 storage enabled with bucket: qrverse and CDN: https://dev-cdn.qrvibe.com", "module": "storage", "function": "__init__", "line": 59}
{"timestamp": "2025-06-11T23:40:11.219097", "level": "INFO", "logger": "backend.middleware.security_middleware", "message": "Enhanced security middleware initialized with tiered rate limiting", "module": "security_middleware", "function": "__init__", "line": 87}
{"timestamp": "2025-06-11T23:40:11.222535", "level": "INFO", "logger": "backend.core.middleware.csrf", "message": "CSRF Protection Middleware initialized with exclude paths: ['/auth/jwt/login', '/auth/logout', '/auth/register', '/auth/2fa', '/auth/me', '/auth/verify-email', '/auth/reset-password', '/admin/login', '/admin/logout']", "module": "csrf", "function": "__init__", "line": 44}
{"timestamp": "2025-06-11T23:40:11.226539", "level": "INFO", "logger": "backend.services.token_blacklist", "message": "Token blacklist service initialized with Redis", "module": "token_blacklist", "function": "initialize", "line": 59}
{"timestamp": "2025-06-11T23:40:11.226666", "level": "INFO", "logger": "backend.main", "message": "Token blacklist service initialized", "module": "main", "function": "initialize_token_blacklist", "line": 98}
{"timestamp": "2025-06-11T23:40:11.227382", "level": "INFO", "logger": "backend.services.qr_preview_cache", "message": "QR preview cache initialized at /Users/<USER>/aihub/qrvibe/storage/cache/qr_preview_cache.db", "module": "qr_preview_cache", "function": "_init_db", "line": 56}
{"timestamp": "2025-06-11T23:40:11.227451", "level": "INFO", "logger": "backend.services.qr_preview_cache", "message": "Started QR preview cache cleanup task", "module": "qr_preview_cache", "function": "start_cleanup_task", "line": 62}
{"timestamp": "2025-06-11T23:40:11.233458", "level": "INFO", "logger": "backend.main", "message": "QR preview cache initialized and cleanup task started", "module": "main", "function": "init_qr_preview_cache", "line": 345}
{"timestamp": "2025-06-11T23:40:22.958173", "level": "WARNING", "logger": "backend.core.middleware.csrf", "message": "CSRF token missing for POST /qr/preview/svg - Header present: False, Cookie present: False", "module": "csrf", "function": "dispatch", "line": 66}
{"timestamp": "2025-06-11T23:40:22.964032", "level": "WARNING", "logger": "backend.core.middleware.csrf", "message": "CSRF validation failed for POST /qr/preview/svg - IP: 127.0.0.1", "module": "csrf", "function": "dispatch", "line": 75}
{"timestamp": "2025-06-11T23:41:41.517917", "level": "WARNING", "logger": "backend.core.security.jwt_keys", "message": "Unknown JWT kid requested: ad4fbdf4-9e77-4c60-a6b8-e900a52a2a12", "module": "jwt_keys", "function": "get_verification_key", "line": 105}
{"timestamp": "2025-06-11T23:41:41.518009", "level": "WARNING", "logger": "backend.core.security.jwt_keys", "message": "Verification failed with key ID ad4fbdf4-9e77-4c60-a6b8-e900a52a2a12: Unknown kid: ad4fbdf4-9e77-4c60-a6b8-e900a52a2a12", "module": "jwt_keys", "function": "decode_token", "line": 203}
{"timestamp": "2025-06-11T23:41:41.528858", "level": "WARNING", "logger": "backend.core.security.jwt_keys", "message": "Verification failed with current key: Signature verification failed.", "module": "jwt_keys", "function": "decode_token", "line": 216}
{"timestamp": "2025-06-11T23:41:41.529755", "level": "ERROR", "logger": "backend.core.security.jwt_keys", "message": "Token verification failed with all keys. Errors: Key 3243159b-5c44-442a-8031-b8ff0ae3140f: Signature verification failed.", "module": "jwt_keys", "function": "decode_token", "line": 239}
{"timestamp": "2025-06-11T23:41:41.529846", "level": "ERROR", "logger": "backend.core.security.jwt_keys", "message": "Token decode error: Token verification failed with all keys. Errors: Key 3243159b-5c44-442a-8031-b8ff0ae3140f: Signature verification failed.", "module": "jwt_keys", "function": "decode_token", "line": 244}
{"timestamp": "2025-06-11T23:41:41.529969", "level": "ERROR", "logger": "backend.services.auth", "message": "JWT token validation failed: Failed to decode token: Token verification failed with all keys. Errors: Key 3243159b-5c44-442a-8031-b8ff0ae3140f: Signature verification failed.", "module": "auth", "function": "authenticate_token", "line": 686}
{"timestamp": "2025-06-11T23:41:41.530371", "level": "WARNING", "logger": "backend.core.security.jwt_keys", "message": "Unknown JWT kid requested: ad4fbdf4-9e77-4c60-a6b8-e900a52a2a12", "module": "jwt_keys", "function": "get_verification_key", "line": 105}
{"timestamp": "2025-06-11T23:41:41.530425", "level": "WARNING", "logger": "backend.core.security.jwt_keys", "message": "Verification failed with key ID ad4fbdf4-9e77-4c60-a6b8-e900a52a2a12: Unknown kid: ad4fbdf4-9e77-4c60-a6b8-e900a52a2a12", "module": "jwt_keys", "function": "decode_token", "line": 203}
{"timestamp": "2025-06-11T23:41:41.530560", "level": "WARNING", "logger": "backend.core.security.jwt_keys", "message": "Verification failed with current key: Signature verification failed.", "module": "jwt_keys", "function": "decode_token", "line": 216}
{"timestamp": "2025-06-11T23:41:41.530703", "level": "ERROR", "logger": "backend.core.security.jwt_keys", "message": "Token verification failed with all keys. Errors: Key 3243159b-5c44-442a-8031-b8ff0ae3140f: Signature verification failed.", "module": "jwt_keys", "function": "decode_token", "line": 239}
{"timestamp": "2025-06-11T23:41:41.530769", "level": "ERROR", "logger": "backend.core.security.jwt_keys", "message": "Token decode error: Token verification failed with all keys. Errors: Key 3243159b-5c44-442a-8031-b8ff0ae3140f: Signature verification failed.", "module": "jwt_keys", "function": "decode_token", "line": 244}
{"timestamp": "2025-06-11T23:41:41.530837", "level": "ERROR", "logger": "backend.services.auth", "message": "JWT token validation failed: Failed to decode token: Token verification failed with all keys. Errors: Key 3243159b-5c44-442a-8031-b8ff0ae3140f: Signature verification failed.", "module": "auth", "function": "authenticate_token", "line": 686}
{"timestamp": "2025-06-11T23:41:41.533522", "level": "WARNING", "logger": "backend.core.security.jwt_keys", "message": "Unknown JWT kid requested: ad4fbdf4-9e77-4c60-a6b8-e900a52a2a12", "module": "jwt_keys", "function": "get_verification_key", "line": 105}
{"timestamp": "2025-06-11T23:41:41.533596", "level": "WARNING", "logger": "backend.core.security.jwt_keys", "message": "Verification failed with key ID ad4fbdf4-9e77-4c60-a6b8-e900a52a2a12: Unknown kid: ad4fbdf4-9e77-4c60-a6b8-e900a52a2a12", "module": "jwt_keys", "function": "decode_token", "line": 203}
{"timestamp": "2025-06-11T23:41:41.534723", "level": "WARNING", "logger": "backend.core.security.jwt_keys", "message": "Verification failed with current key: Signature verification failed.", "module": "jwt_keys", "function": "decode_token", "line": 216}
{"timestamp": "2025-06-11T23:41:41.534873", "level": "ERROR", "logger": "backend.core.security.jwt_keys", "message": "Token verification failed with all keys. Errors: Key 3243159b-5c44-442a-8031-b8ff0ae3140f: Signature verification failed.", "module": "jwt_keys", "function": "decode_token", "line": 239}
{"timestamp": "2025-06-11T23:41:41.534944", "level": "ERROR", "logger": "backend.core.security.jwt_keys", "message": "Token decode error: Token verification failed with all keys. Errors: Key 3243159b-5c44-442a-8031-b8ff0ae3140f: Signature verification failed.", "module": "jwt_keys", "function": "decode_token", "line": 244}
{"timestamp": "2025-06-11T23:41:41.535033", "level": "ERROR", "logger": "backend.auth", "message": "Token validation error: Failed to decode token: Token verification failed with all keys. Errors: Key 3243159b-5c44-442a-8031-b8ff0ae3140f: Signature verification failed.", "module": "__init__", "function": "get_current_user", "line": 193}
{"timestamp": "2025-06-11T23:41:41.535604", "level": "WARNING", "logger": "backend.middleware.subscription_limits", "message": "Error authenticating token in subscription middleware: name 'ValidationError' is not defined", "module": "subscription_limits", "function": "_get_current_user", "line": 308}
{"timestamp": "2025-06-11T23:41:41.535763", "level": "WARNING", "logger": "backend.middleware.subscription_limits", "message": "Error authenticating token in subscription middleware: name 'ValidationError' is not defined", "module": "subscription_limits", "function": "_get_current_user", "line": 308}
{"timestamp": "2025-06-11T23:41:41.539406", "level": "WARNING", "logger": "backend.core.security.jwt_keys", "message": "Unknown JWT kid requested: ad4fbdf4-9e77-4c60-a6b8-e900a52a2a12", "module": "jwt_keys", "function": "get_verification_key", "line": 105}
{"timestamp": "2025-06-11T23:41:41.539475", "level": "WARNING", "logger": "backend.core.security.jwt_keys", "message": "Verification failed with key ID ad4fbdf4-9e77-4c60-a6b8-e900a52a2a12: Unknown kid: ad4fbdf4-9e77-4c60-a6b8-e900a52a2a12", "module": "jwt_keys", "function": "decode_token", "line": 203}
{"timestamp": "2025-06-11T23:41:41.539879", "level": "WARNING", "logger": "backend.core.security.jwt_keys", "message": "Verification failed with current key: Signature verification failed.", "module": "jwt_keys", "function": "decode_token", "line": 216}
{"timestamp": "2025-06-11T23:41:41.540081", "level": "ERROR", "logger": "backend.core.security.jwt_keys", "message": "Token verification failed with all keys. Errors: Key 3243159b-5c44-442a-8031-b8ff0ae3140f: Signature verification failed.", "module": "jwt_keys", "function": "decode_token", "line": 239}
{"timestamp": "2025-06-11T23:41:41.540174", "level": "ERROR", "logger": "backend.core.security.jwt_keys", "message": "Token decode error: Token verification failed with all keys. Errors: Key 3243159b-5c44-442a-8031-b8ff0ae3140f: Signature verification failed.", "module": "jwt_keys", "function": "decode_token", "line": 244}
{"timestamp": "2025-06-11T23:41:41.540261", "level": "ERROR", "logger": "backend.auth", "message": "Token validation error: Failed to decode token: Token verification failed with all keys. Errors: Key 3243159b-5c44-442a-8031-b8ff0ae3140f: Signature verification failed.", "module": "__init__", "function": "get_current_user", "line": 193}
{"timestamp": "2025-06-11T23:41:41.541258", "level": "WARNING", "logger": "backend.core.security.jwt_keys", "message": "Unknown JWT kid requested: ad4fbdf4-9e77-4c60-a6b8-e900a52a2a12", "module": "jwt_keys", "function": "get_verification_key", "line": 105}
{"timestamp": "2025-06-11T23:41:41.541323", "level": "WARNING", "logger": "backend.core.security.jwt_keys", "message": "Verification failed with key ID ad4fbdf4-9e77-4c60-a6b8-e900a52a2a12: Unknown kid: ad4fbdf4-9e77-4c60-a6b8-e900a52a2a12", "module": "jwt_keys", "function": "decode_token", "line": 203}
{"timestamp": "2025-06-11T23:41:41.541467", "level": "WARNING", "logger": "backend.core.security.jwt_keys", "message": "Verification failed with current key: Signature verification failed.", "module": "jwt_keys", "function": "decode_token", "line": 216}
{"timestamp": "2025-06-11T23:41:41.541635", "level": "ERROR", "logger": "backend.core.security.jwt_keys", "message": "Token verification failed with all keys. Errors: Key 3243159b-5c44-442a-8031-b8ff0ae3140f: Signature verification failed.", "module": "jwt_keys", "function": "decode_token", "line": 239}
{"timestamp": "2025-06-11T23:41:41.541713", "level": "ERROR", "logger": "backend.core.security.jwt_keys", "message": "Token decode error: Token verification failed with all keys. Errors: Key 3243159b-5c44-442a-8031-b8ff0ae3140f: Signature verification failed.", "module": "jwt_keys", "function": "decode_token", "line": 244}
{"timestamp": "2025-06-11T23:41:41.541800", "level": "ERROR", "logger": "backend.auth", "message": "Token validation error: Failed to decode token: Token verification failed with all keys. Errors: Key 3243159b-5c44-442a-8031-b8ff0ae3140f: Signature verification failed.", "module": "__init__", "function": "get_current_user", "line": 193}
{"timestamp": "2025-06-11T23:41:58.810632", "level": "INFO", "logger": "backend.services.enhanced_rate_limit", "message": "Auth event for anonymous: login_attempt (failure)", "module": "enhanced_rate_limit", "function": "track_auth_event", "line": 182}
{"timestamp": "2025-06-11T23:41:59.329317", "level": "INFO", "logger": "backend.services.auth", "message": "Authentication successful for user: <EMAIL>", "module": "auth", "function": "authenticate_user", "line": 266}
{"timestamp": "2025-06-11T23:41:59.330494", "level": "INFO", "logger": "backend.routes.auth", "message": "User logged in successfully: <EMAIL>", "module": "auth", "function": "login", "line": 218}
{"timestamp": "2025-06-11T23:41:59.332309", "level": "INFO", "logger": "backend.services.enhanced_rate_limit", "message": "Auth event for user e4ae2d8f-b32f-52d7-99cd-8a33b19652c7: login_success (success)", "module": "enhanced_rate_limit", "function": "track_auth_event", "line": 182}
{"timestamp": "2025-06-11T23:41:59.363015", "level": "INFO", "logger": "backend.services.adaptive_session", "message": "Adaptive session for user e4ae2d8f-b32f-52d7-99cd-8a33b19652c7: risk=low, duration=86400s", "module": "adaptive_session", "function": "get_session_expiry", "line": 161}
{"timestamp": "2025-06-11T23:41:59.363111", "level": "INFO", "logger": "backend.routes.auth", "message": "Adaptive session <NAME_EMAIL>: duration=86400s", "module": "auth", "function": "login", "line": 279}
{"timestamp": "2025-06-11T23:41:59.363168", "level": "INFO", "logger": "backend.routes.auth", "message": "Bearer token authentication issued <NAME_EMAIL>", "module": "auth", "function": "login", "line": 284}
{"timestamp": "2025-06-12T00:16:46.398321", "level": "WARNING", "logger": "backend.core.middleware.csrf", "message": "CSRF token missing for POST /qr/preview/svg - Header present: False, Cookie present: False", "module": "csrf", "function": "dispatch", "line": 66}
{"timestamp": "2025-06-12T00:16:46.400022", "level": "WARNING", "logger": "backend.core.middleware.csrf", "message": "CSRF validation failed for POST /qr/preview/svg - IP: 127.0.0.1", "module": "csrf", "function": "dispatch", "line": 75}
{"timestamp": "2025-06-12T00:16:46.882436", "level": "WARNING", "logger": "backend.core.middleware.csrf", "message": "CSRF token missing for POST /qr/preview/svg - Header present: False, Cookie present: False", "module": "csrf", "function": "dispatch", "line": 66}
{"timestamp": "2025-06-12T00:16:46.882539", "level": "WARNING", "logger": "backend.core.middleware.csrf", "message": "CSRF validation failed for POST /qr/preview/svg - IP: 127.0.0.1", "module": "csrf", "function": "dispatch", "line": 75}
{"timestamp": "2025-06-12T00:19:56.720601", "level": "WARNING", "logger": "backend.core.middleware.csrf", "message": "CSRF token missing for POST /qr/preview/svg - Header present: False, Cookie present: False", "module": "csrf", "function": "dispatch", "line": 66}
{"timestamp": "2025-06-12T00:19:56.723173", "level": "WARNING", "logger": "backend.core.middleware.csrf", "message": "CSRF validation failed for POST /qr/preview/svg - IP: 127.0.0.1", "module": "csrf", "function": "dispatch", "line": 75}
{"timestamp": "2025-06-12T00:19:57.531549", "level": "WARNING", "logger": "backend.core.middleware.csrf", "message": "CSRF token missing for POST /qr/preview/svg - Header present: False, Cookie present: False", "module": "csrf", "function": "dispatch", "line": 66}
{"timestamp": "2025-06-12T00:19:57.531709", "level": "WARNING", "logger": "backend.core.middleware.csrf", "message": "CSRF validation failed for POST /qr/preview/svg - IP: 127.0.0.1", "module": "csrf", "function": "dispatch", "line": 75}
{"timestamp": "2025-06-12T00:20:08.315246", "level": "INFO", "logger": "backend.services.enhanced_rate_limit", "message": "Auth event for anonymous: login_attempt (failure)", "module": "enhanced_rate_limit", "function": "track_auth_event", "line": 182}
{"timestamp": "2025-06-12T00:20:08.978033", "level": "INFO", "logger": "backend.services.auth", "message": "Authentication successful for user: <EMAIL>", "module": "auth", "function": "authenticate_user", "line": 266}
{"timestamp": "2025-06-12T00:20:08.983106", "level": "INFO", "logger": "backend.routes.auth", "message": "User logged in successfully: <EMAIL>", "module": "auth", "function": "login", "line": 218}
{"timestamp": "2025-06-12T00:20:08.985520", "level": "INFO", "logger": "backend.services.enhanced_rate_limit", "message": "Auth event for user e4ae2d8f-b32f-52d7-99cd-8a33b19652c7: login_success (success)", "module": "enhanced_rate_limit", "function": "track_auth_event", "line": 182}
{"timestamp": "2025-06-12T00:20:09.056647", "level": "INFO", "logger": "backend.services.adaptive_session", "message": "Adaptive session for user e4ae2d8f-b32f-52d7-99cd-8a33b19652c7: risk=low, duration=86400s", "module": "adaptive_session", "function": "get_session_expiry", "line": 161}
{"timestamp": "2025-06-12T00:20:09.056748", "level": "INFO", "logger": "backend.routes.auth", "message": "Adaptive session <NAME_EMAIL>: duration=86400s", "module": "auth", "function": "login", "line": 279}
{"timestamp": "2025-06-12T00:20:09.056806", "level": "INFO", "logger": "backend.routes.auth", "message": "Bearer token authentication issued <NAME_EMAIL>", "module": "auth", "function": "login", "line": 284}
{"timestamp": "2025-06-12T00:41:12.806906", "level": "INFO", "logger": "backend.services.qr_preview_cache", "message": "QR preview cache cleanup task stopped", "module": "qr_preview_cache", "function": "stop", "line": 257}
{"timestamp": "2025-06-12T00:41:12.810780", "level": "INFO", "logger": "backend.main", "message": "QR preview cache stopped", "module": "main", "function": "shutdown_qr_preview_cache", "line": 352}
{"timestamp": "2025-06-12T00:41:17.730573", "level": "INFO", "logger": "backend.main", "message": "=== QRVibe API Startup: Logging to file is enabled ===", "module": "main", "function": "<module>", "line": 37}
{"timestamp": "2025-06-12T00:41:17.730689", "level": "INFO", "logger": "backend.main", "message": "Applying critical patches before initialization", "module": "main", "function": "<module>", "line": 43}
{"timestamp": "2025-06-12T00:41:17.730761", "level": "INFO", "logger": "backend.patches", "message": "Applying patches (none required)", "module": "__init__", "function": "apply_patches", "line": 11}
{"timestamp": "2025-06-12T00:41:17.731000", "level": "INFO", "logger": "backend.core.middleware.cors", "message": "Setting up CORS middleware with origins: ['https://localhost:3000', 'http://localhost:3000', 'https://127.0.0.1:3000', 'http://127.0.0.1:3000']", "module": "cors", "function": "setup_cors", "line": 86}
{"timestamp": "2025-06-12T00:41:17.731787", "level": "INFO", "logger": "backend.core.middleware.cors", "message": "CORS middleware configured - preflight requests should be handled automatically", "module": "cors", "function": "setup_cors", "line": 98}
{"timestamp": "2025-06-12T00:41:17.732978", "level": "INFO", "logger": "backend.middleware.security_middleware", "message": "Added Enhanced Security Middleware with tiered rate limiting", "module": "security_middleware", "function": "add_security_middleware", "line": 176}
{"timestamp": "2025-06-12T00:41:17.733112", "level": "INFO", "logger": "backend.main", "message": "Mounting storage directory: /Users/<USER>/aihub/qrvibe/storage", "module": "main", "function": "<module>", "line": 107}
{"timestamp": "2025-06-12T00:41:17.733270", "level": "INFO", "logger": "backend.main", "message": "Storage directory absolute path: /Users/<USER>/aihub/qrvibe/storage", "module": "main", "function": "<module>", "line": 119}
{"timestamp": "2025-06-12T00:41:18.137108", "level": "INFO", "logger": "root", "message": "Storage initialized: environment=test, storage_dir=/Users/<USER>/aihub/qrvibe/storage, s3_enabled=True", "module": "storage", "function": "__init__", "line": 41}
{"timestamp": "2025-06-12T00:41:18.140030", "level": "INFO", "logger": "root", "message": "S3 storage enabled with bucket: qrverse and CDN: https://dev-cdn.qrvibe.com", "module": "storage", "function": "__init__", "line": 59}
{"timestamp": "2025-06-12T00:41:18.187293", "level": "INFO", "logger": "root", "message": "Storage initialized: environment=test, storage_dir=/Users/<USER>/aihub/qrvibe/storage, s3_enabled=True", "module": "storage", "function": "__init__", "line": 41}
{"timestamp": "2025-06-12T00:41:18.190609", "level": "INFO", "logger": "root", "message": "S3 storage enabled with bucket: qrverse and CDN: https://dev-cdn.qrvibe.com", "module": "storage", "function": "__init__", "line": 59}
{"timestamp": "2025-06-12T00:41:18.223628", "level": "INFO", "logger": "backend.middleware.security_middleware", "message": "Enhanced security middleware initialized with tiered rate limiting", "module": "security_middleware", "function": "__init__", "line": 87}
{"timestamp": "2025-06-12T00:41:18.223737", "level": "INFO", "logger": "backend.core.middleware.csrf", "message": "CSRF Protection Middleware initialized with exclude paths: ['/auth/jwt/login', '/auth/logout', '/auth/register', '/auth/2fa', '/auth/me', '/auth/verify-email', '/auth/reset-password', '/admin/login', '/admin/logout']", "module": "csrf", "function": "__init__", "line": 44}
{"timestamp": "2025-06-12T00:41:18.228571", "level": "INFO", "logger": "backend.services.token_blacklist", "message": "Token blacklist service initialized with Redis", "module": "token_blacklist", "function": "initialize", "line": 59}
{"timestamp": "2025-06-12T00:41:18.228635", "level": "INFO", "logger": "backend.main", "message": "Token blacklist service initialized", "module": "main", "function": "initialize_token_blacklist", "line": 98}
{"timestamp": "2025-06-12T00:41:18.229209", "level": "INFO", "logger": "backend.services.qr_preview_cache", "message": "QR preview cache initialized at /Users/<USER>/aihub/qrvibe/storage/cache/qr_preview_cache.db", "module": "qr_preview_cache", "function": "_init_db", "line": 56}
{"timestamp": "2025-06-12T00:41:18.229282", "level": "INFO", "logger": "backend.services.qr_preview_cache", "message": "Started QR preview cache cleanup task", "module": "qr_preview_cache", "function": "start_cleanup_task", "line": 62}
{"timestamp": "2025-06-12T00:41:18.229345", "level": "INFO", "logger": "backend.main", "message": "QR preview cache initialized and cleanup task started", "module": "main", "function": "init_qr_preview_cache", "line": 345}
{"timestamp": "2025-06-12T00:41:41.708000", "level": "WARNING", "logger": "backend.core.security.jwt_keys", "message": "Unknown JWT kid requested: 3243159b-5c44-442a-8031-b8ff0ae3140f", "module": "jwt_keys", "function": "get_verification_key", "line": 105}
{"timestamp": "2025-06-12T00:41:41.708315", "level": "WARNING", "logger": "backend.core.security.jwt_keys", "message": "Verification failed with key ID 3243159b-5c44-442a-8031-b8ff0ae3140f: Unknown kid: 3243159b-5c44-442a-8031-b8ff0ae3140f", "module": "jwt_keys", "function": "decode_token", "line": 203}
{"timestamp": "2025-06-12T00:41:41.739114", "level": "WARNING", "logger": "backend.core.security.jwt_keys", "message": "Verification failed with current key: Signature verification failed.", "module": "jwt_keys", "function": "decode_token", "line": 216}
{"timestamp": "2025-06-12T00:41:41.739463", "level": "ERROR", "logger": "backend.core.security.jwt_keys", "message": "Token verification failed with all keys. Errors: Key d193c25a-f21b-46f7-a864-af99c6eb2746: Signature verification failed.", "module": "jwt_keys", "function": "decode_token", "line": 239}
{"timestamp": "2025-06-12T00:41:41.739533", "level": "ERROR", "logger": "backend.core.security.jwt_keys", "message": "Token decode error: Token verification failed with all keys. Errors: Key d193c25a-f21b-46f7-a864-af99c6eb2746: Signature verification failed.", "module": "jwt_keys", "function": "decode_token", "line": 244}
{"timestamp": "2025-06-12T00:41:41.739650", "level": "ERROR", "logger": "backend.services.auth", "message": "JWT token validation failed: Failed to decode token: Token verification failed with all keys. Errors: Key d193c25a-f21b-46f7-a864-af99c6eb2746: Signature verification failed.", "module": "auth", "function": "authenticate_token", "line": 686}
{"timestamp": "2025-06-12T00:41:41.740611", "level": "WARNING", "logger": "backend.middleware.subscription_limits", "message": "Error authenticating token in subscription middleware: name 'ValidationError' is not defined", "module": "subscription_limits", "function": "_get_current_user", "line": 308}
{"timestamp": "2025-06-12T00:41:41.742086", "level": "WARNING", "logger": "backend.core.security.jwt_keys", "message": "Unknown JWT kid requested: 3243159b-5c44-442a-8031-b8ff0ae3140f", "module": "jwt_keys", "function": "get_verification_key", "line": 105}
{"timestamp": "2025-06-12T00:41:41.743460", "level": "WARNING", "logger": "backend.core.security.jwt_keys", "message": "Verification failed with key ID 3243159b-5c44-442a-8031-b8ff0ae3140f: Unknown kid: 3243159b-5c44-442a-8031-b8ff0ae3140f", "module": "jwt_keys", "function": "decode_token", "line": 203}
{"timestamp": "2025-06-12T00:41:41.743672", "level": "WARNING", "logger": "backend.core.security.jwt_keys", "message": "Verification failed with current key: Signature verification failed.", "module": "jwt_keys", "function": "decode_token", "line": 216}
{"timestamp": "2025-06-12T00:41:41.790729", "level": "ERROR", "logger": "backend.core.security.jwt_keys", "message": "Token verification failed with all keys. Errors: Key d193c25a-f21b-46f7-a864-af99c6eb2746: Signature verification failed.", "module": "jwt_keys", "function": "decode_token", "line": 239}
{"timestamp": "2025-06-12T00:41:41.790883", "level": "ERROR", "logger": "backend.core.security.jwt_keys", "message": "Token decode error: Token verification failed with all keys. Errors: Key d193c25a-f21b-46f7-a864-af99c6eb2746: Signature verification failed.", "module": "jwt_keys", "function": "decode_token", "line": 244}
{"timestamp": "2025-06-12T00:41:41.790966", "level": "ERROR", "logger": "backend.services.auth", "message": "JWT token validation failed: Failed to decode token: Token verification failed with all keys. Errors: Key d193c25a-f21b-46f7-a864-af99c6eb2746: Signature verification failed.", "module": "auth", "function": "authenticate_token", "line": 686}
{"timestamp": "2025-06-12T00:41:41.792618", "level": "WARNING", "logger": "backend.middleware.subscription_limits", "message": "Auth error in subscription middleware: name 'ValidationError' is not defined", "module": "subscription_limits", "function": "dispatch", "line": 234}
{"timestamp": "2025-06-12T00:41:41.843323", "level": "WARNING", "logger": "backend.core.security.jwt_keys", "message": "Unknown JWT kid requested: 3243159b-5c44-442a-8031-b8ff0ae3140f", "module": "jwt_keys", "function": "get_verification_key", "line": 105}
{"timestamp": "2025-06-12T00:41:41.843394", "level": "WARNING", "logger": "backend.core.security.jwt_keys", "message": "Verification failed with key ID 3243159b-5c44-442a-8031-b8ff0ae3140f: Unknown kid: 3243159b-5c44-442a-8031-b8ff0ae3140f", "module": "jwt_keys", "function": "decode_token", "line": 203}
{"timestamp": "2025-06-12T00:41:41.866751", "level": "WARNING", "logger": "backend.core.security.jwt_keys", "message": "Verification failed with current key: Signature verification failed.", "module": "jwt_keys", "function": "decode_token", "line": 216}
{"timestamp": "2025-06-12T00:41:41.866919", "level": "ERROR", "logger": "backend.core.security.jwt_keys", "message": "Token verification failed with all keys. Errors: Key d193c25a-f21b-46f7-a864-af99c6eb2746: Signature verification failed.", "module": "jwt_keys", "function": "decode_token", "line": 239}
{"timestamp": "2025-06-12T00:41:41.868245", "level": "ERROR", "logger": "backend.core.security.jwt_keys", "message": "Token decode error: Token verification failed with all keys. Errors: Key d193c25a-f21b-46f7-a864-af99c6eb2746: Signature verification failed.", "module": "jwt_keys", "function": "decode_token", "line": 244}
{"timestamp": "2025-06-12T00:41:41.868332", "level": "ERROR", "logger": "backend.services.auth", "message": "JWT token validation failed: Failed to decode token: Token verification failed with all keys. Errors: Key d193c25a-f21b-46f7-a864-af99c6eb2746: Signature verification failed.", "module": "auth", "function": "authenticate_token", "line": 686}
{"timestamp": "2025-06-12T00:42:23.670844", "level": "WARNING", "logger": "backend.core.security.jwt_keys", "message": "Unknown JWT kid requested: 3243159b-5c44-442a-8031-b8ff0ae3140f", "module": "jwt_keys", "function": "get_verification_key", "line": 105}
{"timestamp": "2025-06-12T00:42:23.671824", "level": "WARNING", "logger": "backend.core.security.jwt_keys", "message": "Verification failed with key ID 3243159b-5c44-442a-8031-b8ff0ae3140f: Unknown kid: 3243159b-5c44-442a-8031-b8ff0ae3140f", "module": "jwt_keys", "function": "decode_token", "line": 203}
{"timestamp": "2025-06-12T00:42:23.684879", "level": "WARNING", "logger": "backend.core.security.jwt_keys", "message": "Verification failed with current key: Signature verification failed.", "module": "jwt_keys", "function": "decode_token", "line": 216}
{"timestamp": "2025-06-12T00:42:23.685533", "level": "ERROR", "logger": "backend.core.security.jwt_keys", "message": "Token verification failed with all keys. Errors: Key d193c25a-f21b-46f7-a864-af99c6eb2746: Signature verification failed.", "module": "jwt_keys", "function": "decode_token", "line": 239}
{"timestamp": "2025-06-12T00:42:23.685942", "level": "ERROR", "logger": "backend.core.security.jwt_keys", "message": "Token decode error: Token verification failed with all keys. Errors: Key d193c25a-f21b-46f7-a864-af99c6eb2746: Signature verification failed.", "module": "jwt_keys", "function": "decode_token", "line": 244}
{"timestamp": "2025-06-12T00:42:23.686071", "level": "ERROR", "logger": "backend.services.auth", "message": "JWT token validation failed: Failed to decode token: Token verification failed with all keys. Errors: Key d193c25a-f21b-46f7-a864-af99c6eb2746: Signature verification failed.", "module": "auth", "function": "authenticate_token", "line": 686}
{"timestamp": "2025-06-12T00:42:23.687892", "level": "WARNING", "logger": "backend.middleware.subscription_limits", "message": "Error authenticating token in subscription middleware: name 'ValidationError' is not defined", "module": "subscription_limits", "function": "_get_current_user", "line": 308}
{"timestamp": "2025-06-12T00:42:23.689299", "level": "WARNING", "logger": "backend.core.security.jwt_keys", "message": "Unknown JWT kid requested: 3243159b-5c44-442a-8031-b8ff0ae3140f", "module": "jwt_keys", "function": "get_verification_key", "line": 105}
{"timestamp": "2025-06-12T00:42:23.689368", "level": "WARNING", "logger": "backend.core.security.jwt_keys", "message": "Verification failed with key ID 3243159b-5c44-442a-8031-b8ff0ae3140f: Unknown kid: 3243159b-5c44-442a-8031-b8ff0ae3140f", "module": "jwt_keys", "function": "decode_token", "line": 203}
{"timestamp": "2025-06-12T00:42:23.689560", "level": "WARNING", "logger": "backend.core.security.jwt_keys", "message": "Verification failed with current key: Signature verification failed.", "module": "jwt_keys", "function": "decode_token", "line": 216}
{"timestamp": "2025-06-12T00:42:23.689736", "level": "ERROR", "logger": "backend.core.security.jwt_keys", "message": "Token verification failed with all keys. Errors: Key d193c25a-f21b-46f7-a864-af99c6eb2746: Signature verification failed.", "module": "jwt_keys", "function": "decode_token", "line": 239}
{"timestamp": "2025-06-12T00:42:23.689811", "level": "ERROR", "logger": "backend.core.security.jwt_keys", "message": "Token decode error: Token verification failed with all keys. Errors: Key d193c25a-f21b-46f7-a864-af99c6eb2746: Signature verification failed.", "module": "jwt_keys", "function": "decode_token", "line": 244}
{"timestamp": "2025-06-12T00:42:23.689890", "level": "ERROR", "logger": "backend.services.auth", "message": "JWT token validation failed: Failed to decode token: Token verification failed with all keys. Errors: Key d193c25a-f21b-46f7-a864-af99c6eb2746: Signature verification failed.", "module": "auth", "function": "authenticate_token", "line": 686}
{"timestamp": "2025-06-12T00:42:23.691933", "level": "WARNING", "logger": "backend.middleware.subscription_limits", "message": "Auth error in subscription middleware: name 'ValidationError' is not defined", "module": "subscription_limits", "function": "dispatch", "line": 234}
{"timestamp": "2025-06-12T00:42:23.692961", "level": "WARNING", "logger": "backend.core.security.jwt_keys", "message": "Unknown JWT kid requested: 3243159b-5c44-442a-8031-b8ff0ae3140f", "module": "jwt_keys", "function": "get_verification_key", "line": 105}
{"timestamp": "2025-06-12T00:42:23.693019", "level": "WARNING", "logger": "backend.core.security.jwt_keys", "message": "Verification failed with key ID 3243159b-5c44-442a-8031-b8ff0ae3140f: Unknown kid: 3243159b-5c44-442a-8031-b8ff0ae3140f", "module": "jwt_keys", "function": "decode_token", "line": 203}
{"timestamp": "2025-06-12T00:42:23.693540", "level": "WARNING", "logger": "backend.core.security.jwt_keys", "message": "Verification failed with current key: Signature verification failed.", "module": "jwt_keys", "function": "decode_token", "line": 216}
{"timestamp": "2025-06-12T00:42:23.693703", "level": "ERROR", "logger": "backend.core.security.jwt_keys", "message": "Token verification failed with all keys. Errors: Key d193c25a-f21b-46f7-a864-af99c6eb2746: Signature verification failed.", "module": "jwt_keys", "function": "decode_token", "line": 239}
{"timestamp": "2025-06-12T00:42:23.694018", "level": "ERROR", "logger": "backend.core.security.jwt_keys", "message": "Token decode error: Token verification failed with all keys. Errors: Key d193c25a-f21b-46f7-a864-af99c6eb2746: Signature verification failed.", "module": "jwt_keys", "function": "decode_token", "line": 244}
{"timestamp": "2025-06-12T00:42:23.694781", "level": "ERROR", "logger": "backend.services.auth", "message": "JWT token validation failed: Failed to decode token: Token verification failed with all keys. Errors: Key d193c25a-f21b-46f7-a864-af99c6eb2746: Signature verification failed.", "module": "auth", "function": "authenticate_token", "line": 686}
{"timestamp": "2025-06-12T00:43:18.122798", "level": "WARNING", "logger": "backend.core.security.jwt_keys", "message": "Unknown JWT kid requested: 3243159b-5c44-442a-8031-b8ff0ae3140f", "module": "jwt_keys", "function": "get_verification_key", "line": 105}
{"timestamp": "2025-06-12T00:43:18.124306", "level": "WARNING", "logger": "backend.core.security.jwt_keys", "message": "Verification failed with key ID 3243159b-5c44-442a-8031-b8ff0ae3140f: Unknown kid: 3243159b-5c44-442a-8031-b8ff0ae3140f", "module": "jwt_keys", "function": "decode_token", "line": 203}
{"timestamp": "2025-06-12T00:43:18.126171", "level": "WARNING", "logger": "backend.core.security.jwt_keys", "message": "Verification failed with current key: Signature verification failed.", "module": "jwt_keys", "function": "decode_token", "line": 216}
{"timestamp": "2025-06-12T00:43:18.126892", "level": "ERROR", "logger": "backend.core.security.jwt_keys", "message": "Token verification failed with all keys. Errors: Key d193c25a-f21b-46f7-a864-af99c6eb2746: Signature verification failed.", "module": "jwt_keys", "function": "decode_token", "line": 239}
{"timestamp": "2025-06-12T00:43:18.127045", "level": "ERROR", "logger": "backend.core.security.jwt_keys", "message": "Token decode error: Token verification failed with all keys. Errors: Key d193c25a-f21b-46f7-a864-af99c6eb2746: Signature verification failed.", "module": "jwt_keys", "function": "decode_token", "line": 244}
{"timestamp": "2025-06-12T00:43:18.127539", "level": "ERROR", "logger": "backend.services.auth", "message": "JWT token validation failed: Failed to decode token: Token verification failed with all keys. Errors: Key d193c25a-f21b-46f7-a864-af99c6eb2746: Signature verification failed.", "module": "auth", "function": "authenticate_token", "line": 686}
{"timestamp": "2025-06-12T00:43:18.129811", "level": "WARNING", "logger": "backend.core.security.jwt_keys", "message": "Unknown JWT kid requested: 3243159b-5c44-442a-8031-b8ff0ae3140f", "module": "jwt_keys", "function": "get_verification_key", "line": 105}
{"timestamp": "2025-06-12T00:43:18.129876", "level": "WARNING", "logger": "backend.core.security.jwt_keys", "message": "Verification failed with key ID 3243159b-5c44-442a-8031-b8ff0ae3140f: Unknown kid: 3243159b-5c44-442a-8031-b8ff0ae3140f", "module": "jwt_keys", "function": "decode_token", "line": 203}
{"timestamp": "2025-06-12T00:43:18.130115", "level": "WARNING", "logger": "backend.core.security.jwt_keys", "message": "Verification failed with current key: Signature verification failed.", "module": "jwt_keys", "function": "decode_token", "line": 216}
{"timestamp": "2025-06-12T00:43:18.130414", "level": "ERROR", "logger": "backend.core.security.jwt_keys", "message": "Token verification failed with all keys. Errors: Key d193c25a-f21b-46f7-a864-af99c6eb2746: Signature verification failed.", "module": "jwt_keys", "function": "decode_token", "line": 239}
{"timestamp": "2025-06-12T00:43:18.130502", "level": "ERROR", "logger": "backend.core.security.jwt_keys", "message": "Token decode error: Token verification failed with all keys. Errors: Key d193c25a-f21b-46f7-a864-af99c6eb2746: Signature verification failed.", "module": "jwt_keys", "function": "decode_token", "line": 244}
{"timestamp": "2025-06-12T00:43:18.130611", "level": "ERROR", "logger": "backend.auth", "message": "Token validation error: Failed to decode token: Token verification failed with all keys. Errors: Key d193c25a-f21b-46f7-a864-af99c6eb2746: Signature verification failed.", "module": "__init__", "function": "get_current_user", "line": 193}
{"timestamp": "2025-06-12T00:43:18.133494", "level": "WARNING", "logger": "backend.middleware.subscription_limits", "message": "Error authenticating token in subscription middleware: name 'ValidationError' is not defined", "module": "subscription_limits", "function": "_get_current_user", "line": 308}
{"timestamp": "2025-06-12T00:43:18.148764", "level": "WARNING", "logger": "backend.core.security.jwt_keys", "message": "Unknown JWT kid requested: 3243159b-5c44-442a-8031-b8ff0ae3140f", "module": "jwt_keys", "function": "get_verification_key", "line": 105}
{"timestamp": "2025-06-12T00:43:18.148844", "level": "WARNING", "logger": "backend.core.security.jwt_keys", "message": "Verification failed with key ID 3243159b-5c44-442a-8031-b8ff0ae3140f: Unknown kid: 3243159b-5c44-442a-8031-b8ff0ae3140f", "module": "jwt_keys", "function": "decode_token", "line": 203}
{"timestamp": "2025-06-12T00:43:18.149395", "level": "WARNING", "logger": "backend.core.security.jwt_keys", "message": "Verification failed with current key: Signature verification failed.", "module": "jwt_keys", "function": "decode_token", "line": 216}
{"timestamp": "2025-06-12T00:43:18.149583", "level": "ERROR", "logger": "backend.core.security.jwt_keys", "message": "Token verification failed with all keys. Errors: Key d193c25a-f21b-46f7-a864-af99c6eb2746: Signature verification failed.", "module": "jwt_keys", "function": "decode_token", "line": 239}
{"timestamp": "2025-06-12T00:43:18.149669", "level": "ERROR", "logger": "backend.core.security.jwt_keys", "message": "Token decode error: Token verification failed with all keys. Errors: Key d193c25a-f21b-46f7-a864-af99c6eb2746: Signature verification failed.", "module": "jwt_keys", "function": "decode_token", "line": 244}
{"timestamp": "2025-06-12T00:43:18.149744", "level": "ERROR", "logger": "backend.services.auth", "message": "JWT token validation failed: Failed to decode token: Token verification failed with all keys. Errors: Key d193c25a-f21b-46f7-a864-af99c6eb2746: Signature verification failed.", "module": "auth", "function": "authenticate_token", "line": 686}
{"timestamp": "2025-06-12T00:43:18.150902", "level": "WARNING", "logger": "backend.core.security.jwt_keys", "message": "Unknown JWT kid requested: 3243159b-5c44-442a-8031-b8ff0ae3140f", "module": "jwt_keys", "function": "get_verification_key", "line": 105}
{"timestamp": "2025-06-12T00:43:18.151026", "level": "WARNING", "logger": "backend.core.security.jwt_keys", "message": "Verification failed with key ID 3243159b-5c44-442a-8031-b8ff0ae3140f: Unknown kid: 3243159b-5c44-442a-8031-b8ff0ae3140f", "module": "jwt_keys", "function": "decode_token", "line": 203}
{"timestamp": "2025-06-12T00:43:18.151227", "level": "WARNING", "logger": "backend.core.security.jwt_keys", "message": "Verification failed with current key: Signature verification failed.", "module": "jwt_keys", "function": "decode_token", "line": 216}
{"timestamp": "2025-06-12T00:43:18.152764", "level": "ERROR", "logger": "backend.core.security.jwt_keys", "message": "Token verification failed with all keys. Errors: Key d193c25a-f21b-46f7-a864-af99c6eb2746: Signature verification failed.", "module": "jwt_keys", "function": "decode_token", "line": 239}
{"timestamp": "2025-06-12T00:43:18.152845", "level": "ERROR", "logger": "backend.core.security.jwt_keys", "message": "Token decode error: Token verification failed with all keys. Errors: Key d193c25a-f21b-46f7-a864-af99c6eb2746: Signature verification failed.", "module": "jwt_keys", "function": "decode_token", "line": 244}
{"timestamp": "2025-06-12T00:43:18.153425", "level": "ERROR", "logger": "backend.auth", "message": "Token validation error: Failed to decode token: Token verification failed with all keys. Errors: Key d193c25a-f21b-46f7-a864-af99c6eb2746: Signature verification failed.", "module": "__init__", "function": "get_current_user", "line": 193}
{"timestamp": "2025-06-12T00:43:18.154483", "level": "WARNING", "logger": "backend.middleware.subscription_limits", "message": "Error authenticating token in subscription middleware: name 'ValidationError' is not defined", "module": "subscription_limits", "function": "_get_current_user", "line": 308}
{"timestamp": "2025-06-12T00:43:18.155277", "level": "WARNING", "logger": "backend.core.security.jwt_keys", "message": "Unknown JWT kid requested: 3243159b-5c44-442a-8031-b8ff0ae3140f", "module": "jwt_keys", "function": "get_verification_key", "line": 105}
{"timestamp": "2025-06-12T00:43:18.155520", "level": "WARNING", "logger": "backend.core.security.jwt_keys", "message": "Verification failed with key ID 3243159b-5c44-442a-8031-b8ff0ae3140f: Unknown kid: 3243159b-5c44-442a-8031-b8ff0ae3140f", "module": "jwt_keys", "function": "decode_token", "line": 203}
{"timestamp": "2025-06-12T00:43:18.155770", "level": "WARNING", "logger": "backend.core.security.jwt_keys", "message": "Verification failed with current key: Signature verification failed.", "module": "jwt_keys", "function": "decode_token", "line": 216}
{"timestamp": "2025-06-12T00:43:18.156059", "level": "ERROR", "logger": "backend.core.security.jwt_keys", "message": "Token verification failed with all keys. Errors: Key d193c25a-f21b-46f7-a864-af99c6eb2746: Signature verification failed.", "module": "jwt_keys", "function": "decode_token", "line": 239}
{"timestamp": "2025-06-12T00:43:18.156238", "level": "ERROR", "logger": "backend.core.security.jwt_keys", "message": "Token decode error: Token verification failed with all keys. Errors: Key d193c25a-f21b-46f7-a864-af99c6eb2746: Signature verification failed.", "module": "jwt_keys", "function": "decode_token", "line": 244}
{"timestamp": "2025-06-12T00:43:18.156336", "level": "ERROR", "logger": "backend.auth", "message": "Token validation error: Failed to decode token: Token verification failed with all keys. Errors: Key d193c25a-f21b-46f7-a864-af99c6eb2746: Signature verification failed.", "module": "__init__", "function": "get_current_user", "line": 193}
{"timestamp": "2025-06-12T00:43:24.721455", "level": "INFO", "logger": "backend.services.enhanced_rate_limit", "message": "Auth event for anonymous: login_attempt (failure)", "module": "enhanced_rate_limit", "function": "track_auth_event", "line": 182}
{"timestamp": "2025-06-12T00:43:25.867431", "level": "INFO", "logger": "backend.services.auth", "message": "Authentication successful for user: <EMAIL>", "module": "auth", "function": "authenticate_user", "line": 266}
{"timestamp": "2025-06-12T00:43:25.871645", "level": "INFO", "logger": "backend.routes.auth", "message": "User logged in successfully: <EMAIL>", "module": "auth", "function": "login", "line": 218}
{"timestamp": "2025-06-12T00:43:25.873037", "level": "INFO", "logger": "backend.services.enhanced_rate_limit", "message": "Auth event for user e4ae2d8f-b32f-52d7-99cd-8a33b19652c7: login_success (success)", "module": "enhanced_rate_limit", "function": "track_auth_event", "line": 182}
{"timestamp": "2025-06-12T00:43:25.913526", "level": "INFO", "logger": "backend.services.adaptive_session", "message": "Adaptive session for user e4ae2d8f-b32f-52d7-99cd-8a33b19652c7: risk=low, duration=86400s", "module": "adaptive_session", "function": "get_session_expiry", "line": 161}
{"timestamp": "2025-06-12T00:43:25.913628", "level": "INFO", "logger": "backend.routes.auth", "message": "Adaptive session <NAME_EMAIL>: duration=86400s", "module": "auth", "function": "login", "line": 279}
{"timestamp": "2025-06-12T00:43:25.913690", "level": "INFO", "logger": "backend.routes.auth", "message": "Bearer token authentication issued <NAME_EMAIL>", "module": "auth", "function": "login", "line": 284}
{"timestamp": "2025-06-12T00:51:24.219674", "level": "INFO", "logger": "backend.services.qr_preview_cache", "message": "QR preview cache cleanup task stopped", "module": "qr_preview_cache", "function": "stop", "line": 257}
{"timestamp": "2025-06-12T00:51:24.221007", "level": "INFO", "logger": "backend.main", "message": "QR preview cache stopped", "module": "main", "function": "shutdown_qr_preview_cache", "line": 352}
{"timestamp": "2025-06-12T00:51:28.435799", "level": "INFO", "logger": "backend.main", "message": "=== QRVibe API Startup: Logging to file is enabled ===", "module": "main", "function": "<module>", "line": 37}
{"timestamp": "2025-06-12T00:51:28.436002", "level": "INFO", "logger": "backend.main", "message": "Applying critical patches before initialization", "module": "main", "function": "<module>", "line": 43}
{"timestamp": "2025-06-12T00:51:28.436080", "level": "INFO", "logger": "backend.patches", "message": "Applying patches (none required)", "module": "__init__", "function": "apply_patches", "line": 11}
{"timestamp": "2025-06-12T00:51:28.436312", "level": "INFO", "logger": "backend.core.middleware.cors", "message": "Setting up CORS middleware with origins: ['http://localhost:3000', 'https://localhost:3000', 'http://127.0.0.1:3000', 'https://127.0.0.1:3000']", "module": "cors", "function": "setup_cors", "line": 86}
{"timestamp": "2025-06-12T00:51:28.438416", "level": "INFO", "logger": "backend.core.middleware.cors", "message": "CORS middleware configured - preflight requests should be handled automatically", "module": "cors", "function": "setup_cors", "line": 98}
{"timestamp": "2025-06-12T00:51:28.439935", "level": "INFO", "logger": "backend.middleware.security_middleware", "message": "Added Enhanced Security Middleware with tiered rate limiting", "module": "security_middleware", "function": "add_security_middleware", "line": 176}
{"timestamp": "2025-06-12T00:51:28.440074", "level": "INFO", "logger": "backend.main", "message": "Mounting storage directory: /Users/<USER>/aihub/qrvibe/storage", "module": "main", "function": "<module>", "line": 107}
{"timestamp": "2025-06-12T00:51:28.440225", "level": "INFO", "logger": "backend.main", "message": "Storage directory absolute path: /Users/<USER>/aihub/qrvibe/storage", "module": "main", "function": "<module>", "line": 119}
{"timestamp": "2025-06-12T00:51:28.843727", "level": "INFO", "logger": "root", "message": "Storage initialized: environment=test, storage_dir=/Users/<USER>/aihub/qrvibe/storage, s3_enabled=True", "module": "storage", "function": "__init__", "line": 41}
{"timestamp": "2025-06-12T00:51:28.846595", "level": "INFO", "logger": "root", "message": "S3 storage enabled with bucket: qrverse and CDN: https://dev-cdn.qrvibe.com", "module": "storage", "function": "__init__", "line": 59}
{"timestamp": "2025-06-12T00:51:28.892137", "level": "INFO", "logger": "root", "message": "Storage initialized: environment=test, storage_dir=/Users/<USER>/aihub/qrvibe/storage, s3_enabled=True", "module": "storage", "function": "__init__", "line": 41}
{"timestamp": "2025-06-12T00:51:28.895259", "level": "INFO", "logger": "root", "message": "S3 storage enabled with bucket: qrverse and CDN: https://dev-cdn.qrvibe.com", "module": "storage", "function": "__init__", "line": 59}
{"timestamp": "2025-06-12T00:51:28.923798", "level": "INFO", "logger": "backend.middleware.security_middleware", "message": "Enhanced security middleware initialized with tiered rate limiting", "module": "security_middleware", "function": "__init__", "line": 87}
{"timestamp": "2025-06-12T00:51:28.923914", "level": "INFO", "logger": "backend.core.middleware.csrf", "message": "CSRF Protection Middleware initialized with exclude paths: ['/auth/jwt/login', '/auth/logout', '/auth/register', '/auth/2fa', '/auth/me', '/auth/verify-email', '/auth/reset-password', '/admin/login', '/admin/logout']", "module": "csrf", "function": "__init__", "line": 44}
{"timestamp": "2025-06-12T00:51:28.929224", "level": "INFO", "logger": "backend.services.token_blacklist", "message": "Token blacklist service initialized with Redis", "module": "token_blacklist", "function": "initialize", "line": 59}
{"timestamp": "2025-06-12T00:51:28.929299", "level": "INFO", "logger": "backend.main", "message": "Token blacklist service initialized", "module": "main", "function": "initialize_token_blacklist", "line": 98}
{"timestamp": "2025-06-12T00:51:28.929922", "level": "INFO", "logger": "backend.services.qr_preview_cache", "message": "QR preview cache initialized at /Users/<USER>/aihub/qrvibe/storage/cache/qr_preview_cache.db", "module": "qr_preview_cache", "function": "_init_db", "line": 56}
{"timestamp": "2025-06-12T00:51:28.930006", "level": "INFO", "logger": "backend.services.qr_preview_cache", "message": "Started QR preview cache cleanup task", "module": "qr_preview_cache", "function": "start_cleanup_task", "line": 62}
{"timestamp": "2025-06-12T00:51:28.930584", "level": "INFO", "logger": "backend.main", "message": "QR preview cache initialized and cleanup task started", "module": "main", "function": "init_qr_preview_cache", "line": 345}
{"timestamp": "2025-06-12T00:51:38.450955", "level": "INFO", "logger": "backend.services.qr_preview_cache", "message": "QR preview cache cleanup task stopped", "module": "qr_preview_cache", "function": "stop", "line": 257}
{"timestamp": "2025-06-12T00:51:38.451302", "level": "INFO", "logger": "backend.main", "message": "QR preview cache stopped", "module": "main", "function": "shutdown_qr_preview_cache", "line": 352}
{"timestamp": "2025-06-12T00:51:41.905460", "level": "INFO", "logger": "backend.main", "message": "=== QRVibe API Startup: Logging to file is enabled ===", "module": "main", "function": "<module>", "line": 37}
{"timestamp": "2025-06-12T00:51:41.905570", "level": "INFO", "logger": "backend.main", "message": "Applying critical patches before initialization", "module": "main", "function": "<module>", "line": 43}
{"timestamp": "2025-06-12T00:51:41.905633", "level": "INFO", "logger": "backend.patches", "message": "Applying patches (none required)", "module": "__init__", "function": "apply_patches", "line": 11}
{"timestamp": "2025-06-12T00:51:41.905937", "level": "INFO", "logger": "backend.core.middleware.cors", "message": "Setting up CORS middleware with origins: ['https://127.0.0.1:3000', 'http://127.0.0.1:3000', 'https://localhost:3000', 'http://localhost:3000']", "module": "cors", "function": "setup_cors", "line": 86}
{"timestamp": "2025-06-12T00:51:41.906019", "level": "INFO", "logger": "backend.core.middleware.cors", "message": "CORS middleware configured - preflight requests should be handled automatically", "module": "cors", "function": "setup_cors", "line": 98}
{"timestamp": "2025-06-12T00:51:41.907101", "level": "INFO", "logger": "backend.middleware.security_middleware", "message": "Added Enhanced Security Middleware with tiered rate limiting", "module": "security_middleware", "function": "add_security_middleware", "line": 176}
{"timestamp": "2025-06-12T00:51:41.907206", "level": "INFO", "logger": "backend.main", "message": "Mounting storage directory: /Users/<USER>/aihub/qrvibe/storage", "module": "main", "function": "<module>", "line": 107}
{"timestamp": "2025-06-12T00:51:41.907323", "level": "INFO", "logger": "backend.main", "message": "Storage directory absolute path: /Users/<USER>/aihub/qrvibe/storage", "module": "main", "function": "<module>", "line": 119}
{"timestamp": "2025-06-12T00:51:42.354695", "level": "INFO", "logger": "root", "message": "Storage initialized: environment=test, storage_dir=/Users/<USER>/aihub/qrvibe/storage, s3_enabled=True", "module": "storage", "function": "__init__", "line": 41}
{"timestamp": "2025-06-12T00:51:42.358492", "level": "INFO", "logger": "root", "message": "S3 storage enabled with bucket: qrverse and CDN: https://dev-cdn.qrvibe.com", "module": "storage", "function": "__init__", "line": 59}
{"timestamp": "2025-06-12T00:51:42.405825", "level": "INFO", "logger": "root", "message": "Storage initialized: environment=test, storage_dir=/Users/<USER>/aihub/qrvibe/storage, s3_enabled=True", "module": "storage", "function": "__init__", "line": 41}
{"timestamp": "2025-06-12T00:51:42.409081", "level": "INFO", "logger": "root", "message": "S3 storage enabled with bucket: qrverse and CDN: https://dev-cdn.qrvibe.com", "module": "storage", "function": "__init__", "line": 59}
{"timestamp": "2025-06-12T00:51:42.435343", "level": "INFO", "logger": "backend.middleware.security_middleware", "message": "Enhanced security middleware initialized with tiered rate limiting", "module": "security_middleware", "function": "__init__", "line": 87}
{"timestamp": "2025-06-12T00:51:42.435464", "level": "INFO", "logger": "backend.core.middleware.csrf", "message": "CSRF Protection Middleware initialized with exclude paths: ['/auth/jwt/login', '/auth/logout', '/auth/register', '/auth/2fa', '/auth/me', '/auth/verify-email', '/auth/reset-password', '/admin/login', '/admin/logout']", "module": "csrf", "function": "__init__", "line": 44}
{"timestamp": "2025-06-12T00:51:42.440224", "level": "INFO", "logger": "backend.services.token_blacklist", "message": "Token blacklist service initialized with Redis", "module": "token_blacklist", "function": "initialize", "line": 59}
{"timestamp": "2025-06-12T00:51:42.440328", "level": "INFO", "logger": "backend.main", "message": "Token blacklist service initialized", "module": "main", "function": "initialize_token_blacklist", "line": 98}
{"timestamp": "2025-06-12T00:51:42.441056", "level": "INFO", "logger": "backend.services.qr_preview_cache", "message": "QR preview cache initialized at /Users/<USER>/aihub/qrvibe/storage/cache/qr_preview_cache.db", "module": "qr_preview_cache", "function": "_init_db", "line": 56}
{"timestamp": "2025-06-12T00:51:42.441138", "level": "INFO", "logger": "backend.services.qr_preview_cache", "message": "Started QR preview cache cleanup task", "module": "qr_preview_cache", "function": "start_cleanup_task", "line": 62}
{"timestamp": "2025-06-12T00:51:42.441204", "level": "INFO", "logger": "backend.main", "message": "QR preview cache initialized and cleanup task started", "module": "main", "function": "init_qr_preview_cache", "line": 345}
{"timestamp": "2025-06-12T00:51:54.035215", "level": "INFO", "logger": "backend.services.qr_preview_cache", "message": "QR preview cache cleanup task stopped", "module": "qr_preview_cache", "function": "stop", "line": 257}
{"timestamp": "2025-06-12T00:51:54.035804", "level": "INFO", "logger": "backend.main", "message": "QR preview cache stopped", "module": "main", "function": "shutdown_qr_preview_cache", "line": 352}
{"timestamp": "2025-06-12T00:51:57.717579", "level": "INFO", "logger": "backend.main", "message": "=== QRVibe API Startup: Logging to file is enabled ===", "module": "main", "function": "<module>", "line": 37}
{"timestamp": "2025-06-12T00:51:57.717689", "level": "INFO", "logger": "backend.main", "message": "Applying critical patches before initialization", "module": "main", "function": "<module>", "line": 43}
{"timestamp": "2025-06-12T00:51:57.717749", "level": "INFO", "logger": "backend.patches", "message": "Applying patches (none required)", "module": "__init__", "function": "apply_patches", "line": 11}
{"timestamp": "2025-06-12T00:51:57.717971", "level": "INFO", "logger": "backend.core.middleware.cors", "message": "Setting up CORS middleware with origins: ['https://127.0.0.1:3000', 'http://localhost:3000', 'http://127.0.0.1:3000', 'https://localhost:3000']", "module": "cors", "function": "setup_cors", "line": 86}
{"timestamp": "2025-06-12T00:51:57.735099", "level": "INFO", "logger": "backend.core.middleware.cors", "message": "CORS middleware configured - preflight requests should be handled automatically", "module": "cors", "function": "setup_cors", "line": 98}
{"timestamp": "2025-06-12T00:51:57.736399", "level": "INFO", "logger": "backend.middleware.security_middleware", "message": "Added Enhanced Security Middleware with tiered rate limiting", "module": "security_middleware", "function": "add_security_middleware", "line": 176}
{"timestamp": "2025-06-12T00:51:57.736511", "level": "INFO", "logger": "backend.main", "message": "Mounting storage directory: /Users/<USER>/aihub/qrvibe/storage", "module": "main", "function": "<module>", "line": 107}
{"timestamp": "2025-06-12T00:51:57.736635", "level": "INFO", "logger": "backend.main", "message": "Storage directory absolute path: /Users/<USER>/aihub/qrvibe/storage", "module": "main", "function": "<module>", "line": 119}
{"timestamp": "2025-06-12T00:51:58.139311", "level": "INFO", "logger": "root", "message": "Storage initialized: environment=test, storage_dir=/Users/<USER>/aihub/qrvibe/storage, s3_enabled=True", "module": "storage", "function": "__init__", "line": 41}
{"timestamp": "2025-06-12T00:51:58.142053", "level": "INFO", "logger": "root", "message": "S3 storage enabled with bucket: qrverse and CDN: https://dev-cdn.qrvibe.com", "module": "storage", "function": "__init__", "line": 59}
{"timestamp": "2025-06-12T00:51:58.187322", "level": "INFO", "logger": "root", "message": "Storage initialized: environment=test, storage_dir=/Users/<USER>/aihub/qrvibe/storage, s3_enabled=True", "module": "storage", "function": "__init__", "line": 41}
{"timestamp": "2025-06-12T00:51:58.190262", "level": "INFO", "logger": "root", "message": "S3 storage enabled with bucket: qrverse and CDN: https://dev-cdn.qrvibe.com", "module": "storage", "function": "__init__", "line": 59}
{"timestamp": "2025-06-12T00:51:58.229761", "level": "INFO", "logger": "backend.middleware.security_middleware", "message": "Enhanced security middleware initialized with tiered rate limiting", "module": "security_middleware", "function": "__init__", "line": 87}
{"timestamp": "2025-06-12T00:51:58.230435", "level": "INFO", "logger": "backend.core.middleware.csrf", "message": "CSRF Protection Middleware initialized with exclude paths: ['/auth/jwt/login', '/auth/logout', '/auth/register', '/auth/2fa', '/auth/me', '/auth/verify-email', '/auth/reset-password', '/admin/login', '/admin/logout']", "module": "csrf", "function": "__init__", "line": 44}
{"timestamp": "2025-06-12T00:51:58.237696", "level": "INFO", "logger": "backend.services.token_blacklist", "message": "Token blacklist service initialized with Redis", "module": "token_blacklist", "function": "initialize", "line": 59}
{"timestamp": "2025-06-12T00:51:58.237791", "level": "INFO", "logger": "backend.main", "message": "Token blacklist service initialized", "module": "main", "function": "initialize_token_blacklist", "line": 98}
{"timestamp": "2025-06-12T00:51:58.238501", "level": "INFO", "logger": "backend.services.qr_preview_cache", "message": "QR preview cache initialized at /Users/<USER>/aihub/qrvibe/storage/cache/qr_preview_cache.db", "module": "qr_preview_cache", "function": "_init_db", "line": 56}
{"timestamp": "2025-06-12T00:51:58.238596", "level": "INFO", "logger": "backend.services.qr_preview_cache", "message": "Started QR preview cache cleanup task", "module": "qr_preview_cache", "function": "start_cleanup_task", "line": 62}
{"timestamp": "2025-06-12T00:51:58.238659", "level": "INFO", "logger": "backend.main", "message": "QR preview cache initialized and cleanup task started", "module": "main", "function": "init_qr_preview_cache", "line": 345}
{"timestamp": "2025-06-12T00:52:10.004609", "level": "INFO", "logger": "backend.services.qr_preview_cache", "message": "QR preview cache cleanup task stopped", "module": "qr_preview_cache", "function": "stop", "line": 257}
{"timestamp": "2025-06-12T00:52:10.004992", "level": "INFO", "logger": "backend.main", "message": "QR preview cache stopped", "module": "main", "function": "shutdown_qr_preview_cache", "line": 352}
{"timestamp": "2025-06-12T00:52:13.386059", "level": "INFO", "logger": "backend.main", "message": "=== QRVibe API Startup: Logging to file is enabled ===", "module": "main", "function": "<module>", "line": 37}
{"timestamp": "2025-06-12T00:52:13.386151", "level": "INFO", "logger": "backend.main", "message": "Applying critical patches before initialization", "module": "main", "function": "<module>", "line": 43}
{"timestamp": "2025-06-12T00:52:13.386210", "level": "INFO", "logger": "backend.patches", "message": "Applying patches (none required)", "module": "__init__", "function": "apply_patches", "line": 11}
{"timestamp": "2025-06-12T00:52:13.386435", "level": "INFO", "logger": "backend.core.middleware.cors", "message": "Setting up CORS middleware with origins: ['https://localhost:3000', 'https://127.0.0.1:3000', 'http://localhost:3000', 'http://127.0.0.1:3000']", "module": "cors", "function": "setup_cors", "line": 86}
{"timestamp": "2025-06-12T00:52:13.387361", "level": "INFO", "logger": "backend.core.middleware.cors", "message": "CORS middleware configured - preflight requests should be handled automatically", "module": "cors", "function": "setup_cors", "line": 98}
{"timestamp": "2025-06-12T00:52:13.388504", "level": "INFO", "logger": "backend.middleware.security_middleware", "message": "Added Enhanced Security Middleware with tiered rate limiting", "module": "security_middleware", "function": "add_security_middleware", "line": 176}
{"timestamp": "2025-06-12T00:52:13.388603", "level": "INFO", "logger": "backend.main", "message": "Mounting storage directory: /Users/<USER>/aihub/qrvibe/storage", "module": "main", "function": "<module>", "line": 107}
{"timestamp": "2025-06-12T00:52:13.388713", "level": "INFO", "logger": "backend.main", "message": "Storage directory absolute path: /Users/<USER>/aihub/qrvibe/storage", "module": "main", "function": "<module>", "line": 119}
{"timestamp": "2025-06-12T00:52:13.809576", "level": "INFO", "logger": "root", "message": "Storage initialized: environment=test, storage_dir=/Users/<USER>/aihub/qrvibe/storage, s3_enabled=True", "module": "storage", "function": "__init__", "line": 41}
{"timestamp": "2025-06-12T00:52:13.812344", "level": "INFO", "logger": "root", "message": "S3 storage enabled with bucket: qrverse and CDN: https://dev-cdn.qrvibe.com", "module": "storage", "function": "__init__", "line": 59}
{"timestamp": "2025-06-12T00:52:13.862450", "level": "INFO", "logger": "root", "message": "Storage initialized: environment=test, storage_dir=/Users/<USER>/aihub/qrvibe/storage, s3_enabled=True", "module": "storage", "function": "__init__", "line": 41}
{"timestamp": "2025-06-12T00:52:13.866756", "level": "INFO", "logger": "root", "message": "S3 storage enabled with bucket: qrverse and CDN: https://dev-cdn.qrvibe.com", "module": "storage", "function": "__init__", "line": 59}
{"timestamp": "2025-06-12T00:52:13.907511", "level": "INFO", "logger": "backend.middleware.security_middleware", "message": "Enhanced security middleware initialized with tiered rate limiting", "module": "security_middleware", "function": "__init__", "line": 87}
{"timestamp": "2025-06-12T00:52:13.907650", "level": "INFO", "logger": "backend.core.middleware.csrf", "message": "CSRF Protection Middleware initialized with exclude paths: ['/auth/jwt/login', '/auth/logout', '/auth/register', '/auth/2fa', '/auth/me', '/auth/verify-email', '/auth/reset-password', '/admin/login', '/admin/logout']", "module": "csrf", "function": "__init__", "line": 44}
{"timestamp": "2025-06-12T00:52:13.912845", "level": "INFO", "logger": "backend.services.token_blacklist", "message": "Token blacklist service initialized with Redis", "module": "token_blacklist", "function": "initialize", "line": 59}
{"timestamp": "2025-06-12T00:52:13.912961", "level": "INFO", "logger": "backend.main", "message": "Token blacklist service initialized", "module": "main", "function": "initialize_token_blacklist", "line": 98}
{"timestamp": "2025-06-12T00:52:13.913779", "level": "INFO", "logger": "backend.services.qr_preview_cache", "message": "QR preview cache initialized at /Users/<USER>/aihub/qrvibe/storage/cache/qr_preview_cache.db", "module": "qr_preview_cache", "function": "_init_db", "line": 56}
{"timestamp": "2025-06-12T00:52:13.913859", "level": "INFO", "logger": "backend.services.qr_preview_cache", "message": "Started QR preview cache cleanup task", "module": "qr_preview_cache", "function": "start_cleanup_task", "line": 62}
{"timestamp": "2025-06-12T00:52:13.913919", "level": "INFO", "logger": "backend.main", "message": "QR preview cache initialized and cleanup task started", "module": "main", "function": "init_qr_preview_cache", "line": 345}
{"timestamp": "2025-06-12T00:52:28.300966", "level": "INFO", "logger": "backend.services.qr_preview_cache", "message": "QR preview cache cleanup task stopped", "module": "qr_preview_cache", "function": "stop", "line": 257}
{"timestamp": "2025-06-12T00:52:28.301215", "level": "INFO", "logger": "backend.main", "message": "QR preview cache stopped", "module": "main", "function": "shutdown_qr_preview_cache", "line": 352}
{"timestamp": "2025-06-12T00:52:31.873110", "level": "INFO", "logger": "backend.main", "message": "=== QRVibe API Startup: Logging to file is enabled ===", "module": "main", "function": "<module>", "line": 37}
{"timestamp": "2025-06-12T00:52:31.873206", "level": "INFO", "logger": "backend.main", "message": "Applying critical patches before initialization", "module": "main", "function": "<module>", "line": 43}
{"timestamp": "2025-06-12T00:52:31.873330", "level": "INFO", "logger": "backend.patches", "message": "Applying patches (none required)", "module": "__init__", "function": "apply_patches", "line": 11}
{"timestamp": "2025-06-12T00:52:31.873645", "level": "INFO", "logger": "backend.core.middleware.cors", "message": "Setting up CORS middleware with origins: ['http://127.0.0.1:3000', 'https://127.0.0.1:3000', 'http://localhost:3000', 'https://localhost:3000']", "module": "cors", "function": "setup_cors", "line": 86}
{"timestamp": "2025-06-12T00:52:31.873713", "level": "INFO", "logger": "backend.core.middleware.cors", "message": "CORS middleware configured - preflight requests should be handled automatically", "module": "cors", "function": "setup_cors", "line": 98}
{"timestamp": "2025-06-12T00:52:31.874874", "level": "INFO", "logger": "backend.middleware.security_middleware", "message": "Added Enhanced Security Middleware with tiered rate limiting", "module": "security_middleware", "function": "add_security_middleware", "line": 176}
{"timestamp": "2025-06-12T00:52:31.875007", "level": "INFO", "logger": "backend.main", "message": "Mounting storage directory: /Users/<USER>/aihub/qrvibe/storage", "module": "main", "function": "<module>", "line": 107}
{"timestamp": "2025-06-12T00:52:31.875140", "level": "INFO", "logger": "backend.main", "message": "Storage directory absolute path: /Users/<USER>/aihub/qrvibe/storage", "module": "main", "function": "<module>", "line": 119}
{"timestamp": "2025-06-12T00:52:32.311026", "level": "INFO", "logger": "root", "message": "Storage initialized: environment=test, storage_dir=/Users/<USER>/aihub/qrvibe/storage, s3_enabled=True", "module": "storage", "function": "__init__", "line": 41}
{"timestamp": "2025-06-12T00:52:32.314379", "level": "INFO", "logger": "root", "message": "S3 storage enabled with bucket: qrverse and CDN: https://dev-cdn.qrvibe.com", "module": "storage", "function": "__init__", "line": 59}
{"timestamp": "2025-06-12T00:52:32.365336", "level": "INFO", "logger": "root", "message": "Storage initialized: environment=test, storage_dir=/Users/<USER>/aihub/qrvibe/storage, s3_enabled=True", "module": "storage", "function": "__init__", "line": 41}
{"timestamp": "2025-06-12T00:52:32.369281", "level": "INFO", "logger": "root", "message": "S3 storage enabled with bucket: qrverse and CDN: https://dev-cdn.qrvibe.com", "module": "storage", "function": "__init__", "line": 59}
{"timestamp": "2025-06-12T00:52:32.399051", "level": "INFO", "logger": "backend.middleware.security_middleware", "message": "Enhanced security middleware initialized with tiered rate limiting", "module": "security_middleware", "function": "__init__", "line": 87}
{"timestamp": "2025-06-12T00:52:32.399160", "level": "INFO", "logger": "backend.core.middleware.csrf", "message": "CSRF Protection Middleware initialized with exclude paths: ['/auth/jwt/login', '/auth/logout', '/auth/register', '/auth/2fa', '/auth/me', '/auth/verify-email', '/auth/reset-password', '/admin/login', '/admin/logout']", "module": "csrf", "function": "__init__", "line": 44}
{"timestamp": "2025-06-12T00:52:32.403643", "level": "INFO", "logger": "backend.services.token_blacklist", "message": "Token blacklist service initialized with Redis", "module": "token_blacklist", "function": "initialize", "line": 59}
{"timestamp": "2025-06-12T00:52:32.403739", "level": "INFO", "logger": "backend.main", "message": "Token blacklist service initialized", "module": "main", "function": "initialize_token_blacklist", "line": 98}
{"timestamp": "2025-06-12T00:52:32.404501", "level": "INFO", "logger": "backend.services.qr_preview_cache", "message": "QR preview cache initialized at /Users/<USER>/aihub/qrvibe/storage/cache/qr_preview_cache.db", "module": "qr_preview_cache", "function": "_init_db", "line": 56}
{"timestamp": "2025-06-12T00:52:32.404642", "level": "INFO", "logger": "backend.services.qr_preview_cache", "message": "Started QR preview cache cleanup task", "module": "qr_preview_cache", "function": "start_cleanup_task", "line": 62}
{"timestamp": "2025-06-12T00:52:32.405230", "level": "INFO", "logger": "backend.main", "message": "QR preview cache initialized and cleanup task started", "module": "main", "function": "init_qr_preview_cache", "line": 345}
{"timestamp": "2025-06-12T00:52:43.030665", "level": "INFO", "logger": "backend.services.qr_preview_cache", "message": "QR preview cache cleanup task stopped", "module": "qr_preview_cache", "function": "stop", "line": 257}
{"timestamp": "2025-06-12T00:52:43.030925", "level": "INFO", "logger": "backend.main", "message": "QR preview cache stopped", "module": "main", "function": "shutdown_qr_preview_cache", "line": 352}
{"timestamp": "2025-06-12T00:52:46.504092", "level": "INFO", "logger": "backend.main", "message": "=== QRVibe API Startup: Logging to file is enabled ===", "module": "main", "function": "<module>", "line": 37}
{"timestamp": "2025-06-12T00:52:46.504179", "level": "INFO", "logger": "backend.main", "message": "Applying critical patches before initialization", "module": "main", "function": "<module>", "line": 43}
{"timestamp": "2025-06-12T00:52:46.504249", "level": "INFO", "logger": "backend.patches", "message": "Applying patches (none required)", "module": "__init__", "function": "apply_patches", "line": 11}
{"timestamp": "2025-06-12T00:52:46.504464", "level": "INFO", "logger": "backend.core.middleware.cors", "message": "Setting up CORS middleware with origins: ['http://localhost:3000', 'http://127.0.0.1:3000', 'https://localhost:3000', 'https://127.0.0.1:3000']", "module": "cors", "function": "setup_cors", "line": 86}
{"timestamp": "2025-06-12T00:52:46.505283", "level": "INFO", "logger": "backend.core.middleware.cors", "message": "CORS middleware configured - preflight requests should be handled automatically", "module": "cors", "function": "setup_cors", "line": 98}
{"timestamp": "2025-06-12T00:52:46.508253", "level": "INFO", "logger": "backend.middleware.security_middleware", "message": "Added Enhanced Security Middleware with tiered rate limiting", "module": "security_middleware", "function": "add_security_middleware", "line": 176}
{"timestamp": "2025-06-12T00:52:46.508400", "level": "INFO", "logger": "backend.main", "message": "Mounting storage directory: /Users/<USER>/aihub/qrvibe/storage", "module": "main", "function": "<module>", "line": 107}
{"timestamp": "2025-06-12T00:52:46.508544", "level": "INFO", "logger": "backend.main", "message": "Storage directory absolute path: /Users/<USER>/aihub/qrvibe/storage", "module": "main", "function": "<module>", "line": 119}
{"timestamp": "2025-06-12T00:52:46.957995", "level": "INFO", "logger": "root", "message": "Storage initialized: environment=test, storage_dir=/Users/<USER>/aihub/qrvibe/storage, s3_enabled=True", "module": "storage", "function": "__init__", "line": 41}
{"timestamp": "2025-06-12T00:52:46.962002", "level": "INFO", "logger": "root", "message": "S3 storage enabled with bucket: qrverse and CDN: https://dev-cdn.qrvibe.com", "module": "storage", "function": "__init__", "line": 59}
{"timestamp": "2025-06-12T00:52:47.014710", "level": "INFO", "logger": "root", "message": "Storage initialized: environment=test, storage_dir=/Users/<USER>/aihub/qrvibe/storage, s3_enabled=True", "module": "storage", "function": "__init__", "line": 41}
{"timestamp": "2025-06-12T00:52:47.018225", "level": "INFO", "logger": "root", "message": "S3 storage enabled with bucket: qrverse and CDN: https://dev-cdn.qrvibe.com", "module": "storage", "function": "__init__", "line": 59}
{"timestamp": "2025-06-12T00:52:47.048326", "level": "INFO", "logger": "backend.middleware.security_middleware", "message": "Enhanced security middleware initialized with tiered rate limiting", "module": "security_middleware", "function": "__init__", "line": 87}
{"timestamp": "2025-06-12T00:52:47.048432", "level": "INFO", "logger": "backend.core.middleware.csrf", "message": "CSRF Protection Middleware initialized with exclude paths: ['/auth/jwt/login', '/auth/logout', '/auth/register', '/auth/2fa', '/auth/me', '/auth/verify-email', '/auth/reset-password', '/admin/login', '/admin/logout']", "module": "csrf", "function": "__init__", "line": 44}
{"timestamp": "2025-06-12T00:52:47.053110", "level": "INFO", "logger": "backend.services.token_blacklist", "message": "Token blacklist service initialized with Redis", "module": "token_blacklist", "function": "initialize", "line": 59}
{"timestamp": "2025-06-12T00:52:47.053235", "level": "INFO", "logger": "backend.main", "message": "Token blacklist service initialized", "module": "main", "function": "initialize_token_blacklist", "line": 98}
{"timestamp": "2025-06-12T00:52:47.054762", "level": "INFO", "logger": "backend.services.qr_preview_cache", "message": "QR preview cache initialized at /Users/<USER>/aihub/qrvibe/storage/cache/qr_preview_cache.db", "module": "qr_preview_cache", "function": "_init_db", "line": 56}
{"timestamp": "2025-06-12T00:52:47.054993", "level": "INFO", "logger": "backend.services.qr_preview_cache", "message": "Started QR preview cache cleanup task", "module": "qr_preview_cache", "function": "start_cleanup_task", "line": 62}
{"timestamp": "2025-06-12T00:52:47.055096", "level": "INFO", "logger": "backend.main", "message": "QR preview cache initialized and cleanup task started", "module": "main", "function": "init_qr_preview_cache", "line": 345}
{"timestamp": "2025-06-12T00:53:10.948454", "level": "WARNING", "logger": "backend.core.security.jwt_keys", "message": "Unknown JWT kid requested: d193c25a-f21b-46f7-a864-af99c6eb2746", "module": "jwt_keys", "function": "get_verification_key", "line": 105}
{"timestamp": "2025-06-12T00:53:10.950052", "level": "WARNING", "logger": "backend.core.security.jwt_keys", "message": "Verification failed with key ID d193c25a-f21b-46f7-a864-af99c6eb2746: Unknown kid: d193c25a-f21b-46f7-a864-af99c6eb2746", "module": "jwt_keys", "function": "decode_token", "line": 203}
{"timestamp": "2025-06-12T00:53:10.991232", "level": "WARNING", "logger": "backend.core.security.jwt_keys", "message": "Verification failed with current key: Signature verification failed.", "module": "jwt_keys", "function": "decode_token", "line": 216}
{"timestamp": "2025-06-12T00:53:10.991600", "level": "ERROR", "logger": "backend.core.security.jwt_keys", "message": "Token verification failed with all keys. Errors: Key 8e666ab9-44de-46e3-b7dd-d3c99387a71a: Signature verification failed.", "module": "jwt_keys", "function": "decode_token", "line": 239}
{"timestamp": "2025-06-12T00:53:10.991779", "level": "ERROR", "logger": "backend.core.security.jwt_keys", "message": "Token decode error: Token verification failed with all keys. Errors: Key 8e666ab9-44de-46e3-b7dd-d3c99387a71a: Signature verification failed.", "module": "jwt_keys", "function": "decode_token", "line": 244}
{"timestamp": "2025-06-12T00:53:10.991936", "level": "ERROR", "logger": "backend.services.auth", "message": "JWT token validation failed: Failed to decode token: Token verification failed with all keys. Errors: Key 8e666ab9-44de-46e3-b7dd-d3c99387a71a: Signature verification failed.", "module": "auth", "function": "authenticate_token", "line": 686}
{"timestamp": "2025-06-12T00:53:10.992849", "level": "WARNING", "logger": "backend.middleware.subscription_limits", "message": "Error authenticating token in subscription middleware: name 'ValidationError' is not defined", "module": "subscription_limits", "function": "_get_current_user", "line": 308}
{"timestamp": "2025-06-12T00:53:10.995142", "level": "WARNING", "logger": "backend.core.security.jwt_keys", "message": "Unknown JWT kid requested: d193c25a-f21b-46f7-a864-af99c6eb2746", "module": "jwt_keys", "function": "get_verification_key", "line": 105}
{"timestamp": "2025-06-12T00:53:10.996134", "level": "WARNING", "logger": "backend.core.security.jwt_keys", "message": "Verification failed with key ID d193c25a-f21b-46f7-a864-af99c6eb2746: Unknown kid: d193c25a-f21b-46f7-a864-af99c6eb2746", "module": "jwt_keys", "function": "decode_token", "line": 203}
{"timestamp": "2025-06-12T00:53:10.996475", "level": "WARNING", "logger": "backend.core.security.jwt_keys", "message": "Verification failed with current key: Signature verification failed.", "module": "jwt_keys", "function": "decode_token", "line": 216}
{"timestamp": "2025-06-12T00:53:10.997243", "level": "ERROR", "logger": "backend.core.security.jwt_keys", "message": "Token verification failed with all keys. Errors: Key 8e666ab9-44de-46e3-b7dd-d3c99387a71a: Signature verification failed.", "module": "jwt_keys", "function": "decode_token", "line": 239}
{"timestamp": "2025-06-12T00:53:10.997345", "level": "ERROR", "logger": "backend.core.security.jwt_keys", "message": "Token decode error: Token verification failed with all keys. Errors: Key 8e666ab9-44de-46e3-b7dd-d3c99387a71a: Signature verification failed.", "module": "jwt_keys", "function": "decode_token", "line": 244}
{"timestamp": "2025-06-12T00:53:10.997427", "level": "ERROR", "logger": "backend.services.auth", "message": "JWT token validation failed: Failed to decode token: Token verification failed with all keys. Errors: Key 8e666ab9-44de-46e3-b7dd-d3c99387a71a: Signature verification failed.", "module": "auth", "function": "authenticate_token", "line": 686}
{"timestamp": "2025-06-12T00:53:10.998670", "level": "WARNING", "logger": "backend.middleware.subscription_limits", "message": "Auth error in subscription middleware: name 'ValidationError' is not defined", "module": "subscription_limits", "function": "dispatch", "line": 234}
{"timestamp": "2025-06-12T00:53:10.999706", "level": "WARNING", "logger": "backend.core.security.jwt_keys", "message": "Unknown JWT kid requested: d193c25a-f21b-46f7-a864-af99c6eb2746", "module": "jwt_keys", "function": "get_verification_key", "line": 105}
{"timestamp": "2025-06-12T00:53:11.001932", "level": "WARNING", "logger": "backend.core.security.jwt_keys", "message": "Verification failed with key ID d193c25a-f21b-46f7-a864-af99c6eb2746: Unknown kid: d193c25a-f21b-46f7-a864-af99c6eb2746", "module": "jwt_keys", "function": "decode_token", "line": 203}
{"timestamp": "2025-06-12T00:53:11.002227", "level": "WARNING", "logger": "backend.core.security.jwt_keys", "message": "Verification failed with current key: Signature verification failed.", "module": "jwt_keys", "function": "decode_token", "line": 216}
{"timestamp": "2025-06-12T00:53:11.004863", "level": "ERROR", "logger": "backend.core.security.jwt_keys", "message": "Token verification failed with all keys. Errors: Key 8e666ab9-44de-46e3-b7dd-d3c99387a71a: Signature verification failed.", "module": "jwt_keys", "function": "decode_token", "line": 239}
{"timestamp": "2025-06-12T00:53:11.004977", "level": "ERROR", "logger": "backend.core.security.jwt_keys", "message": "Token decode error: Token verification failed with all keys. Errors: Key 8e666ab9-44de-46e3-b7dd-d3c99387a71a: Signature verification failed.", "module": "jwt_keys", "function": "decode_token", "line": 244}
{"timestamp": "2025-06-12T00:53:11.005050", "level": "ERROR", "logger": "backend.services.auth", "message": "JWT token validation failed: Failed to decode token: Token verification failed with all keys. Errors: Key 8e666ab9-44de-46e3-b7dd-d3c99387a71a: Signature verification failed.", "module": "auth", "function": "authenticate_token", "line": 686}
{"timestamp": "2025-06-12T00:53:23.048380", "level": "WARNING", "logger": "backend.core.security.jwt_keys", "message": "Unknown JWT kid requested: d193c25a-f21b-46f7-a864-af99c6eb2746", "module": "jwt_keys", "function": "get_verification_key", "line": 105}
{"timestamp": "2025-06-12T00:53:23.048604", "level": "WARNING", "logger": "backend.core.security.jwt_keys", "message": "Verification failed with key ID d193c25a-f21b-46f7-a864-af99c6eb2746: Unknown kid: d193c25a-f21b-46f7-a864-af99c6eb2746", "module": "jwt_keys", "function": "decode_token", "line": 203}
{"timestamp": "2025-06-12T00:53:23.051126", "level": "WARNING", "logger": "backend.core.security.jwt_keys", "message": "Verification failed with current key: Signature verification failed.", "module": "jwt_keys", "function": "decode_token", "line": 216}
{"timestamp": "2025-06-12T00:53:23.052210", "level": "ERROR", "logger": "backend.core.security.jwt_keys", "message": "Token verification failed with all keys. Errors: Key 8e666ab9-44de-46e3-b7dd-d3c99387a71a: Signature verification failed.", "module": "jwt_keys", "function": "decode_token", "line": 239}
{"timestamp": "2025-06-12T00:53:23.055518", "level": "ERROR", "logger": "backend.core.security.jwt_keys", "message": "Token decode error: Token verification failed with all keys. Errors: Key 8e666ab9-44de-46e3-b7dd-d3c99387a71a: Signature verification failed.", "module": "jwt_keys", "function": "decode_token", "line": 244}
{"timestamp": "2025-06-12T00:53:23.056192", "level": "ERROR", "logger": "backend.services.auth", "message": "JWT token validation failed: Failed to decode token: Token verification failed with all keys. Errors: Key 8e666ab9-44de-46e3-b7dd-d3c99387a71a: Signature verification failed.", "module": "auth", "function": "authenticate_token", "line": 686}
{"timestamp": "2025-06-12T00:53:23.059817", "level": "WARNING", "logger": "backend.middleware.subscription_limits", "message": "Error authenticating token in subscription middleware: name 'ValidationError' is not defined", "module": "subscription_limits", "function": "_get_current_user", "line": 308}
{"timestamp": "2025-06-12T00:53:23.072808", "level": "WARNING", "logger": "backend.core.security.jwt_keys", "message": "Unknown JWT kid requested: d193c25a-f21b-46f7-a864-af99c6eb2746", "module": "jwt_keys", "function": "get_verification_key", "line": 105}
{"timestamp": "2025-06-12T00:53:23.072919", "level": "WARNING", "logger": "backend.core.security.jwt_keys", "message": "Verification failed with key ID d193c25a-f21b-46f7-a864-af99c6eb2746: Unknown kid: d193c25a-f21b-46f7-a864-af99c6eb2746", "module": "jwt_keys", "function": "decode_token", "line": 203}
{"timestamp": "2025-06-12T00:53:23.074140", "level": "WARNING", "logger": "backend.core.security.jwt_keys", "message": "Verification failed with current key: Signature verification failed.", "module": "jwt_keys", "function": "decode_token", "line": 216}
{"timestamp": "2025-06-12T00:53:23.074399", "level": "ERROR", "logger": "backend.core.security.jwt_keys", "message": "Token verification failed with all keys. Errors: Key 8e666ab9-44de-46e3-b7dd-d3c99387a71a: Signature verification failed.", "module": "jwt_keys", "function": "decode_token", "line": 239}
{"timestamp": "2025-06-12T00:53:23.074477", "level": "ERROR", "logger": "backend.core.security.jwt_keys", "message": "Token decode error: Token verification failed with all keys. Errors: Key 8e666ab9-44de-46e3-b7dd-d3c99387a71a: Signature verification failed.", "module": "jwt_keys", "function": "decode_token", "line": 244}
{"timestamp": "2025-06-12T00:53:23.074629", "level": "ERROR", "logger": "backend.services.auth", "message": "JWT token validation failed: Failed to decode token: Token verification failed with all keys. Errors: Key 8e666ab9-44de-46e3-b7dd-d3c99387a71a: Signature verification failed.", "module": "auth", "function": "authenticate_token", "line": 686}
{"timestamp": "2025-06-12T00:53:23.075345", "level": "WARNING", "logger": "backend.middleware.subscription_limits", "message": "Auth error in subscription middleware: name 'ValidationError' is not defined", "module": "subscription_limits", "function": "dispatch", "line": 234}
{"timestamp": "2025-06-12T00:53:23.075940", "level": "WARNING", "logger": "backend.core.security.jwt_keys", "message": "Unknown JWT kid requested: d193c25a-f21b-46f7-a864-af99c6eb2746", "module": "jwt_keys", "function": "get_verification_key", "line": 105}
{"timestamp": "2025-06-12T00:53:23.076001", "level": "WARNING", "logger": "backend.core.security.jwt_keys", "message": "Verification failed with key ID d193c25a-f21b-46f7-a864-af99c6eb2746: Unknown kid: d193c25a-f21b-46f7-a864-af99c6eb2746", "module": "jwt_keys", "function": "decode_token", "line": 203}
{"timestamp": "2025-06-12T00:53:23.076191", "level": "WARNING", "logger": "backend.core.security.jwt_keys", "message": "Verification failed with current key: Signature verification failed.", "module": "jwt_keys", "function": "decode_token", "line": 216}
{"timestamp": "2025-06-12T00:53:23.076361", "level": "ERROR", "logger": "backend.core.security.jwt_keys", "message": "Token verification failed with all keys. Errors: Key 8e666ab9-44de-46e3-b7dd-d3c99387a71a: Signature verification failed.", "module": "jwt_keys", "function": "decode_token", "line": 239}
{"timestamp": "2025-06-12T00:53:23.076433", "level": "ERROR", "logger": "backend.core.security.jwt_keys", "message": "Token decode error: Token verification failed with all keys. Errors: Key 8e666ab9-44de-46e3-b7dd-d3c99387a71a: Signature verification failed.", "module": "jwt_keys", "function": "decode_token", "line": 244}
{"timestamp": "2025-06-12T00:53:23.076513", "level": "ERROR", "logger": "backend.services.auth", "message": "JWT token validation failed: Failed to decode token: Token verification failed with all keys. Errors: Key 8e666ab9-44de-46e3-b7dd-d3c99387a71a: Signature verification failed.", "module": "auth", "function": "authenticate_token", "line": 686}
{"timestamp": "2025-06-12T00:53:35.149410", "level": "INFO", "logger": "backend.services.qr_preview_cache", "message": "QR preview cache cleanup task stopped", "module": "qr_preview_cache", "function": "stop", "line": 257}
{"timestamp": "2025-06-12T00:53:35.149872", "level": "INFO", "logger": "backend.main", "message": "QR preview cache stopped", "module": "main", "function": "shutdown_qr_preview_cache", "line": 352}
{"timestamp": "2025-06-12T00:53:38.576948", "level": "INFO", "logger": "backend.main", "message": "=== QRVibe API Startup: Logging to file is enabled ===", "module": "main", "function": "<module>", "line": 37}
{"timestamp": "2025-06-12T00:53:38.577035", "level": "INFO", "logger": "backend.main", "message": "Applying critical patches before initialization", "module": "main", "function": "<module>", "line": 43}
{"timestamp": "2025-06-12T00:53:38.577098", "level": "INFO", "logger": "backend.patches", "message": "Applying patches (none required)", "module": "__init__", "function": "apply_patches", "line": 11}
{"timestamp": "2025-06-12T00:53:38.577323", "level": "INFO", "logger": "backend.core.middleware.cors", "message": "Setting up CORS middleware with origins: ['https://localhost:3000', 'https://127.0.0.1:3000', 'http://127.0.0.1:3000', 'http://localhost:3000']", "module": "cors", "function": "setup_cors", "line": 86}
{"timestamp": "2025-06-12T00:53:38.582380", "level": "INFO", "logger": "backend.core.middleware.cors", "message": "CORS middleware configured - preflight requests should be handled automatically", "module": "cors", "function": "setup_cors", "line": 98}
{"timestamp": "2025-06-12T00:53:38.583930", "level": "INFO", "logger": "backend.middleware.security_middleware", "message": "Added Enhanced Security Middleware with tiered rate limiting", "module": "security_middleware", "function": "add_security_middleware", "line": 176}
{"timestamp": "2025-06-12T00:53:38.584038", "level": "INFO", "logger": "backend.main", "message": "Mounting storage directory: /Users/<USER>/aihub/qrvibe/storage", "module": "main", "function": "<module>", "line": 107}
{"timestamp": "2025-06-12T00:53:38.584170", "level": "INFO", "logger": "backend.main", "message": "Storage directory absolute path: /Users/<USER>/aihub/qrvibe/storage", "module": "main", "function": "<module>", "line": 119}
{"timestamp": "2025-06-12T00:53:39.095947", "level": "INFO", "logger": "root", "message": "Storage initialized: environment=test, storage_dir=/Users/<USER>/aihub/qrvibe/storage, s3_enabled=True", "module": "storage", "function": "__init__", "line": 41}
{"timestamp": "2025-06-12T00:53:39.100671", "level": "INFO", "logger": "root", "message": "S3 storage enabled with bucket: qrverse and CDN: https://dev-cdn.qrvibe.com", "module": "storage", "function": "__init__", "line": 59}
{"timestamp": "2025-06-12T00:53:39.148271", "level": "INFO", "logger": "root", "message": "Storage initialized: environment=test, storage_dir=/Users/<USER>/aihub/qrvibe/storage, s3_enabled=True", "module": "storage", "function": "__init__", "line": 41}
{"timestamp": "2025-06-12T00:53:39.151690", "level": "INFO", "logger": "root", "message": "S3 storage enabled with bucket: qrverse and CDN: https://dev-cdn.qrvibe.com", "module": "storage", "function": "__init__", "line": 59}
{"timestamp": "2025-06-12T00:53:39.205272", "level": "INFO", "logger": "backend.middleware.security_middleware", "message": "Enhanced security middleware initialized with tiered rate limiting", "module": "security_middleware", "function": "__init__", "line": 87}
{"timestamp": "2025-06-12T00:53:39.205412", "level": "INFO", "logger": "backend.core.middleware.csrf", "message": "CSRF Protection Middleware initialized with exclude paths: ['/auth/jwt/login', '/auth/logout', '/auth/register', '/auth/2fa', '/auth/me', '/auth/verify-email', '/auth/reset-password', '/admin/login', '/admin/logout']", "module": "csrf", "function": "__init__", "line": 44}
{"timestamp": "2025-06-12T00:53:39.212834", "level": "INFO", "logger": "backend.services.token_blacklist", "message": "Token blacklist service initialized with Redis", "module": "token_blacklist", "function": "initialize", "line": 59}
{"timestamp": "2025-06-12T00:53:39.212930", "level": "INFO", "logger": "backend.main", "message": "Token blacklist service initialized", "module": "main", "function": "initialize_token_blacklist", "line": 98}
{"timestamp": "2025-06-12T00:53:39.213725", "level": "INFO", "logger": "backend.services.qr_preview_cache", "message": "QR preview cache initialized at /Users/<USER>/aihub/qrvibe/storage/cache/qr_preview_cache.db", "module": "qr_preview_cache", "function": "_init_db", "line": 56}
{"timestamp": "2025-06-12T00:53:39.213836", "level": "INFO", "logger": "backend.services.qr_preview_cache", "message": "Started QR preview cache cleanup task", "module": "qr_preview_cache", "function": "start_cleanup_task", "line": 62}
{"timestamp": "2025-06-12T00:53:39.213905", "level": "INFO", "logger": "backend.main", "message": "QR preview cache initialized and cleanup task started", "module": "main", "function": "init_qr_preview_cache", "line": 345}
{"timestamp": "2025-06-12T00:54:30.972299", "level": "INFO", "logger": "backend.services.qr_preview_cache", "message": "QR preview cache cleanup task stopped", "module": "qr_preview_cache", "function": "stop", "line": 257}
{"timestamp": "2025-06-12T00:54:30.972836", "level": "INFO", "logger": "backend.main", "message": "QR preview cache stopped", "module": "main", "function": "shutdown_qr_preview_cache", "line": 352}
