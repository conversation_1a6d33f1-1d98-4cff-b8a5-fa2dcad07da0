{"timestamp": "2025-06-12T17:24:12.261822", "level": "INFO", "logger": "backend.services.qr_preview_cache", "message": "QR preview cache cleanup task stopped", "module": "qr_preview_cache", "function": "stop", "line": 257}
{"timestamp": "2025-06-12T17:24:12.262959", "level": "INFO", "logger": "backend.main", "message": "QR preview cache stopped", "module": "main", "function": "shutdown_qr_preview_cache", "line": 352}
{"timestamp": "2025-06-12T17:24:22.102729", "level": "INFO", "logger": "backend.main", "message": "=== QRVibe API Startup: Logging to file is enabled ===", "module": "main", "function": "<module>", "line": 37}
{"timestamp": "2025-06-12T17:24:22.102853", "level": "INFO", "logger": "backend.main", "message": "Applying critical patches before initialization", "module": "main", "function": "<module>", "line": 43}
{"timestamp": "2025-06-12T17:24:22.102951", "level": "INFO", "logger": "backend.patches", "message": "Applying patches (none required)", "module": "__init__", "function": "apply_patches", "line": 11}
{"timestamp": "2025-06-12T17:24:22.103386", "level": "INFO", "logger": "backend.core.middleware.cors", "message": "Setting up CORS middleware with origins: ['http://127.0.0.1:3000', 'https://127.0.0.1:3000', 'http://localhost:3000', 'https://localhost:3000']", "module": "cors", "function": "setup_cors", "line": 86}
{"timestamp": "2025-06-12T17:24:22.103477", "level": "INFO", "logger": "backend.core.middleware.cors", "message": "CORS middleware configured - preflight requests should be handled automatically", "module": "cors", "function": "setup_cors", "line": 98}
{"timestamp": "2025-06-12T17:24:22.104838", "level": "INFO", "logger": "backend.middleware.security_middleware", "message": "Added Enhanced Security Middleware with tiered rate limiting", "module": "security_middleware", "function": "add_security_middleware", "line": 176}
{"timestamp": "2025-06-12T17:24:22.104942", "level": "INFO", "logger": "backend.main", "message": "Mounting storage directory: /Users/<USER>/aihub/qrvibe/storage", "module": "main", "function": "<module>", "line": 107}
{"timestamp": "2025-06-12T17:24:22.105414", "level": "INFO", "logger": "backend.main", "message": "Storage directory absolute path: /Users/<USER>/aihub/qrvibe/storage", "module": "main", "function": "<module>", "line": 119}
{"timestamp": "2025-06-12T17:24:22.504753", "level": "INFO", "logger": "root", "message": "Storage initialized: environment=test, storage_dir=/Users/<USER>/aihub/qrvibe/storage, s3_enabled=True", "module": "storage", "function": "__init__", "line": 41}
{"timestamp": "2025-06-12T17:24:22.507472", "level": "INFO", "logger": "root", "message": "S3 storage enabled with bucket: qrverse and CDN: https://dev-cdn.qrvibe.com", "module": "storage", "function": "__init__", "line": 59}
{"timestamp": "2025-06-12T17:24:22.554754", "level": "INFO", "logger": "root", "message": "Storage initialized: environment=test, storage_dir=/Users/<USER>/aihub/qrvibe/storage, s3_enabled=True", "module": "storage", "function": "__init__", "line": 41}
{"timestamp": "2025-06-12T17:24:22.557444", "level": "INFO", "logger": "root", "message": "S3 storage enabled with bucket: qrverse and CDN: https://dev-cdn.qrvibe.com", "module": "storage", "function": "__init__", "line": 59}
{"timestamp": "2025-06-12T17:24:22.577666", "level": "INFO", "logger": "backend.middleware.security_middleware", "message": "Enhanced security middleware initialized with tiered rate limiting", "module": "security_middleware", "function": "__init__", "line": 87}
{"timestamp": "2025-06-12T17:24:22.577768", "level": "INFO", "logger": "backend.core.middleware.csrf", "message": "CSRF Protection Middleware initialized with exclude paths: ['/auth/jwt/login', '/auth/logout', '/auth/register', '/auth/2fa', '/auth/me', '/auth/verify-email', '/auth/reset-password', '/admin/login', '/admin/logout']", "module": "csrf", "function": "__init__", "line": 44}
{"timestamp": "2025-06-12T17:24:22.594905", "level": "INFO", "logger": "backend.services.token_blacklist", "message": "Token blacklist service initialized with Redis", "module": "token_blacklist", "function": "initialize", "line": 59}
{"timestamp": "2025-06-12T17:24:22.595013", "level": "INFO", "logger": "backend.main", "message": "Token blacklist service initialized", "module": "main", "function": "initialize_token_blacklist", "line": 98}
{"timestamp": "2025-06-12T17:24:22.598145", "level": "INFO", "logger": "backend.services.qr_preview_cache", "message": "QR preview cache initialized at /Users/<USER>/aihub/qrvibe/storage/cache/qr_preview_cache.db", "module": "qr_preview_cache", "function": "_init_db", "line": 56}
{"timestamp": "2025-06-12T17:24:22.598251", "level": "INFO", "logger": "backend.services.qr_preview_cache", "message": "Started QR preview cache cleanup task", "module": "qr_preview_cache", "function": "start_cleanup_task", "line": 62}
{"timestamp": "2025-06-12T17:24:22.598310", "level": "INFO", "logger": "backend.main", "message": "QR preview cache initialized and cleanup task started", "module": "main", "function": "init_qr_preview_cache", "line": 345}
{"timestamp": "2025-06-12T17:24:40.949308", "level": "WARNING", "logger": "backend.core.security.jwt_keys", "message": "Unknown JWT kid requested: d6c16955-9374-4e7f-bbe6-d61c3822877d", "module": "jwt_keys", "function": "get_verification_key", "line": 105}
{"timestamp": "2025-06-12T17:24:40.949397", "level": "WARNING", "logger": "backend.core.security.jwt_keys", "message": "Verification failed with key ID d6c16955-9374-4e7f-bbe6-d61c3822877d: Unknown kid: d6c16955-9374-4e7f-bbe6-d61c3822877d", "module": "jwt_keys", "function": "decode_token", "line": 203}
{"timestamp": "2025-06-12T17:24:40.988790", "level": "WARNING", "logger": "backend.core.security.jwt_keys", "message": "Verification failed with current key: Signature verification failed.", "module": "jwt_keys", "function": "decode_token", "line": 216}
{"timestamp": "2025-06-12T17:24:40.989260", "level": "ERROR", "logger": "backend.core.security.jwt_keys", "message": "Token verification failed with all keys. Errors: Key a69f821a-65e8-4292-8feb-985dc7ac94d9: Signature verification failed.", "module": "jwt_keys", "function": "decode_token", "line": 239}
{"timestamp": "2025-06-12T17:24:40.989359", "level": "ERROR", "logger": "backend.core.security.jwt_keys", "message": "Token decode error: Token verification failed with all keys. Errors: Key a69f821a-65e8-4292-8feb-985dc7ac94d9: Signature verification failed.", "module": "jwt_keys", "function": "decode_token", "line": 244}
{"timestamp": "2025-06-12T17:24:40.989450", "level": "ERROR", "logger": "backend.auth", "message": "Token validation error: Failed to decode token: Token verification failed with all keys. Errors: Key a69f821a-65e8-4292-8feb-985dc7ac94d9: Signature verification failed.", "module": "__init__", "function": "get_current_user", "line": 193}
{"timestamp": "2025-06-12T17:24:42.106369", "level": "WARNING", "logger": "backend.core.middleware.csrf", "message": "CSRF token missing for POST /qr/preview/svg - Header present: False, Cookie present: False", "module": "csrf", "function": "dispatch", "line": 66}
{"timestamp": "2025-06-12T17:24:42.106483", "level": "WARNING", "logger": "backend.core.middleware.csrf", "message": "CSRF validation failed for POST /qr/preview/svg - IP: 127.0.0.1", "module": "csrf", "function": "dispatch", "line": 75}
{"timestamp": "2025-06-12T17:24:54.906887", "level": "INFO", "logger": "backend.services.enhanced_rate_limit", "message": "Auth event for anonymous: login_attempt (failure)", "module": "enhanced_rate_limit", "function": "track_auth_event", "line": 182}
{"timestamp": "2025-06-12T17:24:55.456481", "level": "INFO", "logger": "backend.services.auth", "message": "Authentication successful for user: <EMAIL>", "module": "auth", "function": "authenticate_user", "line": 266}
{"timestamp": "2025-06-12T17:24:55.459055", "level": "INFO", "logger": "backend.routes.auth", "message": "User logged in successfully: <EMAIL>", "module": "auth", "function": "login", "line": 218}
{"timestamp": "2025-06-12T17:24:55.460106", "level": "INFO", "logger": "backend.services.enhanced_rate_limit", "message": "Auth event for user e4ae2d8f-b32f-52d7-99cd-8a33b19652c7: login_success (success)", "module": "enhanced_rate_limit", "function": "track_auth_event", "line": 182}
{"timestamp": "2025-06-12T17:24:55.533428", "level": "INFO", "logger": "backend.services.adaptive_session", "message": "Adaptive session for user e4ae2d8f-b32f-52d7-99cd-8a33b19652c7: risk=low, duration=86400s", "module": "adaptive_session", "function": "get_session_expiry", "line": 161}
{"timestamp": "2025-06-12T17:24:55.533572", "level": "INFO", "logger": "backend.routes.auth", "message": "Adaptive session <NAME_EMAIL>: duration=86400s", "module": "auth", "function": "login", "line": 279}
{"timestamp": "2025-06-12T17:24:55.533648", "level": "INFO", "logger": "backend.routes.auth", "message": "Bearer token authentication issued <NAME_EMAIL>", "module": "auth", "function": "login", "line": 284}
{"timestamp": "2025-06-12T17:24:58.203048", "level": "INFO", "logger": "root", "message": "Getting feature matrix for user e4ae2d8f-b32f-52d7-99cd-8a33b19652c7 (email: <EMAIL>)", "module": "subscription", "function": "get_user_feature_matrix", "line": 142}
{"timestamp": "2025-06-12T17:24:58.211621", "level": "INFO", "logger": "root", "message": "Found subscription for user e4ae2d8f-b32f-52d7-99cd-8a33b19652c7: plan=enterprise, status=active", "module": "subscription", "function": "get_user_feature_matrix", "line": 152}
{"timestamp": "2025-06-12T17:24:58.224958", "level": "INFO", "logger": "root", "message": "Found 0 team addons for user e4ae2d8f-b32f-52d7-99cd-8a33b19652c7", "module": "subscription", "function": "get_user_feature_matrix", "line": 181}
{"timestamp": "2025-06-12T17:24:58.225154", "level": "INFO", "logger": "root", "message": "Retrieved base features for plan enterprise", "module": "subscription", "function": "get_user_feature_matrix", "line": 191}
{"timestamp": "2025-06-12T17:24:58.276696", "level": "INFO", "logger": "root", "message": "Successfully generated feature matrix for user e4ae2d8f-b32f-52d7-99cd-8a33b19652c7", "module": "subscription", "function": "get_user_feature_matrix", "line": 284}
{"timestamp": "2025-06-12T17:31:15.437960", "level": "INFO", "logger": "root", "message": "Getting feature matrix for user e4ae2d8f-b32f-52d7-99cd-8a33b19652c7 (email: <EMAIL>)", "module": "subscription", "function": "get_user_feature_matrix", "line": 142}
{"timestamp": "2025-06-12T17:31:15.456786", "level": "INFO", "logger": "root", "message": "Found subscription for user e4ae2d8f-b32f-52d7-99cd-8a33b19652c7: plan=enterprise, status=active", "module": "subscription", "function": "get_user_feature_matrix", "line": 152}
{"timestamp": "2025-06-12T17:31:15.474233", "level": "INFO", "logger": "root", "message": "Found 0 team addons for user e4ae2d8f-b32f-52d7-99cd-8a33b19652c7", "module": "subscription", "function": "get_user_feature_matrix", "line": 181}
{"timestamp": "2025-06-12T17:31:15.474886", "level": "INFO", "logger": "root", "message": "Retrieved base features for plan enterprise", "module": "subscription", "function": "get_user_feature_matrix", "line": 191}
{"timestamp": "2025-06-12T17:31:15.514052", "level": "INFO", "logger": "root", "message": "Successfully generated feature matrix for user e4ae2d8f-b32f-52d7-99cd-8a33b19652c7", "module": "subscription", "function": "get_user_feature_matrix", "line": 284}
{"timestamp": "2025-06-12T17:32:39.379324", "level": "INFO", "logger": "backend.services.qr_preview_cache", "message": "QR preview cache cleanup task stopped", "module": "qr_preview_cache", "function": "stop", "line": 257}
{"timestamp": "2025-06-12T17:32:39.379981", "level": "INFO", "logger": "backend.main", "message": "QR preview cache stopped", "module": "main", "function": "shutdown_qr_preview_cache", "line": 352}
