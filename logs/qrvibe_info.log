{"timestamp": "2025-06-11T15:25:14.339040", "level": "INFO", "logger": "backend.services.enhanced_rate_limit", "message": "Auth event for anonymous: login_attempt (failure)", "module": "enhanced_rate_limit", "function": "track_auth_event", "line": 182}
{"timestamp": "2025-06-11T15:25:14.819870", "level": "INFO", "logger": "backend.services.auth", "message": "Authentication successful for user: <EMAIL>", "module": "auth", "function": "authenticate_user", "line": 266}
{"timestamp": "2025-06-11T15:25:14.820983", "level": "INFO", "logger": "backend.routes.auth", "message": "User logged in successfully: <EMAIL>", "module": "auth", "function": "login", "line": 218}
{"timestamp": "2025-06-11T15:25:14.825727", "level": "INFO", "logger": "backend.services.enhanced_rate_limit", "message": "Auth event for user e4ae2d8f-b32f-52d7-99cd-8a33b19652c7: login_success (success)", "module": "enhanced_rate_limit", "function": "track_auth_event", "line": 182}
{"timestamp": "2025-06-11T15:25:14.948856", "level": "INFO", "logger": "backend.services.adaptive_session", "message": "Adaptive session for user e4ae2d8f-b32f-52d7-99cd-8a33b19652c7: risk=low, duration=86400s", "module": "adaptive_session", "function": "get_session_expiry", "line": 161}
{"timestamp": "2025-06-11T15:25:14.948954", "level": "INFO", "logger": "backend.routes.auth", "message": "Adaptive session <NAME_EMAIL>: duration=86400s", "module": "auth", "function": "login", "line": 279}
{"timestamp": "2025-06-11T15:25:14.949005", "level": "INFO", "logger": "backend.routes.auth", "message": "Bearer token authentication issued <NAME_EMAIL>", "module": "auth", "function": "login", "line": 284}
{"timestamp": "2025-06-11T15:25:18.086334", "level": "INFO", "logger": "root", "message": "Getting feature matrix for user e4ae2d8f-b32f-52d7-99cd-8a33b19652c7 (email: <EMAIL>)", "module": "subscription", "function": "get_user_feature_matrix", "line": 142}
{"timestamp": "2025-06-11T15:25:18.102793", "level": "INFO", "logger": "root", "message": "Found subscription for user e4ae2d8f-b32f-52d7-99cd-8a33b19652c7: plan=enterprise, status=active", "module": "subscription", "function": "get_user_feature_matrix", "line": 152}
{"timestamp": "2025-06-11T15:25:18.121446", "level": "INFO", "logger": "root", "message": "Found 0 team addons for user e4ae2d8f-b32f-52d7-99cd-8a33b19652c7", "module": "subscription", "function": "get_user_feature_matrix", "line": 181}
{"timestamp": "2025-06-11T15:25:18.121587", "level": "INFO", "logger": "root", "message": "Retrieved base features for plan enterprise", "module": "subscription", "function": "get_user_feature_matrix", "line": 191}
{"timestamp": "2025-06-11T15:25:18.169625", "level": "INFO", "logger": "root", "message": "Successfully generated feature matrix for user e4ae2d8f-b32f-52d7-99cd-8a33b19652c7", "module": "subscription", "function": "get_user_feature_matrix", "line": 284}
{"timestamp": "2025-06-11T15:36:13.124784", "level": "INFO", "logger": "root", "message": "Getting feature matrix for user e4ae2d8f-b32f-52d7-99cd-8a33b19652c7 (email: <EMAIL>)", "module": "subscription", "function": "get_user_feature_matrix", "line": 142}
{"timestamp": "2025-06-11T15:36:13.511360", "level": "INFO", "logger": "root", "message": "Found subscription for user e4ae2d8f-b32f-52d7-99cd-8a33b19652c7: plan=enterprise, status=active", "module": "subscription", "function": "get_user_feature_matrix", "line": 152}
{"timestamp": "2025-06-11T15:36:13.587892", "level": "INFO", "logger": "root", "message": "Found 0 team addons for user e4ae2d8f-b32f-52d7-99cd-8a33b19652c7", "module": "subscription", "function": "get_user_feature_matrix", "line": 181}
{"timestamp": "2025-06-11T15:36:13.588125", "level": "INFO", "logger": "root", "message": "Retrieved base features for plan enterprise", "module": "subscription", "function": "get_user_feature_matrix", "line": 191}
{"timestamp": "2025-06-11T15:36:13.635921", "level": "INFO", "logger": "root", "message": "Successfully generated feature matrix for user e4ae2d8f-b32f-52d7-99cd-8a33b19652c7", "module": "subscription", "function": "get_user_feature_matrix", "line": 284}
{"timestamp": "2025-06-11T15:39:04.521085", "level": "INFO", "logger": "backend.services.qr_preview_cache", "message": "QR preview cache cleanup task stopped", "module": "qr_preview_cache", "function": "stop", "line": 257}
{"timestamp": "2025-06-11T15:39:04.521995", "level": "INFO", "logger": "backend.main", "message": "QR preview cache stopped", "module": "main", "function": "shutdown_qr_preview_cache", "line": 352}
{"timestamp": "2025-06-11T15:39:08.770417", "level": "INFO", "logger": "backend.main", "message": "=== QRVibe API Startup: Logging to file is enabled ===", "module": "main", "function": "<module>", "line": 37}
{"timestamp": "2025-06-11T15:39:08.770508", "level": "INFO", "logger": "backend.main", "message": "Applying critical patches before initialization", "module": "main", "function": "<module>", "line": 43}
{"timestamp": "2025-06-11T15:39:08.770571", "level": "INFO", "logger": "backend.patches", "message": "Applying patches (none required)", "module": "__init__", "function": "apply_patches", "line": 11}
{"timestamp": "2025-06-11T15:39:08.770834", "level": "INFO", "logger": "backend.core.middleware.cors", "message": "Setting up CORS middleware with origins: ['https://localhost:3000', 'http://localhost:3000', 'https://127.0.0.1:3000', 'http://127.0.0.1:3000']", "module": "cors", "function": "setup_cors", "line": 86}
{"timestamp": "2025-06-11T15:39:08.770906", "level": "INFO", "logger": "backend.core.middleware.cors", "message": "CORS middleware configured - preflight requests should be handled automatically", "module": "cors", "function": "setup_cors", "line": 98}
{"timestamp": "2025-06-11T15:39:08.772068", "level": "INFO", "logger": "backend.middleware.security_middleware", "message": "Added Enhanced Security Middleware with tiered rate limiting", "module": "security_middleware", "function": "add_security_middleware", "line": 176}
{"timestamp": "2025-06-11T15:39:08.772170", "level": "INFO", "logger": "backend.main", "message": "Mounting storage directory: /Users/<USER>/aihub/qrvibe/storage", "module": "main", "function": "<module>", "line": 107}
{"timestamp": "2025-06-11T15:39:08.772282", "level": "INFO", "logger": "backend.main", "message": "Storage directory absolute path: /Users/<USER>/aihub/qrvibe/storage", "module": "main", "function": "<module>", "line": 119}
{"timestamp": "2025-06-11T15:39:09.254628", "level": "INFO", "logger": "root", "message": "Storage initialized: environment=test, storage_dir=/Users/<USER>/aihub/qrvibe/storage, s3_enabled=True", "module": "storage", "function": "__init__", "line": 41}
{"timestamp": "2025-06-11T15:39:09.257519", "level": "INFO", "logger": "root", "message": "S3 storage enabled with bucket: qrverse and CDN: https://dev-cdn.qrvibe.com", "module": "storage", "function": "__init__", "line": 59}
{"timestamp": "2025-06-11T15:39:09.303691", "level": "INFO", "logger": "root", "message": "Storage initialized: environment=test, storage_dir=/Users/<USER>/aihub/qrvibe/storage, s3_enabled=True", "module": "storage", "function": "__init__", "line": 41}
{"timestamp": "2025-06-11T15:39:09.307023", "level": "INFO", "logger": "root", "message": "S3 storage enabled with bucket: qrverse and CDN: https://dev-cdn.qrvibe.com", "module": "storage", "function": "__init__", "line": 59}
{"timestamp": "2025-06-11T15:39:09.337793", "level": "INFO", "logger": "backend.middleware.security_middleware", "message": "Enhanced security middleware initialized with tiered rate limiting", "module": "security_middleware", "function": "__init__", "line": 87}
{"timestamp": "2025-06-11T15:39:09.337891", "level": "INFO", "logger": "backend.core.middleware.csrf", "message": "CSRF Protection Middleware initialized with exclude paths: ['/auth/jwt/login', '/auth/logout', '/auth/register', '/auth/2fa', '/auth/me', '/auth/verify-email', '/auth/reset-password', '/admin/login', '/admin/logout']", "module": "csrf", "function": "__init__", "line": 44}
{"timestamp": "2025-06-11T15:39:09.342762", "level": "INFO", "logger": "backend.services.token_blacklist", "message": "Token blacklist service initialized with Redis", "module": "token_blacklist", "function": "initialize", "line": 59}
{"timestamp": "2025-06-11T15:39:09.342879", "level": "INFO", "logger": "backend.main", "message": "Token blacklist service initialized", "module": "main", "function": "initialize_token_blacklist", "line": 98}
{"timestamp": "2025-06-11T15:39:09.345531", "level": "INFO", "logger": "backend.services.qr_preview_cache", "message": "QR preview cache initialized at /Users/<USER>/aihub/qrvibe/storage/cache/qr_preview_cache.db", "module": "qr_preview_cache", "function": "_init_db", "line": 56}
{"timestamp": "2025-06-11T15:39:09.345605", "level": "INFO", "logger": "backend.services.qr_preview_cache", "message": "Started QR preview cache cleanup task", "module": "qr_preview_cache", "function": "start_cleanup_task", "line": 62}
{"timestamp": "2025-06-11T15:39:09.346126", "level": "INFO", "logger": "backend.main", "message": "QR preview cache initialized and cleanup task started", "module": "main", "function": "init_qr_preview_cache", "line": 345}
{"timestamp": "2025-06-11T15:39:45.052234", "level": "INFO", "logger": "backend.services.qr_preview_cache", "message": "QR preview cache cleanup task stopped", "module": "qr_preview_cache", "function": "stop", "line": 257}
{"timestamp": "2025-06-11T15:39:45.052368", "level": "INFO", "logger": "backend.main", "message": "QR preview cache stopped", "module": "main", "function": "shutdown_qr_preview_cache", "line": 352}
{"timestamp": "2025-06-11T15:39:50.092570", "level": "INFO", "logger": "backend.main", "message": "=== QRVibe API Startup: Logging to file is enabled ===", "module": "main", "function": "<module>", "line": 37}
{"timestamp": "2025-06-11T15:39:50.092692", "level": "INFO", "logger": "backend.main", "message": "Applying critical patches before initialization", "module": "main", "function": "<module>", "line": 43}
{"timestamp": "2025-06-11T15:39:50.092770", "level": "INFO", "logger": "backend.patches", "message": "Applying patches (none required)", "module": "__init__", "function": "apply_patches", "line": 11}
{"timestamp": "2025-06-11T15:39:50.093028", "level": "INFO", "logger": "backend.core.middleware.cors", "message": "Setting up CORS middleware with origins: ['https://127.0.0.1:3000', 'http://localhost:3000', 'https://localhost:3000', 'http://127.0.0.1:3000']", "module": "cors", "function": "setup_cors", "line": 86}
{"timestamp": "2025-06-11T15:39:50.095931", "level": "INFO", "logger": "backend.core.middleware.cors", "message": "CORS middleware configured - preflight requests should be handled automatically", "module": "cors", "function": "setup_cors", "line": 98}
{"timestamp": "2025-06-11T15:39:50.097522", "level": "INFO", "logger": "backend.middleware.security_middleware", "message": "Added Enhanced Security Middleware with tiered rate limiting", "module": "security_middleware", "function": "add_security_middleware", "line": 176}
{"timestamp": "2025-06-11T15:39:50.097675", "level": "INFO", "logger": "backend.main", "message": "Mounting storage directory: /Users/<USER>/aihub/qrvibe/storage", "module": "main", "function": "<module>", "line": 107}
{"timestamp": "2025-06-11T15:39:50.097806", "level": "INFO", "logger": "backend.main", "message": "Storage directory absolute path: /Users/<USER>/aihub/qrvibe/storage", "module": "main", "function": "<module>", "line": 119}
{"timestamp": "2025-06-11T15:39:50.698348", "level": "INFO", "logger": "root", "message": "Storage initialized: environment=test, storage_dir=/Users/<USER>/aihub/qrvibe/storage, s3_enabled=True", "module": "storage", "function": "__init__", "line": 41}
{"timestamp": "2025-06-11T15:39:50.706953", "level": "INFO", "logger": "root", "message": "S3 storage enabled with bucket: qrverse and CDN: https://dev-cdn.qrvibe.com", "module": "storage", "function": "__init__", "line": 59}
{"timestamp": "2025-06-11T15:39:50.775746", "level": "INFO", "logger": "root", "message": "Storage initialized: environment=test, storage_dir=/Users/<USER>/aihub/qrvibe/storage, s3_enabled=True", "module": "storage", "function": "__init__", "line": 41}
{"timestamp": "2025-06-11T15:39:50.779595", "level": "INFO", "logger": "root", "message": "S3 storage enabled with bucket: qrverse and CDN: https://dev-cdn.qrvibe.com", "module": "storage", "function": "__init__", "line": 59}
{"timestamp": "2025-06-11T15:39:51.404735", "level": "INFO", "logger": "backend.middleware.security_middleware", "message": "Enhanced security middleware initialized with tiered rate limiting", "module": "security_middleware", "function": "__init__", "line": 87}
{"timestamp": "2025-06-11T15:39:51.406283", "level": "INFO", "logger": "backend.core.middleware.csrf", "message": "CSRF Protection Middleware initialized with exclude paths: ['/auth/jwt/login', '/auth/logout', '/auth/register', '/auth/2fa', '/auth/me', '/auth/verify-email', '/auth/reset-password', '/admin/login', '/admin/logout']", "module": "csrf", "function": "__init__", "line": 44}
{"timestamp": "2025-06-11T15:39:51.480276", "level": "INFO", "logger": "backend.services.token_blacklist", "message": "Token blacklist service initialized with Redis", "module": "token_blacklist", "function": "initialize", "line": 59}
{"timestamp": "2025-06-11T15:39:51.480483", "level": "INFO", "logger": "backend.main", "message": "Token blacklist service initialized", "module": "main", "function": "initialize_token_blacklist", "line": 98}
{"timestamp": "2025-06-11T15:39:51.482523", "level": "INFO", "logger": "backend.services.qr_preview_cache", "message": "QR preview cache initialized at /Users/<USER>/aihub/qrvibe/storage/cache/qr_preview_cache.db", "module": "qr_preview_cache", "function": "_init_db", "line": 56}
{"timestamp": "2025-06-11T15:39:51.482716", "level": "INFO", "logger": "backend.services.qr_preview_cache", "message": "Started QR preview cache cleanup task", "module": "qr_preview_cache", "function": "start_cleanup_task", "line": 62}
{"timestamp": "2025-06-11T15:39:51.489355", "level": "INFO", "logger": "backend.main", "message": "QR preview cache initialized and cleanup task started", "module": "main", "function": "init_qr_preview_cache", "line": 345}
{"timestamp": "2025-06-11T15:39:59.074589", "level": "INFO", "logger": "backend.services.qr_preview_cache", "message": "QR preview cache cleanup task stopped", "module": "qr_preview_cache", "function": "stop", "line": 257}
{"timestamp": "2025-06-11T15:39:59.074897", "level": "INFO", "logger": "backend.main", "message": "QR preview cache stopped", "module": "main", "function": "shutdown_qr_preview_cache", "line": 352}
{"timestamp": "2025-06-11T15:40:02.527223", "level": "INFO", "logger": "backend.main", "message": "=== QRVibe API Startup: Logging to file is enabled ===", "module": "main", "function": "<module>", "line": 37}
{"timestamp": "2025-06-11T15:40:02.527316", "level": "INFO", "logger": "backend.main", "message": "Applying critical patches before initialization", "module": "main", "function": "<module>", "line": 43}
{"timestamp": "2025-06-11T15:40:02.527374", "level": "INFO", "logger": "backend.patches", "message": "Applying patches (none required)", "module": "__init__", "function": "apply_patches", "line": 11}
{"timestamp": "2025-06-11T15:40:02.527586", "level": "INFO", "logger": "backend.core.middleware.cors", "message": "Setting up CORS middleware with origins: ['https://127.0.0.1:3000', 'https://localhost:3000', 'http://localhost:3000', 'http://127.0.0.1:3000']", "module": "cors", "function": "setup_cors", "line": 86}
{"timestamp": "2025-06-11T15:40:02.527644", "level": "INFO", "logger": "backend.core.middleware.cors", "message": "CORS middleware configured - preflight requests should be handled automatically", "module": "cors", "function": "setup_cors", "line": 98}
{"timestamp": "2025-06-11T15:40:02.528631", "level": "INFO", "logger": "backend.middleware.security_middleware", "message": "Added Enhanced Security Middleware with tiered rate limiting", "module": "security_middleware", "function": "add_security_middleware", "line": 176}
{"timestamp": "2025-06-11T15:40:02.528738", "level": "INFO", "logger": "backend.main", "message": "Mounting storage directory: /Users/<USER>/aihub/qrvibe/storage", "module": "main", "function": "<module>", "line": 107}
{"timestamp": "2025-06-11T15:40:02.528868", "level": "INFO", "logger": "backend.main", "message": "Storage directory absolute path: /Users/<USER>/aihub/qrvibe/storage", "module": "main", "function": "<module>", "line": 119}
{"timestamp": "2025-06-11T15:40:02.993305", "level": "INFO", "logger": "root", "message": "Storage initialized: environment=test, storage_dir=/Users/<USER>/aihub/qrvibe/storage, s3_enabled=True", "module": "storage", "function": "__init__", "line": 41}
{"timestamp": "2025-06-11T15:40:02.996127", "level": "INFO", "logger": "root", "message": "S3 storage enabled with bucket: qrverse and CDN: https://dev-cdn.qrvibe.com", "module": "storage", "function": "__init__", "line": 59}
{"timestamp": "2025-06-11T15:40:03.045891", "level": "INFO", "logger": "root", "message": "Storage initialized: environment=test, storage_dir=/Users/<USER>/aihub/qrvibe/storage, s3_enabled=True", "module": "storage", "function": "__init__", "line": 41}
{"timestamp": "2025-06-11T15:40:03.049151", "level": "INFO", "logger": "root", "message": "S3 storage enabled with bucket: qrverse and CDN: https://dev-cdn.qrvibe.com", "module": "storage", "function": "__init__", "line": 59}
{"timestamp": "2025-06-11T15:40:03.172625", "level": "INFO", "logger": "backend.middleware.security_middleware", "message": "Enhanced security middleware initialized with tiered rate limiting", "module": "security_middleware", "function": "__init__", "line": 87}
{"timestamp": "2025-06-11T15:40:03.172733", "level": "INFO", "logger": "backend.core.middleware.csrf", "message": "CSRF Protection Middleware initialized with exclude paths: ['/auth/jwt/login', '/auth/logout', '/auth/register', '/auth/2fa', '/auth/me', '/auth/verify-email', '/auth/reset-password', '/admin/login', '/admin/logout']", "module": "csrf", "function": "__init__", "line": 44}
{"timestamp": "2025-06-11T15:40:03.178000", "level": "INFO", "logger": "backend.services.token_blacklist", "message": "Token blacklist service initialized with Redis", "module": "token_blacklist", "function": "initialize", "line": 59}
{"timestamp": "2025-06-11T15:40:03.178102", "level": "INFO", "logger": "backend.main", "message": "Token blacklist service initialized", "module": "main", "function": "initialize_token_blacklist", "line": 98}
{"timestamp": "2025-06-11T15:40:03.178741", "level": "INFO", "logger": "backend.services.qr_preview_cache", "message": "QR preview cache initialized at /Users/<USER>/aihub/qrvibe/storage/cache/qr_preview_cache.db", "module": "qr_preview_cache", "function": "_init_db", "line": 56}
{"timestamp": "2025-06-11T15:40:03.178835", "level": "INFO", "logger": "backend.services.qr_preview_cache", "message": "Started QR preview cache cleanup task", "module": "qr_preview_cache", "function": "start_cleanup_task", "line": 62}
{"timestamp": "2025-06-11T15:40:03.178895", "level": "INFO", "logger": "backend.main", "message": "QR preview cache initialized and cleanup task started", "module": "main", "function": "init_qr_preview_cache", "line": 345}
{"timestamp": "2025-06-11T15:40:27.778624", "level": "INFO", "logger": "backend.services.qr_preview_cache", "message": "QR preview cache cleanup task stopped", "module": "qr_preview_cache", "function": "stop", "line": 257}
{"timestamp": "2025-06-11T15:40:27.779229", "level": "INFO", "logger": "backend.main", "message": "QR preview cache stopped", "module": "main", "function": "shutdown_qr_preview_cache", "line": 352}
{"timestamp": "2025-06-11T15:40:31.284768", "level": "INFO", "logger": "backend.main", "message": "=== QRVibe API Startup: Logging to file is enabled ===", "module": "main", "function": "<module>", "line": 37}
{"timestamp": "2025-06-11T15:40:31.284937", "level": "INFO", "logger": "backend.main", "message": "Applying critical patches before initialization", "module": "main", "function": "<module>", "line": 43}
{"timestamp": "2025-06-11T15:40:31.285027", "level": "INFO", "logger": "backend.patches", "message": "Applying patches (none required)", "module": "__init__", "function": "apply_patches", "line": 11}
{"timestamp": "2025-06-11T15:40:31.285264", "level": "INFO", "logger": "backend.core.middleware.cors", "message": "Setting up CORS middleware with origins: ['https://127.0.0.1:3000', 'http://localhost:3000', 'http://127.0.0.1:3000', 'https://localhost:3000']", "module": "cors", "function": "setup_cors", "line": 86}
{"timestamp": "2025-06-11T15:40:31.285347", "level": "INFO", "logger": "backend.core.middleware.cors", "message": "CORS middleware configured - preflight requests should be handled automatically", "module": "cors", "function": "setup_cors", "line": 98}
{"timestamp": "2025-06-11T15:40:31.286288", "level": "INFO", "logger": "backend.middleware.security_middleware", "message": "Added Enhanced Security Middleware with tiered rate limiting", "module": "security_middleware", "function": "add_security_middleware", "line": 176}
{"timestamp": "2025-06-11T15:40:31.286398", "level": "INFO", "logger": "backend.main", "message": "Mounting storage directory: /Users/<USER>/aihub/qrvibe/storage", "module": "main", "function": "<module>", "line": 107}
{"timestamp": "2025-06-11T15:40:31.286523", "level": "INFO", "logger": "backend.main", "message": "Storage directory absolute path: /Users/<USER>/aihub/qrvibe/storage", "module": "main", "function": "<module>", "line": 119}
{"timestamp": "2025-06-11T15:40:31.722215", "level": "INFO", "logger": "root", "message": "Storage initialized: environment=test, storage_dir=/Users/<USER>/aihub/qrvibe/storage, s3_enabled=True", "module": "storage", "function": "__init__", "line": 41}
{"timestamp": "2025-06-11T15:40:31.739484", "level": "INFO", "logger": "root", "message": "S3 storage enabled with bucket: qrverse and CDN: https://dev-cdn.qrvibe.com", "module": "storage", "function": "__init__", "line": 59}
{"timestamp": "2025-06-11T15:40:31.796139", "level": "INFO", "logger": "root", "message": "Storage initialized: environment=test, storage_dir=/Users/<USER>/aihub/qrvibe/storage, s3_enabled=True", "module": "storage", "function": "__init__", "line": 41}
{"timestamp": "2025-06-11T15:40:31.799657", "level": "INFO", "logger": "root", "message": "S3 storage enabled with bucket: qrverse and CDN: https://dev-cdn.qrvibe.com", "module": "storage", "function": "__init__", "line": 59}
{"timestamp": "2025-06-11T15:40:31.826312", "level": "INFO", "logger": "backend.middleware.security_middleware", "message": "Enhanced security middleware initialized with tiered rate limiting", "module": "security_middleware", "function": "__init__", "line": 87}
{"timestamp": "2025-06-11T15:40:31.826399", "level": "INFO", "logger": "backend.core.middleware.csrf", "message": "CSRF Protection Middleware initialized with exclude paths: ['/auth/jwt/login', '/auth/logout', '/auth/register', '/auth/2fa', '/auth/me', '/auth/verify-email', '/auth/reset-password', '/admin/login', '/admin/logout']", "module": "csrf", "function": "__init__", "line": 44}
{"timestamp": "2025-06-11T15:40:31.830550", "level": "INFO", "logger": "backend.services.token_blacklist", "message": "Token blacklist service initialized with Redis", "module": "token_blacklist", "function": "initialize", "line": 59}
{"timestamp": "2025-06-11T15:40:31.830640", "level": "INFO", "logger": "backend.main", "message": "Token blacklist service initialized", "module": "main", "function": "initialize_token_blacklist", "line": 98}
{"timestamp": "2025-06-11T15:40:31.831351", "level": "INFO", "logger": "backend.services.qr_preview_cache", "message": "QR preview cache initialized at /Users/<USER>/aihub/qrvibe/storage/cache/qr_preview_cache.db", "module": "qr_preview_cache", "function": "_init_db", "line": 56}
{"timestamp": "2025-06-11T15:40:31.831436", "level": "INFO", "logger": "backend.services.qr_preview_cache", "message": "Started QR preview cache cleanup task", "module": "qr_preview_cache", "function": "start_cleanup_task", "line": 62}
{"timestamp": "2025-06-11T15:40:31.831491", "level": "INFO", "logger": "backend.main", "message": "QR preview cache initialized and cleanup task started", "module": "main", "function": "init_qr_preview_cache", "line": 345}
{"timestamp": "2025-06-11T15:41:03.710533", "level": "INFO", "logger": "backend.services.qr_preview_cache", "message": "QR preview cache cleanup task stopped", "module": "qr_preview_cache", "function": "stop", "line": 257}
{"timestamp": "2025-06-11T15:41:03.710832", "level": "INFO", "logger": "backend.main", "message": "QR preview cache stopped", "module": "main", "function": "shutdown_qr_preview_cache", "line": 352}
{"timestamp": "2025-06-11T15:41:07.197513", "level": "INFO", "logger": "backend.main", "message": "=== QRVibe API Startup: Logging to file is enabled ===", "module": "main", "function": "<module>", "line": 37}
{"timestamp": "2025-06-11T15:41:07.197602", "level": "INFO", "logger": "backend.main", "message": "Applying critical patches before initialization", "module": "main", "function": "<module>", "line": 43}
{"timestamp": "2025-06-11T15:41:07.197662", "level": "INFO", "logger": "backend.patches", "message": "Applying patches (none required)", "module": "__init__", "function": "apply_patches", "line": 11}
{"timestamp": "2025-06-11T15:41:07.197882", "level": "INFO", "logger": "backend.core.middleware.cors", "message": "Setting up CORS middleware with origins: ['http://localhost:3000', 'https://127.0.0.1:3000', 'https://localhost:3000', 'http://127.0.0.1:3000']", "module": "cors", "function": "setup_cors", "line": 86}
{"timestamp": "2025-06-11T15:41:07.197997", "level": "INFO", "logger": "backend.core.middleware.cors", "message": "CORS middleware configured - preflight requests should be handled automatically", "module": "cors", "function": "setup_cors", "line": 98}
{"timestamp": "2025-06-11T15:41:07.198903", "level": "INFO", "logger": "backend.middleware.security_middleware", "message": "Added Enhanced Security Middleware with tiered rate limiting", "module": "security_middleware", "function": "add_security_middleware", "line": 176}
{"timestamp": "2025-06-11T15:41:07.198977", "level": "INFO", "logger": "backend.main", "message": "Mounting storage directory: /Users/<USER>/aihub/qrvibe/storage", "module": "main", "function": "<module>", "line": 107}
{"timestamp": "2025-06-11T15:41:07.199075", "level": "INFO", "logger": "backend.main", "message": "Storage directory absolute path: /Users/<USER>/aihub/qrvibe/storage", "module": "main", "function": "<module>", "line": 119}
{"timestamp": "2025-06-11T15:41:07.616203", "level": "INFO", "logger": "root", "message": "Storage initialized: environment=test, storage_dir=/Users/<USER>/aihub/qrvibe/storage, s3_enabled=True", "module": "storage", "function": "__init__", "line": 41}
{"timestamp": "2025-06-11T15:41:07.619357", "level": "INFO", "logger": "root", "message": "S3 storage enabled with bucket: qrverse and CDN: https://dev-cdn.qrvibe.com", "module": "storage", "function": "__init__", "line": 59}
{"timestamp": "2025-06-11T15:41:07.725621", "level": "INFO", "logger": "root", "message": "Storage initialized: environment=test, storage_dir=/Users/<USER>/aihub/qrvibe/storage, s3_enabled=True", "module": "storage", "function": "__init__", "line": 41}
{"timestamp": "2025-06-11T15:41:07.728588", "level": "INFO", "logger": "root", "message": "S3 storage enabled with bucket: qrverse and CDN: https://dev-cdn.qrvibe.com", "module": "storage", "function": "__init__", "line": 59}
{"timestamp": "2025-06-11T15:41:07.751426", "level": "INFO", "logger": "backend.middleware.security_middleware", "message": "Enhanced security middleware initialized with tiered rate limiting", "module": "security_middleware", "function": "__init__", "line": 87}
{"timestamp": "2025-06-11T15:41:07.751537", "level": "INFO", "logger": "backend.core.middleware.csrf", "message": "CSRF Protection Middleware initialized with exclude paths: ['/auth/jwt/login', '/auth/logout', '/auth/register', '/auth/2fa', '/auth/me', '/auth/verify-email', '/auth/reset-password', '/admin/login', '/admin/logout']", "module": "csrf", "function": "__init__", "line": 44}
{"timestamp": "2025-06-11T15:41:07.756132", "level": "INFO", "logger": "backend.services.token_blacklist", "message": "Token blacklist service initialized with Redis", "module": "token_blacklist", "function": "initialize", "line": 59}
{"timestamp": "2025-06-11T15:41:07.756231", "level": "INFO", "logger": "backend.main", "message": "Token blacklist service initialized", "module": "main", "function": "initialize_token_blacklist", "line": 98}
{"timestamp": "2025-06-11T15:41:07.756857", "level": "INFO", "logger": "backend.services.qr_preview_cache", "message": "QR preview cache initialized at /Users/<USER>/aihub/qrvibe/storage/cache/qr_preview_cache.db", "module": "qr_preview_cache", "function": "_init_db", "line": 56}
{"timestamp": "2025-06-11T15:41:07.756923", "level": "INFO", "logger": "backend.services.qr_preview_cache", "message": "Started QR preview cache cleanup task", "module": "qr_preview_cache", "function": "start_cleanup_task", "line": 62}
{"timestamp": "2025-06-11T15:41:07.756978", "level": "INFO", "logger": "backend.main", "message": "QR preview cache initialized and cleanup task started", "module": "main", "function": "init_qr_preview_cache", "line": 345}
{"timestamp": "2025-06-11T15:42:14.148793", "level": "INFO", "logger": "backend.services.qr_preview_cache", "message": "QR preview cache cleanup task stopped", "module": "qr_preview_cache", "function": "stop", "line": 257}
{"timestamp": "2025-06-11T15:42:14.149114", "level": "INFO", "logger": "backend.main", "message": "QR preview cache stopped", "module": "main", "function": "shutdown_qr_preview_cache", "line": 352}
{"timestamp": "2025-06-11T15:42:30.631377", "level": "INFO", "logger": "backend.main", "message": "=== QRVibe API Startup: Logging to file is enabled ===", "module": "main", "function": "<module>", "line": 37}
{"timestamp": "2025-06-11T15:42:30.631860", "level": "INFO", "logger": "backend.main", "message": "Applying critical patches before initialization", "module": "main", "function": "<module>", "line": 43}
{"timestamp": "2025-06-11T15:42:30.631945", "level": "INFO", "logger": "backend.patches", "message": "Applying patches (none required)", "module": "__init__", "function": "apply_patches", "line": 11}
{"timestamp": "2025-06-11T15:42:30.632201", "level": "INFO", "logger": "backend.core.middleware.cors", "message": "Setting up CORS middleware with origins: ['https://localhost:3000', 'https://127.0.0.1:3000', 'http://127.0.0.1:3000', 'http://localhost:3000']", "module": "cors", "function": "setup_cors", "line": 86}
{"timestamp": "2025-06-11T15:42:30.632266", "level": "INFO", "logger": "backend.core.middleware.cors", "message": "CORS middleware configured - preflight requests should be handled automatically", "module": "cors", "function": "setup_cors", "line": 98}
{"timestamp": "2025-06-11T15:42:30.633402", "level": "INFO", "logger": "backend.middleware.security_middleware", "message": "Added Enhanced Security Middleware with tiered rate limiting", "module": "security_middleware", "function": "add_security_middleware", "line": 176}
{"timestamp": "2025-06-11T15:42:30.633528", "level": "INFO", "logger": "backend.main", "message": "Mounting storage directory: /Users/<USER>/aihub/qrvibe/storage", "module": "main", "function": "<module>", "line": 107}
{"timestamp": "2025-06-11T15:42:30.633691", "level": "INFO", "logger": "backend.main", "message": "Storage directory absolute path: /Users/<USER>/aihub/qrvibe/storage", "module": "main", "function": "<module>", "line": 119}
{"timestamp": "2025-06-11T15:42:31.068178", "level": "INFO", "logger": "root", "message": "Storage initialized: environment=test, storage_dir=/Users/<USER>/aihub/qrvibe/storage, s3_enabled=True", "module": "storage", "function": "__init__", "line": 41}
{"timestamp": "2025-06-11T15:42:31.071233", "level": "INFO", "logger": "root", "message": "S3 storage enabled with bucket: qrverse and CDN: https://dev-cdn.qrvibe.com", "module": "storage", "function": "__init__", "line": 59}
{"timestamp": "2025-06-11T15:42:31.118104", "level": "INFO", "logger": "root", "message": "Storage initialized: environment=test, storage_dir=/Users/<USER>/aihub/qrvibe/storage, s3_enabled=True", "module": "storage", "function": "__init__", "line": 41}
{"timestamp": "2025-06-11T15:42:31.121210", "level": "INFO", "logger": "root", "message": "S3 storage enabled with bucket: qrverse and CDN: https://dev-cdn.qrvibe.com", "module": "storage", "function": "__init__", "line": 59}
{"timestamp": "2025-06-11T15:42:31.143929", "level": "INFO", "logger": "backend.middleware.security_middleware", "message": "Enhanced security middleware initialized with tiered rate limiting", "module": "security_middleware", "function": "__init__", "line": 87}
{"timestamp": "2025-06-11T15:42:31.144040", "level": "INFO", "logger": "backend.core.middleware.csrf", "message": "CSRF Protection Middleware initialized with exclude paths: ['/auth/jwt/login', '/auth/logout', '/auth/register', '/auth/2fa', '/auth/me', '/auth/verify-email', '/auth/reset-password', '/admin/login', '/admin/logout']", "module": "csrf", "function": "__init__", "line": 44}
{"timestamp": "2025-06-11T15:42:31.172973", "level": "INFO", "logger": "backend.services.token_blacklist", "message": "Token blacklist service initialized with Redis", "module": "token_blacklist", "function": "initialize", "line": 59}
{"timestamp": "2025-06-11T15:42:31.173149", "level": "INFO", "logger": "backend.main", "message": "Token blacklist service initialized", "module": "main", "function": "initialize_token_blacklist", "line": 98}
{"timestamp": "2025-06-11T15:42:31.173893", "level": "INFO", "logger": "backend.services.qr_preview_cache", "message": "QR preview cache initialized at /Users/<USER>/aihub/qrvibe/storage/cache/qr_preview_cache.db", "module": "qr_preview_cache", "function": "_init_db", "line": 56}
{"timestamp": "2025-06-11T15:42:31.174053", "level": "INFO", "logger": "backend.services.qr_preview_cache", "message": "Started QR preview cache cleanup task", "module": "qr_preview_cache", "function": "start_cleanup_task", "line": 62}
{"timestamp": "2025-06-11T15:42:31.174139", "level": "INFO", "logger": "backend.main", "message": "QR preview cache initialized and cleanup task started", "module": "main", "function": "init_qr_preview_cache", "line": 345}
{"timestamp": "2025-06-11T15:42:50.915168", "level": "WARNING", "logger": "backend.core.security.jwt_keys", "message": "Unknown JWT kid requested: 244d2fff-8665-4c6c-b84d-c7c2b581bb0c", "module": "jwt_keys", "function": "get_verification_key", "line": 105}
{"timestamp": "2025-06-11T15:42:50.917158", "level": "WARNING", "logger": "backend.core.security.jwt_keys", "message": "Verification failed with key ID 244d2fff-8665-4c6c-b84d-c7c2b581bb0c: Unknown kid: 244d2fff-8665-4c6c-b84d-c7c2b581bb0c", "module": "jwt_keys", "function": "decode_token", "line": 203}
{"timestamp": "2025-06-11T15:42:50.990016", "level": "WARNING", "logger": "backend.core.security.jwt_keys", "message": "Verification failed with current key: Signature verification failed.", "module": "jwt_keys", "function": "decode_token", "line": 216}
{"timestamp": "2025-06-11T15:42:50.990492", "level": "ERROR", "logger": "backend.core.security.jwt_keys", "message": "Token verification failed with all keys. Errors: Key 69b5ada0-20e9-42b1-85e8-1ec80762bb7c: Signature verification failed.", "module": "jwt_keys", "function": "decode_token", "line": 239}
{"timestamp": "2025-06-11T15:42:50.990593", "level": "ERROR", "logger": "backend.core.security.jwt_keys", "message": "Token decode error: Token verification failed with all keys. Errors: Key 69b5ada0-20e9-42b1-85e8-1ec80762bb7c: Signature verification failed.", "module": "jwt_keys", "function": "decode_token", "line": 244}
{"timestamp": "2025-06-11T15:42:50.990692", "level": "ERROR", "logger": "backend.services.auth", "message": "JWT token validation failed: Failed to decode token: Token verification failed with all keys. Errors: Key 69b5ada0-20e9-42b1-85e8-1ec80762bb7c: Signature verification failed.", "module": "auth", "function": "authenticate_token", "line": 686}
{"timestamp": "2025-06-11T15:42:50.991540", "level": "WARNING", "logger": "backend.middleware.subscription_limits", "message": "Error authenticating token in subscription middleware: name 'ValidationError' is not defined", "module": "subscription_limits", "function": "_get_current_user", "line": 308}
{"timestamp": "2025-06-11T15:42:50.995754", "level": "WARNING", "logger": "backend.core.security.jwt_keys", "message": "Unknown JWT kid requested: 244d2fff-8665-4c6c-b84d-c7c2b581bb0c", "module": "jwt_keys", "function": "get_verification_key", "line": 105}
{"timestamp": "2025-06-11T15:42:50.996027", "level": "WARNING", "logger": "backend.core.security.jwt_keys", "message": "Verification failed with key ID 244d2fff-8665-4c6c-b84d-c7c2b581bb0c: Unknown kid: 244d2fff-8665-4c6c-b84d-c7c2b581bb0c", "module": "jwt_keys", "function": "decode_token", "line": 203}
{"timestamp": "2025-06-11T15:42:50.996215", "level": "WARNING", "logger": "backend.core.security.jwt_keys", "message": "Verification failed with current key: Signature verification failed.", "module": "jwt_keys", "function": "decode_token", "line": 216}
{"timestamp": "2025-06-11T15:42:50.996375", "level": "ERROR", "logger": "backend.core.security.jwt_keys", "message": "Token verification failed with all keys. Errors: Key 69b5ada0-20e9-42b1-85e8-1ec80762bb7c: Signature verification failed.", "module": "jwt_keys", "function": "decode_token", "line": 239}
{"timestamp": "2025-06-11T15:42:50.996462", "level": "ERROR", "logger": "backend.core.security.jwt_keys", "message": "Token decode error: Token verification failed with all keys. Errors: Key 69b5ada0-20e9-42b1-85e8-1ec80762bb7c: Signature verification failed.", "module": "jwt_keys", "function": "decode_token", "line": 244}
{"timestamp": "2025-06-11T15:42:50.996537", "level": "ERROR", "logger": "backend.services.auth", "message": "JWT token validation failed: Failed to decode token: Token verification failed with all keys. Errors: Key 69b5ada0-20e9-42b1-85e8-1ec80762bb7c: Signature verification failed.", "module": "auth", "function": "authenticate_token", "line": 686}
{"timestamp": "2025-06-11T15:42:50.997143", "level": "WARNING", "logger": "backend.middleware.subscription_limits", "message": "Auth error in subscription middleware: name 'ValidationError' is not defined", "module": "subscription_limits", "function": "dispatch", "line": 234}
{"timestamp": "2025-06-11T15:42:50.997660", "level": "WARNING", "logger": "backend.core.security.jwt_keys", "message": "Unknown JWT kid requested: 244d2fff-8665-4c6c-b84d-c7c2b581bb0c", "module": "jwt_keys", "function": "get_verification_key", "line": 105}
{"timestamp": "2025-06-11T15:42:50.997759", "level": "WARNING", "logger": "backend.core.security.jwt_keys", "message": "Verification failed with key ID 244d2fff-8665-4c6c-b84d-c7c2b581bb0c: Unknown kid: 244d2fff-8665-4c6c-b84d-c7c2b581bb0c", "module": "jwt_keys", "function": "decode_token", "line": 203}
{"timestamp": "2025-06-11T15:42:50.998053", "level": "WARNING", "logger": "backend.core.security.jwt_keys", "message": "Verification failed with current key: Signature verification failed.", "module": "jwt_keys", "function": "decode_token", "line": 216}
{"timestamp": "2025-06-11T15:42:50.998264", "level": "ERROR", "logger": "backend.core.security.jwt_keys", "message": "Token verification failed with all keys. Errors: Key 69b5ada0-20e9-42b1-85e8-1ec80762bb7c: Signature verification failed.", "module": "jwt_keys", "function": "decode_token", "line": 239}
{"timestamp": "2025-06-11T15:42:50.998518", "level": "ERROR", "logger": "backend.core.security.jwt_keys", "message": "Token decode error: Token verification failed with all keys. Errors: Key 69b5ada0-20e9-42b1-85e8-1ec80762bb7c: Signature verification failed.", "module": "jwt_keys", "function": "decode_token", "line": 244}
{"timestamp": "2025-06-11T15:42:50.998668", "level": "ERROR", "logger": "backend.services.auth", "message": "JWT token validation failed: Failed to decode token: Token verification failed with all keys. Errors: Key 69b5ada0-20e9-42b1-85e8-1ec80762bb7c: Signature verification failed.", "module": "auth", "function": "authenticate_token", "line": 686}
{"timestamp": "2025-06-11T15:43:20.981776", "level": "WARNING", "logger": "backend.core.security.jwt_keys", "message": "Unknown JWT kid requested: 244d2fff-8665-4c6c-b84d-c7c2b581bb0c", "module": "jwt_keys", "function": "get_verification_key", "line": 105}
{"timestamp": "2025-06-11T15:43:20.981891", "level": "WARNING", "logger": "backend.core.security.jwt_keys", "message": "Verification failed with key ID 244d2fff-8665-4c6c-b84d-c7c2b581bb0c: Unknown kid: 244d2fff-8665-4c6c-b84d-c7c2b581bb0c", "module": "jwt_keys", "function": "decode_token", "line": 203}
{"timestamp": "2025-06-11T15:43:20.984702", "level": "WARNING", "logger": "backend.core.security.jwt_keys", "message": "Verification failed with current key: Signature verification failed.", "module": "jwt_keys", "function": "decode_token", "line": 216}
{"timestamp": "2025-06-11T15:43:20.985014", "level": "ERROR", "logger": "backend.core.security.jwt_keys", "message": "Token verification failed with all keys. Errors: Key 69b5ada0-20e9-42b1-85e8-1ec80762bb7c: Signature verification failed.", "module": "jwt_keys", "function": "decode_token", "line": 239}
{"timestamp": "2025-06-11T15:43:20.985102", "level": "ERROR", "logger": "backend.core.security.jwt_keys", "message": "Token decode error: Token verification failed with all keys. Errors: Key 69b5ada0-20e9-42b1-85e8-1ec80762bb7c: Signature verification failed.", "module": "jwt_keys", "function": "decode_token", "line": 244}
{"timestamp": "2025-06-11T15:43:20.985199", "level": "ERROR", "logger": "backend.services.auth", "message": "JWT token validation failed: Failed to decode token: Token verification failed with all keys. Errors: Key 69b5ada0-20e9-42b1-85e8-1ec80762bb7c: Signature verification failed.", "module": "auth", "function": "authenticate_token", "line": 686}
{"timestamp": "2025-06-11T15:43:20.985695", "level": "WARNING", "logger": "backend.middleware.subscription_limits", "message": "Error authenticating token in subscription middleware: name 'ValidationError' is not defined", "module": "subscription_limits", "function": "_get_current_user", "line": 308}
{"timestamp": "2025-06-11T15:43:20.986399", "level": "WARNING", "logger": "backend.core.security.jwt_keys", "message": "Unknown JWT kid requested: 244d2fff-8665-4c6c-b84d-c7c2b581bb0c", "module": "jwt_keys", "function": "get_verification_key", "line": 105}
{"timestamp": "2025-06-11T15:43:20.986460", "level": "WARNING", "logger": "backend.core.security.jwt_keys", "message": "Verification failed with key ID 244d2fff-8665-4c6c-b84d-c7c2b581bb0c: Unknown kid: 244d2fff-8665-4c6c-b84d-c7c2b581bb0c", "module": "jwt_keys", "function": "decode_token", "line": 203}
{"timestamp": "2025-06-11T15:43:20.986613", "level": "WARNING", "logger": "backend.core.security.jwt_keys", "message": "Verification failed with current key: Signature verification failed.", "module": "jwt_keys", "function": "decode_token", "line": 216}
{"timestamp": "2025-06-11T15:43:20.986755", "level": "ERROR", "logger": "backend.core.security.jwt_keys", "message": "Token verification failed with all keys. Errors: Key 69b5ada0-20e9-42b1-85e8-1ec80762bb7c: Signature verification failed.", "module": "jwt_keys", "function": "decode_token", "line": 239}
{"timestamp": "2025-06-11T15:43:20.986823", "level": "ERROR", "logger": "backend.core.security.jwt_keys", "message": "Token decode error: Token verification failed with all keys. Errors: Key 69b5ada0-20e9-42b1-85e8-1ec80762bb7c: Signature verification failed.", "module": "jwt_keys", "function": "decode_token", "line": 244}
{"timestamp": "2025-06-11T15:43:20.986882", "level": "ERROR", "logger": "backend.services.auth", "message": "JWT token validation failed: Failed to decode token: Token verification failed with all keys. Errors: Key 69b5ada0-20e9-42b1-85e8-1ec80762bb7c: Signature verification failed.", "module": "auth", "function": "authenticate_token", "line": 686}
{"timestamp": "2025-06-11T15:43:20.987213", "level": "WARNING", "logger": "backend.middleware.subscription_limits", "message": "Auth error in subscription middleware: name 'ValidationError' is not defined", "module": "subscription_limits", "function": "dispatch", "line": 234}
{"timestamp": "2025-06-11T15:43:20.987531", "level": "WARNING", "logger": "backend.core.security.jwt_keys", "message": "Unknown JWT kid requested: 244d2fff-8665-4c6c-b84d-c7c2b581bb0c", "module": "jwt_keys", "function": "get_verification_key", "line": 105}
{"timestamp": "2025-06-11T15:43:20.987585", "level": "WARNING", "logger": "backend.core.security.jwt_keys", "message": "Verification failed with key ID 244d2fff-8665-4c6c-b84d-c7c2b581bb0c: Unknown kid: 244d2fff-8665-4c6c-b84d-c7c2b581bb0c", "module": "jwt_keys", "function": "decode_token", "line": 203}
{"timestamp": "2025-06-11T15:43:20.988705", "level": "WARNING", "logger": "backend.core.security.jwt_keys", "message": "Verification failed with current key: Signature verification failed.", "module": "jwt_keys", "function": "decode_token", "line": 216}
{"timestamp": "2025-06-11T15:43:20.988839", "level": "ERROR", "logger": "backend.core.security.jwt_keys", "message": "Token verification failed with all keys. Errors: Key 69b5ada0-20e9-42b1-85e8-1ec80762bb7c: Signature verification failed.", "module": "jwt_keys", "function": "decode_token", "line": 239}
{"timestamp": "2025-06-11T15:43:20.988928", "level": "ERROR", "logger": "backend.core.security.jwt_keys", "message": "Token decode error: Token verification failed with all keys. Errors: Key 69b5ada0-20e9-42b1-85e8-1ec80762bb7c: Signature verification failed.", "module": "jwt_keys", "function": "decode_token", "line": 244}
{"timestamp": "2025-06-11T15:43:20.988988", "level": "ERROR", "logger": "backend.services.auth", "message": "JWT token validation failed: Failed to decode token: Token verification failed with all keys. Errors: Key 69b5ada0-20e9-42b1-85e8-1ec80762bb7c: Signature verification failed.", "module": "auth", "function": "authenticate_token", "line": 686}
{"timestamp": "2025-06-11T15:43:39.524943", "level": "WARNING", "logger": "backend.core.security.jwt_keys", "message": "Unknown JWT kid requested: 244d2fff-8665-4c6c-b84d-c7c2b581bb0c", "module": "jwt_keys", "function": "get_verification_key", "line": 105}
{"timestamp": "2025-06-11T15:43:39.525283", "level": "WARNING", "logger": "backend.core.security.jwt_keys", "message": "Verification failed with key ID 244d2fff-8665-4c6c-b84d-c7c2b581bb0c: Unknown kid: 244d2fff-8665-4c6c-b84d-c7c2b581bb0c", "module": "jwt_keys", "function": "decode_token", "line": 203}
{"timestamp": "2025-06-11T15:43:39.526189", "level": "WARNING", "logger": "backend.core.security.jwt_keys", "message": "Verification failed with current key: Signature verification failed.", "module": "jwt_keys", "function": "decode_token", "line": 216}
{"timestamp": "2025-06-11T15:43:39.526889", "level": "ERROR", "logger": "backend.core.security.jwt_keys", "message": "Token verification failed with all keys. Errors: Key 69b5ada0-20e9-42b1-85e8-1ec80762bb7c: Signature verification failed.", "module": "jwt_keys", "function": "decode_token", "line": 239}
{"timestamp": "2025-06-11T15:43:39.527011", "level": "ERROR", "logger": "backend.core.security.jwt_keys", "message": "Token decode error: Token verification failed with all keys. Errors: Key 69b5ada0-20e9-42b1-85e8-1ec80762bb7c: Signature verification failed.", "module": "jwt_keys", "function": "decode_token", "line": 244}
{"timestamp": "2025-06-11T15:43:39.527194", "level": "ERROR", "logger": "backend.auth", "message": "Token validation error: Failed to decode token: Token verification failed with all keys. Errors: Key 69b5ada0-20e9-42b1-85e8-1ec80762bb7c: Signature verification failed.", "module": "__init__", "function": "get_current_user", "line": 193}
{"timestamp": "2025-06-11T15:43:39.528973", "level": "WARNING", "logger": "backend.core.security.jwt_keys", "message": "Unknown JWT kid requested: 244d2fff-8665-4c6c-b84d-c7c2b581bb0c", "module": "jwt_keys", "function": "get_verification_key", "line": 105}
{"timestamp": "2025-06-11T15:43:39.529040", "level": "WARNING", "logger": "backend.core.security.jwt_keys", "message": "Verification failed with key ID 244d2fff-8665-4c6c-b84d-c7c2b581bb0c: Unknown kid: 244d2fff-8665-4c6c-b84d-c7c2b581bb0c", "module": "jwt_keys", "function": "decode_token", "line": 203}
{"timestamp": "2025-06-11T15:43:39.529233", "level": "WARNING", "logger": "backend.core.security.jwt_keys", "message": "Verification failed with current key: Signature verification failed.", "module": "jwt_keys", "function": "decode_token", "line": 216}
{"timestamp": "2025-06-11T15:43:39.529392", "level": "ERROR", "logger": "backend.core.security.jwt_keys", "message": "Token verification failed with all keys. Errors: Key 69b5ada0-20e9-42b1-85e8-1ec80762bb7c: Signature verification failed.", "module": "jwt_keys", "function": "decode_token", "line": 239}
{"timestamp": "2025-06-11T15:43:39.529456", "level": "ERROR", "logger": "backend.core.security.jwt_keys", "message": "Token decode error: Token verification failed with all keys. Errors: Key 69b5ada0-20e9-42b1-85e8-1ec80762bb7c: Signature verification failed.", "module": "jwt_keys", "function": "decode_token", "line": 244}
{"timestamp": "2025-06-11T15:43:39.529553", "level": "ERROR", "logger": "backend.services.auth", "message": "JWT token validation failed: Failed to decode token: Token verification failed with all keys. Errors: Key 69b5ada0-20e9-42b1-85e8-1ec80762bb7c: Signature verification failed.", "module": "auth", "function": "authenticate_token", "line": 686}
{"timestamp": "2025-06-11T15:43:39.530505", "level": "WARNING", "logger": "backend.middleware.subscription_limits", "message": "Error authenticating token in subscription middleware: name 'ValidationError' is not defined", "module": "subscription_limits", "function": "_get_current_user", "line": 308}
{"timestamp": "2025-06-11T15:43:39.531119", "level": "WARNING", "logger": "backend.core.security.jwt_keys", "message": "Unknown JWT kid requested: 244d2fff-8665-4c6c-b84d-c7c2b581bb0c", "module": "jwt_keys", "function": "get_verification_key", "line": 105}
{"timestamp": "2025-06-11T15:43:39.531174", "level": "WARNING", "logger": "backend.core.security.jwt_keys", "message": "Verification failed with key ID 244d2fff-8665-4c6c-b84d-c7c2b581bb0c: Unknown kid: 244d2fff-8665-4c6c-b84d-c7c2b581bb0c", "module": "jwt_keys", "function": "decode_token", "line": 203}
{"timestamp": "2025-06-11T15:43:39.531315", "level": "WARNING", "logger": "backend.core.security.jwt_keys", "message": "Verification failed with current key: Signature verification failed.", "module": "jwt_keys", "function": "decode_token", "line": 216}
{"timestamp": "2025-06-11T15:43:39.531454", "level": "ERROR", "logger": "backend.core.security.jwt_keys", "message": "Token verification failed with all keys. Errors: Key 69b5ada0-20e9-42b1-85e8-1ec80762bb7c: Signature verification failed.", "module": "jwt_keys", "function": "decode_token", "line": 239}
{"timestamp": "2025-06-11T15:43:39.531526", "level": "ERROR", "logger": "backend.core.security.jwt_keys", "message": "Token decode error: Token verification failed with all keys. Errors: Key 69b5ada0-20e9-42b1-85e8-1ec80762bb7c: Signature verification failed.", "module": "jwt_keys", "function": "decode_token", "line": 244}
{"timestamp": "2025-06-11T15:43:39.531603", "level": "ERROR", "logger": "backend.auth", "message": "Token validation error: Failed to decode token: Token verification failed with all keys. Errors: Key 69b5ada0-20e9-42b1-85e8-1ec80762bb7c: Signature verification failed.", "module": "__init__", "function": "get_current_user", "line": 193}
{"timestamp": "2025-06-11T15:43:51.093014", "level": "INFO", "logger": "backend.services.enhanced_rate_limit", "message": "Auth event for anonymous: login_attempt (failure)", "module": "enhanced_rate_limit", "function": "track_auth_event", "line": 182}
{"timestamp": "2025-06-11T15:43:53.422274", "level": "INFO", "logger": "backend.services.auth", "message": "Authentication successful for user: <EMAIL>", "module": "auth", "function": "authenticate_user", "line": 266}
{"timestamp": "2025-06-11T15:43:53.426274", "level": "INFO", "logger": "backend.routes.auth", "message": "User logged in successfully: <EMAIL>", "module": "auth", "function": "login", "line": 218}
{"timestamp": "2025-06-11T15:43:53.427919", "level": "INFO", "logger": "backend.services.enhanced_rate_limit", "message": "Auth event for user e4ae2d8f-b32f-52d7-99cd-8a33b19652c7: login_success (success)", "module": "enhanced_rate_limit", "function": "track_auth_event", "line": 182}
{"timestamp": "2025-06-11T15:43:54.071912", "level": "INFO", "logger": "backend.services.adaptive_session", "message": "Adaptive session for user e4ae2d8f-b32f-52d7-99cd-8a33b19652c7: risk=low, duration=86400s", "module": "adaptive_session", "function": "get_session_expiry", "line": 161}
{"timestamp": "2025-06-11T15:43:54.072018", "level": "INFO", "logger": "backend.routes.auth", "message": "Adaptive session <NAME_EMAIL>: duration=86400s", "module": "auth", "function": "login", "line": 279}
{"timestamp": "2025-06-11T15:43:54.072072", "level": "INFO", "logger": "backend.routes.auth", "message": "Bearer token authentication issued <NAME_EMAIL>", "module": "auth", "function": "login", "line": 284}
