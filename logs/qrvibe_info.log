{"timestamp": "2025-06-11T14:25:07.887794", "level": "ERROR", "logger": "backend.services.qr_preview", "message": "Error formatting QR data for type qrtype.url: TRANSLATION_SERVICE", "module": "qr_preview", "function": "_format_qr_data", "line": 1639, "exception": "Traceback (most recent call last):\n  File \"/Users/<USER>/aihub/qrvibe/backend/services/qr_preview.py\", line 1602, in _format_qr_data\n    elif qr_type_str == QRType.TRANSLATION_SERVICE.lower():\n                        ^^^^^^^^^^^^^^^^^^^^^^^^^^\n  File \"/opt/homebrew/Cellar/python@3.11/3.11.11/Frameworks/Python.framework/Versions/3.11/lib/python3.11/enum.py\", line 786, in __getattr__\n    raise AttributeError(name) from None\nAttributeError: TRANSLATION_SERVICE"}
{"timestamp": "2025-06-11T14:25:14.053067", "level": "ERROR", "logger": "backend.services.qr_preview", "message": "Error formatting QR data for type qrtype.scooter_unlock: TRANSLATION_SERVICE", "module": "qr_preview", "function": "_format_qr_data", "line": 1639, "exception": "Traceback (most recent call last):\n  File \"/Users/<USER>/aihub/qrvibe/backend/services/qr_preview.py\", line 1602, in _format_qr_data\n    elif qr_type_str == QRType.TRANSLATION_SERVICE.lower():\n                        ^^^^^^^^^^^^^^^^^^^^^^^^^^\n  File \"/opt/homebrew/Cellar/python@3.11/3.11.11/Frameworks/Python.framework/Versions/3.11/lib/python3.11/enum.py\", line 786, in __getattr__\n    raise AttributeError(name) from None\nAttributeError: TRANSLATION_SERVICE"}
{"timestamp": "2025-06-11T14:25:17.171882", "level": "ERROR", "logger": "backend.services.qr_preview", "message": "Error formatting QR data for type qrtype.scooter_unlock: TRANSLATION_SERVICE", "module": "qr_preview", "function": "_format_qr_data", "line": 1639, "exception": "Traceback (most recent call last):\n  File \"/Users/<USER>/aihub/qrvibe/backend/services/qr_preview.py\", line 1602, in _format_qr_data\n    elif qr_type_str == QRType.TRANSLATION_SERVICE.lower():\n                        ^^^^^^^^^^^^^^^^^^^^^^^^^^\n  File \"/opt/homebrew/Cellar/python@3.11/3.11.11/Frameworks/Python.framework/Versions/3.11/lib/python3.11/enum.py\", line 786, in __getattr__\n    raise AttributeError(name) from None\nAttributeError: TRANSLATION_SERVICE"}
{"timestamp": "2025-06-11T14:25:17.235930", "level": "ERROR", "logger": "backend.services.qr_preview", "message": "Error formatting QR data for type qrtype.scooter_unlock: TRANSLATION_SERVICE", "module": "qr_preview", "function": "_format_qr_data", "line": 1639, "exception": "Traceback (most recent call last):\n  File \"/Users/<USER>/aihub/qrvibe/backend/services/qr_preview.py\", line 1602, in _format_qr_data\n    elif qr_type_str == QRType.TRANSLATION_SERVICE.lower():\n                        ^^^^^^^^^^^^^^^^^^^^^^^^^^\n  File \"/opt/homebrew/Cellar/python@3.11/3.11.11/Frameworks/Python.framework/Versions/3.11/lib/python3.11/enum.py\", line 786, in __getattr__\n    raise AttributeError(name) from None\nAttributeError: TRANSLATION_SERVICE"}
{"timestamp": "2025-06-11T14:25:17.794781", "level": "ERROR", "logger": "backend.services.qr_preview", "message": "Error formatting QR data for type qrtype.scooter_unlock: TRANSLATION_SERVICE", "module": "qr_preview", "function": "_format_qr_data", "line": 1639, "exception": "Traceback (most recent call last):\n  File \"/Users/<USER>/aihub/qrvibe/backend/services/qr_preview.py\", line 1602, in _format_qr_data\n    elif qr_type_str == QRType.TRANSLATION_SERVICE.lower():\n                        ^^^^^^^^^^^^^^^^^^^^^^^^^^\n  File \"/opt/homebrew/Cellar/python@3.11/3.11.11/Frameworks/Python.framework/Versions/3.11/lib/python3.11/enum.py\", line 786, in __getattr__\n    raise AttributeError(name) from None\nAttributeError: TRANSLATION_SERVICE"}
{"timestamp": "2025-06-11T14:25:17.852286", "level": "ERROR", "logger": "backend.services.qr_preview", "message": "Error formatting QR data for type qrtype.scooter_unlock: TRANSLATION_SERVICE", "module": "qr_preview", "function": "_format_qr_data", "line": 1639, "exception": "Traceback (most recent call last):\n  File \"/Users/<USER>/aihub/qrvibe/backend/services/qr_preview.py\", line 1602, in _format_qr_data\n    elif qr_type_str == QRType.TRANSLATION_SERVICE.lower():\n                        ^^^^^^^^^^^^^^^^^^^^^^^^^^\n  File \"/opt/homebrew/Cellar/python@3.11/3.11.11/Frameworks/Python.framework/Versions/3.11/lib/python3.11/enum.py\", line 786, in __getattr__\n    raise AttributeError(name) from None\nAttributeError: TRANSLATION_SERVICE"}
{"timestamp": "2025-06-11T14:28:07.682003", "level": "INFO", "logger": "backend.services.qr_preview_cache", "message": "QR preview cache cleanup task stopped", "module": "qr_preview_cache", "function": "stop", "line": 257}
{"timestamp": "2025-06-11T14:28:07.682562", "level": "INFO", "logger": "backend.main", "message": "QR preview cache stopped", "module": "main", "function": "shutdown_qr_preview_cache", "line": 352}
{"timestamp": "2025-06-11T14:28:12.816387", "level": "INFO", "logger": "backend.main", "message": "=== QRVibe API Startup: Logging to file is enabled ===", "module": "main", "function": "<module>", "line": 37}
{"timestamp": "2025-06-11T14:28:12.816516", "level": "INFO", "logger": "backend.main", "message": "Applying critical patches before initialization", "module": "main", "function": "<module>", "line": 43}
{"timestamp": "2025-06-11T14:28:12.816598", "level": "INFO", "logger": "backend.patches", "message": "Applying patches (none required)", "module": "__init__", "function": "apply_patches", "line": 11}
{"timestamp": "2025-06-11T14:28:12.817400", "level": "INFO", "logger": "backend.core.middleware.cors", "message": "Setting up CORS middleware with origins: ['https://127.0.0.1:3000', 'http://127.0.0.1:3000', 'http://localhost:3000', 'https://localhost:3000']", "module": "cors", "function": "setup_cors", "line": 86}
{"timestamp": "2025-06-11T14:28:12.817502", "level": "INFO", "logger": "backend.core.middleware.cors", "message": "CORS middleware configured - preflight requests should be handled automatically", "module": "cors", "function": "setup_cors", "line": 98}
{"timestamp": "2025-06-11T14:28:12.819272", "level": "INFO", "logger": "backend.middleware.security_middleware", "message": "Added Enhanced Security Middleware with tiered rate limiting", "module": "security_middleware", "function": "add_security_middleware", "line": 176}
{"timestamp": "2025-06-11T14:28:12.819428", "level": "INFO", "logger": "backend.main", "message": "Mounting storage directory: /Users/<USER>/aihub/qrvibe/storage", "module": "main", "function": "<module>", "line": 107}
{"timestamp": "2025-06-11T14:28:12.819598", "level": "INFO", "logger": "backend.main", "message": "Storage directory absolute path: /Users/<USER>/aihub/qrvibe/storage", "module": "main", "function": "<module>", "line": 119}
{"timestamp": "2025-06-11T14:28:13.400472", "level": "INFO", "logger": "root", "message": "Storage initialized: environment=test, storage_dir=/Users/<USER>/aihub/qrvibe/storage, s3_enabled=True", "module": "storage", "function": "__init__", "line": 41}
{"timestamp": "2025-06-11T14:28:13.403787", "level": "INFO", "logger": "root", "message": "S3 storage enabled with bucket: qrverse and CDN: https://dev-cdn.qrvibe.com", "module": "storage", "function": "__init__", "line": 59}
{"timestamp": "2025-06-11T14:28:13.464757", "level": "INFO", "logger": "root", "message": "Storage initialized: environment=test, storage_dir=/Users/<USER>/aihub/qrvibe/storage, s3_enabled=True", "module": "storage", "function": "__init__", "line": 41}
{"timestamp": "2025-06-11T14:28:13.468727", "level": "INFO", "logger": "root", "message": "S3 storage enabled with bucket: qrverse and CDN: https://dev-cdn.qrvibe.com", "module": "storage", "function": "__init__", "line": 59}
{"timestamp": "2025-06-11T14:28:13.664848", "level": "INFO", "logger": "backend.middleware.security_middleware", "message": "Enhanced security middleware initialized with tiered rate limiting", "module": "security_middleware", "function": "__init__", "line": 87}
{"timestamp": "2025-06-11T14:28:13.664999", "level": "INFO", "logger": "backend.core.middleware.csrf", "message": "CSRF Protection Middleware initialized with exclude paths: ['/auth/jwt/login', '/auth/logout', '/auth/register', '/auth/2fa', '/auth/me', '/auth/verify-email', '/auth/reset-password', '/admin/login', '/admin/logout']", "module": "csrf", "function": "__init__", "line": 44}
{"timestamp": "2025-06-11T14:28:13.671731", "level": "INFO", "logger": "backend.services.token_blacklist", "message": "Token blacklist service initialized with Redis", "module": "token_blacklist", "function": "initialize", "line": 59}
{"timestamp": "2025-06-11T14:28:13.671844", "level": "INFO", "logger": "backend.main", "message": "Token blacklist service initialized", "module": "main", "function": "initialize_token_blacklist", "line": 98}
{"timestamp": "2025-06-11T14:28:13.674950", "level": "INFO", "logger": "backend.services.qr_preview_cache", "message": "QR preview cache initialized at /Users/<USER>/aihub/qrvibe/storage/cache/qr_preview_cache.db", "module": "qr_preview_cache", "function": "_init_db", "line": 56}
{"timestamp": "2025-06-11T14:28:13.675044", "level": "INFO", "logger": "backend.services.qr_preview_cache", "message": "Started QR preview cache cleanup task", "module": "qr_preview_cache", "function": "start_cleanup_task", "line": 62}
{"timestamp": "2025-06-11T14:28:13.675129", "level": "INFO", "logger": "backend.main", "message": "QR preview cache initialized and cleanup task started", "module": "main", "function": "init_qr_preview_cache", "line": 345}
{"timestamp": "2025-06-11T14:28:57.063867", "level": "INFO", "logger": "backend.services.qr_preview_cache", "message": "QR preview cache cleanup task stopped", "module": "qr_preview_cache", "function": "stop", "line": 257}
{"timestamp": "2025-06-11T14:28:57.064277", "level": "INFO", "logger": "backend.main", "message": "QR preview cache stopped", "module": "main", "function": "shutdown_qr_preview_cache", "line": 352}
