{"timestamp": "2025-06-11T14:33:41.498509", "level": "INFO", "logger": "backend.main", "message": "=== QRVibe API Startup: Logging to file is enabled ===", "module": "main", "function": "<module>", "line": 37}
{"timestamp": "2025-06-11T14:33:41.498648", "level": "INFO", "logger": "backend.main", "message": "Applying critical patches before initialization", "module": "main", "function": "<module>", "line": 43}
{"timestamp": "2025-06-11T14:33:41.498728", "level": "INFO", "logger": "backend.patches", "message": "Applying patches (none required)", "module": "__init__", "function": "apply_patches", "line": 11}
{"timestamp": "2025-06-11T14:33:41.499001", "level": "INFO", "logger": "backend.core.middleware.cors", "message": "Setting up CORS middleware with origins: ['https://127.0.0.1:3000', 'https://localhost:3000', 'http://localhost:3000', 'http://127.0.0.1:3000']", "module": "cors", "function": "setup_cors", "line": 86}
{"timestamp": "2025-06-11T14:33:41.499070", "level": "INFO", "logger": "backend.core.middleware.cors", "message": "CORS middleware configured - preflight requests should be handled automatically", "module": "cors", "function": "setup_cors", "line": 98}
{"timestamp": "2025-06-11T14:33:41.500335", "level": "INFO", "logger": "backend.middleware.security_middleware", "message": "Added Enhanced Security Middleware with tiered rate limiting", "module": "security_middleware", "function": "add_security_middleware", "line": 176}
{"timestamp": "2025-06-11T14:33:41.500442", "level": "INFO", "logger": "backend.main", "message": "Mounting storage directory: /Users/<USER>/aihub/qrvibe/storage", "module": "main", "function": "<module>", "line": 107}
{"timestamp": "2025-06-11T14:33:41.500593", "level": "INFO", "logger": "backend.main", "message": "Storage directory absolute path: /Users/<USER>/aihub/qrvibe/storage", "module": "main", "function": "<module>", "line": 119}
{"timestamp": "2025-06-11T14:33:41.957152", "level": "INFO", "logger": "root", "message": "Storage initialized: environment=test, storage_dir=/Users/<USER>/aihub/qrvibe/storage, s3_enabled=True", "module": "storage", "function": "__init__", "line": 41}
{"timestamp": "2025-06-11T14:33:41.960350", "level": "INFO", "logger": "root", "message": "S3 storage enabled with bucket: qrverse and CDN: https://dev-cdn.qrvibe.com", "module": "storage", "function": "__init__", "line": 59}
{"timestamp": "2025-06-11T14:33:42.014380", "level": "INFO", "logger": "root", "message": "Storage initialized: environment=test, storage_dir=/Users/<USER>/aihub/qrvibe/storage, s3_enabled=True", "module": "storage", "function": "__init__", "line": 41}
{"timestamp": "2025-06-11T14:33:42.017883", "level": "INFO", "logger": "root", "message": "S3 storage enabled with bucket: qrverse and CDN: https://dev-cdn.qrvibe.com", "module": "storage", "function": "__init__", "line": 59}
{"timestamp": "2025-06-11T14:33:42.043939", "level": "INFO", "logger": "backend.middleware.security_middleware", "message": "Enhanced security middleware initialized with tiered rate limiting", "module": "security_middleware", "function": "__init__", "line": 87}
{"timestamp": "2025-06-11T14:33:42.044057", "level": "INFO", "logger": "backend.core.middleware.csrf", "message": "CSRF Protection Middleware initialized with exclude paths: ['/auth/jwt/login', '/auth/logout', '/auth/register', '/auth/2fa', '/auth/me', '/auth/verify-email', '/auth/reset-password', '/admin/login', '/admin/logout']", "module": "csrf", "function": "__init__", "line": 44}
{"timestamp": "2025-06-11T14:33:42.049327", "level": "INFO", "logger": "backend.services.token_blacklist", "message": "Token blacklist service initialized with Redis", "module": "token_blacklist", "function": "initialize", "line": 59}
{"timestamp": "2025-06-11T14:33:42.049415", "level": "INFO", "logger": "backend.main", "message": "Token blacklist service initialized", "module": "main", "function": "initialize_token_blacklist", "line": 98}
{"timestamp": "2025-06-11T14:33:42.050271", "level": "INFO", "logger": "backend.services.qr_preview_cache", "message": "QR preview cache initialized at /Users/<USER>/aihub/qrvibe/storage/cache/qr_preview_cache.db", "module": "qr_preview_cache", "function": "_init_db", "line": 56}
{"timestamp": "2025-06-11T14:33:42.050437", "level": "INFO", "logger": "backend.services.qr_preview_cache", "message": "Started QR preview cache cleanup task", "module": "qr_preview_cache", "function": "start_cleanup_task", "line": 62}
{"timestamp": "2025-06-11T14:33:42.050514", "level": "INFO", "logger": "backend.main", "message": "QR preview cache initialized and cleanup task started", "module": "main", "function": "init_qr_preview_cache", "line": 345}
{"timestamp": "2025-06-11T14:33:58.380975", "level": "INFO", "logger": "backend.services.enhanced_rate_limit", "message": "Auth event for anonymous: login_attempt (failure)", "module": "enhanced_rate_limit", "function": "track_auth_event", "line": 182}
{"timestamp": "2025-06-11T14:33:58.846169", "level": "INFO", "logger": "backend.services.auth", "message": "Authentication successful for user: <EMAIL>", "module": "auth", "function": "authenticate_user", "line": 266}
{"timestamp": "2025-06-11T14:33:58.847447", "level": "INFO", "logger": "backend.routes.auth", "message": "User logged in successfully: <EMAIL>", "module": "auth", "function": "login", "line": 218}
{"timestamp": "2025-06-11T14:33:58.848180", "level": "INFO", "logger": "backend.services.enhanced_rate_limit", "message": "Auth event for user e4ae2d8f-b32f-52d7-99cd-8a33b19652c7: login_success (success)", "module": "enhanced_rate_limit", "function": "track_auth_event", "line": 182}
{"timestamp": "2025-06-11T14:33:58.899298", "level": "INFO", "logger": "backend.services.adaptive_session", "message": "Adaptive session for user e4ae2d8f-b32f-52d7-99cd-8a33b19652c7: risk=low, duration=86400s", "module": "adaptive_session", "function": "get_session_expiry", "line": 161}
{"timestamp": "2025-06-11T14:33:58.899397", "level": "INFO", "logger": "backend.routes.auth", "message": "Adaptive session <NAME_EMAIL>: duration=86400s", "module": "auth", "function": "login", "line": 279}
{"timestamp": "2025-06-11T14:33:58.899458", "level": "INFO", "logger": "backend.routes.auth", "message": "Bearer token authentication issued <NAME_EMAIL>", "module": "auth", "function": "login", "line": 284}
{"timestamp": "2025-06-11T14:34:03.053890", "level": "INFO", "logger": "root", "message": "Getting feature matrix for user e4ae2d8f-b32f-52d7-99cd-8a33b19652c7 (email: <EMAIL>)", "module": "subscription", "function": "get_user_feature_matrix", "line": 142}
{"timestamp": "2025-06-11T14:34:03.063656", "level": "INFO", "logger": "root", "message": "Found subscription for user e4ae2d8f-b32f-52d7-99cd-8a33b19652c7: plan=enterprise, status=active", "module": "subscription", "function": "get_user_feature_matrix", "line": 152}
{"timestamp": "2025-06-11T14:34:03.073461", "level": "INFO", "logger": "root", "message": "Found 0 team addons for user e4ae2d8f-b32f-52d7-99cd-8a33b19652c7", "module": "subscription", "function": "get_user_feature_matrix", "line": 181}
{"timestamp": "2025-06-11T14:34:03.073688", "level": "INFO", "logger": "root", "message": "Retrieved base features for plan enterprise", "module": "subscription", "function": "get_user_feature_matrix", "line": 191}
{"timestamp": "2025-06-11T14:34:03.090386", "level": "INFO", "logger": "root", "message": "Successfully generated feature matrix for user e4ae2d8f-b32f-52d7-99cd-8a33b19652c7", "module": "subscription", "function": "get_user_feature_matrix", "line": 284}
{"timestamp": "2025-06-11T14:46:59.372489", "level": "INFO", "logger": "root", "message": "Getting feature matrix for user e4ae2d8f-b32f-52d7-99cd-8a33b19652c7 (email: <EMAIL>)", "module": "subscription", "function": "get_user_feature_matrix", "line": 142}
{"timestamp": "2025-06-11T14:46:59.482915", "level": "INFO", "logger": "root", "message": "Found subscription for user e4ae2d8f-b32f-52d7-99cd-8a33b19652c7: plan=enterprise, status=active", "module": "subscription", "function": "get_user_feature_matrix", "line": 152}
{"timestamp": "2025-06-11T14:46:59.573722", "level": "INFO", "logger": "root", "message": "Found 0 team addons for user e4ae2d8f-b32f-52d7-99cd-8a33b19652c7", "module": "subscription", "function": "get_user_feature_matrix", "line": 181}
{"timestamp": "2025-06-11T14:46:59.574219", "level": "INFO", "logger": "root", "message": "Retrieved base features for plan enterprise", "module": "subscription", "function": "get_user_feature_matrix", "line": 191}
{"timestamp": "2025-06-11T14:46:59.700846", "level": "INFO", "logger": "root", "message": "Successfully generated feature matrix for user e4ae2d8f-b32f-52d7-99cd-8a33b19652c7", "module": "subscription", "function": "get_user_feature_matrix", "line": 284}
{"timestamp": "2025-06-11T15:01:42.312523", "level": "INFO", "logger": "root", "message": "Getting feature matrix for user e4ae2d8f-b32f-52d7-99cd-8a33b19652c7 (email: <EMAIL>)", "module": "subscription", "function": "get_user_feature_matrix", "line": 142}
{"timestamp": "2025-06-11T15:01:42.348482", "level": "INFO", "logger": "root", "message": "Found subscription for user e4ae2d8f-b32f-52d7-99cd-8a33b19652c7: plan=enterprise, status=active", "module": "subscription", "function": "get_user_feature_matrix", "line": 152}
{"timestamp": "2025-06-11T15:01:42.603222", "level": "INFO", "logger": "root", "message": "Found 0 team addons for user e4ae2d8f-b32f-52d7-99cd-8a33b19652c7", "module": "subscription", "function": "get_user_feature_matrix", "line": 181}
{"timestamp": "2025-06-11T15:01:42.604164", "level": "INFO", "logger": "root", "message": "Retrieved base features for plan enterprise", "module": "subscription", "function": "get_user_feature_matrix", "line": 191}
{"timestamp": "2025-06-11T15:01:42.700140", "level": "INFO", "logger": "root", "message": "Successfully generated feature matrix for user e4ae2d8f-b32f-52d7-99cd-8a33b19652c7", "module": "subscription", "function": "get_user_feature_matrix", "line": 284}
{"timestamp": "2025-06-11T15:04:19.579284", "level": "WARNING", "logger": "backend.core.middleware.csrf", "message": "CSRF token missing for POST /qr/preview/svg - Header present: False, Cookie present: False", "module": "csrf", "function": "dispatch", "line": 66}
{"timestamp": "2025-06-11T15:04:19.580428", "level": "WARNING", "logger": "backend.core.middleware.csrf", "message": "CSRF validation failed for POST /qr/preview/svg - IP: 127.0.0.1", "module": "csrf", "function": "dispatch", "line": 75}
{"timestamp": "2025-06-11T15:04:42.938761", "level": "WARNING", "logger": "backend.core.middleware.csrf", "message": "CSRF token missing for POST /qr/preview/svg - Header present: False, Cookie present: False", "module": "csrf", "function": "dispatch", "line": 66}
{"timestamp": "2025-06-11T15:04:42.939220", "level": "WARNING", "logger": "backend.core.middleware.csrf", "message": "CSRF validation failed for POST /qr/preview/svg - IP: 127.0.0.1", "module": "csrf", "function": "dispatch", "line": 75}
{"timestamp": "2025-06-11T15:06:39.605030", "level": "INFO", "logger": "backend.services.enhanced_rate_limit", "message": "Auth event for anonymous: login_attempt (failure)", "module": "enhanced_rate_limit", "function": "track_auth_event", "line": 182}
{"timestamp": "2025-06-11T15:06:39.770024", "level": "INFO", "logger": "backend.services.auth", "message": "Authentication successful for user: <EMAIL>", "module": "auth", "function": "authenticate_user", "line": 266}
{"timestamp": "2025-06-11T15:06:39.771290", "level": "INFO", "logger": "backend.routes.auth", "message": "User logged in successfully: <EMAIL>", "module": "auth", "function": "login", "line": 218}
{"timestamp": "2025-06-11T15:06:39.773646", "level": "INFO", "logger": "backend.services.enhanced_rate_limit", "message": "Auth event for user e4ae2d8f-b32f-52d7-99cd-8a33b19652c7: login_success (success)", "module": "enhanced_rate_limit", "function": "track_auth_event", "line": 182}
{"timestamp": "2025-06-11T15:06:39.816726", "level": "INFO", "logger": "backend.services.adaptive_session", "message": "Adaptive session for user e4ae2d8f-b32f-52d7-99cd-8a33b19652c7: risk=low, duration=86400s", "module": "adaptive_session", "function": "get_session_expiry", "line": 161}
{"timestamp": "2025-06-11T15:06:39.816849", "level": "INFO", "logger": "backend.routes.auth", "message": "Adaptive session <NAME_EMAIL>: duration=86400s", "module": "auth", "function": "login", "line": 279}
{"timestamp": "2025-06-11T15:06:39.816922", "level": "INFO", "logger": "backend.routes.auth", "message": "Bearer token authentication issued <NAME_EMAIL>", "module": "auth", "function": "login", "line": 284}
{"timestamp": "2025-06-11T15:06:41.129579", "level": "INFO", "logger": "root", "message": "Getting feature matrix for user e4ae2d8f-b32f-52d7-99cd-8a33b19652c7 (email: <EMAIL>)", "module": "subscription", "function": "get_user_feature_matrix", "line": 142}
{"timestamp": "2025-06-11T15:06:41.135421", "level": "INFO", "logger": "root", "message": "Found subscription for user e4ae2d8f-b32f-52d7-99cd-8a33b19652c7: plan=enterprise, status=active", "module": "subscription", "function": "get_user_feature_matrix", "line": 152}
{"timestamp": "2025-06-11T15:06:41.150110", "level": "INFO", "logger": "root", "message": "Found 0 team addons for user e4ae2d8f-b32f-52d7-99cd-8a33b19652c7", "module": "subscription", "function": "get_user_feature_matrix", "line": 181}
{"timestamp": "2025-06-11T15:06:41.150262", "level": "INFO", "logger": "root", "message": "Retrieved base features for plan enterprise", "module": "subscription", "function": "get_user_feature_matrix", "line": 191}
{"timestamp": "2025-06-11T15:06:41.164163", "level": "INFO", "logger": "root", "message": "Successfully generated feature matrix for user e4ae2d8f-b32f-52d7-99cd-8a33b19652c7", "module": "subscription", "function": "get_user_feature_matrix", "line": 284}
{"timestamp": "2025-06-11T15:14:16.010813", "level": "INFO", "logger": "backend.services.qr_preview_cache", "message": "QR preview cache cleanup task stopped", "module": "qr_preview_cache", "function": "stop", "line": 257}
{"timestamp": "2025-06-11T15:14:16.011190", "level": "INFO", "logger": "backend.main", "message": "QR preview cache stopped", "module": "main", "function": "shutdown_qr_preview_cache", "line": 352}
{"timestamp": "2025-06-11T15:14:22.091770", "level": "INFO", "logger": "backend.main", "message": "=== QRVibe API Startup: Logging to file is enabled ===", "module": "main", "function": "<module>", "line": 37}
{"timestamp": "2025-06-11T15:14:22.093616", "level": "INFO", "logger": "backend.main", "message": "Applying critical patches before initialization", "module": "main", "function": "<module>", "line": 43}
{"timestamp": "2025-06-11T15:14:22.093747", "level": "INFO", "logger": "backend.patches", "message": "Applying patches (none required)", "module": "__init__", "function": "apply_patches", "line": 11}
{"timestamp": "2025-06-11T15:14:22.094648", "level": "INFO", "logger": "backend.core.middleware.cors", "message": "Setting up CORS middleware with origins: ['https://127.0.0.1:3000', 'https://localhost:3000', 'http://localhost:3000', 'http://127.0.0.1:3000']", "module": "cors", "function": "setup_cors", "line": 86}
{"timestamp": "2025-06-11T15:14:22.094733", "level": "INFO", "logger": "backend.core.middleware.cors", "message": "CORS middleware configured - preflight requests should be handled automatically", "module": "cors", "function": "setup_cors", "line": 98}
{"timestamp": "2025-06-11T15:14:22.101299", "level": "INFO", "logger": "backend.middleware.security_middleware", "message": "Added Enhanced Security Middleware with tiered rate limiting", "module": "security_middleware", "function": "add_security_middleware", "line": 176}
{"timestamp": "2025-06-11T15:14:22.102372", "level": "INFO", "logger": "backend.main", "message": "Mounting storage directory: /Users/<USER>/aihub/qrvibe/storage", "module": "main", "function": "<module>", "line": 107}
{"timestamp": "2025-06-11T15:14:22.103736", "level": "INFO", "logger": "backend.main", "message": "Storage directory absolute path: /Users/<USER>/aihub/qrvibe/storage", "module": "main", "function": "<module>", "line": 119}
{"timestamp": "2025-06-11T15:14:22.713236", "level": "INFO", "logger": "root", "message": "Storage initialized: environment=test, storage_dir=/Users/<USER>/aihub/qrvibe/storage, s3_enabled=True", "module": "storage", "function": "__init__", "line": 41}
{"timestamp": "2025-06-11T15:14:22.721424", "level": "INFO", "logger": "root", "message": "S3 storage enabled with bucket: qrverse and CDN: https://dev-cdn.qrvibe.com", "module": "storage", "function": "__init__", "line": 59}
{"timestamp": "2025-06-11T15:14:22.788717", "level": "INFO", "logger": "root", "message": "Storage initialized: environment=test, storage_dir=/Users/<USER>/aihub/qrvibe/storage, s3_enabled=True", "module": "storage", "function": "__init__", "line": 41}
{"timestamp": "2025-06-11T15:14:22.792777", "level": "INFO", "logger": "root", "message": "S3 storage enabled with bucket: qrverse and CDN: https://dev-cdn.qrvibe.com", "module": "storage", "function": "__init__", "line": 59}
{"timestamp": "2025-06-11T15:14:23.179860", "level": "INFO", "logger": "backend.middleware.security_middleware", "message": "Enhanced security middleware initialized with tiered rate limiting", "module": "security_middleware", "function": "__init__", "line": 87}
{"timestamp": "2025-06-11T15:14:23.180000", "level": "INFO", "logger": "backend.core.middleware.csrf", "message": "CSRF Protection Middleware initialized with exclude paths: ['/auth/jwt/login', '/auth/logout', '/auth/register', '/auth/2fa', '/auth/me', '/auth/verify-email', '/auth/reset-password', '/admin/login', '/admin/logout']", "module": "csrf", "function": "__init__", "line": 44}
{"timestamp": "2025-06-11T15:14:23.194387", "level": "INFO", "logger": "backend.services.token_blacklist", "message": "Token blacklist service initialized with Redis", "module": "token_blacklist", "function": "initialize", "line": 59}
{"timestamp": "2025-06-11T15:14:23.194488", "level": "INFO", "logger": "backend.main", "message": "Token blacklist service initialized", "module": "main", "function": "initialize_token_blacklist", "line": 98}
{"timestamp": "2025-06-11T15:14:23.197434", "level": "INFO", "logger": "backend.services.qr_preview_cache", "message": "QR preview cache initialized at /Users/<USER>/aihub/qrvibe/storage/cache/qr_preview_cache.db", "module": "qr_preview_cache", "function": "_init_db", "line": 56}
{"timestamp": "2025-06-11T15:14:23.197536", "level": "INFO", "logger": "backend.services.qr_preview_cache", "message": "Started QR preview cache cleanup task", "module": "qr_preview_cache", "function": "start_cleanup_task", "line": 62}
{"timestamp": "2025-06-11T15:14:23.197595", "level": "INFO", "logger": "backend.main", "message": "QR preview cache initialized and cleanup task started", "module": "main", "function": "init_qr_preview_cache", "line": 345}
{"timestamp": "2025-06-11T15:14:36.559562", "level": "INFO", "logger": "backend.services.qr_preview_cache", "message": "QR preview cache cleanup task stopped", "module": "qr_preview_cache", "function": "stop", "line": 257}
{"timestamp": "2025-06-11T15:14:36.560374", "level": "INFO", "logger": "backend.main", "message": "QR preview cache stopped", "module": "main", "function": "shutdown_qr_preview_cache", "line": 352}
{"timestamp": "2025-06-11T15:14:40.526606", "level": "INFO", "logger": "backend.main", "message": "=== QRVibe API Startup: Logging to file is enabled ===", "module": "main", "function": "<module>", "line": 37}
{"timestamp": "2025-06-11T15:14:40.526727", "level": "INFO", "logger": "backend.main", "message": "Applying critical patches before initialization", "module": "main", "function": "<module>", "line": 43}
{"timestamp": "2025-06-11T15:14:40.526808", "level": "INFO", "logger": "backend.patches", "message": "Applying patches (none required)", "module": "__init__", "function": "apply_patches", "line": 11}
{"timestamp": "2025-06-11T15:14:40.527085", "level": "INFO", "logger": "backend.core.middleware.cors", "message": "Setting up CORS middleware with origins: ['http://127.0.0.1:3000', 'http://localhost:3000', 'https://localhost:3000', 'https://127.0.0.1:3000']", "module": "cors", "function": "setup_cors", "line": 86}
{"timestamp": "2025-06-11T15:14:40.527168", "level": "INFO", "logger": "backend.core.middleware.cors", "message": "CORS middleware configured - preflight requests should be handled automatically", "module": "cors", "function": "setup_cors", "line": 98}
{"timestamp": "2025-06-11T15:14:40.528590", "level": "INFO", "logger": "backend.middleware.security_middleware", "message": "Added Enhanced Security Middleware with tiered rate limiting", "module": "security_middleware", "function": "add_security_middleware", "line": 176}
{"timestamp": "2025-06-11T15:14:40.528726", "level": "INFO", "logger": "backend.main", "message": "Mounting storage directory: /Users/<USER>/aihub/qrvibe/storage", "module": "main", "function": "<module>", "line": 107}
{"timestamp": "2025-06-11T15:14:40.528880", "level": "INFO", "logger": "backend.main", "message": "Storage directory absolute path: /Users/<USER>/aihub/qrvibe/storage", "module": "main", "function": "<module>", "line": 119}
{"timestamp": "2025-06-11T15:14:41.041463", "level": "INFO", "logger": "root", "message": "Storage initialized: environment=test, storage_dir=/Users/<USER>/aihub/qrvibe/storage, s3_enabled=True", "module": "storage", "function": "__init__", "line": 41}
{"timestamp": "2025-06-11T15:14:41.045055", "level": "INFO", "logger": "root", "message": "S3 storage enabled with bucket: qrverse and CDN: https://dev-cdn.qrvibe.com", "module": "storage", "function": "__init__", "line": 59}
{"timestamp": "2025-06-11T15:14:41.100632", "level": "INFO", "logger": "root", "message": "Storage initialized: environment=test, storage_dir=/Users/<USER>/aihub/qrvibe/storage, s3_enabled=True", "module": "storage", "function": "__init__", "line": 41}
{"timestamp": "2025-06-11T15:14:41.104262", "level": "INFO", "logger": "root", "message": "S3 storage enabled with bucket: qrverse and CDN: https://dev-cdn.qrvibe.com", "module": "storage", "function": "__init__", "line": 59}
{"timestamp": "2025-06-11T15:14:41.140862", "level": "INFO", "logger": "backend.middleware.security_middleware", "message": "Enhanced security middleware initialized with tiered rate limiting", "module": "security_middleware", "function": "__init__", "line": 87}
{"timestamp": "2025-06-11T15:14:41.141012", "level": "INFO", "logger": "backend.core.middleware.csrf", "message": "CSRF Protection Middleware initialized with exclude paths: ['/auth/jwt/login', '/auth/logout', '/auth/register', '/auth/2fa', '/auth/me', '/auth/verify-email', '/auth/reset-password', '/admin/login', '/admin/logout']", "module": "csrf", "function": "__init__", "line": 44}
{"timestamp": "2025-06-11T15:14:41.144647", "level": "INFO", "logger": "backend.services.token_blacklist", "message": "Token blacklist service initialized with Redis", "module": "token_blacklist", "function": "initialize", "line": 59}
{"timestamp": "2025-06-11T15:14:41.144734", "level": "INFO", "logger": "backend.main", "message": "Token blacklist service initialized", "module": "main", "function": "initialize_token_blacklist", "line": 98}
{"timestamp": "2025-06-11T15:14:41.145480", "level": "INFO", "logger": "backend.services.qr_preview_cache", "message": "QR preview cache initialized at /Users/<USER>/aihub/qrvibe/storage/cache/qr_preview_cache.db", "module": "qr_preview_cache", "function": "_init_db", "line": 56}
{"timestamp": "2025-06-11T15:14:41.145555", "level": "INFO", "logger": "backend.services.qr_preview_cache", "message": "Started QR preview cache cleanup task", "module": "qr_preview_cache", "function": "start_cleanup_task", "line": 62}
{"timestamp": "2025-06-11T15:14:41.157809", "level": "INFO", "logger": "backend.main", "message": "QR preview cache initialized and cleanup task started", "module": "main", "function": "init_qr_preview_cache", "line": 345}
{"timestamp": "2025-06-11T15:15:15.228676", "level": "INFO", "logger": "backend.services.qr_preview_cache", "message": "QR preview cache cleanup task stopped", "module": "qr_preview_cache", "function": "stop", "line": 257}
{"timestamp": "2025-06-11T15:15:15.228790", "level": "INFO", "logger": "backend.main", "message": "QR preview cache stopped", "module": "main", "function": "shutdown_qr_preview_cache", "line": 352}
{"timestamp": "2025-06-11T15:15:19.100009", "level": "INFO", "logger": "backend.main", "message": "=== QRVibe API Startup: Logging to file is enabled ===", "module": "main", "function": "<module>", "line": 37}
{"timestamp": "2025-06-11T15:15:19.100283", "level": "INFO", "logger": "backend.main", "message": "Applying critical patches before initialization", "module": "main", "function": "<module>", "line": 43}
{"timestamp": "2025-06-11T15:15:19.100357", "level": "INFO", "logger": "backend.patches", "message": "Applying patches (none required)", "module": "__init__", "function": "apply_patches", "line": 11}
{"timestamp": "2025-06-11T15:15:19.100615", "level": "INFO", "logger": "backend.core.middleware.cors", "message": "Setting up CORS middleware with origins: ['https://127.0.0.1:3000', 'http://localhost:3000', 'https://localhost:3000', 'http://127.0.0.1:3000']", "module": "cors", "function": "setup_cors", "line": 86}
{"timestamp": "2025-06-11T15:15:19.100690", "level": "INFO", "logger": "backend.core.middleware.cors", "message": "CORS middleware configured - preflight requests should be handled automatically", "module": "cors", "function": "setup_cors", "line": 98}
{"timestamp": "2025-06-11T15:15:19.101683", "level": "INFO", "logger": "backend.middleware.security_middleware", "message": "Added Enhanced Security Middleware with tiered rate limiting", "module": "security_middleware", "function": "add_security_middleware", "line": 176}
{"timestamp": "2025-06-11T15:15:19.101787", "level": "INFO", "logger": "backend.main", "message": "Mounting storage directory: /Users/<USER>/aihub/qrvibe/storage", "module": "main", "function": "<module>", "line": 107}
{"timestamp": "2025-06-11T15:15:19.101927", "level": "INFO", "logger": "backend.main", "message": "Storage directory absolute path: /Users/<USER>/aihub/qrvibe/storage", "module": "main", "function": "<module>", "line": 119}
{"timestamp": "2025-06-11T15:15:19.597480", "level": "INFO", "logger": "root", "message": "Storage initialized: environment=test, storage_dir=/Users/<USER>/aihub/qrvibe/storage, s3_enabled=True", "module": "storage", "function": "__init__", "line": 41}
{"timestamp": "2025-06-11T15:15:19.601071", "level": "INFO", "logger": "root", "message": "S3 storage enabled with bucket: qrverse and CDN: https://dev-cdn.qrvibe.com", "module": "storage", "function": "__init__", "line": 59}
{"timestamp": "2025-06-11T15:15:19.717645", "level": "INFO", "logger": "root", "message": "Storage initialized: environment=test, storage_dir=/Users/<USER>/aihub/qrvibe/storage, s3_enabled=True", "module": "storage", "function": "__init__", "line": 41}
{"timestamp": "2025-06-11T15:15:19.730574", "level": "INFO", "logger": "root", "message": "S3 storage enabled with bucket: qrverse and CDN: https://dev-cdn.qrvibe.com", "module": "storage", "function": "__init__", "line": 59}
{"timestamp": "2025-06-11T15:15:19.902847", "level": "INFO", "logger": "backend.middleware.security_middleware", "message": "Enhanced security middleware initialized with tiered rate limiting", "module": "security_middleware", "function": "__init__", "line": 87}
{"timestamp": "2025-06-11T15:15:19.903188", "level": "INFO", "logger": "backend.core.middleware.csrf", "message": "CSRF Protection Middleware initialized with exclude paths: ['/auth/jwt/login', '/auth/logout', '/auth/register', '/auth/2fa', '/auth/me', '/auth/verify-email', '/auth/reset-password', '/admin/login', '/admin/logout']", "module": "csrf", "function": "__init__", "line": 44}
{"timestamp": "2025-06-11T15:15:19.911849", "level": "INFO", "logger": "backend.services.token_blacklist", "message": "Token blacklist service initialized with Redis", "module": "token_blacklist", "function": "initialize", "line": 59}
{"timestamp": "2025-06-11T15:15:19.911976", "level": "INFO", "logger": "backend.main", "message": "Token blacklist service initialized", "module": "main", "function": "initialize_token_blacklist", "line": 98}
{"timestamp": "2025-06-11T15:15:19.913045", "level": "INFO", "logger": "backend.services.qr_preview_cache", "message": "QR preview cache initialized at /Users/<USER>/aihub/qrvibe/storage/cache/qr_preview_cache.db", "module": "qr_preview_cache", "function": "_init_db", "line": 56}
{"timestamp": "2025-06-11T15:15:19.913179", "level": "INFO", "logger": "backend.services.qr_preview_cache", "message": "Started QR preview cache cleanup task", "module": "qr_preview_cache", "function": "start_cleanup_task", "line": 62}
{"timestamp": "2025-06-11T15:15:19.913265", "level": "INFO", "logger": "backend.main", "message": "QR preview cache initialized and cleanup task started", "module": "main", "function": "init_qr_preview_cache", "line": 345}
{"timestamp": "2025-06-11T15:15:37.010643", "level": "WARNING", "logger": "backend.core.security.jwt_keys", "message": "Unknown JWT kid requested: e5166f84-8c7f-48f1-8f38-caf1d94edf75", "module": "jwt_keys", "function": "get_verification_key", "line": 105}
{"timestamp": "2025-06-11T15:15:37.010883", "level": "WARNING", "logger": "backend.core.security.jwt_keys", "message": "Verification failed with key ID e5166f84-8c7f-48f1-8f38-caf1d94edf75: Unknown kid: e5166f84-8c7f-48f1-8f38-caf1d94edf75", "module": "jwt_keys", "function": "decode_token", "line": 203}
{"timestamp": "2025-06-11T15:15:37.033163", "level": "WARNING", "logger": "backend.core.security.jwt_keys", "message": "Verification failed with current key: Signature verification failed.", "module": "jwt_keys", "function": "decode_token", "line": 216}
{"timestamp": "2025-06-11T15:15:37.034229", "level": "ERROR", "logger": "backend.core.security.jwt_keys", "message": "Token verification failed with all keys. Errors: Key 0a93fd4a-d630-44f5-9800-5ed9bcc8485e: Signature verification failed.", "module": "jwt_keys", "function": "decode_token", "line": 239}
{"timestamp": "2025-06-11T15:15:37.034450", "level": "ERROR", "logger": "backend.core.security.jwt_keys", "message": "Token decode error: Token verification failed with all keys. Errors: Key 0a93fd4a-d630-44f5-9800-5ed9bcc8485e: Signature verification failed.", "module": "jwt_keys", "function": "decode_token", "line": 244}
{"timestamp": "2025-06-11T15:15:37.034592", "level": "ERROR", "logger": "backend.services.auth", "message": "JWT token validation failed: Failed to decode token: Token verification failed with all keys. Errors: Key 0a93fd4a-d630-44f5-9800-5ed9bcc8485e: Signature verification failed.", "module": "auth", "function": "authenticate_token", "line": 686}
{"timestamp": "2025-06-11T15:15:37.040725", "level": "WARNING", "logger": "backend.middleware.subscription_limits", "message": "Error authenticating token in subscription middleware: name 'ValidationError' is not defined", "module": "subscription_limits", "function": "_get_current_user", "line": 308}
{"timestamp": "2025-06-11T15:15:37.045810", "level": "WARNING", "logger": "backend.core.security.jwt_keys", "message": "Unknown JWT kid requested: e5166f84-8c7f-48f1-8f38-caf1d94edf75", "module": "jwt_keys", "function": "get_verification_key", "line": 105}
{"timestamp": "2025-06-11T15:15:37.045895", "level": "WARNING", "logger": "backend.core.security.jwt_keys", "message": "Verification failed with key ID e5166f84-8c7f-48f1-8f38-caf1d94edf75: Unknown kid: e5166f84-8c7f-48f1-8f38-caf1d94edf75", "module": "jwt_keys", "function": "decode_token", "line": 203}
{"timestamp": "2025-06-11T15:15:37.046157", "level": "WARNING", "logger": "backend.core.security.jwt_keys", "message": "Verification failed with current key: Signature verification failed.", "module": "jwt_keys", "function": "decode_token", "line": 216}
{"timestamp": "2025-06-11T15:15:37.046529", "level": "ERROR", "logger": "backend.core.security.jwt_keys", "message": "Token verification failed with all keys. Errors: Key 0a93fd4a-d630-44f5-9800-5ed9bcc8485e: Signature verification failed.", "module": "jwt_keys", "function": "decode_token", "line": 239}
{"timestamp": "2025-06-11T15:15:37.046644", "level": "ERROR", "logger": "backend.core.security.jwt_keys", "message": "Token decode error: Token verification failed with all keys. Errors: Key 0a93fd4a-d630-44f5-9800-5ed9bcc8485e: Signature verification failed.", "module": "jwt_keys", "function": "decode_token", "line": 244}
{"timestamp": "2025-06-11T15:15:37.046752", "level": "ERROR", "logger": "backend.services.auth", "message": "JWT token validation failed: Failed to decode token: Token verification failed with all keys. Errors: Key 0a93fd4a-d630-44f5-9800-5ed9bcc8485e: Signature verification failed.", "module": "auth", "function": "authenticate_token", "line": 686}
{"timestamp": "2025-06-11T15:15:37.047874", "level": "WARNING", "logger": "backend.middleware.subscription_limits", "message": "Auth error in subscription middleware: name 'ValidationError' is not defined", "module": "subscription_limits", "function": "dispatch", "line": 234}
{"timestamp": "2025-06-11T15:15:37.048761", "level": "WARNING", "logger": "backend.core.security.jwt_keys", "message": "Unknown JWT kid requested: e5166f84-8c7f-48f1-8f38-caf1d94edf75", "module": "jwt_keys", "function": "get_verification_key", "line": 105}
{"timestamp": "2025-06-11T15:15:37.049362", "level": "WARNING", "logger": "backend.core.security.jwt_keys", "message": "Verification failed with key ID e5166f84-8c7f-48f1-8f38-caf1d94edf75: Unknown kid: e5166f84-8c7f-48f1-8f38-caf1d94edf75", "module": "jwt_keys", "function": "decode_token", "line": 203}
{"timestamp": "2025-06-11T15:15:37.049593", "level": "WARNING", "logger": "backend.core.security.jwt_keys", "message": "Verification failed with current key: Signature verification failed.", "module": "jwt_keys", "function": "decode_token", "line": 216}
{"timestamp": "2025-06-11T15:15:37.049862", "level": "ERROR", "logger": "backend.core.security.jwt_keys", "message": "Token verification failed with all keys. Errors: Key 0a93fd4a-d630-44f5-9800-5ed9bcc8485e: Signature verification failed.", "module": "jwt_keys", "function": "decode_token", "line": 239}
{"timestamp": "2025-06-11T15:15:37.050046", "level": "ERROR", "logger": "backend.core.security.jwt_keys", "message": "Token decode error: Token verification failed with all keys. Errors: Key 0a93fd4a-d630-44f5-9800-5ed9bcc8485e: Signature verification failed.", "module": "jwt_keys", "function": "decode_token", "line": 244}
{"timestamp": "2025-06-11T15:15:37.050146", "level": "ERROR", "logger": "backend.services.auth", "message": "JWT token validation failed: Failed to decode token: Token verification failed with all keys. Errors: Key 0a93fd4a-d630-44f5-9800-5ed9bcc8485e: Signature verification failed.", "module": "auth", "function": "authenticate_token", "line": 686}
{"timestamp": "2025-06-11T15:16:06.305831", "level": "WARNING", "logger": "backend.core.security.jwt_keys", "message": "Unknown JWT kid requested: e5166f84-8c7f-48f1-8f38-caf1d94edf75", "module": "jwt_keys", "function": "get_verification_key", "line": 105}
{"timestamp": "2025-06-11T15:16:06.306101", "level": "WARNING", "logger": "backend.core.security.jwt_keys", "message": "Verification failed with key ID e5166f84-8c7f-48f1-8f38-caf1d94edf75: Unknown kid: e5166f84-8c7f-48f1-8f38-caf1d94edf75", "module": "jwt_keys", "function": "decode_token", "line": 203}
{"timestamp": "2025-06-11T15:16:06.310945", "level": "WARNING", "logger": "backend.core.security.jwt_keys", "message": "Verification failed with current key: Signature verification failed.", "module": "jwt_keys", "function": "decode_token", "line": 216}
{"timestamp": "2025-06-11T15:16:06.311600", "level": "ERROR", "logger": "backend.core.security.jwt_keys", "message": "Token verification failed with all keys. Errors: Key 0a93fd4a-d630-44f5-9800-5ed9bcc8485e: Signature verification failed.", "module": "jwt_keys", "function": "decode_token", "line": 239}
{"timestamp": "2025-06-11T15:16:06.311783", "level": "ERROR", "logger": "backend.core.security.jwt_keys", "message": "Token decode error: Token verification failed with all keys. Errors: Key 0a93fd4a-d630-44f5-9800-5ed9bcc8485e: Signature verification failed.", "module": "jwt_keys", "function": "decode_token", "line": 244}
{"timestamp": "2025-06-11T15:16:06.312208", "level": "ERROR", "logger": "backend.auth", "message": "Token validation error: Failed to decode token: Token verification failed with all keys. Errors: Key 0a93fd4a-d630-44f5-9800-5ed9bcc8485e: Signature verification failed.", "module": "__init__", "function": "get_current_user", "line": 193}
{"timestamp": "2025-06-11T15:16:06.313361", "level": "WARNING", "logger": "backend.core.security.jwt_keys", "message": "Unknown JWT kid requested: e5166f84-8c7f-48f1-8f38-caf1d94edf75", "module": "jwt_keys", "function": "get_verification_key", "line": 105}
{"timestamp": "2025-06-11T15:16:06.313452", "level": "WARNING", "logger": "backend.core.security.jwt_keys", "message": "Verification failed with key ID e5166f84-8c7f-48f1-8f38-caf1d94edf75: Unknown kid: e5166f84-8c7f-48f1-8f38-caf1d94edf75", "module": "jwt_keys", "function": "decode_token", "line": 203}
{"timestamp": "2025-06-11T15:16:06.313688", "level": "WARNING", "logger": "backend.core.security.jwt_keys", "message": "Verification failed with current key: Signature verification failed.", "module": "jwt_keys", "function": "decode_token", "line": 216}
{"timestamp": "2025-06-11T15:16:06.313966", "level": "ERROR", "logger": "backend.core.security.jwt_keys", "message": "Token verification failed with all keys. Errors: Key 0a93fd4a-d630-44f5-9800-5ed9bcc8485e: Signature verification failed.", "module": "jwt_keys", "function": "decode_token", "line": 239}
{"timestamp": "2025-06-11T15:16:06.314077", "level": "ERROR", "logger": "backend.core.security.jwt_keys", "message": "Token decode error: Token verification failed with all keys. Errors: Key 0a93fd4a-d630-44f5-9800-5ed9bcc8485e: Signature verification failed.", "module": "jwt_keys", "function": "decode_token", "line": 244}
{"timestamp": "2025-06-11T15:16:06.314353", "level": "ERROR", "logger": "backend.services.auth", "message": "JWT token validation failed: Failed to decode token: Token verification failed with all keys. Errors: Key 0a93fd4a-d630-44f5-9800-5ed9bcc8485e: Signature verification failed.", "module": "auth", "function": "authenticate_token", "line": 686}
{"timestamp": "2025-06-11T15:16:06.316040", "level": "WARNING", "logger": "backend.middleware.subscription_limits", "message": "Error authenticating token in subscription middleware: name 'ValidationError' is not defined", "module": "subscription_limits", "function": "_get_current_user", "line": 308}
{"timestamp": "2025-06-11T15:16:06.316789", "level": "WARNING", "logger": "backend.core.security.jwt_keys", "message": "Unknown JWT kid requested: e5166f84-8c7f-48f1-8f38-caf1d94edf75", "module": "jwt_keys", "function": "get_verification_key", "line": 105}
{"timestamp": "2025-06-11T15:16:06.316869", "level": "WARNING", "logger": "backend.core.security.jwt_keys", "message": "Verification failed with key ID e5166f84-8c7f-48f1-8f38-caf1d94edf75: Unknown kid: e5166f84-8c7f-48f1-8f38-caf1d94edf75", "module": "jwt_keys", "function": "decode_token", "line": 203}
{"timestamp": "2025-06-11T15:16:06.317085", "level": "WARNING", "logger": "backend.core.security.jwt_keys", "message": "Verification failed with current key: Signature verification failed.", "module": "jwt_keys", "function": "decode_token", "line": 216}
{"timestamp": "2025-06-11T15:16:06.317753", "level": "ERROR", "logger": "backend.core.security.jwt_keys", "message": "Token verification failed with all keys. Errors: Key 0a93fd4a-d630-44f5-9800-5ed9bcc8485e: Signature verification failed.", "module": "jwt_keys", "function": "decode_token", "line": 239}
{"timestamp": "2025-06-11T15:16:06.317863", "level": "ERROR", "logger": "backend.core.security.jwt_keys", "message": "Token decode error: Token verification failed with all keys. Errors: Key 0a93fd4a-d630-44f5-9800-5ed9bcc8485e: Signature verification failed.", "module": "jwt_keys", "function": "decode_token", "line": 244}
{"timestamp": "2025-06-11T15:16:06.317970", "level": "ERROR", "logger": "backend.auth", "message": "Token validation error: Failed to decode token: Token verification failed with all keys. Errors: Key 0a93fd4a-d630-44f5-9800-5ed9bcc8485e: Signature verification failed.", "module": "__init__", "function": "get_current_user", "line": 193}
{"timestamp": "2025-06-11T15:17:01.086426", "level": "INFO", "logger": "backend.services.qr_preview_cache", "message": "QR preview cache cleanup task stopped", "module": "qr_preview_cache", "function": "stop", "line": 257}
{"timestamp": "2025-06-11T15:17:01.088251", "level": "INFO", "logger": "backend.main", "message": "QR preview cache stopped", "module": "main", "function": "shutdown_qr_preview_cache", "line": 352}
