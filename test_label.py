#!/usr/bin/env python3

import sys
import os
sys.path.append(os.path.join(os.path.dirname(__file__), 'backend'))

from backend.services.qr_preview import QRPreviewService
from backend.schemas.qr_preview import QRDesign

# Test the label functionality
def test_label():
    # Create a design with frame and label
    design = QRDesign(
        foreground_color='#000000',
        background_color='#FFFFFF',
        frame_style='classic',
        frame_text='SCAN ME',
        frame_font='Arial',
        frame_font_size=16,
        frame_color='#000000'
    )
    
    # Generate QR code with label
    svg_content = QRPreviewService.generate_qr_preview(
        data='https://example.com',
        design=design
    )
    
    print("SVG length:", len(svg_content))
    print("Checking for label elements...")

    # Check if label elements are present
    if 'qr-label-background' in svg_content:
        print("+ Label background found")
    else:
        print("- Label background NOT found")

    if 'qr-frame-label' in svg_content:
        print("+ Label text element found")
    else:
        print("- Label text element NOT found")

    if 'SCAN ME' in svg_content:
        print("+ Label text 'SCAN ME' found")
    else:
        print("- Label text 'SCAN ME' NOT found")
    
    # Save to file for inspection
    with open('test_label_output.svg', 'w') as f:
        f.write(svg_content)
    print("SVG saved to test_label_output.svg")

if __name__ == '__main__':
    test_label()
